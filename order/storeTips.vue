<template>
	<view class="uni-popup" :class="[popupstyle]" @touchmove.stop.prevent="clear" v-if="showPopup">
		<uni-transition :mode-class="['fade']" :styles="maskClass" :duration="duration" :show="showTrans" @click="close" />
		<view class="uni-popup-dialog">
			<view class="uni-dialog-title">
				<text class="uni-dialog-title-text uni-popup__info">下单提示</text>
			</view>
			<view class="uni-dialog-content">
				<view class="uni-dialog-content-text flex_column_start_start">
					<view class="li" v-for="(item,key) in content" :key="key">{{item}}</view>
				</view>
			</view>
			<view class="uni-dialog-button-group">
				<view class="uni-dialog-button uni-border-left" @click="close">
					<text class="uni-dialog-button-text uni-button-color">{{ confirmText }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import uniTransition from '@/components/uni-transition/uni-transition.vue';
export default {
	name: 'storeTips',
	components: {
		uniTransition
	},
	data() {
		return {
			imgUrl: getApp().globalData.imgUrl,
			show: false,
			content:'',
			confirmText: '确定',
			popupstyle: 'center',
			ani: ['zoom-out', 'fade'],
			duration: 300,
			showPopup: false,
			showTrans: false,
			maskClass: {
				position: 'fixed',
				bottom: 0,
				top: 0,
				left: 0,
				right: 0,
				backgroundColor: 'rgba(0, 0, 0, 0.4)'
			},
			transClass: {
				position: 'fixed',
				left: 0,
				right: 0
			}
		};
	},
	methods: {
		clear(e) {
			// TODO nvue 取消冒泡
			e.stopPropagation()
		},
		open(content) {
			this.content = content
			this.showPopup = true;
			this.$nextTick(() => {
				this.showTrans = true;
			});
		},
		close() {
			this.showTrans = false;
			this.showPopup = false;
		}
	}
};
</script>

<style lang="scss" scoped>
.uni-popup {
	position: fixed;
	/* #ifndef APP-NVUE */
	z-index: 99;
	/* #endif */
}

.uni-popup__mask {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: $uni-bg-color-mask;
	opacity: 0;
}

.mask-ani {
	transition-property: opacity;
	transition-duration: 0.2s;
}

.uni-top-mask {
	opacity: 1;
}

.uni-bottom-mask {
	opacity: 1;
}

.uni-center-mask {
	opacity: 1;
}

.uni-popup__wrapper {
	/* #ifndef APP-NVUE */
	display: block;
	/* #endif */
	position: absolute;
}
.uni-popup-dialog {
	position: fixed;
	top: 25%;
	left: 5%;
	width: 90%;
	border-radius: 14rpx;
	background-color: #fff;
}

.uni-dialog-title {
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	flex-direction: row;
	justify-content: center;
	padding-top: 35rpx;
	padding-bottom: 35rpx;
}

.uni-dialog-title-text {
	font-size: 30rpx;
	font-weight: 500;
	color: $main-font-color;
}

.uni-dialog-content {
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	flex-direction: row;
	justify-content: center;
	align-items: center;
	padding: 0rpx 40rpx 40rpx 40rpx;
}

.uni-dialog-content-text {
	font-size: 28rpx;
	color: $main-second-color;
	row-gap: 20rpx;
}

.uni-dialog-button-group {
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	flex-direction: row;
	border-top-color: rgba(0, 0, 0, 0.05);
	border-top-style: solid;
	border-top-width: 1rpx;
}

.uni-dialog-button {
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */

	flex: 1;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	height: 80rpx;
}

.uni-border-left {
	border-left-color: rgba(0, 0, 0, 0.05);
	border-left-style: solid;
	border-left-width: 1rpx;
}

.uni-dialog-button-text {
	font-size: 30rpx;
	color: $main-font-color;
}

.uni-button-color {
	color: var(--color_main);
}

.uni-dialog-input {
	flex: 1;
	font-size: 14px;
}

.uni-popup__success {
	color: $uni-color-success;
}

.uni-popup__warn {
	color: $uni-color-warning;
}

.uni-popup__error {
	color: $uni-color-error;
}

.uni-popup__info {
	color: #909399;
}
</style>
