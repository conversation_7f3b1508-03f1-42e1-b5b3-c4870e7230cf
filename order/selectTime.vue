<template>
	<view>
		<!-- 选择时间段 start -->
		<uni-popup class="spec_model" ref="timeModel" type="bottom">
			<view class="spec_model_con">
				<view class="spec_model_content">
					<view class="spec_model_top">
						<text>{{ openType == 1 ? "预计到店时间" : $L("选择配送时间") }}</text>
						<image :src="imgUrl + 'goods_detail/close.png'" mode="aspectFit" class="close_spec" @click="close"></image>
					</view>
					<view class="flex_row_between_start day_time_select">
						<view class="select_day">
							<view class="select_day_item" :class="{ selected: selDay == day.date }" v-for="day in dateList" :key="day.date" @click="chooesDay(day)">{{ day.name + (day.name == "今天" ? "" : day.date_f) }}</view>
						</view>
						<scroll-view scroll-y="true" class="select_times spec_content" @touchmove.stop>
							<!-- <view v-if="openType == 2" class="store_item" :class="{ selected: selTime == '尽快送达' }" @click="storeChange('尽快送达')">
								<view>
									<view class="f_16">尽快送达</view>
								</view>
								<view>
									<radio color="#367fff" :checked="selTime == '尽快送达' ? true : false" />
								</view>
							</view> -->
							<template v-for="(item, index) in timeList">
								<view class="store_item" :class="{ selected: selTime == item }" :key="index" @click="chooesTime(item)">
									<view>
										<view class="f_16">{{ item }}</view>
									</view>
									<view>
										<radio color="#367fff" :checked="selTime == item ? true : false" />
									</view>
								</view>
							</template>
						</scroll-view>
					</view>
				</view>
				<block>
					<view class="spec_btn">
						<text class="spec_add_cart_btn spec_btn_only" @click="addCart">{{ $L("确定") }}</text>
					</view>
				</block>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import uniPopup from "@/components/uni-popup/uni-popup.vue";
export default {
	name: "timeModel",
	data() {
		return {
			imgUrl: getApp().globalData.imgUrl,
			selDay: null, //选中日期
			selTime: null, // 选中时间段
			timeList: [],
			dateList: []
		};
	},
	components: {
		uniPopup
	},
	props: {
		data: {
			type: Array,
			default: []
		},
		openType: {
			type: Number,
			default: 1
		}
	},
	mounted() {},
	methods: {
		addCart() {
			this.close();
			this.$emit("chooseOver", { date: this.selDay, time: this.selTime });
		},
		/*选择日期*/
		chooesDay(day) {
			const { date, timeList } = day;
			this.selDay = date;
			this.selTime = timeList[0];
			this.timeList = timeList;
		},
		/*选择时间段*/
		chooesTime(item) {
			this.selTime = item;
		},
		/*
		 * 打开预约时间选择弹窗
		 * @defaultValue 打开默认选中值
		 */
		open(defaultValue) {
			if (this.data.length) {
				const data = this.data;
				const first_list = data[0];
				const dateList = data.map((e) => {
					return {
						name: this.dayjs().isSame(this.dayjs(e.date), "day") ? "今天" : this.dayjs().add(1, "day").isSame(this.dayjs(e.date), "day") ? "明天" : "",
						date_f: this.dayjs(e.date).format("MM月DD日"),
						date: e.date,
						timeList: e.timeList
					};
				});
				// console.log("list", dateList);
				this.dateList = dateList;
				if (defaultValue) {
					this.selDay = defaultValue.date;
					this.timeList = dateList.filter((e) => e.date == defaultValue.date)[0].timeList;
					this.selTime = defaultValue.time;
				} else {
					this.selDay = dateList[0].date;
					this.timeList = dateList[0].timeList;
					this.selTime = dateList[0].timeList[0];
				}
				this.$refs.timeModel.open();
			} else {
				this.$api.msg("暂无可预约时间");
			}
		},
		close() {
			this.$refs.timeModel.close();
		}
	}
};
</script>

<style lang="scss" scoped>
.f_14 {
	font-size: 28rpx;
}

.f_16 {
	font-size: 28rpx;
}

.tips {
	color: #9a9a9a;
	font-size: 24rpx;
	margin-top: 60rpx;
}

.spec_model {
	z-index: 9999 !important;
}
.day_time_select {
	padding: 40rpx 30rpx 0rpx 0;
	height: calc(100% - 80rpx);
	.select_day {
		width: 250rpx;
		height: 100%;
		background-color: #f5f5f5;
		&_item {
			text-align: center;
			width: 100%;
			height: 90rpx;
			line-height: 90rpx;
			font-size: 32rpx;
			color: #464646;
			&.selected {
				background-color: #fff;
				color: $color1;
			}
		}
	}
	.select_times {
		height: 100%;
		flex: 1;
	}
}
.spec_model_con {
	width: 750rpx;
	height: 900rpx;
	background: #ffffff;
	border-radius: 10rpx 10rpx 0;
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	margin: 0 auto;
	z-index: 150;

	.spec_model_content {
		// padding-bottom: 115rpx;
		height: calc(100% - env(safe-area-inset-bottom) - 100rpx);
		.spec_model_top {
			display: flex;
			height: 80rpx;
			justify-content: space-between;
			padding: 16rpx 22rpx 0 30rpx;
			box-sizing: border-box;
			color: #101010;

			.close_spec {
				position: absolute;
				top: 14rpx;
				right: 14rpx;
				z-index: 9;
				width: 46rpx;
				height: 46rpx;
			}
		}

		.spec_content {
			padding: 20rpx 30rpx;
			color: #101010;
			.store_item {
				width: 100%;
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 50rpx;
				&.selected {
					color: $color1;
				}
				::v-deep .uni-radio-input-checked {
					background-color: #e61400 !important;
				}

				::v-deep.uni-radio-input:hover {
					border-color: #e61400;
				}
			}
		}
	}

	.spec_btn {
		width: 750rpx;
		height: 100rpx;
		background: #ffffff;
		box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(86, 86, 86, 0.08);
		display: flex;
		align-items: center;
		justify-content: center;
		position: fixed;
		bottom: env(safe-area-inset-bottom);
		/* #ifdef MP */
		/* height: calc(98rpx + env(safe-area-inset-bottom)); */
		/* padding-bottom: constant(safe-area-inset-bottom); */
		/*���� IOS<11.2*/
		/* padding-bottom: env(safe-area-inset-bottom); */
		/*���� IOS>11.2*/
		/* #endif */
		color: #fff;

		.spec_add_cart_btn {
			width: 345rpx;
			height: 70rpx;
			background: #367fff;
			border-radius: 35rpx 0 0 35rpx;
			font-size: 30rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ffffff;
			text-align: center;
			line-height: 70rpx;
		}

		.spec_buy_btn {
			width: 345rpx;
			height: 70rpx;
			background: #367fff;
			border-radius: 0 35rpx 35rpx 0;
			font-size: 30rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ffffff;
			text-align: center;
			line-height: 70rpx;
		}

		.spec_not_stock {
			background: #adadad;
			border-radius: 35rpx;
			font-size: 30rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ffffff;
			text-align: center;
			line-height: 70rpx;
		}

		.spec_seckill_btn {
			background: linear-gradient(45deg, #fc2d2d 0%, #fd572b 100%);
			border-radius: 35rpx;
			font-size: 30rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ffffff;
			text-align: center;
			line-height: 70rpx;
		}

		.spec_btn_only {
			width: 690rpx;
			height: 70rpx;
			border-radius: 35rpx;
			text-align: center;
			line-height: 70rpx;
		}

		.specifications_btn2 {
			width: 690rpx;
			height: 70rpx;
			background: linear-gradient(45deg, #ff5c00 0%, #fce000 0%, #fe8300 0%, #fb9721 100%);
			border-radius: 35rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ffffff;
			line-height: 32rpx;
		}

		.specifications_bottom_btn3 {
			width: 690rpx;
			height: 70rpx;
			background: linear-gradient(45deg, #ff5d00 0%, #fce000 0%, #fe8400 0%, #fb9721 100%);
			border-radius: 35rpx;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ffffff;
			line-height: 30rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.specifications_bottom_btn4 {
			width: 690rpx;
			height: 70rpx;
			background: linear-gradient(45deg, #fb2d2d 0%, #fc572a 100%);
			border-radius: 35rpx;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ffffff;
			line-height: 30rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.specification_add {
			width: 347rpx;
			height: 70rpx;
			background: linear-gradient(45deg, #ff7918 0%, #fea00d 100%);
			border-radius: 34rpx 0 0 34rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #fff;
			font-size: 28rpx;
		}

		.specification_add text:nth-of-type(1),
		.specification_buy text:nth-of-type(1) {
			margin-right: 20rpx;
		}

		.spec_deposit_btn {
			color: #fff;
			background: linear-gradient(45deg, #ff7a18 0%, #fea10e 100%);
		}

		.specification_buy {
			width: 343rpx;
			height: 70rpx;
			background: linear-gradient(45deg, #fb2d2d 0%, #fc572a 100%);
			border-radius: 0 34rpx 34rpx 0;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #fff;
			font-size: 28rpx;
		}
	}
}
</style>
