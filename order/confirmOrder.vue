<template>
    <view class="page" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }" v-if="loadFlag" id="container">
        <ktabbar :bgImg="imgUrl + 'default_bg.jpg'" :title="'订单结算'" />
        <view class="order_confirm_container">
            <!-- 地址 -->
            <view class="address-section" v-if="isVG == 1">
                <view @click="operateAddress" class="order-content">
                    <view class="cen" v-if="orderAddress.addressId != undefined">
                        <view class="member_info flex_row_between_center">
                            <text class="name">收货人：{{ orderAddress.memberName }}</text>
                            <text class="mobile">{{ orderAddress.telMobile }}</text>
                        </view>
                        <view class="top flex_row_start_center">
                            <!-- <view v-if="orderAddress.isDefault == 1" class="tag">默认</view> -->
                            <text class="address">{{ orderAddress.addressAll }}{{ orderAddress.detailAddress }}</text>
                        </view>
                        <view class="flex_row_between_center click_address">
                            <view>收货地址</view>
                            <text class="iconfont iconziyuan11"></text>
                        </view>
                    </view>

                    <view class="empty_address flex_row_center_center" v-if="orderAddress.addressId == undefined">
                        <text class="add_icon">+</text>
                        <text class="tit">{{ $L('新建收货地址') }}</text>
                    </view>
                </view>
            </view>
            <!-- 虚拟商品的预留信息 -->
            <view class="pre_message" v-else>
                <block v-if="virtualPre.length">
                    <block v-for="(item, index) in virtualPre" :key="index">
                        <view class="pre_msg_item flex_row_start_center" v-if="!orderSn || item.reserveValue">
                            <view class="msg_left flex_row_end_center">
                                <text style="color: red" v-if="item.isRequired == 1">*</text>
                                <text>{{ item.reserveName }}</text>
                                ：
                            </view>
                            <view class="msg_right">
                                <block v-if="item.reserveType == 1">
                                    <input
                                        type="number"
                                        :placeholder="`${$L('请输入')}${item.reserveName}`"
                                        v-model="item.reserveValue"
                                        maxlength="11"
                                        @blur="handleBlur"
                                        @focus="handleFocus"
                                        v-if="!orderSn"
                                        placeholder-style="font-size:26rpx"
                                    />
                                    <text v-else style="opacity: 0.4">{{ item.reserveValue }}</text>
                                </block>
                                <block v-else-if="item.reserveType == 3">
                                    <input
                                        type="number"
                                        :placeholder="`${$L('请输入')}${item.reserveName}`"
                                        v-model="item.reserveValue"
                                        maxlength="50"
                                        @blur="handleBlur"
                                        @focus="handleFocus"
                                        v-if="!orderSn"
                                        placeholder-style="font-size:26rpx"
                                    />
                                    <text v-else style="opacity: 0.4">{{ item.reserveValue }}</text>
                                </block>
                                <block v-else-if="item.reserveType == 5">
                                    <input
                                        type="text"
                                        :placeholder="`${$L('请输入')}${item.reserveName}`"
                                        v-model="item.reserveValue"
                                        maxlength="30"
                                        @blur="handleBlur"
                                        @focus="handleFocus"
                                        v-if="!orderSn"
                                        placeholder-style="font-size:26rpx"
                                    />
                                    <text v-else style="opacity: 0.4">{{ item.reserveValue }}</text>
                                </block>
                                <block v-else>
                                    <input
                                        type="text"
                                        :placeholder="`${$L('请输入')}${item.reserveName}`"
                                        v-model="item.reserveValue"
                                        :maxlength="item.reserveType == 2 ? 18 : 50"
                                        @blur="handleBlur"
                                        @focus="handleFocus"
                                        v-if="!orderSn"
                                        placeholder-style="font-size:26rpx"
                                    />
                                    <text v-else style="opacity: 0.4">{{ item.reserveValue }}</text>
                                </block>
                            </view>
                        </view>
                    </block>
                </block>
            </view>
            <!-- 真实商品信息 -->
            <view class="goods-section flex_column_start_start" v-if="goodsData.length > 0">
                <!-- 商品列表 -->
                <view class="store_list" v-for="(item, index) in goodsData" :key="item.storeId">
                    <view class="store_name" @click.stop="goStoreDetail(item.storeId)">
                        <image class="store_logo" :src="imgUrl + 'goods_detail/store_logo.png'"></image>
                        <text class="store_name_text">{{ item.storeName }}</text>
                        <text class="iconfont iconziyuan11"></text>
                    </view>
                    <view class="product_con" v-for="(product, indexs) in item.productList" :key="indexs">
                        <view class="g-item flex_row_start_start">
                            <view class="image_con">
                                <view class="virtual_tag" v-if="isVG == 2">{{ $L('虚拟') }}</view>
                                <view class="image" :style="{ backgroundImage: 'url(' + product.image + ')' }"></view>
                            </view>
                            <view class="right flex_column_between_start">
                                <view class="flex_column_start_start">
                                    <text class="title">{{ product.goodsName }}</text>
                                </view>
                                <view class="goods_item_specs">
                                    <text class="goods_item_spec" v-if="product.specValues">{{ product.specValues }}</text>
                                    <text class="goods_item_buynum">*{{ product.buyNum }}</text>
                                </view>
                                <view class="price-box price_wrap_super">
                                    <text class="unit">¥</text>
                                    <text class="price_int">{{ $getPartNumber(product.price, 'int') }}</text>
                                    <text class="price_decimal" v-if="product.isSupper">
                                        {{ $getPartNumber(product.price, 'decimal') }}
                                        <text class="price_super_img" :style="'background-image:url(' + imgUrl + 'super/super_price_tag.png)'"></text>
                                    </text>
                                    <text class="price_decimal" v-else>{{ $getPartNumber(product.price, 'decimal') }}</text>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="yt-list store_info">
                        <!-- 满赠商品 -->
                        <view class="yt-list-cell-giveaway b-b" v-if="item.sendProductList.length > 0">
                            <text class="cell-tit clamp">{{ $L('赠品信息') }}</text>
                            <view class="giveaway_list">
                                <view class="giveaway_item" v-for="(giveaway, indexss) in item.sendProductList" :key="indexss">
                                    <view class="giveaway_item_left">
                                        <text class="giveaway_item_index">{{ $L('赠品') }}{{ index + 1 }}：</text>
                                        <text class="giveaway_item_name">{{ giveaway.goodsName }}</text>
                                        <text>{{ $L('（赠完即止）') }}</text>
                                    </view>
                                    <text class="giveaway_item_number">*{{ giveaway.num }}</text>
                                </view>
                            </view>
                        </view>
                        <!-- 定制需求：外卖:2、到店:1订单选择时间 -->
                        <view class="yt-list-cell b-b" v-if="(preParam.isPickup == 1 || preParam.isPickup == 2 || preParam.isPickup == 3) && isShowTime" @click="selectTime()">
                            <text class="cell-tit clamp">{{ preParam.isPickup == 1 ? '到店时间' : '配送时间' }}</text>
                            <text class="cell-tip voice">{{ selDateTime ? selDateTime.date + ' ' + selDateTime.time : '请选择' }}</text>
                            <text class="iconfont iconziyuan11"></text>
                        </view>

                        <!-- 暂无优惠券 -->
                        <!-- 付定金的时候不显示优惠券 -->
                        <template
                            v-if="
                                !(
                                    (allData.promotionType == 105 && allData.ladderGroupInfo.ladderGroupState == 101) ||
                                    (allData.promotionType == 103 && allData.presellInfo.presellState == 101)
                                ) ||
                                (allData.promotionType == 103 && allData.presellInfo.type == 2)
                            "
                        >
                            <block v-if="!allData.showPromotion">
                                <view class="yt-list-cell b-b" v-if="item.availableCouponList.length == 0">
                                    <text class="cell-tit clamp">{{ $L('店铺优惠券') }}</text>
                                    <text class="cell-tip voice">{{ $L('暂无可用优惠券') }}</text>
                                    <text class="iconfont iconziyuan11"></text>
                                </view>
                                <view class="yt-list-cell b-b" v-else @click="select_store_red(item)">
                                    <text class="cell-tit clamp">{{ $L('店铺优惠券') }}</text>
                                    <text class="cell-tip">
                                        {{
                                            item.storeCouponCodeText
                                                ? '-' + `${$L('￥')}${$getPartNumber(item.storeCouponCodeText, 'int')}${$getPartNumber(item.storeCouponCodeText, 'decimal')}`
                                                : ''
                                        }}
                                    </text>
                                    <text class="iconfont iconziyuan11"></text>
                                </view>
                            </block>
                        </template>
                        <template
                            v-if="
                                !(
                                    (allData.promotionType == 105 && allData.ladderGroupInfo.ladderGroupState == 101) ||
                                    (allData.promotionType == 103 && allData.presellInfo.presellState == 101)
                                ) ||
                                (allData.promotionType == 103 && allData.presellInfo.type == 2)
                            "
                        >
                            <view class="yt-list-cell b-b" v-if="goodsData.length == 1">
                                <text class="cell-tit clamp">{{ $L('商品金额') }}</text>
                                <text class="cell-tip">{{ $L('￥') }}{{ $getPartNumber(item.goodsAmount, 'int') }}{{ $getPartNumber(item.goodsAmount, 'decimal') }}</text>
                            </view>
                        </template>

                        <!-- 预售start -->
                        <block v-if="allData.promotionType == 103 && allData.presellInfo.type != 2">
                            <view class="ladder_list b-b">
                                <text class="ladder_tip">{{ $L('其他优惠可在支付尾款时选择') }}</text>
                                <view class="ladder_process">
                                    <view class="process_level on">
                                        <view class="level_top">
                                            <view class="level_left">
                                                <svgGroup type="to_ladder_img" width="43" height="47" px="rpx" :color="diyStyle_var['--color_price']"></svgGroup>
                                                <text>{{ $L('定金支付') }}</text>
                                            </view>
                                            <view class="level_right">
                                                <text>{{ $L('￥') }}{{ allData.presellInfo.firstMoney }}</text>
                                            </view>
                                        </view>
                                        <view class="level_gang"></view>
                                    </view>
                                    <view :class="{ process_level: true, on: allData.presellInfo.presellState == 102 }">
                                        <view class="level_gang"></view>
                                        <view class="level_top">
                                            <view class="level_left">
                                                <svgGroup
                                                    v-if="allData && allData.presellInfo && allData.presellInfo.presellState == 102"
                                                    type="to_commission"
                                                    width="43"
                                                    height="47"
                                                    px="rpx"
                                                    :color="diyStyle_var['--color_price']"
                                                ></svgGroup>
                                                <svgGroup v-else="" type="to_commission" width="43" height="47" px="rpx" color="#ddd"></svgGroup>
                                                <text>{{ $L('尾款结算') }}</text>
                                            </view>
                                            <view :class="{ level_right: allData.presellInfo.presellState == 102 }">
                                                <text>￥{{ allData.presellInfo.secondMoney }}</text>
                                            </view>
                                        </view>
                                    </view>
                                    <view class="preDesc_con" style="margin-top: 48rpx">
                                        <view class="preSale_desc" v-if="allData.presellInfo && allData.presellInfo.remainStartTime">
                                            <text style="color: $color1">{{ allData.presellInfo.remainStartTime }}</text>
                                            <text style="color: #2d2d2d">{{ $L('开始支付尾款') }}</text>
                                        </view>
                                        <view class="preSale_desc" v-if="allData.presellInfo.firstExpand">
                                            {{ $L('预售定金可抵') }}{{ allData.presellInfo.firstExpand }}{{ $L('元') }}，{{ $L('支付定金后尾款优惠')
                                            }}{{ allData.presellInfo.finalDiscount }}元
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </block>
                        <!-- 预售end -->

                        <!-- 阶梯团start -->
                        <block v-if="allData.promotionType == 105">
                            <view class="ladder_list b-b">
                                <text class="ladder_tip">{{ $L('其他优惠可在支付尾款时选择') }}</text>
                                <view class="ladder_process">
                                    <view class="process_level on">
                                        <view class="level_top">
                                            <view class="level_left">
                                                <svgGroup type="to_ladder_img" width="43" height="47" px="rpx" :color="diyStyle_var['--color_price']"></svgGroup>
                                                <text>{{ $L('定金支付') }}</text>
                                            </view>
                                            <view class="level_right">
                                                <text>
                                                    ￥{{ $getPartNumber(allData.ladderGroupInfo.advanceDeposit, 'int')
                                                    }}{{ $getPartNumber(allData.ladderGroupInfo.advanceDeposit, 'decimal') }}
                                                </text>
                                            </view>
                                        </view>
                                        <view class="level_gang"></view>
                                    </view>
                                    <view :class="{ process_level: true, on: allData.ladderGroupInfo.ladderGroupState == 102 }">
                                        <view class="level_gang"></view>
                                        <view class="level_top">
                                            <view class="level_left">
                                                <svgGroup
                                                    v-if="allData && allData.ladderGroupInfo && allData.ladderGroupInfo.ladderGroupState == 102"
                                                    type="to_commission"
                                                    width="43"
                                                    height="47"
                                                    px="rpx"
                                                    :color="diyStyle_var['--color_price']"
                                                ></svgGroup>
                                                <svgGroup v-else="" type="to_commission" width="43" height="47" px="rpx" color="#ddd"></svgGroup>
                                                <text>{{ $L('尾款结算') }}</text>
                                            </view>
                                            <view :class="{ level_right: allData.ladderGroupInfo.ladderGroupState == 102 }">
                                                <text v-if="allData.ladderGroupInfo.ladderGroupState == 102">
                                                    ￥{{ $getPartNumber(allData.ladderGroupInfo.remainAmount, 'int')
                                                    }}{{ $getPartNumber(allData.ladderGroupInfo.remainAmount, 'decimal') }}
                                                </text>
                                                <text v-else>--</text>
                                            </view>
                                        </view>
                                    </view>
                                    <view class="preDesc_con" style="margin-top: 48rpx">
                                        <view class="preSale_desc" v-if="allData.ladderGroupInfo.ladderLevel > 0">
                                            {{ $L('当前阶梯为第') }}{{ allData.ladderGroupInfo.ladderLevel }}{{ $L('阶梯，商品价格优惠') }}{{ allData.ladderGroupInfo.discount
                                            }}{{ $L('元') }}
                                        </view>
                                        <view class="preSale_desc" v-if="allData.ladderGroupInfo.ladderLevel == 0">
                                            {{ $L('未达到第一阶梯，不享受优惠') }}
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </block>
                        <!-- 阶梯团send -->

                        <!-- 付定金的时候不显示运费、优惠信息 -->
                        <template
                            v-if="
                                !(
                                    (allData.promotionType == 105 && allData.ladderGroupInfo.ladderGroupState == 101) ||
                                    (allData.promotionType == 103 && allData.presellInfo.presellState == 101)
                                ) ||
                                (allData.promotionType == 103 && allData.presellInfo.type == 2)
                            "
                        >
                            <view class="yt-list-cell b-b" v-if="preParam.isPickup == 0">
                                <text class="cell-tit clamp">{{ $L('运费') }}</text>
                                <text class="cell-tip">
                                    {{
                                        item.originalExpressFee
                                            ? $L('+￥') + $getPartNumber(item.originalExpressFee, 'int') + $getPartNumber(item.originalExpressFee, 'decimal')
                                            : $L('免运费')
                                    }}
                                </text>
                            </view>
                            <view class="yt-list-cell b-b">
                                <text class="cell-tit clamp">{{ $L('店铺总优惠') }}</text>
                                <text class="cell-tip">-{{ $L('￥') }}{{ $getPartNumber(item.totalDiscount, 'int') }}{{ $getPartNumber(item.totalDiscount, 'decimal') }}</text>
                            </view>
                        </template>
                        <view class="yt-list-cell b-b" v-if="goodsData.length > 1">
                            <text class="cell-tit clamp">{{ $L('小计') }}</text>
                            <text class="cell-tip">
                                ￥{{ $getPartNumber((item.totalAmount * 1000 + item.originalExpressFee * 1000) / 1000, 'int')
                                }}{{ $getPartNumber((item.totalAmount * 1000 + item.originalExpressFee * 1000) / 1000, 'decimal') }}
                            </text>
                        </view>
                    </view>
                    <!-- 特殊需求，填写姓名手机号 -->
                    <template v-if="needOtherInfo">
                        <view class="yt-list order_remark">
                            <view class="flex_row_start_center">
                                <text class="required">*</text>
                                <text class="title">姓名</text>
                            </view>
                            <input class="content-input" v-model="special.userName" type="text" maxlength="20" placeholder="请输入姓名" :adjust-position="true" />
                        </view>
                        <view class="yt-list order_remark">
                            <view class="flex_row_start_center">
                                <text class="required">*</text>
                                <text class="title">手机号</text>
                            </view>
                            <input class="content-input" v-model="special.userPhone" type="tel" maxlength="11" placeholder="请输入手机号" :adjust-position="true" />
                        </view>
                    </template>
                    <!-- 付定金的时候不备注 -->
                    <template
                        v-if="
                            !(
                                (allData.promotionType == 105 && allData.ladderGroupInfo.ladderGroupState == 101) ||
                                (allData.promotionType == 103 && allData.presellInfo.presellState == 101)
                            ) ||
                            (allData.promotionType == 103 && allData.presellInfo.type == 2)
                        "
                    >
                        <view class="yt-list order_remark">
                            <text class="title">{{ $L('订单备注') }}</text>
                            <!-- #ifndef MP -->
                            <textarea
                                :placeholder="$L('给商家留言,最多100字')"
                                v-model="item.remark"
                                :adjust-position="true"
                                placeholder-class="placeholder"
                                class="content uni-input"
                                maxlength="100"
                                cursor-spacing="10"
                                @focus="handleFocus"
                                @blur="handleBlur"
                                :data-index="index"
                                @input="inputRemark"
                            ></textarea>
                            <!-- #endif -->

                            <!-- #ifdef MP-ALIPAY||MP-BAIDU -->
                            <textarea
                                :placeholder="$L('给商家留言,最多100字')"
                                v-model="item.remark"
                                :adjust-position="true"
                                placeholder-class="placeholder"
                                class="content uni-input"
                                maxlength="100"
                                :enableNative="false"
                                cursor-spacing="20"
                                :show-count="false"
                                @focus="handleFocus"
                                @blur="handleBlur"
                                :data-index="index"
                                @input="inputRemark"
                            ></textarea>
                            <!-- #endif -->

                            <!-- wx-1-start -->
                            <!-- #ifdef MP-WEIXIN -->
                            <textarea
                                :placeholder="$L('给商家留言,最多100字')"
                                v-model="item.remark"
                                :adjust-position="true"
                                placeholder-class="placeholder"
                                class="content uni-input"
                                maxlength="100"
                                cursor-spacing="30"
                                @focus="handleFocus"
                                @blur="handleBlur"
                                :data-index="index"
                                @input="inputRemark"
                            ></textarea>
                            <!-- #endif -->
                            <!-- wx-1-end -->
                        </view>
                    </template>
                </view>
                <!-- 发票、平台优惠券(阶梯团付定金的时候不显示发票、平台优惠券) -->
                <template
                    v-if="
                        !(
                            (allData.promotionType == 105 && allData.ladderGroupInfo.ladderGroupState == 101) ||
                            (allData.promotionType == 103 && allData.presellInfo.presellState == 101 && allData.presellInfo.type != 2)
                        )
                    "
                >
                    <view class="yt-list" v-if="spe_invoice">
                        <view class="yt-list-cell b-b">
                            <text class="cell-tit clamp">{{ $L('发票') }}</text>
                            <text class="cell-tip voice" v-if="spe_invoice.status == 1">暂不支持开发票</text>
                            <view class="cell-tip voice" v-if="spe_invoice.status == 2">{{ spe_invoice.tips }}</view>
                        </view>
                    </view>
                    <view class="yt-list" v-else>
                        <view class="yt-list-cell b-b" @click="toInvoice">
                            <text class="cell-tit clamp">{{ $L('发票') }}</text>
                            <text class="cell-tip voice" v-if="invoice_info == '' || invoice_info == '不需要发票'">{{ $L('不需要发票') }}</text>
                            <view class="cell-tip voice" v-else>
                                <text class="limVoice">{{ invoice_info }}</text>
                                <text>{{ invoice_content == 1 ? $L('商品明细') : $L('商品类别') }}</text>
                            </view>
                            <text class="iconfont iconziyuan11"></text>
                        </view>
                        <template v-if="!allData.showPromotion">
                            <!-- 暂无平台优惠券 -->
                            <view class="yt-list-cell b-b" v-if="platformCouponList.length == 0">
                                <text class="cell-tit clamp">{{ $L('平台优惠券') }}</text>
                                <text class="cell-tip voice">{{ $L('暂无优惠券') }}</text>
                                <text class="iconfont iconziyuan11"></text>
                            </view>
                            <!-- 平台优惠券 -->
                            <view class="yt-list-cell b-b" v-else @click="select_platform_red">
                                <text class="cell-tit clamp">{{ $L('平台优惠券') }}</text>
                                <text class="cell-tip">{{ platformCouponCodeText ? '-￥' + filter.toFix(platformCouponCodeText) : platformCouponCodeText }}</text>
                                <text class="iconfont iconziyuan11"></text>
                            </view>
                        </template>

                        <template v-if="preParam.isPickup == 0">
                            <!-- 暂无运费券 -->
                            <view class="yt-list-cell b-b" v-if="freightCoupon.list.length == 0">
                                <text class="cell-tit clamp">{{ $L('运费券') }}</text>
                                <text class="cell-tip voice">{{ $L('暂无运费券') }}</text>
                                <text></text>
                            </view>
                            <!-- 平台运费券 -->
                            <view class="yt-list-cell b-b" v-else @click="freightCoupon.showFlag = true">
                                <text class="cell-tit clamp">{{ $L('运费券') }}</text>
                                <text class="cell-tip" v-if="freightCoupon.couponCode">-￥{{ filter.toFix(freightCoupon.couponCodeText) }}</text>
                                <text class="cell-tip voice" v-else>不选择运费券</text>
                                <text class="iconfont iconziyuan11"></text>
                            </view>
                        </template>

                        <!-- 积分抵现 -->
                        <!-- <view class="yt-list-cell b-b" @click="select_integral" v-if="intRuleList[0] == 1 && allData.integralList.length">
					<view class="cell_int">
						<text class="cell-tit clamp">{{ $L('积分抵现') }}</text>
						<image :src="imgUrl + 'order/int_ques.png'" mode="aspectFit" @click.stop="showIntRule"></image>
					</view>
					<text class="cell-tip" v-if="allData.integralList.length && Number(allData.totalAmount) >= Number(intRuleList[2])">
						{{ integral > 0 ? `${$L('积分抵现')}：¥${integral / allData.integralScale}` : `${$L('可用积分')}：${allData.memberIntegral}` }}
					</text>
					<block v-else>
						<text class="cell-tip disabled" v-if="Number(allData.totalAmount) * Number(intRuleList[3] * 0.01) < Number(intRuleList[2])">
							{{ $L('订单金额') }}：{{ allData.totalAmount }}，{{ $L('满') }}{{ Number(intRuleList[2]).toFixed(2) }}{{ $L('可用') }}
						</text>
						<text class="cell-tip disabled" v-else>{{ $L('当前积分') }}：{{ allData.memberIntegral }}，{{ $L('满') }}{{ allData.integralScale }}{{ $L('可用') }}</text>
					</block>
				</view> -->
                    </view>
                </template>

                <template v-if="allData.promotionType == 105 && allData.ladderGroupInfo.ladderGroupState == 101">
                    <view class="agreement-part flex_row_start_center">
                        <!-- <image @click="checkAgrement" class="register_icon" :src="show_check_icon" mode="aspectFill" /> -->
                        <text class="iconfont iconkebianse_weixuanzhong register_icon" v-if="!show_check_icon" @click="checkAgrement"></text>
                        <text class="iconfont iconkebianse_xuanzhong register_icon" style="color: $color1" @click="checkAgrement" v-else></text>
                        {{ $L('我已阅读并同意') }}
                        <text class="agreement" @click="agreement('ladder')">{{ $L('《阶梯团定金协议》') }}</text>
                    </view>
                </template>

                <template v-if="allData.promotionType == 103 && allData.presellInfo.presellState == 101 && allData.presellInfo.type != 2">
                    <view class="agreement-part flex_row_start_center">
                        <text class="iconfont iconkebianse_weixuanzhong register_icon" v-if="!show_check_icon" @click="checkAgrement"></text>
                        <text class="iconfont iconkebianse_xuanzhong register_icon" style="color: $color1" @click="checkAgrement" v-else></text>
                        {{ $L('我已阅读并同意') }}
                        <text class="agreement" @click="agreement('preSale')">{{ $L('《预售定金协议》') }}</text>
                    </view>
                </template>

                <view v-if="userCenterData.isSuper && allData.superDiscount >= 0 && allData.superDiscount != undefined" class="bottom_super flex_row_between_center">
                    <view class="bottom_super_left flex_row_start_center">
                        <image :src="imgUrl + 'user/vip.png'" mode="aspectFit"></image>
                        <span>会员折扣： 购物享{{ allData.superDiscount }}折</span>
                    </view>
                    <view class="bottom_super_right">-￥{{ allData.superDiscountAmount }}</view>
                </view>
            </view>
            <view class="empty_h"></view>

            <!-- 使用店铺优惠券(注：阶梯团付定金的时候不可以使用优惠券) -->
            <view id="store_red_wrap" @tap="hide_store_red" v-if="store_show_flag" @touchmove.stop.prevent="moveHandle">
                <view class="store_red">
                    <view class="title">{{ $L('使用店铺优惠券') }}</view>
                    <scroll-view scroll-y class="store_red_list">
                        <view v-for="(reditem, index) in currentStore.availableCouponList" :key="index" class="ticket-item">
                            <view class="circle_radio a" :for="'red_id_' + index" :data-id="index">
                                <view class="red_item_wrap">
                                    <view class="red_h1" v-if="reditem.couponType == 2">
                                        <text>{{ reditem.value }}</text>
                                        {{ $L('折') }}
                                    </view>
                                    <view class="red_h1" v-else>
                                        <text>{{ reditem.value }}</text>
                                        {{ $L('元') }}
                                    </view>
                                    <view class="red_h2">
                                        <view>
                                            <block>
                                                {{ reditem.content }}
                                            </block>
                                        </view>
                                        <view>
                                            {{ reditem.useTime }}
                                        </view>
                                    </view>
                                    <view class="red_h3">
                                        <view class="red_h3_top">
                                            <image :src="imgUrl + 'ok_w.png'"></image>
                                            <text>{{ $L('您已领券') }}</text>
                                        </view>
                                        <view class="red_h3_bottom" @tap="store_red(reditem.couponCode)">
                                            {{ currentStore.storeCouponCode == reditem.couponCode ? '已选择' : '点击使用' }}
                                        </view>
                                    </view>
                                </view>
                            </view>
                            <view class="line_left"></view>
                            <view class="line_right"></view>
                            <view class="red_p">
                                <text style="margin-left: 40rpx">{{ reditem.description }}</text>
                            </view>
                        </view>
                    </scroll-view>
                </view>
            </view>

            <view id="platform_red_wrap" @tap="hide_platform_red" v-if="store_show_flag_platform">
                <view class="store_red">
                    <view class="title">{{ $L('使用平台优惠券') }}</view>
                    <scroll-view scroll-y class="store_red_list">
                        <view v-for="(reditem, index) in platformCouponList" :key="index" class="ticket-item">
                            <view class="circle_radio a" :for="'red_id_' + index" :data-id="index">
                                <view class="red_item_wrap">
                                    <view class="red_h1" v-if="reditem.couponType == 2">
                                        <text>{{ reditem.value }}</text>
                                        {{ $L('折') }}
                                    </view>
                                    <view class="red_h1">
                                        <text>{{ reditem.value }}</text>
                                        {{ $L('元') }}
                                    </view>
                                    <view class="red_h2">
                                        <view>
                                            <block>
                                                {{ reditem.content }}
                                            </block>
                                        </view>
                                        <view>
                                            {{ reditem.useTime }}
                                        </view>
                                    </view>
                                    <view class="red_h3">
                                        <view class="red_h3_top">
                                            <image :src="imgUrl + 'ok_w.png'"></image>
                                            <text>{{ $L('您已领券') }}</text>
                                        </view>
                                        <view class="red_h3_bottom" @tap="platform_red(reditem.couponCode)">
                                            {{ platformCouponCode == reditem.couponCode ? $L('已选择') : $L('点击使用') }}
                                        </view>
                                    </view>
                                </view>
                            </view>
                            <view class="line_left"></view>
                            <view class="line_right"></view>
                            <view class="red_p">
                                <text style="margin-left: 40rpx">{{ reditem.description }}</text>
                            </view>
                        </view>
                    </scroll-view>
                </view>
            </view>

            <view id="freight_red_wrap" @tap="freightCoupon.showFlag = false" v-if="freightCoupon.showFlag">
                <view class="store_red">
                    <view class="title">{{ $L('使用运费券') }}</view>
                    <scroll-view scroll-y class="store_red_list">
                        <view v-for="(reditem, index) in freightCoupon.list" :key="index" class="ticket-item">
                            <view class="circle_radio a" :for="'red_id_' + index" :data-id="index">
                                <view class="red_item_wrap">
                                    <view class="red_h1">
                                        <text v-if="reditem.value">{{ reditem.value }}{{ $L('元') }}</text>
                                        <text v-else>免运费</text>
                                    </view>
                                    <view class="red_h2">
                                        <view>
                                            <block>
                                                {{ reditem.content }}
                                            </block>
                                        </view>
                                        <view>
                                            {{ reditem.useTime }}
                                        </view>
                                    </view>
                                    <view class="red_h3">
                                        <view class="red_h3_top">
                                            <image :src="imgUrl + 'ok_w.png'"></image>
                                            <text>{{ $L('您已领券') }}</text>
                                        </view>
                                        <view class="red_h3_bottom" @tap="freight_red(reditem.couponCode)">
                                            {{ freightCoupon.couponCode == reditem.couponCode ? $L('已选择') : $L('点击使用') }}
                                        </view>
                                    </view>
                                </view>
                            </view>
                            <view class="line_left"></view>
                            <view class="line_right"></view>
                            <view class="red_p">
                                <text style="margin-left: 40rpx">{{ reditem.description }}</text>
                            </view>
                        </view>
                    </scroll-view>
                </view>
            </view>
        </view>
        <!-- 底部 -->
        <view class="footer flex_row_between_center" v-if="isBottomShow">
            <view class="price-content flex_column_center_end">
                <view class="should_pay flex_row_end_end">
                    <text class="tit">{{ $L('待付金额') }}：</text>
                    <text class="unit">{{ $L('￥') }}</text>
                    <text class="big_price">{{ (allData.totalAmount + '').split('.')[0] }}.</text>
                    <text class="small_price">{{ (allData.totalAmount + '').split('.')[1] != undefined ? (allData.totalAmount + '').split('.')[1] : '00' }}</text>
                </view>
            </view>
            <text class="submit flex_row_center_center" @click="submitOrder">{{ orderSn ? $L('去付尾款') : $L('提交订单') }}</text>
        </view>
        <!-- 积分抵现弹框 -->
        <uni-popup ref="integralModel" type="bottom">
            <view class="address_list_con">
                <view class="address_top">
                    <view class="address_top_text">{{ $L('积分抵现') }}</view>
                    <image :src="imgUrl + 'goods_detail/close.png'" mode="aspectFit" @click="conInt('close')"></image>
                </view>
                <view class="member_int">
                    <view>{{ $L('可用积分') }}：{{ allData.memberIntegral }}</view>
                </view>
                <scroll-view scroll-y="true" class="address_list" @touchmove.stop.prevent="moveHandle">
                    <radio-group>
                        <view v-for="(item, index) in allData.integralList" :key="index" class="list" @click="selInt(item)">
                            <view class="wrapper flex_row_start_center">
                                <view class="flex_column_start_start">
                                    <view class="address-box">
                                        <text class="int_desc">{{ $L('抵扣') }}</text>
                                        <text class="int_desc" style="color: $color1">¥{{ item / allData.integralScale }}</text>
                                        <text class="int_desc">{{ $L('使用') }}</text>
                                        <text class="int_desc" style="color: $color1">{{ item }}</text>
                                        <text class="int_desc">{{ $L('积分') }}</text>
                                    </view>
                                </view>
                            </view>
                            <label class="wrapper_right">
                                <radio :value="item.toString()" color=" $color1" :checked="item == tmpInt" :disabled="item > allData.memberIntegral" />
                            </label>
                        </view>
                    </radio-group>
                </scroll-view>
                <view class="other_address">
                    <view class="integral_opt">
                        <view class="no_int" @click="conInt('noInt')">{{ $L('暂不使用积分') }}</view>
                        <view class="int_con" @click="conInt('confirm')">{{ $L('确定') }}</view>
                    </view>
                </view>
            </view>
        </uni-popup>
        <!-- 积分规则 -->
        <uni-popup ref="intRule" type="center">
            <view class="intRule_box">
                <view class="int_title">{{ $L('使用规则') }}</view>
                <view class="int_content">
                    <view>
                        {{ $L('订单大于等于') }}
                        <text style="color: $color1">{{ intRuleList[2] }}{{ $L('元') }}</text>
                        {{ $L('可用') }};
                    </view>
                    <view>
                        {{ $L('积分支付不超过订单金额的') }}
                        <text style="color: $color1">{{ intRuleList[3] }}%;</text>
                    </view>
                    <view>
                        {{ $L('积分使用数量为') }}
                        <text style="color: $color1">{{ intRuleList[1] }}</text>
                        {{ $L('的整数倍') }};
                    </view>
                    <view>
                        {{ intRuleList[1] }}{{ $L('积分等于') }}
                        <text style="color: $color1">1{{ $L('元') }}</text>
                        ;
                    </view>
                </view>
            </view>
        </uni-popup>
        <purchasePop ref="purchasePop" :exList="exceptionProList" :exState="exState" :exStateTxt="exStateTxt" @goNext="goNext" @delNext="delNext"></purchasePop>
        <!-- 选择店铺组件 -->
        <storeModel ref="storeModelPopup" :storeList="storeList" @chooseOver="chooseOver" />
        <!-- 店铺提示 -->
        <tipsModel ref="tipsModelPopup" openType="center" />
    </view>
</template>

<script>
import { mapState } from 'vuex';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue';
import purchasePop from '@/components/purchasePop.vue';
import filter from '@/utils/filter.js';
import storeModel from './storeModel.vue';
import tipsModel from './storeTips.vue';
import ktabbar from '@/components/ktabbar.vue';
export default {
    components: {
        ktabbar,
        uniPopup,
        uniPopupDialog,
        purchasePop,
        storeModel,
        tipsModel
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            loadFlag: false,
            dian: null, //商品店铺信息
            needOtherInfo: '', //是否需要填写其他信息
            special: { userName: '', userPhone: '' }, //其他信息
            spe_invoice: null, //发票是否特殊处理
            preParam: {}, //上一个页面的参数
            orderAddress: {}, //下单所用的地址信息
            goodsData: [], //商品数据
            goodsRemark: [], //商品订单备注
            allData: {}, //确认下单返回的所有数据信息
            maskState: 0, //优惠券面板显示状态
            desc: '', //备注
            payType: 1, //1微信 2支付宝
            isBottomShow: true, //底部是否显示
            windowHeight: '',
            remark: '',
            cartIds: '', //购物车id集合
            ifOnShow: false,
            invoice_info: '', //发票信息
            invoice_content: '',
            invoiceId: '', //发票id
            store_show_flag: false,
            currentStore: {},
            platformCouponList: [], //平台优惠券列表
            platformCouponCode: '',
            platformCouponCodeText: '',
            store_show_flag_platform: false,

            freightCoupon: {
                list: [],
                couponCode: '',
                couponCodeText: '',
                showFlag: false
            },

            totalDiscount: '',
            no_good_info: {},
            timer: '',
            isVatInvoice: true,
            orderSn: '', //订单号，目前阶梯团付尾款的时候会用到
            check_agreement: false,
            show_check_icon: false,
            isAloneBuy: false, //拼团是否单独购买
            spellTeamId: 0,
            spreaderMemberId: 0, //专门的推手分享ID设置
            integral: 0,
            intRuleList: [],
            tmpInt: 0,
            overFlowInterval: true,
            //虚拟商品的相关字段
            isVG: null,
            virtualPre: [],
            reserveInfoList: [],
            isPreventClick: false,
            exceptionProList: [],
            exState: 0,
            exStateTxt: '',
            showState: '',
            filter,
            position: {},
            storeList: [], //自提点列表
            choosedItem: {},
            isShowTime: false, // 确认订单是否选择预约时间
            openDateTime: '', //商品预售时间数据
            selDateTime: null //选中的时间
        };
    },
    async onLoad(option) {
        setTimeout(() => {
            uni.setNavigationBarTitle({
                title: this.$L('确认订单')
            });
        }, 0);

        //商品数据
        this.getAddressList();
        this.getIntRule();
        this.preParam = this.$Route.query;
        this.orderSn = this.$Route.query.orderSn != undefined && this.$Route.query.orderSn ? this.$Route.query.orderSn : '';
        this.isAloneBuy = this.$Route.query.isAloneBuy ? this.$Route.query.isAloneBuy : false;
        this.spellTeamId = this.$Route.query.spellTeamId ? this.$Route.query.spellTeamId : 0;
        let spreaderTmp = uni.getStorageSync('spreaderId');
        if (spreaderTmp) {
            this.spreaderMemberId = spreaderTmp;
        }
        const invoice_msg = uni.getStorageSync('invoice_info');

        if (invoice_msg != '') {
            if (invoice_msg.invoice_title) {
                this.invoice_info = invoice_msg.invoice_title + ' ';
                this.invoice_content = invoice_msg.invoice_content;
            } else {
                this.invoice_info = invoice_msg.company_name + ' ';
                this.invoice_content = invoice_msg.invoice_content;
            }
            this.invoiceId = invoice_msg.invoiceId;
        }

        // #ifndef MP-BAIDU
        uni.onWindowResize(function (res) {
            let windowHeight = uni.getSystemInfoSync().windowHeight;

            if (res.size.windowHeight < this.windowHeight) {
                this.isBottomShow = false;
            } else {
                this.isBottomShow = true;
            }
        });
        // #endif
    },
    computed: {
        ...mapState(['hasLogin', 'userInfo', 'userCenterData'])
    },
    onHide() {
        this.ifOnShow = true;
        //清除定时器
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    },

    onUnload() {
        //清除定时器
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    },
    onShow() {
        if (this.ifOnShow) {
            const is_need_invoice = uni.getStorageSync('is_need_invoice');
            const invoice_msg = uni.getStorageSync('invoice_info');
            if (!is_need_invoice) {
                this.invoice_info = this.$L('不需要发票');
            }
            if (invoice_msg != '') {
                if (invoice_msg.invoice_title) {
                    this.invoice_info = invoice_msg.invoice_title + ' ';
                    this.invoice_content = invoice_msg.invoice_content;
                } else {
                    this.invoice_info = invoice_msg.company_name + ' ';
                    this.invoice_content = invoice_msg.invoice_content;
                }
                this.invoiceId = invoice_msg.invoiceId;
            }
        }
        if (this.showState == 'from_address') {
            this.getAddressList();
        }
    },

    onBackPress() {
        uni.removeStorageSync('goodsRemark');
        uni.removeStorageSync('invoice_info');
        uni.removeStorageSync('is_need_invoice');
    },

    methods: {
        /*选择时间*/
        selectTime() {
            this.$refs.selectTimeModelPopup.open(this.selDateTime);
        },
        // 确认时间选择
        chooseTimeOver(selectedValue) {
            this.selDateTime = selectedValue;
        },
        openModel() {
            this.$refs.storeModelPopup.open();
        },
        chooseOver(ids) {
            this.choosedItem = this.storeList.find((item) => item.id == ids[0]);
        },
        tonavigation() {
            let { position } = this;

            uni.openLocation({
                latitude: Number(position.latitude), //目标纬度
                longitude: Number(position.longitude), //目标经度
                name: position.name, //名称
                address: position.address, //地址
                success: function () {
                    console.log('success');
                }
            });
        },
        // 获取提交订单特殊配置
        getConfirmSetingJson(store) {
            let _base = 'https://qdairport.lightcloudapps.com';
            // #ifdef MP-WEIXIN
            const Appversion = __wxConfig.envVersion;
            if (Appversion == 'release') {
                _base = 'https://disp.qdairport.com';
            }
            // #endif
            uni.request({
                url: _base + '/jcshop/mp-qq/data/speStore.json',
                method: 'GET',
                header: { 'content-type': 'application/x-www-form-urlencoded' },
                success: (res) => {
                    if (res.statusCode == 200) {
                        this.needOtherInfo = res.data.data.name.includes(store.storeId);
                        this.spe_invoice = res.data.data.invoice[`s_${store.storeId}`] || null;
                    }
                },
                fail: (err) => {}
            });
        },
        //阶梯团定金协议点击事件
        checkAgrement() {
            this.check_agreement = !this.check_agreement;
            this.show_check_icon = this.check_agreement ? true : false;
        },
        agreement(type) {
            if (type == 'ladder') {
                this.$Router.push('/standard/ladder/agreement/agreement');
            } else if (type == 'preSale') {
                this.$Router.push('/standard/presale/agreement/agreement');
            }
        },
        operateAddress() {
            this.showState = 'from_address';
            this.$Router.push({
                path: `/newPages/address/list`,
                query: {
                    source: 1,
                    sourceId: this.orderAddress.addressId != undefined ? this.orderAddress.addressId : ''
                }
            });
        },
        moveHandle() {},

        // 隐藏优惠券弹窗
        hide_store_red(e) {
            this.store_show_flag = false;
        },
        // 显示店铺优惠券弹窗
        select_store_red(store) {
            let store_info = JSON.stringify(store);
            this.store_show_flag = true;
            this.currentStore = JSON.parse(store_info);
        },
        //选择店铺优惠券
        store_red(couponCode) {
            if (this.currentStore.storeCouponCode == couponCode) {
                this.currentStore.storeCouponCode = '';
            } else {
                this.currentStore.storeCouponCode = couponCode;
            }
            this.confirmOrder(2);
        },
        // h隐藏平台优惠券弹窗
        hide_platform_red(e) {
            this.store_show_flag_platform = false;
        },
        // 显示平台优惠券弹窗
        select_platform_red() {
            this.store_show_flag_platform = true;
        },
        select_integral() {
            if (!this.allData.integralList.length && Number(allData.totalAmount) >= this.intRuleList[2]) {
                return;
            }
            this.tmpInt = this.integral;
            this.$refs.integralModel.open();
        },
        showIntRule() {
            this.$refs.intRule.open();
        },
        //选择平台优惠券
        platform_red(platformCouponCode) {
            if (this.platformCouponCode == platformCouponCode) {
                this.platformCouponCode = '';
            } else {
                this.platformCouponCode = platformCouponCode;
            }
            this.confirmOrder(2);
        },

        freight_red(couponCode) {
            if (this.freightCoupon.couponCode == couponCode) {
                this.freightCoupon.couponCode = '';
            } else {
                this.freightCoupon.couponCode = couponCode;
            }
            this.confirmOrder(2);
        },

        //获取自提点列表
        getPickupPointList(storeId) {
            this.$request({
                url: 'v3/seller/front/store/pickupPointList',
                method: 'GET',
                data: { pageSize: 1000, current: 1, storeId }
            }).then((res) => {
                if (res.state == 200) {
                    this.storeList = res.data.list;
                }
            });
        },
        //获取地址列表
        getAddressList() {
            this.$request({
                url: 'v3/member/front/memberAddress/list',
                method: 'GET'
            })
                .then((res) => {
                    if (res.state == 200) {
                        if (res.data.list.length > 0) {
                            if (uni.getStorageSync('addressId')) {
                                let addressID = uni.getStorageSync('addressId');
                                let used_index = res.data.list.findIndex((i) => i.addressId == addressID);
                                if (used_index > -1) {
                                    this.orderAddress = res.data.list[used_index];
                                }
                            } else {
                                let defaultAddress = res.data.list.find((i) => i.isDefault == 1);
                                if (defaultAddress) {
                                    this.orderAddress = defaultAddress;
                                } else {
                                    this.orderAddress = res.data.list[0];
                                }
                            }
                        } else {
                            this.orderAddress = {};
                        }
                        this.confirmOrder(1);
                    } else {
                        this.confirmOrder(1);
                        this.$api.msg(res.msg);
                    }
                })
                .catch((e) => {
                    //异常处理
                });
        },
        returnLastPage(state) {
            this.$Router.back(1);
        },

        selInt(e) {
            this.tmpInt = e;
        },
        conInt(type) {
            switch (type) {
                case 'confirm': {
                    this.integral = this.tmpInt;
                    this.$refs.integralModel.close();
                    this.confirmOrder(2);
                    break;
                }
                case 'close': {
                    this.$refs.integralModel.close();
                    break;
                }
                case 'noInt': {
                    this.integral = 0;
                    this.tmpInt = 0;
                    this.confirmOrder(2);
                    this.$refs.integralModel.close();
                    break;
                }
            }
        },

        // 输入订单备注信息
        inputRemark(e) {
            let index = e.currentTarget.dataset.index;
            let val = e.detail.value;
            this.goodsRemark[index] = val;
            uni.setStorageSync('goodsRemark', JSON.stringify(this.goodsRemark));
        },

        //用于切换地址，使用优惠券，获取信息，运费等
        confirmOrder(type) {
            let goodsRemark = uni.getStorageSync('goodsRemark');
            if (goodsRemark) {
                goodsRemark = JSON.parse(goodsRemark);
            }
            const { preParam } = this;
            let param = {};
            let url = this.orderSn ? 'v3/business/front/orderOperate/balanceConfirm' : 'v3/business/front/orderOperate/confirm';
            param.url = url;
            param.method = 'POST';
            param.data = {};
            param.header = {
                'Content-Type': 'application/json'
            };
            param.data.addressId = this.orderAddress.addressId;
            param.data.platformCouponCode = this.platformCouponCode;
            param.data.couponFreightCode = this.freightCoupon.couponCode;
            param.data.orderSn = this.orderSn ? this.orderSn : '';
            param.data.isAloneBuy = this.isAloneBuy; //拼团商品是否单独购买
            if (this.spellTeamId != 0) {
                param.data.spellTeamId = this.spellTeamId;
            }
            if (this.integral > 0) {
                param.data.integral = this.integral;
            }

            let storeInfoList = [];
            this.goodsData.map((item) => {
                let storeitem = {};
                storeitem.invoiceId = this.invoiceId;
                storeitem.invoiceContent = this.invoice_content;
                if (this.currentStore.storeId == item.storeId) {
                    storeitem.remark = this.currentStore.remark;
                    storeitem.storeCouponCode = this.currentStore.storeCouponCode;
                    storeitem.storeId = this.currentStore.storeId;
                } else {
                    storeitem.remark = item.remark;
                    storeitem.storeCouponCode = item.storeCouponCode;
                    storeitem.storeId = item.storeId;
                }
                storeInfoList.push(storeitem);
            });
            param.data.storeInfoList = storeInfoList;
            param.data.source = type;
            if (preParam.ifcart == 1) {
                //来自于购物车
                param.data.isCart = true;
            } else {
                //立即购买

                param.data.productId = preParam.productId;
                param.data.number = preParam.numbers;
                param.data.isCart = false;
            }
            param.data.isPickup = preParam.isPickup;
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    let result = res.data;
                    if ((this.preParam.isPickup == 2 || this.preParam.isPickup == 3) && !this.storeList.length) {
                        this.getPickupPointList(res.data.dian.storeId);
                    }
                    if (type == 1) {
                        this.goodsData = result.storeGroupList;
                        //处理店铺优惠券
                        this.goodsData.map((item, index) => {
                            let default_coupon = item.availableCouponList.filter(function (coupon) {
                                return coupon.checked == true;
                            });
                            if (default_coupon.length > 0) {
                                this.$set(item, 'storeCouponCode', default_coupon[0].couponCode);
                                this.$set(item, 'storeCouponCodeText', default_coupon[0].discount);
                            } else {
                                this.$set(item, 'storeCouponCode', '');
                                this.$set(item, 'storeCouponCodeText', '');
                            }
                            if (!goodsRemark) {
                                this.goodsRemark.push('');
                            }
                        });
                    } else {
                        let tmpList = result.storeGroupList;
                        this.goodsData.map((item, index) => {
                            let default_coupon = tmpList[index].availableCouponList.filter((coupon) => coupon.checked == true);
                            if (default_coupon.length > 0) {
                                item.storeCouponCode = default_coupon[0].couponCode;
                                item.storeCouponCodeText = default_coupon[0].discount;
                            } else {
                                item.storeCouponCode = '';
                                item.storeCouponCodeText = '';
                            }
                            if (goodsRemark) {
                                this.$set(item, 'remark', goodsRemark[index] ? goodsRemark[index] : '');
                            } else {
                                this.goodsRemark.push('');
                            }
                            item.originalExpressFee = tmpList[index].originalExpressFee;
                            item.totalDiscount = tmpList[index].totalDiscount;
                        });
                    }
                    // 处理是否需要填写特殊字段
                    this.getConfirmSetingJson(result.dian);
                    this.isVatInvoice = result.isVatInvoice;
                    this.totalDiscount = result.totalDiscount;
                    //处理平台优惠券
                    this.platformCouponList = result.availableCouponList;
                    let default_plat_coupon = this.platformCouponList.filter(function (coupon) {
                        return coupon.checked == true;
                    });
                    if (default_plat_coupon.length > 0) {
                        this.platformCouponCodeText = default_plat_coupon[0].discount;
                        this.platformCouponCode = default_plat_coupon[0].couponCode;
                    } else {
                        this.platformCouponCodeText = '';
                        this.platformCouponCode = '';
                    }
                    //处理运费券
                    this.freightCoupon.list = result.availableFreightCouponList;
                    let freightCoupon = result.availableFreightCouponList.find((cp) => cp.checked);
                    this.freightCoupon.couponCodeText = freightCoupon ? freightCoupon.discount : '';
                    this.freightCoupon.couponCode = freightCoupon ? freightCoupon.couponCode : '';

                    this.allData = result;
                    //需求 职工超市提交订单弹出提示
                    if (result.dian.storeId == 530002) {
                        uni.request({
                            url: this.imgUrl + 'data/zhigong_config.json',
                            method: 'GET',
                            dataType: 'json',
                            header: {
                                'Content-Type': 'application/json'
                            },
                            success: (rest) => {
                                if (rest && rest.statusCode == 200 && rest.data.data.showPop) {
                                    this.$refs.tipsModelPopup && this.$refs.tipsModelPopup.open(rest.data.data.content);
                                }
                            }
                        });
                    }
                    if (Number(this.preParam.isPickup) === 1) {
                        const position = {
                            longitude: result.dian.lng,
                            latitude: result.dian.lat,
                            name: result.dian.areaInfo,
                            address: result.dian.address
                        };
                        this.position = position;
                    }
                    this.loadFlag = true;

                    // 虚拟商品相关字段
                    if (type == 1) {
                        this.isVG = result.isVirtualGoods;
                        if (this.orderSn) {
                            this.virtualPre = result.orderReserveList;
                        } else {
                            this.virtualPre = result.reserveNameList.map((item) => {
                                item.reserveValue = '';
                                return item;
                            });
                        }
                    }
                } else {
                    this.$api.msg(res.msg);
                }
            });
        },
        //显示优惠券面板
        toggleMask(type) {
            let timer = type === 'show' ? 10 : 300;
            let state = type === 'show' ? 1 : 0;
            this.maskState = 2;
            setTimeout(() => {
                this.maskState = state;
            }, timer);
        },
        numberChange(data) {
            this.number = data.number;
        },
        changePayType(type) {
            this.payType = type;
        },
        clearFailureGoods() {
            let param = {};
            param.url = 'v3/business/front/cart/emptyInvalid';
            param.method = 'POST';
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    this.submit();
                }
            });
        },

        submitOrder() {
            let { testConfirmOrder, $preventMutiClick } = this;
            $preventMutiClick(testConfirmOrder, this);
        },

        //确认订单前，检验商品是否可结算
        testConfirmOrder(continueExec) {
            const { preParam } = this;
            let param = {};
            param.method = 'POST';

            param.data = {};
            param.header = {
                'Content-Type': 'application/json'
            };
            this.reserveInfoList = [];
            if (this.isVG == 2 && this.virtualPre.length > 0 && !this.orderSn) {
                for (let i = 0; i < this.virtualPre.length; i++) {
                    let { reserveName, reserveType, reserveValue, reserveNameId, isRequired } = this.virtualPre[i];
                    if (this.checkPreMsg(reserveType, reserveValue, reserveName, isRequired)) {
                        this.reserveInfoList.push({
                            reserveName,
                            reserveValue,
                            reserveNameId
                        });
                    } else {
                        continueExec();
                        return;
                    }
                }
            }

            if (this.allData.promotionType == 105 && this.allData.ladderGroupInfo.ladderGroupState == 101) {
                //阶梯团付定金阶段
                if (!this.check_agreement) {
                    continueExec();
                    this.$api.msg(this.$L('请同意阶梯团定金协议!'));
                    return false;
                }
            } else if (this.allData.promotionType == 103 && this.allData.presellInfo.presellState == 101 && this.allData.presellInfo.type != 2) {
                //阶梯团付定金阶段
                if (!this.check_agreement) {
                    continueExec();
                    this.$api.msg(this.$L('请同意预售定金协议!'));
                    return false;
                }
            }
            // if(preParam.isPickup == 2 && Object.keys(this.choosedItem) == 0){
            // 	this.$api.msg(this.$L('选择自提点'))
            // 	return
            // }
            if (this.orderSn) {
                //阶梯团付尾款时用
                param.url = 'v3/business/front/orderOperate/balanceConfirm';
                preParam.isCart = 'false'; //目的是要让param.data.isCart为false
                param.data.source = 2;
                param.data.orderSn = this.orderSn;
            } else {
                param.url = 'v3/business/front/orderOperate/check';
            }
            if (preParam.ifcart == 1) {
                param.data.isCart = true;
            } else {
                let { price } = this.goodsData[0].productList[0];
                let { promotionId, promotionType } = this.allData;

                param.data.isCart = false;
                param.data.productId = preParam.productId;
                param.data.number = preParam.numbers;
                param.data.promotionId = promotionId;
                param.data.promotionType = promotionType;

                //针对阶梯团，预售的producPrice传值的处理
                switch (promotionType) {
                    case 103: {
                        let { type, firstPrice, secondPrice, presellState } = this.allData.presellInfo;

                        switch (type) {
                            case 1: {
                                if (presellState == 101) {
                                    param.data.productPrice = firstPrice;
                                } else {
                                    param.data.productPrice = secondPrice;
                                }
                                break;
                            }
                            case 2: {
                                param.data.productPrice = price;
                                break;
                            }
                        }

                        break;
                    }

                    case 105: {
                        let { firstPrice, secondPrice, remainAmount, ladderGroupState } = this.allData.ladderGroupInfo;

                        if (ladderGroupState == 101) {
                            param.data.productPrice = firstPrice;
                        } else {
                            param.data.productPrice = secondPrice;
                        }

                        break;
                    }

                    default: {
                        param.data.productPrice = price;
                        break;
                    }
                }

                if (this.isAloneBuy && promotionType == '102') {
                    delete param.data.promotionId;
                    delete param.data.promotionType;
                }

                if (this.spellTeamId != 0) {
                    param.data.spellTeamId = this.spellTeamId;
                }
            }
            if (this.isVG == 2) {
                param.data.reserveInfoList = this.reserveInfoList;
            }

            param.data.isAloneBuy = this.isAloneBuy;
            param.data.isPickup = preParam.isPickup;
            this.$request(param).then((res) => {
                if (res.state == 267) {
                    this.exState = res.data.state;
                    this.exceptionProList = res.data.productList;
                    this.exStateTxt = res.data.stateValue;
                    if (this.exState == 7 || this.exState == 4 || this.exState == 3 || this.exState == 2 || this.exState == 1) {
                        this.$refs.purchasePop.open(0);
                    } else if (this.exState == 5) {
                        this.$refs.purchasePop.open(1);
                    }
                    continueExec();
                } else if (res.state == 200) {
                    this.submit(continueExec);
                } else if (res.state == 255) {
                    this.$api.msg(res.msg);
                    continueExec();
                }
            });
        },

        delNext() {
            this.clearFailureGoods();
        },

        goNext() {
            this.submit();
        },

        //提交订单
        submit(continueExec) {
            // #ifdef H5
            this.order_from = 2;
            // #endif

            //app-1-start
            // #ifdef APP-PLUS
            switch (uni.getDeviceInfo().platform) {
                case 'android':
                    this.order_from = 3;
                    break;
                case 'ios':
                    this.order_from = 4;
                    break;
                default:
                    break;
            }
            // #endif
            //app-1-end
            //wx-2-start
            // #ifdef MP-WEIXIN
            this.order_from = 5;
            // #endif
            //wx-2-end
            // #ifdef MP-BAIDU
            this.order_from = 7;
            // #endif

            // #ifdef MP-ALIPAY
            this.order_from = 8;
            // #endif

            // #ifdef MP-TOUTIAO
            this.order_from = 9;
            // #endif

            const { preParam } = this;
            let param = {};
            param.method = 'POST';
            param.header = {
                'Content-Type': 'application/json'
            };
            param.data = {};
            let storeInfoList = [];
            this.goodsData.map((item) => {
                let storeitem = {};
                storeitem.invoiceId = this.invoiceId;
                storeitem.invoiceContent = this.invoice_content;
                storeitem.remark = item.remark;
                storeitem.storeCouponCode = item.storeCouponCode;
                storeitem.storeId = item.storeId;
                storeInfoList.push(storeitem);
            });
            // 新增特殊需求，有的店下单需要填写姓名、手机号
            if (this.needOtherInfo) {
                if (!this.special.userName.trim()) {
                    this.$api.msg('请输入姓名');
                    continueExec && continueExec();
                    return;
                }
                if (!this.special.userPhone.trim()) {
                    this.$api.msg('请输入手机号');
                    continueExec && continueExec();
                    return;
                } else if (!/^1[3-9]\d{9}$/.test(this.special.userPhone)) {
                    this.$api.msg('请输入正确的手机号');
                    continueExec && continueExec();
                    return;
                }
                param.data.userName = this.special.userName;
                param.data.userPhone = this.special.userPhone;
            }
            param.data.platformCouponCode = this.platformCouponCode;
            param.data.couponFreightCode = this.freightCoupon.couponCode;
            if (this.isVG == 1) {
                if (!this.orderAddress.addressId && preParam.isPickup != 1 && preParam.isPickup != 2) {
                    this.$api.msg(this.$L('请设置收货地址'));
                    continueExec && continueExec();
                    return;
                }
                param.data.addressId = this.orderAddress.addressId;
            }
            if (preParam.isPickup == 2 || preParam.isPickup == 3) {
                param.data.pickupPointId = this.choosedItem.id;
            }
            param.data.storeInfoList = storeInfoList;
            param.data.orderFrom = this.order_from;
            param.data.reserveInfoList = this.reserveInfoList;
            if (this.orderSn) {
                param.url = 'v3/business/front/orderOperate/balanceSubmit';
                preParam.isCart = 'false';
                param.data.source = 2;
                param.data.orderSn = this.orderSn;
            } else {
                param.url = 'v3/business/front/orderOperate/submit';
                param.data.source = 3;
            }
            if (preParam.ifcart == 1) {
                //来自于购物车
                param.data.isCart = true;
            } else {
                param.data.productId = JSON.parse(preParam.productId);
                param.data.number = preParam.numbers;
                param.data.isCart = false;
                if (this.spellTeamId != 0) {
                    param.data.spellTeamId = this.spellTeamId;
                }
            }
            if (this.integral > 0) {
                param.data.integral = this.integral;
            }
            param.data.isAloneBuy = this.isAloneBuy;
            param.data.isPickup = preParam.isPickup;
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    let need_pay = res.data.needPay;
                    let paySn = res.data.paySn;
                    if (!this.orderSn) {
                        this.$sldStatEvent({
                            behaviorType: 'buy',
                            paySn
                        });
                    }
                    this.timer = setInterval(() => {
                        this.getPayInfo(paySn, continueExec);
                    }, 100);
                } else {
                    continueExec && continueExec();
                    this.$api.msg(res.msg);
                }
            });
        },

        stopPrevent() {},

        //获取订单支付数据
        getPayInfo(paySn, continueExec) {
            let _this = this;
            let param = {};
            param.url = 'v3/business/front/orderPay/payInfo';
            param.method = 'get';
            param.data = {
                paySn: paySn,
                payFrom: 1
            };
            this.$request(param)
                .then((res) => {
                    if (res.state == 200) {
                        if (res.data.dealState == 3) {
                            //清除定时器
                            if (this.timer) {
                                clearInterval(this.timer);
                                this.timer = null;
                            }
                            //即使清除了定时器，经测验发现定时任务还是会存在导致跳转执行多遍，于
                            //是增加判断，发生第一次时将条件置为false，防止下一次的跳转
                            if (this.overFlowInterval) {
                                this.overFlowInterval = false;
                                // 如果支付金额为0，则不需要跳转支付页
                                if (res.data.paySn && res.data.needPay == 0) {
                                    this.$Router.replace({
                                        path: '/order/list',
                                        query: {
                                            state: 0
                                        }
                                    });
                                } else {
                                    this.$Router.replace({
                                        path: '/order/pay',
                                        query: {
                                            paySn,
                                            payMethodType: 'create',
                                            isPickup: this.preParam.isPickup
                                        }
                                    });
                                }
                                uni.removeStorageSync('goodsRemark');
                                uni.removeStorageSync('invoice_info');
                                uni.removeStorageSync('is_need_invoice');
                            }
                        } else if (res.data.dealState == 2) {
                            this.$api.msg(res.data.failReason || this.$L('提交订单失败，请稍后重试'));
                            continueExec && continueExec();
                            if (this.timer) {
                                clearInterval(this.timer);
                                this.timer = null;
                            }
                        } else {
                            continueExec && continueExec();
                        }
                    } else if (res.state == 267) {
                        if (this.timer) {
                            clearInterval(this.timer);
                            this.timer = null;
                        }
                        if (this.overFlowInterval) {
                            this.overFlowInterval = false;
                            this.$api.msg(res.msg + this.$L(',2s后自动跳转订单列表'));
                            setTimeout(() => {
                                this.$Router.push({
                                    path: '/order/list',
                                    query: {
                                        state: 0
                                    }
                                });
                            }, 2000);
                            uni.removeStorageSync('goodsRemark');
                            uni.removeStorageSync('invoice_info');
                            uni.removeStorageSync('is_need_invoice');
                        }
                    } else {
                        if (this.timer) {
                            clearInterval(this.timer);
                            this.timer = null;
                        }
                        continueExec && continueExec();
                    }
                })
                .catch((err) => {
                    continueExec && continueExec();
                });
        },
        //跳转店铺详情页面
        goStoreDetail(vid) {
            this.$Router.push({
                path: '/standard/store/shopHomePage',
                query: {
                    vid: vid
                }
            });
        },
        //根据收货地址id获取总的运费
        changeAddress(selAddress) {
            this.orderAddress = selAddress;
            this.confirmOrder(2);
            uni.setStorageSync('addressId', this.orderAddress.addressId);
        },
        //备注输入框聚焦
        handleFocus() {
            // #ifdef MP
            this.isBottomShow = false;
            // #endif
        },
        //失去焦点
        handleBlur() {
            this.isBottomShow = true;
        },
        // 跳转我的发票页面
        toInvoice() {
            this.showState = 'from_invoice';
            this.$Router.push({
                path: '/newPages/invoice/myInvoice',
                query: {
                    isVatInvoice: this.isVatInvoice ? 1 : 0
                }
            });
        },
        getIntRule() {
            this.$request({
                url: 'v3/system/front/setting/getSettings',
                data: {
                    names: 'integral_cash_out_is_enable,integral_conversion_ratio,integral_use_lowest_amount,integral_max_deduct_rate'
                }
            }).then((res) => {
                if (res.state == 200) {
                    this.intRuleList = res.data;
                }
            });
        },
        //校验预留信息
        checkPreMsg(type, value, name, isRequired) {
            switch (type) {
                case 1: {
                    if (isRequired == 1) {
                        return this.$checkMobile(value, name);
                    } else {
                        let regMobile = /(1[3-9]\d{9}$)/;
                        if (value && !regMobile.test(value)) {
                            this.$api.msg(`请输入正确的${name}!`);
                            return false;
                        } else {
                            return true;
                        }
                    }

                    break;
                }
                case 2: {
                    if (isRequired == 1) {
                        return this.$checkIdentity(value, name);
                    } else {
                        if (value) {
                            let reg18 = /^[1-9][0-9]{5}(18|19|20)[0-9]{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)[0-9]{3}([0-9]|(X|x))/;
                            let reg15 = /^[1-9][0-9]{5}[0-9]{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)[0-9]{2}[0-9]/;
                            if (reg18.test(value) || reg15.test(value)) {
                                return true;
                            } else {
                                this.$api.msg(`请输入正确的${name}`);
                                return false;
                            }
                        } else {
                            return true;
                        }
                    }

                    break;
                }

                case 3: {
                    let regNum = /[0-9]+(.[0-9]+)?/;
                    if (isRequired == 1) {
                        if (!value) {
                            this.$api.msg(`请输入${name}`);
                            return false;
                        } else if (!regNum.test(value)) {
                            this.$api.msg(`请输入正确的${name}`);
                            return false;
                        } else {
                            return true;
                        }
                    } else {
                        return true;
                    }
                    break;
                }
                case 4: {
                    if (isRequired == 1) {
                        if (!value) {
                            this.$api.msg(`请输入${name}`);
                            return false;
                        } else {
                            return true;
                        }
                    } else {
                        return true;
                    }
                    break;
                }
                case 5: {
                    if (isRequired == 1) {
                        return this.$checkEmail(value, name);
                    } else {
                        let reg = /^([a-zA-Z0-9]+[-_.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[-_.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,6}$/;
                        if (value && !reg.test(value)) {
                            this.$api.msg(`请输入正确的${name}!`);
                            return false;
                        } else {
                            return true;
                        }
                    }

                    break;
                }
            }
        }
    }
};
</script>

<style lang="scss">
.page {
    width: 750rpx;
    min-height: 100vh;
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    // 背景图片固定，不滚动
    background-attachment: fixed;
    .order_confirm_container {
        width: 100%;
        padding-top: 20rpx;
        padding-bottom: 100rpx;
    }
    .address-section {
        width: 96%;
        margin: 0 auto;
        border-radius: 40rpx;
        background: #fff;
        position: relative;

        .order-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 30rpx 40rpx;

            .iconfont {
                color: $main-second-color;
                font-size: 26rpx;
            }

            .cen {
                .member_info {
                    margin-bottom: 15rpx;
                    font-size: 28rpx;
                    color: #000;
                    .name {
                    }

                    .mobile {
                        margin-left: 40rpx;
                    }
                }
                .top {
                    .tag {
                        width: 63rpx;
                        height: 32rpx;
                        margin-right: 20rpx;
                        background: $color1;
                        line-height: 32rpx;
                        text-align: center;
                        font-size: 24rpx;
                        color: #fff;
                        border-radius: 4rpx;
                    }
                    .address {
                        color: rgba(0, 0, 0, 0.5);
                        font-size: $fs-s;
                        line-height: 40rpx;
                        text-overflow: -o-ellipsis-lastline;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        line-clamp: 2;
                        -webkit-box-orient: vertical;
                        word-break: break-all;
                    }
                }

                .click_address {
                    margin-top: 15rpx;
                    padding-top: 15rpx;
                    border-top: 0.5px solid rgba(0, 0, 0, 0.1);
                    color: $color1;
                }
            }

            .empty_address {
                color: $main-font-color;
                font-size: 34rpx;
                flex: 1;

                .add_icon {
                    margin-right: 20rpx;
                    margin-top: -8rpx;
                    font-size: 40rpx;
                }
            }
        }

        .cen {
            display: flex;
            flex-direction: column;
            flex: 1;
            font-size: 28rpx;
        }

        .a-bg {
            position: absolute;
            left: 0;
            bottom: 0;
            display: block;
            width: 100%;
            height: 7rpx;
        }

        .wm_content {
            height: 174rpx;
            padding: 0 26rpx 0 40rpx;

            .wm_address {
                height: 104rpx;
                flex: 1;
                color: #101010;

                .wm_tit {
                    font-weight: bold;
                    font-size: 32rpx;
                }

                .wm_detail {
                    margin-top: 20rpx;
                    font-size: 28rpx;
                }
            }

            .choose_a {
                flex: 1;
                padding-left: 250rpx;
            }

            .iconfont {
                color: #999999;
                font-size: 13px;
            }
        }

        .pick_content {
            .cen {
                .pick_top {
                    text {
                        font-size: 32rpx !important;
                        color: #2d2d2d !important;
                        font-weight: bold;
                    }
                }

                .pick_address {
                    font-size: 28rpx !important;
                    color: #2d2d2d;
                    font-weight: normal !important;
                    margin-top: 20rpx;
                }
            }

            .pick_right {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                image {
                    width: 40rpx;
                    height: 40rpx;
                }

                .dao_text {
                    color: $color1;
                    font-size: 24rpx;
                    margin-top: 10rpx;
                }
            }
        }
    }
    .goods-section {
        width: 96%;
        margin: 0 auto;
        margin-top: 20rpx;
        border-radius: 40rpx;
        background: #fff;
        position: relative;
        box-sizing: border-box;

        .store_list {
            width: 100%;
        }

        .product_con {
            border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);

            .product_giveaway {
                color: #666666;
                background-color: #f8f8f8;
                padding: 6rpx 10rpx;
                font-size: 24rpx;
                width: fit-content;
                margin-bottom: 20rpx;
                margin-left: 20rpx;
                border-radius: 3px;
            }
        }

        .g-item {
            display: flex;
            padding: 20rpx 20rpx 20rpx 0;
            margin-left: 20rpx;
            width: calc(750rpx - 20rpx);

            .image_con {
                position: relative;

                .virtual_tag {
                    position: absolute;
                    top: 0;
                    left: 0;
                    background: #e8bc4d;
                    color: #fff;
                    padding: 4rpx;
                    font-size: 24rpx;
                    border-radius: 15rpx 0 0 0;
                }

                .image {
                    flex-shrink: 0;
                    display: block;
                    width: 220rpx;
                    height: 220rpx;
                    border-radius: 15rpx;
                    background-size: cover;
                    background-position: center center;
                    background-repeat: no-repeat;
                    background-color: #f8f6f7;
                }
            }

            .right {
                flex: 1;
                padding: 15rpx 0 15rpx 24rpx;
                overflow: hidden;
                height: 220rpx;
            }

            .title {
                font-size: 28rpx;
                color: $main-font-color;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                word-break: break-word;
                line-height: 40rpx;
            }

            .spec {
                font-size: 24rpx;
                color: #949494;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                word-break: break-word;
                line-height: 40rpx;
            }

            .goods_item_specs {
                display: flex;
                align-items: center;
                font-size: 24rpx;
                font-family: PingFang SC;
                font-weight: 500;
                color: #999999;
                line-height: 34rpx;

                .goods_item_spec {
                    margin-right: 10rpx;
                }
            }

            .price-box {
                font-size: 32rpx;
                color: $color1;

                &.price_wrap_super {
                    .unit,
                    .price_decimal,
                    .price_int {
                        color: $color1;
                    }
                }

                .unit {
                    font-size: 24rpx;
                    font-weight: bold;
                }

                .price_int {
                    font-size: 34rpx;
                    margin-left: 4rpx;
                    font-weight: bold;
                }

                .price_decimal {
                    font-size: 24rpx;
                    font-weight: bold;
                }

                .price_decimal {
                    position: relative;

                    .price_super_img {
                        position: absolute;
                        bottom: 4rpx;
                        display: inline-block;
                        width: 82rpx;
                        height: 28rpx;
                        line-height: 26rpx;
                        color: #cfb295;
                        font-size: 20rpx;
                        font-family: PingFang SC;
                        font-weight: 500;
                        text-align: center;
                        text-indent: 6rpx;
                        background-position: center;
                        background-repeat: no-repeat;
                        background-size: cover;
                        margin-left: 16rpx;
                    }
                }
            }

            .step-box {
                position: relative;
            }
        }
    }
}
.uni-popup__wrapper-box {
    background-color: #fff;
}

.preSale_desc {
    margin-top: 18rpx;
    font-size: 24rpx;
    font-family: PingFang SC;
    font-weight: 500;
    color: #949494;
}

.ladder_list {
    padding: 20rpx 40rpx;
    background-color: #fff;
    position: relative;

    .ladder_tip {
        font-size: 24rpx;
        font-family: PingFang SC;
        font-weight: 400;
        color: #2d2d2d;
    }

    .ladder_process {
        margin-top: 40rpx;

        .on {
            .level_left {
                text {
                    color: $color1 !important;
                }
            }

            .level_right {
                color: $color1 !important;
            }

            .level_gang {
                background: $color1 !important;
            }
        }

        .process_level {
            .level_top {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 6rpx;
                margin-top: 6rpx;
                font-size: 28rpx;

                .level_left {
                    display: flex;
                    align-items: center;

                    text {
                        line-height: 43rpx;
                        margin-left: 32rpx;
                        font-size: 28rpx;
                        font-family: PingFang SC;
                        font-weight: 400;
                        color: #2d2d2d;
                    }

                    image {
                        width: 39rpx;
                        height: 43rpx;
                    }
                }

                .level_right {
                    font-size: 28rpx;
                    font-family: PingFang SC;
                    font-weight: bold;
                    color: #2d2d2d;
                }
            }

            .level_gang {
                width: 1px;
                height: 31rpx;
                background: #e1e1e1;
                margin-left: 18rpx;
            }
        }
    }

    .ladder_total {
        display: flex;
        justify-content: flex-end;
        margin-top: 42rpx;
        font-size: 28rpx;
        font-family: PingFang SC;

        text:first-child {
            color: #666666;
            margin-right: 10rpx;
        }

        text:nth-child(2) {
            color: #2d2d2d;
        }

        text:nth-child(3) {
            color: $color1;
        }
    }
}

.yt-list {
    width: 100%;
    &.order_remark {
        padding: 10rpx 20rpx 20rpx;

        .title {
            position: relative;
            color: $main-font-color;
            font-size: 28rpx;
            line-height: 32rpx;
        }

        .required {
            text-indent: 2;
            color: red;
        }

        .placeholder {
            color: $main-third-color;
            font-size: 24rpx;
        }

        .content {
            width: inherit;
            height: 180rpx;
            background: rgba(245, 245, 245, 1);
            border-radius: 40rpx;
            padding: 20rpx 30rpx;
            color: $main-second-color;
            font-size: 26rpx;
            margin-top: 20rpx;
        }

        .content-input {
            width: inherit;
            background: rgba(245, 245, 245, 1);
            height: 76rpx;
            border-radius: 6rpx;
            padding: 0 20rpx;
            color: $main-second-color;
            font-size: 26rpx;
            margin-top: 10rpx;
        }
    }
}

.store_info {
    margin-top: 0;
}

.yt-list-cell-giveaway {
    padding: 20rpx;
    line-height: 60rpx;

    &.b-b:after {
        left: 20rpx;
    }

    position: relative;

    .cell-tit {
        flex: 1;
        font-size: 26rpx;
        color: #333333;
        margin-right: 20rpx;
    }

    .giveaway_list {
        padding: 30rpx 20rpx;
        background: #f5f5f5;
        border-radius: 6px 6px 6px 6px;
        font-size: 24rpx;

        .giveaway_item {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .giveaway_item_left {
            display: flex;
            align-items: center;

            .giveaway_item_index {
                color: #999999;
            }

            .giveaway_item_name {
                color: #333333;
                max-width: 400rpx;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
                display: inline-block;
            }
        }

        .giveaway_item_number {
            float: right;
        }
    }
}

.yt-list-cell {
    display: flex;
    align-items: center;
    padding: 20rpx;
    line-height: 60rpx;
    position: relative;

    .iconfont {
        color: #999999;
        font-size: 26rpx;
        margin-left: 19rpx;
    }

    &.cell-hover {
        background: #fafafa;
    }

    &.b-b:after {
        left: 20rpx;
    }

    .cell-icon {
        height: 32rpx;
        width: 32rpx;
        font-size: 22rpx;
        color: #fff;
        text-align: center;
        line-height: 32rpx;
        background: #f85e52;
        border-radius: 4rpx;
        margin-right: 12rpx;

        &.hb {
            background: #ffaa0e;
        }

        &.lpk {
            background: #3ab54a;
        }
    }

    .voice {
        color: #333333 !important;
        font-weight: 500 !important;
        display: flex;
        align-items: center;
    }

    .cell-more {
        align-self: center;
        font-size: 24rpx;
        color: $font-color-light;
        margin-left: 8rpx;
        margin-right: -10rpx;
    }

    .cell-tit {
        flex: 1;
        font-size: 26rpx;
        color: #333333;
        margin-right: 20rpx;
    }

    .cell-tip {
        font-size: 26rpx;
        color: $color1;
        font-weight: bold;
        &.disabled {
            color: $font-color-light;
        }

        &.active {
            color: $base-color;
        }

        &.red {
            color: $base-color;
        }
    }

    &.desc-cell {
        .cell-tit {
            max-width: 90rpx;
        }
    }

    .desc {
        flex: 1;
        font-size: $font-base;
        color: $font-color-dark;
    }

    .cell_int {
        display: flex;
        flex: 1;
        align-items: center;

        image {
            width: 30rpx;
            height: 30rpx;
        }

        .cell-tit {
            flex: unset;
        }
    }
}

/* 支付列表 */
.pay-list {
    padding-left: 40rpx;
    margin-top: 16rpx;
    background: #fff;

    .pay-item {
        display: flex;
        align-items: center;
        padding-right: 20rpx;
        line-height: 1;
        height: 110rpx;
        position: relative;
    }

    .icon-weixinzhifu {
        width: 80rpx;
        font-size: 40rpx;
        color: #6bcc03;
    }

    .icon-alipay {
        width: 80rpx;
        font-size: 40rpx;
        color: #06b4fd;
    }

    .icon-xuanzhong2 {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60rpx;
        height: 60rpx;
        font-size: 40rpx;
        color: $base-color;
    }

    .tit {
        font-size: 32rpx;
        color: $font-color-dark;
        flex: 1;
    }
}

.footer {
    position: fixed;
    left: 0;
    right: 0;
    margin: 0 auto;
    bottom: 0;
    z-index: 99;
    width: 100%;
    height: 120rpx;
    padding-bottom: env(safe-area-inset-bottom);
    font-size: 30rpx;
    background-color: #fff;
    color: $color1;
	padding: 0 4%;
	box-sizing: border-box;
    .price-content {
        .should_pay {
            color: $color1;

            .tit {
                color: $main-font-color;
                font-size: 30rpx;
                line-height: 30rpx;
            }

            .unit,
            .small_price {
                font-size: 24rpx;
                font-weight: bold;
                line-height: 26rpx;
            }

            .big_price {
                font-size: 30rpx;
                font-weight: bold;
                line-height: 30rpx;
            }
        }

        .promotion_total {
            color: $main-font-color;
            font-size: 22rpx;
            margin-top: 8rpx;
        }
    }

    .submit {
        width: 200rpx;
        height: 70rpx;
        background:  $color1;
        border-radius: 35rpx;
        color: #fff;
        font-size: 30rpx;
        margin: 0 20rpx;
    }
}

/* 优惠券面板 */
.mask {
    display: flex;
    align-items: flex-end;
    position: fixed;
    left: 0;
    top: var(--window-top);
    bottom: 0;
    width: 100%;
    background: rgba(0, 0, 0, 0);
    z-index: 9995;
    transition: 0.3s;

    .mask-content {
        width: 100%;
        min-height: 30vh;
        max-height: 70vh;
        background: #f3f3f3;
        transform: translateY(100%);
        transition: 0.3s;
        overflow-y: scroll;
    }

    &.none {
        display: none;
    }

    &.show {
        background: rgba(0, 0, 0, 0.4);

        .mask-content {
            transform: translateY(0);
        }
    }
}

.limVoice {
    display: block;
    max-width: 400rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-right: 12rpx;
}

/* 优惠券列表 */
.coupon-item {
    display: flex;
    flex-direction: column;
    margin: 20rpx 24rpx;
    background: #fff;

    .con {
        display: flex;
        align-items: center;
        position: relative;
        height: 120rpx;
        padding: 0 30rpx;

        &:after {
            position: absolute;
            left: 0;
            bottom: 0;
            content: '';
            width: 100%;
            height: 0;
            border-bottom: 1px dashed #f3f3f3;
            transform: scaleY(50%);
        }
    }

    .left {
        display: flex;
        flex-direction: column;
        justify-content: center;
        flex: 1;
        overflow: hidden;
        height: 100rpx;
    }

    .title {
        font-size: 32rpx;
        color: $font-color-dark;
        margin-bottom: 10rpx;
    }

    .time {
        font-size: 24rpx;
        color: $font-color-light;
    }

    .right {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        font-size: 26rpx;
        color: $font-color-base;
        height: 100rpx;
    }

    .price {
        font-size: 44rpx;
        color: $base-color;

        &:before {
            content: '￥';
            font-size: 34rpx;
        }
    }

    .tips {
        font-size: 24rpx;
        color: $font-color-light;
        line-height: 60rpx;
        padding-left: 30rpx;
    }

    .circle {
        position: absolute;
        left: -6rpx;
        bottom: -10rpx;
        z-index: 10;
        width: 20rpx;
        height: 20rpx;
        background: #f3f3f3;
        border-radius: 100px;

        &.r {
            left: auto;
            right: -6rpx;
        }
    }
}

.bottom_super {
    width: 750rpx;
    height: 100rpx;
    margin-top: 20rpx;
    padding-left: 20rpx;
    padding-right: 20rpx;
    background: linear-gradient(90deg, #fbe7cf, #efd2a3);

    .bottom_super_left {
        image {
            position: relative;
            bottom: 4rpx;
            width: 40rpx;
            height: 40rpx;
            margin-right: 20rpx;
        }

        span {
            color: #684625;
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 500;
        }
    }

    .bottom_super_right {
        color: #000000;
        font-size: 26rpx;
        font-family: PingFang SC;
        font-weight: bold;
    }
}

.empty_h {
    width: 750rpx;
    height: 60rpx;
}

.store_name {
    padding-left: 20rpx;
    padding-bottom: 30rpx;
    margin-top: 30rpx;
    display: flex;
    align-items: center;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);

    .ladder_group {
        height: 32rpx;
        border-radius: 15rpx;
        line-height: 32rpx;
        padding: 0 10rpx;
        margin-right: 10rpx;
        background: linear-gradient(22deg, #fe901e 0%, #fead28 100%);
        font-size: 22rpx;
        color: #ffffff;
    }

    image {
        width: 34rpx;
        height: 32rpx;
    }

    .store_name_text {
        font-size: 32rpx;
        color: #2d2d2d;
        font-weight: bold;
        margin-left: 10rpx;
    }

    .iconfont {
        font-size: 24rpx;
        margin-left: 10rpx;
    }
}

#store_red_wrap,
#store_no_good,
#platform_red_wrap,
#freight_red_wrap {
    position: fixed;
    top: 0;
    left: 0;
    width: 750rpx;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 999;
    right: 0;
    margin: 0 auto;
}

.store_red {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 750rpx;
    height: 50vh;
    background-color: #f5f5f5;
    box-sizing: border-box;
}

.store_red .title {
    line-height: 110rpx;
    padding: 0 30rpx;
    border-bottom: 1rpx solid #eee;
    font-size: 30rpx;
    color: #666;
    background: #fff;
}

.store_red_list {
    height: 78%;
    box-sizing: border-box;
    padding: 30rpx 30rpx 0;
}

.store_red_list .ticket-item {
    position: relative;
    display: block;
    margin-bottom: 30rpx;
    border-radius: 20rpx;
}

.ticket-item .line_left,
.ticket-item .line_right {
    position: absolute;
    top: 150rpx;
    width: 20rpx;
    height: 20rpx;
    border-radius: 50%;
    background-color: #f5f5f5;
}

.ticket-item .line_left {
    left: -10rpx;
}

.ticket-item .line_right {
    right: -10rpx;
}

.store_red_list .ticket-item:last-child {
    margin: 0;
}

.store_red_list .circle_radio {
    display: flex;
    align-items: center;
    height: 160rpx;
    color: #fff;
    border-top-left-radius: 15rpx;
    border-top-right-radius: 15rpx;
    background: $color2;
}

.circle_radio .red_item_wrap {
    display: flex;
    width: 100%;
    padding: 0 30rpx 0 50rpx;
    align-items: center;
    justify-content: space-between;
}

.circle_radio .red_item_wrap .red_h1 {
    font-size: 26rpx;
}

.circle_radio .red_item_wrap .red_h1 em {
    font-size: 54rpx;
}

.circle_radio .red_item_wrap .red_h2 {
    font-size: 26rpx;
}

.circle_radio .red_item_wrap .red_h2 em {
    display: block;
}

.circle_radio .red_item_wrap .red_h2 em:nth-child(2) {
    font-size: 20rpx;
    padding-top: 10rpx;
}

.circle_radio .red_item_wrap .red_h3 .red_h3_top {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 22rpx;
}

.red_h3 .red_h3_top image {
    width: 20rpx;
    height: 20rpx;
    margin-right: 15rpx;
}

.circle_radio .red_item_wrap .red_h3 .red_h3_bottom {
    padding: 10rpx 15rpx;
    font-size: 28rpx;
    color: #fff;
    background: rgba(255, 255, 255, 0.5);
    margin-top: 18rpx;
    border-radius: 6rpx;
    text-align: center;
}

.ticket-item .red_p {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80rpx;
    font-size: 26rpx;
    color: #666666;
    background: #fff;
    border-top: 3rpx dashed #fff;
    border-bottom-left-radius: 15rpx;
    border-bottom-right-radius: 15rpx;
}

.ticket-item .red_p image {
    width: 26rpx;
    height: 26rpx;
    margin-right: 10rpx;
}

.ticket-item .red_p text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.agreement-part {
    width: 100%;
    font-size: 24rpx;
    color: #999999;
    text-align: center;
    box-sizing: border-box;
    padding: 15rpx 0 20rpx 10rpx;

    .register_icon {
        font-size: 30rpx;
        margin-right: 5rpx;
        margin-top: 5rpx;
    }

    .agreement {
        color: $color1;
        border-bottom: 1rpx solid $color1;
    }
}

.address_list {
    width: 750rpx;
    height: 610rpx;
    margin: 0 auto;
    z-index: 150;
    background-color: #fff;
}

.address_list_con {
    border-radius: 5px 5px 0;
}

.other_address {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 110rpx;
    background: #fff;

    .integral_opt {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 80rpx;

        .no_int {
            width: 334rpx;
            border-radius: 44rpx 0 0 44rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 30rpx;
            color: #fff;
            background-color: #999;
            height: 70rpx;
        }

        .int_con {
            width: 334rpx;
            border-radius: 0 44rpx 44rpx 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 30rpx;
            color: #fff;
            background-color: $color1;
            height: 70rpx;
        }
    }

    .other_btn {
        background: linear-gradient(-90deg, #fc1d1c 0%, #ff7a18 100%);
        border-radius: 44rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 34rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #fefefe;
    }
}

.address_top {
    padding: 20rpx 30rpx;
    border-radius: 5px 5px 0 0;
    font-size: 32rpx;
    font-family: PingFang SC;
    font-weight: bold;
    color: #333333;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    border-bottom: 0.5px solid #f2f2f2;

    image {
        width: 50rpx;
        height: 50rpx;
    }
}

.member_int {
    height: 79rpx;
    background: #f8f8f8;
    line-height: 79rpx;
    font-size: 28rpx;
    font-family: Adobe Heiti Std;
    font-weight: bold;
    color: #333333;
    padding-left: 30rpx;
}

.list {
    display: flex;
    /* flex-direction: column; */
    align-items: center;
    justify-content: flex-start;
    padding: 24rpx 30rpx;
    background: #fff;
    position: relative;

    &.b-b {
        /* &:after {
				position: absolute;
				z-index: 3;
				left: 20rpx;
				right: 0;
				height: 0;
				content: '';
				-webkit-transform: scaleY(0.5);
				transform: scaleY(0.5);
				border-bottom: 1px solid rgba(0, 0, 0, .1);
			} */
    }
}

.wrapper {
    flex: 1;
    background: #fff;

    .iconfont {
        color: $color1;
        font-size: 32rpx;
        margin-right: 30rpx;
    }

    image {
        width: 36rpx;
        height: 38rpx;
        margin-right: 22rpx;
    }
}

.wrapper_right {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 32rpx;
}

.address-box {
    display: flex;
    align-items: center;

    .int_desc {
        font-size: 28rpx;
        color: #333;
        line-height: 38rpx;
        margin-top: 5rpx;
        padding: 0 5rpx;
    }

    .tag {
        width: 63rpx;
        height: 30rpx;
        margin-left: 20rpx;
        margin-right: 0rpx;
    }
}

uni-radio ::v-deep .uni-radio-input {
    width: 32rpx;
    height: 32rpx;
}

uni-radio ::v-deep .uni-radio-input.uni-radio-input-checked:before {
    font-size: 28rpx;
}

.uni-popup {
    z-index: 1000;
}

.intRule_box {
    width: 578rpx;
    height: 464rpx;
    background: #ffffff;
    border-radius: 10px;

    .int_title {
        padding: 20rpx 30rpx;
        font-size: 32rpx;
        font-family: Adobe Heiti Std;
        font-weight: bold;
        color: #2d2d2d;
        border-bottom: 1px solid #f2f2f2;
    }

    .int_content {
        padding: 30rpx;

        view {
            line-height: 40rpx;
            font-size: 26rpx;
            color: #2d2d2d;
            margin-bottom: 20rpx;
        }
    }
}

.pre_message {
    background: #fff;

    .pre_msg_item {
        padding: 28rpx 22rpx;
        width: 100%;
        border-bottom: 2rpx solid #f2f2f2;

        .msg_left {
            font-size: 28rpx;
            font-family: MicrosoftYaHei;
            color: #333333;
            font-weight: 600;
            width: 200rpx;
            text-align: right;
            word-break: break-all;
        }

        .msg_right {
            margin-left: 20rpx;

            input {
                font-size: 24rpx;
                width: 500rpx;
            }
        }
    }
}
</style>
