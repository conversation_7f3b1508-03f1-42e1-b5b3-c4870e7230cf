<!-- 查看物流页面 -->
<template>
	<view class="look_logistics" :style="mix_diyStyle">
		<!-- 订单状态为待收货或者已完成而且只有一个包裹的一定是齐套发货，否则就是分批发货 -->
		<block v-if="isOrder&&!((detailInfo.orderState==30||detailInfo.orderState==40)&&deliverIds.length==1)">
			<view class="order_send flex_row_start_center">
				<view v-for="(item,index) in deliverIds" :key="index" @click="changeLogistics(index)"
					class="order_send_item" :class="{active:logisticsIndex==index}">包裹{{index+1}}</view>
				<view v-if="remain" class="order_send_item" :class="{active:logisticsIndex==deliverIds.length}"
					@click="showRemain('change')">待发货</view>
			</view>
			<view class="goods_list" v-if="remain&&logisticsIndex==deliverIds.length">
				<block v-for="(item,index) in remainProduct" :key='item.deliverId'>
					<goodsItemO bodyWidth="100%" :goods_info="item"
						:goods_type="4" ref="recom_goods" :deliverNum="true" :padding-side="false"/>
				</block>
			</view>
		</block>
		<block v-if="logisticsIndex!==deliverIds.length">
			<view v-if="logisticsInfo.type == 1" class="logistics_information_type2">
				<view class="flex_row_start_center">
					<view class="deli_name">物流方式：</view>
					<view class="deli_value">自行配送</view>
				</view>
				<view class="flex_row_start_center" style="margin-top: 22rpx;">
					<view class="deli_name">联系人：</view>
					<view class="deli_value">{{logisticsInfo.expressName}}</view>
				</view>
				<view class="flex_row_start_center" style="margin-top: 22rpx;">
					<view class="deli_name">联系电话：</view>
					<view class="deli_value">{{logisticsInfo.expressNumber}}</view>
				</view>
			</view>
			<view style="padding: 20rpx;" v-else>
				<!-- 物流公司,及商品 start-->
				<view class="logistics_des" :class="{no_data_look:!logisticsRouteList.length}">
					<view class="logistics_desc">
						<image :src="imgUrl+'order-detail/new_wu.png'" mode="aspectFit" class="desc_image"></image>
						<view class="logistics_type">
							{{logisticsInfo.expressName ? logisticsInfo.expressName : '--'}}
						</view>
						<view class="logistics_type" style="margin-left: 20rpx;">
							{{logisticsInfo.expressNumber ? logisticsInfo.expressNumber : '--'}}
						</view>
					</view>
				</view>
				<!-- 物流公司,及商品 end-->

				<!-- 物流轨迹 start -->
				<view class="lofistics_info" v-if="logisticsRouteList.length > 0">
					<!-- 纵向排列 -->
				<uni-steps :options="logisticsRouteList" direction="column"
					:active="0"></uni-steps>
				</view>
				<view class="no_data" v-if="loadFlag&&logisticsRouteList.length == 0">
					<image :src="imgUrl + 'order-detail/no_logistics.png'" mode="aspectFit"></image>
					<text>{{ $L('暂无物流信息，请耐心等待哦') }}~</text>
				</view>
				<view v-if="!loadFlag" style="text-align: center;margin-top: 30rpx;color: #9a9a9a;font-size: 24rpx;">
					物流信息加载中...
				</view>
				<!-- 物流轨迹 end -->
			</view>
		</block>
		<!-- 推荐商品 start-->
		<!-- <scroll-view class="recomment" scroll-y @scrolltolower="getData">
			<recommendGoods ref="recomment_goods" />
		</scroll-view> -->
		<!-- 推荐商品 end-->
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex'
	import recommendGoods from '@/components/recommend-goods.vue'
	import uniPopup from '@/components/uni-popup/uni-popup.vue'
	import uniPopupMessage from '@/components/uni-popup/uni-popup-message.vue'
	import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue'
	import uniSteps from '@/components/uni-steps/uni-steps.vue' //步骤条
	import goodsItemO from '@/components/goods_item_o.vue';
	let startY = 0,
		moveY = 0,
		pageAtTop = true
	export default {
		components: {
			recommendGoods,
			uniPopup,
			uniPopupMessage,
			uniPopupDialog,
			uniSteps,
			goodsItemO
		},
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				coverTransform: 'translateY(0px)',
				coverTransition: '0s',
				moving: false,
				logisticsInfo: {}, //物流信息
				logisticsRouteList: [], //物流轨迹信息
				type: '',
				afsSn: '',

				orderSn: '',
				deliverId: '', //批量退货查询id
				isSample: false, //是否是样品申请单
				logReason: '',
				// 批量发货物流数据
				logisticsIndex: 0,
				deliverIds: [],
				remain: false,
				remainProduct: [],
				orderSn: '',
				reason: '',
				loadFlag: false,
				detailInfo:{}

			}
		},
		async onLoad(option) {
			setTimeout(() => {
				uni.setNavigationBarTitle({
					title: this.$L('查看物流')
				})
			}, 0);
			//如果传过来的只有orderSn，先从订单详情接口获取物流deliverId,再查询物流接口
			//如果传过来的有orderSn和deliverId，先从订单详情接口获取物流,再查询物流接口，后展示该deliverId对应的物流
			if (this.$Route.query.orderSn) {
				this.orderSn = this.$Route.query.orderSn;
				this.initData()
			}
			if (this.$Route.query.deliverId) {
				this.deliverId = this.$Route.query.deliverId;
			}

			//退款单号
			this.type = this.$Route.query.type
			if(this.$Route.query.afsSn){
				this.afsSn = this.$Route.query.afsSn
				this.getLogisticsTrajectory()
			}
		},
		// #ifndef MP
		onNavigationBarButtonTap(e) {
			const index = e.index
			if (index === 0) {
				this.navTo('/newPages/set/set')
			} else if (index === 1) {
				//app-1-start
				// #ifdef APP-PLUS
				const pages = getCurrentPages()
				const page = pages[pages.length - 1]
				const currentWebview = page.$getAppWebview()
				currentWebview.hideTitleNViewButtonRedDot({
					index
				})
				// #endif
				//app-1-end
				this.$Router.push('/newPages/notice/notice')
			}
		},
		// #endif
		computed: {
			...mapState(['hasLogin', 'userInfo', 'userCenterData']),
			isOrder(){
				return Object.keys(this.detailInfo).length>0
			}
		},
		onReachBottom() {
			this.$refs.recomment_goods.getMoreData()
		},
		methods: {
			initData() {
				uni.showLoading({
					title: '加载中'
				});
				let that = this;
				let param = {};
				param.url = 'v3/business/front/orderInfo/detail';
				param.method = 'POST';
				param.data = {};
				param.data.orderSn = this.orderSn;
				this.$request(param).then(res => {
					uni.hideLoading()
					if (res.state == 200) {
						this.detailInfo = res.data
						let {orderDeliverList,orderDeliver} = this.detailInfo
						if (orderDeliverList && orderDeliverList.length) {
							this.deliverIds = orderDeliverList.map(item => item.deliverId)
						}
						if (this.deliverId) {
							this.logisticsIndex = orderDeliverList.findIndex(item=>item.deliverId==this.deliverId)
						} else {
							if (orderDeliver) {
								this.deliverId = orderDeliver.deliverId
							} else if (this.deliverIds.length) {
								this.deliverId = orderDeliverList[this.logisticsIndex].deliverId
							} else {
								return;
							}
						}
						this.showRemain('init')
						this.getLogisticsTrajectory()
					}
				})
			},

			/**
			 * 统一跳转接口,拦截未登录路由
			 * navigator标签现在默认没有转场动画，所以用view
			 */
			navTo(url) {
				if (!this.hasLogin) {
					let urls = this.$Route.path
					const query = this.$Route.query
					uni.setStorageSync('fromurl', {
						url: urls,
						query
					})
					url = '/pages/public/login'
				}
				this.$Router.push(url)
			},

			/**
			 *  会员卡下拉和回弹
			 *  1.关闭bounce避免ios端下拉冲突
			 *  2.由于touchmove事件的缺陷（以前做小程序就遇到，比如20跳到40，h5反而好很多），下拉的时候会有掉帧的感觉
			 *    transition设置0.1秒延迟，让css来过渡这段空窗期
			 *  3.回弹效果可修改曲线值来调整效果，推荐一个好用的bezier生成工具 http://cubic-bezier.com/
			 */
			coverTouchstart(e) {
				if (pageAtTop === false) {
					return
				}
				this.coverTransition = 'transform .1s linear'
				startY = e.touches[0].clientY
			},
			coverTouchmove(e) {
				moveY = e.touches[0].clientY
				let moveDistance = moveY - startY
				if (moveDistance < 0) {
					this.moving = false
					return
				}
				this.moving = true
				if (moveDistance >= 80 && moveDistance < 100) {
					moveDistance = 80
				}

				if (moveDistance > 0 && moveDistance <= 80) {
					this.coverTransform = `translateY(${moveDistance}px)`
				}
			},
			coverTouchend() {
				if (this.moving === false) {
					return
				}
				this.moving = false
				this.coverTransition = 'transform 0.3s cubic-bezier(.21,1.93,.53,.64)'
				this.coverTransform = 'translateY(0px)'
			},
			//获取物流轨迹接口
			getLogisticsTrajectory() {
				let that = this
				let param = {}
				if (this.type && this.type == 'afs') {
					param.url = 'v3/business/front/logistics/afs/getTrace'
				} else {
					param.url = 'v3/business/front/logistics/order/getTrace'
				}
				param.method = 'GET'
				param.data = {}
				if (this.type && this.type == 'afs') {
					param.data.afsSn = this.afsSn
				} else if (this.deliverId) {
					param.data.deliverId = this.deliverId; //订单号
				} else {
					this.$api.msg('查询信息有误');
					return;
				}
				that.$request(param).then((res) => {
					if (res.state == 200) {
						let result = res.data
						that.logisticsInfo = result || {} //物流信息
						that.logisticsRouteList = result.routeList || [] //物流轨迹信息
						that.logisticsRouteList = that.logisticsRouteList.map(function(item) {
							return {
								title: item.acceptTime,
								desc: item.acceptStation ? item.acceptStation : item.remark,
							}
						})
						that.loadFlag = true
						if (res.data && res.data.reason) {
							that.logReason = res.data.reason;
						} else {
							that.logReason = that.$L('暂无物流信息，请耐心等待哦') + '~';
						}
					} else {
						this.$api.msg(res.msg)
					}
				})
			},
			//获取推荐商品
			getData() {},

			//切换物流批次
			changeLogistics(index) {
				if (this.logisticsIndex != index) {
					this.logisticsIndex = index;
					this.getLogistics();
				}
			},
			//显示待发货
			showRemain(type) {
				if (type == 'init') {
					let that = this;
					let params = {};
					params.url = 'v3/business/front/orderInfo/orderProductList';
					params.method = 'GET';
					params.data = {
						orderSn: this.orderSn
					};
					this.$request(params).then(res => {
						that.remainProduct = res.data ? res.data : [];
						// 如果有待发货的,展示待发货tab
						if (that.remainProduct.length) {
							this.remain = true
						}
					})
				} else {
					this.logisticsIndex = this.deliverIds.length
				}
			},

			//获取已发货数据
			getLogistics() {
				let that = this;
				let param = {};
				param.url = 'v3/business/front/logistics/order/getTrace';
				param.method = 'GET';
				param.data = {
					deliverId: that.deliverIds[that.logisticsIndex]
				};
				this.$request(param).then(res => {
					this.loaded = false
					if (res.state == 200) {
						let result = res.data;
						that.logisticsInfo = result || {}; //物流信息
						that.logisticsRouteList = result.routeList || []; //物流轨迹信息
						that.logisticsRouteList = that.logisticsRouteList.map(function(item) {
							return {
								"title": item.acceptTime,
								"desc": item.remark || item.acceptStation
							}
						})
						that.loadFlag = true;
						if (res.data && res.data.reason) {
							that.logReason = res.data.reason;
						} else {
							that.logReason = that.$L('暂无物流信息，请耐心等待哦') + '~';
						}
					} else {
						this.$api.msg(res.msg);
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	page {
		background: $bg-color-split;
		width: 750rpx;
		margin: 0 auto;
	}
	
	.logistics_information_type2{
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: 500;
		
		background: #Fff;
		padding: 40rpx 36rpx;
		
		.deli_name{
			color: #666666;
		}
		
		.deli_value{
			color: #121212;
		}
	}

	.look_logistics {
		width: 100%;
		background: #f5f5f5;

		.order_send {
			overflow-x: auto;
			padding: 30rpx;
			border-bottom: 1rpx solid #F2F2F2;

			.order_send_item {
				color: #111111;
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				margin-right: 74rpx;
				white-space: nowrap;

				&.active {
					color: var(--color_main);
					font-weight: bold;
				}
			}
		}

		.goods_list {
			padding: 20rpx;
			margin-top: 20rpx;
			background-color: #fff;
			margin: 0 20rpx;
		}

		.logistics_des {
			background-color: #ffffff;
			display: flex;
			padding: 28rpx 22rpx 0;
			box-sizing: border-box;
			padding-bottom: 20rpx;
			border-radius: 20rpx 20rpx 0 0;

			&.no_data_look {
				padding-bottom: 0;
				border-radius: 20rpx
			}

			.goods_image {
				width: 200rpx;
				height: 200rpx;
				background: #f3f3f3;
				border-radius: 14rpx;

				.image {
					background-position: center center;
					background-repeat: no-repeat;
					background-size: cover;
					width: 200rpx;
					height: 200rpx;
					border-radius: 14rpx;
				}
			}

			.logistics_desc {
				display: flex;
				flex-direction: row;
				justify-content: start;
				align-items: center;
				margin-left: 10rpx;
				border-bottom: 1px solid #f5f5f5;
				padding-bottom: 28rpx;
				width: 100%;

				.desc_image {
					width: 40rpx;
					height: 40rpx;
				}

				.logistics_type {
					margin-left: 20rpx;
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: bold;
					color: #121212;
				}
			}
		}

		.lofistics_info {
			background: #ffffff;
			border-radius: 15rpx;
			padding: 20rpx;
		}

		.no_data {
			width: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;

			image {
				width: 380rpx;
				height: 280rpx;
				margin: 81rpx 0 43rpx;
			}

			text {
				font-size: 26rpx;
				font-family: Source Han Sans CN;
				font-weight: 400;
				color: #9a9a9a;
			}
		}

		.recomment {
			margin-top: 60rpx;
		}
	}
</style>