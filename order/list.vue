<template>
	<view class="content" :style="mix_diyStyle">
		<!-- 搜索头部分 start -->
		<view class="sea_input_part">
			<view class="search_center">
				<image class="search_icon" :src="imgUrl + 'search.png'" @click="search"></image>
				<input disabled class="sea_input" type="text" v-model="keyword" :placeholder="$L('订单号/商品名称/店铺名称')" @click="search" />
				<image v-show="keyword" class="clear_content" :src="imgUrl + 'input_clear.png'" @click.stop="clearInput" />
			</view>
			<view class="filter" @click="showFilter">{{ timeState == 0 ? $L("筛选") : $L("已筛选") }}</view>
		</view>
		<!-- 搜索头部分 end -->
		<scroll-view class="navbar" scroll-x="true" :scroll-into-view="targetView">
			<view class="flex_row_start_center" style="height: 100%">
				<view v-for="(item, index) in navList" :key="index" class="nav-item" :id="'item' + item.state" :class="{ current: tabCurrentIndex === index }" @click="tabClick(index)">
					{{ item.text }}
				</view>
			</view>
		</scroll-view>

		<swiper :current="tabCurrentIndex" class="swiper-box" duration="300" @change="changeTab" :disable-touch="clientH5">
			<swiper-item class="tab-content" v-for="(tabItem, tabIndex) in navList" :key="tabIndex">
				<!-- <view class="scroll_content"> -->
				<view v-if="triggered" :class="{ loading: true, show_loading: triggered }">
					<image :src="imgUrl + 'loading_icon.gif'" mode="aspectFit"></image>
				</view>
				<scroll-view
					class="list-scroll-content"
					scroll-y
					@scrolltolower="scrollLoadData"
					:lower-threshold="150"
					@scroll="(e) => handleScroll(e, tabIndex)"
					:scroll-top="scrollToTop"
					:refresher-enabled="false"
					:refresher-threshold="45"
					refresher-background="#f8f8f8"
					:refresher-triggered="triggered"
					@refresherpulling="onPulling"
					:refresher-default-style="refreshStyle"
					@refresherrefresh="onRefresh"
				>
					<!-- 订单列表 -->
					<template v-if="tabItem.orderList.length > 0">
						<view v-for="(item, index) in tabItem.orderList" :key="index" class="order-item flex_column_start_start" @click.stop="goOrderDetail(item.orderSn, item)">
							<view class="i-top">
								<!-- <text class="order_sn">订单编号：{{item.orderSn}}</text> -->
								<view class="store_name" @click.stop="goShopHome(item.storeId)">
									<image class="store_logo" :src="imgUrl + 'goods_detail/store_logo.png'"></image>
									<text class="store_name_text">{{ item.storeName }}</text>
									<image class="store_logo_right" :src="item.storeId == 0 ? '' : imgUrl + 'order/store_right.png'"></image>
									<!-- <text class="iconfont iconziyuan11"></text> -->
								</view>
								<text class="state" :style="{ color: item.stateTipColor }">{{ item.orderStateValue }}</text>
							</view>
							<!-- 循环更改 -->
							<view v-if="item.orderProductListVOList" class="goods-box flex_row_between_center" v-for="(goodsItem, goodsIndex) in item.orderProductListVOList" :key="goodsIndex">
								<view class="left flex_row_start_start">
									<view class="goods-img" :style="{ backgroundImage: 'url(' + goodsItem.productImage + ')' }"></view>
									<view class="goods_info flex_column_between_start">
										<view class="flex_column_start_start">
											<text class="goods_name">{{ goodsItem.goodsName }}</text>
											<text class="spec_info" v-if="goodsItem.specValues">{{ goodsItem.specValues }}</text>
										</view>

										<block v-if="goodsItem.isGift == 0">
											<view class="label_con flex_row_start_start" v-if="item.orderType == 103">
												<view class="act_label preSale">{{ $L("预售") }}</view>
												<view v-if="item.orderType == 103 && item.deliverTime" class="presale_deliver_time">{{ item.deliverTime }} {{ $L("开始发货") }}</view>
											</view>
											<view class="label_con flex_row_start_start" v-if="item.orderType == 102">
												<view class="act_label pinGroup">{{ $L("拼团") }}</view>
												<view v-if="item.orderType == 103 && item.deliverTime" class="presale_deliver_time">{{ item.deliverTime }} {{ $L("开始发货") }}</view>
											</view>
											<view class="label_con flex_row_start_start" v-if="item.orderType == 105">
												<view class="act_label ladder">{{ $L("阶梯团") }}</view>
											</view>
											<view class="label_con flex_row_start_start" v-if="item.orderType == 104">
												<view class="act_label seckill">{{ $L("秒杀") }}</view>
											</view>
										</block>
									</view>
								</view>
								<view class="right flex_column_center_end">
									<block v-if="goodsItem.isGift == 0">
										<view class="price_info flex_row_end_end">
											<text class="unit">{{ $L("￥") }}</text>
											<text class="price_int">{{ $getPartNumber(goodsItem.productShowPrice, "int") }}</text>
											<text class="price_decimal">{{ $getPartNumber(goodsItem.productShowPrice, "decimal") }}</text>
										</view>
										<text class="goods_num">*{{ goodsItem.productNum }}</text>
									</block>
									<block v-else>
										<!-- <text class="price_decimal">赠品</text> -->
										<text class="goods_num_give">{{ $L("赠品") }}</text>
										<text class="goods_num">*{{ goodsItem.productNum }}</text>
									</block>
								</view>
								<view class="goods_return_status" v-if="goodsItem.afsStateValue">
									{{ goodsItem.afsStateValue }}
								</view>
							</view>
							<view class="price-box">
								{{ $L("共") }}{{ item.goodsNum }}{{ $L("件商品") }}
								<text class="order_mount">{{ $L("合计") }} :</text>
								<view class="total_price flex_row_end_end">
									<text class="unit">{{ $L("¥") }}</text>
									<text class="price_int">{{ $getPartNumber(item.totalMoney, "int") }}</text>
									<text class="price_decimal">{{ $getPartNumber(item.totalMoney, "decimal") }}</text>
								</view>
							</view>
							<view v-if="((item.orderState == 10 || item.orderState == 20) && item.isVirtualGoods == 1 && !item.isUpdateAddress) || (item.orderState == 40 && item.evaluateState == 3) || item.orderState == 31" style="height: 30rpx"></view>
							<view class="action-box">
								<!-- 待付款、待发货订单可以修改地址 -->
								<button v-if="(item.orderState == 10 || item.orderState == 20) && item.isVirtualGoods == 1 && item.isPickup == 0" class="action-btn flex_row_center_center" @click.stop="editAddress(item)">
									{{ $L("修改地址") }}
								</button>
								<!-- 待收货、已完成订单可以查看物流 -->
								<button v-if="(item.orderState == 30 || item.orderState == 40 || item.orderState == 31) && item.isVirtualGoods == 1 && !item.isPickup" class="action-btn flex_row_center_center" @click.stop="lookLogistics(item.orderSn)">
									{{ $L("查看物流") }}
								</button>
								<!-- 待收货订单可以确认收货 -->
								<button v-if="item.orderState == 30 && item.lockState == 0 && item.isPickup == 0" class="action-btn recom flex_row_center_center" @click.stop="confirmReceipt('open', item.orderSn)">
									{{ $L("确认收货") }}
								</button>
								<!-- 待评价订单可以评价 -->
								<!-- 	<button v-if="item.orderState == 40 && item.evaluateState!=3"
									class="action-btn recom flex_row_center_center"
									@click.stop="remainEvaluated(item.orderSn)">{{$L('评价')}}</button> -->
								<!-- 待付款订单可以取消订单 -->
								<button v-if="item.orderState == 10" class="action-btn flex_row_center_center" @click.stop="cancelPopup(item)">{{ $L("取消订单") }}</button>
								<!-- 待付款订单可以立即支付 -->
								<button v-if="item.orderState == 10 && !((item.orderType == 105 && item.orderSubState == 102 && item.depositRemainTime > 0) || (item.orderType == 103 && item.orderSubState == 102 && item.remainEndTime > 0))" class="action-btn recom flex_row_center_center" @click.stop="goPay(item)">
									{{ $L("立即支付") }}
								</button>
								<!-- 已取消、全部评价完成订单可以删除订单 -->
								<button v-if="item.orderState == 0 || (item.orderState == 40 && item.evaluateState == 3) || item.orderState == 50" class="action-btn flex_row_center_center" @click.stop="delOrder(item.orderSn)">
									{{ $L("删除订单") }}
								</button>
							</view>
						</view>
					</template>
					<view v-if="tabItem.loadingState != 'first_loading' && tabItem.orderList.length == 0" class="empty_part flex_column_start_center">
						<image :src="imgUrl + 'empty_orders.png'" />
						<text>{{ $L("这里空空如也~快去商品中心加购商品吧！") }}</text>
						<button class="flex_row_center_center" @click="goGoodsList" :plain="true">{{ $L("马上去逛逛") }}</button>
					</view>
					<view v-else :class="scrollTop > 0 && scrollCurrent > 0 ? 'scroll_loading' : ''">
						<loadingState :state="tabItem.loadingState" />
					</view>
					<!-- <view v-if="recommendShow && navList[tabCurrentIndex].loadingState != 'first_loading' && tabIndex == tabCurrentIndex">
						<recommendGoods ref='recomment_goods' />
					</view> -->
				</scroll-view>
			</swiper-item>
		</swiper>

		<!-- 取消订单选择原因弹框 -->
		<uni-popup ref="cancelPopup" type="bottom">
			<view class="cancel_popup">
				<view class="popup_top">
					<text>{{ $L("取消原因") }}</text>
					<image :src="imgUrl + 'order-detail/guanbi.png'" mode="aspectFit" @click="notCancel"></image>
				</view>
				<scroll-view class="uni-list cancel_list" scroll-y="true">
					<radio-group @change="radioChange">
						<label class="cancle_pre" v-for="(item, index) in cancelList" :key="index">
							<text>{{ item.content }}</text>
							<radio :value="item.reasonId" :checked="item.reasonId == reasonId" color="var(--color_main)" style="transform: scale(0.8); margin-right: 0" />
						</label>
					</radio-group>
				</scroll-view>
				<view class="cancel_popup_btn">
					<text class="" @click="notCancel()">{{ $L("暂不取消") }}</text>
					<text class="" @click="confirmCancel()">{{ $L("确定取消") }}</text>
				</view>
			</view>
		</uni-popup>

		<!-- 预售，阶梯团定金取消订单提示 -->
		<uni-popup ref="popup" type="dialog">
			<uni-popup-dialog type="input" :title="$L('提示')" :content="$L('取消该订单定金不予退还,确定取消?')" :duration="2000" @close="acDialog(false)" @confirm="acDialog(true)"></uni-popup-dialog>
		</uni-popup>

		<!-- 确认收货提示 -->
		<uni-popup ref="receivePopup" type="dialog">
			<uni-popup-dialog type="input" :title="$L('提示')" :content="$L('确认收货?')" :duration="2000" @close="acDialog(false)" @confirm="confirmReceipt('confirm')"></uni-popup-dialog>
		</uni-popup>

		<!-- 筛选栏 -->
		<store-time ref="storeTime" @updateFilter="updateFilter"></store-time>
		<!-- 筛选栏 -->

		<loginPop ref="loginPop" @closeLogin="$Router.back(1)" @confirmLogin="showState = true"></loginPop>
	</view>
</template>

<script>
import loadingState from "@/components/loading-state.vue";
import recommendGoods from "@/components/recommend-goods.vue";
import uniPopup from "@/components/uni-popup/uni-popup.vue";
import uniPopupDialog from "@/components/uni-popup/uni-popup-dialog.vue";
import storeTime from "@/components/filter_store_time";
import { mapState } from "vuex";
export default {
	components: {
		loadingState,
		recommendGoods,
		uniPopup,
		uniPopupDialog,
		storeTime
	},
	data() {
		return {
			imgUrl: getApp().globalData.imgUrl,
			tabCurrentIndex: 0,
			navList: [
				{
					state: 0,
					text: this.$L("全部"),
					loadingState: "first_loading",
					orderList: [],
					current: 1 //分页
				},
				{
					state: 1,
					text: this.$L("待付款"),
					loadingState: "first_loading",
					orderList: [],
					current: 1 //分页
				},
				{
					state: 2,
					text: this.$L("待发货"),
					loadingState: "first_loading",
					orderList: [],
					current: 1 //分页
				},
				{
					state: 3,
					text: this.$L("待收货"),
					loadingState: "first_loading",
					orderList: [],
					current: 1 //分页
				},
				{
					state: 4,
					text: "部分发货",
					loadingState: "first_loading",
					orderList: [],
					current: 1 //分页
				}
				// {
				// 	state: 5,
				// 	text: this.$L('待评价'),
				// 	loadingState: 'first_loading',
				// 	orderList: [],
				// 	current: 1, //分页
				// }
			],
			stopPullDownRefresh: false, //是否下拉刷新中
			current: "0", //取消原因当前点击的是第0项
			reasonId: -1, //取消原因当前点击的原因id
			cancelList: [], //取消原因列表
			curOrderSn: "", //当前订单的订单号
			isHasMore: true,
			pn: 1,
			recGoodsList: [],
			isloading: "first_loading",
			isShow: false,
			recommendShow: false, //推荐商品是否显示
			ifOnShow: false,
			selOrderSn: "",
			showState: false,
			keyword: "", //搜索关键词
			refresh: false, //从搜索页返回后是否需要刷新
			timeState: 0, //订单列表时间筛选条件
			storeId: "",

			//下拉刷新start
			triggered: false,
			refreshStyle: "white",
			refresherEnabled: false,
			_freshing: false,

			targetView: "item0",

			scrolling: false, //是否进入滚动事件处理中
			scrollTop: 0, //滚动条滚动高度
			scrollToTop: 0, //返回列表页时当前tab滚动到的高度
			scrollCurrent: 0 //返回列表页时当前tab滚动到的页数
		};
	},
	onLoad(options) {
		setTimeout(() => {
			uni.setNavigationBarTitle({
				title: this.$L("我的订单")
			});
		}, 0);

		/**
		 * 修复app端点击除全部订单外的按钮进入时不加载数据的问题
		 * 替换onLoad下代码即可
		 */
		if (this.$Route.query.state) {
			this.tabCurrentIndex = +this.$Route.query.state;
		} else {
			this.tabCurrentIndex = 0;
		}

		this.targetView = "item01";
		this.$nextTick(() => {
			this.targetView = `item${this.tabCurrentIndex}`;
		});
		this.initialList();

		//app-1-start
		// #ifdef APP-PLUS
		this.refreshStyle = "none";
		// #endif
		//app-1-end

		// #ifdef APP-PLUS||MP-WEIXIN
		this.refresherEnabled = true;
		// #endif
	},

	onShow() {
		// 返回列表页判断是否进入滚动定位-start
		if (this.scrollTop > 0 && this.navList[this.tabCurrentIndex].current > 1) {
			this.scrollToTop = 0;
			this.scrollCurrent = this.navList[this.tabCurrentIndex].current;
		}
		// 返回列表页判断是否进入滚动定位-end
		if (this.showState || this.refresh) {
			let index = this.tabCurrentIndex;
			if (index > 0) {
				this.navList[0].loadingState = "first_loading";
				this.navList[0].orderList = [];
				this.navList[0].current = 1;
			}
			this.initialList();
			this.showState = false;
			this.recommendShow = false;
			this.refresh = false;
		}
	},
	computed: {
		...mapState(["userInfo", "hasLogin"]),
		clientH5() {
			// #ifdef H5
			return true;
			// #endif
			// #ifndef H5
			return false;
			// #endif
		}
	},
	//下拉刷新
	onPullDownRefresh() {
		this.stopPullDownRefresh = true; //下拉刷新状态
		this.initialList();
	},
	methods: {
		initialList() {
			let index = this.tabCurrentIndex;
			let navItem = this.navList[index];
			const userInfo = uni.getStorageSync("userInfo");
			if (this.hasLogin || userInfo) {
				uni.showLoading({
					title: this.$L("加载中"),
					icon: "none"
				});
				navItem.loadingState = "first_loading";
				navItem.orderList = [];
				navItem.current = 1;
				this.loadData();
			} else {
				this.$refs.loginPop.openLogin("no_replace");
				navItem.loadingState = "no_more_data";
				// this.recommendShow = true
			}
		},

		//输入关键词
		search() {
			this.$Router.push({
				path: "/pages/search/search",
				query: {
					type: "order",
					key: this.keyword
				}
			});
		},
		//清空输入
		clearInput() {
			this.keyword = "";
			this.navList[this.tabCurrentIndex].current = 1;
			this.getOrderList();
		},
		//甄选推荐
		getData() {
			// this.$refs.recomment_goods[0].getMoreData();
		},
		//获取订单列表
		loadData(source) {
			//将订单挂载到tab列表下,起到缓存的效果，避免多次请求
			let index = this.tabCurrentIndex;
			let navItem = this.navList.filter((item) => item.state == index)[0];
			let state = navItem.state;

			if (navItem.loadingState === "loading") {
				//防止重复加载
				return;
			}

			this.getOrderList();
		},
		//滚动触底事件
		scrollLoadData() {
			if (this.scrolling || this.scrollCurrent > 0) return;
			let index = this.tabCurrentIndex;
			let navItem = this.navList.filter((item) => item.state == index)[0];
			if (navItem.loadingState != "no_more_data") {
				this.scrolling = true;
				this.getOrderList();
			}

			if (navItem.loadingState == "no_more_data") {
				this.getData();
			}
		},
		//滚动条滚动事件
		handleScroll(e, tabIndex) {
			if (this.tabCurrentIndex == tabIndex && !this.scrollCurrent) {
				this.scrollTop = e.target.scrollTop;
			}
		},
		//下拉刷新事件start
		onPulling(e) {
			this.triggered = true;
		},
		onRefresh() {
			if (this._freshing) return;
			this._freshing = true;
			let index = this.tabCurrentIndex;
			let navItem = this.navList[index];
			navItem.current = 1;
			navItem.loadingState = "refreshing";
			setTimeout(() => {
				this.loadData();
			}, 1000);
		},
		onRestore() {},
		onAbort() {},
		//下拉刷新事件end

		//此方法只有删除订单，取消订单等需要从列表中删除订单时调用，其余获取订单列表请调用loadData
		getOrderList() {
			let _this = this;
			let index = this.tabCurrentIndex;
			let navItem = this.navList.filter((item) => item.state == index)[0];
			let state = navItem.state;

			let param = {};
			param.url = "v3/business/front/orderInfo/list";
			param.data = {};
			param.data.pageSize = 10;
			param.data.current = navItem.current;
			param.data.time = this.timeState;
			if (this.keyword) {
				param.data.keyword = this.keyword;
			}
			navItem.loadingState = navItem.loadingState == "first_loading" ? navItem.loadingState : "loading";
			//状态处理
			if (navItem.state == 0) {
				param.data.state = ""; //全部订单
			} else if (navItem.state == 1) {
				param.data.orderState = 10; //待付款
			} else if (navItem.state == 2) {
				param.data.orderState = 20; //待发货
			} else if (navItem.state == 3) {
				param.data.orderState = 30; //待收货
			} else if (navItem.state == 4) {
				param.data.orderState = 31; //部分发货
			} else if (navItem.state == 5) {
				//待评价
				param.data.orderState = 40;
				param.data.evaluateState = 1;
			}

			this.$request(param).then((res) => {
				if (this.scrolling) {
					this.scrolling = false;
				}
				if (this._freshing) {
					this.triggered = false;
				}
				this._freshing = false;
				if (res.state == 200) {
					if (navItem.current == 1) {
						navItem.orderList = res.data.list;
					} else {
						navItem.orderList = navItem.orderList.concat(res.data.list);
					}
					let hasMore = this.$checkPaginationHasMore(res.data.pagination); //是否还有数据
					if (hasMore) {
						navItem.current++;
						if (!(this.scrollCurrent > 0 && this.scrollCurrent >= navItem.current)) {
							navItem.loadingState = "allow_loading_more";
						}
						this.recommendShow = false;
					} else {
						navItem.loadingState = "no_more_data";
						this.recommendShow = true;
					}
					uni.hideLoading();

					// 返回列表页判断是否进入滚动定位-start
					if (this.scrollCurrent > 0) {
						if (hasMore && this.scrollCurrent >= navItem.current) {
							navItem.loadingState = "loading";
							this.getOrderList();
						} else if ((!hasMore && this.scrollCurrent == navItem.current) || (hasMore && this.scrollCurrent < navItem.current)) {
							//滚动到最后一个需要获取的页面时，再给滚动高度赋值
							this.scrollCurrent = 0;
							setTimeout(() => {
								_this.scrollToTop = _this.scrollTop;
							}, 200);
						}
					}
					// 返回列表页判断是否进入滚动定位-end
				} else {
					navItem.loadingState = "no_more_data";
					this.$api.msg(res.msg);
				}
				if (this.stopPullDownRefresh) {
					this.stopPullDownRefresh = false;
					uni.stopPullDownRefresh();
				}
			});
		},
		//刷新当前列表
		refreshOrderList() {
			let index = this.tabCurrentIndex;
			let navItem = this.navList.filter((item) => item.state == index)[0];
			navItem.current = 1;
			this.getOrderList();
		},
		//swiper 切换
		changeTab(e) {
			this.tabCurrentIndex = e.target.current;
			this.isHasMore = true;
			this.pn = 1;
			this.recGoodsList = [];
			this.isloading = "first_loading";
			this.loading = false;
			this.loadData("tabChange");
		},
		//顶部tab点击
		tabClick(index) {
			this.tabCurrentIndex = index;
			this.isHasMore = true;
			this.pn = 1;
			this.isloading = "first_loading";
			this.loading = false;
			this.recGoodsList = [];
			// #ifdef MP-ALIPAY
			this.loadData("tabChange");
			// #endif
		},
		//马上去逛逛事件
		goGoodsList() {
			this.showState = true;
			this.$Router.push(`/standard/product/list`);
		},
		//跳转订单详情页
		goOrderDetail(orderSn, item) {
			this.showState = true;
			if (item.isPickup == 1) {
				// 自提
				this.$Router.push({
					path: "/order/detailPickup",
					query: {
						orderSn
					}
				});
			} else if (item.isPickup == 2 || item.isPickup == 3) {
				// 外卖 外送自提
				this.$Router.push({
					path: "/order/detailTakeaway",
					query: {
						orderSn
					}
				});
			} else {
				this.$Router.push({
					path: "/order/detail",
					query: {
						orderSn
					}
				});
			}
		},
		//删除订单操作
		delOrder(orderSn) {
			let that = this;
			uni.showModal({
				title: that.$L("提示"),
				content: that.$L("确定删除该订单?"),
				confirmColor: this.diyStyle_var["--color_main"],
				success: function (res) {
					if (res.confirm) {
						let param = {};
						param.url = "v3/business/front/orderOperate/delete";
						param.method = "POST";
						param.data = {};
						param.data.orderSn = orderSn;
						that.$request(param)
							.then((res) => {
								if (res.state == 200) {
									that.refreshOrderList();
									that.refreshOrder();
									that.$api.msg(res.msg);
								} else {
									that.$api.msg(res.msg);
								}
							})
							.catch((e) => {
								//异常处理
							});
					} else if (res.cancel) {
					}
				}
			});
		},
		//立即支付
		goPay(val) {
			let goodsInfo = val.orderProductListVOList[0];
			this.showState = true;
			if (val.orderType == 105 && val.orderSubState == 102 && val.depositRemainTime == 0) {
				//阶梯团付尾款
				this.$Router.push({
					path: "/order/confirmOrder",
					query: {
						goodsId: goodsInfo.goodsId,
						productId: goodsInfo.productId,
						numbers: goodsInfo.productNum,
						ifcart: 2,
						orderSn: val.orderSn
					}
				});
			} else if (val.orderType == 103 && val.orderSubState == 102 && val.depositRemainTime == 0) {
				this.$Router.push({
					path: "/order/confirmOrder",
					query: {
						goodsId: goodsInfo.goodsId,
						productId: goodsInfo.productId,
						numbers: goodsInfo.productNum,
						ifcart: 2,
						orderSn: val.orderSn
					}
				});
			} else {
				this.$Router.push({
					path: "/order/pay",
					query: {
						paySn: val.paySn,
						payMethodType: "orderList"
					}
				});
			}
		},
		//打开取消订单弹框
		cancelPopup(item) {
			if (!item.isRefundDeposit && (item.orderType == 105 || item.orderType == 103) && item.orderSubState == 102) {
				this.curOrderSn = item.parentSn;
				this.$refs.popup.open();
			} else {
				this.$refs.cancelPopup.open();
				this.curOrderSn = item.parentSn;
				this.getCancelList();
			}
		},
		// 预售,阶梯团的提示确认
		acDialog(type) {
			if (type == true) {
				this.$refs.popup.close();
				this.$refs.cancelPopup.open();
				this.getCancelList();
			} else {
				this.$refs.popup.close();
			}
		},
		//获取取消订单原因列表
		getCancelList() {
			let param = {};
			param.url = "v3/system/front/reason/list";
			param.method = "GET";
			param.data = {};
			param.data.type = 104;
			this.$request(param)
				.then((res) => {
					if (res.state == 200) {
						this.cancelList = res.data || [];
						this.cancelList && this.cancelList.map((item, index) => (item.value = "" + index));
						this.reasonId = this.cancelList[0].reasonId;
					} else {
						this.$api.msg(res.msg);
					}
				})
				.catch((e) => {
					//异常处理
				});
		},
		//取消原因单选框切换
		radioChange(e) {
			this.reasonId = e.detail.value;
		},
		//暂不取消订单
		notCancel() {
			this.$refs.cancelPopup.close();
			// this.goRefresh();
		},
		//确定取消订单
		confirmCancel() {
			let that = this;
			uni.showModal({
				title: that.$L("提示"),
				content: that.$L("确定取消该订单?"),
				success: function (res) {
					if (res.confirm) {
						let param = {};
						param.url = "v3/business/front/orderOperate/cancel";
						param.method = "POST";
						param.data = {};
						param.data.parentSn = that.curOrderSn;
						param.data.reasonId = that.reasonId;
						that.$request(param)
							.then((res) => {
								if (res.state == 200) {
									that.refreshOrderList();
									that.refreshOrder();
									that.$api.msg(res.msg);
									that.$refs.cancelPopup.close();
								} else {
									that.$api.msg(res.msg);
								}
							})
							.catch((e) => {
								//异常处理
							});
					} else if (res.cancel) {
						that.$refs.cancelPopup.close();
					}
				}
			});
		},
		//更新当前页面方法
		goRefresh() {
			let pages = getCurrentPages();
			let currPage = pages[pages.length - 1]; //当前页面
			let index = this.tabCurrentIndex;
			let navItem = this.navList[index];
			navItem.loadingState = "first_loading";
			navItem.orderList = [];
			navItem.current = 1;
			currPage.loadData(); //更新当前页面数据
		},
		//查看物流
		lookLogistics(orderSn) {
			this.showState = true;
			this.$Router.push({
				path: "/order/lookLogistics",
				query: {
					orderSn
				}
			});
		},
		//修改地址
		editAddress(item) {
			this.showState = true;
			let { receiverAddress, receiverAreaInfo, receiverMobile, orderSn } = item;
			this.$Router.push({
				path: "/newPages/address/list",
				query: {
					source: 2,
					orderSn,
					sourceOrderAddress: encodeURIComponent(`${receiverAddress},${receiverMobile}`)
				}
			});
		},
		// 确认收货
		confirmReceipt(type, orderSn) {
			switch (type) {
				case "open": {
					this.selOrderSn = orderSn;
					this.$refs.receivePopup.open();
					break;
				}
				case "confirm": {
					this.$refs.receivePopup.close();
					let param = {};
					param.url = "v3/business/front/orderOperate/receive";
					param.method = "POST";
					param.data = {
						orderSn: this.selOrderSn
					};
					this.$request(param).then((res) => {
						if (res.state == 200) {
							uni.showToast({
								title: this.$L("收货成功！"),
								icon: "none",
								duration: 700
							});
							this.refreshOrderList();
							this.refreshOrder();
						} else {
							this.$api.msg(res.msg);
						}
					});
				}
			}
		},
		// 评价
		remainEvaluated(orderSn) {
			this.showState = true;
			this.$Router.push({
				path: "/order/publishEvaluation",
				query: {
					orderSn
				}
			});
		},
		//去店铺首页
		goShopHome(storeId) {
			if (storeId != 0) {
				this.showState = true;
				this.$Router.push({
					path: "/standard/store/shopHomePage",
					query: {
						vid: storeId
					}
				});
			}
		},
		//当全部的部分订单操作后，其他状态的列表要更新
		refreshOrder() {
			let index = this.tabCurrentIndex;
			if (index == 0) {
				this.navList[1].loadingState = "first_loading";
				this.navList[2].loadingState = "first_loading";
				this.navList[3].loadingState = "first_loading";
				this.navList[4].loadingState = "first_loading";
			}
		},
		//打开筛选
		showFilter() {
			this.$refs.storeTime.open();
		},
		//筛选组件传值
		updateFilter(data) {
			if (this.firstOpen) {
				this.firstOpen = false;
				return;
			}
			this.storeId = data.storeId;
			this.timeState = data.timeId;
			this.tabCurrentIndex = 0;
			(this.navList = [
				{
					state: 0,
					text: this.$L("全部"),
					loadingState: "first_loading",
					orderList: [],
					current: 1 //分页
				},
				{
					state: 1,
					text: this.$L("待付款"),
					loadingState: "first_loading",
					orderList: [],
					current: 1 //分页
				},
				{
					state: 2,
					text: this.$L("待发货"),
					loadingState: "first_loading",
					orderList: [],
					current: 1 //分页
				},
				{
					state: 3,
					text: this.$L("待收货"),
					loadingState: "first_loading",
					orderList: [],
					current: 1 //分页
				},
				{
					state: 4,
					text: "部分发货",
					loadingState: "first_loading",
					orderList: [],
					current: 1 //分页
				},
				{
					state: 5,
					text: this.$L("待评价"),
					loadingState: "first_loading",
					orderList: [],
					current: 1 //分页
				}
			]),
				this.getOrderList();
		}
	}
};
</script>

<style lang="scss">
.content {
	background: $bg-color-split;
	height: 100%;
	width: 750rpx;
	margin: 0 auto;
}

.tab-content {
	position: relative;
}

.swiper-box {
	height: calc(100vh - 40px);
}

swiper {
	height: calc(100vh - 40px);
}

button::after {
	border: none;
}

.list-scroll-content {
	height: 100%;

	.scroll_loading {
		width: 100%;
		position: fixed;
		top: 80rpx;
		left: 50%;
		transform: translateX(-50%);
	}
}

.label_con {
	position: relative;
	left: 0;

	.act_label {
		height: 36rpx;
		border-radius: 15rpx;
		line-height: 36rpx;
		padding: 0 14rpx;
	}

	.preSale {
		// width: 38px;
		// height: 14px;
		background: linear-gradient(90deg, #ec0093 0%, #ff085b 100%);
		border-radius: 4rpx;
		color: #fff;
		font-size: 24rpx;
	}

	.pinGroup {
		background: linear-gradient(45deg, #ff6000 0%, #ff9c00 100%);
		border-radius: 4rpx;
		color: #fff;
		font-size: 24rpx;
	}

	.ladder {
		background: linear-gradient(22deg, #fe901e 0%, #fead28 100%);
		color: #fff;
		font-size: 24rpx;
		border-radius: 4rpx;
	}

	.seckill {
		background: linear-gradient(to right, #fc5300, #ff1353);
		color: #fff;
		font-size: 24rpx;
		border-radius: 4rpx;
	}
}

.sea_input_part {
	display: flex;
	align-items: center;
	width: 750rpx;
	height: 88rpx;
	background-color: #fff;
	margin: 0 auto;
	padding-left: 10rpx;
	padding-right: 10rpx;

	.search_center {
		overflow: hidden;
		display: flex;
		align-items: center;
		flex: 1;
		height: 65rpx;
		border-radius: 32.5rpx;
		background-color: #f5f5f5;
		border: 1rpx solid #f5f5f5;
		margin-left: 10rpx;
		padding-left: 20rpx;

		.search_icon {
			width: 30rpx;
			height: 30rpx;
			margin-right: 22rpx;
		}

		.sea_input {
			flex: 1;
			height: 65rpx;
			font-size: 28rpx;
			color: #333;
			background-color: #f5f5f5;
		}
	}

	.clear_content {
		width: 45rpx !important;
		height: 45rpx !important;
		margin-right: 15rpx !important;
	}

	.filter {
		min-width: 66rpx;
		font-size: 28rpx;
		text-align: center;
		margin-left: 10rpx;
	}

	&:after {
		position: absolute;
		content: "";
		left: 0;
		bottom: 0;
		width: 100%;
		height: 1rpx;
		background-color: #eee;
		transform: scaleY(0.5);
	}
}

.navbar {
	display: flex;
	height: 80rpx;
	padding: 0 5px;
	background: #fff;
	position: relative;
	z-index: 10;
	overflow-x: scroll;
	width: 750rpx;
	.nav-item {
		flex: 1;
		display: flex;
		justify-content: center;
		align-items: center;
		height: 100%;
		font-size: 32rpx;
		color: $main-font-color;
		position: relative;
		white-space: nowrap;
		padding: 0 28rpx;
		&.current {
			color: var(--color_main);
			font-size: 32rpx;
			font-weight: bold;

			&:after {
				content: "";
				position: absolute;
				left: 50%;
				bottom: 0;
				transform: translateX(-50%);
				width: 35rpx;
				height: 8rpx;
				background-color: var(--color_main);
				border-radius: 4rpx;
			}
		}
	}
}

.uni-swiper-item {
	height: auto;
}

.order-item {
	width: 100%;
	padding-left: 20rpx;
	background: #fff;
	margin-top: 20rpx;

	.i-top {
		display: flex;
		align-items: center;
		padding-right: 20rpx;
		position: relative;
		width: 100%;
		margin-top: 30rpx;
		border-bottom: 1rpx solid #f2f2f2;
		padding-bottom: 30rpx;

		.store_name {
			flex: 1;
			display: flex;
			align-items: center;

			image {
				width: 34rpx;
				height: 32rpx;
				transform: scale(0.9);
			}

			.store_name_text {
				font-size: 26rpx;
				color: #2d2d2d;
				font-weight: bold;
				margin-left: 10rpx;
			}

			.store_logo_right {
				width: 13rpx;
				height: 22rpx;
				margin-left: 10rpx;
			}
		}

		.state {
			font-size: 26rpx;
			color: var(--color_price);
		}
	}

	.goods-box {
		position: relative;
		padding: 20rpx 0;
		width: 100%;
		border-bottom: 1rpx solid #f2f2f2;

		.goods_return_status {
			position: absolute;
			bottom: 24rpx;
			right: 21rpx;
			font-size: 22rpx;
			color: var(--color_vice);
		}

		.left {
			.goods-img {
				background-size: cover;
				background-position: center center;
				background-repeat: no-repeat;
				width: 200rpx;
				height: 200rpx;
				overflow: hidden;
				background-color: #f8f6f7;
				border-radius: 14rpx;
				flex-shrink: 0;
			}

			.goods_info {
				margin-left: 25rpx;
				padding-top: 10rpx;
				height: 200rpx;
				width: 100%;

				.goods_name {
					color: #2d2d2d;
					font-size: 28rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;
					word-break: break-word;
					line-height: 38rpx;
				}

				.spec_info {
					color: #949494;
					font-size: 24rpx;
					line-height: 28rpx;
					background-color: #f8f8f8;
					padding: 3rpx 7rpx;
					border-radius: 6rpx;
					margin-top: 10rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;
				}

				.presale_deliver_time {
					padding: 0 6rpx;
					border: 2rpx solid var(--color_vice);
					font-size: 24rpx;
					color: var(--color_vice);
					position: absolute;
					left: 90rpx;
					white-space: nowrap;
				}
			}
		}

		.right {
			padding: 0 30rpx 0 24rpx;
			overflow: hidden;
			flex-shrink: 0;

			.price_info {
				color: $main-font-color;
				font-weight: bold;
				align-items: baseline;

				.unit,
				.price_decimal {
					font-size: 24rpx;
					line-height: 28rpx;
				}

				.price_int {
					font-size: 32rpx;
					line-height: 32rpx;
				}
			}

			.goods_num {
				color: #2d2d2d;
				font-size: 24rpx;
			}

			.goods_num_give {
				color: #2d2d2d;
				font-size: 28rpx;
				font-weight: bold;
			}

			.title {
				font-size: 24rpx;
				color: $font-color-dark;
				line-height: 1;
			}

			.attr-box {
				font-size: 22rpx;
				color: $font-color-light;
				padding: 10rpx 12rpx;
			}
		}
	}

	.price-box {
		display: flex;
		justify-content: flex-end;
		align-items: baseline;
		padding: 0 18rpx;
		font-size: 24rpx;
		color: #949494;
		width: 100%;
		margin-top: 30rpx;
		position: relative;

		.order_type {
			position: absolute;
			left: 0;
			top: 0;
			background: linear-gradient(22deg, #fe901e 0%, #fead28 100%);
			height: 36rpx;
			padding: 0 14rpx;
			color: #fff;
			border-radius: 18rpx;
			font-size: 22rpx;
		}

		.order_mount {
			color: #333333;
			font-size: 24rpx;
			margin-left: 20rpx;
		}

		.total_price {
			color: #2d2d2d;
			font-weight: bold;
			align-items: baseline;
			margin-left: 15rpx;

			.unit,
			.price_decimal {
				font-size: 24rpx;
			}

			.price_int {
				font-size: 32rpx;
				line-height: 32rpx;
				margin-left: 5rpx;
			}
		}
	}

	.action-box {
		display: flex;
		justify-content: flex-end;
		align-items: flex-start;
		height: 70rpx;
		position: relative;
		padding-right: 20rpx;
		width: 100%;
		padding-bottom: 20rpx;
		margin-top: 30rpx;
	}

	.action-btn {
		width: 125rpx;
		height: 50rpx;
		margin: 0;
		margin-left: 10rpx;
		padding: 0;
		text-align: center;
		line-height: 50rpx;
		font-size: 24rpx;
		color: $main-font-color;
		background: #fff;
		border-radius: 25rpx;
		border: 1rpx solid #eeeeee;

		&:after {
			border: none;
		}

		&.recom {
			color: #fff;
			background: var(--color_main);
			border: none;
		}
	}
}

.empty_part {
	padding-top: 108rpx;

	image {
		width: 380rpx;
		height: 280rpx;
	}

	text {
		color: $main-third-color;
		font-size: 26rpx;
		margin-top: 57rpx;
	}

	button {
		width: 245rpx;
		height: 66rpx;
		background: var(--color_halo);
		border-radius: 33rpx;
		color: var(--color_main);
		font-size: 30rpx;
		font-weight: bold;
		margin-top: 29rpx;
		border: none;
	}

	uni-button:after {
		border-radius: 200rpx;
		border-color: #fff;
	}
}

.cancel_popup {
	width: 100%;
	height: 700rpx;
	background: #ffffff;
	border-radius: 15rpx 15rpx 0 0;
	position: fixed;
	width: 100% !important;
	z-index: 20;
	bottom: 0;

	.popup_top {
		height: 100rpx;
		width: 100%;
		display: flex;
		padding: 0 39rpx;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1rpx solid #f8f8f8;

		text {
			font-size: 32rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #343434;
			line-height: 32rpx;
		}

		image {
			width: 30rpx;
			height: 30rpx;
		}
	}

	.cancel_list {
		// padding-bottom: 128rpx;
		box-sizing: border-box;
		height: 468rpx;
		z-index: 150;

		.cancle_pre {
			width: 100%;
			padding: 29rpx 40rpx;
			box-sizing: border-box;
			display: flex;
			justify-content: space-between;

			text {
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #666666;
				line-height: 32rpx;
			}
		}
	}

	.cancel_popup_btn {
		position: fixed;
		bottom: 40rpx;
		z-index: 30;
		display: flex;
		width: 100%;
		justify-content: center;

		text:nth-child(1) {
			width: 334rpx;
			height: 70rpx;
			background: var(--color_vice);
			border-radius: 35rpx 0 0 35rpx;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ffffff;
			line-height: 32rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		text:nth-child(2) {
			width: 334rpx;
			height: 70rpx;
			background: var(--color_main);
			border-radius: 0 35rpx 35rpx 0;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ffffff;
			line-height: 32rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
}

.Giveaway {
	width: 100rpx;
	height: 40rpx;
	border: 1rpx solid red;
	line-height: 40rpx;
	text-align: center;
	color: red;
	font-size: 25rpx;
	border-radius: 10rpx;
}
</style>
