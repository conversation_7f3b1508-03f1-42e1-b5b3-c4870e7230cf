<template>
	<view>
		<!-- 选择自提点 start -->
		<uni-popup class="spec_model" ref="specModel" type="bottom" @change="modelChange">
			<view class="spec_model_con">
				<view class="spec_model_content">
					<view class="spec_model_top">
						<text>{{$L("选择自提点")}}</text>
						<image :src="imgUrl + 'goods_detail/close.png'" mode="aspectFit" class="close_spec" @click="close"></image>
					</view>
					<scroll-view scroll-y="true" class="spec_content" @touchmove.stop>
						<view class="store_item" :key="index" v-for="(item, index) in storeList">
							<view>
								<view class="f_16" style="font-weight: bold">
									{{ item.name }}
								</view>
								<view class="f_14" style="margin-top: 20rpx">
									{{ item.address }}
								</view>
							</view>
							<view><radio @click="storeChange(item.id)" color="#367fff" :checked="checkedList.indexOf(item.id) > -1" /></view>
						</view>
					</scroll-view>
				</view>
				<block>
					<view class="spec_btn">
						<text class="spec_add_cart_btn spec_btn_only" @click="addCart">{{$L("确定")}}</text>
					</view>
				</block>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
export default {
	name: 'specModel',
	data() {
		return {
			imgUrl: getApp().globalData.imgUrl,
			checkedList: []
		};
	},
	components: {
		uniPopup
	},
	props: {
		storeList: {
			type: Array,
			default: []
		}
	},
	mounted() {},
	computed: {},
	methods: {
		addCart() {
			this.close();
			this.$emit('chooseOver', this.checkedList);
		},
		storeChange(id) {
			const index = this.checkedList.indexOf(id);
			if (index > -1) {
				this.checkedList.splice(index, 1);
			} else {
				this.checkedList = [id];
			}
		},
		modelChange() {},
		open() {
			this.$refs.specModel.open();
		},
		close() {
			this.$refs.specModel.close();
		}
	}
};
</script>

<style lang="scss" scoped>
.f_14 {
	font-size: 28rpx;
}
.f_16 {
	font-size: 32rpx;
}
.tips {
	color: #9a9a9a;
	font-size: 24rpx;
	margin-top: 60rpx;
}
.spec_model {
	z-index: 9999 !important;
}
.spec_model_con {
	width: 750rpx;
	height: 900rpx;
	background: #ffffff;
	border-radius: 10rpx 10rpx 0;
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	margin: 0 auto;
	z-index: 150;

	.spec_model_content {
		// padding-bottom: 115rpx;

		.spec_model_top {
			display: flex;
			justify-content: space-between;
			padding: 16rpx 22rpx 0 30rpx;
			box-sizing: border-box;
			color: #101010;
			.close_spec {
				position: absolute;
				top: 14rpx;
				right: 14rpx;
				z-index: 9;
				width: 46rpx;
				height: 46rpx;
			}
		}

		.spec_content {
			padding: 60rpx 30rpx;
			height: 700rpx;
			color: #101010;
			.store_item {
				width: 100%;
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 50rpx;
				::v-deep .uni-radio-input-checked {
					background-color: #e61400 !important;
				}
				::v-deep.uni-radio-input:hover {
					border-color: #e61400;
				}
			}
		}
	}

	.spec_btn {
		width: 750rpx;
		height: 98rpx;
		background: #ffffff;
		box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(86, 86, 86, 0.08);
		display: flex;
		align-items: center;
		justify-content: center;
		position: fixed;
		bottom: env(safe-area-inset-bottom);
		/* #ifdef MP */
		/* height: calc(98rpx + env(safe-area-inset-bottom)); */
		/* padding-bottom: constant(safe-area-inset-bottom); */
		/*���� IOS<11.2*/
		/* padding-bottom: env(safe-area-inset-bottom); */
		/*���� IOS>11.2*/
		/* #endif */
		color: #fff;

		.spec_add_cart_btn {
			width: 345rpx;
			height: 70rpx;
			background: #367fff;
			border-radius: 35rpx 0 0 35rpx;
			font-size: 30rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ffffff;
			text-align: center;
			line-height: 70rpx;
		}

		.spec_buy_btn {
			width: 345rpx;
			height: 70rpx;
			background: #367fff;
			border-radius: 0 35rpx 35rpx 0;
			font-size: 30rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ffffff;
			text-align: center;
			line-height: 70rpx;
		}

		.spec_not_stock {
			background: #adadad;
			border-radius: 35rpx;
			font-size: 30rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ffffff;
			text-align: center;
			line-height: 70rpx;
		}

		.spec_seckill_btn {
			background: linear-gradient(45deg, #fc2d2d 0%, #fd572b 100%);
			border-radius: 35rpx;
			font-size: 30rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ffffff;
			text-align: center;
			line-height: 70rpx;
		}

		.spec_btn_only {
			width: 690rpx;
			height: 70rpx;
			border-radius: 35rpx;
			text-align: center;
			line-height: 70rpx;
		}

		.specifications_btn2 {
			width: 690rpx;
			height: 70rpx;
			background: linear-gradient(45deg, #ff5c00 0%, #fce000 0%, #fe8300 0%, #fb9721 100%);
			border-radius: 35rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ffffff;
			line-height: 32rpx;
		}

		.specifications_bottom_btn3 {
			width: 690rpx;
			height: 70rpx;
			background: linear-gradient(45deg, #ff5d00 0%, #fce000 0%, #fe8400 0%, #fb9721 100%);
			border-radius: 35rpx;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ffffff;
			line-height: 30rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.specifications_bottom_btn4 {
			width: 690rpx;
			height: 70rpx;
			background: linear-gradient(45deg, #fb2d2d 0%, #fc572a 100%);
			border-radius: 35rpx;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ffffff;
			line-height: 30rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.specification_add {
			width: 347rpx;
			height: 70rpx;
			background: linear-gradient(45deg, #ff7918 0%, #fea00d 100%);
			border-radius: 34rpx 0 0 34rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #fff;
			font-size: 28rpx;
		}

		.specification_add text:nth-of-type(1),
		.specification_buy text:nth-of-type(1) {
			margin-right: 20rpx;
		}

		.spec_deposit_btn {
			color: #fff;
			background: linear-gradient(45deg, #ff7a18 0%, #fea10e 100%);
		}

		.specification_buy {
			width: 343rpx;
			height: 70rpx;
			background: linear-gradient(45deg, #fb2d2d 0%, #fc572a 100%);
			border-radius: 0 34rpx 34rpx 0;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #fff;
			font-size: 28rpx;
		}
	}
}
</style>
