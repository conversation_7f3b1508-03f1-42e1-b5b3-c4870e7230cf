<!-- 订单详情页面 -->
<template>
	<view class="order_detail" :style="mix_diyStyle">
		<scroll-view class="container" scroll-y @scrolltolower="getData" v-if="allData && isShow">
			<!-- <view class="main_content"
				:style="{backgroundImage:'url('+commonBg+')',backgroundSize:!isVirListEmp?'750rpx 332rpx':'750rpx 452rpx'}"> -->
			<view class="main_content">
				<view class="main_content_box">
					<!-- 订单状态 start -->
					<view class="order_state">
						<!-- 自提订单订单状态 start -->
						<view class="pick_order_state">
							<image :src="imgUrl + 'business/pick_order_icon.png'" mode=""></image>
							<text>{{ allData.orderStateValue }}</text>
						</view>
						<!-- 自提订单订单状态 end -->
					</view>
					<!-- 订单状态 end -->

					<!-- 待提货码 start -->
					<view class="pick_code" v-if="allData.orderState == 20">
						<view class="pick_code_cont" v-if="barcodeVal">
							<tki-barcode ref="barcode" :val="barcodeVal" :opations="{ fontSize: 30 }" :onval="true" />
						</view>
					</view>
					<!-- 待提货码 end -->

					<!-- 物流信息 start -->
					<!-- 齐套发货 -->
					<view class="order_des look_des" v-if="allData.orderState == 30 && allData.orderDeliverList && allData.orderDeliverList.length == 1">
						<view class="order_des_title look_des_title">物流信息</view>
						<block v-if="logisticsList.type == 1">
							<view class="logistics_information_type1">
								<view class="flex_row_start_center">
									<view class="deli_name">物流方式：</view>
									<view class="deli_value">自行配送</view>
								</view>

								<view class="flex_row_start_center" style="margin-top: 18rpx">
									<view class="deli_name">联系人：</view>
									<view class="deli_value">{{ allData.orderDeliverList[0].deliverName }} {{ allData.orderDeliverList[0].deliverMobile }}</view>
								</view>
							</view>
						</block>

						<block v-else>
							<view class="logistics_information" @click="lookLogistics()" v-if="logisticsList.routeList && logisticsList.routeList.length > 0">
								<view class="logistics_time">
									{{ logisticsList.routeList[0].acceptTime }}
								</view>
								<view class="logistics_des text-clamp-ellipsis">
									{{ logisticsList.routeList[0].remark || logisticsList.routeList[0].acceptStation }}
								</view>
							</view>
							<view v-else class="noLogistics">{{ logReason ? logReason : '暂无物流信息' }}</view>
						</block>
					</view>
					<!-- 分批发货 -->
					<view class="order_des look_des" v-else-if="allData.orderDeliverList">
						<view class="order_des_title look_des_title">物流信息</view>
						<view class="order_send flex_row_start_center">
							<view
								v-for="(item, index) in allData.orderDeliverList"
								:key="index"
								@click="changeLogistics(index)"
								class="order_send_item"
								:class="{ active: logisticsIndex == index }"
							>
								包裹{{ index + 1 }}
							</view>
							<view
								class="order_send_item"
								:class="{ active: logisticsIndex == allData.orderDeliverList.length }"
								v-if="remainProduct.length > 0"
								@click="showRemain()"
							>
								待发货
							</view>
						</view>
						<block v-if="logisticsIndex < allData.orderDeliverList.length">
							<block v-if="logisticsList.type == 1">
								<view class="logistics_information_type1 mutiple">
									<view class="flex_row_start_center">
										<view class="deli_name">物流方式：</view>
										<view class="deli_value">自行配送</view>
									</view>

									<view class="flex_row_start_center" style="margin-top: 14rpx">
										<view class="deli_name">联系人：</view>
										<view class="deli_value">
											{{ allData.orderDeliverList[logisticsIndex].deliverName }} {{ allData.orderDeliverList[logisticsIndex].deliverMobile }}
										</view>
									</view>
								</view>
							</block>
							<block v-else>
								<view class="logistics_information" @click="lookLogistics()" v-if="logisticsList.routeList && logisticsList.routeList.length > 0">
									<view class="logistics_time">
										<text>{{ logisticsList.routeList[0].acceptTimeDate }}</text>
										<text style="margin-left: 12rpx">{{ logisticsList.routeList[0].acceptTimeWhen }}</text>
									</view>
									<view class="logistics_des text-clamp-ellipsis">
										{{ logisticsList.routeList[0].remark || logisticsList.routeList[0].acceptStation }}
									</view>
								</view>
								<view v-else class="noLogistics">{{ logReason ? logReason : '暂无物流信息' }}</view>
							</block>
						</block>
						<block v-if="logisticsIndex == allData.orderDeliverList.length">
							<block v-for="(item, index) in remainProduct" :key="item.deliverId">
								<goodsItemO :goods_info="item" :goods_type="4" ref="recom_goods" bodyWidth="100%" :deliverNum="true" :paddingSide="false" />
							</block>
						</block>
						<block v-else-if="allData.orderDeliverList.length > 0">
							<block v-for="(item, index) in allData.orderDeliverList[logisticsIndex].productInfoList" :key="item.deliverId">
								<goodsItemO :goods_info="item" :goods_type="4" ref="recom_goods" bodyWidth="100%" :deliverNum="true" :paddingSide="false" />
							</block>
						</block>
						<view v-else class="noLogistics">暂无产品信息</view>
					</view>
					<!-- 物流信息 end -->

					<!-- 虚拟商品的预留信息start -->
					<view class="virtual_msg" v-if="allData.orderReserveList.length">
						<view class="virtual_msg_item flex_row_start_center" v-for="(item, index) in allData.orderReserveList" :key="index">
							<view class="msg_item_l">{{ item.reserveName }}:</view>
							<view class="msg_item_r">{{ item.reserveValue || '--' }}</view>
						</view>
					</view>
					<!-- 虚拟商品的预留信息end -->

					<!-- 自提订单详细信息 start -->
					<view class="pick_detail">
						<view class="pick_detail_top flex_row_between_start">
							<view class="top_left">
								<view class="pick_title">{{ allData.store.dianName }}</view>
								<view class="pick_title_detail">{{ allData.store.address }}</view>
							</view>
							<!-- <view class="top_right flex_column_start_center" @click="tonavigation">
								<image :src="imgUrl + 'business/navigation.png'" mode=""></image>
								<text class="dao_text">导航</text>
							</view> -->
							<view class="lb_right flex_column_start_center" v-if="allData.store.poiId" @click="tonavigation">
								<image class="icon" mode="widthFix" :src="imgUrl + 'address.png'" />
								<!-- <text>导航</text> -->
							</view>
						</view>
						<view class="pick_detail_next">营业时间：{{ allData.store.daysText }}</view>
					</view>
					<!-- 自提订单详细信息 end -->

					<!-- 买家个人信息 start-->
					<!-- 	<view class="buyer_info" v-if="allData.isVirtualGoods == 1">
						<image :src="imgUrl + 'order-detail/order_address1.png'" mode="aspectFit" class="buyer_map">
						</image>
						<view class="info_det">
							<view class="info_detail">
								<view class="info_name">
									<text class="buyer_namer">{{ allData.receiverName }}</text>
									<text class="buyer_phone">{{ allData.receiverMobile }}</text>
								</view>
								<view class="info_address">{{ allData.receiverAreaInfo + allData.receiverAddress }}
								</view>
							</view>
						</view>
					</view> -->
					<!-- 买家个人信息 end-->

					<!-- 订单内商品信息 start -->
					<view class="order_goods">
						<view class="goods_list">
							<view v-for="(store, index) in orderProductList" :key="index" class="store_item">
								<view class="store_name" @click="goStore(store.storeId)">
									<image class="store_logo" :src="imgUrl + 'goods_detail/store_logo.png'"></image>
									<text class="store_name_text">{{ store.storeName }}</text>
									<text class="iconfont iconziyuan11"></text>
								</view>
								<view
									class="goods_pre"
									v-for="(item, indexChild) in store.orderProductListVOList"
									:key="indexChild"
									@click="goProductDetail(item.productId, item.goodsId)"
								>
									<view class="goods-img" :style="{ backgroundImage: 'url(' + item.productImage + ')' }"></view>
									<view class="goods_pre_right">
										<view class="goods_des">
											<view class="">
												<view class="goods_name">{{ item.goodsName }}</view>
												<view class="goods_content">
													<view class="goods_spec" style="margin-right: 20rpx">{{ $L('数量') }}：{{ item.productNum }}</view>
													<view class="goods_spec" v-if="item.specValues">{{ $L('规格') }}：{{ item.specValues }}</view>
												</view>
											</view>
											<view class="goods_prices">
												<view class="goods_price" v-if="item.isGift == 0">
													<text class="unit">¥</text>
													<text class="price_int">{{ $getPartNumber(item.productShowPrice, 'int') }}</text>
													<text class="price_decimal">{{ $getPartNumber(item.productShowPrice, 'decimal') }}</text>
												</view>
												<view class="goods_num_give" v-else>赠品</view>
												<!-- <view class="goods_num">*{{item.productNum}}</view> -->
												<block v-if="item.afsButton != null">
													<!-- 退款中 可查看退款详情  售后按钮，100-退款（商家未发货），200-退款（商家发货,买家未收货），300-申请售后，401-退款中，402-退款完成,403-换货中，404-换货完成	301,退款失败-平台审核失败-->
													<view
														class="refund_btn refund_btn_o"
														v-if="
															item.afsButton == 401 ||
															item.afsButton == 402 ||
															item.afsButton == 403 ||
															item.afsButton == 404 ||
															item.afsButton == 301
														"
														@click.stop="lookRefundDetail(item.afsSn, item.afsButton)"
													>
														{{ item.afsButtonValue }}
													</view>
													<!-- 退款 可申请退款-->
													<view
														class="refund_btn"
														v-if="item.isGift == 0 && (item.afsButton == 100 || item.afsButton == 200 || item.afsButton == 300)"
														@click.stop="selectService(allData.orderSn, item.orderProductId, item.afsButton)"
													>
														{{ item.afsButtonValue }}
													</view>
												</block>
											</view>
										</view>
									</view>
								</view>
								<!-- 订单内商品信息 end -->
								<!-- <view class="store_price_info no_top"v-if="allData.orderState != 10&&allData.orderType!=105"> -->
								<view class="store_price_info no_top" v-if="allData.orderState == 10 && allData.orderType != 105">
									<view class="freight">
										<text class="freight_title">{{ '商品合计' }}</text>
										<text class="freight_price">￥{{ store.goodsAmount }}</text>
									</view>
									<view class="freight" v-if="store.fullDiscountAmount">
										<text class="freight_title">{{ $L('满优惠') }}</text>
										<text class="freight_price">-￥{{ store.fullDiscountAmount }}</text>
									</view>
									<view class="freight">
										<text class="freight_title">{{ $L('运费') }}</text>
										<text class="freight_price">￥{{ store.expressFee }}</text>
									</view>

									<view class="freight" v-if="store.storeVoucherAmount">
										<text class="freight_title">{{ $L('店铺优惠券') }}</text>
										<text class="freight_price">-￥{{ store.storeVoucherAmount }}</text>
									</view>
									<view class="freight" v-if="store.integralCashAmount">
										<text class="freight_title">{{ $L('积分抵扣') }}</text>
										<text class="freight_price">-￥{{ store.integralCashAmount }}</text>
									</view>
									<view class="actual_payment">
										<view class="actual_payment_title">
											<text>{{ allData.orderState == 10 || (allData.orderState == 0 && allData.payState == 0) ? $L('需付款') : $L('实付款') }}</text>
											<text>{{ $L('(含运费)') }}</text>
										</view>
										<view class="actual_payment_price">
											<text class="unit">¥</text>
											<text>{{ $getPartNumber(store.orderAmount, 'int') }}</text>
											<text>{{ $getPartNumber(store.orderAmount, 'decimal') }}</text>
										</view>
									</view>

									<view class="order_mark" v-if="store.orderRemark">
										<text class="order_mark_title">{{ $L('订单备注') }}</text>
										<text class="order_mark_text">{{ store.orderRemark }}</text>
									</view>
								</view>
								<!-- 联系商家，拨打电话 start-->
								<view class="flex_row_around_center contact_phone">
									<view class="item_con flex_row_center_center border" @click="contactShop(store)">
										<image :src="imgUrl + 'order-detail/lianxishangjia.png'" mode="aspectFit"></image>
										<text>{{ $L('联系商家') }}</text>
									</view>
									<view class="item_con flex_row_center_center" @click="goCall(store)">
										<image :src="imgUrl + 'order-detail/dadianhua.png'" mode="aspectFit"></image>
										<text>{{ $L('拨打电话') }}</text>
									</view>
								</view>
								<!-- 联系商家，拨打电话 end-->
							</view>
						</view>
					</view>

					<!-- 定金预售订单信息 start -->
					<view class="ladder_group flex_column_start_start" v-if="allData.orderType == 103 && allData.presellInfo.isAllPay == 0">
						<template v-for="(presale_item, presale_index) in presaleInfo">
							<view :class="{ item: true, split: presale_index < presaleInfo.length - 1 ? true : false }">
								<view class="title flex_row_start_center">
									<view class="right_split"></view>
									<view class="content">{{ presale_item.title }}</view>
								</view>
								<view class="goods_amount flex_row_between_center">
									<text class="left">{{ presale_item.goods_left }}</text>
									<text class="right">{{ presale_item.goods_right == '--' ? '' : $L('￥') }}{{ presale_item.goods_right }}</text>
								</view>
								<view v-if="presale_item.goods_expand_right" class="goods_amount flex_row_between_center">
									<text class="left">{{ presale_item.goods_expand_left }}</text>
									<text class="right">{{ presale_item.goods_expand_right == '--' ? '' : $L('￥') }}{{ presale_item.goods_expand_right }}</text>
								</view>
								<view class="need_pay_amount flex_row_between_center">
									<text class="left">{{ presale_item.need_pay_left }}</text>
									<text :class="{ right: true, cur: presale_item.is_cur == 1 ? true : false }">
										{{ presale_item.need_pay_right == '--' ? '' : $L('￥') }}{{ presale_item.need_pay_right }}
									</text>
								</view>
							</view>
						</template>
					</view>
					<!-- 定金预售订单信息 end -->

					<!-- 阶梯团订单信息 start -->
					<view class="ladder_group flex_column_start_start" v-if="allData.orderType == 105">
						<template v-for="(ladder_pro_item, ladder_pro_index) in ladderInfo">
							<view :class="{ item: true, split: ladder_pro_index < ladderInfo.length - 1 ? true : false }">
								<view class="title flex_row_start_center">
									<view class="right_split"></view>
									<view class="content">{{ ladder_pro_item.title }}</view>
								</view>
								<view class="goods_amount flex_row_between_center">
									<text class="left">{{ ladder_pro_item.goods_left }}</text>
									<text class="right">{{ ladder_pro_item.goods_right == '--' ? '' : $L('￥') }}{{ ladder_pro_item.goods_right }}</text>
								</view>
								<view class="need_pay_amount flex_row_between_center">
									<text class="left">{{ ladder_pro_item.need_pay_left }}</text>
									<text :class="{ right: true, cur: ladder_pro_item.is_cur == 1 ? true : false }">
										{{ ladder_pro_item.need_pay_right == '--' ? '' : $L('￥') }}{{ ladder_pro_item.need_pay_right }}
									</text>
								</view>
							</view>
						</template>
					</view>
					<!-- 阶梯团订单信息 end -->

					<!-- //订单价格信息 --普通商品 -->
					<view class="store_price_info" v-if="allData.orderState != 10 && allData.orderType != 105 && allData.orderType != 103">
						<view class="actual_payment">
							<view class="actual_payment_title">
								<text>{{ $L('商品总额') }}</text>
							</view>
							<view class="actual_payment_price">
								<text class="unit">¥</text>
								<text>{{ $getPartNumber(allData.totalMoney, 'int') }}</text>
								<text>{{ $getPartNumber(allData.totalMoney, 'decimal') }}</text>
							</view>
						</view>
						<view class="freight">
							<text class="freight_title">{{ $L('满优惠') }}</text>
							<text class="freight_price">-￥{{ allData.fullDiscountAmount }}</text>
						</view>
						<view class="freight">
							<text class="freight_title">{{ $L('运费') }}</text>
							<text class="freight_price">￥{{ allData.totalExpress }}</text>
						</view>
						<view class="freight" v-if="allData.platformVoucherAmount">
							<text class="freight_title">{{ $L('平台优惠券') }}</text>
							<text class="freight_price">-￥{{ allData.platformVoucherAmount }}</text>
						</view>
						<view class="freight" v-if="allData.storeVoucherAmount">
							<text class="freight_title">{{ $L('店铺优惠券') }}</text>
							<text class="freight_price">-￥{{ allData.storeVoucherAmount }}</text>
						</view>
						<view class="freight" v-if="allData.integralCashAmount">
							<text class="freight_title">{{ $L('积分抵扣') }}</text>
							<text class="freight_price">-￥{{ allData.integralCashAmount }}</text>
						</view>
						<view class="actual_payment">
							<view class="actual_payment_title">
								<text>{{ allData.orderState == 10 || (allData.orderState == 0 && allData.payState == 0) ? $L('需付款') : $L('实付款') }}</text>
								<text>{{ $L('(含运费)') }}</text>
							</view>
							<view class="actual_payment_price">
								<text class="unit">¥</text>
								<text>{{ $getPartNumber(allData.actualPayment, 'int') }}</text>
								<text>{{ $getPartNumber(allData.actualPayment, 'decimal') }}</text>
							</view>
						</view>
					</view>

					<!-- //订单价格信息 --预售 -->
					<view class="store_price_info" v-if="allData.orderState == 10 && allData.orderType == 103">
						<view class="freight">
							<text class="freight_title">{{ $L('满优惠') }}</text>
							<text class="freight_price">-￥{{ allData.fullDiscountAmount }}</text>
						</view>
						<view class="freight" v-if="allData.presellInfo.remainAmount != null">
							<text class="freight_title">{{ $L('运费') }}</text>
							<text class="freight_price">￥{{ allData.totalExpress }}</text>
						</view>
						<view class="freight" v-if="allData.platformVoucherAmount">
							<text class="freight_title">{{ $L('平台优惠券') }}</text>
							<text class="freight_price">-￥{{ allData.platformVoucherAmount }}</text>
						</view>
						<view class="freight" v-if="allData.storeVoucherAmount">
							<text class="freight_title">{{ $L('店铺优惠券') }}</text>
							<text class="freight_price">-￥{{ allData.storeVoucherAmount }}</text>
						</view>
						<view class="freight" v-if="allData.integralCashAmount">
							<text class="freight_title">{{ $L('积分抵扣') }}</text>
							<text class="freight_price">-￥{{ allData.integralCashAmount }}</text>
						</view>
						<view class="actual_payment">
							<view class="actual_payment_title">
								<text v-if="allData.orderSubState == 101">{{ $L('需付款') }}</text>
								<text v-else-if="allData.orderSubState != 101 && allData.presellInfo.remainAmount > 0">{{ $L('需付款') }}</text>
								<text v-else>{{ $L('实付款') }}</text>
								<!-- <text>{{$L('(含运费)')}}</text> -->
							</view>
							<view class="actual_payment_price">
								<text class="unit">¥</text>
								<text>{{ $getPartNumber(allData.actualPayment, 'int') }}</text>
								<text>{{ $getPartNumber(allData.actualPayment, 'decimal') }}</text>
							</view>
						</view>
					</view>

					<!-- //订单价格信息 --阶梯团 -->
					<view class="store_price_info" v-if="allData.orderState == 10 && allData.orderType == 105">
						<view class="freight" v-if="allData.fullDiscountAmount">
							<text class="freight_title">{{ $L('满优惠') }}</text>
							<text class="freight_price">-￥{{ allData.fullDiscountAmount }}</text>
						</view>
						<view class="freight" v-if="allData.ladderGroupDetailInfo.remainAmount != null">
							<text class="freight_title">{{ $L('运费') }}</text>
							<text class="freight_price">￥{{ allData.totalExpress }}</text>
						</view>
						<view class="freight" v-if="allData.platformVoucherAmount">
							<text class="freight_title">{{ $L('平台优惠券') }}</text>
							<text class="freight_price">-￥{{ allData.platformVoucherAmount }}</text>
						</view>
						<view class="freight" v-if="allData.storeVoucherAmount">
							<text class="freight_title">{{ $L('店铺优惠券') }}</text>
							<text class="freight_price">-￥{{ allData.storeVoucherAmount }}</text>
						</view>
						<view class="freight" v-if="allData.integralCashAmount">
							<text class="freight_title">{{ $L('积分抵扣') }}</text>
							<text class="freight_price">-￥{{ allData.integralCashAmount }}</text>
						</view>
						<view class="actual_payment">
							<view class="actual_payment_title">
								<text v-if="allData.orderSubState == 101">{{ $L('需付款') }}</text>
								<text v-else-if="allData.orderSubState != 101 && allData.ladderGroupDetailInfo.remainAmount > 0">{{ $L('需付款') }}</text>
								<text v-else>{{ $L('实付款') }}</text>
								<!-- <text>{{$L('(含运费)')}}</text> -->
							</view>
							<view class="actual_payment_price">
								<text class="unit">¥</text>
								<text>{{ $getPartNumber(allData.actualPayment, 'int') }}</text>
								<text>{{ $getPartNumber(allData.actualPayment, 'decimal') }}</text>
							</view>
						</view>
					</view>
					<!-- ss -->

					<!-- 订单信息  start-->
					<view class="order_des">
						<view class="order_des_title">{{ $L('订单信息') }}</view>
						<view class="order_des_pre">
							<text>
								<text class="invoice">{{ $L('发票') }}</text>
								：
							</text>
							<text v-if="allData && allData.invoice && allData.invoice.invoiceTitle">{{ allData.invoice.invoiceTitle }}</text>
							<text v-else>--</text>
						</view>
						<view class="order_des_pre" v-if="allData.orderTypeValue">
							<text>{{ $L('订单类型') }}：</text>
							<text>{{ allData.orderTypeValue }}{{ $L('订单') }}</text>
						</view>
						<view class="order_des_pre" v-if="(allData.isPickup == 1 || allData.isPickup == 2) && allData.appointTime">
							<text>{{ allData.isPickup == 1 ? $L('到店时间') : $L('配送时间') }}：</text>
							<text>{{ allData.appointTime }}</text>
						</view>
						<view class="order_des_pre">
							<text>{{ $L('订单编号') }}：</text>
							<text>{{ allData.orderSn }}</text>
						</view>
						<!-- <view class="order_des_pre" v-if="allData.tradeSn">
							<text>{{ $L('交易流水') }}：</text>
							<text>{{ allData.tradeSn }}</text>
						</view> -->
						<view class="order_des_pre">
							<text>{{ $L('订单备注') }}：</text>
							<text>{{ allData.orderRemark || '--' }}</text>
						</view>
						<block v-if="orderLogs && orderLogs.length > 0">
							<view class="order_des_pre" v-for="(item, index) in orderLogs" :key="index">
								<text>
									{{
										item.orderStateLog == 10
											? '创建时间：'
											: item.orderStateLog == 20
											? '付款时间：'
											: item.orderStateLog == 30
											? '发货时间：'
											: item.orderStateLog == 40
											? '完成时间：'
											: item.orderStateLog == 102
											? '定金支付时间：'
											: '取消时间：'
									}}
								</text>
								<text>{{ item.logTime }}</text>
							</view>
						</block>
						<!-- 订单二维码 -->
						<view v-if="allData.orderState == 30" class="order_qrcode flex_row_center_center">
							<canvas canvas-id="qrcode"></canvas>
						</view>
					</view>
					<!-- 订单信息  end-->
				</view>

				<!-- 推荐商品 start-->
				<!-- 	<view class="recomment">
					<recommendGoods ref='recomment_goods' />
				</view> -->
				<!-- 推荐商品 end-->
			</view>

			<!-- 取消订单选择原因弹框 -->
			<uni-popup ref="cancelPopup" type="bottom" @change="moveHandle">
				<view class="cancel_popup">
					<view class="popup_top">
						<text>{{ $L('取消原因') }}</text>
						<image :src="imgUrl + 'order-detail/guanbi.png'" mode="aspectFit" @click="notCancel"></image>
					</view>
					<scroll-view class="uni-list cancel_list" scroll-y="true">
						<radio-group @change="radioChange">
							<label class="cancle_pre" v-for="(item, index) in cancelList" :key="index">
								<text>{{ item.content }}</text>
								<radio :value="item.reasonId" :checked="item.reasonId == reasonId" color="var(--color_main)" style="transform: scale(0.8); margin-right: 0" />
							</label>
						</radio-group>
					</scroll-view>
					<view class="cancel_popup_btn">
						<text class="" @click="notCancel()">{{ $L('暂不取消') }}</text>
						<text class="" @click="confirmCancel()">{{ $L('确定取消') }}</text>
					</view>
				</view>
			</uni-popup>
			<!-- 预售，阶梯团定金取消订单提示 -->
			<uni-popup ref="popup" type="dialog">
				<uni-popup-dialog
					type="input"
					title="提示"
					content="取消该订单定金不予退还,确定取消?"
					:duration="2000"
					@close="acDialog(false)"
					@confirm="acDialog(true)"
				></uni-popup-dialog>
			</uni-popup>
		</scroll-view>
		<!-- 详情底部操作按钮 start-->
		<view class="order_det_bottom" v-if="bottomShow">
			<!-- 待付款 -->
			<block v-if="allData.orderState == 10">
				<!-- <view class="edit_address_btn" @click="editAddress" v-if="allData.isVirtualGoods == 1 && allData.isPickup == 0">{{ $L('修改地址') }}</view> -->
				<view class="cancel_order" @click="cancelPopup()">{{ $L('取消订单') }}</view>
				<template
					v-if="
						!(
							(allData.orderType == 105 && allData.orderSubState == 102 && allData.ladderGroupDetailInfo.depositRemainTime > 0) ||
							(allData.orderType == 103 && allData.orderSubState == 102 && allData.presellInfo.remainEndTime > 0)
						) ||
						(allData.presellInfo && allData.presellInfo.isStartRemainPay)
					"
				>
					<view v-show="ladderGroupDisplayStatus" class="go_pay" @click="goPay">{{ $L('去付款') }}</view>
				</template>
			</block>
			<!-- 待发货 -->
			<!-- <block v-if="allData.orderState == 20 && allData.isVirtualGoods == 1 && allData.isPickup == 0">
				<view class="edit_address_btn" @click="editAddress">{{ $L('修改地址') }}</view>
			</block> -->

			<!-- 部分发货 -->
			<!-- <block v-if="allData.orderState == 31 && allData.isPickup == 0">
				<view class="cancel_order" @click="lookLogistics()" v-if="allData.isVirtualGoods == 1">{{ $L('查看物流') }}</view>
			</block> -->

			<!-- 待收货 -->
			<!-- <block v-if="allData.orderState == 30 && allData.isPickup == 0">
				<view class="cancel_order" @click="lookLogistics()" v-if="allData.isVirtualGoods == 1 && !allData.isPickup">{{ $L('查看物流') }}</view>
				<view class="confirm_receipt" @click="confirmReceipt()" v-if="allData.lockState == 0 && !allData.isPickup">{{ $L('确认收货') }}</view>
				<view class="confirm_receipt" @click="confirmReceipt()" v-if="allData.lockState == 0 && allData.isPickup">{{ $L('确认提货') }}</view>
			</block> -->
			<!-- 待评价 -->
			<!-- <block v-if="allData.orderState == 40">
				<view class="cancel_order" @click="lookLogistics()" v-if="allData.isVirtualGoods == 1 && allData.isPickup == 0">{{ $L('查看物流') }}</view>
				<view class="go_pay" @click="goEvaluate()" v-if="allData.evaluateState != 3">{{ $L('评价') }}</view>
			</block> -->
			<!-- 订单取消 -->
			<block v-if="allData.orderState == 0 || allData.orderState == 50">
				<view class="edit_address_btn" @click="delOrder()">{{ $L('删除订单') }}</view>
			</block>
		</view>
		<!-- 详情底部操作按钮 end-->
	</view>
</template>
<script>
import { mapState, mapMutations } from 'vuex';
var QRCode = require('../utils/wxapp-qrcode.js');
import { openNavigator } from '@/utils/base.js';
import recommendGoods from '@/components/recommend-goods.vue';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue';
import goodsItemO from '@/components/goods_item_o.vue';
let startY = 0,
	moveY = 0,
	pageAtTop = true;
export default {
	components: {
		recommendGoods,
		uniPopup,
		uniPopupDialog,
		goodsItemO
	},
	data() {
		return {
			barcodeVal: null,
			position: {},
			imgUrl: getApp().globalData.imgUrl,
			coverTransform: 'translateY(0px)',
			coverTransition: '0s',
			moving: false,
			orderSn: '', //订单号
			parentSn: '',
			allData: {}, //订单详细信息
			orderProductList: [], //订单商品列表
			cancelList: [], //取消原因列表
			current: '0', //取消原因当前点击的是第0项
			reasonId: -1, //取消原因当前点击的原因id
			stateTime: '', //等待买家付款的剩余时间
			isShow: false,
			orderLogs: [], //订单日志
			secInterval: '', //定时器
			ladderInfo: [], //阶梯团信息
			presaleInfo: [], //定金预售信息
			state: '', //返跳转的时候用到这个参数
			commonBg: getApp().globalData.imgUrl + 'order/detail_bg.png',
			ladderGroupDisplayStatus: true, //立即支付按钮显示状态(此变量只在阶梯团商品下起作用)
			isVirListEmp: false,
			bottomShow: true, //底部按钮显示
			
			store:'',//订单店铺信息
			memberId: 0,
			logisticsIndex: 0, //批次发货-物流信息下标
			logisticsList: [], //物流节点信息
			deliverIds: '', //分批发货批次-deliverId
			remainProduct: [], //待发货物流
			logReason: '' //物流信息原因
		};
	},
	async onLoad(option) {
		setTimeout(() => {
			uni.setNavigationBarTitle({
				title: this.$L('订单详情')
			});
		}, 0);
		//订单号
		this.orderSn = this.$Route.query.orderSn;
		// 创建订单二维码
		let qrcode = new QRCode('qrcode', {
			width: 200,
			height: 200,
			colorDark: '#000000',
			colorLight: '#ffffff',
			correctLevel: QRCode.CorrectLevel.H
		});
		qrcode.makeCode(this.orderSn);
	},
	onShow(option) {
		this.getOrderDetail();
	},
	onUnload() {
		if (this.secInterval) {
			clearInterval(this.secInterval);
		}
	},

	computed: {
		...mapState(['hasLogin', 'userInfo', 'userCenterData'])
	},
	methods: {
		...mapMutations(['saveChatBaseInfo']),
		tonavigation() {
			// 跳转到地图导航
			let { allData } = this;
			if (allData.store.poiId) {
				openNavigator(allData.store.poiId);
			}
			// let { position } = this;
			// console.log(position);
			// uni.openLocation({
			// 	latitude: Number(position.latitude), //目标纬度
			// 	longitude: Number(position.longitude), //目标经度
			// 	name: position.name, //名称
			// 	address: position.address, //地址
			// 	success: function () {
			// 		console.log('success');
			// 	}
			// });
		},
		//去店铺
		goStore(storeid) {
			this.$Router.push({
				path: '/standard/store/shopHomePage',
				query: {
					vid: storeid
				}
			});
		},

		/**
		 * 统一跳转接口,拦截未登录路由
		 * navigator标签现在默认没有转场动画，所以用view
		 */
		navTo(url) {
			if (!this.hasLogin) {
				let urls = this.$Route.path;
				const query = this.$Route.query;
				uni.setStorageSync('fromurl', {
					url: urls,
					query
				});
				url = '/pages/public/login';
			}
			this.$Router.push(url);
		},

		//获取订单详情信息
		getOrderDetail() {
			uni.showLoading({
				title: '加载中'
			});
			let that = this;
			let param = {};
			param.url = 'v3/business/front/orderInfo/detail';
			param.method = 'POST';
			param.data = {};
			param.data.orderSn = that.orderSn;
			that.$request(param).then((res) => {
				if (res.state == 200) {
					uni.hideLoading();
					let result = res.data;
					that.orderProductList = result.childOrdersVOS;
					that.orderLogs = result.orderLogs.reverse();
					that.allData = result || {};
					this.store = result.store
					this.barcodeVal = result.pickupCode;
					// 导航位置信息
					const position = {
						longitude: result.store.lng,
						latitude: result.store.lat,
						name: result.store.areaInfo,
						address: result.store.address
					};
					this.position = position;

					that.isShow = true;
					that.parentSn = res.data.parentSn;
					if (result.orderType == 105) {
						//阶梯团信息
						let ladderInfo = result.ladderGroupDetailInfo; //阶梯团信息
						let tmpData = [];
						tmpData.push({
							key: 0,
							title: that.$L('阶段1：等待买家付款'),
							goods_left: that.$L('商品定金'),
							goods_right: ladderInfo.advanceDeposit,
							need_pay_left: that.$L('定金需付款'),
							need_pay_right: ladderInfo.needAdvanceDeposit,
							is_cur: 0
						});
						if (ladderInfo.orderSubState == 101) {
							tmpData[0].is_cur = 1;
							if (result.orderState != 0) {
								this.countDownBySecond(result.remainTime, result);
							}
						} else if (ladderInfo.orderSubState > 101) {
							tmpData[0].title = that.$L('阶段1：已完成');
							tmpData.push({
								key: 1,
								title: ladderInfo.orderSubState == 102 ? that.$L('阶段2：等待买家付款') : that.$L('阶段2：已完成'),
								goods_left: that.$L('商品尾款'),
								goods_right: ladderInfo.remainAmount,
								need_pay_left: that.$L('尾款需付款'),
								need_pay_right: ladderInfo.needRemainAmount,
								is_cur: ladderInfo.orderSubState == 102 ? 1 : 0
							});
							if (ladderInfo.orderSubState == 102 && ladderInfo.depositRemainTime > 0) {
								tmpData[1].title = that.$L('尾款生成中');
								tmpData[1].goods_right = '--';
								tmpData[1].need_pay_right = '--';
								this.countDownBySecond(ladderInfo.depositRemainTime + 60);
							} else {
								this.countDownBySecond(ladderInfo.remainEndTime);
							}
						}
						this.ladderInfo = tmpData;
					} else if (result.orderType == 103 && result.presellInfo.isAllPay == 0) {
						//全款预售（isAllPay==0）不显示下面的判断
						//定金预售信息
						let presaleInfo = result.presellInfo; //定金预售信息
						this.commonBg = this.imgUrl + 'order/preSale_bg.png';
						let tmpData = [];
						tmpData.push({
							key: 0,
							title: that.$L('阶段1：等待买家付款'),
							goods_left: that.$L('商品定金'),
							goods_right: presaleInfo.depositAmount,
							goods_expand_left: that.$L('定金膨胀'),
							goods_expand_right: presaleInfo.firstExpand,
							need_pay_left: that.$L('定金需付款'),
							need_pay_right: presaleInfo.needDepositAmount,
							is_cur: 0
						});
						if (presaleInfo.orderSubState == 101) {
							tmpData[0].is_cur = 1;
							if (result.orderState != 0) {
								this.countDownBySecond(result.remainTime);
							}
							tmpData.push({
								key: 1,
								title: that.$L('阶段2：未开始'),
								goods_left: that.$L('商品尾款'),
								goods_right: presaleInfo.remainAmount,
								need_pay_left: that.$L('尾款需付款'),
								need_pay_right: presaleInfo.needRemainAmount,
								is_cur: 0
							});
						} else if (presaleInfo.orderSubState > 101) {
							tmpData[0].title = that.$L('阶段1：已完成');
							let curTitle = '';
							if (presaleInfo.orderSubState == 102) {
								if (presaleInfo.isStartRemainPay) {
									this.countDownBySecond(presaleInfo.remainEndTime);
									curTitle = that.$L('阶段2：等待买家付款');
								} else {
									this.stateTime = ''; //清空倒计时
									curTitle = that.$L('阶段2：') + presaleInfo.remainStartTime + that.$L('开始付尾款');
								}
							} else {
								curTitle = that.$L('阶段2：已完成');
							}
							tmpData.push({
								key: 1,
								title: curTitle,
								goods_left: that.$L('商品尾款'),
								goods_right: presaleInfo.remainAmount,
								need_pay_left: that.$L('尾款需付款'),
								need_pay_right: presaleInfo.needRemainAmount,
								is_cur: presaleInfo.orderSubState == 102 ? 1 : 0
							});
						}
						this.presaleInfo = tmpData;
					} else if (result.orderType == 104 && result.orderState == 10) {
						if (result.remainTime > 0) {
							this.countDownBySecond(result.remainTime, result);
						} else if (!this.allData.refuseReason) {
							this.allData.orderState = 0;
							this.allData.orderStateValue = '交易关闭';
							this.allData.refuseReason = '支付超时系统自动取消秒杀订单';
						}
					} else {
						that.countup();
					}

					if (this.allData.orderReserveList.length) {
						this.isVirListEmp = this.allData.orderReserveList.some((i) => i.reserveValue != '');
					} else {
						this.isVirListEmp = false;
					}

					if (result.orderState >= 30) {
						this.getLogistics(result);
						if (result.orderDeliverList && result.orderDeliverList.length > 0) {
							let arr = [];
							result.orderDeliverList.forEach((item) => {
								arr.push(item.deliverId);
							});
							this.deliverIds = arr.join(',');
						}
					}
				} else if (res.state == 267) {
					this.$api.msg(res.msg);
					setTimeout(() => {
						this.$Router.back(1);
					}, 1500);
				} else {
					this.$api.msg(res.msg);
				}
			});
		},

		//计算时间差
		countup() {
			let that = this;
			let createTime = '';
			if (that.allData.orderType == 102) {
				createTime = that.allData.cancelTime;
			} else {
				createTime = that.allData.createTime;
			}
			let startStrs = createTime.split(' ');
			let createTimeStamp = that.strtotime(startStrs[0], startStrs[1]); //开始时间时间戳(毫秒)
			if (that.allData.orderState == 10) {
				//等待买家付款 ，24小时过期
				let endTimeStamp = 0;
				if (that.allData.orderType == 102) {
					//拼团订单获取倒计时
					endTimeStamp = createTimeStamp + that.allData.remainTime * 1000;
				} else {
					endTimeStamp = createTimeStamp + 86400000; //结束时间时间戳   86400000是1天的
				}
				that.countDown(endTimeStamp);
			} else if (that.allData.orderState == 30) {
				//等待买家收货
				let endTime = that.allData.autoReceiveTime; //结束时间时间戳
				let endStrs = endTime.split(' ');
				let endTimeStamp = that.strtotime(endStrs[0], endStrs[1]); //开始时间时间戳(毫秒)
				that.countDown(endTimeStamp);
			}
		},

		//倒计时(参数为：剩余秒数)
		countDownBySecond(second, goodsInfo) {
			let that = this;
			//创建定时器前先清除定时器
			clearInterval(that.secInterval);
			let diffrentTimeStamp = second * 1000;
			that.secInterval = setInterval(() => {
				if (diffrentTimeStamp == 0) {
					that.stateTime = '';
					clearInterval(that.secInterval);
					if (!((goodsInfo && goodsInfo.orderType === 105 && goodsInfo.orderSubState === 101) || goodsInfo.orderType === 102)) {
						//非 商品为阶梯团&&定金未付款 情况下才会重新请求商品详情
						that.getOrderDetail();
					} else {
						//阶梯团&&定金未付款商品 倒计时结束后主动隐藏支付按钮
						that.ladderGroupDisplayStatus = false;
					}
				} else if (diffrentTimeStamp > 0) {
					//将时间戳转换为天，时，分，秒 并倒计时
					that.stateTime = that.formatDuring(diffrentTimeStamp);
				} else {
					that.stateTime = '';
				}
				diffrentTimeStamp -= 1000; //相差时间 毫秒数
			}, 1000);
		},

		//倒计时
		countDown(endTimeStamp) {
			let that = this;
			//创建定时器前先清除定时器
			clearInterval(that.secInterval);
			that.secInterval = setInterval(() => {
				let currentTimestamp = new Date().getTime(); //当前时间时间戳 （毫秒数）
				let diffrentTimeStamp = endTimeStamp - currentTimestamp; //相差时间 毫秒数
				if (diffrentTimeStamp == 0) {
					that.stateTime = '';
					clearInterval(that.secInterval);
					that.getOrderDetail();
				} else if (diffrentTimeStamp > 0) {
					//将时间戳转换为天，时，分，秒 并倒计时
					that.stateTime = that.formatDuring(diffrentTimeStamp);
				} else {
					that.stateTime = '';
				}
			}, 1000);
		},
		//将标准格式（2014-08-02 11:23:12）转化为时间戳  函数   参数：time_str为（2014-08-02）   fix_time为（11:23:12）
		strtotime(time_str, fix_time) {
			let time = new Date().getTime();
			if (time_str) {
				let str = time_str.split('-');
				if (3 === str.length) {
					let year = str[0] - 0;
					let month = str[1] - 0 - 1;
					var day = str[2] - 0;
					if (fix_time) {
						let fix = fix_time.split(':');
						if (3 === fix.length) {
							let hour = fix[0] - 0;
							let minute = fix[1] - 0;
							time = new Date(year, month, day, hour, minute).getTime();
						}
					} else {
						time = new Date(year, month, day).getTime();
					}
				}
			}
			return time;
		},
		//将时间戳转换为时分秒
		formatDuring(mss) {
			let days = parseInt(mss / (1000 * 60 * 60 * 24));
			let hours = parseInt((mss % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
			let minutes = parseInt((mss % (1000 * 60 * 60)) / (1000 * 60));
			if (minutes < 10) {
				minutes = '0' + minutes;
			}
			let seconds = ((mss % (1000 * 60)) / 1000).toFixed(0);
			if (days > 0) {
				return days + ' 天 ' + hours + ' 小时 ' + minutes + ' 分钟 ';
			} else if (hours > 0) {
				return hours + ' 小时 ' + minutes + ' 分钟 ';
			} else if (minutes >= 1) {
				return minutes + ' 分钟 ';
			} else {
				//如果剩 1分钟之内就不让显示
				return seconds + '秒';
			}
		},

		//切换物流批次
		changeLogistics(index) {
			if (this.logisticsIndex != index) {
				this.logisticsIndex = index;
				this.getLogistics(this.allData);
			}
		},
		//显示待发货
		showRemain() {
			this.logisticsIndex = this.allData.orderDeliverList.length;
		},
		//获取物流信息
		getLogistics(detailInfo) {
			let that = this;
			let deliverId = '';
			if (detailInfo.orderDeliver) {
				deliverId = detailInfo.orderDeliver.deliverId;
			} else if (detailInfo.orderDeliverList && detailInfo.orderDeliverList.length > 0) {
				deliverId = detailInfo.orderDeliverList[this.logisticsIndex].deliverId;
			} else {
				return;
			}

			// 获取已发货数据
			let param = {};
			param.url = 'v3/business/front/logistics/order/getTrace';
			param.method = 'GET';
			param.data = {
				deliverId
			};
			this.$request(param).then((res) => {
				that.logisticsList = res.data ?? {};
				if (res.data && res.data.routeList) {
					that.logisticsList.routeList = res.data.routeList.map((item) => {
						if (item.acceptTime) {
							item.acceptTimeDate = item.acceptTime.split(' ')[0];
							item.acceptTimeWhen = item.acceptTime.split(' ')[1] || '';
						}
						return item;
					});
				}
				if (res.data && res.data.reason) {
					that.logReason = res.data.reason;
				} else if (res.state == 255 && res.msg) {
					that.logReason = res.msg;
				} else {
					that.logReason = '';
				}
			});

			// 获取待发货数据
			if (detailInfo.orderDeliverList && detailInfo.orderDeliverList.length > 0) {
				let params = {};
				params.url = 'v3/business/front/orderInfo/orderProductList';
				params.method = 'GET';
				params.data = {
					orderSn: detailInfo.orderSn
				};
				this.$request(params).then((res) => {
					that.remainProduct = res.data ? res.data : [];
				});
			}
		},

		//获取推荐商品
		getData() {
			// this.$refs.recomment_goods.getMoreData();
		},
		//拨打电话
		goCall(store) {
			if (!store.servicePhone) {
				uni.showToast({
					title: '该商家暂未设置电话',
					icon: 'none'
				});
				return;
			}
			uni.makePhoneCall({
				phoneNumber: store.servicePhone
			});
		},
		//联系商家，跳转客服页面
		contactShop(orderItem) {
			let chatBaseInfo = {};
			chatBaseInfo.memberId = this.userCenterData.memberId;
			chatBaseInfo.memberName = this.userCenterData.memberName;
			chatBaseInfo.memberNickName = this.userCenterData.memberNickName;
			chatBaseInfo.memberAvatar = this.userCenterData.memberAvatar;
			chatBaseInfo.storeId = orderItem.storeId;
			chatBaseInfo.storeLogo = orderItem.storeLogo;
			chatBaseInfo.storeName = orderItem.storeName;
			chatBaseInfo.source = 'order';
			let tempGoodsData = [
				{
					productId: orderItem.orderProductListVOList[0].orderProductId,
					goodsName: orderItem.orderProductListVOList[0].goodsName,
					goodsImage: orderItem.orderProductListVOList[0].productImage,
					goodsPrice: orderItem.orderProductListVOList[0].productShowPrice
				}
			];
			chatBaseInfo.showData = {
				orderSn: orderItem.orderSn,
				orderStateValue: this.allData.orderStateValue,
				createTime: this.allData.createTime,
				orderProductList: tempGoodsData
			};
			this.saveChatBaseInfo(chatBaseInfo);
			this.$Router.push({
				path: '/standard/chat/detail',
				query: {
					vid: orderItem.storeId
				}
			});
		},
		//打开取消订单弹框
		cancelPopup() {
			if (
				(this.allData.orderType == 105 && !this.allData.ladderGroupDetailInfo.isRefundDeposit) ||
				(this.allData.orderType == 103 && this.allData.presellInfo.isAllPay == 0)
			) {
				this.$refs.popup.open();
			} else {
				this.$refs.cancelPopup.open();
				this.getCancelList();
				this.bottomShow = false;
			}
		},

		//弹窗打开关闭事件
		moveHandle(e) {
			if (!e.show) {
				setTimeout(() => {
					this.bottomShow = true;
				}, 300);
			}
		},

		// 预售,阶梯团的提示确认
		acDialog(type) {
			if (type == true) {
				this.$refs.popup.close();
				this.$refs.cancelPopup.open();
				this.getCancelList();
				this.bottomShow = false;
			} else {
				this.$refs.popup.close();
				this.bottomShow = true;
			}
		},

		//获取取消订单原因列表
		getCancelList() {
			let param = {};
			param.url = 'v3/system/front/reason/list';
			param.method = 'GET';
			param.data = {};
			param.data.type = 104;
			this.$request(param)
				.then((res) => {
					if (res.state == 200) {
						this.cancelList = res.data || [];
						this.cancelList && this.cancelList.map((item, index) => (item.value = '' + index));
						this.reasonId = this.cancelList[0].reasonId;
					} else {
						this.$api.msg(res.msg);
					}
				})
				.catch((e) => {
					//异常处理
				});
		},
		//取消原因单选框切换
		radioChange(e) {
			this.reasonId = e.detail.value;
		},
		//暂不取消订单
		notCancel() {
			this.$refs.cancelPopup.close();
			// this.bottomShow = true;
			this.goRefresh();
		},
		//确定取消订单
		confirmCancel() {
			let that = this;
			uni.showModal({
				title: '提示',
				content: '确定取消该订单?',
				confirmColor: this.diyStyle_var['--color_main'],
				success: function (res) {
					if (res.confirm) {
						let param = {};
						param.url = 'v3/business/front/orderOperate/cancel';
						param.method = 'POST';
						param.data = {};
						param.data.parentSn = that.parentSn;
						param.data.reasonId = that.reasonId;
						that.$request(param)
							.then((res) => {
								if (res.state == 200) {
									that.$api.msg(res.msg);
									that.$refs.cancelPopup.close();
									that.bottomShow = true;
									that.goRefresh();
								} else {
									that.$api.msg(res.msg);
									that.bottomShow = true;
								}
							})
							.catch((e) => {
								//异常处理
							});
					} else if (res.cancel) {
						that.$refs.cancelPopup.close();
						that.bottomShow = true;
					}
				}
			});
		},
		//去付款
		goPay() {
			//阶梯团 付定金  付尾款、
			let goodsInfo = this.allData.childOrdersVOS[0].orderProductListVOList[0];
			if (this.allData.orderType == 105 && this.allData.orderSubState == 102 && this.allData.ladderGroupDetailInfo.depositRemainTime == 0) {
				this.$Router.push({
					path: '/order/confirmOrder',
					query: {
						goodsId: goodsInfo.goodsId,
						productId: goodsInfo.productId,
						numbers: goodsInfo.productNum,
						ifcart: 2,
						orderSn: this.allData.orderSn
					}
				});
			} else if (this.allData.orderType == 103 && this.allData.orderSubState == 102 && this.allData.presellInfo.remainEndTime > 0) {
				this.$Router.push({
					path: '/order/confirmOrder',
					query: {
						goodsId: goodsInfo.goodsId,
						productId: goodsInfo.productId,
						numbers: goodsInfo.productNum,
						ifcart: 2,
						orderSn: this.allData.orderSn
					}
				});
			} else {
				this.$Router.push({
					path: '/order/pay',
					query: {
						paySn: this.allData.paySn,
						payMethodType: 'orderDetail'
					}
				});
			}
		},
		//修改地址
		editAddress() {
			let { receiverAddress, receiverAreaInfo, receiverMobile } = this.allData;
			this.$Router.push({
				path: '/newPages/address/list',
				query: {
					source: 3,
					orderSn: this.orderSn,
					sourceOrderAddress: encodeURIComponent(`${receiverAddress},${receiverMobile},${receiverAreaInfo}`)
				}
			});
		},
		//查看退款详情  换货详情
		lookRefundDetail(afsSn, afsButton) {
			if (afsButton == 403 || afsButton == 404) {
				//可查看换货详情
				let sourceType = 'exchange';
				this.$Router.push({
					path: '/standard/refund/refundDetail',
					query: {
						afsSn,
						sourceType
					}
				});
			} else {
				let sourceType = '';
				this.$Router.push({
					path: '/standard/refund/refundDetail',
					query: {
						afsSn,
						sourceType,
						orderState: this.allData.orderState
					}
				});
			}
		},
		//选择服务
		selectService(orderSn, orderProductId, afsButton) {
			let that = this;
			let param = {};
			param.url = 'v3/business/front/after/sale/apply/applyInfo';
			param.method = 'GET';
			param.data = {};
			param.data.orderSn = orderSn; //订单号
			param.data.orderProductId = orderProductId; //订单明细id
			const [orderProductListVOList] = this.orderProductList;
			this.$request(param).then((res) => {
				if (res.state == 200) {
					if (that.allData.orderState == 20) {
						//待发货直接进入申请退款页面
						this.$Router.push({
							path: '/standard/refund/applyRefund',
							query: {
								orderSn,
								orderProductId,
								isPickup:that.allData.isPickup,
								sourceType: 'orderDetail'
							}
						});
					} else if (that.allData.orderState == 30 || that.allData.orderState == 40) {
						this.$Router.push({
							path: '/standard/refund/selectService',
							query: {
								orderSn,
								isPickup:that.allData.isPickup,
								orderProductId,
								sourceType: 'orderDetail'
							}
						});
					} else if (that.allData.orderState == 31) {
						if (afsButton == 100) {
							this.$Router.push({
								path: '/standard/refund/applyRefund',
								query: {
									orderSn,
									orderProductId,
									isPickup:that.allData.isPickup,
									sourceType: 'orderDetail'
								}
							});
						} else {
							this.$Router.push({
								path: '/standard/refund/selectService',
								query: {
									orderSn,
									orderProductId,
									isPickup:that.allData.isPickup,
									sourceType: 'orderDetail'
								}
							});
						}
					}
				} else {
					this.$api.msg(res.msg);
				}
			});
		},
		//查看物流
		lookLogistics() {
			let query = {};
			let deliverId = '';
			if (this.allData.orderDeliver) {
				query.deliverId = this.allData.orderDeliver.deliverId;
			} else if (this.allData.orderDeliverList && this.allData.orderDeliverList.length) {
				if (this.logisticsIndex < this.allData.orderDeliverList.length) {
					query.deliverId = this.allData.orderDeliverList[this.logisticsIndex].deliverId;
				}
			}
			query.orderSn = this.orderSn;
			this.$Router.push({
				path: '/order/lookLogistics',
				query
			});
		},
		//更新当前页面方法
		goRefresh() {
			let pages = getCurrentPages();
			let currPage = pages[pages.length - 1]; //当前页面
			let beforePage = pages[pages.length - 2]; //上一页
			currPage.$vm.getOrderDetail(); //更新当前页面数据
			beforePage.$vm.loadData(); //更新上一页数据
		},
		//删除订单
		delOrder() {
			let that = this;
			uni.showModal({
				title: '提示',
				content: '确定删除该订单?',
				confirmColor: this.diyStyle_var['--color_main'],
				success: function (res) {
					if (res.confirm) {
						let param = {};
						param.url = 'v3/business/front/orderOperate/delete';
						param.method = 'POST';
						param.data = {};
						param.data.orderSn = that.orderSn;
						that.$request(param)
							.then((res) => {
								if (res.state == 200) {
									that.$api.msg(res.msg);
									that.goRefresh();
									that.$Router.back(1);
								} else {
									that.$api.msg(res.msg);
								}
							})
							.catch((e) => {
								//异常处理
							});
					} else if (res.cancel) {
					}
				}
			});
		},
		//确认收货
		confirmReceipt() {
			let that = this;
			uni.showModal({
				title: '提示',
				content: '确认收货？',
				confirmColor: this.diyStyle_var['--color_main'],
				success: function (res) {
					if (res.confirm) {
						let param = {};
						param.url = 'v3/business/front/orderOperate/receive';
						param.method = 'POST';
						param.data = {};
						param.data.orderSn = that.orderSn;
						that.$request(param)
							.then((res) => {
								if (res.state == 200) {
									that.$api.msg(res.msg);
									that.goRefresh();
									setTimeout((_) => {
										that.$Router.push({
											path: '/order/tradeSuccess',
											query: {
												orderSn: that.orderSn,
												sourceType: 'orderDetail'
											}
										});
									}, 1000);
								} else {
									that.$api.msg(res.msg);
								}
							})
							.catch((e) => {
								//异常处理
							});
					}
				}
			});
		},
		//去商品详情页
		goProductDetail(productId, goodsId) {
			this.$Router.push({
				path: '/standard/product/detail',
				query: {
					productId,
					goodsId
				}
			});
		},
		//去评价页面
		goEvaluate(orderSn) {
			this.$Router.push({
				path: '/order/publishEvaluation',
				query: {
					orderSn: this.orderSn
				}
			});
		}
	}
};
</script>
<style lang="scss">
page {
	background: #f2f2f2;
	width: 750rpx;
	margin: 0 auto;
}

.contact_phone {
	.item_con {
		flex: 1;

		&.border {
			border-right: 1px solid #eaeaea;
		}

		padding: 30rpx 0;

		image {
			width: 30rpx;
			height: 30rpx;
			margin-right: 20rpx;
		}

		text {
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #333333;
			line-height: 24rpx;
		}
	}
}

.order_mark {
	display: flex;
	padding: 5px 11px;
	box-sizing: border-box;
	justify-content: space-between;
	align-items: flex-start;
	font-size: 28rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: #343434;
	line-height: 32rpx;

	.order_mark_text {
		max-width: 450rpx;
		word-break: break-all;
		color: #999;
		text-align: right;
	}
}
.order_qrcode {
	padding: 20rpx 0;
	canvas{
		width: 200px;
		height: 200px;
	}
}
.container {
	display: flex;
	flex: 1;
	width: 100%;
	min-height: 100vh;
	position: relative;
	background-color: #ffffff;

	.main_content {
		width: 100%;
		min-height: calc(var(--status-bar-height) + 452rpx);
		padding-top: 20rpx;
		/* app-1-start */
		/* #ifdef APP */
		padding-top: calc(var(--status-bar-height) + 20rpx);
		/* #endif */
		/* app-1-end */
		/* #ifdef H5 */
		padding-top: 20rpx;
		/* #endif */
		background-repeat: no-repeat;
		background-size: 750rpx 452rpx;
		padding-bottom: 90rpx;
		background-color: #f2f2f2;

		.main_content_box {
			padding: 0 20rpx;
		}

		.ladder_group {
			/* border-top: 10px solid #F5F5F5; */
			margin-top: 20rpx;
			/* width: 750rpx; */
			background: #fff;
			border-radius: 24rpx;

			.item {
				margin-left: 20rpx;
				width: 690rpx;
				box-sizing: border-box;
				padding-right: 20rpx;

				&.split {
					border-bottom: 1rpx solid #f2f2f2;
				}

				.title {
					margin-top: 22rpx;

					.right_split {
						width: 5rpx;
						height: 26rpx;
						background: var(--color_main_bg);
						border-radius: 3rpx;
						margin-right: 18rpx;
					}

					.content {
						color: var(--color_price);
						font-size: 28rpx;
					}
				}

				.goods_amount {
					margin-top: 20rpx;
					color: #666666;
					font-size: 26rpx;
					line-height: 30rpx;
				}

				.need_pay_amount {
					margin-top: 20rpx;
					color: #2d2d2d;
					font-size: 26rpx;
					line-height: 30rpx;
					margin-bottom: 25rpx;

					.cur {
						color: var(--color_price);
					}
				}
			}
		}

		.order_state {
			/* #ifndef H5 */
			padding-top: 0;
			/* #endif */
			/* #ifdef H5 */
			/* padding-top: 96rpx; */
			/* #endif */
			box-sizing: border-box;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			width: 100%;
			/* app-2-start */
			/* #ifdef APP-PLUS */
			/* height: 216rpx; */
			/* #endif */
			/* app-2-end */
			/* #ifndef APP-PLUS */
			/* height: 296rpx; */
			/* #endif */
			margin-bottom: 19rpx;
			padding-left: 20rpx;
			padding-right: 20rpx;

			.state_btn {
				width: 195rpx;
				height: 66rpx;
				background: var(--color_main_bg);
				border-radius: 33rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #ffffff;
				cursor: pointer;
			}

			.state_title {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 100%;

				image {
					width: 45rpx;
					height: 45rpx;
					margin-right: 10rpx;
				}

				.text_1 {
					color: var(--color_main);
				}

				text {
					font-size: 38rpx;
					font-family: PingFang SC;
					font-weight: bold;
					line-height: 32rpx;
				}
			}

			.state_reason {
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #333;
				line-height: 32rpx;
				margin: 20rpx 0;
			}

			.state_remark {
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #333;
				line-height: 32rpx;
			}

			.await {
				/* margin-bottom: 139rpx; */
			}

			.state_time_box {
				width: 100%;
				height: 85rpx;
				text-align: center;
			}

			.state_time {
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #121212;
				margin: 22rpx 0 32rpx;
				line-height: 32rpx;

				.unit {
					font-size: 24rpx;
				}
			}
		}

		.logistics_information {
			background: #f8f8f8;
			margin: 0 auto;
			padding: 40rpx 0 30rpx 20rpx;
			border-radius: 10rpx;
			border-bottom: 1rpx solid #f4f4f4;
			word-break: break-all;
			line-height: 40rpx;
			/* margin-bottom: 20rpx; */

			.logistics_image {
				width: 34rpx;
				height: 28rpx;
				margin-right: 18rpx;
			}

			.logistics_time {
				font-size: 26rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #949494;
				line-height: 45rpx;
			}

			.logistics_des {
				font-size: 26rpx;
				font-family: PingFang SC;
				font-weight: 400;
				color: #333333;
				line-height: 39rpx;
				text-overflow: -o-ellipsis-lastline;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				line-clamp: 2;
				-webkit-box-orient: vertical;
				margin-top: 14rpx;
			}

			.logistics_des_right {
				display: flex;
				align-items: center;

				.right_down {
					width: 46rpx;
					height: 46rpx;
				}
			}
		}

		.logistics_information_type1 {
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;

			&.mutiple {
				background: #f8f8f8;
				padding: 20rpx;
				margin-bottom: 20rpx;
			}

			.deli_name {
				color: #666666;
			}

			.deli_value {
				color: #121212;
			}
		}

		.buyer_info {
			/* width: 710rpx; */
			background: #ffffff;
			/* box-shadow: 1rpx 3rpx 30rpx 0rpx rgba(102, 102, 102, 0.1); */
			border-radius: 20rpx;
			margin: 0 auto;
			display: flex;
			padding: 41rpx 20rpx 30rpx;
			box-sizing: border-box;

			.buyer_map {
				width: 34rpx;
				height: 32rpx;
				margin-right: 13rpx;
			}

			.info_det {
				display: flex;
				width: 100%;
				justify-content: space-between;

				.info_detail {
					display: flex;
					flex-direction: column;

					.info_name {
						display: flex;
						align-items: center;
						font-size: 28rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #2e2e2e;
						line-height: 28rpx;
						font-weight: bold;

						.buyer_namer {
							font-size: 32rpx;
							color: #2e2e2e;
						}

						.buyer_phone {
							color: #2e2e2e;
							margin-left: 20rpx;
						}
					}

					.info_address {
						/* width: 560rpx; */
						font-size: 28rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #666;
						line-height: 39rpx;
						margin-top: 24rpx;
						word-break: break-all;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 2;
						overflow: hidden;
					}
				}
			}
		}

		.virtual_msg {
			width: 710rpx;
			background: #ffffff;
			box-shadow: 1rpx 3rpx 30rpx 0rpx rgba(102, 102, 102, 0.1);
			border-radius: 8px;
			margin: 0 auto;
			padding: 20px;

			.virtual_msg_item {
				font-size: 26rpx;
				line-height: 50rpx;
				color: #333;

				.msg_item_l {
					white-space: nowrap;
				}

				.msg_item_r {
					margin-left: 10rpx;
					word-break: break-all;
				}
			}
		}

		.order_goods {
			.goods_list {
				padding: 20rpx 0 0 0;

				.store_item:not(:first-child) {
					border-top: 20rpx solid #f5f5f5;
				}

				.store_item {
					background: #fff;
					border-radius: 20rpx;
					/* padding: 20rpx; */
				}

				.goods_pre {
					display: flex;
					margin: 0 20rpx;
					box-sizing: border-box;
					padding: 20rpx 0;
					border-bottom: 1rpx solid #f2f2f2;

					.goods_image {
						width: 200rpx;
						height: 200rpx;
						background: #f3f3f3;
						border-radius: 14px;

						image {
							width: 200rpx;
							height: 200rpx;
							border-radius: 14rpx;
						}
					}

					.vop_state {
						position: absolute;
						bottom: 20rpx;
						right: 0px;
						font-size: 24rpx;
						color: #fc1c1c;
					}

					.goods-img {
						background-size: cover;
						background-position: center center;
						background-repeat: no-repeat;
						width: 174rpx;
						height: 174rpx;
						overflow: hidden;
						background-color: #f8f6f7;
						border-radius: 14rpx;
						flex-shrink: 0;
					}

					.goods_pre_right {
						display: flex;
						justify-content: space-between;
						width: 585rpx;

						/* width: 100%; */
						.goods_des {
							margin-left: 25rpx;
							padding-top: 8rpx;
							box-sizing: border-box;
							flex: 1;
							display: flex;
							flex-direction: column;
							justify-content: space-between;
							align-items: flex-start;

							.goods_name {
								font-size: 28rpx;
								font-family: PingFang SC;
								font-weight: 500;
								color: #343434;
								line-height: 39rpx;
								text-overflow: -o-ellipsis-lastline;
								overflow: hidden;
								text-overflow: ellipsis;
								overflow: hidden;
								white-space: nowrap;
								text-overflow: ellipsis;
								width: 450rpx;
							}

							.goods_content {
								display: flex;
								align-items: center;
								justify-content: flex-start;
								margin-top: 20rpx;
							}

							.goods_spec {
								font-size: 24rpx;
								font-family: PingFang SC;
								font-weight: 400;
								color: #949494;
								line-height: 30rpx;
								/* width: 280rpx; */
								overflow: hidden;
								text-overflow: ellipsis;
							}
						}

						.goods_num_give {
							color: #2d2d2d;
							font-size: 26rpx;
							/* font-weight: bold; */
						}

						.goods_prices {
							display: flex;
							width: 100%;
							/* flex-direction: column; */
							justify-content: space-between;
							align-items: center;

							.goods_price {
								white-space: nowrap;

								text {
									display: inline-block;
									font-family: PingFang SC;
									font-weight: 500;
									color: #343434;
									line-height: 30rpx;
								}

								.unit {
									font-size: 24rpx;
								}

								.price_int {
									font-size: 32rpx;
								}

								.price_decimal {
									font-size: 24rpx;
								}
							}

							.goods_num {
								font-size: 24rpx;
								font-family: PingFang SC;
								font-weight: 500;
								color: #2d2d2d;
								line-height: 30rpx;
							}

							.refund_btn {
								/* padding: 12rpx 15rpx; */
								width: 140rpx;
								height: 56rpx;
								box-sizing: border-box;
								border: 1rpx solid #aaa;
								border-radius: 28rpx;
								font-size: 26rpx;
								font-family: PingFang SC;
								font-weight: 400;
								color: #333333;
								white-space: nowrap;
								display: flex;
								align-items: center;
								justify-content: center;
							}

							.refund_btn_o {
								/* border: 1rpx solid var(--color_main);
									color: var(--color_main); */
							}
						}
					}
				}
			}
		}

		.order_des {
			/* border-top: 20rpx solid #F5F5F5; */
			margin-top: 20rpx;
			/* padding: 29rpx 40rpx 0 20rpx; */
			padding: 35rpx 20rpx;
			box-sizing: border-box;
			background: #fff;
			border-radius: 20rpx;
			margin-bottom: 20rpx;

			&.look_des {
				padding-top: 37rpx;
			}

			.order_des_title {
				font-size: 30rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #343434;
				line-height: 24rpx;
				margin-bottom: 35rpx;

				&.look_des_title {
					margin-bottom: 28rpx;
				}
			}

			.order_send {
				overflow-x: auto;
				padding-bottom: 25rpx;

				/* border-bottom: 1rpx solid #F2F2F2; */
				.order_send_item {
					color: #111111;
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
					margin-right: 72rpx;
					white-space: nowrap;

					&.active {
						color: #fc1c1c;
						font-weight: bold;
					}
				}
			}

			.noLogistics {
				height: 100rpx;
				background: #f8f8f8;
				border-radius: 10rpx;
				font-size: 26rpx;
				color: #999999;
				font-family: PingFang SC;
				font-weight: 500;
				display: flex;
				align-items: center;
				padding-left: 30rpx;
			}

			.logistics_information {
				padding: 20rpx 20rpx 30rpx;
			}

			.order_des_pre {
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #9a9a9a;
				line-height: 28rpx;
				display: flex;
				margin-bottom: 19rpx;
				line-height: 39rpx;
				display: flex;
				justify-content: space-between;

				text:nth-child(1) {
					color: #9a9a9a;
				}

				text:nth-child(2) {
					width: 488rpx;
					color: #343434;
					word-break: break-all;
				}

				&:last-child {
					margin-bottom: 0;
				}

				.invoice {
					height: 13px;
					width: 52px;
					display: inline-block;
					text-align: justify;
					vertical-align: top;
				}

				.invoice::after {
					content: '';
					display: inline-block;
					width: 100%;
					overflow: hidden;
					height: 0;
				}

				.order_des_pre_tit {
					flex-shrink: 0;
				}

				.order_voucher {
					width: 254rpx;
					height: 254rpx;
					margin-right: 234rpx;
				}
			}
		}

		.recomment {
			background: #f5f5f5;
			box-sizing: border-box;
		}
	}
}

.cancel_popup {
	width: 100%;
	height: 700rpx;
	background: #ffffff;
	border-radius: 15rpx 15rpx 0 0;
	position: fixed;
	width: 100% !important;
	z-index: 20;
	bottom: 0;

	.popup_top {
		height: 100rpx;
		width: 100%;
		display: flex;
		padding: 0 39rpx;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1rpx solid #f8f8f8;

		text {
			font-size: 32rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #343434;
			line-height: 32rpx;
		}

		image {
			width: 30rpx;
			height: 30rpx;
		}
	}

	.cancel_list {
		padding-bottom: 128rpx;
		box-sizing: border-box;
		height: 600rpx;

		.cancle_pre {
			width: 100%;
			padding: 29rpx 40rpx;
			box-sizing: border-box;
			display: flex;
			justify-content: space-between;

			text {
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #666666;
				line-height: 32rpx;
			}
		}
	}

	.cancel_popup_btn {
		position: fixed;
		bottom: 40rpx;
		z-index: 30;
		display: flex;
		width: 100%;
		justify-content: center;

		text:nth-child(1) {
			width: 334rpx;
			height: 70rpx;
			background: var(--color_vice_bg);
			border-radius: 35rpx 0 0 35rpx;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ffffff;
			line-height: 32rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		text:nth-child(2) {
			width: 334rpx;
			height: 70rpx;
			background: var(--color_main_bg);
			border-radius: 0 35rpx 35rpx 0;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ffffff;
			line-height: 32rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
}

.store_name {
	padding-left: 20rpx;
	padding-bottom: 30rpx;
	padding-top: 30rpx;
	display: flex;
	align-items: center;

	image {
		width: 34rpx;
		height: 32rpx;
	}

	.store_name_text {
		font-size: 32rpx;
		color: #2d2d2d;
		font-weight: bold;
		margin-left: 10rpx;
	}

	.iconfont {
		// width: 13rpx;
		// height: 22rpx;
		font-size: 24rpx;
		margin-left: 10rpx;
	}
}

.store_price_info {
	padding-top: 5rpx;
	margin-top: 20rpx;
	border-radius: 20rpx;
	padding: 20rpx 0;
	background: #fff;

	.store_price_all {
		margin: 18rpx 0;
	}

	.freight {
		display: flex;
		padding: 10rpx 20rpx;
		box-sizing: border-box;
		justify-content: space-between;

		.freight_title {
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #343434;
			line-height: 30rpx;
		}

		.freight_price {
			font-size: 24rpx;
			font-family: PingFang SC;
			font-weight: bold;
			color: #2e2e2e;
			line-height: 30rpx;
		}
	}

	.actual_payment {
		display: flex;
		justify-content: space-between;
		padding: 0 20rpx;
		box-sizing: border-box;
		margin: 18rpx 0;

		.actual_payment_title {
			display: flex;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			line-height: 30rpx;
			color: #343434;

			text:nth-child(2) {
				color: #949494;
			}
		}

		.actual_payment_price {
			font-size: 24rpx;
			font-family: PingFang SC;
			font-weight: bold;
			color: var(--color_price);
			line-height: 30rpx;

			text:nth-child(2) {
				font-size: 32rpx;
			}
		}
	}
}

.no_top {
	border-top: none;
	margin-top: 20rpx;
}

.Giveaway {
	width: 100rpx;
	height: 40rpx;
	border: 1rpx solid red;
	line-height: 40rpx;
	text-align: center;
	color: red;
	font-size: 25rpx;
	border-radius: 10rpx;
}

.order_det_bottom {
	position: fixed;
	bottom: 0;
	width: 100%;
	padding-bottom: env(safe-area-inset-bottom);
	height: calc(env(safe-area-inset-bottom) + 90rpx);
	background: #ffffff;
	box-shadow: 1rpx 1rpx 20rpx 0rpx rgba(86, 86, 86, 0.11);
	display: flex;
	justify-content: flex-end;
	align-items: center;
	padding-right: 20rpx;
	box-sizing: border-box;
	z-index: 99;
	.edit_address_btn {
		width: 160rpx;
		height: 66rpx;
		border: 1rpx solid #aaa;
		border-radius: 33rpx;
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: 400;
		color: #343434;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.cancel_order {
		width: 160rpx;
		height: 66rpx;
		border: 1rpx solid #aaa;
		border-radius: 33rpx;
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: 400;
		color: #343434;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 10rpx;
	}
	.confirm_receipt {
		width: 160rpx;
		height: 66rpx;
		border-radius: 33rpx;
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: 400;
		color: #fff;
		background-color: var(--color_main);
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 10rpx;
	}
	.go_pay {
		width: 160rpx;
		height: 66rpx;
		background: var(--color_main);
		box-shadow: 1rpx 3rpx 15rpx 0rpx var(--color_halo);
		border-radius: 33rpx;
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: 400;
		color: #ffffff;
		display: flex;
		align-items: center;
		justify-content: center;
	}
}
.pick_order_state {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 40rpx 0 56rpx;
	image {
		width: 56rpx;
		height: 56rpx;
	}
	text {
		font-size: 40rpx;
		font-weight: bold;
		color: #4f4f4f;
		margin-left: 14rpx;
	}
}
.pick_detail {
	background-color: #fff;
	padding: 22rpx 56rpx 32rpx 28rpx;
	border-radius: 10rpx;
	color: #101010;
	.pick_detail_top {
		.top_left {
			.pick_title {
				font-size: 32rpx;
				font-weight: bold;
			}
			.pick_title_detail {
				max-width: 540rpx;
				font-size: 28rpx;
				margin-top: 20rpx;
				word-break: break-all;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				overflow: hidden;
				line-height: 1.5;
			}
		}
		.top_right {
			image {
				width: 40rpx;
				height: 40rpx;
			}
			.dao_text {
				font-size: 24rpx;
				color: #fc1c1c;
				margin-top: 10rpx;
			}
		}
	}
	.pick_detail_next {
		font-size: 24rpx;
		margin-top: 26rpx;
	}
}
.pick_code {
	background-color: #fff;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-bottom: 20rpx;
	padding: 40rpx 0;
	border-radius: 10rpx;
	.pick_code_cont {
		/* width: 492rpx; */
		/* height: 200rpx; */
	}
}
</style>
