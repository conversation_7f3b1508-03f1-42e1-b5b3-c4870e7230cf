<template>
	<view :class="isCookie == true ? 'container3 prevent' : 'container3' ">
		<!-- 专题页标题 -->
		<!-- #ifdef MP -->
		<view class="fixed_top_mp" :style="{height:'calc(160rpx + '+menuButtonTop+')'}"></view>
		<!-- #endif -->
		<view class="fixed_top">
			<!-- #ifdef MP -->
			<view class="fixed_bar" :style="{height:menuButtonTop,background:tab_index == 0?pure_bg_color:top_bg}" v-if="is_from_found == false"></view>
			<!-- #endif -->
			  <!-- #ifndef MP -->
			  <view class="fixed_bar" :style="{height:stateBarHeight+'px',background:tab_index == 0?pure_bg_color:top_bg}" v-if="is_from_found == false"></view>
			  <!-- #endif -->
			<!-- #ifdef MP -->
			<view class="topic_top_bar" @click="toAddressPage" :style="{background:tab_index == 0?pure_bg_color:top_bg}" v-if="is_from_found == false&&is_from_founds==true">
			<view class="topic_top_bar_bix" :style="{height:menuButtonHeight}">
				<image :src="imgUrl + 'business/locate.png'"></image>
				<text class="topic_name">{{cur_city}}</text>
			</view>
			</view>
			<!-- #endif -->
			<!-- #ifndef MP -->
			<view class="topic_top_bar" @click="toAddressPage" :style="{background:tab_index == 0?pure_bg_color:top_bg}" v-if="is_from_found == false&&is_from_founds==true">
			<view class="topic_top_bar_bix">
				<image :src="imgUrl + 'business/locate.png'"></image>
				<text class="topic_name">{{cur_city}}</text>
			</view>
			</view>
			<!-- #endif -->

			<!-- #ifdef MP -->
			<uni-nav-bar
				v-if="transpar==true"
				fixed="true"
				:color="navBarColor.color"
				status-bar="true"
				:left-icon="nav_left_icon"
				:leftText="topic_name"
				@clickLeft="toBack"
				mode="left"
				leftFontSize="16px"
				:backgroundColor="navBarColor.backgroundColor"
			></uni-nav-bar>
			<!-- #endif -->

			<!-- 小程序头部兼容 -->
			<view class="mp-search-box_fixed" v-if="is_show_top == true"></view>
			<view class="mp-search-box" v-if="is_show_top == true"
				:style="{background:tab_index == 0?pure_bg_color:top_bg}">
				<!-- <image :src="imgUrl+'index/scan.png'" mode="aspectFit" class="scan_img" @click="scanCode('goods')"></image> -->
				<image :src="imgUrl+'search.png'" mode="aspectFit" class="search_img"></image>
				<view class="ser-input" @click.stop="toSearchPage">输入关键字搜索</view>
				<!-- app-1-start -->
				<!-- #ifdef APP-PLUS -->
				<text class="scan_login iconfont iconsaoyisao1" @click="scanCode('login')"></text>
				<!-- #endif -->
				<!-- app-1-end -->
				<view class="msg_img">
					<image :src="imgUrl+'message_index.png'" mode="aspectFit" @click="toMsg"></image>
					<!-- #ifndef MP -->
					<view class="message_new" v-if="userCenterData&&userCenterData.msgNum&&userCenterData.msgNum>=1">
						{{userCenterData.msgNum > 99 ? '99+' : userCenterData.msgNum}}
					</view>
					<!-- #endif -->
					<!-- #ifdef MP -->
					<view class="message_new message_new_o" v-if="userCenterData&&userCenterData.msgNum&&userCenterData.msgNum>=1">
						{{userCenterData.msgNum > 99 ? '99+' : userCenterData.msgNum}}
					</view>
					<!-- #endif -->
				</view>
			</view>

			<!-- 头部分类 -->
			<tab-menu :backGround="tab_index == 0?pure_bg_color:top_bg" :tabInfo="sort_nav_list" ref="tabmenu"
				@getChildList="getChildList" v-if="is_show_top == true && home_is_show_top_cat"></tab-menu>
		</view>
		<!-- 首页装修 -->
		<!-- #ifndef MP -->
		<view v-if="is_show_index == true && isShow == true"
			:class="{deco_wrap:isShowTab&&home_is_show_top_cat,deco_wrap2:!isShowTab,deco_wrap_no_top_cat:isShowTab&&type=='home'&&!home_is_show_top_cat}"
			:style="{marginTop:!is_show_top&&!transpar?'110rpx':!transpar&&!store_id?'calc(230rpx + '+stateBarHeight+'px)':'auto'}">
		<!-- #endif -->
		<!-- #ifdef MP -->
		<view v-if="is_show_index == true && isShow == true"
			:class="{deco_wrap:isShowTab&&home_is_show_top_cat,deco_wrap2:!isShowTab,deco_wrap_no_top_cat:isShowTab&&type=='home'&&!home_is_show_top_cat}"
			:style="{marginTop:!is_show_top&&!transpar?'110rpx':!transpar&&!store_id&&(isShowTab&&home_is_show_top_cat)?'calc(210rpx + '+menuButtonTop+')':!transpar&&!store_id&&(isShowTab&&type=='home'&&!home_is_show_top_cat)?'calc(160rpx + '+menuButtonTop+')':'auto'}">
		<!-- #endif -->
			<view class="index_deco" v-for="(decoItem,decoIndex) in deco_info" :key="decoIndex">
				<view class="carousel-section" v-if="decoItem.type == 'top_cat_nav' && decoItem.data.length>0">
					<!-- 背景色区域 -->
					<view
						:class="decoItem.swiper_bg_style == 1?'titleNview-background top_swiper_style1':'titleNview-background top_swiper_style2'"
						:style="{background:titleNViewBackground,borderRadius:decoItem.swiper_bg_style == 1?'border_radius':'0'}">
					</view>
					<uni-swiper-dot :current="swiperCurrent" :info="decoItem.data" mode="dot" :dotsStyles="dotsStyles">
						<swiper class="carousel" circular @change="swiperChange" autoplay="true"
							:style="{margin:isIos?'10rpx':0,width:'710rpx',height:(decoItem.height/decoItem.width*710)+'rpx'}">
							<swiper-item v-for="(item, index) in decoItem.data" :key="index" class="carousel-item">
								<!-- <image :src="item.img" mode="aspectFit" class="itemImg"/> -->
								<view class="itemImg" :style="'background-image:url('+item.img+')'"
									@click="skipTo(item.url_type,item.url,item.info.productId || item.info.defaultProductId,item.info.pid,item.info.seckillId,item.info)">
								</view>
							</swiper-item>
						</swiper>
					</uni-swiper-dot>
				</view>
				<!-- 公告 -->
				<view class="notice_box" v-if="decoItem.type == 'gonggao' && decoItem.is_show == true">
					<!-- 公告样式一 -->
					<view class="notice_wrap1" v-if="decoItem.type == 'gonggao' && decoItem.show_style == 'one'"
						@click="skipTo(decoItem.url_type,decoItem.url,decoItem.info.productId || decoItem.info.defaultProductId,decoItem.info.pid,decoItem.info.seckillId,decoItem.info)">
						<image :src="noticeImg1" mode="aspectFit" class="notice_img1"></image>
						<view class="notice_content_wrap">
							<uni-notice-bar :scrollable="true" :single="true" :text="decoItem.text" color="#666"
								backClass="backColorClass1" />
						</view>
						<view class="notice_wrap1_line"></view>
						<view class="notice_more">>></view>
					</view>
					<!-- 公告样式二 -->
					<view class="notice_wrap2" v-if="decoItem.type == 'gonggao' && decoItem.show_style == 'two'"
						@click="skipTo(decoItem.url_type,decoItem.url,decoItem.info.productId || decoItem.info.defaultProductId,decoItem.info.pid,decoItem.info.seckillId,decoItem.info)">
						<image :src="noticeImg2" mode="aspectFit" class="notice_img2"></image>
						<view class="notice_content_wrap2">
							<uni-notice-bar :scrollable="true" :single="true" :text="decoItem.text" color="#fff"
								backClass="backColorClass2" />
						</view>
						<view class="notice_wrap2_line"></view>
						<view class="notice_more">>></view>
					</view>
				</view>

				<view class="nav_wrap" v-if="decoItem.type == 'nav' && decoItem.is_show == true">
					<!-- 导航样式一、二（图标在上/不显示图标） -->
					<view class="cate-section"
						v-if="decoItem.style_set == 'nav' && decoItem.icon_set == 'up' || decoItem.icon_set == 'no-icon' && decoItem.is_show == true">
						<view class="cate-item" v-for="(item,index) in decoItem.data" :key="index"
							@click="skipTo(item.url_type,item.url,item.info.productId || item.info.defaultProductId,item.info.pid,item.info.seckillId,item.info)">
							<image :src="item.img" v-if="decoItem.icon_set == 'up'"
								:style="'width:'+decoItem.slide*2+'rpx;height:'+decoItem.slide*2+'rpx'"></image>
							<text>{{item.name.substring(0,9)}}</text>
						</view>
					</view>

					<!-- 导航样式三 （图标文字左右显示）-->
					<view class="cate-section"
						v-if="decoItem.style_set == 'nav' && decoItem.icon_set == 'left' && decoItem.is_show == true"
						style="justify-content:space-around;padding:10rpx;">
						<view class="cate-item2" v-for="(item,index) in decoItem.data" :key="index"
							@click="skipTo(item.url_type,item.url,item.info.productId || item.info.defaultProductId,item.info.pid,item.info.seckillId,item.info)">
							<image :src="item.img" style="margin-right:10rpx;" mode="aspectFit"
								:style="'width:'+(decoItem.slide>54?54:decoItem.slide)*2+'rpx;height:'+(decoItem.slide>54?54:decoItem.slide)*2+'rpx'">
							</image>
							<view class="cate_name">{{item.name.substring(0,9)}}</view>
						</view>
					</view>

					<!-- 导航分组 -->
					<view class="nav_group" v-if="decoItem.style_set == 'tag-nav' && decoItem.is_show == true ">
						<view class="nav_group_item" v-for="(item,index) in decoItem.data" :key="index"
							@click="skipTo(item.url_type,item.url,item.info.productId || item.info.defaultProductId,item.info.pid,item.info.seckillId,item.info)">
							<image :src="item.img" mode="aspectFit"
								:style="'width:'+decoItem.slide*2+'rpx;height:'+decoItem.slide*2+'rpx'"></image>
							<view class="nav_group_name">{{item.name}}</view>
						</view>
					</view>
				</view>
				
				<!-- 门店系统 -->
				<!-- <view class="service_wrap" v-if="decoItem.type == 'mendian' && decoItem.is_show == true"> -->
				<view class="store-list" v-if="decoItem.type == 'dianpuliebiao' && decoItem.is_show == true">
					<view class="search-box">
						<view class="search-item" @click="showSelect1 = !showSelect1">
							<span>{{ searchStoreVal.distance }}</span>
							<image :src="imgUrl + 'business/arrow.png'" mode=""></image>
						</view>
						<view class="search-item" @click="showSelect2 = !showSelect2">
							<span>{{ searchStoreVal.sort }}</span>
							<image :src="imgUrl + 'business/arrow.png'" mode=""></image>
						</view>
						<view class="search-con1" v-show="showSelect1">
							<view class="search-con-item" @click="handleSearch(1,item)" v-for="item in distanceList">
								{{ item.label }}
							</view>
						</view>
						<view class="search-con2" v-show="showSelect2">
							<view class="search-con-item" @click="handleSearch(2,item)" v-for="item in sortList">
								{{ item.label }}
							</view>
						</view>
					</view>
					<view class="store-box" v-for="(item, index) in nearStoreList" :key="index">
						<view class="store-box-con">
							<image class="sbc-item1" @click="goShopHome(item.storeId)" :src="item.storeLogoUrl" mode=""></image>
							<view class="sbc-item2">
								<view class="sbc-item2-tit" @click="goShopHome(item.storeId)">
									{{ item.storeName }}
								</view>
								<view class="sbc-item2-star">
									<uni-icons color="#FF7E28" v-for="(star1,index1) in Number(item.deliverScore)" :key="index1" type="star-filled" size="20"></uni-icons>
									<template v-if="item.deliverScore < 5">
										<uni-icons v-for="(star2,index2) in 5 - item.deliverScore" :key="index2" type="star-filled" size="20"></uni-icons>
									</template>
									{{ item.deliverScore }}
								</view>
							</view>
							<view class="sbc-item3">
								<view class="sbc-item3-km">
									{{ item.distance }}
								</view>
								<view v-if="item.isHasAbleCoupon" class="sbc-item3-ticket">
									优惠券
								</view>
							</view>
						</view>
						<view class="store-box-goods">
							<view class="" style="flex: 1;display: flex;">
								<view class="store-box-gitem" @click="goGoodsDetail(item)" v-for="item in item.goodsListVOList.slice(0,3)">
									<image :src="item.goodsImage" mode=""></image>
									<view>
										{{ item.goodsName }}
									</view>
								</view>
							</view>
							<view v-if="item.goodsListVOList.length > 3" class="store-box-dotted flex_row_end_center">
								<image :src="imgUrl + 'business/dotted.png'" mode=""></image>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 客服 -->
				<view class="service_wrap" v-if="decoItem.type == 'kefu' && decoItem.is_show == true"
					@click="callUp(decoItem.tel)">
					<image :src="telImg" mode="aspectFit"></image>
					<text>{{decoItem.text}}{{decoItem.tel}}</text>
				</view>

				<!-- 富文本 -->
				<view class="rich_text_wrap" v-if="decoItem.type == 'fuwenben' && decoItem.is_show == true">
					<!-- <rich-text :nodes="decoItem.text" class="rich_text"></rich-text> -->
					<jyfParser :isAll="true" :html="decoItem.text"></jyfParser>
				</view>

				<!-- 图片组合 -->
				<view class="combination_wrap" v-if="decoItem.type == 'tupianzuhe'&& decoItem.is_show == true">
					<!-- 图片组合0123 -->
					<view v-if="decoItem.type == 'tupianzuhe' && decoItem.sele_style<4" style="background-color: #fff;">
						<view class="modules-slide">
							<view :class="'image-list style' + decoItem.sele_style" v-if="decoItem.sele_style<3">
								<view
									:class="decoItem.sele_style == 2?'combination_style no_margin_right flex_row_start_start tupianzuhe2':'space_between combination_style'"
									:style="{'display':decoItem.sele_style < 2?'block':'flex'}">
									<view v-for="(childitem, childindex) in decoItem.data" :key="childindex"
										:class="decoItem.sele_style < 2?'combine1':'combine2'"
										:style="{'marginTop':decoItem.sele_style == 1?'20rpx':'0','marginRight':decoItem.sele_style < 3?'0':'20rpx','marginBottom':decoItem.sele_style < 3?'0':'20rpx'}">
										<view class="flex_column_start_center"
											v-if="decoItem.sele_style == 0||decoItem.sele_style == 1">
											<image v-if="decoItem.sele_style == 0 "
												@click="skipTo(childitem.url_type,childitem.url,childitem.info.productId || childitem.info.defaultProductId,childitem.info.pid,childitem.info.seckillId,childitem.info)"
												mode="aspectFit" :src="childitem.img"
												:style="{'display': 'block','width':'750rpx','height':(750*childitem.height/childitem.width)+'rpx'}">
											</image>
											<image v-if="decoItem.sele_style == 1"
												@click="skipTo(childitem.url_type,childitem.url,childitem.info.productId || childitem.info.defaultProductId,childitem.info.pid,childitem.info.seckillId,childitem.info)"
												mode="aspectFit" :src="childitem.img"
												:style="{'display': 'block','width':'710rpx','height':(710*childitem.height/childitem.width)+'rpx','margin-bottom':childindex == (decoItem.data.length-1)?'20rpx':0}">
											</image>
										</view>

										<view class="flex_row_center_center combine3" v-if="decoItem.sele_style == 2"
											:style="{'height':childindex%2==0?((345*childitem.height/childitem.width)+'rpx'):((345*decoItem.data[childindex-1].height/decoItem.data[childindex-1].width)+'rpx'),'margin-left':'20rpx'}">
											<image
												@click="skipTo(childitem.url_type,childitem.url,childitem.info.productId || childitem.info.defaultProductId,childitem.info.pid,childitem.info.seckillId,childitem.info)"
												mode="aspectFit" :src="childitem.img" style="width: 100%;height: 100%;">
											</image>
										</view>
									</view>
								</view>
							</view>

							<view :class="'image-list style' + decoItem.sele_style" v-if="decoItem.sele_style == 3">
								<view class="combination_style no_margin_right2"
									style="display: flex;margin-bottom: 20rpx;flex-wrap:wrap">
									<view v-for="(childitem, childindex) in decoItem.data" :key="childindex"
										class="combine2">
										<view class="combine4"
											:style="{'height':childindex%3==0?((690/3*childitem.height/childitem.width)+'rpx'):(childindex%3==1?((690/3*decoItem.data[childindex-1].height/decoItem.data[childindex-1].width)+'rpx'):((690/3*decoItem.data[childindex-2].height/decoItem.data[childindex-2].width)+'rpx')),}">
											<image
												@click="skipTo(childitem.url_type,childitem.url,childitem.info.productId || childitem.info.defaultProductId,childitem.info.pid,childitem.info.seckillId,childitem.info)"
												mode="aspectFit" :src="childitem.img"></image>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!--图片组合4  -->
					<view v-if="decoItem.type == 'tupianzuhe' && decoItem.sele_style == 4"
						style="background-color: #fff;">
						<view class="modules-slide">
							<view class="image-ad clearfix images-tpl">
								<view style="display: flex;">
									<view class="tupianzuhe04_left flex_row_center_center"
										@click="skipTo(decoItem.data[0].url_type,decoItem.data[0].url,decoItem.data[0].info.productId || decoItem.data[0].info.defaultProductId,decoItem.data[0].info.pid,decoItem.data[0].info.seckillId,decoItem.data[0].info)">
										<image mode="aspectFit" :src="decoItem.data[0].img"></image>
									</view>
									<view style="display: flex;flex-direction: column;justify-content: space-between;">
										<view class="tupianzuhe04_right_item flex_row_center_center"
											@click="skipTo(decoItem.data[1].url_type,decoItem.data[1].url,decoItem.data[1].info.productId || decoItem.data[1].info.defaultProductId,decoItem.data[1].info.pid,decoItem.data[1].info.seckillId,decoItem.data[1].info)">
											<image mode="aspectFit" :src="decoItem.data[1].img"></image>
										</view>
										<view class="tupianzuhe04_right_item flex_row_center_center"
											@click="skipTo(decoItem.data[2].url_type,decoItem.data[2].url,decoItem.data[2].info.productId || decoItem.data[2].info.defaultProductId,decoItem.data[2].info.pid,decoItem.data[2].info.seckillId,decoItem.data[2].info)">
											<image mode="aspectFit" :src="decoItem.data[2].img"></image>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 图片组合5 6 display: flex;flex-wrap:wrap;-->
					<view v-if="decoItem.type == 'tupianzuhe'&& decoItem.sele_style == 5 || decoItem.sele_style == 6"
						style="background-color: #fff;">
						<view class="modules-slide">
							<view class="image-ad clearfix images-tpl" style="padding-top: 0;">
								<view class="combine5_wrap" style="display:flex;flex-wrap:wrap;width:100%;"
									v-if="decoItem.sele_style == 5">
									<view v-for="(childitem, childindex) in decoItem.data" :key="childindex"
										class="combine5 flex_row_center_center"
										@click="skipTo(childitem.url_type,childitem.url,childitem.info.productId || childitem.info.defaultProductId,childitem.info.pid,childitem.info.seckillId,childitem.info)"
										:style="{width: childindex == 0 || childindex == 3 ? 230 +'rpx' : 460 + 'rpx',
											height: '230rpx',marginTop:'20rpx',marginLeft:'20rpx',backGround:'red'}">
										<image mode="aspectFit" :src="childitem.img" style="width: 100%;height: 100%;">
										</image>
									</view>
								</view>

								<view class="" v-if="decoItem.sele_style == 6" style="display:flex;">
									<view class="combine6" style="margin-left: 20rpx;">
										<view class="flex_row_center_center"
											:style="{width:'345rpx',height:345/2+'rpx','flex-shrink':0,'margin-bottom':'20rpx'}"
											@click="skipTo(decoItem.data[0].url_type,decoItem.data[0].url,decoItem.data[0].info.productId || decoItem.data[0].info.defaultProductId,decoItem.data[0].info.pid,decoItem.data[0].info.seckillId,decoItem.data[0].info)">
											<image :src="decoItem.data[0].img" mode="aspectFit"
												style="width: 100%;height: 100%;"></image>
										</view>
										<view class="flex_row_center_center"
											:style="{width:  '345rpx',height:'345rpx','flex-shrink':0}"
											@click="skipTo(decoItem.data[1].url_type,decoItem.data[1].url,decoItem.data[1].info.productId || decoItem.data[1].info.defaultProductId,decoItem.data[1].info.pid,decoItem.data[1].info.seckillId,decoItem.data[1].info)">
											<image :src="decoItem.data[1].img" mode="aspectFit"
												style="width: 100%;height: 100%;"></image>
										</view>
									</view>
									<view class="combine6">
										<view class="flex_row_center_center"
											:style="{width:  '345rpx',height:'345rpx','flex-shrink':0,'margin-bottom':'20rpx'}"
											@click="skipTo(decoItem.data[2].url_type,decoItem.data[2].url,decoItem.data[2].info.productId || decoItem.data[2].info.defaultProductId,decoItem.data[2].info.pid,decoItem.data[2].info.seckillId,decoItem.data[2].info)">
											<image :src="decoItem.data[2].img" mode="aspectFit"
												style="width: 100%;height: 100%;"></image>
										</view>
										<view class="flex_row_center_center"
											:style="{width:  '345rpx',height:345/2+'rpx','flex-shrink':0}"
											@click="skipTo(decoItem.data[3].url_type,decoItem.data[3].url,decoItem.data[3].info.productId || decoItem.data[3].info.defaultProductId,decoItem.data[3].info.pid,decoItem.data[3].info.seckillId,decoItem.data[3].info)">
											<image :src="decoItem.data[3].img" mode="aspectFit"
												style="width: 100%;height: 100%;"></image>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 图片组合7-->
					<view v-if="decoItem.sele_style == 7" data-index="index" style="background-color: #fff;">
						<view class="modules-slide">
							<view class="image-ad images-tpl"
								style="display: flex;justify-content: flex-start;align-items: center;padding-top: 0;">
								<view :style="{'display': 'flex','flex-wrap':'wrap','width':(670/3*2+61)+'rpx',}">
									<view class="flex_row_center_center"
										v-for="(childitem, childindex) in decoItem.data" v-if="childindex<4"
										:key="childindex"
										@click="skipTo(childitem.url_type,childitem.url,childitem.info.productId || childitem.info.defaultProductId,childitem.info.pid,childitem.info.seckillId,childitem.info)"
										:style="{'margin-left':'20rpx',width:670/3+'rpx',height:670/3+'rpx',marginTop:'20rpx','flex-shrink':0}">
										<image mode="aspectFit" :src="childitem.img" style="width: 100%;height: 100%;">
										</image>
									</view>
								</view>
								<view class="flex_row_center_center" v-if="decoItem.data[4]"
									@click="skipTo(decoItem.data[4].url_type,decoItem.data[4].url,decoItem.data[4].info.productId || decoItem.data[4].info.defaultProductId,decoItem.data[4].info.pid,decoItem.data[4].info.seckillId,decoItem.data[4].info)"
									:style="{'margin-top':'20rpx',width:670/3+'rpx',height: (670/3*2+20) +'rpx'}">
									<image mode="aspectFit" :src="decoItem.data[4].img"
										style="width: 100%;height: 100%;"></image>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 搭配 -->
				<view class="match_wrap" v-if="decoItem.type == 'dapei' && decoItem.is_show == true">
					<view class="match_top">
						<view class="match_top_title" v-if="decoItem.dapei_title">{{decoItem.dapei_title}}</view>
						<view class="match_image_wrap  flex_row_center_center">
							<view mode="aspectFit" class="match_image"
								:style="{width:'750rpx',height:710*decoItem.height/decoItem.width+'rpx',backgroundImage:'url('+decoItem.dapei_img+')'}">
							</view>
						</view>
						<view class="match_top_text" v-if="decoItem.dapei_desc">{{decoItem.dapei_desc}}</view>
					</view>
					<view class="match_main_wrap">
						<view class="match_main" v-if="decoItem && decoItem.data && decoItem.data.info.length">
							<view class="match_item" v-for="(item,index) in decoItem.data.info" :key="index"
								@click="toGoodsDetail(item.productId || item.defaultProductId,item.goodsId)">
								<view class="match_goods_img">
									<view class="image" :style="{backgroundImage:'url('+item.mainImage+')'}"></view>
									<!-- <image :src="item.mainImage" mode="aspectFit"></image> -->
								</view>
								<view class="match_goods_name">{{item.goodsName}}</view>
								<view class="match_goods_price">
									<text class="small_price">￥</text>
									<text class="big_price">{{$getPartNumber(item.goodsPrice,'int')}}</text>
									<text class="small_price">{{$getPartNumber(item.goodsPrice,'decimal')}}</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 辅助线 -->
				<view class="subline_wrap" v-if="decoItem.type == 'fzx' && decoItem.is_show == true"
					:style="{paddingLeft:decoItem.lrmargin*2+'rpx',paddingRight:decoItem.lrmargin*2+'rpx',marginTop:decoItem.tbmargin*2+'rpx',marginBottom:decoItem.tbmargin*2+'rpx'}">
					<view class="subline"
						:style="{height:decoItem.tbmargin+'px',borderBottomColor:decoItem.color,borderBottomStyle:decoItem.val}">
					</view>
					<view :style="{height:decoItem.tbmargin+'px'}"></view>
				</view>

				<!-- 轮播图 -->
				<view class="carousel_bottom_wrap" v-if="decoItem.type == 'lunbo' && decoItem.is_show == true"
					style="padding:0;" :style="decoIndex==0?'margin-top:0;':''">
					<uni-swiper-dot :current="swiperCurrent2" :info="decoItem.data" mode="dot" :dotsStyles="dotsStyles">
						<swiper class="carousel carousel_bottom" circular autoplay="true" @change="swiperChangeIndex"
							:style="{width:'710rpx',height:(decoItem.height/decoItem.width*710)+'rpx'}">
							<swiper-item v-for="(item, index) in decoItem.data" :key="index" class="carousel-item"
								style="padding:0;"
								@click="skipTo(item.url_type,item.url,item.info.productId || item.info.defaultProductId,item.info.pid,item.info.seckillId,item.info)">
								<!-- <image :src="item.img" class="carousel_img" mode="aspectFit" /> -->
								<view class="carousel_img" :style="'background-image:url('+item.img+')'"></view>
							</swiper-item>
						</swiper>
					</uni-swiper-dot>
				</view>

				<!-- 推荐商品样式一 -->
				<view class="recommend_goods_wrap"
					v-if="decoItem.type == 'tuijianshangpin' && decoItem.show_style == 'small'" style="padding:0">
					<view
						:style="{backgroundColor:decoItem.border_style == 'border_none_grey_bg'?'f8f8f8':'#fff',paddingLeft:decoItem.page_margin*2+'rpx',paddingRight:decoItem.page_margin*2+'rpx',paddingTop:'20rpx'}"
						class="rec_goods_wrap">
						<goods-item-v :goods_info="item" :show_sale="decoItem.isshow_sales == 1?true:false"
							:icon_type="decoItem.cart_icon_type" :height="225" :border_radius="decoItem.border_radius"
							:border_style="decoItem.border_style" :goods_margin="decoItem.goods_margin"
							:page_margin="decoItem.page_margin" v-for="(item,index) in decoItem.data.info" :key="index"
							@click="skipTo(decoItem.type,item.gid,item.info)" @reloadCartList="cartUpdate"
							@addCart="showSpcPop"></goods-item-v>
							
					</view>
				</view>
				<!-- 推荐商品样式二 -->
				<view class="recommend_goods_wrap"
					v-if="decoItem.type == 'tuijianshangpin' && decoItem.show_style == 'list' && decoItem.is_show == true"
					:style="{paddingLeft:decoItem.page_margin+'px',paddingRight:decoItem.page_margin+'px',marginTop:0,backgroundColor:decoItem.border_style == 'border_none_grey_bg'?'f8f8f8':'#fff'}">
					<view class="rec_goods_wrap">
						<view class="recommend_goods1" v-for="(item,index) in decoItem.data.info" :key="index"
							:style="{borderRadius:decoItem.border_radius+'px',border:decoItem.border_style == 'border_eee'?'1rpx solid #eee':'',boxShadow:decoItem.border_style == 'card-shadow'?'rgba(93, 113, 127, 0.08) 0px 2px 8px':'',marginBottom:decoItem.goods_margin+'px'}">
							<view class="recommend_goods_img1"
								@click="toGoodsDetail(item.productId || item.defaultProductId,item.goodsId)">
								<view class="image" :style="{backgroundImage:`url(${item.mainImage})`,borderRadius:border_radius2}" >
									
								</view>
							</view>
							<view class="recommend_goods_right">
								<view class="recommend_goods_name"
									@click="toGoodsDetail(item.productId || item.defaultProductId,item.goodsId)">
									{{item.goodsName}}
								</view>
								<view :class="decoItem.isshow_sales == 1?'':'hide_sold_wrap'">
									<view class="recommend_goods_price"
										:style="{position:decoItem.isshow_sales == 1?'absolute':'static'}">
										<text class="small_price">￥</text>
										<text class="big_price">{{$getPartNumber(item.goodsPrice,'int')}}</text>
										<text class="small_price">{{$getPartNumber(item.goodsPrice,'decimal')}}</text>
									</view>
									<view class="recommend_goods_bottom"
										:style="{position:decoItem.isshow_sales == 1?'absolute':'static',width:decoItem.isshow_sales == 1?'100%':'auto'}">
										<view class="have_sold" v-if="decoItem.isshow_sales == 1">
											已售{{item.actualSales}}件</view>
										<block>
											<!-- <image :src="imgUrl+'add-cart.png'" mode="aspectFit"
												v-if="decoItem.cart_icon_type == 1"
												@click="addCart(item.productId || item.defaultProductId,item.goodsId,item)">
											</image>
											<image :src="icon2" mode="aspectFit" v-if="decoItem.cart_icon_type == 2"
												@click="addCart(item.productId || item.defaultProductId,item.goodsId,item)">
											</image>
											<image :src="icon3" mode="aspectFit" v-if="decoItem.cart_icon_type == 3"
												@click="addCart(item.productId || item.defaultProductId,item.goodsId,item)">
											</image>
											<image :src="icon4" mode="aspectFit" v-if="decoItem.cart_icon_type == 4"
												@click="addCart(item.productId || item.defaultProductId,item.goodsId,item)">
											</image> -->
											<image :src="imgUrl+'add-cart.png'" mode="aspectFit"
												v-if="decoItem.cart_icon_type == 1"
												@click="showSpcPop(item)">
											</image>
											<image :src="icon2" mode="aspectFit" v-if="decoItem.cart_icon_type == 2"
												@click="showSpcPop(item)">
											</image>
											<image :src="icon3" mode="aspectFit" v-if="decoItem.cart_icon_type == 3"
												@click="showSpcPop(item)">
											</image>
											<image :src="icon4" mode="aspectFit" v-if="decoItem.cart_icon_type == 4"
												@click="showSpcPop(item)">
											</image>
											
										</block>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				<!-- 推荐商品样式三 -->
				<view class="recommend_goods_wrap"
					v-if="decoItem.type == 'tuijianshangpin' && decoItem.show_style == 'big' && decoItem.is_show == true"
					:style="{paddingLeft:decoItem.page_margin+'px',paddingRight:decoItem.page_margin+'px',marginTop:0,backgroundColor:decoItem.border_style == 'border_none_grey_bg'?'f8f8f8':'#fff'}">
					<view class="recommend_goods2" v-for="(item,index) in decoItem.data.info" :key="index"
						:style="{borderRadius:decoItem.border_radius+'px',border:decoItem.border_style == 'border_eee'?'1rpx solid #eee':'',boxShadow:decoItem.border_style == 'card-shadow'?'rgba(93, 113, 127, 0.08) 0px 2px 8px':'',marginBottom:decoItem.goods_margin+'px'}">
						<view class="recommend_goods_img2"
							@click="toGoodsDetail(item.productId || item.defaultProductId,item.goodsId)">
							<image :src="item.mainImage" mode="aspectFit" :style="{borderRadius:border_radius1}">
							</image>
						</view>
						<view class="recommend_goods_bottom2" :style="{borderRadius:border_radius3}">
							<view class="recommend_goods_name2"
								@click="toGoodsDetail(item.productId || item.defaultProductId,item.goodsId)">
								{{item.goodsName}}
							</view>
							<view class="goods_bottom"
								:style="{flexDirection:decoItem.isshow_sales == 1?'':'row',justifyContent:decoItem.isshow_sales == 1?'':'space-between'}">
								<view class="recommend_goods_price" style="color:var(--color_main);">
									<text class="small_price">￥</text>
									<text class="big_price">{{$getPartNumber(item.goodsPrice,'int')}}</text>
									<text class="small_price">{{$getPartNumber(item.goodsPrice,'decimal')}}</text>
								</view>
								<view class="recommond_goods3_wrap">
									<view class="have_sold" v-if="decoItem.isshow_sales == 1">已售{{item.actualSales}}件
									</view>
									<!-- <image :src="imgUrl+'add-cart.png'" mode="aspectFit"
										v-if="decoItem.cart_icon_type == 1"
										@click="addCart(item.productId || item.defaultProductId,item.goodsId,item)">
									</image>
									<image :src="icon2" mode="aspectFit" v-if="decoItem.cart_icon_type == 2"
										@click="addCart(item.productId || item.defaultProductId,item.goodsId,item)">
									</image>
									<image :src="icon3" mode="aspectFit" v-if="decoItem.cart_icon_type == 3"
										@click="addCart(item.productId || item.defaultProductId,item.goodsId,item)">
									</image>
									<image :src="icon4" mode="aspectFit" v-if="decoItem.cart_icon_type == 4"
										@click="addCart(item.productId || item.defaultProductId,item.goodsId,item)">
									</image> -->
									<image :src="imgUrl+'add-cart.png'" mode="aspectFit"
										v-if="decoItem.cart_icon_type == 1"
										@click="showSpcPop(item)">
									</image>
									<image :src="icon2" mode="aspectFit" v-if="decoItem.cart_icon_type == 2"
										@click="showSpcPop(item)">
									</image>
									<image :src="icon3" mode="aspectFit" v-if="decoItem.cart_icon_type == 3"
										@click="showSpcPop(item)">
									</image>
									<image :src="icon4" mode="aspectFit" v-if="decoItem.cart_icon_type == 4"
										@click="showSpcPop(item)">
									</image>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 辅助空白 -->
				<view class="blank_wrap" v-if="decoItem.type == 'fzkb' && decoItem.id && decoItem.is_show == true"
					:style="{backgroundColor:decoItem.color,height:decoItem.text+'px'}"></view>
				<!-- tab切换 -->
				<sort-list :nav_list="decoItem.data" :isShowSale="false" :borderRadius="decoItem.border_radius"
					v-if="decoItem.type == 'more_tab' && decoItem.is_show == true" :card_height="225"
					@updateCart="cartUpdate"></sort-list>

				<!-- 直播 start -->
				<!-- 方案一 -->
				<view class="svideo" v-if="decoItem.type == 'live' && decoItem.is_show == true">
					<view class="svideo_title">
						<view class="svideo_title_l">{{decoItem.title}}</view>
						<view class="svideo_title_r" @click="skipTo('live_center','','')">更多直播 ></view>
					</view>
					<view class="svideo_main" v-if="decoItem.show_style == 'one' && decoItem.is_show == true">
						<view class="svideo_main_block svideo_main_block_l" v-for="(item,index) in decoItem.data.info"
							:key="index" :style="{borderRadius:decoItem.border_radius+'px'}"
							@click="skipTo(decoItem.type,item.liveId,item.info,decoIndex,index)">
							<image class="live_list_b_img_hua live_list_b_img_hua1"
								:src="imgUrl+'svideo/zhibo-dianzan.gif'"></image>
							<view class="svideo_main_block_lw">
								<view class="svideo_main_block_lt">
									<image class="svideo_block_t_img svideo_block_t_img2zb"
										:src="imgUrl+'svideo/zx_zhibo_gif.gif'"></image>
									<text class="svideo_block_t_text svideo_block_t_text2">{{item.viewingNum}}人观看</text>
								</view>
							</view>
							<!-- wx-1-start -->
							<!-- #ifdef MP-WEIXIN -->
							<image class="video_bg1" :src="item.liveCover"></image>
							<!-- #endif -->
							<!-- wx-1-end -->
							<!-- #ifndef MP-WEIXIN -->
							<view class="video_bg1" :style="'background-image:url('+item.liveCover+')'"></view>
							<!-- #endif -->
							<view class="svideo_main_block_zb">{{item.liveName}}</view>
						</view>
					</view>
					<!-- 方案2 -->
					<view class="svideo_main2" v-if="decoItem.show_style == 'two' && decoItem.is_show == true">
						<scroll-view class="svideo2_wrap scroll-view" scroll-x="true" show-scrollbar="false">
							<block v-for="(item, index) in decoItem.data.info" :key="index">
								<view class="svideo_main_block2 svideo_main_block_l2"
									@click="skipTo(decoItem.type,item.liveId,item.info)"
									:style="{borderRadius:decoItem.border_radius+'px'}">
									<image class="video_bg1" :src="item.liveCover"></image>
									<image class="live_list_b_img_hua" :src="imgUrl+'svideo/zhibo-dianzan.gif'"></image>
									<view class="svideo_main_block_lw">
										<view class="svideo_main_block_lt">
											<image class="svideo_block_t_img svideo_block_t_img2zb"
												:src="imgUrl+'svideo/zx_zhibo_gif.gif'"></image>
											<text
												class="svideo_block_t_text svideo_block_t_text2">{{item.viewingNum}}人观看</text>
										</view>
									</view>
									<view class="svideo_main_block_b2 svideo_main_block_b2_zb">
										<text>{{item.liveName}}</text>
									</view>
								</view>
							</block>
						</scroll-view>
					</view>
				</view>
				<!-- 直播 end -->

				<!-- 短视频 start -->
				<view class="svideo" v-if="decoItem.type == 'svideo' && decoItem.is_show == true">
					<view class="svideo_title">
						<view class="svideo_title_l">{{decoItem.title}}</view>
						<view class="svideo_title_r" @click="skipTo('svideo_center')">更多视频 ></view>
					</view>
					<!-- 方案一 -->
					<view class="svideo_main" v-if="decoItem.show_style == 'one' && decoItem.is_show == true">
						<view class="svideo_main_block svideo_main_block_l" v-for="(item,index) in decoItem.data.info"
							:key="index" @click="skipTo(decoItem.type,item)"
							:style="{borderRadius:decoItem.border_radius+'px'}">
							<view class="svideo_main_block_w">
								<view class="svideo_main_block_t">
									<image class="svideo_block_t_img svideo_block_t_img2s svideo_block_t_img0"
										:src="imgUrl+'svideo/zx_play.png'"></image>
									<text class="svideo_block_t_text">{{item.clickNum}}人观看</text>
								</view>
							</view>
							<!-- <image class="video_bg1" :src="item.videoImage"></image> -->
							<view class="video_bg1" :style="'background-image:url('+item.videoImage+')'"></view>
							<view class="svideo_main_block_b">{{item.videoName}}</view>
						</view>
					</view>


					<!-- 方案2 -->
					<view class="svideo_main2" v-if="decoItem.show_style == 'two' && decoItem.is_show == true">
						<scroll-view class="scroll-view svideo2_wrap" scroll-x="true" show-scrollbar="false">
							<block v-for="(item, index) in decoItem.data.info" :key="index">
								<view class="svideo_main_block2 svideo_main_block_l2"
									@click="skipTo(decoItem.type,item)"
									:style="{borderRadius:decoItem.border_radius+'px'}">
									<image class="video_bg1" :src="item.videoImage"></image>
									<view class="svideo_main_block_w">
										<view class="svideo_main_block_t">
											<image class="svideo_block_t_img svideo_block_t_img2 svideo_block_t_img0"
												:src="imgUrl+'svideo/zx_play.png'"></image>
											<text class="svideo_block_t_text">{{item.clickNum}}人观看</text>
										</view>
									</view>
									<view class="svideo_main_block_b2">{{item.videoName}}</view>
								</view>
							</block>
						</scroll-view>
					</view>

					<!-- 方案3 -->
					<view class="svideo_main5" v-if="decoItem.show_style == 'three' && decoItem.is_show == true">
						<block v-for="(item, index) in decoItem.data.info" :key="index">
							<view class="svideo_main_block5 svideo_main_block50" @click="skipTo(decoItem.type,item)"
								:style="{borderRadius:decoItem.border_radius+'px'}">
								<image class="video_bg3" :src="item.videoImage"></image>
								<view class="svideo_bg_img"></view>
								<view class="svideo_block_bgimg0">
									<view class="svideo_block_t5_w">
										<view class="svideo_block_t5">{{item.clickNum}}人观看</view>
									</view>
									<view class="svideo_block_bgimg1_wrap">
										<view class="svideo_block_bgimg1">
											<image class="video_bg" :src="item.videoImage"></image>
											<image class="svideo_block_bgimg3 svideo_block_t_img0 svideo_block_t_img0"
												:src="imgUrl+'svideo/zx_play.png'"></image>
										</view>
									</view>
								</view>
								<view class="svideo5_b">
									<view class="svideo5_b_title">{{item.videoName}}</view>
									<view class="svideo5_b_text">{{item.introduction}}</view>
								</view>
							</view>
						</block>
					</view>

					<!-- 方案4 -->
					<view class="svideo_main3" v-if="decoItem.show_style == 'four' && decoItem.is_show == true"
						style="padding-bottom:30rpx;">
						<!-- #ifndef MP-ALIPAY -->
						<swiper class="swiper-block" :current="1" :circular="true" @change="swiperChange2"
							previous-margin="120rpx" next-margin="120rpx">
							<block v-for="(item, index) in decoItem.data.info" :key="index" :index="index">
								<swiper-item :style="'left:'+(index*63+71)+'rpx;'"
									:class="swiperIndex1 == index  ? 'swiper-item1' : 'swiper-item'">
									<view class="optionBox" style="text-align:center"
										@click="skipTo(decoItem.type,item)">
										<!-- wx-2-start -->
										<!-- #ifdef MP-WEIXIN -->
										<image mode="aspectFill" :src="item.videoImage"
											:class="swiperIndex1 == index  ? 'active1' : 'active2'"
											:style="{height:'345rpx',borderRadius:decoItem.border_radius+'px'}"></image>
										<!-- #endif -->
										<!-- wx-2-end -->

										<!-- #ifndef MP-WEIXIN -->
										<view :class="swiperIndex1 == index  ? 'active1' : 'active2'"
											:style="'height:'+(swiperIndex1 == index?'345rpx':'280rpx')+';border-radius:'+decoItem.border_radius+'px;'+'backgroundImage:url('+item.videoImage+')'">
										</view>
										<!-- #endif -->

										<view class="svideo_main_block_sw">
											<view class="">
												<image class="svideo_block_t_img svideo_person_num"
													:src="imgUrl+'svideo/play_video.png'"></image>
											</view>
										</view>
										<view class="svideo_main_block_b3_w">
											<view class="svideo_main_block_b3">{{item.clickNum}}人观看</view>
										</view>
									</view>
								</swiper-item>
							</block>
						</swiper>
						<!-- #endif -->

						<!-- #ifdef MP-ALIPAY -->
						<swiper class="swiper-block" :current="1" :circular="true" @change="swiperChange2"
							previous-margin="120px" next-margin="120px">
							<block v-for="(item, index) in decoItem.data.info" :key="index" :index="index">

								<swiper-item :class="swiperIndex1 == index  ? 'swiper-item1' : 'swiper-item'">
									<view class="optionBox" style="text-align:center"
										@click="skipTo(decoItem.type,item)">
										<view :class="swiperIndex1 == index  ? 'active1' : 'active2'"
											:style="'height:'+(swiperIndex1 == index?'345rpx':'280rpx')+';border-radius:'+decoItem.border_radius+'px;'+'backgroundImage:url('+item.videoImage+')'">
										</view>
										<view class="svideo_main_block_sw">
											<view class="">
												<image class="svideo_block_t_img svideo_person_num"
													:src="imgUrl+'svideo/play_video.png'"></image>
											</view>
										</view>
										<view class="svideo_main_block_b3_w">
											<view class="svideo_main_block_b3">{{item.clickNum}}人观看</view>
										</view>
									</view>
								</swiper-item>


							</block>
						</swiper>
						<!-- #endif -->

					</view>
					<!-- 方案5 -->
					<view class="svideo_main4" v-if="decoItem.show_style == 'five' && decoItem.is_show == true">
						<scroll-view class="scroll-view svideo4_wrap" scroll-x="true" show-scrollbar="false">
							<block v-for="(item, index) in decoItem.data.info" :key="index">
								<view class="svideo_main_block4" @click="skipTo(decoItem.type,item)"
									:style="{borderRadius:decoItem.border_radius+'px'}">
									<image class="video_bg" :src="item.videoImage"
										:style="{borderRadius:decoItem.border_radius+'px'}"></image>
									<view class="svideo_main_block_w svideo_main_block_t svideo_main_block_t0">
										<image class="svideo_block_t_img svideo_block_t_img24 svideo_block_t_img0"
											:src="imgUrl+'svideo/zx_play.png'"></image>
										<text class="svideo_block_t_text4">{{item.clickNum}}人观看</text>
									</view>
									<view class="svideo_main_block_b4 svideo_main_block_b40"
										:style="{borderRadius:[0,0,decoItem.border_radius+'px',decoItem.border_radius+'px']}">
										<image class="video_bg"
											:style="{borderRadius:[0,0,decoItem.border_radius+'px',decoItem.border_radius+'px']}"
											:src="imgUrl+'svideo/zx_v_bg'+(index%3+1)+'.png'"></image>
										<view class="svideo_main_block_b4_text">{{item.videoName}}</view>
									</view>
								</view>
							</block>
						</scroll-view>
					</view>
				</view>
				<!-- 短视频 end -->

				<!-- 图片组合 start -->
				<view class="index_deco"
					v-if="decoItem.type == 'tupianzuhe' && decoItem.sele_style == 8 && decoItem.is_show == true">
					<view class="featured">
						<view class="featured_item" v-for="(itemTp,indexTp) in decoItem.data" :key="indexTp">
							<view class="featured_tit" @click="skipTo(itemTp.url_type,itemTp.url,itemTp.info.productId || itemTp.info.defaultProductId,
									itemTp.info.pid,itemTp.info.seckillId,itemTp.info)">{{itemTp.main_title}}</view>
							<view class="featured_text" @click="skipTo(itemTp.url_type,itemTp.url,itemTp.info.productId || itemTp.info.defaultProductId,
									itemTp.info.pid,itemTp.info.seckillId,itemTp.info)">{{itemTp.sub_title}}</view>
							<view class="featured_img">
								<block v-for="(itemPic,indexPic) in itemTp.img" :key="indexPic">
									<image :src="itemPic.img" mode="aspectFit" @click="skipTo(itemPic.url_type,itemPic.url,itemPic.info.productId || itemPic.info.defaultProductId,
											itemPic.info.pid,itemPic.info.seckillId,itemPic.info)"></image>
								</block>
							</view>
						</view>
					</view>
				</view>
				<!-- 图片组合 end -->
			</view>
		</view>

		<!-- 顶部分类切换 -->
		<view class="sort_sub_wrap" v-if="tab_index != 0 && sort_obj.categoryList.length>0" :style="{marginTop:`calc(${client=='mp'?240:160}rpx + ${stateBarHeight+10}px)`}">
			<view class="sort_sub_top">
				<view class="sort_sub_item" v-for="(item,index) in sort_obj.categoryList" :key="index"
					@click="goGoodsList(item.categoryId)">
					<view class="sort_sub_img">
						<image :src="item.categoryImage" mode="aspectFit"></image>
					</view>
					<view class="sort_sub_name">{{filterFun(item.categoryName)}}</view>
				</view>
				<view class="see_more_wrap" @click="toAllSort(sort_obj.categoryId)"
					v-if="sort_obj.categoryList.length>7">
					<view class="more_icon_circle">
						<image :src="imgUrl+'index/more.png'" mode="aspectFit" class="more_icon"></image>
					</view>
					<view class="see_more_text">查看更多</view>
				</view>
			</view>
			<view class="sort_sub_goods">
				<goods-item-v :goods_info="item" :show_sale="false" :icon_type="1" :border_radius="8" :height="225"
					v-for="(item,index) in sort_obj.goodsList" :key="index"></goods-item-v>
			</view>
		</view>

		<!-- 空首页装修 -->
		<view class="empty_sort_page" v-if="deco_info == null" style="padding-top: 25vh;">
			<image :src="imgUrl+'empty_fitUp.png'" mode="aspectFit" class="empty_img"></image>
			<view class="empty_text">首页暂未装修</view>
		</view>
		<!-- 专题空页面 -->
		<view class="empty_sort_page" v-if="is_show_top == false && deco_info.length == 0 && noData==false  && noDatas == false"
			style="padding-top: 25vh;height:100vh;">
			<image :src="imgUrl+'empty_fitUp.png'" mode="aspectFit" class="empty_img"></image>
			<view class="empty_text">{{topicNotExit?'专题页不存在':'专题页暂未装修'}}</view>
		</view>
		<!-- 分类空页面 -->
		<view class="empty_sort_page" v-if="tab_index > 0 && is_show_empty == true">
			<image :src="imgUrl+'empty_goods.png'" mode="aspectFit" class="empty_img"></image>
			<view class="empty_text">该分类暂无相关商品</view>
		</view>

		<!-- 分类空商品 -->
		<view class="empty_sort_page" v-if="tab_index > 0 && is_show_empty_goods == true && is_show_empty == false"
			style="padding-top: 260rpx;">
			<image :src="imgUrl+'empty_goods.png'" mode="aspectFit" class="empty_img"></image>
			<view class="empty_text">该分类暂无相关商品</view>
		</view>

		<!-- 首页开屏框 start -->
		<view :class="isCookie == true ? 'open_screen show-dialog' : 'open_screen hide-dialog'"
			v-if="isCookie == true && home_page_img&&home_page_img.length > 0 && home_page_img[0].imgUrl"
			@touchmove.stop.prevent="()=>{}">
			<view class="open_screen_con" @click="gotoGoods_detail('home')">
				<view class="con_img" @click.stop="close_openScreen">
					<image :src="openscnImg"></image>
				</view>
				<image class="open_screen_con_img image_mode_fill_h5" mode="aspectFit" :src="home_page_img[0].imgUrl"
					:style="{ width: width + 'rpx', 'height': height + 'rpx' }"></image>
			</view>
		</view>
		<!-- 开屏框 end -->
		<!-- 店铺首页开屏框 start -->
		<!-- <view :class="storeIsCookie == true ? 'open_screen show-dialog' : 'open_screen hide-dialog'"
			v-if="store_page_img && storeIsCookie == true && store_page_img.length > 0 && store_page_img[0].imgUrl">
			<view class="open_screen_con" @click="gotoGoods_detail('store')">
				<view class="con_img" @click.stop="close_storeOpenScreen">
					<image :src="openscnImg"></image>
				</view>
				<image class="open_screen_con_img" :src="store_page_img[0].imgUrl"
					:style="{ width: store_width + 'rpx', 'height': store_height + 'rpx' }" mode="aspectFit"></image>
			</view>
		</view> -->
		<!-- 开屏框 end -->
	</view>
</template>

<script>
	import { mapState} from 'vuex';
	import filters from "@/utils/filter.js"
	import tabMenu from '@/components/index-tab-menu.vue'
	import sortList from '@/components/index-sort-list.vue'
	import uniSwiperDot from '@/components/uni-swiper-dot/uni-swiper-dot.vue'
	import { decoType } from '@/utils/common.js'
	import  goodsItemV from '@/components/goods_item_v.vue';
	import loadingState from "@/components/loading-state.vue";
	import jyfParser from '@/components/jyf-parser/jyf-parser.vue'
	import uniNoticeBar from '@/components/uni-notice-bar/uni-notice-bar.vue'
	import { quillEscapeToHtml, getSceneParam } from '@/utils/common.js'
	export default {
		data() {
			return {
				vid: '',
				nearStoreList: [],
				showSelect1: false,
				showSelect2: false,
				imgUrl: getApp().globalData.imgUrl,
				titleNViewBackground: '',
				swiperCurrent: 0,
				// #ifdef MP
				menuButtonHeight:uni.getMenuButtonBoundingClientRect().height+'px',
				menuButtonTop:uni.getMenuButtonBoundingClientRect().top+'px',
				// #endif
				swiperCurrent2: 0,
				swiperLength: 0,
				carouselList: [],
				goodsList: [],
				current: 0, //轮播图指示点
				dotsStyles: {
					selectedBackgroundColor: '#fff',
					width: 8,
					height: 8,
					selectedBorder: 'none',
					backgroundColor: 'rgba(255,255,255,0.4)',
					border: 'none',
					bottom: 8
				},
				noticeImg1: getApp().globalData.imgUrl + 'index/notice1.png',
				noticeImg2: getApp().globalData.imgUrl + 'index/notice2.png',
				// deco_info:[] ,//首页装修数据
				// home_page_img:[] , //首页开屏图列表
				dataObj: {}, //完整版首页装修数据
				border_radius: '', //顶部轮播背景圆角
				broadcastData1: [], //公告1滚动文字
				broadcastData2: [], //公告2滚动文字
				broadcastStyle1: { //滚动文字样式设置
					speed: 1, //每秒30px
					font_size: "24", //字体大小(rpx)
					text_color: "#666", //字体颜色
					back_color: "linear-gradient(to right,rgba(250,244,244,0.2) 0%, rgba(255,244,244,1) 50%, rgba(250,244,244,0.2) 100%);", //背景色
				},
				broadcastStyle2: { //滚动文字样式设置
					speed: 1, //每秒30px
					font_size: "24", //字体大小(rpx)
					text_color: "#fff", //字体颜色
					back_color: "#3A3A3A", //背景色
				},
				telImg: getApp().globalData.imgUrl + 'index/mobile.png',
				sort_nav_list: [], //顶部分类列表
				sort_obj: {}, //二级分类列表+分类商品列表
				tab_index: 0, //分类切换下标
				is_show_index: true,
				adArr: [], //公告数组
				icon2: getApp().globalData.imgUrl + 'index/add2.png',
				icon3: getApp().globalData.imgUrl + 'index/add.png',
				icon4: getApp().globalData.imgUrl + 'index/add3.png',
				icon5: getApp().globalData.imgUrl + 'index/stop.png',
				isCookie: false,
				storeIsCookie: false,
				openscnImg: getApp().globalData.imgUrl + 'index/close_screen.png',
				isShowTab: true, //是否显示顶部分类
				sortLen: '', //二级分类长度
				isShow: true,
				is_show_empty: false, //是否展示分类空页面
				pure_bg_color: '', //顶部栏有弧度纯色
				border_radius1: '', //推荐商品二角度设置
				border_radius2: '', //推荐商品三角度设置
				border_radius3: '',
				top_bg: 'var(--color_main)', //顶部状态栏颜色
				noData: false, //暂无数据
				noDatas: true, //暂无数据
				is_show_empty_goods: false, //是否展示分类空商品页面
				// #ifdef APP-PLUS || H5
				isIos: uni.getDeviceInfo().platform == 'ios', //是否ios手机
				// #endif
				goods_info: {},
				swiperIndex1: 1,
				filters,
				showState: false,
				stateBarHeight:20,
				nav_left_icon: 'back', //底部tab进入的话为空，否则为back
				navBarColor:{
					backgroundColor:'rgba(255,255,255,0)',
					color:'#fff'
				},
				cur_city: '',
				searchStoreParam: {
					distance: 0.1,
					sort: 1,
				},
				searchStoreVal: {
					distance: '附近',
					sort: '综合排序',
				},
				distanceList: [
					{
						label: '附近',
						val: 0.1
					},{
						label: '500m以内',
						val: 0.5
					},{
						label: '1km以内',
						val: 1
					},{
						label: '3km以内',
						val: 3
					},{
						label: '5km以内',
						val: 5
					},{
						label: '10km以内',
						val: 10,
					},
				],
				sortList: [
					{
						label: '综合排序',
						val: 1
					},{
						label: '按距离排序',
						val: 2
					},{
						label: '按评分排序',
						val: 3
					}
				],
			};
		},
		components: {
			tabMenu,
			sortList,
			uniSwiperDot,
			goodsItemV,
			jyfParser,
			uniNoticeBar
		},
		props: [
			'is_show_top', 'deco_info', 'topic_name', 'is_from_found','is_from_founds', 'home_is_show_top_cat', 'home_page_img',
			'width', 'height', 'store_width', 'store_height', 'store_page_img', 'topicNotExit', 'store_id', 'type','transpar'
		],
		computed: {
			...mapState(['hasLogin', 'userInfo', 'userCenterData', 'locationObj']),
			client(){
				//app-2-start
				// #ifdef APP-PLUS
				return 'app'
				// #endif
				//app-2-end
				
				// #ifdef MP
				return 'mp'
				// #endif
				
				// #ifdef H5
				return 'h5'
				// #endif
			}
		},
		onShow() {
	
		},
		async mounted() {
			this.getSortList();
			this.searchStore();
			//首页装修开平图缓存
			let type = this.type != undefined && this.type ? this.type : ''
			let cookievalue = uni.getStorageSync('homeCookie' + type);
			if (!cookievalue) {
				this.isCookie = true;
				uni.setStorage({
					key: 'homeCookie' + type,
					data: new Date().getTime()
				});
			} else {
				if (new Date().getTime() * 1 - cookievalue * 1 > 24 * 60 * 60 * 1000) {
					this.isCookie = true;
					uni.setStorage({
						key: 'homeCookie' + type,
						data: new Date().getTime()
					});
				} else {
					this.isCookie = false;
				}
			}
			let systemInfo = uni.getSystemInfoSync()
			this.stateBarHeight = systemInfo.statusBarHeight
			// 推荐商品圆角设置

			// 更新直播观看数量
			uni.$on('updateWatchNum', (res) => {
				this.deco_info[res.decoIndex].data.info[res.index].viewingNum = Number(this.deco_info[res
					.decoIndex].data.info[res.index].viewingNum) + 1
			})
			this.cur_city = this.locationObj.cityName;
		},
		destroyed() {
			uni.$off('updateWatchNum');
		},
		watch: {
			locationObj: {
			      handler(newVal, oldVal) {
			      	if(JSON.stringify(newVal) != JSON.stringify(oldVal)){
			      		// console.log('oldVal--',JSON.stringify(oldVal));
			      		// console.log('newVal--',JSON.stringify(newVal));
			      		this.searchStore();
						this.cur_city = newVal.cityName;
			      	}				  
			      },
				  deep: true, // 开启深度监听
			},
			deco_info(val) {
				this.noData = val && val.length == 0 ? true : false
				this.noDatas = val && val.length == 0 ? false : true
				let is_top_cat_nav = false
				val && val.map(item => {
					if (item.type == 'top_cat_nav' && item.data && item.data.length > 0) {
						is_top_cat_nav = true
						let {swiperCurrent} = this
						if(item.data[swiperCurrent].bg_color){
							this.pure_bg_color = item.data[swiperCurrent].bg_color;
						}else{
							this.pure_bg_color = 'var(--color_main_bg)';
						}
						this.titleNViewBackground = 'linear-gradient(' + item.data[swiperCurrent].bg_color + ' 0%,' + item.data[0].bg_color +' 42%,#ffffff 100%)';
					} else if (item.type == 'tuijianshangpin' && item.show_style == "big") {
						this.border_radius1 = item.border_radius + 'px' + ' ' + item.border_radius + 'px' + ' 0 0'
						this.border_radius3 = '0 0 ' + item.border_radius + 'px' + ' ' + item.border_radius + 'px'
					} else if (item.type == 'tuijianshangpin' && item.show_style == "list") {
						this.border_radius2 = item.border_radius + 'px' + ' 0 0 ' + item.border_radius + 'px'
					}
					if (item.type == 'top_cat_nav') {
						var newList = []
						for (var i = 0; i < item.data.length; i++) {
							if (item.data[i].img) {
								newList.push(item.data[i])
							}
						}
						item.data = newList
					} else if (item.type == 'fuwenben' && item.is_show == true) {
						let translateText = quillEscapeToHtml(item.text);
						item.text = quillEscapeToHtml(translateText);
					}
				})
				
				if(!is_top_cat_nav){
					this.pure_bg_color = 'var(--color_main)';
				}
			},
		},
		methods: {
			// 加入购物车 规格选择
			showSpcPop(goods_info) {
				this.vid = goods_info.storeId
				goods_info.defaultProductId = goods_info.productId
				this.$emit('showSpcPop',goods_info)
			},
			//进入商品详情页
			goGoodsDetail(goods_info) {
				this.$Router.push({
					path: '/standard/product/detail',
					query: {
						productId: goods_info.defaultProductId,
						goodsId: goods_info.goodsId
					}
				})
			},
			//去店铺页面
			goShopHome(storeId) {
				this.$Router.push({
					path: '/standard/store/shopHomePage',
					query: {
						vid: storeId,
					}
				})
			},
			searchStore() {
				const { distance, sort } = this.searchStoreParam;
				const locationArr = (this.locationObj.location || '').split(',')
				let param = {}
				param.url = 'v3/seller/front/store/nearestList'
				param.method = 'GET'
				param.data = {
					distance,
					sort,
					lng: locationArr[0],
					lat: locationArr[1],
				}
				this.$request(param).then(res => {
					if (res.state == 200) {
						this.nearStoreList = res.data.list;
					}
				})
			},
			handleSearch(type,item) {
				if(type == 1){ //距离
					this.searchStoreParam.distance = item.val;
					this.searchStoreVal.distance = item.label;
					this.searchStore()
					this.showSelect1 = false;
				}
				if(type == 2){
					this.searchStoreParam.sort = item.val;
					this.searchStoreVal.sort = item.label;
					this.searchStore()
					this.showSelect2 = false;
				}
			},
			// 跳转页面
			toAddressPage() {
				this.$Router.push({
					path: '/newPages/chooseAddress/chooseAddress'
				})
			},
			restoreTab(){
				this.$refs.tabmenu.currIndex=0
				this.getChildList([],0,0)
			},
			filterFun: function(value) {
				if (value && value.length > 4) {
					value = value.substring(0, 4);
				}
				return value;
			},
			//跳转商品列表
			goGoodsList(categoryId) {
				this.$Router.push({
					path: '/standard/product/list',
					query: {
						categoryId
					}
				})
			},
			onScroll(res){
				if(res.scrollTop>100){
					this.navBarColor.backgroundColor = 'rgb(255,255,255)'
					this.navBarColor.color='#333'
				}else{
					this.navBarColor.backgroundColor='rgba(255,255,255,0)'
					this.navBarColor.color='#fff'
				}
			},
			// 扫描二维码
			scanCode(type) {
				let _this = this
				uni.scanCode({
					success: function(res) {
						let regExp = /^([0-9a-zA-Z]\-?)+$/; //返回的登录code格式
						if (res.result && regExp.test(res.result)) {
							// 扫码登录
							_this.$Router.push({
								path: '/pages/public/codeLogin',
								query: {
									u: res.result
								}
							})
						} else {
							let producPathExp = /standard\/product\/detail/g
							if (res.result && producPathExp.test(res.result)) {
								let productId = getSceneParam(res.result, 'productId')
								_this.$Router.push({
									path: '/standard/product/detail',
									query: {
										productId
									}
								})
							} else {
								_this.$api.msg('未识别二维码，请稍后重试')
							}
						}
					},
					fail(err) {
						_this.$api.msg('扫码失败')
					}
				});
			},
			// 返回上一页
			toBack() {
				this.$Router.back(1)
			},
			//轮播图切换修改背景色
			swiperChange(e) {
				const index = e.detail.current;
				this.swiperCurrent = index;
				this.deco_info && this.deco_info.map(item => {
					if (item.type == 'top_cat_nav' && item.data) {
						this.pure_bg_color = item.data[index].bg_color
						if (item.swiper_bg_style == 1) {
							this.titleNViewBackground = item.data[index].bg_color
						} else {
							this.titleNViewBackground = 'linear-gradient(' + item.data[index].bg_color +' 0%,' +item.data[index].bg_color +' 42%,#ffffff 100%)'
						}
					}
				})
			},
			// 短视频轮播
			swiperChange2(e) {
				this.swiperIndex1 = e.detail.current
			},
			// 轮播图模块切换下标
			swiperChangeIndex(e) {
				this.swiperCurrent2 = e.detail.current
			},
			// 获取分类列表
			getSortList() {
				let param = {}
				param.url = 'v3/goods/front/goods/category/topCategory'
				param.method = 'GET'
				this.$request(param).then(res => {
					if (res.state == 200) {
						this.sort_nav_list = res.data
						this.sort_nav_list.unshift({
							categoryName: '首页'
						})
					}
				})
			},
			// 获取二级分类及分类商品列表
			getChildList(list, index,categoryId) {
				if (this.tab_index != index) {
					// 切换tab回到顶部
					uni.pageScrollTo({
						scrollTop: 0,
						duration: 0
					})
					this.sort_list = [];
				}
				this.tab_index = index
				if (index > 0) {
					this.sort_obj = list
					this.tab_current = 1
					if (this.sort_obj.categoryList.length > 0) {
						this.sortLen = this.sort_obj.categoryList.length
						this.is_show_empty = false
					} else {
						this.is_show_empty = true
					}

					if (this.sortLen > 9) {
						this.sort_obj.categoryList = this.sort_obj.categoryList.slice(0, 9)
					}
					if (this.sort_obj.goodsList.length > 0) {
						this.is_show_empty_goods = false
					} else {
						this.is_show_empty_goods = true
					}
					this.is_show_index = false
					this.tab_categoryId = categoryId
					this.tab_hasMore = this.$checkPaginationHasMore(list.pagination)
				} else {
					this.is_show_index = true
					this.swiperChange({
						detail: {
							current: 0
						}
					});
				}
				this.isShow = false
				this.isShow = true
			},
			// 拨打客服电话
			callUp(tel) {
				uni.makePhoneCall({
					phoneNumber: tel.toString()
				})
			},
			toSearchPage() {
				const page = getCurrentPages()
				let curPage = page[page.length - 1]
				if (curPage.route == 'extra/tshou/index/index') {
					this.$Router.push('/extra/tshou/search/search')
				} else {
					this.$Router.push('/pages/search/search')
				}
			},
			// 相关跳转
			skipTo(type, url, productId, pid, seckillId, info, item) {
				if (type == 'url') { //跳转链接地址
					if (!url) {
						return;
					}
					// #ifdef H5
					window.open(url)
					// #endif

					//app-3-start
					// #ifdef APP-PLUS
					plus.runtime.openURL(url) //这里默认使用外部浏览器打开而不是内部web-view组件打开
					// #endif
					//app-3-end

					// #ifdef MP
					this.$Router.push({
						path: '/pages/index/skip_to',
						query: {
							url
						}
					})
					// #endif
				} else if (type == 'goods') { //跳转商品详情页
					this.$Router.push({
						path: '/standard/product/detail',
						query: {
							productId,
							goodsId: url
						}
					})
				} else if (type == 'category') { // 分类列表
					this.$Router.push({
						path: '/standard/product/list',
						query: {
							categoryId: url
						}
					})
				} else if (type == 'keyword') { // 关键词
					if (this.store_id) {
						this.$Router.push({
							path: '/standard/product/list',
							query: {
								keyword: url,
								source: 'search',
								storeId: this.store_id
							}
						})
					} else {
						this.$Router.push({
							path: '/standard/product/list',
							query: {
								keyword: url,
								source: 'search'
							}
						})
					}
				} else if (type == 'topic') { //跳转专题页
					this.$Router.push({
						path: '/pages/index/topic',
						query: {
							id: (info.decoId ? info.decoId : info.id)
						}
					})
				} else if (type == 'brand_home') { //品牌列表
					this.$Router.push('/pages/public/brand')
				} else if (type == 'seckill') { //秒杀
					this.$Router.push({
						path: '/standard/seckill/seckill',
						query: {
							seckillId
						}
					})
				} else if (type == 'ladder_group') { //阶梯团
					this.$Router.push('/standard/ladder/index/index')
				} else if (type == 'presale') { //预售入口页
					this.$Router.push('/standard/presale/index/list')
				} else if (type == 'voucher_center') { //优惠券领券中心
					this.$Router.push('/standard/coupon/couponCenter')
				} else if (type == 'point') { //积分商城首页
					this.$Router.push('/standard/point/index/index')
				} else if (type == 'svideo_center') { //短视频列表
					// this.$Router.pushTab('/pages/index/information')
					this.$Router.push('/extra/svideo/svideoList')
				} else if (type == 'live_center') { //直播列表
					this.$Router.push('/extra/live/liveList')
				} else if (type == 'spreader_center') { //推手中心
					if (!this.hasLogin) {
						this.$emit('needLogin')
					} else {
						this.$Router.push('/extra/tshou/index/index')
					}
				} else if (type == 'live') { //直播播放页面
					this.$livePlayNav({
						live_id: url,
						decoIndex: pid,
						index: seckillId,
					})
				} else if (type == 'svideo') { //短视频播放页面
					this.$videoPlayNav({
							video_id: url.videoId,
							curLabelId: url.labelId,
							author_id: url.authorId
						})
				} else if (type == 'spell_group') {
					this.$Router.push('/standard/pinGroup/index/index')
				} else if (type == 'sign_center') {
					if (!this.hasLogin) {
						this.$emit('needLogin')
					} else {
						this.$Router.push('/standard/signIn/signIn')
					}
				} else if (type == 'rank') {
					this.$Router.push('/standard/rank/aggr')
				} else if (type == 'draw') {
					if (!this.hasLogin) {
						this.$emit('needLogin')
					} else {
						this.$Router.push({
							path: '/standard/lottery/detail',
							query: {
								drawId: url
							}
						})
					}
				} else if (type == 'store_list') { //店铺街
					this.$Router.push('/standard/store/list')
				} else if (type == 'store') {
					this.$Router.push(`/standard/store/shopHomePage?vid=${info.storeId}`)
				} 
			},
			// 跳转商品详情页
			toGoodsDetail(productId, goodsId) {
				this.$Router.push({
					path: '/standard/product/detail',
					query: {
						productId,
						goodsId
					}
				})
			},
			// 跳转消息列表页
			toMsg() {
				if (!this.hasLogin) {
					this.$emit('needLogin')
				} else {
					this.$Router.push('/standard/chat/list')
				}
			},
			// 跳转全部分类页
			toAllSort(cateId) {
				let app = getApp()
				app.globalData.cateId = cateId
				this.$Router.pushTab('/pages/category/category')
			},

			// 加入购物车
			addCart(productId, goodsId, item) {
				let _this = this;
				this.goods_info = item
				if (this.userInfo.access_token) {
					let param = {}
					param.url = 'v3/business/front/cart/add'
					param.method = 'POST'
					param.data = {
						productId: productId,
						number: 1
					}
					this.$request(param).then(res => {
						if (res.state == 200) {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
							_this.cartUpdate();
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none',
								duration: 700
							})
						}
					})
				} else {
					let cart_list = {
						storeCartGroupList: [{
							promotionCartGroupList: [{
								cartList: [{
									buyNum: 1,
									goodsId: this.goods_info.goodsId,
									productId: this.goods_info.productId || this
										.goods_info
										.defaultProductId,
									productImage: this.goods_info.goodsPic ? this
										.goods_info
										.goodsPic : this.goods_info.goodsImage,
									goodsName: this.goods_info.goodsName,
									isChecked: 1,
									productPrice: this.goods_info.goodsPrice,
									// productStock: this.goods_info.productStock
								}],
							}],
							storeId: this.goods_info.storeId,
							storeName: this.goods_info.storeName,
							checkedAll: true
						}],
						checkedAll: true,
						invalidList: []
					}

					let local_cart_list = uni.getStorageSync('cart_list') //购物车列表本地缓存
					if (local_cart_list) {
						let tmp_list1 = []
						let tmp_list2 = []
						cart_list.storeCartGroupList.forEach(item => {
							item.promotionCartGroupList.forEach(item1 => {
								item1.cartList.forEach(item2 => {
									local_cart_list.storeCartGroupList.forEach(v => {
										v.promotionCartGroupList.forEach(v1 => {
											v1.cartList.forEach(v2 => {
												if (v2.productId ==
													item2
													.productId && v
													.storeId ==
													item
													.storeId) {
													tmp_list1.push(
														v)
												}
											})
											tmp_list2 = local_cart_list
												.storeCartGroupList.filter(
													v => {
														return v.storeId ==
															item
															.storeId
													})
										})
									})
								})
							})
						})
						if (tmp_list1.length > 0 && tmp_list2.length > 0) { //同一店铺同一商品
							local_cart_list.storeCartGroupList.map(item => {
								item.promotionCartGroupList.map(item1 => {
									item1.cartList.map(item2 => {
										if (item2.productId == this.goods_info.productId &&
											item
											.storeId == this.goods_info.storeId) {
											item2.buyNum += 1
										}
									})
								})
							})
						} else if (tmp_list1.length == 0 && tmp_list2.length > 0) { //同一店铺不同商品
							local_cart_list.storeCartGroupList.map(item => {
								if (item.storeId == this.goods_info.storeId) {
									item.promotionCartGroupList.map(item2 => {
										item2.cartList.push(cart_list.storeCartGroupList[0]
											.promotionCartGroupList[0].cartList[0])
									})
								}
							})
						} else { //不同店铺不同商品
							local_cart_list.storeCartGroupList.push(cart_list.storeCartGroupList[0])
						}

						// 未登录购物车展示数据
						uni.setStorage({
							key: 'cart_list',
							data: local_cart_list,
							success: function() {
								//更新购物车数量和购物车数据
							}
						});

					} else {
						uni.setStorage({
							key: 'cart_list',
							data: cart_list,
							success: function() {
								//更新购物车数量和购物车数据
							}
						});
					}
					uni.showToast({
						title: '加入购物车成功！',
						icon: 'none'
					})
					_this.cartUpdate();
				}
			},
			
			//关闭首页广告屏
			close_openScreen() {
				this.isCookie = false;
			},
			//关闭首页广告屏
			close_storeOpenScreen() {
				this.storeIsCookie = false;
			},
			//点击广告屏跳转到详情页面
			gotoGoods_detail(type) {
				if (type == 'home') {
					this.isCookie = false;
					let osValue = this.home_page_img[0];
					this.$diyNavTo(osValue, 'home');
				} else {
					this.storeIsCookie = false
					let osValue = this.store_page_img[0];

					this.$diyNavTo(osValue, this.store_id);
				}
			},
			getMoreData() {
				if (this.tab_index == 0) {
					return
				}
				if (this.tab_hasMore) {
					this.tab_current++
					this.$request({
						url: 'v3/goods/front/goods/category/list?categoryId1=' + this.tab_categoryId,
						data: {
							current: this.tab_current
						}
					}).then(res => {
						if (res.state == 200) {
							this.sort_obj.goodsList = this.sort_obj.goodsList.concat(res.data.goodsList)
							this.tab_hasMore = this.$checkPaginationHasMore(res.data.pagination)
						}
					})
				}
			}
		},
		// #ifndef MP
		// 标题栏input搜索框点击
		onNavigationBarSearchInputClicked: async function(e) {
			this.$Router.push('/pages/search/search')
		},
		//点击导航栏 buttons 时触发
		onNavigationBarButtonTap(e) {
			const index = e.index;
			if (index === 0) {
				this.$api.msg('点击了扫描');
			} else if (index === 1) {
				//app-4-start
				// #ifdef APP-PLUS
				const pages = getCurrentPages();
				const page = pages[pages.length - 1];
				const currentWebview = page.$getAppWebview();
				currentWebview.hideTitleNViewButtonRedDot({
					index
				});
				// #endif
				//app-4-end
				this.$Router.push('/newPages/notice/notice')
			}
		},
		// #endif
	}
</script>

<style lang="scss">
	// 开屏 -- start
	.container3 {
		width: 100%;
		margin: 0 auto;
		position: relative;

	}
  .fixed_top_mp{
    position: fixed;
    top: 0;
    z-index: 100;
    background: #fff;
  }

	.fixed_top {
		position: fixed;
		top: 0;
		z-index: 100;

		.fixed_bar {
			width: 100%;
			background: linear-gradient(90deg, #6984a4 0%, #3a4b5c 100%);
		}
	}

	.show-dialog {
		animation: 100ms showDialog linear forwards;
	}

	.hide-dialog {
		animation: 100ms hideDialog linear forwards;
	}

	@keyframes hideDialog {
		0% {
			opacity: 1;
		}


		25% {
			opacity: 0.75;
		}


		50% {
			opacity: 0.5;
		}


		75% {
			opacity: 0.25;
		}


		100% {
			opacity: 0;
		}
	}

	@keyframes showDialog {
		0% {
			opacity: 0;
		}


		25% {
			opacity: 0.25;
		}


		50% {
			opacity: 0.5;
		}


		75% {
			opacity: 0.75;
		}


		100% {
			opacity: 1;
		}
	}

	.container3 .open_screen {
		width: 750rpx;
		height: calc(100vh);
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		margin: 0 auto;
		background-color: rgba(0, 0, 0, 0.3);
		z-index: 99999;
	}

	.container3 .open_screen .open_screen_con {
		maring: 0 auto;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		border-radius: 15rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.container3 .open_screen .open_screen_con .open_screen_con_img {
		max-width: 580rpx !important;
		max-height: 776rpx !important;
		background-size: contain;
		border-radius: 15rpx;
	}

	.container3 .open_screen .open_screen_con .con_img {
		width: 58rpx;
		height: 58rpx;
		position: absolute;
		top: -58rpx;
		right: -58rpx;
	}

	.open_screen_con .con_img image {
		width: 100%;
		height: 100%;
	}

	// 开屏 -- end

	/* #ifdef H5 */
	.deco_wrap {
		margin-top: 160rpx !important;
	}
	/* #endif */

	/* #ifdef MP */
	.deco_wrap {
		// margin-top: calc(150rpx + var(--status-bar-height)) !important;
		padding-top: 0rpx;
	}

	.deco_wrap2 {
		margin-top: 108rpx;
	}
	/* #endif */
	//app-5-start
	/* #ifdef APP-PLUS */
	.deco_wrap {
		margin-top: calc(var(--status-bar-height) + 155rpx) !important;
	}
	/* #endif */
	//app-5-end
	.container3 {
		// padding-top: 180rpx;
		padding-bottom: 20rpx;
		overflow-x: hidden;
		background-color: #F5F5F5;
	}
	//app-6-start
	/* #ifdef APP-PLUS */
	.container3 {
		width: 750rpx;
		padding-top: 0rpx !important;
		padding-bottom: 0;
	}
	/* #endif */
	//app-6-end
	.mp-search-box_fixed{
		position: fixed;
		z-index: 9999;
		width: 750rpx;
		height: 100rpx;
		background: #fff;
	}
	.mp-search-box {
		position: relative;
		z-index: 9999;
		width: 750rpx;
		height: 100rpx;
		padding: 0 20rpx 0 20rpx;
		display: flex;
		box-sizing: border-box;
		align-items: center;
		overflow: hidden;

		/* #ifndef MP */
		justify-content: space-between;
		/* #endif */

		.ser-input {
			height: 65upx;
			line-height: 68upx;
			text-align: left;
			font-size: 28rpx;
			color: #999;
			border-radius: 20px;
			background: rgba(255, 255, 255, .6);
			padding-left: 62rpx;
			flex: 1;
			box-sizing: border-box;
			background-color: #fff;
		}

		.msg_img {
      height: 46rpx;
			margin-left: 20rpx;
			position: relative;
			image {
				width: 40rpx;
				height: 46rpx;
			}
			
			.message_new {
				min-width: 34rpx;
				height: 34rpx;
				background-color: #fff;
				position: absolute;
				top: 8rpx;
				right: 4rpx;
        /* #ifndef MP */
				top: -10rpx;
				right: -12rpx;
        /* #endif */
				border-radius: 50rpx;
				font-size: 20rpx;
				color: var(--color_price);
				text-align: center;
				vertical-align: middle;
				display: flex;
				justify-content: center;
				align-items: center;
			}
			.message_new_o{
				top: -12rpx;
				right: -12rpx;
			}
		}
	}

	page {
		.topic_top {
			padding-top: 0 !important;
		}

		.cate-section {
			position: relative;
			z-index: 5;
			//border-radius: 16upx 16upx 0 0;
			overflow-x: hidden;
		}

		.carousel-section {
			padding: 20rpx;
			box-sizing: border-box;
			background-color: #FFFFFF;

			.titleNview-placing {
				padding-top: 0;
				height: 0;
			}

			.carousel {
				.carousel-item {
					padding: 0;
				}
			}

			.swiper-dots {
				left: 45upx;
				bottom: 40upx;
			}
		}
	}

	.search_img {
		position: absolute;
		width: 31rpx;
		height: 31rpx;
		left: 40rpx;
		top: 50%;
		transform: translateY(-50%);
		z-index: 99;
	}



	page {
		background: #f5f5f5;
	}

	.m-t {
		margin-top: 16upx;
	}

	/* 头部 轮播图 */
	.carousel-section {
		position: relative;
		padding-top: 10px;

		.titleNview-placing {
			height: var(--status-bar-height);
			padding-top: 88px;
			box-sizing: content-box;
		}

		.titleNview-background {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 200upx;
			transition: .4s;
			border-radius: 0 0 30rpx 30rpx;
		}
	}

	.carousel {
		width: 100%;

		.carousel-item {
			width: 100%;
			height: 100%;
			// padding: 0 28upx !important;
			overflow: hidden;
		}

		image {
			width: 100%;
			height: 100%;
			border-radius: 10upx;
			overflow: hidden;
		}

		.itemImg {
			width: 100%;
			height: 100%;
			background-position: center center;
			background-repeat: no-repeat;
			background-size: contain;
			border-radius: 10upx;
		}

		.carousel_img {
			width: 100%;
			height: 100%;
			background-position: center top;
			background-repeat: no-repeat;
			background-size: contain;
		}
	}

	.swiper-dots {
		display: flex;
		position: absolute;
		left: 60upx;
		bottom: 15upx;
		width: 72upx;
		height: 36upx;
		background-image: url(data:image/png;base64,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);
		background-size: 100% 100%;

		.num {
			width: 36upx;
			height: 36upx;
			border-radius: 50px;
			font-size: 24upx;
			color: #fff;
			text-align: center;
			line-height: 36upx;
		}

		.sign {
			position: absolute;
			top: 0;
			left: 50%;
			line-height: 36upx;
			font-size: 12upx;
			color: #fff;
			transform: translateX(-50%);
		}
	}

	/* 分类 */
	.cate-section {
		display: flex;
		justify-content: space-around;
		align-items: center;
		// flex-wrap: wrap;
		padding: 20rpx 0;
		background: #fff;

		.cate-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			font-size: 26upx;
			color: #303133;
			flex: 1;

			image {
				overflow: visible;
				margin-bottom: 10rpx;
			}

			text {
				white-space: nowrap;
				/* #ifdef MP-BAIDU */
				font-size: 24rpx;
				/* #endif */
			}
		}

		.cate-item2 {
			display: flex;
			align-items: center;
			font-size: 24upx;
			color: #303133;
		}

		.cate_name {
			// width: 78rpx;
		}

		/* 原图标颜色太深,不想改图了,所以加了透明度 */
		image {
			width: 88upx;
			height: 88upx;
			// margin-bottom: 14upx;
			border-radius: 50%;
		}
	}

	.ad-1 {
		width: 100%;
		height: 210upx;
		padding: 10upx 0;
		background: #fff;

		image {
			width: 100%;
			height: 100%;
		}
	}

	.notice_box {
		margin: 20rpx 20rpx 0 20rpx;
		background: #FFFFFF;
		border-radius: 14rpx;

		// margin-bottom:20rpx;
		.notice_wrap1 {
			width: 100%;
			height: 80rpx;
			margin-top: 20rpx;
			background-color: #fff;
			display: flex;
			align-items: center;
			border-radius: 14rpx;

			// border-radius: 6px;
			.notice_img1 {
				width: 127rpx;
				height: 80rpx;
				border-radius: 6px 0 0 6px;
			}

			.notice_content_wrap {
				font-size: 28rpx;
				font-weight: 600;
				width: 530rpx;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
				margin-left: -16rpx;

				.notice_content_title {
					color: #E1261C;
				}

				.notice_content {
					color: #666666;
				}
			}

			.notice_wrap1_line {
				width: 1rpx;
				height: 34rpx;
				background-color: rgba(0, 0, 0, 0.1);
				margin-left: 6rpx;
			}

			.notice_more {
				width: 80rpx;
				text-align: center;
				font-size: 29rpx;
				color: #2E2E2E;
				font-weight: 600;
			}
		}

		.notice_wrap2 {
			width: 100%;
			height: 80rpx;
			// margin-bottom: 20rpx;
			background-color: #3A3A3A;
			display: flex;
			align-items: center;
			box-shadow: 1px 6px 19px 1px rgba(86, 86, 86, 0.1);

			// border-radius: 6px;
			.notice_img2 {
				width: 138rpx;
				height: 80rpx;
				border-radius: 6px 0 0 6px;
			}

			.notice_content_wrap2 {
				font-size: 26rpx;
				font-weight: 600;
				width: 510rpx;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
				color: #fff;
				margin-left: 5rpx;
			}

			.notice_wrap2_line {
				width: 1rpx;
				height: 38rpx;
				background-color: #fff;
				margin-left: 2rpx;
			}

			.notice_more {
				width: 80rpx;
				text-align: center;
				font-size: 26rpx;
				color: #fff;
				font-weight: 600;
			}
		}
	}
	
	.store-list{
		font-size: 24rpx;
		min-height: 600rpx;
		padding: 40rpx 34rpx;
		padding-right: 14rpx;
		background-color: #fff;
		.store-box{
			padding: 24rpx 14rpx 24rpx 0;
			margin-top: 14rpx;
			.store-box-goods{
				padding-left: 132rpx;
				margin-top: 12rpx;
				display: flex;
				justify-content: space-between;
				.store-box-gitem{
					font-size: 20rpx;
					margin-right: 28rpx;
					image{
						width: 130rpx;
						height: 130rpx;
						border-radius: 16rpx;
						margin-bottom: 14rpx;
					}
				}
				.store-box-dotted{
					width: 100rpx;
					height: 130rpx;
					flex-shrink: 0;
					image{
						width: 32rpx;
						height: 50rpx;
					}
				}
			}
			.store-box-con{
				display: flex;
				color: #333333;
				.sbc-item1{
					width: 114rpx;
					height: 114rpx;
					border-radius: 16rpx;
				}
				.sbc-item2{
					flex: 1;
					margin-left: 14rpx;
					.sbc-item2-tit{
						font-size: 32rpx;
					}
					.sbc-item2-star{
						margin-top: 26rpx;
						font-size: 24rpx;
						color: #FF7E28;
					}
				}
				.sbc-item3{
					font-size: 24rpx;
					.sbc-item3-km{
						line-height: 42rpx;
						height: 42rpx;
					}
					.sbc-item3-ticket{
						margin-top: 36rpx;
						width: 86rpx;
						height: 32rpx;
						line-height: 32rpx;
						border-radius: 30rpx;
						background-color: #FED4CF;
						color: #F81C05;
						text-align: center;
					}
				}
			}
		}
		.search-box{
			display: flex;
			position: relative;
			// border: 2rpx solid grey;
			.search-con-item{
				height: 52rpx;
				display: flex;
				align-items: center;
				margin-bottom: 18rpx;
			}
			.search-con1{
				position: absolute;
				width: 216rpx;
				padding: 20rpx;
				top: 52rpx;
				left: 0;
				z-index: 999;
				background-color: #fff;
			}
			.search-con2{
				position: absolute;
				width: 216rpx;
				padding: 20rpx;
				top: 52rpx;
				left: 216rpx;
				z-index: 999;
				background-color: #fff;
			}
			.search-item{
				width: 216rpx;
				height: 52rpx;
				padding: 0 10rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				image{
					transform:rotate(90deg);
					width: 24rpx;
					height: 24rpx;
				}
			}
		}
	}
	
	.service_wrap {
		// width:100%;
		padding: 20rpx;
		font-size: 26rpx;
		color: #333;
		background-color: #fff;
		display: flex;
		align-items: center;
		margin: 20rpx 20rpx 0 20rpx;
		border-radius: 14rpx;

		image {
			width: 30rpx;
			height: 32rpx;
			margin-right: 10rpx;
		}
	}

	.rich_text_wrap {
		color: #333;
		font-size: 28rpx;
		box-sizing: border-box;
		background: #fff;
		margin-top: 20rpx;
		/* #ifdef H5 */
		padding: 0 8rpx;
		/* #endif */
		/* #ifndef H5 */
		padding: 30rpx;
		/* #endif */
	}

	.match_wrap {
		// padding: 20rpx;
		box-sizing: border-box;
		margin-top: 20rpx;
		flex-direction: column;
		background-color: #fff;

		.match_top {
			display: flex;
			flex-direction: column;
			background-color: #fff;

			image {
				width: 100%;
			}

			.match_image_wrap {
				width: 100%;
				display: flex;
				justify-content: center;

				.match_image {
					margin: 0 auto;
					background-position: center center;
					background-repeat: no-repeat;
					background-size: cover;
				}
			}

			.match_top_title {
				text-align: center;
				padding-bottom: 20rpx;
				font-size: 32rpx;
				color: #333;
			}

			.match_top_text {
				padding: 20rpx 20rpx 0;
				font-size: 28rpx;
				color: #333;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
			}
		}

		.match_main_wrap {
			background-color: #fff;
			margin-top: 20rpx;
		}

		.match_main {
			display: flex;
			justify-content: space-between;
			overflow-x: scroll;
			box-sizing: border-box;
			// padding: 0 20rpx;
			width: 710rpx;
			margin: 0 auto;

			.match_item {
				width: 222rpx;
				height: 370rpx;
				margin-right: 20rpx;
				background-color: #fff;
				border-radius: 15rpx;
				position: relative;

				.match_goods_img {
					width: 222rpx;
					height: 222rpx;
					background-color: #ccc;
					border-radius: 15rpx 15rpx 0 0;

					.image {
						background-position: center center;
						background-repeat: no-repeat;
						background-size: cover;
						width: 222rpx;
						height: 222rpx;
						border-radius: 10rpx 10rpx 0 0;
					}
				}

				.match_goods_name {
					font-size: 28rpx;
					color: #333;
					text-overflow: -o-ellipsis-lastline;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					line-clamp: 2;
					-webkit-box-orient: vertical;
					box-sizing: border-box;
					padding: 10rpx 0rpx 0 0rpx;
				}

				.match_goods_price {
					font-size: 28rpx;
					color: rgb(255, 43, 32);
					font-weight: 600;
					position: absolute;
					bottom: 12rpx;
					// left: 20rpx;
				}
			}
		}
	}

	.subline_wrap {
		padding: 30rpx 0;
		background-color: #fff;

		// margin: 20rpx 20rpx 0 20rpx;
		.subline {
			width: 100%;
			border-bottom: 1px dotted #fff;
		}
	}

	.carousel_bottom_wrap {
		padding: 20rpx 0 !important;
		background-color: #fff;
		display: flex;
		justify-content: center;
		margin-top: 20rpx;

		.carousel_bottom {
			width: 100%;
			// margin-top: 20rpx;
			// margin-bottom: 20rpx;
			// padding-top: 20rpx;
		}
	}

	.recommend_goods_wrap {
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;
		padding: 0 20rpx;
		margin-top: 20rpx;
	}

	.carousel_img {
		// height:340rpx !important;
	}

	.blank_wrap {
		margin: 0 20rpx;
		margin-top: 20rpx;
	}

	.nav_wrap {
		width: 100%;
		// padding: 0 20rpx;
		box-sizing: border-box;
		background-color:#fff ;
	}

	.nav_wrap::-webkit-scrollbar {
		display: none;
	}

	.sort_sub_wrap {
		width: 100%;
		background: #F5F5F5;

		.sort_sub_top {
			margin: 180rpx 21rpx 0 21rpx;
			//app-7-start
			/* #ifdef APP-PLUS */
			margin-top: calc(var(--status-bar-height) + 180rpx);
			/* #endif*/
			//app-7-end
			/* #ifdef MP */
			margin-top: calc(50px + 80rpx);
			/* #endif*/
			display: flex;
			border-radius: 10rpx;
			background-color: #fff;
			padding: 0 20rpx 20rpx 20rpx;
			flex-wrap: wrap;

			.sort_sub_item {
				display: flex;
				flex-direction: column;
				margin-right: 35rpx;
				justify-content: center;
				align-items: center;
				margin-top: 20rpx;

				.sort_sub_img {
					width: 106rpx;
					height: 106rpx;
					border-radius: 50%;
					margin-bottom: 20rpx;

					image {
						width: 106rpx;
						height: 106rpx;
						border-radius: 50%;
					}
				}

				.sort_sub_name {
					font-size: 24rpx;
					color: #333;
					font-weight: 600;
					width: 96rpx;
					white-space: nowrap;
					text-overflow: ellipsis;
					overflow: hidden;
					word-break: break-all;
					text-align: center;
				}
			}
		}

		.sort_sub_top>view:nth-child(5n) {
			margin-right: 0 !important;
		}

		.sort_sub_goods {
			width: 100%;
			padding: 0 20rpx;
			box-sizing: border-box;
			display: flex;
			justify-content: space-between;
			flex-wrap: wrap;
			margin-top: 20rpx;
		}

		.sort_sub_goods>view:nth-child(2n) {
			margin-right: 0 !important;
		}
	}

	// 图片组合样式
	.goods {
		border: none;
		background: #fff;
		margin-top: 0;
	}

	.index_block {
		width: 750rpx;
		clear: both;
		overflow: hidden;
		background: #fff;
		display: block;
	}

	.goods .content {
		background: #f0f2f5;
		clear: both;
		overflow: hidden;
		display: block;
	}

	.goods .goods-small.goods-item:nth-child(2n+1) {
		padding-right: 8rpx;
	}

	.goods-small.goods-item {
		overflow: hidden;
		float: left;
		width: 50%;
		box-sizing: border-box;
		padding-bottom: 8rpx;
		position: relative;
		-moz-box-sizing: border-box;
		-webkit-box-sizing: border-box;
	}

	.goods-item navigator {
		display: block;
		background: #fff;
	}

	.goods-item-pic {
		vertical-align: middle;
		line-height: 0;
		display: table-cell;
		text-align: center;
		width: calc(50vw - 30rpx);
		height: calc(50vw - 30rpx);
	}

	.goods-item-pic image {
		width: calc(50vw - 30rpx);
		height: calc(50vw - 30rpx);
	}

	.goods-small .goods-item-name {
		height: 66rpx;
		font-size: 26rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		word-break: break-word;
		color: #232326;
		margin-top: 10rpx;
		line-height: 33rpx;
		margin-bottom: 6rpx;
		padding: 0 8rpx;
	}

	.goods-item-price {
		color: #f23030;
		display: inline-block;
		padding: 0 10rpx 0 8rpx;
		position: relative;
		top: 2rpx;
		height: 50rpx;
		line-height: 50rpx;
	}

	.goods-item-price .yens {
		font-size: 26rpx;
	}

	.goods-item-price .bigprice {
		font-size: 32rpx;
		font-weight: bold;
		display: inline-block;
	}

	.goods-big.goods-item {
		overflow: hidden;
		float: left;
		width: 100%;
		box-sizing: border-box;
		padding-bottom: 8rpx;
		position: relative;
		-moz-box-sizing: border-box;
		-webkit-box-sizing: border-box;
		padding: 16rpx 16rpx 0;
	}

	.goods-item-name {
		height: 66rpx;
		font-size: 26rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		word-break: break-word;
		color: #232326;
		margin-top: 10rpx;
		line-height: 34rpx;
		margin-bottom: 6rpx;
		padding: 0 8rpx;
	}

	.goods-big .goods-item-price {
		color: #f23030;
		display: inline-block;
		padding: 0 10rpx 0 8rpx;
		position: relative;
		top: 2rpx;
		height: 50rpx;
		line-height: 50rpx;
	}

	.goods-big .goods-item-pic image {
		width: 734rpx;
		height: 734rpx;
		padding: 0 8rpx;
	}

	.hide_title .goods-item .goods-item-name {
		display: none !important;
	}

	.hide_price .goods-item .goods-item-price {
		display: none !important;
	}

	.goods-list.goods-item .goods-item-pic {
		float: left;
		width: 214rpx !important;
		height: 214rpx !important;
	}

	.goods-list.goods-item {
		overflow: hidden;
		float: left;
		width: 100%;
		box-sizing: border-box;
		padding-bottom: 8rpx;
		position: relative;
		-moz-box-sizing: border-box;
		-webkit-box-sizing: border-box;
		background: #fff;
		margin-bottom: 16rpx;
	}

	.goods-list.goods-item .goods-item-pic image {
		width: 214rpx !important;
		height: 214rpx !important;
	}

	.goods-list .goods-item-name {
		padding-top: 40rpx;
	}

	.goods .new-content .goods-item.goods-list .goods-item-name {
		padding-top: 10rpx;
	}



	/*图片组合样式  */

	.modules-slide {
		display: block;
	}

	.modules-slide .image-list.style0,
	.modules-slide .image-list.style0 ul {
		padding: 0;
		margin: 0;
		box-sizing: border-box;
	}

	.modules-slide .image-list.style0,
	.modules-slide .image-list.style0 ul {
		padding: 0;
		margin: 0;
		box-sizing: border-box;
		display: block;
	}

	.modules-slide .image-list ul {
		overflow: hidden;
		box-sizing: border-box;
		display: block;
	}

	.modules-slide .image-list.style0 ul li {
		display: block;
		box-sizing: border-box;
	}

	.modules-slide .image-list ul li navigator {
		display: block;
	}

	.modules-slide .image-list ul li navigator image {
		width: 100%;
		background: #f1f1f1;
		display: block !important;
	}

	.modules-slide image {
		max-width: 100%;
		height: auto;
		vertical-align: middle;
		display: inline-block !important;
	}

	.modules-slide .image-list {
		overflow: hidden;

		.tupianzuhe2 {
			margin-bottom: 20rpx;
			flex-wrap: wrap;
		}
	}

	.modules-slide .image-list.style1 ul li {
		display: block;
		margin: 0 16rpx 16rpx;
	}

	.modules-slide .image-list.style1 ul li image {
		height: 100%;
	}

	.modules-slide .image-list ul li navigator image {
		width: 100%;
		background: #f1f1f1;
		display: block !important;
	}

	.modules-slide .image-list.style2 ul,
	.modules-slide .image-list.style3 ul {
		padding-right: 16rpx;
	}

	.modules-slide .image-list.style2 ul li {
		box-sizing: border-box;
		padding: 0 0 16rpx 16rpx;
		width: 50%;
		float: left;
	}

	.modules-slide .image-list.style3 ul li {
		float: left;
		width: 33.33333%;
		box-sizing: border-box;
		padding: 0 0 16rpx 16rpx;
	}

	.modules-slide .image-ad {
		padding: 20rpx 0;
		box-sizing: border-box;
		display: block;
		clear: both;
		overflow: hidden;

		.tupianzuhe04_left {
			width: calc((750rpx - 60rpx)/2);
			height: calc((750rpx - 60rpx)/2 + 20rpx);
			margin-right: 20rpx;
			margin-left: 20rpx;

			image {
				width: 100%;
				height: 100%
			}
		}

		.tupianzuhe04_right_item {
			width: calc((750rpx - 60rpx)/2);
			height: calc((750rpx - 60rpx)/4);

			image {
				width: 100%;
				height: 100%
			}
		}
	}

	.modules-slide .image-ad>div {
		float: left;
		width: 50%;
		box-sizing: border-box;
	}

	.modules-slide .image-ad div navigator {
		display: block;
		margin: 0 16rpx 16rpx 0;
		box-sizing: border-box;
	}

	.modules-slide .images-tpl image {
		width: 374rpx;
		vertical-align: middle;
		box-sizing: border-box;
	}

	.modules-slide .image-ad2 {
		margin: 0 16rpx 0 0;
		box-sizing: border-box;
		display: block;
		clear: both;
		overflow: hidden;
	}

	.modules-slide .image-ad2 .clearfix {
		display: block;
		clear: both;
		overflow: hidden;
	}

	.modules-slide .image-ad2 div:first-child navigator:nth-child(1),
	.modules-slide .image-ad2 div:first-child navigator:nth-child(1) image {
		width: 228rpx;
	}

	.modules-slide .image-ad2 div:first-child .big:nth-child(2),
	.modules-slide .image-ad2 div:first-child .big:nth-child(2) image {
		width: 473rpx;
	}

	.modules-slide .image-ad2 div navigator {
		display: block;
		float: left;
		margin: 0 0 16rpx 16rpx;
		box-sizing: border-box;
	}

	.modules-slide .images-tpl img {
		width: 100%;
		vertical-align: middle;
		box-sizing: border-box;
	}

	.modules-slide .image-ad2 div:last-child navigator:nth-child(1),
	.modules-slide .image-ad2 div:last-child navigator:nth-child(1) image {
		width: 473rpx;
	}

	.modules-slide .image-ad2 div:last-child navigator:nth-child(2),
	.modules-slide .image-ad2 div:last-child navigator:nth-child(2) image {
		width: 228rpx;
	}

	.modules-slide .image-ad3 {
		padding: 0 0 0 16rpx;
		box-sizing: border-box;
		display: block;
		clear: both;
		overflow: hidden;
	}

	.modules-slide .image-ad3 div {
		width: 367rpx;
		float: left;
		box-sizing: border-box;
	}

	.modules-slide .image-ad3 div image {
		width: 351rpx;
	}

	.modules-slide .image-ad3 div navigator {
		padding: 0 16rpx 16rpx 0;
		display: inline-block;
		box-sizing: border-box;
	}

	.modules-slide .image-ad4 {
		padding: 0 16rpx 16rpx 0;
		box-sizing: border-box;
		display: block;
	}

	.modules-slide .image-ad4 div {
		float: left;
		width: 33.33333%;
		box-sizing: border-box;
	}

	.modules-slide .image-ad4 div navigator {
		display: block;
		margin: 0 0 16rpx 16rpx;
	}

	.countdown {
		width: 100%;
		height: 49.2rpx;
		line-height: 49.2rpx;
		font-size: 39.4rpx;
	}

	.countdown .countdown-name {
		float: left;
		display: block;
		-webkit-transform: scale(0.8);
	}

	.countdown .countdown-main {
		display: block;
		-webkit-transform: scale(0.8);
	}

	.countdown .countdown-num {
		background-color: #000000;
		display: inline-block;
		padding: 0 0rpx;
		width: 25px;
		height: 32.2rpx;
		line-height: 32.2rpx;
	}

	.combine1 {
		display: flex !important;
		flex-direction: column !important;
		text-align: center;
	}

	.combine2 {
		display: flex !important;
	}

	// 推荐商品
	.recommend_goods1 {
		width: 100%;
		height: 350rpx;
		display: flex;
		background-color: #fff;
		border-radius: 20rpx;
		margin-bottom: 20rpx;
		border-radius: 20rpx;

		.recommend_goods_img1 {
			width: 350rpx;
			height: 350rpx;
			margin-right: 20rpx;

			.image {
				width: 350rpx;
				height: 350rpx;
				background-color: #ccc;
				background-position: center center;
				background-size: cover;
				background-repeat: no-repeat;
			}
		}

		.recommend_goods_right {
			width: 100%;
			height: 350rpx;
			position: relative;

			.recommend_goods_name {
				padding-right: 20rpx;
				font-size: 30rpx;
				margin-top: 20rpx;
				text-overflow: -o-ellipsis-lastline;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				line-clamp: 2;
				-webkit-box-orient: vertical;
			}

			.recommend_goods_price {
				position: absolute;
				bottom: 80rpx;
				left: 0;
				color: var(--color_price);
			}

			.recommend_goods_bottom {
				width: 100%;
				position: absolute;
				bottom: 20rpx;
				left: 0;
				display: flex;
				justify-content: space-between;

				image {
					width: 42rpx;
					height: 42rpx;
					margin-right: 20rpx;
				}
			}
		}
	}

	.recommend_goods2 {
		display: flex;
		flex-direction: column;
		width: 100%;

		.recommend_goods_img2 {
			width: 100%;
			height: 702rpx;

			image {
				width: 100%;
				height: 702rpx;
				background-color: #ccc;
			}
		}

		.recommend_goods_bottom2 {
			width: 100%;
			height: 204rpx;
			padding: 20rpx;
			box-sizing: border-box;
			background-color: #fff;
			position: relative;

			.recommend_goods_name2 {
				font-size: 30rpx;
				text-overflow: -o-ellipsis-lastline;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				line-clamp: 2;
				-webkit-box-orient: vertical;
			}

			.goods_bottom {
				width: 100%;
				display: flex;
				flex-direction: column;

				image {
					width: 42rpx;
					height: 42rpx;
				}
			}
		}
	}

	.big_price {
		font-size: 34rpx;
	}

	.small_price {
		font-size: 24rpx;
	}

	.combination_style {
		display: flex;
	}

	.combination_wrap {
		width: 100%;
		background-color: #fff;
		box-sizing: border-box;
		/* #ifndef MP-WEIXIN||APP-PLUS */
		// margin-top: 20rpx;
		/* #endif */

	}

	.combine6 {
		display: flex;
		flex-direction: column;
		margin-right: 20rpx;
		margin-top: 20rpx;
	}

	.scan_img {
		width: 44rpx;
		height: 44rpx;
		position: absolute;
		left: 15rpx;
	}

	.scan_login {
		position: absolute;
		right: 110rpx;
		top: 50%;
		margin-top: -25rpx;
		z-index: 9;
		width: 48rpx;
		height: 48rpx;
		color: #666666;
		font-size: 48rpx;
	}



	

	.combine3 {
		width: calc((750rpx - 60rpx)/2);
		margin-top: 20rpx;
	}

	.combine4 {
		width: calc((750rpx - 80rpx)/3);
		margin-top: 20rpx;
		margin-left: 20rpx;

		image {
			width: 100%;
			height: 100%;
		}
	}

	.paddingTB20 {
		padding: 0 20rpx;
	}

	.no_margin_right {
		padding: 0px
	}

	.no_margin_right>view:nth-child(2n) {
		margin-right: 0 !important;
	}

	.no_margin_right2>view:nth-child(3n) {
		margin-right: 0 !important;
	}

	.see_more_wrap {
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		margin-top: 14rpx;

		.more_icon_circle {
			width: 106rpx;
			height: 106rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			border-radius: 50%;
			background-color: #F8F8F8;
			margin-bottom: 20rpx;

			.more_icon {
				width: 52rpx;
				height: 14rpx;
			}
		}

		.see_more_text {
			font-size: 24rpx;
			color: #333;
			font-weight: 600;
		}
	}

	.carousel-section ::v-deep .uni-swiper__warp {
		// margin-top: 158rpx !important;
	}
	//app-8-start
	/* #ifdef APP-PLUS */
	.carousel-section ::v-deep .uni-swiper__warp {
		padding-top: 14rpx;
		margin-top: -2rpx !important;
	}
	/* #endif */
	//app-8-end
	/* #ifdef H5 */
	.carousel-section ::v-deep .uni-swiper__warp {
		margin-top: -2rpx !important;
		padding-top: 8rpx !important;
	}

	/* #endif */
	.combine5_wrap>view:nth-child(2n) {
		margin-right: 0 !important;
	}

	.mp-search-box ::v-deep .ser-input ::v-deep .uni-input-wrapper ::v-deep .uni-input-input {
		background-color: #fff;
	}

	.search_input {
		text-align: left;
	}

	.rec_goods_wrap {
		width: 750rpx;
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;
	}

	.city_wrap {
		font-size: 30rpx;
		display: flex;
		align-items: center;
		color: #fff;
		flex-shrink: 0;
		max-width: 58px;
		white-space: nowrap;
		text-overflow: ellipsis;
		overflow: hidden;
		display: inline-block;
	}

	.top_icon {
		width: 24rpx;
		height: 24rpx;
		margin-left: 11rpx;
		margin-right: 17rpx;
		margin-top: 2rpx;
		flex-shrink: 0;
	}

	.deco_wrap {
		width: 750rpx;
	}


	/* ----TAB切换：---- */
	.tab_nav {
		margin-top: 100rpx;
	}

	.tab_nav_scroll {
		white-space: nowrap;
	}

	.tab_nav_block {
		display: inline-block;
		width: 25%;
		text-align: center;
	}

	.tab_nav_block_t {
		color: #2D2D2D;
		font-size: 28rpx;
		padding-bottom: 14rpx;
		margin: 0 50rpx 4rpx 50rpx
	}

	.tab_nav_block_on {
		color: #333333;
		font-weight: 700;
		font-size: 32rpx;
		margin-bottom: 8rpx;
	}

	.tab_nav_block_i {
		color: #999999;
		font-size: 22rpx;

		padding: 4rpx 0;
		margin: 5rpx 26rpx
	}

	.tab_nav_block_on2 {
		background-color: #FA1C1C;
		color: #FFFFFF;
		border-radius: 16rpx;
	}

	// 短视频3d轮播
	.swiper-block {
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		height: 346rpx !important;
		margin: 0
	}

	.optionBox {
		position: relative;
	}

	.slide-image {
		height: 320rpx;
		width: 520rpx;
		border-radius: 9rpx;
		box-shadow: 0px 0px 30rpx rgba(0, 0, 0, .2);
		margin: 0rpx 30rpx;
		z-index: 1;
	}

	.active1 {
		/* transform: scale(1.44); */
		background-position: center center;
		background-repeat: no-repeat;
		background-size: cover;
		transition: all .2s ease-in 0s;
		height: 100%;
		width: 100%;
		border-radius: 16rpx;
		animation: swiperMove1 .6s ease-in-out;
	}

	@keyframes swiperMove {
		from {
			height: 280rpx;
		}

		to {
			height: 345rpx;
		}
	}

	.active2 {
		background-position: center center;
		background-repeat: no-repeat;
		background-size: cover;
		width: 100%;
		height: 100%;
		border-radius: 16rpx;
		animation: swiperMove0 .6s ease-in-out
	}

	@keyframes swiperMove {
		from {
			height: 345rpx;
		}

		to {
			height: 280rpx;
		}
	}

	// 空页面
	.empty_sort_page {
		width: 100%;
		// height: 100vh;
		background: #F5F5F5;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-top: 340rpx;

		.empty_img {
			width: 380rpx;
			height: 280rpx;
			margin-bottom: 32rpx;
		}

		.empty_text {
			font-size: 26rpx;
			color: #999;
		}
	}

	.scan,
	.message {
		margin-left: 18rpx;
		display: flex;
		align-items: center;
		flex-direction: column;
		flex-shrink: 0;

		image {
			width: 34rpx;
			height: 34rpx;
		}

		text {
			color: white;
			font-size: 20rpx;
			line-height: 22rpx;
			margin-top: 6rpx;
		}
	}

	.search_con {
		position: relative;
		flex: 1;
	}

	.svideo_person_num {
		width: 40rpx;
		height: 40rpx;
		border-radius: 0 0 50% 0;
	}

	/* #ifndef MP-ALIPAY */
	.swiper-item {
		color: #fff;
		overflow: hidden;
		left: 40rpx !important;
		right: 20rpx;
		top: 20rpx;
		bottom: 20rpx;
		width: 80% !important;
		height: 280rpx !important;
		// transform: translate(80%, 0px) translateZ(0px);
	}

	.swiper-item1 {
		color: #fff;
		box-sizing: border-box;
		height: 346rpx;
		width: 346rpx !important;
		top: 0rpx;
		bottom: 0rpx;
		overflow: hidden;
		left: 80rpx;
	}
	/* #endif */


	/* #ifdef MP-ALIPAY */
	.swiper-item {
		padding-top: 40rpx;
	}

	.swiper-item1 {}
	/* #endif */

	// 专题页头部
	.topic_top_bar {
		width: 750rpx;
		height: 60rpx;
		display: flex;
		// align-items: center;
		justify-content: flex-start;
		font-size: 30rpx;
		color: #2d2d2d;
		z-index: 99999;
		margin: 0 auto;
		background: linear-gradient(90deg, #6984a4 0%, #3a4b5c 100%);
    .topic_top_bar_bix{
      display: flex;
      align-items: center;
      justify-content: flex-start;
	  padding-left: 24rpx;
    }
		image {
			width: 32rpx;
			height: 32rpx;
			margin-right: 8rpx;
		}
		
		.topic_name{
			color: #fff;
			font-size: 35rpx;
			line-height: 35rpx;
		}
	}

	

	.svideo2_wrap ::v-deep .uni-scroll-view ::v-deep .uni-scroll-view ::v-deep .uni-scroll-view-content>view:nth-last-child(1) {
		margin-right: 0 !important;
	}

	.top_swiper_style1 {
		background: #FF1D1D;
	}

	.top_swiper_style2 {
		background: linear-gradient(#FC1D1C 0%, #FF7A18 42%, #fff 100%);
	}

	.svideo4_wrap ::v-deep .uni-scroll-view ::v-deep .uni-scroll-view ::v-deep .uni-scroll-view-content>view:nth-last-child(1) {
		margin-right: 0 !important;
	}

	.recommond_goods3_wrap {
		display: flex;
		justify-content: space-between;
	}

	.have_sold {
		font-size: 24rpx;
		color: #9a9a9a;
	}

	.topic_back_icon {
		width: 60rpx;
		height: 38rpx;
		margin-left: 20rpx;
	}

	.hide_sold_wrap {
		width: 100%;
		position: absolute;
		bottom: 20rpx;
		left: 0;
		display: flex;
		justify-content: space-between;
	}

	// 导航分组
	.nav_group {
		// padding: 0 86rpx;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;

		.nav_group_item {
			width: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			padding-top: 40rpx;
			margin-bottom: 40rpx;

			image {
				width: 90rpx;
				height: 90rpx;
				border-radius: 50%;
				margin-right: 20rpx;
			}

			.nav_group_name {
				font-size: 26rpx;
				color: #333;
			}
		}

		.nav_group_item:nth-last-child(1) {
			margin-right: 0;
		}
	}

	.nav_group>view:nth-child(2n) {
		margin-right: 0;
	}

	// 导航分组end
	.rec_goods_wrap>view:nth-child(2n) {
		margin-right: 0 !important;
	}

	.carousel-section ::v-deep .uni-swiper__warp ::v-deep .uni-swiper__dots-box ::v-deep .uni-swiper__dots-item {
		width: 16rpx !important;
	}

	.deco_wrap_no_top_cat {
		/* #ifndef MP */
			margin-top: 100rpx !important;
		/* #endif */
		//app-9-start
		/* #ifdef APP-PLUS */
		margin-top: calc(var(--status-bar-height) + 100rpx) !important;
		/* #endif */
		//app-9-end
	}

	.rich_text ::v-deep div .ql-align-center {
		text-align: center;
	}

	.rich_text ::v-deep div .ql-align-right {
		text-align: right;
	}

	.rich_text ::v-deep div .ql-align-left {
		text-align: left;
	}

	/* #ifdef MP */
	.rich_text {
		text-align: center;
	}
	/* #endif */

	/* 短视频模块 */
	/* 方案1 */
	.svideo {
		background-color: #FFFFFF;
		margin-top: 20rpx;
		// width: 100%;
		box-sizing: border-box;
	}

	.svideo_title {
		width: 100%;
		font-family: PingFang SC;
		color: #2D2D2D;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 20rpx 20rpx 20rpx;
		box-sizing: border-box;
	}

	.svideo_title_l {
		font-size: 32rpx;
		font-weight: bold;
	}

	.svideo_title_r {
		font-size: 26rpx;
		color: #666666;
		font-weight: 600;
	}

	.svideo_main {
		width: 100%;
		padding: 0 20rpx 20rpx;
		box-sizing: border-box;
		color: #FFFFFF;
		display: flex;
		justify-content: space-between;
	}

	.svideo_main_block {
		border-radius: 16rpx;
		overflow: hidden;
		background-color: #3a4db8;
		position: relative;
		height: 345rpx;
		width: 345rpx;
	}

	.svideo_main_block_l {
		margin-left: 0;
	}

	.svideo_main_block_r {
		margin-right: 0;
	}

	.svideo_main_block_t {
		margin: 10rpx 0 0 10rpx;
		height: 36rpx;
		line-height: 36rpx;
		font-size: 20rpx;
		background: rgba(0, 0, 0, 0.2);
		padding-right: 10rpx;
		border-radius: 16rpx;
		position: relative;
		display: inline-block;
		display: flex;
		align-items: center;
	}

	.svideo_main_block_lt {
		height: 100%;
		font-size: 18rpx;
		// background: url("http://site7.55jimu.com/data/upload/mall/store/goods/301/301_06506538074418754.jpg");
		background-color: rgba(0, 0, 0, 0.2);
		padding: 3rpx 10rpx 3rpx 6rpx;
		border-radius: 0 0 16rpx 0;
		position: relative;
		display: flex;
		align-items: center;
	}

	.svideo_main_block_t0 {
		margin: 0 16rpx 16rpx 0;
		border-radius: 0rpx 0rpx 20rpx 0;
		padding: 5rpx 15rpx 5rpx 0rpx;
	}

	.svideo_block_t_img {
		// position: absolute;
		// left: 0;
		// top: 3rpx;
		width: 36rpx;
		height: 36rpx;
	}

	.svideo_block_t_img0 {
		width: 24rpx;
		height: 24rpx;
		// margin-top: 4rpx;
		margin-left: 6rpx;
		margin-right: 7rpx;
	}

	.svideo_block_t_img1 {
		width: 22rpx;
		height: 22rpx;
		// margin-top: 6rpx;
	}

	.svideo_block_t_img2 {
		// margin-top: -4rpx;

	}

	.svideo_block_t_img2zb {
		margin-top: -1rpx;
		margin-left: 4rpx;
	}

	.svideo_block_t_img24 {
		margin-top: 1rpx;
	}

	.svideo_main_block_b {
		font-size: 26rpx;
		white-space: nowrap;
		text-overflow: ellipsis;
		overflow: hidden;
		word-break: break-all;
		position: absolute;
		bottom: 20rpx;
		padding: 0 20rpx;
		width: 100%
	}

	.svideo_block_t_text {
		// padding-left: 35rpx;
	}

	.svideo_block_t_text2 {
		padding-left: 6rpx;
		font-size: 22rpx;
	}

	/* 方案2 */
	.svideo_main2 {
		color: #FFFFFF;
		flex-direction: row;
		overflow: auto;
		flex-wrap: nowrap;
		width: auto;
		box-sizing: border-box;
		padding: 0 20rpx;
	}

	.svideo_main_block2 {
		margin: 0 20rpx 10rpx 20rpx;
		border-radius: 16rpx;
		overflow: hidden;
		background-color: #3a4db8;
		position: relative;
		height: 224rpx;
		width: 224rpx;
		display: inline-block;
	}

	.svideo_main2_scroll {
		min-width: 100%;
	}

	.svideo_main_block_c2 {
		margin: 20rpx 0;
	}

	.svideo_main_block_l2 {
		margin-left: 0;
	}

	.svideo_main_block_b2 {
		font-size: 24rpx;
		white-space: nowrap;
		text-overflow: ellipsis;
		overflow: hidden;
		word-break: break-all;
		position: absolute;
		bottom: 0rpx;
		box-sizing: border-box;
		padding-left: 10rpx;
		padding-right: 8rpx;
		width: 100%;
		height: 40rpx;
		line-height: 40rpx;
		background-color: rgba(0, 0, 0, 0.3);
	}

	.svideo_main_block_b2_zb text {
		width: 85%;
		white-space: nowrap;
		text-overflow: ellipsis;
		overflow: hidden;
		word-break: break-all;
		display: inline-block;
	}

	/* 方案三 */
	.svideo_main3 {
		color: #FFFFFF;

		flex-direction: row;
		overflow: hidden;

		width: auto;
	}

	.svideo_main_block3 {
		margin: 20rpx;
		border-radius: 16rpx;
		overflow: hidden;
		background-color: #3a4db8;
		position: relative;
		height: 345rpx;
		width: 345rpx;
		display: inline-block;
	}

	.svideo_main_block_l3 {
		width: 280rpx;
		height: 280rpx;
	}

	.svideo_main3_scroll {
		width: 180vw;
		vertical-align: middle;
		display: flex;
		align-items: center;
		margin-left: -140rpx;
	}

	.svideo_main_block_c3 {
		margin-left: 0;
		margin-right: 0;
	}

	.svideo_main_block_r3 {
		width: 280rpx;
		height: 280rpx;
	}

	.svideo_main_block_b3 {
		font-size: 26rpx;
		background: rgba(1, 1, 1, 0.2);
		padding: 0 24rpx;
		color: #FFFFFF;
		border-radius: 24rpx;
		height: 100%;
	}

	.svideo_main_block_b3_w {
		display: flex;
		justify-content: center;
		align-items: center;
		position: absolute;
		left: 0;
		bottom: 20rpx;
		right: 0;
		height: 48rpx;
		line-height: 48rpx;
	}

	/* 方案4  */
	.svideo_main4 {
		color: #FFFFFF;
		flex-direction: row;
		overflow: auto;
		flex-wrap: nowrap;
		width: auto;
		padding: 0 20rpx;
	}

	.svideo_main_block4 {
		margin: 0 20rpx 20rpx 20rpx;
		margin-left: 0;
		border-radius: 16rpx;
		overflow: hidden;
		background-color: #3a4db8;
		position: relative;
		height: 300rpx;
		width: 300rpx;
		display: inline-block;
	}

	.scroll-view {
		width: 100%;
		white-space: nowrap;
	}

	.svideo_main_block_b4 {
		font-size: 24rpx;
		position: absolute;
		bottom: 0rpx;
		box-sizing: border-box;
		margin-left: 30rpx;
		margin-right: 30rpx;
		width: 100%;
		height: 90rpx;
		line-height: 110rpx;
		/* 换图片 */
	}

	.svideo_main_block_b4_text {
		position: absolute;
		left: 20rpx;
		top: 0;
		right: 20rpx;
		height: 90rpx;
		line-height: 90rpx;
		text-align: center;
		z-index: 683;
		white-space: nowrap;
		text-overflow: ellipsis;
		overflow: hidden;
		word-break: break-all;
		box-sizing: border-box;
		margin-top: 16rpx;
		font-size: 24rpx;
	}

	.svideo_main_block_b40 {
		margin-left: 0;
		margin-right: 0;
	}

	.svideo_block_t_text4 {
		font-size: 20rpx;
		// padding-left: 20rpx;
	}

	/* -------------方案5--------- */
	.svideo_main5 {
		width: 100%;
		color: #FFFFFF;
		display: flex;
		flex-direction: row;
		overflow: auto;
		flex-wrap: nowrap;
		width: auto;
		background-color: #F8F8F8;
		padding: 0 20rpx;
		box-sizing: border-box;
		justify-content: space-between;
	}


	.svideo_main_block5 {
		display: inline-block;
		border-radius: 16rpx;
		overflow: hidden;
		width: 345rpx;
	}

	.svideo_main_block50 {
		position: relative;
	}

	.svideo_block_bgimg0 {
		height: 274rpx;
		width: 346rpx
	}

	.svideo5_b {
		background-color: #fff;
		height: 146rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}

	.svideo5_b_title {
		width: 290rpx;
		color: #333333;
		font-size: 28rpx;
		font-weight: bold;
		margin-top: 16rpx;
		white-space: nowrap;
		text-overflow: ellipsis;
		overflow: hidden;
	}

	.svideo5_b_text {
		width: 290rpx;
		color: #666666;
		font-size: 26rpx;
		white-space: nowrap;
		text-overflow: ellipsis;
		overflow: hidden;
	}

	.svideo_block_t5_w {
		display: flex;
		justify-content: center;
		margin-top: 32rpx;
	}

	.svideo_block_t5 {
		margin: 16rpx;
		height: 32rpx;
		line-height: 34rpx;
		font-size: 20rpx;
		background-color: rgba(1, 1, 1, 0.7);
		padding: 0 20rpx;
		border-radius: 16rpx;
		position: relative;
		display: inline-block;
		color: #FFFFFF;
		opacity: 0.7;
		margin-bottom: 30rpx;
		z-index: 99;
	}

	.svideo_block_bgimg1_wrap {
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.svideo_block_bgimg1 {
		width: 230rpx;
		height: 230rpx;
		border-radius: 115rpx;
		overflow: hidden;
		background-color: red;
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
	}

	.svideo_block_bgimg2 {
		width: 40rpx;
		height: 40rpx;
	}

	.svideo_block_bgimg3 {
		position: absolute;
		width: 60rpx;
		height: 60rpx;
	}

	/* ------------- 直播列表/短视频列表*/
	.svideo_main_block_zb {
		font-size: 26rpx;
		white-space: nowrap;
		text-overflow: ellipsis;
		overflow: hidden;
		word-break: break-all;
		position: absolute;
		bottom: 10rpx;
		padding: 0 20rpx;
		width: 80%
	}

	.live_list_b {
		width: 346rpx;
		border-radius: 16rpx;
		overflow: hidden;
		margin: 10rpx 0 10rpx 0;
	}

	.live_list_b_img {
		width: 100%;
		height: 346rpx;
		overflow: hidden;
		position: relative;
	}

	.live_list_b_img_img {
		position: absolute;
		width: 346rpx;
		height: 346rpx;
	}

	.live_list_b_img_hua {
		position: absolute;
		width: 90rpx;
		height: 300rpx;
		right: 5rpx;
		bottom: 3rpx;
		z-index: 9;
	}

	.live_list_b_img_hua1 {
		bottom: 10rpx;
	}

	.live_list_b_text {
		font-size: 20rpx;
		color: #fff;
		margin-left: 30rpx;
	}

	.live_list_b_tip2 {
		padding: 4rpx 10rpx;
		height: 30rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background-image: url("http://site7.55jimu.com/data/upload/mall/store/goods/301/301_06506538074418754.jpg");
		/* 换地址，要用服务器地址 */
		border-radius: 16rpx;
		margin-left: 12rpx;
		margin-top: 10rpx;
		position: relative;
	}

	.live_list_img_bottom1 {
		font-size: 30rpx;
		padding: 10rpx 10rpx 4rpx 10rpx;
		color: #2D2D2D;
		text-overflow: -o-ellipsis-lastline;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		line-clamp: 2;
		-webkit-box-orient: vertical;
	}

	.live_list_main5 {
		display: flex;
		flex-wrap: wrap;
		flex-direction: row;
		overflow: auto;
		width: auto;
		justify-content: space-between;
		padding: 20rpx;
		background-color: #fff;
	}

	.live_list_text3 {
		border-radius: 15rpx;
		background-color: red;
		font-size: 22rpx;
		padding: 0 10rpx;
		color: #fff;
		line-height: 30rpx;
		position: absolute;
		right: -1rpx;
		top: 4rpx;
	}

	.live_list_text30 {
		background-color: #BCAEFE;
	}

	.live_panic_buy3 {
		border: none;
		position: relative;
	}

	.live_panic_time_i {
		white-space: nowrap;
		text-overflow: ellipsis;
		overflow: hidden;
		word-break: break-all;
		width: 35%;
		color: #9A9A9A;
		margin: 0 8rpx
	}

	.live_panic_time_v {
		width: 60%;
	}


	.active_panic_time_img2 {
		width: 40rpx;
		height: 40rpx;
		margin-right: 5rpx;
		border-radius: 20rpx;
		overflow: hidden;
	}

	/* ------ */
	.active_panic_text2 {
		margin: 0 5rpx;
	}

	.active_top_tip20 {
		font-size: 22rpx;
		padding-top: 0;
		padding-bottom: 0;
		line-height: 30rpx;
	}

	.active .tab_nav_block_t {
		color: var(--color_main);
		font-size: 30rpx;
		border-bottom: 2px solid var(--color_main);
		font-weight: bold
	}

	.active .tab_nav_block_i {
		color: var(--color_main);
		font-size: 22rpx
	}

	.video_bg {
		width: 100%;
		height: 100%;
		z-index: 99;
	}

	.video_bg1 {
		width: 100%;
		height: 100%;
		position: absolute;
		left: 0;
		top: 0;
		background-position: center center;
		// background-size: contain;
		background-size: cover;
		background-repeat: no-repeat;

	}

	.svideo_main_block_w {
		z-index: 99;
		position: absolute;
		top: 0;
		left: 0;
	}

	.svideo_main_block_sw {
		z-index: 3;
		position: absolute;
		top: 0;
		left: 0;
		width: 40rpx;
		height: 50rpx;
	}

	.svideo_main_block_lw {
		z-index: 3;
		position: absolute;
		top: 0;
		left: 0;
	}

	.video_bg3 {
		z-index: 3;
		position: absolute;
		top: 0;
		left: 0;
		width: 184px;
		height: 163px;
		// opacity: 0.2;
	}

	.svideo_main5>view:nth-child(1) .svideo_bg_img {
		position: absolute;
		top: 0;
		left: 0;
		background: #B9E5FF;
		opacity: 0.7;
		width: 184px;
		height: 163px;
		z-index: 10;
	}

	.svideo_main5>view:nth-child(2) .svideo_bg_img {
		position: absolute;
		top: 0;
		left: 0;
		background: #FFCEB9;
		opacity: 0.7;
		width: 184px;
		height: 163px;
		z-index: 10;
	}

	.featured {
		display: flex;
		justify-content: center;
		flex-wrap: wrap;
		margin-top: 20rpx;
		margin-bottom: 20rpx;
		background: #ffffff;
	}

	.featured_item {
		width: 49.6%;
		color: #333333;
		border-top: 1rpx solid #DFDFDF;
		border-left: 1rpx solid #DFDFDF;
		padding: 32rpx 22rpx 36rpx;
	}

	.featured>.featured_item:nth-child(1) {
		border-top: none;
	}

	.featured>.featured_item:nth-child(2) {
		border-top: none;
	}

	.featured>.featured_item:nth-child(odd) {
		border-left: none;
	}

	.featured_tit {
		font-size: 36rpx;
		font-weight: bold;
		white-space: nowrap;
	}

	.featured_text {
		font-size: 26rpx;
		font-weight: 400;
		margin-top: 12rpx;
		margin-bottom: 24rpx;
		white-space: nowrap;
	}

	.featured_img {
		overflow: hidden;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.featured_img>image {
		width: 182rpx;
		height: 192rpx;
		border-radius: 4rpx;
		background: #ECF5FF;
	}

	.featured_img>image:first-of-type {
		margin-right: 8rpx;
	}

	.featured_img>image:last-of-type {
		margin-left: 8rpx;
	}

	.featured_img ::v-deep img {
		opacity: 0 !important;
	}

	.featured_img ::v-deep div {
		background-size: cover !important;
	}
</style>
