<template>
	<view class="stack-swiper-container" :class="{ show: isShow }">
		<!-- @touchend="tauchEnd"-->
		<view class="swiper-box" @touchstart="handleTauchStart" @touchmove="tauchMove" @touchend="tauchEnd">
			<view
				class="item-box"
				v-for="(brand, index) in swiperList"
				:key="index"
				:class="{ none: brand.zIndex == 1 }"
				:style="{ '--index': brand.zIndex, '--left': brand.mLeft }"
			>
				<view class="swiper-item" @tap.stop="handleBrandClick(brand)" :data-type="brand.type" :data-url="data.url">
					<image :src="brand.img" mode="aspectFill"></image>
					<!-- <video :src="brand.url" autoplay loop muted :show-play-btn="false" :controls="false" objectFit="cover" v-if="brand.type == 'video'"></video> -->
				</view>
			</view>
		</view>
	</view>
</template>
<script>
const app = getApp();
export default {
	name: 'StackSwiper',
	props: {
		data: {
			type: Array,
			default: []
		},
		autoplay: {
			type: [String, Boolean],
			default: false
		},
		interval: {
			type: Number,
			default: 5000
		},
		current: {
			type: Boolean,
			default: false
		},
		duration: {
			type: Number,
			default: 500
		}
	},
	data() {
		return {
			swiperList: [{ type: 'image', url: '' }],
			isShow: false,
			tauchStart: '',
			direction: '',
			intervalObj: null
		};
	},
	created() {
		this.swiperList = this.data;
		this.tauchSwiper();
		if (this.autoplay) {
			this.setAutoPlay();
		}
		// console.log('this.data', this.data);
	},
	methods: {
		//轮播点击
		handleBrandClick(item) {
			this.$Router.push('/pages/index/brandingWall')
			return
			console.log('轮播点击', item);
			if (item.url_type == 'store') {
				this.$Router.push(`/standard/store/shopHomePage?vid=${item.info.storeId}`)
			} 
		},
		// 设置自动轮播
		setAutoPlay() {
			this.intervalObj = setInterval(() => {
				this.direction = 'left';
				this.tauchEnd();
			}, this.interval);
		},
		// 初始化tauchSwiper
		tauchSwiper(name) {
			let list = this.swiperList;
			for (let i = 0; i < list.length; i++) {
				// Math.abs(x) 函数返回指定数字 “x“ 的绝对值
				list[i].zIndex = parseInt(list.length / 2) + 1 - Math.abs(i - parseInt(list.length / 2));
				list[i].mLeft = i - parseInt(list.length / 2);
			}
			this.swiperList = list;
			setTimeout(() => {
				this.isShow = true;
			}, 300);
		},
		// tauchSwiper触摸开始
		handleTauchStart(e) {
			this.tauchStart = e.touches[0].pageX;
			if (this.intervalObj) {
				clearInterval(this.intervalObj);
				this.intervalObj = null;
			}
		},
		// tauchSwiper计算方向
		tauchMove(e) {
			this.direction = e.touches[0].pageX - this.tauchStart > 0 ? 'right' : 'left';
		},
		// tauchSwiper计算滚动
		tauchEnd(e) {
			this.tauchStart = '';
			if (!this.direction) {
				if (this.autoplay && !this.intervalObj) {
					this.setAutoPlay();
				}
				return;
			}
			let direction = this.direction;
			let list = this.swiperList;
			this.direction = '';
			if (direction == 'right') {
				let mLeft = list[0].mLeft;
				let zIndex = list[0].zIndex;
				for (let i = 1; i < list.length; i++) {
					list[i - 1].mLeft = list[i].mLeft;
					list[i - 1].zIndex = list[i].zIndex;
				}
				list[list.length - 1].mLeft = mLeft;
				list[list.length - 1].zIndex = zIndex;
				this.swiperList = list;
			} else {
				let mLeft = list[list.length - 1].mLeft;
				let zIndex = list[list.length - 1].zIndex;
				for (let i = list.length - 1; i > 0; i--) {
					list[i].mLeft = list[i - 1].mLeft;
					list[i].zIndex = list[i - 1].zIndex;
				}
				list[0].mLeft = mLeft;
				list[0].zIndex = zIndex;
				this.swiperList = list;
			}
			if (this.autoplay && !this.intervalObj) {
				this.setAutoPlay();
			}
		}
	}
};
</script>

<style lang="scss">
.stack-swiper-container {
	width: 92%;
	margin: 0 auto;
	opacity: 0;
	&.show {
		opacity: 1;
	}
	.swiper-box {
		width: 100%;
		height: 200rpx;
		position: relative;
		overflow: hidden;
		box-sizing: border-box;
	}
}
.swiper-item image,
.swiper-item video {
	width: 100%;
	display: block;
	height: 100%;
	margin: 0;
	pointer-events: none;
}

image {
	max-width: 100%;
	display: inline-block;
	position: relative;
	z-index: 0;
}

.swiper-box .item-box {
	position: absolute;
	// width: 300rpx;
	// height: 380rpx;
	width: 163rpx;
	height: 125rpx;
	top: 0;
	bottom: 0;
	left: 50%;
	margin: auto;
	transition: all 0.2s ease-in 0s;
	opacity: 1;
	box-shadow: 0px 2px 12px rgba(0, 0, 0, 0.08);
	border-radius: 19rpx;
	overflow: hidden;
}

.swiper-box .item-box.none {
	opacity: 0;
}

.swiper-box .item-box .swiper-item {
	width: 100%;
	height: 100%;
	border-radius: 6rpx;
	overflow: hidden;
}

.swiper-box .item-box {
	transform: scale(calc(0.5 + var(--index) / 10));
	margin-left: calc(var(--left) * 90rpx - 11vw);
	z-index: var(--index);
}
</style>
