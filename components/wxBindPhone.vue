<template>
    <view class="">
        <uni-popup ref="wt" type="center" :mask-click="false">
            <view class="sld-success flex_column_center_center">
                <view class="sld-success-title">{{ $L('是否允许获取绑定的手机号？') }}</view>
                <view class="sld-btns">
                    <button @click="getBindCancle">{{ $L('取消') }}</button>
                    <button open-type="getPhoneNumber" @getphonenumber="getMobile" class="confirm">
                        {{ $L('确定') }}
                    </button>
                </view>
            </view>
        </uni-popup>

        <uni-popup ref="bind" type="center" :mask-click="false">
            <view class="wrapper">
                <view class="bind_state_top">
                    <text class="mobile_num">{{ userPhone }}</text>
                    <text class="mobile_binded">{{ $L('该手机号已被绑定') }}</text>
                </view>
                <view class="bind_change_info">
                    <view class="bind_change_info_text">
                        {{ $L('继续绑定：将解除与账号') }}
                        <text class="change_info_mobile">{{ bindedAccount }}</text>
                        {{ $L('的绑定关系') }}
                    </view>
                    <view class="bind_change_info_text">
                        {{ $L('更新信息：授权信息将绑定到账号') }}
                        <text class="change_info_mobile">{{ bindedAccount }}</text>
                        {{ $L('上') }}
                    </view>
                </view>
                <view class="bind_state_btn_con">
                    <view class="update_btn" @click="bindMobile(3)">{{ $L('更新信息') }}</view>
                    <view class="go_on_btn" @click="bindMobile(2)">{{ $L('继续绑定') }}</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import { mapMutations } from 'vuex';
export default {
    data() {
        return {
            wtCode: '',
            userPhone: '',
            bindedAccount: '',
            bindKey: ''
        };
    },

    components: {
        uniPopup
    },

    methods: {
        ...mapMutations(['setUserCenterData', 'login']),

        openKey(key, spreaderMemberId) {
            this.bindKey = key;
            this.spreaderMemberId = spreaderMemberId;
            this.$refs.wt.open();
        },

        getMobile(e) {
            if (e.detail.errMsg == 'getPhoneNumber:ok') {
                this.$refs.wt.close();
                uni.login({
                    success: (code) => {
                        let { encryptedData, iv } = e.detail;
                        this.$request({
                            url: 'v3/member/front/login/wechat/getMobile',
                            method: 'POST',
                            data: {
                                code: code.code,
                                encryptedData,
                                iv
                            }
                        }).then((res) => {
                            if (res.state == 200) {
                                this.userPhone = res.data.phoneNumber;
                                this.bindMobile(1);
                            } else {
                                this.$api.msg(res.msg);
                            }
                        });
                    }
                });
            }
        },

        bindMobile(bindType) {
            uni.showLoading({});
            let param = {};
            param.url = 'v3/member/front/login/wechat/bindMobile';
            param.data = {};
            param.data.bindType = bindType;
            param.data.mobile = this.userPhone;
            param.data.resource = 4;
            param.data.bindKey = this.bindKey;
            //如果推手分享，则建立推手分享关系
            if (this.spreaderMemberId) {
                param.data.spreaderKey = this.spreaderMemberId;
            }

            //如果有缓存的购物车数据，登录需要把数据同步，并清除本地缓存
            if (this.$getUnLoginCartParam()) {
                param.data.cartInfo = this.$getUnLoginCartParam();
            }

            param.method = 'POST';
            this.$request(param).then((res) => {
                uni.hideLoading();
                if (res.state == 200) {
                    this.$refs.bind.close();
                    //更新登录时间
                    uni.setStorage({ key: 'lm_login_time', data: new Date().getTime() });
                    res.data.loginTime = Date.parse(new Date()); //登录时间
                    this.login(res.data);
                    this.$sldStatEvent({ behaviorType: 'reg' }); //统计埋点
                    //获取个人中心的数据
                    this.$request({ url: 'v3/member/front/member/memberInfo' }).then((result) => {
                        this.setUserCenterData(result.data);
                        this.$loginGoPage();
                    });
                    this.$api.msg(res.msg);
                } else if (res.state == 267) {
                    //手机号已被绑定,提示用户
                    this.$refs.bind.open();
                    this.bindedAccount = res.data;
                } else {
                    //错误提示
                    this.$api.msg(res.msg);
                }
            });
        },
        getBindCancle() {
            this.$refs.wt.close();
            let query = {
                code: this.bindKey
            };
            //如果推手分享，则建立推手分享关系
            if (uni.getStorageSync('u')) {
                query.spreaderKey = uni.getStorageSync('u');
            }
            this.$Router.replace({
                path: '/pages/public/bindMobile',
                query
            });
        }
    }
};
</script>

<style lang="scss">
.wrapper {
    background: #fff;
    padding: 30rpx;
    border-radius: 20rpx;
}

.bind_state_top {
    display: flex;
    flex-direction: column;
    align-items: center;

    .mobile_logo {
        width: 120rpx;
        height: 120rpx;
    }

    .mobile_num {
        font-size: 30rpx;
        font-weight: 500;
        color: #fc2624;
        margin-top: 39rpx;
    }

    .mobile_binded {
        font-size: 30rpx;
        font-weight: 500;
        color: #333333;
        margin-top: 19rpx;
    }
}

.bind_change_info {
    font-size: 24rpx;
    text-align: center;
    color: #666666;
    line-height: 39rpx;
    margin-top: 31rpx;

    .change_info_mobile {
        color: #333333;
    }
}

.bind_state_btn_con {
    width: 520rpx;
    height: 70rpx;
    margin: 0 auto;
    margin-top: 40rpx;
    font-size: 30rpx;
    display: flex;
    justify-content: center;

    .update_btn {
        width: 208rpx;
        height: 69rpx;
        line-height: 69rpx;
        color: #ff0000;
        text-align: center;
        border-radius: 35rpx 0 0 35rpx;
        border: 1px solid #ff0000;
    }

    .go_on_btn {
        width: 208rpx;
        height: 69rpx;
        line-height: 69rpx;
        text-align: center;
        background-color: #ff0000;
        color: white;
        border-radius: 0 35rpx 35rpx 0;
        border: 1px solid #ff0000;
    }
}

.sld-success-title {
    line-height: 42rpx;
    font-size: 32rpx;
    color: #666666;
    padding: 40rpx 30rpx;
    text-align: center;
}

.sld-success {
    background-color: #fff;
    color: #666666;
    font-size: 28rpx;
    width: 500rpx;
    border-radius: 20rpx;
    padding: 30rpx;

    .sld-success-content {
        line-height: 45rpx;
        margin-bottom: 30rpx;
        padding: 0 35rpx;
        color: #999;
        font-size: 26rpx;
    }

    .sld-btns {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 20rpx;
        width: 100%;

        button {
            width: 200rpx;
            height: 50rpx;
            border-radius: 40rpx;
            font-size: 28rpx;
            line-height: 50rpx;
            outline: none;
            background-color: #fff;
            color: #999;
            border: 1px solid #999999;
            margin: 0;

            &.confirm {
                color: #fff;
                background: linear-gradient(135deg, #fb3e31 0%, #fed600 0%, #ff4e00 0%, #ff001d 100%);
                border: none;
            }

            &::after {
                border: none;
            }
        }
    }
}
</style>
