// 历遍生成4个方向的底部安全区
@each $d in top, right, bottom, left {
	.uv-safe-area-inset-#{$d} {
		padding-#{$d}: 0;
		padding-#{$d}: constant(safe-area-inset-#{$d});  
		padding-#{$d}: env(safe-area-inset-#{$d});  
	}
}

//提升H5端uni.toast()的层级，避免被uvui的modal等遮盖
/* #ifdef H5 */
uni-toast {
    z-index: 10090;
}
uni-toast .uni-toast {
   z-index: 10090;
}
/* #endif */

// 隐藏scroll-view的滚动条
::-webkit-scrollbar {
    display: none;  
    width: 0 !important;  
    height: 0 !important;  
    -webkit-appearance: none;  
    background: transparent;  
}

$uvui-nvue-style: true !default;
@if $uvui-nvue-style == false {
	view, scroll-view, swiper-item {
		display: flex;
		flex-direction: column;
		flex-shrink: 0;
		flex-grow: 0;
		flex-basis: auto;
		align-items: stretch;
		align-content: flex-start;
	}
}
