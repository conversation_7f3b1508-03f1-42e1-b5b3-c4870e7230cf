<template>
	<view>
		<!-- 规格弹框 start -->
		<uni-popup @change="spcpopChange" class="spec_model" ref="specModel" type="bottom">
			<view class="spec_model_con">
				<view class="spec_model_content">
					<view class="spec_model_top">
						<view class="spec_model_goods">
							<view class="spec_goods_image" v-if="defaultProduct.goodsPics&&defaultProduct.goodsPics[0] ">
								<image :src="defaultProduct.goodsPics[0]" mode="aspectFit"></image>
							</view>
							
							<view class="spec_goods_right">
								<view class="spec_goods_price_con">
									<view class="spec_prices">
										<!-- 立即秒杀进行中 start -->
										<view class="spec_goods_price spec_goods_price_seck"
											v-if="secKillInfo && secKillInfo.state == 2">
											<text>{{ $L('￥') }}</text>
											<text>{{
		                                    $getPartNumber(secKillInfo.seckillPrice, 'int')
		                                    }}</text>
											<text>{{
		                                    $getPartNumber(secKillInfo.seckillPrice, 'decimal')
		                                    }}</text>
										</view>
										<!-- 立即秒杀进行中 end -->
		
										<!-- 预售 start -->
										<!-- 立即付定金 start -->
										<view class="spec_goods_price"
											:class="{'spec_goods_price_pre':valiInfo(preSellInfo) && preSellInfo.pre_run != 1}"
											v-else-if="preSellInfo &&preSellInfo.type == 1 &&preSellInfo.pre_run == 2">
											<text>{{ $L('￥') }}</text>
											<text>{{$getPartNumber(preSellInfo.firstMoney, 'int')}}</text>
											<text>{{$getPartNumber(preSellInfo.firstMoney, 'decimal')}}</text>
										</view>
										<!-- 立即付定金 end -->
										<!-- 全款 start -->
										<view class="spec_goods_price"
											:class="{'spec_goods_price_pre':valiInfo(preSellInfo) && preSellInfo.pre_run != 1}"
											v-else-if="preSellInfo &&preSellInfo.type == 2 &&preSellInfo.pre_run == 2">
											<text>{{ $L('￥') }}</text>
											<text>{{$getPartNumber(preSellInfo.presellPrice, 'int')}}</text>
											<text>{{$getPartNumber(preSellInfo.presellPrice, 'decimal')}}</text>
										</view>
										<!-- 全款 end -->
										<!-- 预售 end -->
		
										<!-- 阶梯团start -->
										<view class="spec_goods_price spec_goods_price_lad"
											v-else-if="JSON.stringify(ladderInfo) != '{}'">
											<text>{{ $L('￥') }}</text>
											<text>{{$getPartNumber(ladderInfo.advanceDeposit, 'int')}}</text>
											<text>{{$getPartNumber(ladderInfo.advanceDeposit, 'decimal')}}</text>
										</view>
										<!-- 阶梯团end -->
		
										<!-- 拼团start -->
										<view class="spec_goods_price spec_goods_price_pin"
											v-else-if="JSON.stringify(pinInfo) != '{}' && pinButState">
											<text>{{ $L('￥') }}</text>
											<text>{{$getPartNumber(pinInfo.leaderPrice
		                                    ? pinButState == 3
		                                    ? pinInfo.spellPrice
		                                    : pinInfo.leaderPrice
		                                    : pinInfo.spellPrice,
		                                    'int'
		                                    )
		                                    }}</text>
											<text>{{
		                                    $getPartNumber(
		                                    pinInfo.leaderPrice
		                                    ? pinButState == 3
		                                    ? pinInfo.spellPrice
		                                    : pinInfo.leaderPrice
		                                    : pinInfo.spellPrice,
		                                    'decimal'
		                                    )
		                                    }}</text>
										</view>
										<!-- 拼团end -->
		
										<!-- 正常商品start -->
										
										<view class="flex_row_start_center" v-else>
											<block v-if="defaultProduct.superPrice&&userCenterData.isSuper==1">
												<view class="spec_goods_price left_super flex_row_start_center">
													<view class="sell_price">
														<text class="unit">¥ </text>
														<text class="price_int">{{$getPartNumber(defaultProduct.superPrice,'int')}}</text>
														<text class="price_decimal">{{$getPartNumber(defaultProduct.superPrice,'decimal')}}</text>
													</view>
													<view class="left_super_price_img"
														:style="'background-image:url('+imgUrl+'super/super_price.png)'">会员价
													</view>
													
													
												</view>
											</block>
											<block v-else>
												<view class="spec_goods_price">
													<text
														:class="{address_pin:valiInfo(pinInfo)&&pinInfo.state==1,address_pre:valiInfo(preSellInfo) && preSellInfo.pre_run != 1,address_lad:valiInfo(ladderInfo),address_seck:secKillInfo && (secKillInfo.state == 2)}">{{ $L('￥') }}</text>
													<text
														:class="{address_pin:valiInfo(pinInfo)&&pinInfo.state==1,address_pre:valiInfo(preSellInfo) && preSellInfo.pre_run != 1,address_lad:valiInfo(ladderInfo),address_seck:secKillInfo && (secKillInfo.state == 2)}">{{
												    $getPartNumber(defaultProduct.productPrice, 'int')
												    }}</text>
													<text
														:class="{address_pin:valiInfo(pinInfo)&&pinInfo.state==1,address_pre:valiInfo(preSellInfo) && preSellInfo.pre_run != 1,address_lad:valiInfo(ladderInfo),address_seck:secKillInfo && (secKillInfo.state == 2)}">{{
												    $getPartNumber(defaultProduct.productPrice, 'decimal')
												    }}</text>
												</view>
											</block>
		
											<view v-if="defaultProduct.superPrice&&userCenterData.isSuper!=1"
												class="left_super flex_row_start_center">
												<view class="sell_price">
													<text class="unit">¥ </text>
													<text
														class="price_int">{{$getPartNumber(defaultProduct.superPrice,'int')}}</text>
													<text
														class="price_decimal">{{$getPartNumber(defaultProduct.superPrice,'decimal')}}</text>
												</view>
												<view class="left_super_price_img"
													:style="'background-image:url('+imgUrl+'super/super_price.png)'">会员价
												</view>
											</view>
										</view>
										<!-- 正常商品end -->
									</view>
									<!-- 活动标识 start -->
									<view class="sec_kill_tips" v-if="secKillInfo &&(secKillInfo.state == 2)">
										{{ $L('限时秒杀') }}
									</view>
									<view class="pre_sale_tips"
										v-if="JSON.stringify(preSellInfo) != '{}' &&preSellInfo.pre_run == 2">
										{{ $L('预售') }}
									</view>
									<text class="ladder_regiment_tips"
										v-if="JSON.stringify(ladderInfo) != '{}'">{{ $L('阶梯团') }}</text>
									<text class="pin_tips"
										v-if="JSON.stringify(pinInfo) != '{}' && pinButState">{{ $L('拼团') }}</text>
									<!-- 活动标识 end -->
								</view>
								<!-- 已下架商品 start -->
								<view class="spec_goods_des" v-if="goodsData.state != 3">
									{{ $L('商品已下架') }}
								</view>
								<!-- 已下架商品 end -->
								<!-- 普通商品 start -->
								<view class="spec_goods_des" v-else>
									{{ $L('已选规格') }}：
									<text v-if="defaultProduct.getSpecValues">{{defaultProduct.getSpecValues}}</text>
									<text v-else>{{ $L('默认') }}</text>
								</view>
								<!-- 普通商品 end -->
							</view>
						</view>
						<image :src="imgUrl + 'goods_detail/close.png'" mode="aspectFit" class="close_spec"
							@click="closeSpecModel"></image>
					</view>
					<scroll-view scroll-y="true" class="spec_content">
						<view class="spec_list" v-if="specs && specs.length > 0">
							<view class="spec_list_pre" v-for="(item, index) in specs" :key="index">
								<view class="spec_list_pre_name">{{ item.specName }}</view>
								<block v-if="
								item && item.specValueList && item.specValueList.length > 0
							  " v-for="(item1, index1) in item.specValueList" :key="index1">
									<!-- checkState : 1-选中，2-可选，3-禁用 -->
									<view class="spec_list_pre_desc" :class="{
								  spec_list_pre_desc_disabled: item1.checkState == '3'
								}" v-if="item1.checkState == '3'">
										<view class="spec_list_pre_con">
											<image :src="item1.image" mode="aspectFit" v-if="item1.image">
											</image>
											<text>{{ item1.specValue }}</text>
										</view>
									</view>
									<view class="spec_list_pre_desc"
										:class="{spec_list_pre_desc_active:item1.checkState == '1',spec_list_pre_desc_active_pin:valiInfo(pinInfo)&&item1.checkState == '1'&&pinInfo.state==1,spec_list_pre_desc_active_pre:valiInfo(preSellInfo) && preSellInfo.pre_run != 1&&item1.checkState == '1',spec_list_pre_desc_active_lad:item1.checkState == '1'&&valiInfo(ladderInfo),spec_list_pre_desc_active_seck:item1.checkState == '1'&&secKillInfo && (secKillInfo.state == 2)}"
										@click="
									  selectSpecVal('choice', item.specId, item1.specValueId)
									" v-else>
										<view class="spec_list_pre_con">
											<image :src="item1.image" mode="aspectFit" v-if="item1.image">
											</image>
											<text>{{ item1.specValue }}</text>
										</view>
									</view>
								</block>
							</view>
						</view>
						<view class="spec_num">
							<view class="spec_num_left">
								{{ $L('购买数量') }}
							</view>
							<view class="">
								<view class="spec_num_right">
									<text @click="editNum('reduce')"
										:class="{ no_edit: currentSpecNum == 1 }">-</text>
									<input type="number" v-model="currentSpecNum" @blur="editNum('edit', $event)"
										cursor-spacing="0" :cursor="currentSpecNum.toString().length"
										maxlength="5" />
									<text @click="editNum('add')" :class="{ no_edit: noEdit }">+</text>
								</view>
								<view v-if="actiState">
									<text class="buyLimit"
										v-if="JSON.stringify(preSellInfo) != '{}' &&preSellInfo.buyLimit > 0">{{ $L('限购数量') }}{{ preSellInfo.buyLimit
		                            }}{{ $L('件') }}</text>
									<text class="buyLimit"
										v-if="JSON.stringify(ladderInfo) != '{}' &&ladderInfo.buyLimit > 0">{{ $L('限购数量') }}{{ ladderInfo.buyLimit
		                            }}{{ $L('件') }}</text>
									<text class="buyLimit address_pin"
										v-if="JSON.stringify(pinInfo) != '{}' && pinInfo.buyLimit > 0">{{ $L('限购数量') }}{{ pinInfo.buyLimit
		                            }}{{ $L('件') }}</text>
									<text class="buyLimit"
										v-if="JSON.stringify(secKillInfo) != '{}' &&secKillInfo.buyLimit > 0">{{ $L('限购数量') }}{{ secKillInfo.buyLimit
		                            }}{{ $L('件') }}</text>
								</view>
							</view>
						</view>
						<view class="delivery_type">
							<view class="">配送方式</view>
							<view class="delivery_types">
								<view v-if="supportMailing == 1" @click="isPickup = 0" :class="isPickup === 0 ? 'delivery_type_item_active' : ''" class="delivery_type_item">
									送货到家
								</view>
								<view v-if="supportPickup == 1" @click="isPickup = 1" :class="isPickup === 1 ? 'delivery_type_item_active' : ''" class="delivery_type_item">
									到店自提
								</view>
							</view>
						</view>
					</scroll-view>
				</view>
				<!-- 规格弹框的底部按钮 start -->
		
				<!-- 商品下架 start -->
				<view class="spec_btn" v-if="goodsData.state != 3">
					<button class="spec_not_stock spec_btn_only">
						{{ $L('商品已下架') }}
					</button>
				</view>
				<!-- 商品下架 end -->
		
		
				<!-- 秒杀商品start -->
				<block v-else-if="JSON.stringify(secKillInfo) != '{}' && secKillInfo.state == 2">
					<!-- 秒杀已抢完 start -->
					<view class="spec_btn" v-if="defaultProduct &&defaultProduct.productStock == 0">
						<button class="spec_not_stock spec_btn_only">
							{{ $L('已抢完') }}
						</button>
					</view>
					<!-- 秒杀已抢完 end -->
					<!--立即秒杀 start -->
					<view class="spec_btn" @click="buy" v-else>
						<button class="spec_seckill_btn spec_btn_only spec_seckill_btn_seck">
							{{ $L('立即秒杀') }}
						</button>
					</view>
					<!--立即秒杀 end -->
				</block>
				<!-- 秒杀商品end -->
				<!-- 预售活动 start -->
				<block v-else-if="valiInfo(preSellInfo) && preSellInfo.pre_run != 1">
					<view class="spec_btn" v-if="defaultProduct &&defaultProduct.productStock == 0">
						<button class="spec_not_stock spec_btn_only">
							{{ $L('库存不足') }}
						</button>
					</view>
					<!--立即付定金/全款支付 start -->
					<view class="spec_btn" v-else @click="buy">
						<button :class="{
						spec_deposit_btn: preSellInfo.type == 1,
						spec_seckill_btn: preSellInfo.type == 2,
						spec_btn_only: true
					  }">
							{{
		                preSellInfo.type == 1 ? `${$L('立即付定金')}` : $L('立即购买')
		                }}
						</button>
						<!-- {{preSellInfo.type==1?`￥${preSellInfo.firstMoney}`:''}} -->
					</view>
					<!--立即付定金/全款支付 end -->
				</block>
				<!-- 预售活动 end -->
				<!-- 阶梯团活动start -->
				<block v-else-if="valiInfo(ladderInfo) && ladderInfo.ladder_run == 2">
					<view class="spec_btn" v-if="defaultProduct &&defaultProduct.productStock == 0">
						<button class="spec_not_stock spec_btn_only">
							{{ $L('库存不足') }}
						</button>
					</view>
					<view class="spec_btn" v-else>
						<view @click="buy" class="specifications_btn2">
							<text>{{ $L('立即付定金') }}</text>
						</view>
					</view>
				</block>
				<!-- 阶梯团活动end -->
				<!-- 拼团活动start -->
				<block v-else-if="JSON.stringify(pinInfo) != '{}' && pinInfo.state == 1">
					<view class="spec_btn" v-if="defaultProduct&&defaultProduct.productStock == 0">
						<button class="spec_not_stock spec_btn_only">
							{{ $L('库存不足') }}
						</button>
					</view>
					<block v-else>
						<view class="spec_btn" v-if="!pinButState" @click="buy('aloneBuy')">
							<button class="specifications_bottom_btn3">
								<text>{{ $L('单独买') }}</text>
								<text>({{ $L('￥') }}{{ pinInfo.productPrice }})</text>
							</button>
						</view>
						<view class="spec_btn" v-if="pinButState == 1" @click="buy">
							<button class="specifications_bottom_btn4">
								<text>{{ $L('去开团') }}</text>
								<text>{{ $L('￥')
		                        }}{{
		                        pinInfo.leaderIsPromotion == 1
		                        ? pinInfo.leaderPrice
		                        : pinInfo.spellPrice
		                        }}</text>
							</button>
						</view>
						<view class="spec_btn" v-if="pinButState == 2">
							<view class="specification_add" @tap="buy('aloneBuy')">
								<text>{{ $L('￥') }}{{ pinInfo.productPrice }}</text>
								<text>{{ $L('单独买') }}</text>
							</view>
							<view class="specification_buy" @tap="buy">
								<text>{{ $L('￥')
		                        }}{{
		                        pinInfo.leaderIsPromotion == 1
		                        ? pinInfo.leaderPrice
		                        : pinInfo.spellPrice
		                        }}</text>
								<text>{{ $L('去开团') }}</text>
							</view>
						</view>
						<view class="spec_btn" v-if="pinButState == 3" @click="buy">
							<button class="specifications_bottom_btn4">
								<text>{{ $L('去参团') }}</text>
								<text>({{ $L('￥') }}{{ pinInfo.spellPrice }})</text>
							</button>
						</view>
					</block>
				</block>
				<!-- 拼团活动end -->
				<block v-else>
					<!--库存不足 start -->
					<view class="spec_btn" v-if="(defaultProduct &&defaultProduct.productStock == 0)">
						<button class="spec_not_stock spec_btn_only">
							{{ $L('库存不足') }}
						</button>
					</view>
					<!--库存不足 end -->
					<!-- 普通商品 start-->
					<block v-else>
						<view class="spec_btn" v-if="showSpecModelType == ''">
							<text class="spec_add_cart_btn"
								:class="{'other_btn_pin':valiInfo(pinInfo)&&pinInfo.state==1,'other_btn_lad':valiInfo(ladderInfo),'other_btn_seck':secKillInfo && (secKillInfo.state == 2)}"
								@click="addCart">{{
		                    $L('加入购物车')
		                    }}</text>
							<text class="spec_buy_btn"
								:class="{'buy_now_btn_seck':secKillInfo && (secKillInfo.state == 2)}"
								@click="buy">{{
		                    $L('立即购买')
		                    }}</text>
						</view>
						<view class="spec_btn" v-if="showSpecModelType == 'add'">
							<text class="spec_add_cart_btn spec_btn_only"
								:class="{'other_btn_pin':valiInfo(pinInfo)&&pinInfo.state==1,'other_btn_lad':valiInfo(ladderInfo),'other_btn_seck':secKillInfo && (secKillInfo.state == 2)}"
								@click="addCart">{{
		                    $L('加入购物车')
		                    }}</text>
						</view>
						<view class="spec_btn" v-if="showSpecModelType == 'buy'">
							<text class="spec_buy_btn spec_btn_only"
								:class="{'spec_buy_btn_seck':secKillInfo && (secKillInfo.state == 2)}"
								@click="buy">{{
		                    $L('立即购买')
		                    }}</text>
						</view>
					</block>
		
					<!-- 普通商品 end-->
				</block>
			</view>
			<!-- 规格弹框的底部按钮 end -->
		</uni-popup>
		<!-- 规格弹框 end -->
		<uni-popup ref="popLogin" type="dialog">
			<uni-popup-dialog type="input" :title="$L('提示')" :content="$L('请登录')" :duration="2000"
				@confirm="confirmLogin">
			</uni-popup-dialog>
		</uni-popup>
	</view>
</template>

<script>
	import uniPopup from '@/components/uni-popup/uni-popup.vue'
	import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue'
	import {
		mapState,
		mapMutations,
		mapActions
	} from 'vuex'
	export default {
		components: {
			uniPopup,
			uniPopupDialog,
		},
		data() {
			return {
				supportPickup: 1,// 是否支持自提
				supportMailing: 1, //否支持邮寄
				isPickup: 0, //邮寄
				imgUrl: getApp().globalData.imgUrl,
				imgurl: getApp().globalData.imgUrl,
				dialogTitle: this.$L('温馨提示!'),
				dialogCon: this.$L('您需要先登录哦～'),
				goodsData: {},
				specSelected: [],
				statistics: {}, //商品评价数据
				shareList: [],
				selAddressData: [],
				addressAll: this.$L('请选择所在地区'),
				addressList: [], //地址列表
				goodsId: '', //商品id
				curFreight: '', //当前运费
				currentSelId: '', //默认选中第一个地址
				isNavShow: false, //是否显示导航
		
				currentNav: 0, //当前点击的默认是第一项
				nav1ScrollTop: 0, //商品模块距离顶部的距离
				nav2ScrollTop: 0, //评价模块距离顶部的距离
				nav3ScrollTop: 0, //推荐模块距离顶部的距离
				nav4ScrollTop: 0, //详情模块距离顶部的距离
				codeImg: '', //海报图片
				poster: false, //生成海报
				specModel: false, //规格弹框
				currentSpecNum: 1, //当前规格弹框中的购买数量
				couponList: [], //优惠券列表
				defaultProduct: {}, //默认货品信息
				specs: [], //商品规格列表
				productId: '', //货品id
				source: '', //来源，终端类型，1、pc 2、app；3、公众号或微信内部浏览器；4、小程序
				mode: 'nav', //轮播图的显示样式类型
				current: 0, //轮播图默认显示第一页
				showSpecModelType: '', //规格弹框的底部按钮的显示类型	默认：加入购物车及立即购买都有	add：加入购物车	buy：立即购买		nosocket库存不足	offshelf商品下架
				noOpcity: false, //顶部导航的背景是否有透明度，轮播图以上有，以下没有
				isLoading: false,
				cartNum: 0, //购物车数量
				cartNumShow: false, //购物车数量的角标是否显示
				storeInf: {}, //店铺信息
				goodsCommentsInfo: {}, //评价信息
				recommendedList: [], //店铺推荐列表
				deliverInfo: {}, //发货地及运费信息
				transparent_mask: false, //透明遮罩蒙层
				tips_show: false, //分享链接弹框
				storeInf: {}, //店铺信息
				goodsParameterList: [], //规格参数列表
				openGoodsParam: false, //规格参数超出5行，点击展开，收起
				pageCurrent: 1, //优惠券列表，页
				pageSize: 10, //优惠券列表 每页的条数
				goReceiveBg: getApp().globalData.imgUrl + 'coupon/coupon_pre_bg.png', //立即领取背景
				finishReceiveBg: getApp().globalData.imgUrl + 'coupon/finishReceiveBg.png', //已抢完背景
				hasReceiveBg: getApp().globalData.imgUrl + 'coupon/hasReceiveBg.png', //已领取背景
				fullDisList: [], //满优惠列表
				promotionId: '', //活动id
				secKillInfo: {}, //秒杀活动详情信息
				secKillDay: '00', //秒杀活动倒计时 天
				secKillHr: '00', //秒杀活动倒计时 时
				secKillMin: '00', //秒杀活动倒计时 分
				secKillSec: '00', //秒杀活动倒计时 秒
				preSellInfo: {}, // 预售商品详情信息
				noEdit: false, //不可编辑
				ladderInfo: {}, //阶梯团信息
				ladderDay: 0, //天
				ladderHour: 0, //时
				ladderMinute: 0, //分钟
				ladderSecond: 0, //秒
				ladderProcess: '', //阶梯团进度
				pinInfo: {}, //拼团信息
				spellTeamId: 0, //拼团团队Id
				pinButState: null, //拼团按钮状态,
				showState: 0, //当当前页面进入到下一个页面时，将此值置为1，上一个页面回退到当前页面时，再onShow里判断，防止onShow触发过多
				spreaderMemberId: 0, //专门的推手分享ID设置
				goodsVideo: '',
				showControls: true, //是否显示轮播角标
				playVFlag: false,
				address_list: [],
				curAddress: '',
				sourceId: '',
				selAddressData: [],
				isBack: true,
				dis: false,
				rankData: [],
				copyname_now: false, //复制弹窗是否展示
				copyname_go: false,
				
				//更新价格和活动状态
				changePrice: 0,
				
				//下单价格提示
				exceptionProList: [],
				exState: 200,
				exStateTxt: '',
				
				rondomMod: false, //随机弹框
				rondomDes: {},
				clients: '',
				
				saveAmount:{}
			}
		},
		props: ['storeId'],
		onLoad() {
			//wx-1-start
			//#ifdef MP-WEIXIN
			this.source = 4
			//#endif
			//wx-1-end
			// #ifdef MP-BAIDU
			this.source = 3
			// #endif
			// #ifdef MP-ALIPAY
			this.source = 2
			// #endif
			// #ifdef MP-TOUTIAO
			this.source = 1
			// #endif
			console.log(this.$refs.popLogin)
		},
		computed: {
			...mapState(['hasLogin', 'userInfo', 'userCenterData', 'memberConfig']),
			
			activityState() {
				let {
					secKillInfo,
					ladderInfo,
					preSellInfo,
					pinInfo
				} = this
				return (
					JSON.stringify(secKillInfo) != '{}' ||
					JSON.stringify(ladderInfo) != '{}' ||
					JSON.stringify(preSellInfo) != '{}' ||
					JSON.stringify(pinInfo) != '{}'
				)
			},
			actiState() {
				let now = new Date().getTime()
				switch (this.defaultProduct.promotionType) {
					case 104: {
						return this.valiInfo(this.secKillInfo) && this.secKillInfo.state == 2
						break
					}
					case 103: {
						let st = new Date(this.preSellInfo.startTime).getTime()
						let et = new Date(this.preSellInfo.endTime).getTime()
						return this.valiInfo(this.preSellInfo) && now > st && et > now
						break
					}
					case 105: {
						let st = new Date(this.ladderInfo.startTime).getTime()
						let et = new Date(this.ladderInfo.endTime).getTime()
						return this.valiInfo(this.ladderInfo) && now > st && et > now
						break
					}
					case 102: {
						let st = new Date(this.pinInfo.startTime).getTime()
						let et = new Date(this.pinInfo.endTime).getTime()
						return this.valiInfo(this.pinInfo) && now > st && et > now
						break
					}
				}
			}
		},
		methods: {
			confirmLogin() {
				let url = this.$Route.path
				const query = this.$Route.query
				this.$refs.popLogin.close()
				uni.setStorageSync('fromurl', {
					url,
					query
				})
				this.$Router.push('/pages/public/login')
			},
			showSpcPop(goods_info){
				this.goodsId = goods_info.goodsId;
				this.productId = goods_info.defaultProductId;
				this.getGoodsDetailDynamic()
				this.$refs.specModel.open()
			},
			//确认下单
			buy(arg) {
				if (!this.hasLogin) {
					this.$refs.popLogin.open()
					return
				} else {
					if(!this.defaultProduct.productStock){
						this.$api.msg('商品总库存不足')
						return
					}
					this.editNum('edit')
					this.$refs.specModel.close()
					
					let data = {
						isCart: false,
						productId: this.productId,
						number: this.currentSpecNum,
						source: 1,
						productPrice: (this.userCenterData.isSuper == 1&&this.defaultProduct.superPrice) ? this.defaultProduct.superPrice : this.changePrice,
						isPickup: this.isPickup
					}
					
			
					let {
						promotionId,
						promotionType
					} = this.defaultProduct
			
					if (promotionId && promotionType && this.actiState) {
						data.promotionId = promotionId
						data.promotionType = promotionType
					}
			
					if (this.valiInfo(this.pinInfo) && this.pinButState == 0) {
						data.productPrice = this.defaultProduct.productPrice
						delete data.promotionId
						delete data.promotionType
					}
			
					if (this.valiInfo(this.pinInfo) && this.pinButState == 1) {
						data.productPrice =
							this.pinInfo.leaderIsPromotion == 1 ?
							this.pinInfo.leaderPrice :
							this.pinInfo.spellPrice
					}
			
					if (this.valiInfo(this.pinInfo) && this.pinButState == 3) {
						data.spellTeamId = this.spellTeamId
					} else {
						delete data.spellTeamId
					}
			
					let param = {
						url: 'v3/business/front/orderOperate/check',
						method: 'POST',
						header: {
							'Content-Type': 'application/json'
						},
						data
					}
			
					if (typeof arg == 'string' && arg == 'aloneBuy') {
						param.data.isAloneBuy = true
					}
					uni.showLoading()
					uni.setStorageSync('addressId', this.sourceId)
					this.$request(param).then((res) => {
						uni.hideLoading()
						if (res.state == 200) {
							//从这里跳转页面后置为1，从一个页面返回回来，将在onShow里调用getGoodsDetail
							this.showState = 1
							let query = {
								orderType: 1,
								goodsId: this.goodsId,
								productId: this.productId,
								numbers: this.currentSpecNum,
								ifcart: 2,
								isPickup: this.isPickup
							}
							if (this.valiInfo(this.pinInfo) && this.pinButState == 0) {
								query.isAloneBuy = true
							} else if (this.valiInfo(this.pinInfo) && this.pinButState == 1) {
								query.isAloneBuy = false
							} else if (this.valiInfo(this.pinInfo) && this.pinButState == 3) {
								query.isAloneBuy = false
								query.spellTeamId = this.spellTeamId
							}
			
							this.$Router.push({
								path: '/order/confirmOrder',
								query
							})
						} else if (res.state == 267) {
							this.exceptionProList = res.data.productList
							this.exState = res.data.state
							this.exStateTxt = res.data.stateValue
							if (this.exState == 7) {
								this.$refs.purchasePop.open(0)
							} else if (this.exState == 5) {
								this.$refs.purchasePop.open(1)
							} else {
								this.$api.msg(res.data.stateValue)
							}
						} else {
							this.$api.msg(res.msg)
						}
					})
				}
			},
			
			//加入购物车功能
			addCart() {
				const that = this
				if (!this.hasLogin) {
					//-----------start---------
					if(this.isPickup){ // 自提
						let specValues = '' // 存储商品多规格值
						if (this.specs && this.specs.length > 0) {
							this.specs.forEach((item) => {
								item.specValueList.forEach((items) => {
									if (items.checkState == 1) {
										specValues += items.specValue + ' '
									}
								})
							})
						}
						let cart_list = {
							storeCartGroupList: [{
								promotionCartGroupList: [{
									cartList: [{
										buyNum: this.currentSpecNum,
										goodsId: this.goodsId - 0,
										productId: this.productId - 0,
										productImage: this.defaultProduct.goodsPics[0],
										goodsName: this.goodsData.goodsName,
										isChecked: 0,
										productPrice: this.defaultProduct.productPrice,
										productStock: this.defaultProduct.productStock,
										specValues: specValues
									}]
								}],
								storeId: this.storeInf.storeId,
								storeName: this.storeInf.storeName,
								checkedAll: false
							}],
							checkedAll: false,
							invalidList: []
						}
						//未登录加入本地缓存
						let local_cart_list = uni.getStorageSync('cart_list_Pickup') //购物车列表本地缓存
						if (local_cart_list) {
							//如果不是第一次存储
							let tmp_list1 = []
							let tmp_list2 = []
							cart_list.storeCartGroupList.forEach((item) => {
								item.promotionCartGroupList.forEach((item1) => {
									item1.cartList.forEach((item2) => {
										local_cart_list.storeCartGroupList.forEach((v) => {
											v.promotionCartGroupList.forEach((v1) => {
												v1.cartList.forEach((v2) => {
													if (
														v2.productId == item2
														.productId &&
														v.storeId == item
														.storeId
													) {
														tmp_list1.push(v)
													}
												})
											})
										})
										tmp_list2 = local_cart_list.storeCartGroupList.filter((v) => {
											return v.storeId == item.storeId
										})
									})
								})
							})
							if (tmp_list1.length > 0 && tmp_list2.length > 0) {
								//同一店铺同一商品
								local_cart_list.storeCartGroupList.map((item) => {
									item.promotionCartGroupList.map((item1) => {
										item1.cartList.map((item2) => {
											if (
												item2.productId == this.productId &&
												this.storeInf &&
												item.storeId == this.storeInf.storeId
											) {
												item2.buyNum += this.currentSpecNum
											}
										})
									})
								})
							} else if (tmp_list1.length == 0 && tmp_list2.length > 0) {
								//同一店铺不同商品
								local_cart_list.storeCartGroupList.map((item) => {
									if (item.storeId == this.storeInf.storeId) {
										item.promotionCartGroupList.map((item2) => {
											item2.cartList.push(
												cart_list.storeCartGroupList[0].promotionCartGroupList[0]
												.cartList[0]
											)
										})
									}
								})
							} else {
								//不同店铺不同商品
								local_cart_list.storeCartGroupList.push(
									cart_list.storeCartGroupList[0]
								)
							}
						
							// 未登录购物车展示数据
							uni.setStorage({
								key: 'cart_list_Pickup',
								data: local_cart_list,
								success: function() {
									//更新购物车数量和购物车数据
								}
							})
						} else {
							uni.setStorage({
								key: 'cart_list_Pickup',
								data: cart_list,
								success: function() {
									//更新购物车数量和购物车数据
								}
							})
						}
						uni.showToast({
							title: this.$L('加入购物车成功！'),
							icon: 'none',
							duration: 700
						})
						this.$sldStatEvent({
							behaviorType: 'cart',
							goodsId: this.goodsId,
							storeId: this.storeInf.storeId
						})
						this.$refs.specModel.close()
						this.$emit('getPreCartNum')
					} else { // 邮寄
						let specValues = '' // 存储商品多规格值
						if (this.specs && this.specs.length > 0) {
							this.specs.forEach((item) => {
								item.specValueList.forEach((items) => {
									if (items.checkState == 1) {
										specValues += items.specValue + ' '
									}
								})
							})
						}
						let cart_list = {
							storeCartGroupList: [{
								promotionCartGroupList: [{
									cartList: [{
										buyNum: this.currentSpecNum,
										goodsId: this.goodsId - 0,
										productId: this.productId - 0,
										productImage: this.defaultProduct.goodsPics[0],
										goodsName: this.goodsData.goodsName,
										isChecked: 1,
										productPrice: this.defaultProduct.productPrice,
										productStock: this.defaultProduct.productStock,
										specValues: specValues
									}]
								}],
								storeId: this.storeInf.storeId,
								storeName: this.storeInf.storeName,
								checkedAll: true
							}],
							checkedAll: true,
							invalidList: []
						}
						//未登录加入本地缓存
						let local_cart_list = uni.getStorageSync('cart_list_Email') //购物车邮寄列表本地缓存
						if (local_cart_list) {
							//如果不是第一次存储
							let tmp_list1 = []
							let tmp_list2 = []
							cart_list.storeCartGroupList.forEach((item) => {
								item.promotionCartGroupList.forEach((item1) => {
									item1.cartList.forEach((item2) => {
										local_cart_list.storeCartGroupList.forEach((v) => {
											v.promotionCartGroupList.forEach((v1) => {
												v1.cartList.forEach((v2) => {
													if (
														v2.productId == item2
														.productId &&
														v.storeId == item
														.storeId
													) {
														tmp_list1.push(v)
													}
												})
											})
										})
										tmp_list2 = local_cart_list.storeCartGroupList.filter((v) => {
											return v.storeId == item.storeId
										})
									})
								})
							})
							if (tmp_list1.length > 0 && tmp_list2.length > 0) {
								//同一店铺同一商品
								local_cart_list.storeCartGroupList.map((item) => {
									item.promotionCartGroupList.map((item1) => {
										item1.cartList.map((item2) => {
											if (
												item2.productId == this.productId &&
												this.storeInf &&
												item.storeId == this.storeInf.storeId
											) {
												item2.buyNum += this.currentSpecNum
											}
										})
									})
								})
							} else if (tmp_list1.length == 0 && tmp_list2.length > 0) {
								//同一店铺不同商品
								local_cart_list.storeCartGroupList.map((item) => {
									if (item.storeId == this.storeInf.storeId) {
										item.promotionCartGroupList.map((item2) => {
											item2.cartList.push(
												cart_list.storeCartGroupList[0].promotionCartGroupList[0]
												.cartList[0]
											)
										})
									}
								})
							} else {
								//不同店铺不同商品
								local_cart_list.storeCartGroupList.push(
									cart_list.storeCartGroupList[0]
								)
							}
						
							// 未登录购物车展示数据
							uni.setStorage({
								key: 'cart_list_Email',
								data: local_cart_list,
								success: function() {
									//更新购物车数量和购物车数据
								}
							})
						} else {
							uni.setStorage({
								key: 'cart_list_Email',
								data: cart_list,
								success: function() {
									//更新购物车数量和购物车数据
								}
							})
						}
						uni.showToast({
							title: this.$L('加入购物车成功！'),
							icon: 'none',
							duration: 700
						})
						this.$sldStatEvent({
							behaviorType: 'cart',
							goodsId: this.goodsId,
							storeId: this.storeInf.storeId
						})
						this.$refs.specModel.close()
						this.$emit('getPreCartNum')
					}
					//------------over-------------
				} else {
					//已登录
					this.$request({
						url: 'v3/business/front/cart/add',
						data: {
							storeId: this.storeId,
							productId: this.productId,
							number: this.currentSpecNum,
							isPickup: this.isPickup
						},
						method: 'POST'
					}).then((res) => {
						if (res.state == 200) {
							//更新购物车数量
							this.$refs.specModel.close()
							this.$sldStatEvent({
								behaviorType: 'cart',
								goodsId: this.goodsId,
								storeId: this.storeInf.storeId
							})
							this.$api.msg(res.msg)
							this.$emit('getPreCartNum')
						} else {
							this.$api.msg(res.msg)
						}
					})
				}
			},
		
			//编辑数量
			editNum(type, e) {
				let that = this
				let reg = /\./g
				let reg0 = /0+\d/
			
				if (
					reg.test(this.currentSpecNum.toString()) ||
					this.currentSpecNum <= 0
				) {
					setTimeout(() => {
						that.currentSpecNum = 1
					}, 0)
				}
			
				if (type == 'add') {
					if (this.actiState) {
						that.activityAddEdit('add')
					} else {
						let productStock =this.defaultProduct.productStock
						let limit = Math.min(productStock, 99999)
						this.currentSpecNum++
						if (this.currentSpecNum > limit) {
							this.currentSpecNum = limit
							this.noEdit = true
						}
					}
				} else if (type == 'edit') {
					if (that.currentSpecNum == '' || that.currentSpecNum < 0) {
						setTimeout(() => {
							that.currentSpecNum = 1
						}, 0)
						return
					}
					if (this.actiState) {
						that.activityAddEdit('edit')
					} else {
						if (that.currentSpecNum > that.defaultProduct.productStock) {
							setTimeout(() => {
								this.currentSpecNum =that.defaultProduct.productStock
							}, 0)
							that.noEdit = true
							return
						} else {
							that.currentSpecNum =
								e && e.detail.value ? e.detail.value : that.currentSpecNum
							if (that.currentSpecNum == 0 || that.currentSpecNum < 0) {
								setTimeout(() => {
									that.currentSpecNum = 1
								}, 0)
								return
							} else {
								that.currentSpecNum = that.currentSpecNum
									.toString()
									.replace(/\D/g, '')
								if (that.currentSpecNum > 99999) {
									setTimeout(() => {
										that.currentSpecNum = 99999
									}, 0)
									that.noEdit = true
									return
								} else {
									setTimeout(() => {
										that.currentSpecNum = Number(that.currentSpecNum)
									}, 0)
									that.noEdit = false
								}
							}
						}
					}
				} else if (type == 'reduce') {
					if (that.currentSpecNum > 1) {
						that.currentSpecNum--
						that.noEdit = false
					} else {
						that.currentSpecNum = 1
					}
				}
				// this.getUserSaveAmount(this.cityCode)
				// this.getUserEx(this.cityCode)
			},
			spcpopChange(e) {
				if(!e.show) {
					this.currentSpecNum = 1
				}
			},
			/**选择规格值
			 * @param {Object} type 类型   值：choice,规格选择    default:默认
			 * @param {Object} specId 父级规格值
			 * @param {Object} specValueId   点击的当前的规格值
			 */
			selectSpecVal(type, specId, specValueId) {
				let that = this
				let curParSpec = [] //当前点击的规格的父级id的当前项
				curParSpec = that.specs.filter((item) => item.specId == specId)
				let curSPec = [] //当前点击的规格的规格id的当前项
				curSPec = curParSpec[0].specValueList.filter(
					(item1) => item1.specValueId == specValueId
				)
				curSPec[0].checkState = 1
				//被选择的规格值的id
				let choiceSpecIds = []
				that.specs.forEach((item) => {
					if (item.specId != specId) {
						item.specValueList.forEach((item1) => {
							if (item1.checkState == '1') {
								// checkState: 1-选中，2-可选，3-禁用
								choiceSpecIds.push(item1.specValueId)
							}
						})
					} else {
						choiceSpecIds.push(specValueId)
					}
				})
			
				let param = {}
				param.url = 'v3/goods/front/goods/productInfo'
				param.method = 'GET'
				param.data = {}
				param.data.goodsId = that.goodsId
				param.data.specValueIds = choiceSpecIds.join(',')
				that.$request(param).then((res) => {
					if (res.state == 200) {
						let result = res.data
						this.defaultProduct = result.defaultProduct
						this.productId = result.defaultProduct.productId
						this.changePrice = this.defaultProduct.productPrice
						this.showPrice = this.defaultProduct.productPrice
						// this.deliverInfo = result.deliverInfo //地址及运费
						this.specs = result.specs //规格列表
						this.goodsData.shareLink = result.shareLink
						// this.current = 0
						this.promotionId = this.defaultProduct.promotionId //活动id
						this.defaultProduct.promotionType = result.defaultProduct.promotionType
						this.currentSpecNum = 1
						if (!this.defaultProduct.promotionType) {
							this.preSellInfo = {}
							this.ladderInfo = {}
							this.secKillInfo = {}
							this.pinInfo = {}
						} else {
							if (this.promotionId && this.defaultProduct.promotionType == 104) {
								// 秒杀
								this.getSecKill()
							} else if (this.defaultProduct.promotionType == 103) {
								// 预售
								this.getPreSell()
							} else {
								this.secKillInfo = {}
								this.preSellInfo = {}
							}
							if (this.promotionId && this.defaultProduct.promotionType == 102) {
								//拼团
								this.getPinInfo()
							} else {
								this.pinInfo = {}
							}
							if (this.promotionId && this.defaultProduct.promotionType == 105) {
								this.getLadder()
							} else {
								this.ladderInfo = {}
							}
						}
					} else {
						this.$api.msg(res.msg)
					}
				})
			},
			valiInfo(info) {
				return JSON.stringify(info) != '{}'
			},
			getGoodsDetailDynamic() {
				this.$request({
						url: 'v3/goods/front/goods/details2',
						data: {
							productId: this.productId, //货品id
							goodsId: this.goodsId, //商品id
							source: this.source
						}
					})
					.then(async (res) => {
						if (res.state == 200) {
							this.supportPickup = res.data.supportPickup || 0;
							this.supportMailing = res.data.supportMailing || 0;
							if(this.supportPickup == 1){
								this.isPickup = 1;
							} else {
								this.isPickup = 0;
							}
							let dynamicData = [
								'effectSpecValueIds',
								'followGoods',
								'sales',
								'state',
								'shareLink',
								'shareImage',
								'isVirtualGoods'
							]
							dynamicData.forEach((item) => {
								this.goodsData[item] = res.data[item]
							})
							this.defaultProduct = res.data.defaultProduct //默认货品信息
							this.changePrice = this.defaultProduct.productPrice
							this.showPrice = this.defaultProduct.productPrice
							this.specs = res.data.specs //规格列表
							this.storeInf = res.data.storeInf //店铺信息
							this.deliverInfo = res.data.deliverInfo??{} //发货地及运费信息
							this.promotionId = this.defaultProduct.promotionId //活动id
							
							if (this.promotionId && this.defaultProduct.promotionType == 104) {
								// 秒杀
								await this.getSecKill()
							} else if (this.defaultProduct.promotionType == 103) {
								// 预售
								await this.getPreSell()
							} else if (this.promotionId && this.defaultProduct.promotionType == 102) {
								//拼团
								await this.getPinInfo()
							} else if (this.promotionId && this.defaultProduct.promotionType == 105) {
								await this.getLadder()
							} else {
								this.pinInfo = {}
								this.secKillInfo = {}
								this.preSellInfo = {}
								this.ladderInfo = {}
							}
							
						} else {
							this.$api.msg(res.msg)
						}
					})
					.then(() => {
						
					})
			},
			//统一处理活动商品的数量的加及编辑
			activityAddEdit(type) {
				let that = this
			
				that.currentSpecNum = that.currentSpecNum.toString().replace(/\D/g, '')
				if (that.currentSpecNum == '' || that.currentSpecNum < 0) {
					setTimeout(() => {
						that.currentSpecNum = 1
					}, 0)
					return
				}
			
				let activityLimitNumber = 0 //活动的限购数量 0代表不限购
				let activityProductStock = this.defaultProduct.productStock //活动的库存
				
			
				if (that.secKillInfo && that.secKillInfo.state == 2) {
					//秒杀活动进行中
					activityLimitNumber = that.secKillInfo.upperLimit
				} else if (
					this.valiInfo(that.preSellInfo) &&
					that.preSellInfo.startTime
				) {
					//预售进行中
					activityLimitNumber = that.preSellInfo.buyLimit
				} else if (this.valiInfo(that.pinInfo)) {
					activityLimitNumber = that.pinInfo.buyLimit
				} else if (this.valiInfo(that.ladderInfo)) {
					activityLimitNumber = that.ladderInfo.buyLimitNum
				}
				if (activityLimitNumber < activityProductStock && activityLimitNumber > 0) {
					// 限购数 < 库存
					if (that.currentSpecNum >= activityLimitNumber) {
						setTimeout(() => {
							that.currentSpecNum = activityLimitNumber
							that.noEdit = true
						}, 0)
					} else {
						setTimeout(() => {
							type == 'add' ? that.currentSpecNum++ : that.currentSpecNum
						}, 0)
						that.noEdit = false
					}
				} else {
					//限购数 > 库存
					if (that.currentSpecNum < activityProductStock) {
						if (that.currentSpecNum == 0 || that.currentSpecNum < 0) {
							setTimeout(() => {
								that.currentSpecNum = 1
							}, 0)
						} else {
							if (that.currentSpecNum > 99999) {
								setTimeout(() => {
									that.currentSpecNum = 99999
								}, 0)
								that.noEdit = true
							} else {
								setTimeout(() => {
									type == 'add' ? that.currentSpecNum++ : that.currentSpecNum
								}, 0)
								that.noEdit = false
							}
						}
					} else {
						setTimeout(() => {
							that.currentSpecNum = activityProductStock
						}, 0)
						that.noEdit = true
					}
				}
				// this.getUserSaveAmount(this.cityCode)
				// this.getUserEx(this.cityCode)
			},
			//关闭规格弹框
			closeSpecModel() {
				this.$refs.specModel.close()
			},
			//打开规格弹框
			showSpecModel(type) {
				//如果是购买操作，并且商品总库存为0
				if (
					(type === 'buy' || typeof type !== 'string') &&
					!this.defaultProduct.productStock
				) {
					return
				}
				if (type == 'add') {
					this.showSpecModelType = 'add'
				} else if (type == 'buy') {
					this.showSpecModelType = 'buy'
				} else if (type == 'offshelf') {
					this.showSpecModelType = 'offshelf'
				} else if (type == 'nosocket') {
					this.showSpecModelType = 'nosocket'
				} else if (type == 'pinAlone' && this.valiInfo(this.pinInfo)) {
					this.pinButState = 0
				} else if (type == 'pinLeague' && this.valiInfo(this.pinInfo)) {
					this.pinButState = 1
				} else if (!type && this.valiInfo(this.pinInfo)) {
					this.pinButState = 2
				} else if (type == 'joinLeague') {
					this.pinButState = 3
				} else {
					this.showSpecModelType = ''
				}
				this.$forceUpdate()
				this.$refs.specModel.open()
			},
			
			//获取秒杀商品详情
			async getSecKill() {
				if (this.secInterval) {
					clearInterval(this.secInterval)
				}
				let param = {}
				param.url = 'v3/promotion/front/seckill/detail'
				param.method = 'GET'
				param.data = {}
				param.data.productId = this.productId
				param.data.promotionId = this.promotionId
				await this.$request(param)
					.then((res) => {
						if (res.state == 200) {
							let result = res.data
							this.secKillInfo = result
							this.productId = this.secKillInfo.productId
			
							if (this.secKillInfo.state == 2) {
								this.changePrice = this.secKillInfo.seckillPrice
								this.showPrice = this.secKillInfo.seckillPrice
							}
							// if (this.secKillInfo.state == 1 || this.secKillInfo.state == 2) {
							// 	let countTime = 0
							// 	countTime = this.secKillInfo.distanceEndTime //剩余时间	秒
							// 	this.secInterval = setInterval(() => {
							// 		if (countTime == 0) {
							// 			//倒计时结束，清除倒计时
							// 			clearInterval(this.secInterval)
							// 			this.getGoodsDetail(this.secKillInfo.productId)
							// 		} else {
							// 			countTime--
							// 			let day = parseInt(countTime / 60 / 60 / 24)
							// 			let hours = parseInt((countTime / 60 / 60) % 24)
							// 			let minutes = parseInt((countTime / 60) % 60)
							// 			let seconds = parseInt(countTime % 60)
							// 			this.secKillDay = day
							// 			this.secKillHr = hours > 9 ? hours : '0' + hours
							// 			this.secKillMin = minutes > 9 ? minutes : '0' + minutes
							// 			this.secKillSec = seconds > 9 ? seconds : '0' + seconds
							// 		}
							// 	}, 1000)
							// }
						} else {
							this.$api.msg(res.msg)
						}
					})
			},
			//获取预售商品详情
			async getPreSell() {
				let param = {}
				param.url = 'v3/promotion/front/preSell/detail'
				param.method = 'GET'
				param.data = {}
				param.data.productId = this.productId
				param.data.promotionId = this.promotionId
				await this.$request(param).then((res) => {
						if (res.state == 200) {
							let now = new Date()
							let preStartDate = new Date(res.data.startTime.replace(/-/g, '/'))
							let preEndDate = new Date(res.data.endTime.replace(/-/g, '/'))
							let result = res.data
							this.preSellInfo = result
							this.productId = this.preSellInfo.productId
							this.showPrice = this.preSellInfo.presellPrice
							this.changePrice =
								this.preSellInfo.type == 1 ?
								this.preSellInfo.firstMoney :
								this.preSellInfo.presellPrice
							    this.preSellInfo.endTime = res.data.endTime.substring(0,this.preSellInfo.endTime.length - 3)
							this.preSellInfo.startTime = res.data.startTime.substring(0,this.preSellInfo.startTime.length - 3)
							if (now > preStartDate && now < preEndDate) {
								this.preSellInfo.pre_run = 2 //活动进行中
							} else if (now < preStartDate) {
								this.preSellInfo.pre_run = 1 //活动未开始
							} else if (now > preEndDate) {
								this.preSellInfo.pre_run = 3 //活动已结束
							}
						} else {
							this.$api.msg(res.msg)
						}
					})
			},
			//获取拼团商品信息
			async getPinInfo() {
				if (this.secInterval) {
					clearInterval(this.secInterval)
				}
				let param = {}
				param.url = 'v3/promotion/front/spell/detail'
				param.method = 'GET'
				param.data = {}
				param.data.productId = this.productId
				param.data.promotionId = this.promotionId
				await this.$request(param).then((res) => {
					if (res.state == 200) {
						let result = res.data
						this.pinInfo = result
						this.changePrice = this.pinInfo.spellPrice
						this.showPrice = this.pinInfo.spellPrice
						// if (this.showState == 1) {
						// 	this.$refs.pinGroup.getPinTeam()
						// }
					} else {
						this.$api.msg(res.msg)
					}
				})
			},
			// 阶梯团商品详情
			async getLadder() {
				if (this.secInterval) {
					clearInterval(this.secInterval)
				}
				let param = {}
				param.url = 'v3/promotion/front/ladder/group/detail'
				param.method = 'GET'
				param.data = {}
				param.data.productId = this.productId
				param.data.promotionId = this.promotionId
				await this.$request(param)
					.then((res) => {
						if (res.state == 200) {
							let result = res.data
							this.ladderInfo = result
							this.productId = this.ladderInfo.productId
							this.changePrice = this.ladderInfo.advanceDeposit
							this.showPrice = this.ladderInfo.currentPrice
							this.ladderProcess =
								result.joinedNum <
								result.ruleList[result.ruleList.length - 1].joinGroupNum ?
								(result.joinedNum /
									result.ruleList[result.ruleList.length - 1].joinGroupNum) *
								100 :
								100
							let now = new Date()
							let start = new Date(this.ladderInfo.startTime.replace(/-/g, '/'))
							let end = new Date(this.ladderInfo.endTime.replace(/-/g, '/'))
							if (now < start) {
								this.ladderInfo.ladder_run = 1
							} else if (now > start && now < end) {
								this.ladderInfo.ladder_run = 2
							} else {
								this.ladderInfo.ladder_run = 3
							}
						} else {
							this.$api.msg(res.msg)
						}
					})
					.catch((e) => {
						//异常处理
					})
			},
			
		}
	}
</script>

<style lang="scss" scoped>
	.spec_model_con {
		width: 750rpx;
		height: 900rpx;
		background: #ffffff;
		border-radius: 10rpx 10rpx 0;
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		margin: 0 auto;
		z-index: 150;
	
		.spec_model_content {
			padding-bottom: 115rpx;
	
			.spec_model_top {
				display: flex;
				justify-content: space-between;
				padding: 30rpx 22rpx 0 30rpx;
				box-sizing: border-box;
	
				.spec_model_goods {
					display: flex;
					height: 151rpx;
					/* align-items: center; */
	
					.spec_goods_image {
						width: 151rpx;
						height: 151rpx;
						background: #eeeeee;
						border-radius: 15rpx;
	
						image {
							width: 151rpx;
							height: 151rpx;
							border-radius: 15rpx;
						}
					}
	
					.spec_goods_right {
						margin-left: 30rpx;
						flex-shrink: 0;
	
						.spec_goods_price_con {
							display: flex;
							align-items: center;
	
							.spec_prices {
								.spec_goods_price {
									margin-right: 18rpx;
									text {
										font-size: 24rpx;
										font-family: PingFang SC;
										font-weight: 500;
										color: var(--color_price);
									}
	
									text:nth-child(2) {
										font-size: 50rpx;
									}
								}
							}
	
							.sec_kill_tips {
								width: 130rpx;
								height: 40rpx;
								background: var(--color_seckill_main_bg);
								border-radius: 20rpx;
								font-size: 24rpx;
								font-family: PingFang SC;
								font-weight: 500;
								color: #ffffff;
								text-align: center;
								line-height: 40rpx;
								margin-left: 20px;
							}
	
							.pre_sale_tips {
								width: 76rpx;
								height: 38rpx;
								background: var(--color_presell_main_bg);
								border-radius: 18rpx;
								font-size: 22rpx;
								font-family: PingFang SC;
								font-weight: 500;
								color: #fff;
								display: flex;
								align-items: center;
								justify-content: center;
								margin-left: 20px;
							}
	
							.ladder_regiment_tips {
								width: 100rpx;
								height: 40rpx;
								background: var(--color_ladder_main_bg);
								border-radius: 20rpx;
								font-size: 24rpx;
								font-family: PingFang SC;
								font-weight: 500;
								color: #ffffff;
								display: flex;
								align-items: center;
								justify-content: center;
								margin-left: 20rpx;
							}
	
							.pin_tips {
								width: 80rpx;
								height: 40rpx;
								background: var(--color_spell_main_bg);
								border-radius: 20rpx;
								font-size: 24rpx;
								font-family: PingFang SC;
								font-weight: 500;
								color: #ffffff;
								display: flex;
								align-items: center;
								justify-content: center;
								margin-left: 20rpx;
							}
						}
	
						.spec_goods_des {
							font-size: 28rpx;
							font-family: PingFang SC;
							font-weight: 500;
							color: #343434;
							margin-top: 19rpx;
							width: 520rpx;
							/* white-space: nowrap;
							text-overflow: ellipsis;
							overflow: hidden; */
							word-break: break-all;
						}
					}
				}
	
				.close_spec {
					position: absolute;
					top: 14rpx;
					right: 14rpx;
					z-index: 9;
					width: 46rpx;
					height: 46rpx;
				}
			}
	
			.spec_content {
				height: 620rpx;
	
				.spec_list {
					margin: 0 30rpx;
					padding-top: 34rpx;
	
					.spec_list_pre {
						border-bottom: 1rpx solid #f5f5f5;
	
						.spec_list_pre_name {
							font-size: 28rpx;
							font-family: PingFang SC;
							font-weight: 500;
							color: #666666;
							margin-bottom: 30rpx;
						}
	
						.spec_list_pre_desc {
							display: inline-table;
							padding: 13rpx 25rpx;
							box-sizing: border-box;
							box-sizing: border-box;
							background: #f5f5f5;
							border-radius: 50rpx;
							margin-bottom: 30rpx;
							margin-right: 30rpx;
							border: 1rpx solid #f5f5f5;
	
							.spec_list_pre_con {
								display: flex;
								align-items: center;
	
								text {
									font-size: 26rpx;
									font-family: PingFang SC;
									font-weight: 500;
									color: #343434;
									text-align: left;
								}
	
								image {
									width: 36rpx;
									height: 36rpx;
									margin-right: 20rpx;
								}
							}
						}
	
						.spec_list_pre_desc_active {
							background: #ffffff;
							border: 1rpx solid var(--color_main);
	
							.spec_list_pre_con {
								text {
									color: var(--color_main);
								}
							}
						}
	
						.spec_list_pre_desc_disabled {
							background: #f5f5f5;
							opacity: 0.2;
	
							.spec_list_pre_con {
								text {
									color: #2d2d2d;
								}
							}
						}
					}
				}
				.delivery_type{
						font-size: 28rpx;
					    font-family: PingFang SC;
					    font-weight: 500;
					    color: #666666;
						padding-left: 30rpx;
						margin-top: 16rpx;
					.delivery_types{
						display: flex;
						margin-top: 26rpx;
						.delivery_type_item{
							width: 162rpx;
							height: 60rpx;
							line-height: 60rpx;
							border-radius: 32rpx;
							font-size: 28rpx;
							text-align: center;
							margin-right: 20rpx;
							background-color: #F5F5F5;
							color: #000000;
						}
						.delivery_type_item_active{
							background-color: #fff;
							color: rgba(215, 81, 95, 1);
							border: 2rpx solid rgba(215, 81, 95, 1);
						}
					}
				}
				.spec_num {
					display: flex;
					justify-content: space-between;
					align-items: flex-start;
					padding: 0 20rpx 0 30rpx;
					box-sizing: border-box;
					margin-top: 16rpx;
					padding-bottom: 28rpx;
					border-bottom: 2rpx solid #EFEFEF;
					.spec_num_left {
						font-size: 28rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #666666;
	
						text {
							color: #949494;
						}
					}
	
					.spec_num_right {
						width: 220rpx;
						height: 60rpx;
						border: 1rpx solid #E5E5E5;
						display: flex;
						justify-content: center;
						align-items: center;
						font-size: 24rpx;
						font-family: PingFang SC;
						font-weight: bold;
						color: #A6A6A6;
						line-height: 30rpx;
						border-radius: 30rpx;
						overflow: hidden;
	
						text {
							width: 60rpx;
							height: 60rpx;
							text-align: center;
							line-height: 60rpx;
							border-left: 1px solid #E5E5E5;
							font-size: 34rpx;
							color: #333;
	
							&.no_edit {
								background: #EDEDED;
								opacity: 0.5;
								color: #949494;
							}
						}
	
						text:nth-child(1) {
							color: #949494;
							border-right: 1rpx solid #E5E5E5;
							border-left: none;
						}
	
						input {
							width: 88rpx;
							height: 50rpx;
							line-height: 50rpx;
							text-align: center;
							font-size: 24rpx;
							/* #ifdef MP-ALIPAY */
							border-top: 1rpx solid #E5E5E5;
							border-bottom: 1rpx solid #E5E5E5;
							/* #endif */
							font-size: 28rpx;
							color: #2D2D2D;
						}
					}
	
	
					.buyLimit {
						color: #e2231a;
						font-size: 24rpx;
					}
				}
			}
		}
	
		.spec_btn {
			width: 750rpx;
			height: 98rpx;
			background: #ffffff;
			box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(86, 86, 86, 0.08);
			display: flex;
			align-items: center;
			justify-content: center;
			position: fixed;
			bottom: env(safe-area-inset-bottom);
			/* #ifdef MP */
			/* height: calc(98rpx + env(safe-area-inset-bottom)); */
			/* padding-bottom: constant(safe-area-inset-bottom); */
			/*兼容 IOS<11.2*/
			/* padding-bottom: env(safe-area-inset-bottom); */
			/*兼容 IOS>11.2*/
			/* #endif */
			color: #fff;
	
			.spec_add_cart_btn {
				width: 345rpx;
				height: 70rpx;
				background: var(--color_vice_bg);
				border-radius: 35rpx 0 0 35rpx;
				font-size: 30rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #ffffff;
				text-align: center;
				line-height: 70rpx;
			}
	
			.spec_buy_btn {
				width: 345rpx;
				height: 70rpx;
				background: var(--color_main);
				border-radius: 0 35rpx 35rpx 0;
				font-size: 30rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #ffffff;
				text-align: center;
				line-height: 70rpx;
			}
	
			.spec_buy_btn_seck {
				background: var(--color_seckill_main);
			}
	
			.spec_not_stock {
				background: #adadad;
				border-radius: 35rpx;
				font-size: 30rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #ffffff;
				text-align: center;
				line-height: 70rpx;
			}
	
			.spec_seckill_btn {
				background: linear-gradient(45deg, #fc2d2d 0%, #fd572b 100%);
				border-radius: 35rpx;
				font-size: 30rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #ffffff;
				text-align: center;
				line-height: 70rpx;
			}
	
			.spec_seckill_btn_seck {
				background: var(--color_seckill_main_bg);
			}
	
			.spec_btn_only {
				width: 690rpx;
				height: 70rpx;
				border-radius: 35rpx;
				text-align: center;
				line-height: 70rpx;
			}
	
			.specifications_btn2 {
				width: 690rpx;
				height: 70rpx;
				background: var(--color_ladder_main_bg);
				border-radius: 35rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #ffffff;
				line-height: 32rpx;
			}
	
			.specifications_bottom_btn3 {
				width: 690rpx;
				height: 70rpx;
				background: var(--color_spell_vice);
				border-radius: 35rpx;
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #ffffff;
				line-height: 30rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
	
			.specifications_bottom_btn4 {
				width: 690rpx;
				height: 70rpx;
				background: var(--color_spell_main_bg);
				border-radius: 35rpx;
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #ffffff;
				line-height: 30rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
	
			.specification_add {
				width: 347rpx;
				height: 70rpx;
				background: var(--color_spell_vice);
				border-radius: 34rpx 0 0 34rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				color: #fff;
				font-size: 28rpx;
			}
	
			.specification_add text:nth-of-type(1),
			.specification_buy text:nth-of-type(1) {
				margin-right: 20rpx;
			}
	
			.spec_deposit_btn {
				color: #fff;
				background: var(--color_presell_vice);
			}
	
			.specification_buy {
				width: 343rpx;
				height: 70rpx;
				background: var(--color_spell_main);
				border-radius: 0 34rpx 34rpx 0;
				display: flex;
				align-items: center;
				justify-content: center;
				color: #fff;
				font-size: 28rpx;
			}
		}
		
	}
</style>
