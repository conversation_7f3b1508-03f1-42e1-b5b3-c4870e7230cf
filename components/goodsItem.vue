<!-- 商品组件：竖直方向点击进入商品详情页加入购物车事件-->
<template name="goodsItemV">
    <view
        class="goods_v_item flex_column_start_start"
        @click="goGoodsDetail(goods_info)"
        :style="{
            width: 'calc((750rpx - ' + page_margin * 4 + 'rpx - ' + goods_margin * 2 + 'rpx)/2)',
            borderRadius: border_radius + 'px',
            border: border_style == 'border_eee' ? '1rpx solid #eee' : '',
            boxShadow: border_style == 'card-shadow' ? 'rgba(93, 113, 127, 0.08) 0px 2px 8px' : ''
        }"
    >
        <view class="goods_desc_top" :style="{ height: height * 2 + 'rpx' }">
            <view
                class="goods-img"
                :style="{
                    backgroundImage: 'url(' + (goods_info.mainImage || goods_info.goodsImage) + ')',
                    width: 'calc((750rpx - ' + page_margin * 4 + 'rpx - ' + goods_margin * 2 + 'rpx)/2)',
                    borderTopLeftRadius: border_radius * 2 + 'rpx',
                    borderTopRightRadius: border_radius * 2 + 'rpx'
                }"
            ></view>
            <text class="goods-name">{{ goods_info.goodsName }}</text>
        </view>
        <view :class="show_sale == true ? 'goods-price' : 'goods-price have_no_sale'" :style="{ marginTop: isIos ? '26rpx' : 0 }">
            <view class="left">
                <text class="unit">{{ $L('￥') }}</text>
                <text class="price_int">
                    {{
                        goods_info.goodsPrice >= 0
                            ? $getPartNumber(goods_info.goodsPrice, 'int')
                            : goods_info.productPrice >= 0
                            ? $getPartNumber(goods_info.productPrice, 'int')
                            : $getPartNumber(goods_info.marketPrice, 'int')
                    }}
                </text>
                <text class="price_decimal">
                    {{
                        goods_info.goodsPrice >= 0
                            ? $getPartNumber(goods_info.goodsPrice, 'decimal')
                            : goods_info.productPrice >= 0
                            ? $getPartNumber(goods_info.productPrice, 'decimal')
                            : $getPartNumber(goods_info.marketPrice, 'decimal')
                    }}
                </text>
            </view>
            <view class="pre_bottom" :class="show_sale == true ? 'goods_item_bottom' : ''">
                <view class="have_sold_text" v-if="show_sale == true">{{ $L('已售') }}{{ goods_info.actualSales >= 0 ? goods_info.actualSales : goods_info.saleNum }}件</view>
            </view>
        </view>
    </view>
</template>

<script>
import { mapState } from 'vuex';
export default {
    name: 'goodsItemV',
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            icon2: getApp().globalData.imgUrl + 'index/add2.png',
            icon3: getApp().globalData.imgUrl + 'index/add.png',
            icon4: getApp().globalData.imgUrl + 'index/add3.png',
            icon5: getApp().globalData.imgUrl + 'index/stop.png',
            icon_url: '', //加车图标
            goods_pic: '', //商品图片
            goods_sale: '', //销量
            isIos: uni.getDeviceInfo().platform == 'ios' //是否ios手机
        };
    },
    computed: {
        ...mapState(['userInfo'])
    },
    props: {
        goods_info: {
            type: Object,
            value: {}
        },
        icon_type: {
            type: Number
        },
        show_sale: {
            type: Boolean
        },
        border_radius: {
            type: Number
        },
        border_style: {
            type: String
        },
        // 商品边距
        goods_margin: {
            type: Number,
            default: 10
        },
        // 页面边距
        page_margin: {
            type: Number,
            default: 10
        },
        height: {
            type: Number,
            default: 258
        },
        transType: {
            type: String,
            default: ''
        }
    },
    onLoad() {},
    methods: {
        //进入商品详情页
        goGoodsDetail(goods_info) {
            let path = '/standard/product/detail';
            this.$Router.push({
                path,
                query: {
                    productId: goods_info.productId || goods_info.defaultProductId,
                    goodsId: goods_info.goodsId
                }
            });
        },
        // 加入购物车
        showSpcPop(goods_info) {
            this.$emit('addCart', goods_info);
        },

        changeImg(val) {
            return val.mainImage ? val.mainImage : val.goodsImage;
        },
        saleNum(val) {
            if (val.actualSales) {
                return val.actualSales;
            } else {
                return val.saleNum;
            }
            // return val.actualSales?val.actualSales:val.saleNum
        }
    }
};
</script>

<style lang="scss">
.goods_v_item {
    background: transparent;
    border-radius: 20rpx;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    & {
        margin-right: 0 !important;
    }

    .goods_desc_top {
        height: 516rpx;
        width: 100%;
        .goods-img {
            background-size: cover;
            background-position: center center;
            background-repeat: no-repeat;
            height: calc((750rpx - 60rpx) / 2);
            overflow: hidden;
            background-color: #fff;
        }

        .goods-name {
            height: 173rpx;
            margin-top: 20rpx;
            font-size: 28rpx;
            color: $com-main-font-color;
            line-height: 40rpx;
            height: 80rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            word-break: break-word;
            padding: 0;
        }

        .goods_des {
            font-size: 24rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #888888;
            line-height: 36rpx;
            padding: 0 20rpx;
            box-sizing: border-box;
            width: 355rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }

    .goods-price {
        padding: 0 20rpx;
        width: 100%;
        display: flex;
        align-items: center;
        flex-direction: column;

        .left {
            width: 100%;
            color: var(--color_price);

            .unit,
            .price_decimal {
                font-size: 24rpx;
                margin-right: 3rpx;
                font-weight: bold;
            }

            .price_int {
                font-size: 34rpx;
                line-height: 34rpx;
                font-weight: bold;
            }
        }

        image {
            width: 42rpx;
            height: 42rpx;
        }
    }
}

.goods_item_bottom {
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin-top: 20rpx;
    padding-bottom: 20rpx;
}

.have_sold_text {
    font-size: 24rpx;
    color: #9a9a9a;
}

.pre_bottom {
    display: flex;
    align-items: center;
}

.have_no_sale {
    width: 100%;
    flex-direction: row !important;
    justify-content: space-between !important;
    padding: 20rpx !important;
    padding-bottom: 26rpx !important;
}
</style>
