<template>
    <view class="explore-card" :style="{ backgroundColor: bgColor }">
        <view class="card-header">
            <view class="user-info" @tap="goLiveUserCenter">
                <image class="avatar" :src="authorDetail.memberAvatar" mode="aspectFill" />
                <view class="username">{{ authorDetail.memberNickname }}</view>
                <view class="level">
                    <image class="vip-image" :src="imgUrl + 'user/vip-' + (authorDetail.memberLevel ? authorDetail.memberLevel : '1') + '.png'" mode="aspectFit" />
                </view>
            </view>
            <view class="follow-block" v-if="!authorDetail.isFollow">
                <view class="follow_btn" type="info" @click="collect">关注</view>
            </view>
        </view>
    </view>
</template>

<script>
import { mapState } from 'vuex';
export default {
    name: 'AuthorItem',
    props: {
        detail: {
            type: Object,
            default: () => ({})
        },
        bgColor: {
            type: String,
            default: '#fff'
        }
    },
    emits: ['changeFollow'],
    data() {
        return {
            authorDetail: {}, // 作者详情
            imgUrl: getApp().globalData.imgUrl // 图片前缀
        };
    },
    computed: {
        ...mapState(['hasLogin'])
    },
    watch: {
        detail: {
            handler(newVal) {
                // console.log('detail changed:', newVal);
                this.authorDetail = newVal;
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        //进入作者页面
        goLiveUserCenter() {
            let author_id = this.authorDetail.authorId;
            let page = getCurrentPages();
            let len = page.length;
            if (len > 4) {
                this.$Router.replace({
                    path: '/extra/user/my',
                    query: {
                        author_id
                    }
                });
            } else {
                this.$Router.push({
                    path: '/extra/user/my',
                    query: {
                        author_id
                    }
                });
            }
        },
        //关注、取消关注事件
        collect() {
            if (this.hasLogin) {
                let { authorId } = this.authorDetail;
                let param = {};
                param.data = {};
                param.method = 'POST';
                param.data.authorId = authorId;
                if (this.authorDetail.isFollow) {
                    param.url = 'v3/video/front/video/cancelFollow';
                } else {
                    param.url = 'v3/video/front/video/followAuthor';
                }
                this.$request(param).then((res) => {
                    if (res.state == 200) {
                        this.authorDetail.isFollow = this.authorDetail.isFollow == true ? false : true;
                        this.$emit('changeFollow', this.authorDetail);
                    } else {
                        uni.showToast({
                            title: res.msg,
                            icon: 'none'
                        });
                    }
                });
            } else {
                getApp().globalData.goLogin(this.$Route);
            }
        }
    }
};
</script>
<style lang="scss">
.explore-card {
    border-radius: 40rpx;
    padding: 25rpx 20rpx;
    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .user-info {
            display: flex;
            align-items: center;

            .avatar {
                width: 60rpx;
                height: 60rpx;
                border-radius: 50%;
                margin-right: 15rpx;
                border: 2px solid rgba(204, 204, 204, 1);
            }

            .username {
                font-size: $fs-base;
                font-weight: 500;
                margin-right: 15rpx;
            }
            .level {
                padding: 0 20rpx;
                height: 40rpx;
                border-radius: 20rpx;
                border: 1px solid #000;
                display: flex;
                align-items: center;
                justify-content: center;
                .vip-image {
                    width: 30rpx;
                    height: 30rpx;
                }
            }
        }
        .follow-block {
            display: flex;
            justify-content: center;
            align-items: center;

            .follow_btn {
                background-color: transparent;
                color: #000;
                border: 1px solid rgba(89, 87, 87, 1);
                border-radius: 25rpx;
                padding: 0 30rpx;
                font-size: 24rpx;
                text-align: center;
                cursor: pointer;
                height: 50rpx;
                line-height: 50rpx;
            }
        }
    }
}
</style>
