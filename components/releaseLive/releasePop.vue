<template>
    <uni-popup ref="releaseModal" type="bottom" :clickMask="true">
        <view :class="{ content: true }" :style="{ paddingBottom: offsetBottom }">
            <view class="detail">
                <view @tap="releaseLive('graphic')" class="item">
                    <image class="release_icon" :src="imgUrl + 'svideo/release_img.png'"></image>
                    <text>{{ '发布图文' }}</text>
                </view>
                <view @tap="releaseLive('video')" class="item">
                    <image class="release_icon" :src="imgUrl + 'svideo/release_video.png'"></image>
                    <text>{{ $L('发布短视频') }}</text>
                </view>
            </view>
        </view>
    </uni-popup>
</template>
<script>
import uniPopup from '@/components/uni-popup/uni-popup.vue';
export default {
    components: {
        uniPopup
    },
    props: {
        offsetBottom: {
            type: String,
            default: '0rpx'
        }
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl
        };
    },

    methods: {
        open() {
            this.$refs.releaseModal.open();
        },
        //
        releaseLive(type) {
            this.$refs.releaseModal.close();
            if (type === 'graphic') {
                this.$Router.push('/extra/graphic/graphicRelease');
            } else if (type === 'video') {
                this.$Router.push('/extra/svideo/svideoRelease');
            }
        }
    }
};
</script>

<style lang="scss">
.content {
    width: 100%;
    height: 100%;
    background-color: #fff;
    border-radius: 40rpx 40rpx 0 0;
    .detail {
        width: 100%;
        height: 260rpx;
        display: flex;
        justify-content: space-around;
        align-items: flex-start;
    }
    .item {
        width: 50%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        font-size: $fs-base;
        color: #000;
    }

    .release_icon {
        margin-left: -20rpx;
        width: 170rpx;
        height: 170rpx;
    }
}
</style>
