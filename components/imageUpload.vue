<template>
    <view class="img_upload">
        <view v-if="showTitleBar" class="el-cu-bar" :style="textStyle">
            <view class="action">
                <text class="title-text" :style="titleStyle">{{ titleText }}</text>
                <text class="request" v-if="isRequired">*</text>
            </view>
            <view v-if="isShowNumber" class="action">{{ imgList.length }}/{{ imgMaxCount }}</view>
        </view>
        <view class="cu-form-group" :style="imgStyle">
            <view class="flex-sub">
                <view v-for="(item, index) in imgList" :key="index" class="bg-img" :data-url="item.url" @tap="ViewImage">
                    <image class="sel_img" :src="item.url" mode="aspectFill"></image>
                    <view class="delete-btn" @tap.stop="DelImg">
                        <image :src="imgUrl + 'order/close.png'" class="close-icon" mode="aspectFill" />
                    </view>
                </view>
                <!-- 添加图片 -->
                <view v-if="imgList.length < imgMaxCount" class="upload-box" @tap="ChooseImage">
                    <text class="cuIcon-add">+</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
/**
 * 基础ColorUI的选择图片组插件
 * @description  基于Color UI的选择图片组插件
 * @property {String} titleText 默认标题（默认值上传凭证）
 * @property {Boolean} showTitleBar 是否展示标题栏（默认值true）
 * @property {Number} imgMaxCount 最大上传图片数（默认10）
 * @event {Function()} change 修改图片后当前的图片组
 */
export default {
    name: 'ImageUpload',
    props: {
        value: {
            type: Array,
            default: () => []
        },
        // 域前缀
        prefix: {
            type: String,
            default: '/yst/aftersale'
        },
        // 默认标题
        titleText: {
            type: String,
            default: '上传凭证'
        },
        // 是否展示标题栏
        showTitleBar: {
            type: Boolean,
            default: true
        },
        // 最大图片数
        imgMaxCount: {
            type: Number,
            default: 10
        },
        // 是否显示限制数量
        isShowNumber: {
            type: Boolean,
            default: false
        },
        // 标题栏样式
        titleStyle: {
            type: Object,
            default: null
        },
        isViewImage: {
            type: Boolean,
            default: false
        },
        textStyle: {
            type: String,
            default: null
        },
        imgStyle: {
            type: String,
            default: null
        },
        // 是否显示必填样式
        isRequired: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            imgList: [],
            imgUrl: getApp().globalData.imgUrl,
            apiUrl: getApp().globalData.apiUrl
        };
    },
    watch: {
        value: {
            handler(value) {
                this.imgList = value;
            },
            immediate: true
        },
        isViewImage() {
            this.imgList = [];
        }
    },
    methods: {
        // 上传图片
        uploadImage(filepath) {
            console.log(filepath);
            uni.showLoading({
                title: '正在上传',
                mask: true
            });
            const userInfo = uni.getStorageSync('userInfo');
            uni.uploadFile({
                url: this.apiUrl + 'v3/oss/front/upload',
                filePath: filepath,
                name: 'file',
                formData: {
                    source: 'afterSale'
                },
                header: {
                    Authorization: 'Bearer ' + (userInfo.access_token ? userInfo.access_token : '')
                },
                success: (uploadFileRes) => {
                    const tempResp = JSON.parse(uploadFileRes.data);
                    if (tempResp.state == 200) {
                        this.imgList.push({
                            ...tempResp.data
                        });
                        this.changeValue(this.imgList);
                    } else {
                        this.$api.msg(tempResp.msg || '上传失败，请重试');
                    }
                },
                fail: (err) => {
                    console.error('上传失败', err);
                    this.$api.msg('上传失败，请重试');
                },
                complete: () => {
                    uni.hideLoading();
                }
            });
        },

        //保存 把当前的数据回调出去
        changeValue(params) {
            this.$emit('change', params);
        },

        /**
         * 选择图片
         */
        ChooseImage() {
            uni.showActionSheet({
                itemList: ['相机', '从相册中选择'],
                success: (res) => {
                    uni.chooseMedia({
                        count: 1, // 默认上传图片数
                        sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
                        sourceType: res.tapIndex === 1 ? ['album'] : ['camera'], //从相册选择
                        success: (res) => {
                            this.uploadImage(res.tempFiles[0].tempFilePath);
                        }
                    });
                }
            });
        },

        /** 查看图片
         * @param {Object} e
         */
        ViewImage(e) {
            uni.previewImage({
                urls: this.imgList.map((item) => item.url),
                current: e.currentTarget.dataset.url
            });
        },

        /** 删除图片
         * @param {Object} e
         */
        DelImg(e) {
            uni.showModal({
                title: '提示',
                content: '确认要删除吗',
                cancelText: '取消',
                confirmText: '确认',
                success: async (res) => {
                    if (res.confirm) {
                        this.imgList.splice(e.currentTarget.dataset.index, 1);
                    }
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.img_upload {
    width: 100%;
	--img-width: 180rpx;
    .flex-sub {
        width: 100%;
        display: flex;
        column-gap: 3.33%;
        row-gap: 20rpx;
        flex-wrap: wrap;

        .bg-img {
            position: relative;
            width: var(--img-width);
			height: var(--img-width);
        }

        .sel_img {
			width: var(--img-width);
			height: var(--img-width);
            border-radius: 6rpx;
            overflow: hidden;
            position: relative;
        }

        .bg-img:hover .delete-btn {
            display: block;
        }

        .delete-btn {
            position: absolute;
            top: -15rpx;
            right: -15rpx;
            width: 40rpx;
            height: 40rpx;
            background-color: rgba(0, 0, 0, 0.5);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;

            .close-icon {
                width: 50%;
                height: 50%;
                object-fit: contain;
            }
        }
    }

    .upload-box {
		width: var(--img-width);
		height: var(--img-width);
        border-radius: 6rpx;
        position: relative;
        overflow: hidden;
        background: #f8f8f8;
    }

    .el-cu-bar {
        display: flex;
        position: relative;
        justify-content: space-between;
        align-items: center;
        padding: 32rpx;
    }

    .cuIcon-add {
        position: absolute;
        top: 45%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 52rpx;
        color: #8799a3;
    }

    .title-text {
        line-height: 40rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: rgba(0, 0, 0, 0.6);
    }

    .upload-box {
        border-radius: 8rpx;
        background: #f8f8f8;
        // .upload-icon {
        //   width: 40rpx;
        //   height: 40rpx;
        // }
    }

    .request {
        color: #ff474a;
    }
	.cu-form-group{
		padding-bottom: 30rpx;
	}
}
</style>
