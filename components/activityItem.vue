<template>
    <view class="explore-card" :style="{ backgroundColor: bgColor }">
        <view class="card-content" @click="toActivityDetail">
            <view class="content-image" :style="{ backgroundImage: `url(${detail.headImg})` }">
                <!-- <view class="status">{{ activityStatus }}</view> -->
            </view>
            <view class="content-footer">
                <view class="content-title">{{ detail.questionnaireName }}</view>
                <view class="content-address">
                    <image :src="imgUrl + 'address.png'" class="icon" mode="scaleToFill" />
                    <text>活动地址：{{ detail.address || '--' }}</text>
                </view>
                <view class="content-time">
                    <image :src="imgUrl + 'time.png'" class="icon" mode="scaleToFill" />
                    <text>活动时间：{{ detail.startTime + ' - ' + detail.endTime }}</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import { mapState } from 'vuex';
export default {
    name: 'ActivityItem',
    props: {
        detail: {
            type: Object,
            default: () => ({})
        },
        bgColor: {
            type: String,
            default: '#fff'
        }
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl // 图片前缀
        };
    },
    computed: {
        ...mapState(['hasLogin']),
        activityStatus() {
            // 根据活动状态返回对应的文字
            const statusMap = {
                0: '未开始',
                1: '进行中',
                2: '已结束'
            };
            return statusMap[this.detail.state] || '未知状态';
        }
    },
    methods: {
        //进入活动详情页面
        toActivityDetail() {
            if (this.hasLogin) {
                this.$Router.push({ path: '/newPages/activity/detail', query: { id: this.detail.questionnaireId } });
            } else {
                getApp().globalData.goLogin(this.$Route);
            }
        }
    }
};
</script>
<style lang="scss">
.explore-card {
    border-radius: 40rpx;
    overflow: hidden;
    .card-content {
        display: flex;
        flex-direction: column;
        background-color: #fff;
        overflow: hidden;
    }
    .content-image {
        position: relative;
        width: 100%;
        height: 200rpx;
        background-size: cover;
        background-position: center;
        .status {
            position: absolute;
            font-size: 24rpx;
            top: 0;
            right: 0;
            background-color: rgba(0, 0, 0, 0.5);
            color: #fff;
            padding: 5rpx 30rpx;
            height: 40rpx;
            border-radius: 0 0 0 40rpx;
        }
    }
    .content-footer {
        padding: 20rpx;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        .content-title {
            font-size: $fs-base;
            font-weight: bold;
            color: #000;
            margin-bottom: 20rpx;
            // // 超出两行...
            // overflow: hidden;
            // text-overflow: ellipsis;
            // display: -webkit-box;
            // -webkit-line-clamp: 2;
            // -webkit-box-orient: vertical;
            @extend .omitLine2;
        }
        .content-address,
        .content-time {
            display: flex;
            align-items: flex-start;
            font-size: $fs-m;
            color: #666;
            .icon {
                width: 30rpx;
                height: 30rpx;
                // margin-top: 4rpx;
                margin-right: 10rpx;
            }
        }
        .content-address {
            margin-bottom: 20rpx;
        }
    }
}
</style>
