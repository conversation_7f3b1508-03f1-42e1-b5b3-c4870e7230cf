<template>
  <view>
    <view class="picker-tap" @click="open">
        <!-- 默认在表单中使用 -->
        <block v-if="useType === 'inForm'">
            <text class="value-text" :style="{ color: pickerValue ? 'rgba(0,0,0,0.80)' : placeholderColor, fontSize: placeholderFontSize, textAlign: textAlign }">
                {{ pickerValue || placeholder }}
            </text>
            <slot name="arrow">
                <text v-if="showArrow" class="iconfont iconarrow" :style="{ color: arrowColor, fontSize: arrowSize }"></text>
            </slot>
        </block>
        <!-- 在form表单外使用插槽来实现自定义 -->
        <block v-else>
            <slot name="custom"></slot>
        </block>
    </view>
    <view v-if="showPicker" class="picker-modal"></view>
    <view v-if="showPicker" class="picker-content" :style="{ transform: showPicker ? 'translateY(0)' : 'translateY(100%)' }">
        <view class="picker-header">
            <view v-if="cancelType === 'text'" :style="{ color: cancelColor, fontSize: cancelFontSize, fontWeight: cancelFontWeight }" @click="close">取消</view>
            <icon v-else type="clear" size="18" @click="close" />
            <view class="packer-title" :style="{ color: titleColor, fontSize: titleFontSize, fontWeight: titleFontWeight }">
                {{ title }}
            </view>
            <view :style="{ color: confirmColor, fontSize: confirmFontSize, fontWeight: confirmFontWeight }" @click="confirmPicker">确定</view>
        </view>
        <picker-view indicator-style="height: 90rpx;" class="picker-view" @change="changePicker">
            <picker-view-column>
                <view v-for="(item, index) in dataSource" :key="index" class="picker-item" :style="{ fontSize: itemFontSize }">
                    {{ item.label }}
                </view>
                <view v-if="dataSource && dataSource.length === 0" class="picker-item" :style="{ fontSize: itemFontSize }">暂无数据</view>
            </picker-view-column>
        </picker-view>
    </view>
  </view>
</template>

<script>
export default {
    name: 'Picker',
    props: {
        useType: {
            type: String,
            default: 'inForm'
        },
        transfer: {
            type: Object,
            default: function () {
                return {
                    label: 'label',
                    value: 'value'
                };
            }
        },
        value: {
            type: String,
            default: ''
        },
        placeholder: {
            type: String,
            default: '请选择'
        },
        placeholderColor: {
            type: String,
            default: 'rgba(0,0,0,0.5)'
        },
        placeholderFontSize: {
            type: String,
            default: '30rpx'
        },
        cancelColor: {
            type: String,
            default: 'rgba(0,0,0,0.40)'
        },
        cancelType: {
            type: String,
            default: 'text'
        },
        cancelFontSize: {
            type: String,
            default: '28rpx'
        },
        cancelFontWeight: {
            type: [String, Number],
            default: 400
        },
        title: {
            type: String,
            default: ''
        },
        titleColor: {
            type: String,
            default: 'rgba(0,0,0,0.80)'
        },
        titleFontSize: {
            type: String,
            default: '32rpx'
        },
        titleFontWeight: {
            type: [String, Number],
            default: 500
        },
        confirmColor: {
            type: String,
            default: '#C70E2D'
        },
        confirmFontSize: {
            type: String,
            default: '28rpx'
        },
        confirmFontWeight: {
            type: [String, Number],
            default: 400
        },
        itemFontSize: {
            type: String,
            default: '32rpx'
        },
        showArrow: {
            type: Boolean,
            default: true
        },
        arrowColor: {
            type: String,
            default: '#757575'
        },
        arrowSize: {
            type: String,
            default: '28rpx'
        },
        pickerList: {
            type: Array,
            default: function () {
                return [{ label: '选项1', value: '1' }];
            }
        },
        textAlign: {
            type: String,
            default: 'unset'
        }
    },
    data() {
        return {
            showPicker: false,
            pickerValue: this.value,
            pickerValueIndex: 0,
            dataSource: []
        };
    },
    watch: {
        pickerList: {
            handler(newVal) {
                this.dataSource = (newVal || []).map((item) => ({
                    ...item,
                    label: item[this.transfer.label],
                    value: item[this.transfer.value]
                }));
            },
            immediate: true,
            deep: true
        },
        value(newVal) {
            this.pickerValue = newVal;
        }
    },
    created() {
        this.dataSource = (this.pickerList || []).map((item) => ({
            ...item,
            label: item[this.transfer.label],
            value: item[this.transfer.value]
        }));
        this.pickerValue = this.value;
        this.pickerValueIndex = 0;
    },
    methods: {
        open() {
            this.pickerValueIndex = 0;
            this.showPicker = true;
        },
        close() {
            this.showPicker = false;
            this.pickerValueIndex = 0;
        },
        changePicker(e) {
            this.pickerValueIndex = e.detail.value;
        },
        confirmPicker() {
            const checkedValue = this.dataSource[this.pickerValueIndex];
            this.pickerValue = checkedValue ? checkedValue.label : '';
            this.$emit('update:value', this.pickerValue);
            this.$emit('change', checkedValue);
            this.showPicker = false;
        }
    }
};
</script>

<style lang="scss" scoped>
.picker-tap {
    display: flex;
    align-items: center;
    box-sizing: border-box;
}
.value-text {
    flex: 1;
}
.iconarrow {
    text-align: center;
    width: 50rpx;
}
.picker-modal {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 9;
    background-color: rgba(0, 0, 0, 0.4);
    animation: dropdown1 0.2s linear;
}
.picker-content {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2048;
    border-top-left-radius: 30rpx;
    border-top-right-radius: 30rpx;
    background-color: #fff;
    transition: all 0.3s;
    animation: dropdown 0.2s linear;
}
.picker-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 20rpx;
    padding: 20rpx 0;
}
.packer-title {
    text-align: center;
}
.picker-view {
    position: relative;
    left: 0;
    right: 0;
    bottom: 0;
    height: 490rpx;
    background-color: rgba(255, 255, 255, 1);
}
.picker-item {
    line-height: 70rpx;
    text-overflow: ellipsis;
    text-align: center;
}
@keyframes dropdown {
    0% {
        height: 0;
    }
    100% {
        height: 490rpx;
    }
}
@keyframes dropdown1 {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}
</style>
