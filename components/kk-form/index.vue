<template>
    <view>
        <form @submit="handleSubmit"></form>
    </view>
</template>
<script>
export default {
    props: {
        colunms: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            formData: {}
        };
    },
    methods: {
        // 获取表单数据
        getFormData() {
            return this.formData;
        },
        // 提交表单
        handleSubmit(e) {
            e.preventDefault();
            // 处理表单提交逻辑
            console.log('提交的车辆信息:', this.lmVehicleInfo);
            // 这里可以添加API调用或其他逻辑
        },
        handleSelect(name) {
            // 处理选择逻辑
            console.log('选择了:', name);
        },
        // 扫码
        scanCode(type) {
            // #ifdef H5
            window.jsBridgeHelper?.sendMessage('qrCode').then((res) => {
                // code: string; msg: string; data: string
                if (res.code === '200') {
                    // 正常二维码格式L1ZL1ZEH8P0503159、062310230361
                    // 旧版本格式 http://www.lima-info.com/wx/062310230361.aspx
                    const value = res.data.replace(/^.*\/([A-Za-z0-9]+)\.aspx$/, '$1').trim();
                    this.vin = value;
                }
            });
            // #endif
            // #ifndef H5
            uni.scanCode({
                onlyFromCamera: false,
                scanType: ['qrCode'],
                success: (res) => {
                    // res.result 可能是二维码内容
                    const value = res.result.replace(/^.*\/([A-Za-z0-9]+)\.aspx$/, '$1').trim();
                    this.vin = value;
                },
                fail: (err) => {
                    console.error('扫码失败:', err);
                }
            });
            // #endif
        }
    }
};
</script>

<style lang="scss" scoped>
.request {
    color: #c70e2d;
}
.kk-form-group {
    background-color: #ffffff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 100rpx;

    &:not(:last-child) {
        border-bottom: 1rpx solid #eee;
    }

    .form_input {
        font-size: $fs-base;
    }

    .title {
        width: 200rpx;
        font-size: $fs-base;
    }

    .form-wrapper {
        flex: 1 1 calc(100% - 200rpx);
        display: flex;
        align-items: center;
        justify-content: space-between;

        .form_input {
            flex: 1 1 calc(100% - 50rpx);
        }

        .suffix-icon {
            width: 50rpx;
        }

        .arrow-icon {
            width: 24rpx;
            height: 24rpx;
            -webkit-transform: rotate(45deg);
            transform: rotate(45deg);
            margin-left: 10rpx;
            margin-right: 20rpx;
            box-shadow: -2px 2px 0 0px #c7c7c7 inset;
        }
    }

    .picker {
        text-align: left;
    }

    .placeholder {
        font-weight: 400;
        font-size: $fs-base;
        color: rgba(0, 0, 0, 0.25);
    }
}
</style>
