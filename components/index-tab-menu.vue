<template>
	<view class="content1" :style="[{background:backGround}]">
		<view class="nav_wrap">
			<view class="nav_item" v-for="(item,index) in tabInfo" :key="index" @click="changeTab(index,item.categoryId)">
				<view :class="currIndex == index?'active_nav nav_text':'nav_text'">{{item.categoryName}}</view>
				<image :src="icon" mode="aspectFit" class="nav_icon" v-if="currIndex == index"></image>
			</view>
		</view>
		<view class="category">
			<!-- <view class="gap_line"></view> -->
      <image class="xian_img" :src="xian" mode=""></image>
			<view class="sort_wrap" @click="toSortPage">
				<image :src="sortImg" mode="aspectFit"></image>
				<text>分类</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default{
		name:'tabMenu',
		data(){
			return {
				icon:getApp().globalData.imgUrl+'index/icon.png',
				sortImg:getApp().globalData.imgUrl+'index/sorts.png',
        xian:getApp().globalData.imgUrl+'index/shuxian.png',
				currIndex:0
			}
		},
		props: ['backGround','tabInfo'],
		methods:{
			changeTab(index,categoryId){
				this.currIndex = index
				if(index>0){
					let param = {}
					param.method = 'GET'
					param.url = 'v3/goods/front/goods/category/list?categoryId1='+categoryId
					this.$request(param).then(res=>{
						if(res.state == 200){
							                            this.$emit('getChildList',res.data,index,categoryId)
						}
					})
				}else{
					this.$emit('getChildList',null,index)
				}
			},
			toSortPage(){
				this.$Router.pushTab(`/pages/category/category`)
			}
		}
	}
</script>

<style lang='scss' scoped>
	.content1{
		width:750rpx;
		padding: 0 20rpx;
		height: 60rpx;
		display: flex;
		align-items: flex-start;
		box-sizing: border-box;
		overflow-y: hidden;
		
		.nav_wrap{
			width:599rpx;
		
			height:64rpx;
			display:flex;
			overflow-x: scroll;
			
			padding-top: -9rpx;
			
			.nav_item{
				margin-right:35rpx;
				display:flex;
				flex-direction: column;
				align-items: center;
				box-sizing: border-box;
				flex-shrink: 0;
				.nav_text{
					font-size:30rpx;
					color:#fff;
					white-space: nowrap;
          margin-top: 1px;
				}
				.nav_icon{
					width:27rpx;
					height:10rpx;
				}
			}	
		}
		
		.category{
			display: flex;
			align-items: center;
			/* #ifdef MP */
			margin-top: 3rpx;
			/* #endif */
			/* #ifndef MP */
			margin-top: 2rpx;
			/* #endif */
      margin-left: 10rpx;
			.gap_line{
				width:6rpx;
				height:30rpx;
				background: linear-gradient(-90deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);
				opacity: 0.8;
			}
			.sort_wrap{
				color:#fff;
				display: flex;
				box-sizing: border-box; 
				align-items: center;
				text-align: right;
				width: max-content;
				image{
					width:27rpx;
					height:24rpx;
					margin: 0 8rpx;
          margin-left: 14rpx;
				}
				
				text{
					font-size:30rpx;
					margin-bottom: 2rpx;
				}
				
			}
		}
	}
	
	.active_nav{
		font-weight: bold;
		/* margin-bottom: 6rpx; */
	}
  .xian_img{
    width: 2rpx;
    margin-top: 2rpx;
    height:31rpx;
  }
</style>
