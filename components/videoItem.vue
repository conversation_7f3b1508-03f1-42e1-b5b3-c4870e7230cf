<template>
    <view class="explore-card" :style="{ backgroundColor: bgColor }">
        <view class="card-header">
            <view class="user-info" @tap="goLiveUserCenter">
                <image class="avatar" :src="video.memberAvatar" mode="aspectFill" />
                <view class="username">{{ video.memberNickname }}</view>
                <view class="level">
                    <image v-if="video.memberType != 1" class="vip-image" :src="imgUrl + 'user/vip-' + (video.memberLevel ? video.memberLevel : '1') + '.png'" mode="" />
                    <image v-else class="lima-image" :src="imgUrl + 'lima_text.png'" mode="widthFix" />
                </view>
            </view>
            <view class="follow-block" v-if="!video.isFollow && !video.isSelf">
                <view class="follow_btn" :class="{ follow: video.isFollow }" @click="collect">{{ video.isFollow ? '已关注' : '关注' }}</view>
            </view>
            <view class="del-block" v-if="video.isSelf && page == 'my'">
                <image class="del_icon" :src="imgUrl + 'del_search2.png'" mode="widthFix" />
                <view class="del_btn" @click="deleteVideo">删除</view>
            </view>
        </view>
        <view class="card-content" @click="goVideoPlay">
            <view class="content-text">{{ video.videoName }}</view>
            <view class="content-image" v-if="video.videoType == 1" :style="{ gridTemplateColumns: '1fr', gridTemplateRows: '1fr' }">
                <view class="image-grid" :style="{ backgroundImage: `url(${video.videoImage})` }">
                    <image class="video-play" :src="imgUrl + 'svideo/video_play.png'" mode="" />
                </view>
            </view>
            <view
                class="content-image"
                v-else-if="video.videoImageUrl && video.videoImageUrl.length == 9"
                :style="{ gridTemplateColumns: 'repeat(3, 1fr)', gridTemplateRows: 'repeat(3, 1fr)' }"
            >
                <view class="image-grid" v-for="(img, index) in video.videoImageUrl" :style="{ backgroundImage: `url(${img})` }" :key="index"></view>
            </view>
            <view
                class="content-image"
                v-else-if="video.videoImageUrl && video.videoImageUrl.length >= 4"
                :style="{ gridTemplateColumns: 'repeat(2, 1fr)', gridTemplateRows: 'repeat(2, 1fr)' }"
            >
                <block v-for="(img, index) in video.videoImageUrl" :key="index">
                    <view v-if="index < 4" class="image-grid" :style="{ backgroundImage: `url(${img})` }"></view>
                </block>
            </view>
            <view class="content-image" v-else :style="{ gridTemplateColumns: '1fr', gridTemplateRows: '1fr' }">
                <view class="image-grid" v-if="video.videoImage" :style="{ backgroundImage: `url(${video.videoImage})` }"></view>
            </view>
        </view>
        <!-- 主题信息 -->
        <view class="tag_part" v-if="video.themeId">
            <view class="tag" @tap.stop="goThemeDetail">
                <text class="icon">#</text>
                <text class="tag_text">{{ video.themeName }}</text>
            </view>
        </view>
        <view class="card-footer">
            <view class="interaction">
                <view class="views">
                    <image class="image" mode="aspectFit" :src="imgUrl + 'svideo/eye.png'"></image>
                    <text class="num">{{ video.clickNum }}</text>
                </view>
                <view class="comments">
                    <image class="image" mode="aspectFit" :src="imgUrl + 'svideo/comment.png'"></image>
                    <text class="num">{{ video.commentNum }}</text>
                </view>
                <view class="likes">
                    <image class="image" mode="aspectFit" :src="imgUrl + 'svideo/fav.png'"></image>
                    <text class="num">{{ video.likeNum }}</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import { mapState } from 'vuex';
export default {
    name: 'VideoItem',
    props: {
        detail: {
            type: Object,
            default: () => ({})
        },
        bgColor: {
            type: String,
            default: '#fff'
        },
        page: {
            type: String,
            default: '' // 用于区分页面来源
        }
    },
    data() {
        return {
            video: {},
            imgUrl: getApp().globalData.imgUrl // 图片前缀
        };
    },
    emits: ['changeFollow', 'delete'],
    mounted() {},
    computed: {
        ...mapState(['hasLogin'])
    },
    // 深度监听 detail 属性变化
    watch: {
        detail: {
            handler(newVal) {
                // console.log('detail changed:', newVal);
                this.video = newVal;
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        //删除视频
        deleteVideo() {
            uni.showModal({
                title: '提示',
                content: '确定删除该条数据吗?',
                success: (res) => {
                    if (res.confirm) {
                        let param = {};
                        param.data = {};
                        param.data.videoId = this.video.videoId;
                        param.url = 'v3/video/front/video/delVideo';
                        param.method = 'POST';
                        this.$request(param).then((res) => {
                            this.$emit('delete', { state: res.state, msg: res.msg, videoId: this.video.videoId });
                        });
                    }
                }
            });
        },
        //进入主题详情
        goThemeDetail() {
            if (page === 'theme') return; // 防止重复进入
            if (this.video.themeId) {
                this.$Router.push({ path: '/extra/topic/theme', query: { themeId: this.video.themeId } });
            }
        },
        //进入播放页面
        goVideoPlay() {
            const { videoType, videoId, authorId } = this.video;
            console.log('进入视频播放页面', videoType, videoId, authorId);
            if (videoType == 1) {
                this.$videoPlayNav({
                    video_id: videoId,
                    author_id: authorId
                });
            } else {
                this.$Router.push({
                    path: '/extra/graphic/graphicDetail',
                    query: {
                        video_id: videoId,
                        author_id: authorId
                    }
                });
            }
        },
        //进入作者页面
        goLiveUserCenter() {
            let author_id = this.video.authorId;
            let page = getCurrentPages();
            let len = page.length;
            if (len > 4) {
                this.$Router.replace({
                    path: '/extra/user/my',
                    query: {
                        author_id
                    }
                });
            } else {
                this.$Router.push({
                    path: '/extra/user/my',
                    query: {
                        author_id
                    }
                });
            }
        },
        //关注、取消关注事件
        collect() {
            if (this.hasLogin) {
                let { authorId, isFollow } = this.detail;
                let param = {};
                param.data = {};
                param.method = 'POST';
                param.data.authorId = authorId;
                if (isFollow) {
                    param.url = 'v3/video/front/video/cancelFollow';
                } else {
                    param.url = 'v3/video/front/video/followAuthor';
                }
                this.$request(param).then((res) => {
                    if (res.state == 200) {
                        this.video.isFollow = this.video.isFollow == true ? false : true;
                        this.$emit('changeFollow', this.video);
                        this.$api.msg(res.msg);
                    } else {
                        uni.showToast({
                            title: res.msg,
                            icon: 'none'
                        });
                    }
                });
            } else {
                getApp().globalData.goLogin(this.$Route);
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.explore-card {
    border-radius: 40rpx;
    padding: 25rpx 20rpx;
    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20rpx;

        .user-info {
            display: flex;
            align-items: center;

            .avatar {
                width: 60rpx;
                height: 60rpx;
                border-radius: 50%;
                margin-right: 15rpx;
                border: 2px solid rgba(204, 204, 204, 1);
            }

            .username {
                font-size: $fs-base;
                font-weight: 500;
                margin-right: 15rpx;
            }
            .level {
                padding: 0 20rpx;
                height: 40rpx;
                border-radius: 20rpx;
                border: 1px solid #000;
                display: flex;
                align-items: center;
                justify-content: center;
                .vip-image {
                    width: 30rpx;
                    height: 30rpx;
                }
                .lima-image {
                    width: 60rpx;
                    height: 15rpx;
                }
            }
        }
        .follow-block {
            display: flex;
            justify-content: center;
            align-items: center;

            .follow_btn {
                background-color: transparent;
                color: #000;
                border: 1px solid;
                border-color: rgba(89, 87, 87, 1);
                border-radius: 25rpx;
                padding: 0 30rpx;
                font-size: 24rpx;
                text-align: center;
                height: 50rpx;
                line-height: 50rpx;
                &.follow {
                    color: rgba(0, 0, 0, 0.3);
                    border-color: rgba(0, 0, 0, 0.3);
                }
            }
        }
        .del-block{
            display: flex;
            align-items: center;
            justify-content: center;

            .del_icon {
                width: 30rpx;
                height: 30rpx;
                margin-right: 10rpx;
            }
            .del_btn {
                color: rgba(0, 0, 0, 0.5);
                font-size: 24rpx;
                text-align: center;
                height: 50rpx;
                line-height: 50rpx;
            }
        }
    }

    .card-content {
        .content-text {
            font-size: $fs-base;
            line-height: 1.6;
            color: #000;
            margin-bottom: 20rpx;
        }

        .content-image {
            width: 100%;
            aspect-ratio: 1/1;
            display: grid;
            gap: 10rpx;
            border-radius: 40rpx;
            overflow: hidden;
        }
        .image-grid {
            width: 100%;
            height: 100%;
            background-position: center;
            background-repeat: no-repeat;
            background-size: cover;
            display: flex;
            align-items: center;
            justify-content: center;

            .video-play {
                width: 100rpx;
                height: 100rpx;
                object-fit: contain;
            }
        }
    }
    .tag_part {
        width: 100%;
        margin: 20rpx auto;
        padding: 0rpx 0rpx 20rpx;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .tag {
            display: inline-block;
            padding: 0rpx 20rpx 0 20rpx;
            height: 50rpx;
            border-radius: 25rpx;
            color: #fff;
            font-size: $fs-base;
            background: rgba(178, 161, 240, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            .icon {
                height: 30rpx;
                width: 30rpx;
                line-height: 30rpx;
                text-align: center;
                margin-right: 5px;
                border-radius: 50%;
                color: #9794ff;
                background-color: #fff;
            }
        }
    }
    .card-footer {
        display: flex;
        justify-content: space-between;
        margin-top: 20rpx;
        color: #999;
        font-size: 24rpx;
        box-sizing: border-box;
        width: 100%;
        padding: 0 5px;

        .interaction {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .views,
            .likes,
            .comments {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 6rpx;
                width: 33%;
                .image {
                    width: 40rpx;
                    height: 40rpx;
                }

                .num {
                    margin-left: 10rpx;
                    font-size: 26rpx;
                    color: rgba(0, 0, 0, 0.5);
                }
            }
        }
    }
}
</style>
