<template>
<view class="sc-tabbar-wrap">
  <view class="sc-tabbar" v-if="comList && comList.length > 0">
    <view :style="{ 'grid-template-columns': `repeat(${list.length}, 1fr)` }" class="sc-tabbar-main">
      <view v-for="(item, index) in list" :key="item.text" class="tabbar-item" @tap="tapTabbarItem(index, item)">
        <image :key="`img-${index}`" :class="[activeIndex == index ? 'icon-in' : 'icon-out']" :src="item.icon"
          :style="[iconSizeStyle]" class="item-icon"></image>
        <text :key="`text-${index}`" :class="[activeIndex == index ? 'text-in' : 'text-out']"
          :style="{ fontSize: addUnit(textSize), color: activeIndex == index ? activeTextColor : textColor }"
          class="item-text">
          {{ item.text }}
        </text>
      </view>
    </view>
    <view :style="{ width: addUnit(750 * 2 + 750 / comList.length), left: addUnit(bgLeft) }" class="tb-bg">
      <view :style="{ width: addUnit(750 / comList.length) }" class="bg-middle">
        <image :src="base64Img" class="bg-ao"></image>
        <view :style="{ backgroundColor: circularColor }" class="middle-circular">
          <image v-for="(_, index) in comList" :key="`circular-icon-${index}`"
            :class="[activeIndex == index ? 'activeIcon-in' : 'activeIcon-out']"
            :src="comList[activeIndex].selectedIcon" :style="[iconSizeStyle]" class="circular-icon">
          </image>
        </view>
      </view>
    </view>
  </view>
  <view class="sc-tabbar-placeholder"></view>
</view>
</template>

<script>
/**
 * @description 底部导航栏
 * @private
 * @param {Array} list 列表 (长度 2~5)
 * @param {String} circularColor 中间圆形的背景色
 * @param {Number} textSize 文字大小
 * @param {Number} textColor 文字颜色
 * @param {Number} activeTextColor 激活文本的颜色
 * @param {Number} iconSize 图标大小
 * @param {Number} current 当前选中的下标
 * @param {Boolean} autoJump 是否自动跳转(默认false)
 * @param {Function} interceptor 跳转拦截器(传入一个函数,函数返回一个布尔值,返回true则切换，返回false则不切换)
 *
 * @emits {change} 切换时触发
 */
export default {
  name: 'ScTabbar',
  props: {
    list: {
      type: Array,
      required: true,
      validator: function (val) {
        return val.length >= 2 && val.length <= 5;
      }
    },
    circularColor: {
      type: String,
      default: '#c70e2d'
    },
    textSize: {
      type: Number,
      default: 30
    },
    textColor: {
      type: String,
      default: '#666'
    },
    activeTextColor: {
      type: String,
      default: '#c70e2d'
    },
    iconSize: {
      type: Number,
      default: 40
    },
    current: {
      type: Number,
      default: 0
    },
    autoJump: {
      type: Boolean,
      default: false
    },
    interceptor: {
      type: Function
    }
  },
  data() {
    return {
      base64Img: 'data:image/png;base64,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',
      activeIndex: this.current,
      bgLeft: -750 + 750 / this.list.length * this.current
    };
  },
  computed: {
    iconSizeStyle() {
      return {
        width: this.addUnit(this.iconSize),
        height: this.addUnit(this.iconSize)
      };
    },
    comList() {
      console.log('comList', this.list);
      return this.list
    }
  },
  watch: {
    current: {
      handler(newVal) {
        this.activeIndex = newVal;
        this.bgLeft = -750 + 750 / this.list.length * newVal;
      },
      immediate: true
    }
  },
  methods: {
    addUnit(num) {
      console.log('addUnit', num);
      return num + 'rpx';
    },
    tapTabbarItem(index, item) {
      if (this.activeIndex === index) return;
      if (this.interceptor) {
        if (!this.interceptor(index, item)) return;
      }
      if (this.autoJump && item.path) {
        uni.reLaunch({
          url: item.path
        });
      }
      this.activeIndex = index;
      this.bgLeft = -750 + 750 / this.list.length * index;
      this.$emit('change', index, item);
    }
  }
};
</script>


<style lang="scss" scoped>
.sc-tabbar-wrap {
  --tabbar-height: 120rpx;

  .sc-tabbar {
    position: relative;
    margin-bottom: env(safe-area-inset-bottom);
    width: 100%;
    height: var(--tabbar-height);
  }

  .sc-tabbar-placeholder {
    box-sizing: border-box;
    padding-bottom: env(safe-area-inset-bottom);
    width: 100%;
    height: var(--tabbar-height);
  }

  .sc-tabbar-main {
    position: absolute;
    z-index: 2;
    display: flex;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0);

    .tabbar-item {
      position: relative;
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      width: 25%;
      height: 100%;
      .item-icon {
        width: 20rpx;
        height: 20rpx;
        transition: all 0.3s ease;
      }

      .item-icon.icon-out {
        transform: translateY(0rpx);
      }

      .item-icon.icon-in {
        opacity: 0;
        transform: translateY(-10rpx);
      }

      .item-text {
        width: 100%;
        text-align: center;
        line-height: 45rpx;
        transition: all 0.1s ease;
      }

      .item-text.text-out {
        opacity: 1;
      }

      .item-text.text-in {
        opacity: 0;
      }
    }
  }

  .tb-bg {
    position: absolute;
    z-index: 1;
    display: flex;
    justify-content: center;
    background: radial-gradient(circle at 50% 0rpx, transparent 90rpx, #E8E8E8 0) top left 100% no-repeat;
    transition: left 0.3s ease;
    height: 100%;
    .bg-middle {
      display: flex;
      justify-content: center;
      height: 100%;
      .bg-ao {
        display: block;
        width: 100%;
        height: 100%;
      }

      .middle-circular {
        position: absolute;
        top: -5rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        background-color: #fff;
      }

      .circular-icon {
        position: absolute;
        transition: all 0.5s ease;
      }

      .circular-icon.activeIcon-out {
        opacity: 0;
        transition: all 0s ease;
        transform: translateY(30rpx);
      }

      .circular-icon.activeIcon-in {
        opacity: 1;
      }
    }
  }

}
</style>