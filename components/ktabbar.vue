<template>
    <!-- 自定义顶部导航栏 -->
    <view class="custom-nav-taber-wrapper">
        <!-- #ifdef MP -->
        <view class="top_status_bar_seat" :style="{ height: topBar.navBarHeight + 'px' }"></view>
        <view
            class="custom-nav-taber"
            :style="{ height: topBar.navBarHeight + 'px', paddingTop: topBar.offsettop - 5 + 'px', backgroundColor: bgColor, backgroundImage: `url(${bgImg})` }"
        >
            <view v-if="showLeft" class="taber-left" @click="back" :style="{ height: topBar.navBarHeight - topBar.offsettop + 'px' }">
                <uv-icon class="to-art" name="arrow-left" :color="leftIconColor" size="20"></uv-icon>
            </view>
            <view :style="{ height: topBar.menuHeight + 'px', lineHeight: topBar.menuHeight + 'px' }" class="taber-title">
                <text class="t">{{ title }}</text>
            </view>
        </view>
        <!-- #endif -->
        <!-- #ifdef H5 -->
        <view class="top_status_bar_seat"></view>
        <view class="custom-nav-taber" :style="{ backgroundColor: bgColor, backgroundImage: `url(${bgImg})` }">
            <view v-if="showLeft" class="taber-left" @click="back">
                <uv-icon class="to-art" name="arrow-left" :color="leftIconColor" size="20"></uv-icon>
            </view>
            <view class="taber-title">
                <text class="t">{{ title }}</text>
            </view>
        </view>
        <!-- #endif -->
    </view>
    <!-- 自定义顶部导航栏 end-->
</template>
<script>
export default {
    props: {
        bgColor: {
            type: String,
            default: 'transparent'
        },
        bgImg: {
            type: String,
            default: ''
        },
        title: {
            type: String,
            default: ''
        },
        showLeft: {
            type: Boolean,
            default: true
        },
        leftIconColor: {
            type: String,
            default: '#3e3e3e'
        },
        leftText: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            topBar: getApp().globalData.topBar
        };
    },
    mounted() {
        // #ifdef MP
        // 胶囊按钮位置信息
        const systemInfo = wx.getWindowInfo();
        // 导航栏高度 = 状态栏高度 + 44
        const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
        let globalData = {};
        globalData.navBarHeight = systemInfo.statusBarHeight + 44;
        globalData.menuRight = systemInfo.screenWidth - menuButtonInfo.right;
        globalData.menuHeight = menuButtonInfo.height;
        globalData.offsettop = menuButtonInfo.top;
        this.topBar = globalData;
        // #endif
    },
    methods: {
        // 返回上一页
        back() {
            this.$Router.back();
        }
    }
};
</script>
<style lang="scss" scoped>
.custom-nav-taber {
    position: fixed;
    top: 0;
    // #ifdef MP
    left: 0;
    width: 100%;
    // #endif
    z-index: 10;
    display: flex;
    justify-content: center;
    align-items: center;
    // #ifdef H5
    padding-top: 80rpx;
    box-sizing: content-box;
    left: 50%;
    width: 750rpx;
    height: 100rpx;
    transform: translateX(-50%);
    // #endif
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    transition: background-color 0.3s ease;
	background-color: transparent;
    .taber-title {
        /* #ifdef MP-WEIXIN */
        width: 400rpx;
        /* #endif */
        /* #ifdef H5 */
        display: flex;
        align-items: center;
        justify-content: center;
        width: calc(100% - 250rpx);
        height: 100rpx;
        /* #endif */
        .t {
            text-align: center;
            width: 100%;
            @extend .omitLine1;
        }
    }
    .taber-left {
        position: absolute;
        /* #ifdef MP-WEIXIN */
        left: 0;
        bottom: 0;
        /* #endif */
        width: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        padding-left: 30rpx;
        /* #ifdef H5 */
        height: 100rpx;
        left: 0;
        bottom: 0;
        /* #endif */
    }
}

.top_status_bar_seat {
    // #ifdef MP
    width: 100%;
    // #endif
    /* app-2-start */
    // #ifdef H5
    width: 750rpx;
    height: 180rpx;
    // #endif
}
</style>
