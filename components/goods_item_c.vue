<!-- 商品组件：横向方向、点击进入商品详情页、加入购物车事件-->
<template name="goodsItemC">
	<view
		class="goods_c_item flex_column_start_start"
		@click="goGoodsDetail(goods_info)"
		:style="{
			borderRadius: border_radius + 'rpx',
			width: 'calc((100% - ' + page_margin + 'rpx - ' + goods_margin + 'rpx))',
			height: height + 'rpx'
		}"
	>
		<view class="goods_desc_top">
			<view
				class="goods-img"
				:style="{
					backgroundImage: 'url(' + (goods_info.mainImage || goods_info.goodsImage) + ')',
					width: 'calc((100% - ' + page_margin + 'rpx - ' + goods_margin + 'rpx))'
				}"
			>
				<view class="goods-type flex_row_start_center" v-if="false">
					<view class="type-item w">外卖</view>
					<view class="type-item k">快递</view>
					<view class="type-item d">到店</view>
				</view>
			</view>
			<text class="good-store">{{ goods_info.storeName }}</text>
			<text class="goods-name">{{ goods_info.goodsName }}</text>
			<!-- <view class="goods_des">{{goods_info.goodsBrief}}</view> -->
		</view>
		<view class="goods-price have_no_sale">
			<view class="">
				<view class="left">
					<!-- <text class="unit">{{ $L('￥') }}</text> -->
					<text class="price_int">
						￥{{
							goods_info.goodsPrice >= 0
								? $getPartNumber(goods_info.goodsPrice, 'int')
								: goods_info.productPrice >= 0
								? $getPartNumber(goods_info.productPrice, 'int')
								: $getPartNumber(goods_info.marketPrice, 'int')
						}}
					</text>
					<text class="price_decimal">
						{{
							goods_info.goodsPrice >= 0
								? $getPartNumber(goods_info.goodsPrice, 'decimal')
								: goods_info.productPrice >= 0
								? $getPartNumber(goods_info.productPrice, 'decimal')
								: $getPartNumber(goods_info.marketPrice, 'decimal')
						}}
					</text>
				</view>
				<view class="have_sold_text" v-if="show_sale == true">{{ $L('已售') }}{{ goods_info.actualSales >= 0 ? goods_info.actualSales : goods_info.saleNum }}件</view>
			</view>
			<view class="pre_bottom" :class="show_sale == true ? 'goods_item_bottom' : ''">
				<!-- @click.stop="addCart(goods_info.productId || goods_info.defaultProductId)" -->
			<!-- 	<view class="icon_set">
					<image :src="imgUrl + 'add-cart.png'" style="width: 58rpx; height: 58rpx"></image>
				</view> -->
			</view>
		</view>
	</view>
</template>

<script>
import { mapState } from 'vuex';
export default {
	name: 'goodsItemV',
	data() {
		return {
			imgUrl: getApp().globalData.imgUrl,
			icon2: getApp().globalData.imgUrl + 'index/add2.png',
			icon3: getApp().globalData.imgUrl + 'index/add.png',
			icon4: getApp().globalData.imgUrl + 'index/add3.png',
			icon5: getApp().globalData.imgUrl + 'index/stop.png',
			icon_url: '', //加车图标
			goods_pic: '', //商品图片
			goods_sale: '', //销量
			isIos: uni.getDeviceInfo().platform == 'ios' //是否ios手机
		};
	},
	computed: {
		...mapState(['userInfo'])
	},
	props: {
		goods_info: {
			type: Object,
			value: {}
		},
		show_sale: {
			type: Boolean
		},
		border_radius: {
			type: Number,
			default: 20
		},
		border_style: {
			type: String
		},
		// 商品边距
		goods_margin: {
			type: Number,
			default: 10
		},
		// 页面边距
		page_margin: {
			type: Number,
			default: 10
		},
		height: {
			type: Number,
			default: 258
		},
		transType: {
			type: String,
			default: ''
		}
	},
	onLoad() {
		console.log('goods_info', this.goods_info);
	},
	methods: {
		//进入商品详情页
		goGoodsDetail(goods_info) {
			// console.log('goods_info', this.goods_info);
			// return;
			let path = '/standard/product/detail';
			this.$Router.push({
				path,
				query: {
					productId: goods_info.productId || goods_info.defaultProductId,
					goodsId: goods_info.goodsId
				}
			});
		},
		changeImg(val) {
			return val.mainImage ? val.mainImage : val.goodsImage;
		},
		saleNum(val) {
			if (val.actualSales) {
				return val.actualSales;
			} else {
				return val.saleNum;
			}
			// return val.actualSales?val.actualSales:val.saleNum
		}
	}
};
</script>

<style lang="scss">
.goods_c_item {
	background: #fff;
	border-radius: 20rpx;
	overflow: hidden;
	margin: 0 auto;
	padding: 20rpx 0 0 0;
	display: flex;
	flex-direction: column;
	.goods_desc_top {
		width: 100%;
		.goods-img {
			height: 238rpx;
			margin: 0 auto;
			border-radius: 20rpx 20rpx 0 0;
			background-size: cover;
			background-position: center center;
			background-repeat: no-repeat;
			overflow: hidden;
			background-color: #fff;
		}
		.goods-type {
			.type-item {
				color: #fff;
				font-size: 20rpx;
				display: inline-block;
				text-align: center;
				width: 62rpx;
				height: 31rpx;
				line-height: 31rpx;
				&.w {
					background-color: #ffc300;
				}
				&.k {
					background-color: #2a82e4;
				}
				&.d {
					background-color: #00baad;
				}
				&:first-child {
					border-radius: 20rpx 0 0 20rpx;
				}
				&:last-child {
					border-radius: 0rpx 20rpx 20rpx 0rpx;
				}
			}
		}
		.good-store {
			width: 100%;
			margin-top: 15rpx;
			font-size: $fs-base;
			padding: 0 20rpx;
			color: rgba(18, 57, 112, 1);
			font-weight: 700;
			display: inline-block;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
		.goods-name {
			width: 100%;
			font-size: 22rpx;
			color: rgba(18, 57, 112, 1);
			display: inline-block;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			padding: 0 20rpx;
			// height: 80rpx;
			// line-height: 1em;
			// display: -webkit-box;
			// -webkit-line-clamp: 2;
			// -webkit-box-orient: vertical;
			// word-break: break-word;
		}

		.goods_des {
			font-size: 24rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #888888;
			line-height: 36rpx;
			padding: 0 20rpx;
			box-sizing: border-box;
			width: 355rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
	}

	.goods-price {
		padding: 0 20rpx;
		width: 100%;
		display: flex;
		align-items: center;
		flex-direction: column;

		.left {
			width: 100%;
			color: var(--color_price);

			.unit,
			.price_decimal {
				font-size: 24rpx;
				margin-right: 3rpx;
			}

			.price_int {
				font-size: 34rpx;
				line-height: 34rpx;
			}
		}
		.have_sold_text {
			// margin-top: 6rpx;
			color: #858585;
			font-size: 22rpx;
		}
		image {
			width: 42rpx;
			height: 42rpx;
		}
	}
}

.pre_bottom {
	display: flex;
	align-items: center;
}

.have_no_sale {
	width: 100%;
	flex-direction: row !important;
	justify-content: space-between !important;
	padding: 20rpx !important;
	padding-bottom: 0;
	padding-bottom: 26rpx !important;
}
</style>
