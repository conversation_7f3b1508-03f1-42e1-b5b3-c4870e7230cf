<template>
	<div>
		<uniPopup ref="activityPop">
			<image @tap="goView()" class="pop_img" :src="imgUrl + 'activity/luck_draw/pop_banner.png'" mode="widthFix"></image>
		</uniPopup>
	</div>
</template>

<script>
import uniPopup from "./uni-popup/uni-popup.vue";
import { mapMutations, mapState } from "vuex";
export default {
	components: {
		uniPopup
	},
	data() {
		return {
			activityPopup: false,
			imgUrl: getApp().globalData.imgUrl
		};
	},
	methods: {
		// 打开弹窗
		open() {
			this.$refs.activityPop.open();
		},
		// 跳转集合页
		goView() {
			this.$refs.activityPop.close();
			uni.navigateTo({
				url: "/staticPages/luck_draw"
			});
		}
	}
};
</script>

<style lang="less">
.pop_img {
	width: 80vw;
}
</style>
