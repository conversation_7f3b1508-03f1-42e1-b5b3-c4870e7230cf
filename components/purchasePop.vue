<template>
	<view class="purchase_box" :class="{ 'tushou': type == 'tshou', 'piont_purchase': type == 'point' }">
		<uni-popup type="center" ref="popup0">
			<view class="purchase_info">
				<view class="info_header">
					<text>{{ exStateTxt }}</text>
				</view>
				<scroll-view scroll-y="true" class="scroll_goods">
					<view>
						<view class="goods_item_info flex_row_start_start" v-for="(item, index) in exList" :key="index">
							<view class="image" :style="{ backgroundImage: 'url(' + item.image + ')' }">
							</view>
							<view class="goods_text_info flex_column_between_start">
								<text class="text1">{{ item.goodsName }}</text>
								<text class="text2">{{ item.specValues }}</text>
								<text class="oriPrice" v-if="exState == 7">{{ $L('最新价格：') }}{{ $L('￥') }}{{ item.price }}</text>
							</view>
						</view>
					</view>
				</scroll-view>

				<view class="btn_group flex_row_center_center">
					<view class="btn1" @click="close(0)">{{ $L('返回') }}</view>
					<view class="btn1 wBorder" v-if="exState == 7" @click="goNext">{{ $L('继续下单') }}</view>
					<view class="btn1 wBorder" v-if="exState == 4 || exState == 2" @click="delNext">{{ $L('移除并下单') }}</view>
				</view>
			</view>
		</uni-popup>

		<uni-popup type="center" ref="popup1">
			<view class="purchase_info">
				<view class="info_header">
					<text>{{ $L('商品促销信息发生变化') }}</text>
				</view>
				<view class="change_info">
					<text>{{ $L('商品促销信息发生变化') }}</text>
					<text>{{ isDetail ? "" : $L('请返回后 重新下单') }}</text>
				</view>
				<view class="btn_group flex_row_center_center">
					<view class="btn1" @click="close(1)">{{ isDetail ? $L('确定') : $L('返回') }}</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import uniPopup from '@/components/uni-popup/uni-popup.vue'
export default {
	components: {
		uniPopup
	},
	props: ['exList', 'exState', 'exStateTxt', 'isDetail', 'type'],


	methods: {
		open(n = 0) {
			this.$refs[`popup${n}`].open()
		},

		close(n) {
			this.$refs[`popup${n}`].close()
			if (this.isDetail) {

			} else {
				this.$Router.back()
			}
		},

		goNext() {
			this.$refs[`popup${0}`].close()
			this.$emit('goNext')
		},

		delNext() {
			this.$refs[`popup${0}`].close()
			this.$emit('delNext')
		}
	}
}
</script>

<style lang="scss">
.purchase_info {
	padding-top: 30rpx;
	width: 620rpx;
	// height: 730rpx;
	background: #FFFFFF;
	border-radius: 10rpx;
}

.info_header {
	text-align: center;
	position: relative;

	text {
		font-size: 40rpx;
		font-family: PingFang SC-Heavy, PingFang SC;
		font-weight: 800;
		color: #000000;
	}
}

.new_price {
	margin-top: 20rpx;
	padding: 0 20rpx;
	font-size: 26rpx !important;
	font-family: PingFang SC-Medium, PingFang SC;
	font-weight: 500;
	color: var(--color_price) !important;
	display: flex;
	justify-content: flex-end;
}

.oriPrice {
	margin-top: 8rpx;
	font-size: 24rpx;
	font-family: PingFang SC-Medium, PingFang SC;
	font-weight: 500;
	color: var(--color_price);
}

.change_info {
	margin: 70rpx 0;
	padding: 0 70rpx;
	text-align: center;
	font-size: 30rpx;
	font-family: PingFang SC-Medium, PingFang SC;
	font-weight: 500;
	color: #333333;
	line-height: 50rpx;
}

.scroll_goods {
	margin-top: 20rpx;
	height: 520rpx;
	padding: 0 20rpx;

	.goods_item_info {
		padding: 20rpx 0;
		border-bottom: 1px solid #DCDCDC;

		.image {
			min-width: 90rpx;
			width: 90rpx;
			height: 90rpx;
			border-radius: 8rpx;
			background-position: center center;
			background-size: cover;
		}

		.goods_text_info {
			min-height: 90rpx;
			margin-left: 20rpx;
			flex-shrink: 0;
			flex: 1;

			.text1 {
				font-size: 28rpx;
				font-family: Inter-Regular, Inter;
				font-weight: 400;
				color: #222222;
				max-width: 470rpx;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}

			.text2 {
				margin-top: 8rpx;
				font-size: 24rpx;
				font-family: Inter-Regular, Inter;
				font-weight: 400;
				color: #999999;
			}
		}
	}


}


.btn_group {
	margin-top: 14rpx;
	height: 90rpx;
	border-top: 1px solid #DCDCDC;

	.btn1 {
		height: 88rpx;
		flex: 1;
		text-align: center;
		font-size: 34rpx;
		font-family: PingFang SC-Medium, PingFang SC;
		font-weight: 500;
		color: var(--color_vice) !important;
		line-height: 88rpx;

	}

	.wBorder {
		color: var(--color_main) !important;
		border-left: 1px solid #DCDCDC;
	}
}

.tushou {
	.new_price {
		color: var(--color_extral_main) !important;
	}

	.oriPrice {
		color: var(--color_extral_main) !important;
	}

	.btn_group {
		.btn1 {
			color: var(--color_extral_vice) !important;
		}

		.wBorder {
			color: var(--color_extral_main) !important;
		}
	}
}

.piont_purchase {
	.new_price {
		color: var(--color_integral_main) !important;
	}

	.oriPrice {
		color: var(--color_integral_main) !important;
	}

	.btn_group {
		.btn1 {
			color: var(--color_integral_vice) !important;
		}

		.wBorder {
			color: var(--color_integral_main) !important;
		}
	}
}</style>
