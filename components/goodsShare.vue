<template>
	<!-- 分享 -->
	<view class="share">
		<!-- 分享弹框 start -->
		<view class="share_model flex_column_end_center" v-if="share_model" @touchmove.stop.prevent="moveHandle">
			<view class="share_model_list">
				<!-- #ifdef H5 -->
				<view class="share_model_pre" @tap.stop="sldShareBrower(1)" v-if="isWeiXinBrower">
					<image :src="imgUrl + 'goods_detail/wx_share.png'" mode="aspectFit"></image>
					<text>{{ $L('微信好友') }}</text>
				</view>
				<view class="share_model_pre" @tap.stop="sldShareBrower(2)" v-if="isWeiXinBrower">
					<image :src="imgUrl + 'goods_detail/wechat_moments.png'" mode="aspectFit"></image>
					<text>{{ $L('微信朋友圈') }}</text>
				</view>
				<!-- #endif -->
				<!-- wx-1-start -->
				<!-- #ifdef MP-WEIXIN -->
				<button class="share_model_pre" open-type="share">
					<image :src="imgUrl + 'goods_detail/wx_share.png'" mode="aspectFit"></image>
					<text>{{ $L('微信好友') }}</text>
				</button>
				<!-- #endif -->
				<!-- wx-1-end -->
				<!-- app-1-start -->
				<!-- #ifdef APP-PLUS -->
				<view class="share_model_pre" @tap.stop="sldShare(0, 'WXSceneSession')">
					<image :src="imgUrl + 'goods_detail/wx_share.png'" mode="aspectFit"></image>
					<text>{{ $L('微信好友') }}</text>
				</view>
				<view class="share_model_pre" @tap.stop="sldShare(0, 'WXSenceTimeline')">
					<image :src="imgUrl + 'goods_detail/wechat_moments.png'" mode="aspectFit"></image>
					<text>{{ $L('微信朋友圈') }}</text>
				</view>
				<!-- #endif -->
				<!-- app-1-end -->
				<view class="share_model_pre">
					<image :src="imgUrl + 'goods_detail/poster.png'" mode="aspectFit" @click="getPoster"></image>
					<text>{{ $L('生成海报') }}</text>
				</view>
			</view>
			<view class="share_model_close" @click="closeShareModel">
				<image :src="imgUrl + 'goods_detail/share_close.png'" mode="aspectFit"></image>
			</view>
		</view>
		<!-- 分享弹框 end -->
	
		<!-- 生成海报  start-->
		<view class="poster" v-if="poster" @touchmove.stop.prevent="moveHandle">
			<!-- 分享海报弹框 start -->
			<view class="share_model" :class="{ poster_share_model: poster }">
				<sldPoster ref="sldPoster" :info="posterInfo" @cachePoster="cachePoster"></sldPoster>
				
				<view class="share_model_list">
					<!-- #ifdef MP -->
					<view class="share_model_pre" @tap.stop="downloadPoster">
						<image :src="imgUrl + 'goods_detail/poster.png'" mode="aspectFit"></image>
						<text>{{ $L('下载海报') }}</text>
					</view>
					<!-- #endif -->
					<!-- app-2-start -->
					<!-- #ifdef APP-PLUS -->
					<view class="share_model_pre" @tap.stop="saveImgAPP">
						<image :src="imgUrl + 'goods_detail/poster.png'" mode="aspectFit"></image>
						<text>{{ $L('保存海报') }}</text>
					</view>
					<view class="share_model_pre" @tap.stop="sldShare(2, 'WXSceneSession')">
						<image :src="imgUrl + 'goods_detail/wx_share.png'" mode="aspectFit"></image>
						<text>{{ $L('微信好友') }}</text>
					</view>
					<view class="share_model_pre" @tap.stop="sldShare(2, 'WXSenceTimeline')">
						<image :src="imgUrl + 'goods_detail/wechat_moments.png'" mode="aspectFit"></image>
						<text>{{ $L('微信朋友圈') }}</text>
					</view>
					<!-- #endif -->
					<!-- app-2-end -->
				</view>
				<!-- #ifndef H5 -->
				<view class="share_model_close" @click="closeShareModel">
					<image :src="imgUrl + 'goods_detail/share_close.png'" mode="aspectFit"></image>
				</view>
				<!-- #endif -->
				<!-- #ifdef H5 -->
				<image :src="imgUrl + 'goods_detail/poster_share.png'" mode="aspectFit" class="poster_share_img"></image>
				<image :src="imgUrl + 'goods_detail/poster_share_close.png'" mode="aspectFit" class="poster_share_close"
					@click.stop="closePoster"></image>
				<!-- #endif -->
			</view>
			<!-- 分享海报弹框 end -->
		</view>
		<!-- 生成海报  end-->
		
		
		<!-- 微信浏览器分享提示  start-->
		<view class="wx_brower_share_mask" v-if="showWeiXinBrowerTip">
			<view class="wx_brower_share_top_wrap">
				<image :src="imgUrl + 'wx_share_tip.png'" mode="widthFix" @tap="closeShareModel"
					class="wx_brower_share_img"></image>
			</view>
		</view>
		<!-- 微信浏览器分享提示  end-->
	</view>
	<!-- 分享 -->
</template>

<script>
	import sldPoster from '@/components/sld_poster/poster.vue'
	export default{
		components:{
			sldPoster
		},
		
		props:{
			type:{
				type:String,
				default:'normal'
			}
		},
		
		data(){
			return{
				share_model: false, //分享弹框
				showWeiXinBrowerTip:false,
				isWeiXinBrower:false,
				imgUrl: getApp().globalData.imgUrl,
				goodsData:{},
				poster:false
			}
		},
		
		computed:{
			posterInfo() {
				let {
					goodsName,
					goodsBrief,
					defaultProduct
				} = this.goodsData
				
				let url = this.$Route.path
				Object.keys(this.$Route.query).forEach(key=>{
					if(url.indexOf('?')>-1){
						url += `&${key}=${this.$Route.query[key]}`
					}else{
						url += `?${key}=${this.$Route.query[key]}`
					}
				})				
			
				let info = {
					name: goodsName,
					brief: goodsBrief,
					url:getApp().globalData.apiUrl + url.substring(1)
				}
			
				if (defaultProduct) {
					info.marketPrice = defaultProduct.marketPrice ? `¥${Number(defaultProduct.marketPrice).toFixed(2)}` :''
					info.image = defaultProduct.goodsPics[0]
					
					if(this.type=='point'){
						if(defaultProduct.cashPrice){
							info.price = `${defaultProduct.integralPrice}积分+¥${Number(defaultProduct.cashPrice).toFixed(2)}`
						}else{
							info.price = `${defaultProduct.integralPrice}积分`
						}
					}else{
						info.price = '¥' + Number(defaultProduct.productPrice).toFixed(2)
					}
				}
			
				return info
			}
		},
		
		
		mounted() {
			// #ifdef H5
			this.isWeiXinBrower = this.$isWeiXinBrower()
			// #endif 
		},
		
		methods:{
			//浏览器分享
			sldShareBrower(type) {
				this.showWeiXinBrowerTip = true
				this.share_model = false
				this.$WXBrowserShareThen(type, {
					title: this.goodsData.goodsName,
					desc: this.goodsData.goodsBrief,
					link: this.goodsData.shareLink,
					imgUrl: this.goodsData.shareImage
				})
			},
			
			
			//关闭分享弹框
			closeShareModel() {
				this.share_model = false
				this.showWeiXinBrowerTip = false
				this.poster = false
			},
			//获取海报
			getPoster() {
				//wx-2-start
				// #ifdef MP-WEIXIN
				if(this.posterInfo.posterCache){
					this.share_model = false
					this.showWeiXinBrowerTip = false
					this.poster = true
				}else{
					this.getSunCode()
				}
				// #endif
				//wx-2-end
				// #ifndef MP-WEIXIN
				this.share_model = false
				this.showWeiXinBrowerTip = false
				this.poster = true
				// #endif
			},
			
			//分享 type：分享类型 0 图文 2图片，scene 场景 WXSceneSession：分享朋友  WXSenceTimeline：分享朋友圈
			sldShare(type, scene) {
				let shareData = {};
				let _this= this
				let {
					goodsData,
					sharePoster
				} = this;
				if (type == 0) {
					shareData.href = goodsData.shareLink;
					shareData.title = goodsData.goodsName;
					shareData.summary = goodsData.goodsBrief;
					shareData.imageUrl = goodsData.shareImage;
				} else if (type == 2) {
					shareData.imageUrl = this.posterInfo.posterCache;
				}
				this.$weiXinAppShare(type, scene, shareData);
				this.closeShareModel(); //关闭分享
			},
			
			//下载海报
			downloadPoster() {
				this.$refs.sldPoster.savePoster()
			},
			
			//APP端保存图片的方法
			saveImgAPP() {
				this.$refs.sldPoster.savePoster()
			},
			
			moveHandle(){
				
			},
			
			
			injectInfo(goodsInfo){
				this.goodsData = goodsInfo
				this.share_model = true
			},
			
			
			getSunCode() {
				uni.showLoading({
					title:'加载中...'
				})
				let {defaultProduct} = this.goodsData
				this.$request({
					url: 'v3/goods/common/sunCode',
					data: {
						productId: defaultProduct.productId||defaultProduct.integralProductId,
						goodsId: this.goodsData.goodsId||0,
						page: this.$Route.path.substring(1),
					}
				}).then(res => {
					if(res.state==200){
						this.posterInfo.qrcode = `data:image/png;base64,${res.data}`
						this.share_model = false
						this.showWeiXinBrowerTip = false
						this.poster = true
					}else{
						uni.hideLoading()
						setTimeout(()=>{
							this.$api.msg(res.msg)
						},500)
					}
				})
			},
			
			//缓存poster组件生成的海报
			cachePoster(url){
				this.posterInfo.posterCache = url
			},
			
			
			//关闭海报
			closePoster() {
				this.poster = false
			},
			
		}
	}
</script>

<style lang="scss">
	.share_model {
		width: 750rpx;
		height: 100%;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		margin: 0 auto;
		background: rgba(0, 0, 0, 0.6);
		z-index: 100;
	}
	
	.share_model_list {
		display: flex;
		justify-content: space-around;
		padding: 0 50rpx;
		box-sizing: border-box;
		
		z-index: 110;
		width: 750rpx;
	
		.share_model_pre {
			display: flex;
			flex-direction: column;
			align-items: center;
			background: transparent;
			border-radius: 0;
			height: auto;
			line-height: auto;
			margin: 20rpx 0;
	
			&::after {
				border-width: 0;
			}
	
			image {
				width: 105rpx;
				height: 105rpx;
			}
	
			text {
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #ffffff;
				line-height: 36rpx;
				margin-top: 30rpx;
			}
		}
	}
	
	.share_model_close {
		width: 46rpx;
		height: 46rpx;
		
		z-index: 110;
		left: 0;
		right: 0;
		margin: 0 auto;
		margin-bottom: 60rpx;
	
		image {
			width: 46rpx;
			height: 46rpx;
		}
	}

	.poster {
		width: 750rpx;
		height: 100%;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		margin: 0 auto;
		background: rgba(0, 0, 0, 0.6);
		z-index: 100;
	
		.poster_share_img {
			width: 390rpx;
			height: 90rpx;
			margin: 72rpx 0 22rpx;
		}
	
		.poster_share_close {
			width: 49rpx;
			height: 49rpx;
		}
	
		.poster_share_model {
			width: 750rpx;
			height: 100%;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			padding-top: 100rpx;
	
			.poster_share_img {
				width: 390rpx;
				height: 90rpx;
				margin: 72rpx 0 22rpx;
			}
	
			.poster_share_close {
				width: 49rpx;
				height: 49rpx;
			}
		}
	}

	button {
	    padding: 0;
	    margin: 0;
	}
</style>