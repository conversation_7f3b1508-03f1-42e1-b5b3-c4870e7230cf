<template>
	<uni-popup ref="privacyModal" type="center">
		<view class="privacy_modal">
			<view class="privacy_main">
				<view class="privacy_title">温馨提示</view>
				<view class="privacy_content">在你使用服务之前，请仔细阅读<text class="privacy_link" @click="onClickPrivacyPopupTitle">{{
					PrivacyProtocol.privacyContractName }}</text>。如果你同意{{
					PrivacyProtocol.privacyContractName }}，请点击“同意”开始使用。
				</view>
				<view class="privacy_btns">
					<view class="privacy_refuse">
						<button @click="handleRefusePrivacyAuthorization">拒绝</button>
					</view>
					<view class="privacy_accept">
						<button id="agree-btn" class="btn-agree" open-type="agreePrivacyAuthorization"
							@agreeprivacyauthorization="handleAgreePrivacyAuthorization">
							同意
						</button>
					</view>
				</view>
			</view>
		</view>
	</uni-popup>
</template>
<script>
	let resolvePrivacyAuthorization;
	import { sldStatEvent } from "@/utils/stat.js";
	import uniPopup from '@/components/uni-popup/uni-popup.vue';
	export default {
		components: {
			uniPopup,
		},
		data() {
			return {
				PrivacyProtocol: {
					needAuthorization: '',
					privacyContractName: '',
				}
			}
		},
		methods: {
			// 打开隐私协议
			onClickPrivacyPopupTitle() {
				wx.openPrivacyContract();
			},
			// 用户点击拒绝
			handleRefusePrivacyAuthorization() {
				let _this = this;
				uni.showModal({
					content: '如果拒绝,我们将无法获取您的信息,包括手机号、位置信息、相册等功能,确定要拒绝吗?',
					success: (res) => {
						if (res.confirm) {
							getApp().globalData.allow_privacy = false;
							_this.closePop();
							if (resolvePrivacyAuthorization) {
								resolvePrivacyAuthorization({
									event: 'disagree'
								})
							}
						}
					}
				})
			},
			// 用户点击同意
			handleAgreePrivacyAuthorization() {
				let _this = this;
				getApp().globalData.allow_privacy = true;
				if (uni.getStorageSync('allow_initStat')) { //app.vue文件中统计事件暂停执行，此处通过后再执行
					sldStatEvent(JSON.parse(uni.getStorageSync('allow_initStat')));
				}
				_this.closePop();
				if (resolvePrivacyAuthorization) {
					resolvePrivacyAuthorization({
						buttonId: 'agree-btn',
						event: 'agree'
					})
				}
			},
			// 弹窗打开事件
			open() {
				this.$refs.privacyModal.open();
				if (wx.onNeedPrivacyAuthorization) {
					wx.onNeedPrivacyAuthorization(resolve => {
						resolvePrivacyAuthorization = resolve;
					})  
				} 
			},
			// 弹窗关闭事件
			closePop() {
				if (uni.getStorageSync('allow_initStat')) {
					uni.removeStorageSync('allow_initStat');
				}
				this.$refs.privacyModal.close();
			},
		}
	}
</script>
<style lang="scss" scoped>
	.privacy_modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, .4);
		display: flex;
		align-items: center;
		justify-content: center;
		
		.privacy_main {
			width: 600rpx;
			line-height: 40rpx;
			padding: 16px;
			background: #fff;
			border-radius: 12rpx;
			overflow: hidden;
			.privacy_title {
				text-align: center;
			}
			.privacy_content {
				font-size: 30rpx;
				margin: 30rpx 16px 40rpx;
				.privacy_link {
					color: #3C7EF7;
				}
			}
			.privacy_btns {
				display: flex;
				align-items: center;
				justify-content: center;
				color: #454545;
				button {
					font-size: 30rpx;
					height: 70rpx;
					line-height: 70rpx;
				}
				.privacy_refuse {
					flex: 1;
					margin-right: 14rpx;
					button {
						color: #232323;
						background-color: #F3F3F3;
					}
				}
				.privacy_accept{
					flex: 1;
					margin-left: 14rpx;
					button {
						color: #fff;
						background-color: #3478F7;
					}
				}
			}
		}
	}
</style>