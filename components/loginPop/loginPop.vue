<template>
    <uni-popup ref="loginModal" type="dialog">
        <uni-popup-dialog
            type="input"
            :title="$L('提示')"
            :content="$L('需要登录后才能使用此功能哟~')"
            :duration="2000"
            :confirmText="$L('去登录')"
            @confirm="confirmLogin"
            @close="close"
            :before-close="true"
        ></uni-popup-dialog>
    </uni-popup>
</template>

<script>
import { callNativeLogin, isAppInset } from '@/utils/common.js';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue';
export default {
    components: {
        uniPopup,
        uniPopupDialog
    },

    data() {
        return {};
    },

    methods: {
        confirmLogin() {
            this.$refs.loginModal.close();
            this.$emit('confirmLogin');
            // #ifdef MP-WEIXIN
            uni.removeStorageSync('fromurl');
            let url = this.$Route.path;
            const query = this.$Route.query;
            uni.setStorageSync('fromurl', {
                url,
                query
            });
            // #endif
            if (isAppInset()) {
                console.log('loginPop App内嵌H5页面，调用原生登录');
                callNativeLogin();
            } else {
                this.$Router.push('/pages/public/login');
            }
        },
        openLogin(type) {
            this.type = type;
            this.$refs.loginModal.open();
        },
        close(fn) {
            fn && fn();
            this.$emit('closeLogin');
            this.$refs.loginModal.close();
        }
    }
};
</script>

<style></style>
