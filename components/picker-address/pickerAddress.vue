<template>
    <view>
        <view class="picker-tap" @click="open">
            <text class="value-text" :style="{ color: pickerValue ? 'rgba(0,0,0,0.80)' : placeholderColor, fontSize: '30rpx' }">{{ pickerValue || placeholder }}</text>
            <slot name="arrow">
                <view v-if="showArrow" class="arrow-icon" :style="{ boxShadow: `-4rpx 4rpx 0 0rpx ${arrowColor} inset` }" />
            </slot>
        </view>
        <view v-if="showPicker" class="picker-modal"></view>
        <view v-if="showPicker" class="picker-content" :style="{ transform: showPicker ? 'translateY(0)' : 'translateY(100%)' }">
            <view class="picker-header">
                <view v-if="cancelType === 'text'" :style="{ color: cancelColor, fontSize: cancelFontSize, fontWeight: cancelFontWeight }" @click="close">取消</view>
                <icon v-else type="clear" size="18" @click="close" />
                <view class="packer-title" :style="{ color: titleColor, fontSize: titleFontSize, fontWeight: titleFontWeight }">{{ title }}</view>
                <view :style="{ color: confirmColor, fontSize: confirmFontSize, fontWeight: confirmFontWeight }" @click="confirmPicker">确定</view>
            </view>
            <picker-view indicator-style="height: 90rpx;" class="picker-view" :value="data.indexArr" @change="changePicker">
                <picker-view-column>
                    <view v-for="(item, index) in data.province" :key="index" class="picker-item" :style="{ fontSize: itemFontSize }">{{ item.value }}</view>
                </picker-view-column>
                <picker-view-column>
                    <view v-for="(item, index) in data.city" :key="index" class="picker-item" :style="{ fontSize: itemFontSize }">{{ item.value }}</view>
                </picker-view-column>
                <picker-view-column v-if="isShowArea">
                    <view v-for="(item, index) in data.area" :key="index" class="picker-item" :style="{ fontSize: itemFontSize }">{{ item.value }}</view>
                </picker-view-column>
            </picker-view>
        </view>
    </view>
</template>

<script>
/**
 * 示例 <pickerAddress class="picker" title="选择城市" style="width: 100%;" :is-show-area='false' @change="handleChange" />
 * */
export default {
    name: 'PickerAddress',
    props: {
        isShowArea: {
            type: Boolean,
            default: true
        },
        onlyShowCity: {
            type: Boolean,
            default: false
        },
        value: {
            type: String,
            default: ''
        },
        placeholder: {
            type: String,
            default: '请选择城市'
        },
        placeholderColor: {
            type: String,
            default: 'rgba(0,0,0,0.25)'
        },
        cancelColor: {
            type: String,
            default: 'rgba(0,0,0,0.40)'
        },
        cancelType: {
            type: String,
            default: 'text'
        },
        cancelFontSize: {
            type: String,
            default: '28rpx'
        },
        cancelFontWeight: {
            type: Number,
            default: 400
        },
        title: {
            type: String,
            default: ''
        },
        titleColor: {
            type: String,
            default: 'rgba(0,0,0,0.80)'
        },
        titleFontSize: {
            type: String,
            default: '32rpx'
        },
        titleFontWeight: {
            type: Number,
            default: 500
        },
        confirmColor: {
            type: String,
            default: '#C70E2D'
        },
        confirmFontSize: {
            type: String,
            default: '28rpx'
        },
        confirmFontWeight: {
            type: Number,
            default: 400
        },
        itemFontSize: {
            type: String,
            default: '32rpx'
        },
        showArrow: {
            type: Boolean,
            default: true
        },
        arrowColor: {
            type: String,
            default: 'rgba(0,0,0,0.25)'
        }
    },

    data() {
        return {
            showPicker: false,
            pickerValue: '',
            data: {
                value: {},
                indexArr: [0, 0, 0],
                cityIndex: 0,
                areaIndex: 0,
                province: [],
                city: [],
                area: []
            }
        };
    },

    watch: {
        value: {
            handler(newVal) {
                this.pickerValue = newVal;
            },
            immediate: true
        }
    },

    mounted() {
        this.data.cityIndex = 0;
        this.data.areaIndex = 0;
        this.data.indexArr = [0, 0, 0];
        // 加载省市区JSON数据
        uni.request({
            url: getApp().globalData.imgUrl + 'data/area.json',
            success: (res) => {
                this.data.province = res.data;
                // 初始化为第一条数据
                this.data.city = this.data.province[this.data.cityIndex].children;
                this.data.area = this.data.city[this.data.areaIndex].children;
            },
            fail: () => {
                console.error('加载省市区数据失败');
            }
        });
    },

    methods: {
        // 滚动触发
        changePicker(e) {
            this.data.indexArr = e.detail.value;
            this.data.cityIndex = e.detail.value[0];
            this.data.areaIndex = e.detail.value[1];
            this.data.city = this.data.province[this.data.cityIndex].children;
            this.data.area = this.data.city[this.data.areaIndex].children;
        },

        // 确认
        confirmPicker() {
            this.showPicker = false;
            // 确认数据
            if (this.isShowArea) {
                this.data.value = {
                    province: {
                        code: this.data.province[this.data.indexArr[0]].code,
                        value: this.data.province[this.data.indexArr[0]].value
                    },
                    city: {
                        code: this.data.city[this.data.indexArr[1]].code,
                        value: this.data.city[this.data.indexArr[1]].value
                    },
                    area: {
                        code: this.data.area[this.data.indexArr[2]].code,
                        value: this.data.area[this.data.indexArr[2]].value
                    },
                    indexArr: this.data.indexArr
                };
                if (this.onlyShowCity) {
                    this.pickerValue = `${this.data.value.city.value}`;
                } else {
                    this.pickerValue = `${this.data.value.province.value}${this.data.value.city.value}${this.data.value.area.value}`;
                }
            } else {
                this.data.value = {
                    province: {
                        code: this.data.province[this.data.indexArr[0]].code,
                        value: this.data.province[this.data.indexArr[0]].value
                    },
                    city: {
                        code: this.data.city[this.data.indexArr[1]].code,
                        value: this.data.city[this.data.indexArr[1]].value
                    },
                    indexArr: this.data.indexArr
                };
                if (this.onlyShowCity) {
                    this.pickerValue = `${this.data.value.city.value}`;
                } else {
                    this.pickerValue = `${this.data.value.province.value}${this.data.value.city.value}`;
                }
            }
            // 传递数据父组件
            this.$emit('change', this.data.value);
            this.$emit('update:value', this.pickerValue);

            // 重新初始化
            this.data.cityIndex = 0;
            this.data.areaIndex = 0;
            this.data.indexArr = [0, 0, 0];
        },

        // 打开
        open() {
            this.data.cityIndex = 0;
            this.data.areaIndex = 0;
            this.data.indexArr = [0, 0, 0];

            this.showPicker = true;
            this.data.city = this.data.province[this.data.cityIndex].children;
            this.data.area = this.data.city[this.data.areaIndex].children;
        },

        // 关闭
        close() {
            this.data.cityIndex = 0;
            this.data.areaIndex = 0;
            this.data.indexArr = [0, 0, 0];

            this.showPicker = false;
        }
    }
};
</script>

<style lang="scss" scoped>
.picker-tap {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    box-sizing: border-box;
    .value-text {
        // 互换行，超出部分省略
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        max-width: calc(100% - 28rpx);
        text-align: right;
        margin-right: 10rpx;
    }
    .arrow-icon {
        width: 18rpx;
        height: 18rpx;
        transform: rotate(135deg);
    }
}

.picker-modal {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 9;
    background-color: rgba(0, 0, 0, 0.4);
    animation: dropdown1 0.2s linear;
}

.picker-content {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2048;
    border-top-left-radius: 30rpx;
    border-top-right-radius: 30rpx;
    background-color: #fff;
    transition: all 0.3s;
    animation: dropdown 0.2s linear;
}

.picker-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 20rpx;
    padding: 20rpx 0;
}

.packer-title {
    text-align: center;
}

.picker-view {
    position: relative;
    left: 0;
    right: 0;
    bottom: 0;
    height: 490rpx;
    background-color: rgba(255, 255, 255, 1);
}

.picker-item {
    line-height: 70rpx;
    text-overflow: ellipsis;
    text-align: center;
}

@keyframes dropdown {
    0% {
        height: 0;
    }

    100% {
        height: 490rpx;
    }
}

@keyframes dropdown1 {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}
</style>
