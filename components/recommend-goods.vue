<template name="recommendGoods">
	<view v-if="recommendGoods && recommendGoods.length > 0">
		<view class="recommend-title">
			<image :src="imgUrl+'user/recommend-title.png'" mode="aspectFit"/>
		</view>
		<view class="recommend-goods flex_row_start_start">
			<goodsItemV v-for="(item,index) in recommendGoods" :goods_info="item" :key='index' :show_sale="false" :icon_type="1" @reloadCartList="reloadCartList" @addCart="addCart" ref="recom_goods" :border_radius="10"/>
		</view>
		<loadingState :state='loadingState'/>
	</view>
</template>

<script>
	import goodsItemV from "@/components/goods_item_v.vue";
	import loadingState from "@/components/loading-state.vue";
	export default {
		name: "recommendGoods",
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				recommendGoods: [],
				loadingState: 'first_loading',
				pageSize: 8,
				current: 1,
				hasMore: true,//是否还有数据
			}
		},
		props: {
			arriveBotFlag: {
				type: Boolean,
				default: false,
			}
		},
		components: {
			goodsItemV,
			loadingState
		},
		created() {
			
		},
		mounted() {
			this.getData();//获取推荐商品数据
		},
		methods: {
			getData() {
				let param = {};
				param.url = 'v3/goods/front/goods/recommendList';
				param.method = 'GET';
				param.data = {};
				param.data.queryType = 'cart';
				// param.data.queryDetail = 'recommend';
				param.data.pageSize = this.pageSize;
				param.data.current = this.current;
				this.loadingState = this.loadingState == 'first_loading'?this.loadingState:'loading';
				this.$request(param).then(res => {
					if (res.state == 200) {
						if(this.current == 1){
							this.recommendGoods = res.data.list;
						}else{
							this.recommendGoods = this.recommendGoods.concat(res.data.list);
						}
						this.hasMore = this.$checkPaginationHasMore(res.data.pagination||{});//是否还有数据
						if(this.hasMore){
							this.current++;
							this.loadingState = 'allow_loading_more';
						}else{
							this.loadingState = 'no_more_data';
						}
						// 子组件向父组件传值
						uni.$emit("recommendGoods",{
							recommendLen:this.recommendGoods.length
						})
					} else {
						//错误提示
					}
				})
			},
			//页面到底部加载更多数据
			getMoreData(){
				if(this.hasMore){
					this.getData();
				}
			},
			reloadCartList(val){
				this.$emit('reload_cart',val)
			},
			addCart(val){
				this.$emit('addToCart',val)
			}
		}
	}
</script>
<style lang='scss'>
	.list-scroll-content{
		height: 100vh;
	}
	.recommend-title {
		display: flex;
		justify-content: center;

		image {
			width: 387rpx;
			height: 34rpx;
			margin: 40rpx 0;
		}
	}
	.recommend-goods {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		width: 100%;
		padding:0 20rpx;
		box-sizing: border-box;
	}
</style>
