/* 主题颜色变量 */
$color1: #c70e2d;
$color2: #fc701e;
$bg1: #f5f6f7;
/* 主题文字尺寸 */
$fs-m: 24rpx;
$fs-s: 26rpx;
$fs-base: 28rpx;
$fs-lg: 32rpx;

/**
* 全局样式
*/
@mixin trim($numLines: null) {
    @if $numLines !=null {
        display: flex;
        overflow-y: hidden;
        -webkit-line-clamp: $numLines;
        -webkit-box-orient: vertical;
    } @else {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

.line1 {
    @include trim;
}

.line2 {
    @include trim(2);
}
.omitLine1 {
    display: -webkit-box;
    overflow: hidden;
    white-space: normal !important;
    text-overflow: ellipsis;
    word-wrap: break-word;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
}
.omitLine2 {
    display: -webkit-box;
    overflow: hidden;
    white-space: normal !important;
    text-overflow: ellipsis;
    word-wrap: break-word;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}
/* 页面左右间距 */
$page-row-spacing: 30upx;
$page-color-base: #f8f8f8;
$page-color-light: #f8f6fc;
$base-color: #fa436a;

/* 文字尺寸 */
$font-sm: 24upx;
$font-base: 28upx;
$font-lg: 32upx;
/*文字颜色*/
$font-color-dark: #303133;
$font-color-base: #606266;
$font-color-light: #909399;
$font-color-disabled: #c0c4cc;
$font-color-spec: #4399fc;
/* 边框颜色 */
$border-color-dark: #dcdfe6;
$border-color-base: #e4e7ed;
$border-color-light: #ebeef5;
/* 图片加载中颜色 */
$image-bg-color: #eee;
/* 行为相关颜色 */
$uni-color-primary: #fa436a;
$uni-color-success: #4cd964;
$uni-color-warning: #f0ad4e;
$uni-color-error: #dd524d;

/* zokxc-设计规范 */
$bg-color-split: #f5f5f5; //用于分割背景的底色,页面背景色
$border-color-split: rgba(0, 0, 0, 0.1); //辅助分割线
$main-color: #fc1c1c; //主体红色
$com-v-border: 20rpx; //主体内容距离两边的距离
$com-h-border: 20rpx; //主体内容距上下模块的间距
$com-main-font-color: #2d2d2d; //主要内容字体颜色，eg:商品名称
$main-font-color: #333; //用于重要级文字信息、内页标题信息，如导航名称、大板块标题、类目名称等
$main-second-color: #666; //用于普通级段落信息引导词二级标题信息，如商品详情段落标题信息
$main-third-color: #999; //用于辅助、次要的文字信息，如商品评论、规格标识

/* 积分商城 颜色*/
$point-good-price-color: #b64cfb; //用于积分商城商品价格背景颜色
$point-btn-color: #9344ff; //用于积分商城兑换按钮背景颜色
$point-index-title-color: #7b2ad0; //用于积分商城首页顶部背景颜色

/*推手*/
$ts-but: linear-gradient(270deg, #f04047 0%, #fe7951 100%);
$ts-but2: linear-gradient(270deg, #ff2c40 0%, #ff3f5e 100%);
$ts-ac: #ff3044;
$ts-font-color: #e9323e;
$ts-font-color2: #ff0000;

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 颜色变量 */

/* 行为相关颜色 */
$uni-color-primary: #007aff;
$uni-color-success: #4cd964;
$uni-color-warning: #f0ad4e;
$uni-color-error: #dd524d;

/* 文字基本颜色 */
$uni-text-color: #333; //基本色
$uni-text-color-inverse: #fff; //反色
$uni-text-color-grey: #999; //辅助灰色，如加载更多的提示信息
$uni-text-color-placeholder: #808080;
$uni-text-color-disable: #c0c0c0;

/* 背景颜色 */
$uni-bg-color: #ffffff;
$uni-bg-color-grey: #f8f8f8;
$uni-bg-color-hover: #f1f1f1; //点击状态颜色
$uni-bg-color-mask: rgba(0, 0, 0, 0.4); //遮罩颜色

/* 边框颜色 */
$uni-border-color: #c8c7cc;

/* 尺寸变量 */

/* 文字尺寸 */
$uni-font-size-sm: 12px;
$uni-font-size-base: 14px;
$uni-font-size-lg: 16;

/* 图片尺寸 */
$uni-img-size-sm: 20px;
$uni-img-size-base: 26px;
$uni-img-size-lg: 40px;

/* Border Radius */
$uni-border-radius-sm: 2px;
$uni-border-radius-base: 3px;
$uni-border-radius-lg: 6px;
$uni-border-radius-circle: 50%;

/* 水平间距 */
$uni-spacing-row-sm: 5px;
$uni-spacing-row-base: 10px;
$uni-spacing-row-lg: 15px;

/* 垂直间距 */
$uni-spacing-col-sm: 4px;
$uni-spacing-col-base: 8px;
$uni-spacing-col-lg: 12px;

/* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

/* 文章场景相关 */
$uni-color-title: #2c405a; // 文章标题颜色
$uni-font-size-title: 20px;
$uni-color-subtitle: #555555; // 二级标题颜色
$uni-font-size-subtitle: 26px;
$uni-color-paragraph: #3f536e; // 文章段落颜色
$uni-font-size-paragraph: 15px;
