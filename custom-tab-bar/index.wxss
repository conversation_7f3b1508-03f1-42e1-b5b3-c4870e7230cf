.sc-tabbar-wrap {
	--tabbar-height: 100rpx;
}

.sc-tabbar {
	bottom: 0;
	left: 0;
	right: 0;
	position: relative;
	padding-bottom: 30rpx;
	/* env(safe-area-inset-bottom); */
	width: 100%;
	box-sizing: content-box;
	height: var(--tabbar-height);
}

.sc-tabbar-main {
	position: absolute;
	z-index: 2;
	display: flex;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0);
}

.tabbar-item {
	position: relative;
	display: flex;
	align-items: center;
	flex-direction: column;
	justify-content: center;
	/*  25% + 2px是为了给防止中间出现1px的空隙 */
	width: 25%;
	height: 100%;
	margin-left: -1px;
	margin-right: -1px;
	background-color: #FBFBFB;
	border-top: 1px solid #E3E3E3;
}
.tabbar-item.icon-in{
	border: none;
	background-color: transparent;
}

.item-icon {
	width: 40rpx;
	height: 40rpx;
	/* transition: all 0.3s ease; */
}

.item-icon.icon-out {
	/* transform: translateY(0rpx); */
}

.item-icon.icon-in {
	opacity: 0;
	/* transform: translateY(-10rpx); */
}

.item-text {
	width: 100%;
	text-align: center;
	line-height: 45rpx;
	transition: all 0.1s ease;
	font-size: 12px;
}

.item-text.text-out {
	opacity: 1;
}

.item-text.text-in {
	opacity: 0;
}

.tb-bg {
	position: absolute;
	z-index: 1;
	display: flex;
	justify-content: center;
	/* background: radial-gradient(circle at 50% 0rpx, transparent 90rpx, #FBFBFB 0) top left 100% no-repeat; */
	height: 100%;
}

.tb-bg.is-animation {
	/* transition: left 0.3s ease; */
}

.bg-middle {
	display: flex;
	justify-content: center;
	height: 100%;
}

.bg-ao {
	display: block;
	width: 100%;
	height: 100%;
}

.middle-circular {
	position: absolute;
	top: -5rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background-color: #fff;
}

.circular-icon {
	position: absolute;
	transition: all 0.5s ease;
	width: 40rpx;
	height: 40rpx;
}
/* 
.circular-icon.activeIcon-out {
	opacity: 0;
	transition: all 0s ease;
	transform: translateY(30rpx);
}

.circular-icon.activeIcon-in {
	opacity: 1;
} */