<view class="sc-tabbar-wrap">
  <view class="sc-tabbar">
    <view class="sc-tabbar-main" style="grid-template-columns: repeat({{list.length}}, 1fr)">
      <view wx:for="{{list}}" wx:key="index" class="tabbar-item {{selected == index ? 'icon-in' : 'icon-out'}}" data-index="{{index}}" data-path="{{item.pagePath}}" bindtap="switchTab">
        <image class="item-icon {{selected == index ? 'icon-in' : 'icon-out'}}" src="{{item.iconPath}}" />
        <text class="item-text {{selected == index ? 'text-in' : 'text-out'}}" style="color: {{activeIndex == index ? activeTextColor : textColor}}">{{item.text}}</text>
      </view>
    </view>
    <view style="width:{{bgWidth}};left: {{bgLeft}}rpx;" class="tb-bg">
      <view class="bg-middle" style="width:{{750 / list.length + 8}}rpx">
        <image class="bg-ao" src="{{base64Img}}" />
        <view class="middle-circular" style="background-color:{{circularColor}}">
            <image class="circular-icon {{animation?'activeIcon-in':'activeIcon-out'}}" src="{{list[selected].selectedIconPath}}" />
        </view>
      </view>
    </view>
  </view>
</view>