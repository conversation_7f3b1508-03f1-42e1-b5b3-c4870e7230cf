Component({
    data: {
        selected: 10,
        color: '#6E6E6E',
        selectedColor: '#6E6E6E',
        activeTextColor: '#6E6E6E',
        textColor: '#666',
        circularColor: '#c70e2d',
        bgLeft: 0,
        animation: false,
        bgWidth: 750 * 2 + 750 / 5 + 'rpx',
        base64Img:
            'data:image/png;base64,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',
        list: [
            {
                pagePath: '/pages/index/index',
                iconPath: '/static/tab-bar/home.png',
                selectedIconPath: '/static/tab-bar/home_sel.png',
                text: '首页'
            },
            {
                pagePath: '/pages/shop/home/<USER>',
                iconPath: '/static/tab-bar/shop.png',
                selectedIconPath: '/static/tab-bar/shop_sel.png',
                text: '商城'
            },
            {
                pagePath: '/pages/vehicle/home',
                iconPath: '/static/tab-bar/vehicle.png',
                selectedIconPath: '/static/tab-bar/vehicle_sel.png',
                text: '车辆'
            },
            {
                pagePath: '/pages/service/home',
                iconPath: '/static/tab-bar/service.png',
                selectedIconPath: '/static/tab-bar/service_sel.png',
                text: '服务'
            },
            {
                pagePath: '/pages/user/user',
                iconPath: '/static/tab-bar/user.png',
                selectedIconPath: '/static/tab-bar/user_sel.png',
                text: '我的'
            }
        ]
    },
    lifetimes: {
        ready: function () {
            console.log('在渲染线程被初始化已经完成');
        }
    },
    methods: {
        switchTab(e) {
            const data = e.currentTarget.dataset;
            const url = data.path;
            if (this.data.selected == data.index) return;
            wx.switchTab({
                url,
                success: () => {
                    // console.log('跳转成功：', wx.getStorageSync('fromTabIndex'))
                },
                fail: (err) => {
                    console.log('跳转失败：', err);
                }
            });
        },
        resetAnimation() {
            this.setData({
                animation: false
            });
        }
    }
});
