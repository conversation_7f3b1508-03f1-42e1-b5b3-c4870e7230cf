import Vue from 'vue'
import Vuex from 'vuex'
import {
	updateStatCommonProperty
} from '../utils/stat.js'
import Config from '../utils/config.js'
import request from '../utils/request.js'
Vue.use(Vuex)
let timer = null
let clickCount = 0
let tmp_args = []
let fun_args = []
const store = new Vuex.Store({
	state: {
		hasLogin: false,
		userInfo: {
			access_token: '',
			refresh_token: ''
		},
		userCenterData: {}, //个人中心页面数据
		cartData: {}, //购物车数据
		addressList: [], //收货地址列表
		chatBaseInfo: uni.getStorageSync('chatBaseInfo') ? uni.getStorageSync('chatBaseInfo') :
		{}, //聊天的基本信息，包含会员id、头像、店铺id、头像
		memberConfig: {},
		x_diyStyle: {},
		locationObj: uni.getStorageSync('sldStatCommonProperty') ? uni.getStorageSync('sldStatCommonProperty') :
		{},
		storeDetail: uni.getStorageSync('storeDetail') ? uni.getStorageSync('storeDetail') : {}
	},
	mutations: {
		login(state, provider) {
			state.hasLogin = true;
			state.userInfo = provider;
			updateStatCommonProperty({
				memberId: provider.memberId
			}); //登录成功需要更新统计里面的会员id
			//缓存用户登陆状态
			uni.setStorageSync('userInfo', provider);
		},
		appToh5(state, provider) {
			state.hasLogin = true;
			state.userInfo = provider;
			//获取个人中心的数据
			uni.request({
				url: Config.apiUrl + 'v3/member/front/member/memberInfo',
				method: 'get',
				header: {
					Authorization: 'Bearer ' + provider.access_token
				},
				success: res => {
					if (res.data.state == 266) {
						store.commit('logout', '')
					} else {
						store.commit('setUserCenterData', res.data)
						//缓存用户登陆状态
						uni.setStorageSync('userInfo', provider);
					}
				}
			})
		},
		setuserInfo(state, provider) {
			state.userInfo = {
				...state.userInfo,
				...provider
			};
			uni.setStorageSync('userInfo', state.userInfo);
		},
		logout(state) {
			state.hasLogin = false;
			state.userInfo = {};
			state.userCenterData = {};
			state.cartData = {};
			state.addressList = [];
			state.chatBaseInfo = {}
			uni.removeStorage({
				key: 'addressId'
			});
			uni.removeStorage({
				key: 'userInfo'
			});
			uni.removeStorage({
				key: 'userCenterData'
			});
			//退出登录需要将会员id置为0
			// updateStatCommonProperty({
			// 	memberId: 0
			// });
		},
		//设置个人中心的数据
		setUserCenterData(state, provider) {
			state.userCenterData = provider
			//缓存用户个人信息
			uni.setStorageSync('userCenterData', provider)
		},

		//操作购物车的数据
		operateCartData(state, provider) {
			state.cartData = provider
		},

		//操作收货地址
		operateAddressData(state, provider) {
			state.addressList = provider
		},
		//保存聊天的会员id、会员头像，店铺id、店铺头像
		saveChatBaseInfo(state, provider) {
			state.chatBaseInfo = provider
			//缓存聊天的基本信息
			uni.setStorageSync('chatBaseInfo', provider)
		},

		saveMemberConfig(state, payload) {
			state.memberConfig = {
				...state.memberConfig,
				...payload
			}
		},

		saveDiyStyle(state, provider) {
			state.x_diyStyle = provider
			uni.setStorageSync('x_diyStyle', provider)
		},

		setLocation(state, provider) {
			state.locationObj = {
				...state.locationObj,
				...provider
			}
			uni.setStorageSync('locationObj', state.locationObj)
		},

	},
	actions: {
		getMemberConfig(context, args) {
			const execFunc = () => {
				request({
					url: 'v3/member/front/memberSetting/getSettingList',
					data: {
						str: tmp_args.join(',')
					}
				}).then(res => {
					if (res.state == 200) {
						let obj = {}
						for (let i in tmp_args) {
							obj[tmp_args[i]] = res.data[i].imageUrl || res.data[i].value
						}
						fun_args.forEach(({
							resolve
						}) => {
							resolve(obj)
						})
						context.commit('saveMemberConfig', obj)
					} else {
						fun_args.forEach(({
							resolve
						}) => {
							resolve(obj)
						})
						context.commit('saveMemberConfig', {})
					}
				}).catch(() => {
					fun_args.forEach(({
						reject
					}) => {
						reject({})
					})
				}).finally(() => {
					fun_args = []
					tmp_args = []
				})
			}
			clearTimeout(timer);
			return new Promise((resolve, reject) => {
				tmp_args = Array.from(new Set([...tmp_args, ...args]))
				fun_args = fun_args.concat({
					resolve,
					reject
				})
				timer = setTimeout(() => {
					if (clickCount === 1) {
						execFunc()
					} else if (clickCount > 1) {
						execFunc()
					}
					clickCount = 0
				}, 100)
				clickCount++;
			})
		}
	}
})

export default store