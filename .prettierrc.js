/** @type {import("prettier").Config} */
module.exports = {
    stylelintIntegration: true,
    printWidth: 180, //单行长度
    tabWidth: 4, //缩进长度
    useTabs: false, //使用空格代替tab缩进
    semi: true, //句末使用分号
    singleQuote: true, //使用单引号
    endOfLine: 'auto',
    vueIndentScriptAndStyle: false, //vue文件中script和style标签缩进
    trailingComma: 'none', //末尾逗号
    arrowParens: 'always', //箭头函数参数括号 avoid
    bracketSpacing: true, //对象字面量大括号间的空格
    jsxBracketSameLine: false, //jsx标签闭合位置
    jsxSingleQuote: false, //jsx中使用单引号
    quoteProps: 'as-needed', //对象属性名称是否加引号
    htmlWhitespaceSensitivity: 'ignore', //html空格敏感度
};
