<template>
	<view class="usper_history">
		<view class="history_info flex_column_start_center" :style="`background-image:url(`+imgUrl+`super/history_bg.png)`">
			<image class="history_info_logo" mode="aspectFit" :src="userCenterData.memberAvatar ?
				userCenterData.memberAvatar : imgUrl + 'super/history_logo.png'"></image>
			<view class="history_info_name">{{userCenterData.memberNickName || userCenterData.memberName}}</view>
			<view class="history_info_btn" @click="navTo('/standard/super/pay')">立即续费</view>
			<view class="history_info_time">{{userCenterData.superExpirationDay}}到期</view>
		</view>
		<view class="history_nav flex_row_between_center">
			<view class="history_nav_item flex_column_center_center" :class="{active:navIndex==1}" @click="changeIndex(1)">
				<span class="history_nav_item_title">累计省钱</span>
				<span class="history_nav_item_money">￥{{totalSaveAmount}}</span>
			</view>
			<view class="history_nav_item flex_column_center_center" :class="{active:navIndex==2}" @click="changeIndex(2)">
				<span class="history_nav_item_title">本月累计省钱</span>
				<span class="history_nav_item_money">￥{{currentMonthSaveAmount}}</span>
			</view>
		</view>
		
		<scroll-view class="history_list" @scrolltolower="getMore" scroll-y v-if="navIndex==1">
			<view v-for="(item,index) in totalSaveRecord" :key="index" class="history_list_item">
				<view class="history_list_item_info flex_row_between_center">
					<view class="history_list_item_info_time">{{index}}</view>
					<view class="history_list_item_info_num">共省<span>￥{{item.monthTotalSaveAmount>0 ?
						item.monthTotalSaveAmount : 0}}</span></view>
				</view>
				<view v-for="(items,indexs) in item.monthSaveProductVO" :key="indexs" class="history_item flex_row_between_center">
					<image class="history_item_goods" :src="items.productImage" mode="aspectFit" lazy-load></image>
					<view class="history_item_info flex_column_between_start">
						<view class="history_item_info_title">{{items.goodsName}}</view>
						<view class="history_item_info_bot flex_row_between_between">
							<view class="history_item_info_other flex_row_start_start">
								<span class="history_item_info_other_desc" v-if="items.specValues">{{items.specValues}}</span>
								<span class="history_item_info_other_num">共{{items.productNum}}件</span>
							</view>
							<view class="history_item_info_num">已省 ¥{{items.productSaveAmount>0 ?
								items.productSaveAmount : 0}}</view>
						</view>
					</view>
				</view>
			</view>
			<view v-if="onready && !Object.keys(totalSaveRecord).length"
				class="empty_part flex_column_start_center">
				<image :src="imgUrl+'empty_goods.png'" />
				<text>暂无数据～</text>
			</view>
		</scroll-view>
		
		<view class="history_list" v-if="navIndex==2">
			<view class="history_list_item" v-if="Object.keys(currentMonthSaveRecord).length">
				<view v-for="(items,indexs) in currentMonthSaveRecord.monthSaveProductVO" :key="indexs" class="history_item flex_row_between_center">
					<image class="history_item_goods" :src="items.productImage" mode="aspectFit" lazy-load></image>
					<view class="history_item_info flex_column_between_start">
						<view class="history_item_info_title">{{items.goodsName}}</view>
						<view class="history_item_info_bot flex_row_between_between">
							<view class="history_item_info_other flex_row_start_start">
								<span class="history_item_info_other_desc" v-if="items.specValues">{{items.specValues}}</span>
								<span class="history_item_info_other_num">共{{items.productNum}}件</span>
							</view>
							<view class="history_item_info_num">已省 ¥{{Number(items.productSaveAmount).toFixed(2)}}</view>
						</view>
					</view>
				</view>
			</view>
			<view v-if="onready && !(Object.keys(currentMonthSaveRecord).length)"
				class="empty_part flex_column_start_center">
				<image :src="imgUrl+'empty_goods.png'" />
				<text>暂无数据～</text>
			</view>
		</view>
	</view>
</template>

<script>
	import { mapState } from 'vuex';
	export default {
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				navIndex: 1,
				totalSaveAmount: 0, //累计省钱
				currentMonthSaveAmount: 0, //本月累计省钱
				current: 1,
				pageSize: 10,
				hasMore: true,
				totalSaveRecord: [],
				currentMonthSaveRecord: [],
				onready: false,
			}
		},
		computed: {
			...mapState(['userCenterData'])
		},
		onLoad() {
			this.getList();
		},
		methods: {
			changeIndex(index) {
				if (this.navIndex !== index) {
					this.navIndex = index;
					// this.current = 1;
					// this.hasMore = true;
					// this.getList();
				}
			},
			getList() {
				let param = {};
				param.url = 'v3/member/front/memberSuper/saveRecord';
				param.method = 'POST';
				param.data = {};
				param.data.type = this.navIndex; //类型，1-累计省钱；2-本月累计省钱
				this.$request(param).then(res=>{
					if (res.state == 200) {
						this.totalSaveAmount = res.data.totalSaveAmount==0 ?
							res.data.totalSaveAmount : Number(res.data.totalSaveAmount).toFixed(2);
						this.currentMonthSaveAmount = res.data.currentMonthSaveAmount==0 ?
							res.data.currentMonthSaveAmount : Number(res.data.currentMonthSaveAmount).toFixed(2);
						this.totalSaveRecord = res.data.totalSaveRecord ? res.data.totalSaveRecord : {};
						this.currentMonthSaveRecord = res.data.currentMonthSaveRecord ? res.data.currentMonthSaveRecord : {};
						this.hasMore = false;
						this.onready = true;
					}
				})
			},
			getMore() {
				if (this.hasMore) {
					this.getList();
				}
			},
			navTo(path) {
				this.$Router.push(path);
			},
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #F5F5F5;
	}
	.usper_history {
		.history_info {
			width: 750rpx;
			height: 430rpx;
			font-family: PingFang SC;
			font-weight: 500;
			background-position: center;
			background-repeat: no-repeat;
			background-size: cover;
			.history_info_logo {
				width: 120rpx;
				height: 120rpx;
				background-color: #F9DD93;
				border-radius: 50%;
				margin-top: 40rpx;
			}
			.history_info_name {
				color: #101238;
				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: bold;
				margin-top: 14rpx;
			}
			.history_info_btn {
				width: 270rpx;
				height: 76rpx;
				line-height: 76rpx;
				color: #603C22;
				font-size: 30rpx;
				text-align: center;
				background: linear-gradient(#FFE8CE, #F4DAAD);
				border: 2rpx solid #FDDD88;
				border-radius: 38rpx;
				margin-top: 44rpx;
			}
			.history_info_time {
				color: #918E8C;
				font-size: 24rpx;
				margin-top: 14rpx;
			}
		}
		.history_nav {
			height: 120rpx;
			background-color: #FFFFFF;
			margin-top: 20rpx;
			.history_nav_item {
				width: 50%;
				height: 120rpx;
				font-family: PingFang SC;
				&:nth-child(1) {
					&.active {
						background: linear-gradient(90deg, #EFD2A3, #FFECC4);
						border-radius: 0 0 40rpx 0;
					}
				}
				&:nth-child(2) {
					&.active {
						background: linear-gradient(90deg, #FFECC4, #EFD2A3);
						border-radius: 0 0 0 40rpx;
					}
				}
				.history_nav_item_title {
					color: #121212;
					font-size: 30rpx;
					font-weight: bold;
				}
				.history_nav_item_money {
					color: #B07123;
					font-size: 28rpx;
					font-weight: 500;
					margin-top: 6rpx;
				}
			}
		}
		.history_list {
			width: 750rpx;
			/* #ifdef H5 */
			height: calc(100vh - 654rpx);
			/* #endif */
			/* #ifndef H5 */
			height: calc(100vh - var(--status-bar-height) - constant(safe-area-inset-bottom) - 570rpx);
			height: calc(100vh - var(--status-bar-height) - env(safe-area-inset-bottom) - 570rpx);
			/* #endif */
			
			.history_list_item {
				.history_list_item_info {
					color: #999999;
					height: 75rpx;
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
					padding: 25rpx 30rpx;
					.history_list_item_info_time {}
					.history_list_item_info_num {
						span {
							color: #c0a078;
							margin-left: 8rpx;
						}
					}
				}
				.history_item {
					position: relative;
					width: 750rpx;
					overflow: hidden;
					padding: 30rpx;
					background-color: #FFFFFF;
					&:before {
						content: '';
						position: absolute;
						left: 50%;
						top: 0;
						margin-left: -340rpx;
						width: 680rpx;
						height: 1rpx;
						background-color: #F3F3F3;
					}
					&:nth-child(1) {
						&:before {
							display: none;
						}
					}
					.history_item_goods {
						width: 80rpx;
						height: 80rpx;
						flex-shrink: 0;
						margin-right: 12rpx;
					}
					.history_item_info {
						width: 598rpx;
						height: 80rpx;
						font-size: 26rpx;
						font-family: PingFang SC;
						font-weight: 500;
						.history_item_info_title {
							width: 598rpx;
							color: #333333;
							overflow: hidden;
							white-space: nowrap;
							text-overflow: ellipsis;
						}
						.history_item_info_bot {
							width: 598rpx;
							color: #AAAAAA;
							.history_item_info_other {
								.history_item_info_other_desc {
									margin-right: 10rpx;
								}
								.history_item_info_other_num {}
							}
							.history_item_info_num {}
						}
					}
				}
			}
		}
		
		.empty_part {
			padding-top: 108rpx;
		
			image {
				width: 210rpx;
				height: 210rpx;
			}
		
			text {
				color: $main-third-color;
				font-size: 26rpx;
				margin-top: 57rpx;
			}
		}
	}
</style>