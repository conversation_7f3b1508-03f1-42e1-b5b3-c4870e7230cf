<template>
	<view class="super_pay">
		<view class="pay_item">
			<view class="pay_title">权益选择</view>
			<view class="pay_types flex_row_between_center" :class="{active:curIndex==index}"
				v-for="(item,index) in payList" :key="index" @click="changeIndex(index)">
				<view class="pay_types_title">{{item.payTypeValue}}</view>
				<view class="pay_types_right flex_column_center_end">
					<view class="pay_types_price">
						<span>￥</span>
						<span>{{item.needPay}}</span>
						<span v-if="item.unit">/{{item.unit}}</span>
					</view>
					<view class="pay_types_origin" v-if="item.saveAmount">
						<text>省</text>
						<text>￥{{(Math.abs(item.saveAmount)).toFixed(2)}}</text>
					</view>
				</view>
				<image v-if="curIndex == index" :src="imgUrl + 'super/pay_checled.png'"
					class="pay_type_checked" mode="aspectFit"></image>
			</view>
		</view>
		<view class="pay_item">
			<view class="pay_title">专属特权</view>
			<view class="pay_owns flex_row_center_center">
				<view class="pay_owns_item flex_column_center_center" v-for="(item,index) in ownlist" :key="index">
					<image :src="item.img" mode="aspectFill" lazy-load></image>
					<span>{{item.desc}}</span>
				</view>
			</view>
		</view>
		<view class="pay_bottom flex_column_center_center">
			<view v-if="curIndex != -1" class="pay_btn flex_row_center_center" @click="goBuy">
				<span>立即支付</span>
				<span>¥</span>
				<span>{{payDetail.needPay}}<span v-if="payDetail.saveAmount"
					:style="'background-image:url('+imgUrl+'super/pay_tips.png)'"
					>已优惠￥{{Math.abs(payDetail.saveAmount)}}</span></span>
				<span v-if="payDetail.unit">/{{payDetail.unit}}</span>
			</view>
			<view v-else class="pay_btn gray">
				<span>立即支付</span>
			</view>
			<view class="pay_tips" @click="navTo('/standard/super/policy')">开通即同意<span>《付费会员用户协议》</span></view>
		</view>
		<view v-if="showModel" class="pay_model flex_column_center_center">
			<view class="pay_model_info flex_column_center_center">
				<view class="pay_model_title">你有待支付的订单</view>
				<view class="pay_model_tips">若支付新订单，需取消原订单</view>
				<view class="pay_model_detail flex_row_between_center">
					<view class="pay_model_detail_tips">原订单</view>
					<view class="pay_model_detail_title">{{waitTiPay.payTypeValue}}</view>
					<view class="pay_model_detail_price">￥{{waitTiPay.needPay}}</view>
				</view>
				<view class="pay_model_btn flex_row_around_center">
					<view class="pay_model_cancle" @click="closeModel('cancel')">取消原订单</view>
					<view class="pay_model_pay" @click="submitModel">支付原订单</view>
				</view>
			</view>
			<image class="pay_model_close" :src="imgUrl + 'signIn/close_screen.png'"
				mode="aspectFit" @click="closeModel('close')"></image>
		</view>
	</view>
</template>

<script>
	import {mapState,mapActions} from 'vuex'
	export default {
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				curIndex: -1, //当前选择权益下标
				payList: [],
				ownlist: [],
				payDetail: {}, //支付订单信息
				showModel: false, //支付订单弹窗显示
				waitTiPay:{}
			}
		},
		onLoad() {
			this.getPayList();
			this.getOwnList();
			
		},
		
		computed:{
			...mapState(['memberConfig'])
		},
		
		methods: {
			...mapActions(['getMemberConfig']),
			//获取权限列表
			getPayList() {
				this.$request({url: 'v3/member/front/memberSuper/pay/superPayInfo'}).then(res=>{
					if (res.state == 200) {
						this.payList = res.data;
						this.curIndex = this.payList.length - 1
						this.payDetail = this.payList[this.curIndex];
					}
				})
			},
			// 获取特权列表
			getOwnList() {
				this.getMemberConfig([
					'super_custom_name',
					'super_discount_enabled',
					'super_discount',
					'super_integral_multiple_enabled',
					'super_integral_multiple',
					'super_coupon_enabled',
					'super_birthday_coupon_enabled',
					'super_freight_coupon_enabled',
				]).then(res=>{
					if(res.super_custom_name){
						uni.setNavigationBarTitle({
							title:res.super_custom_name
						})
					}else{
						uni.setNavigationBarTitle({
							title:'超级会员'
						})
					}
					let tmpList = []
					if(res.super_discount_enabled==1){
						tmpList.push({
							desc: `购物打${res.super_discount}折`,
							img: this.imgUrl+'super/tips1_1.png'
						})
					}
					
					if(res.super_integral_multiple_enabled==1){
						tmpList.push({
							desc: `${res.super_integral_multiple}倍领积分`,
							img: this.imgUrl+'super/tips2_1.png',
						})
					}
					
					if(res.super_coupon_enabled==1){
						tmpList.push({
							desc: '专属优惠券',
							img: this.imgUrl+'super/tips3_1.png',
						})
					}
					
					if(res.super_birthday_coupon_enabled==1){
						tmpList.push({
							desc: '生日大礼包',
							img: this.imgUrl+'super/tips4_1.png'
						})
					}
					
					if(res.super_freight_coupon_enabled==1){
						tmpList.push({
							desc: '运费优惠券',
							img: this.imgUrl+'super/tips5_1.png',
						})
					}
					
					this.ownlist = tmpList
				})
				
				
				
				
			},
			// 切换权益
			changeIndex(index) {
				if (this.curIndex == index) {
					this.curIndex = -1;
					this.payDetail = {};
				} else {
					this.curIndex = index;
					this.payDetail = this.payList[index];
				}
			},
			//立即支付
			goBuy() {
				
				let param = {};
				param.url = 'v3/member/front/memberSuper/pay/buySuper';
				param.method = 'POST';
				param.data = {};
				param.data.payType = this.payDetail.payType;
				this.$request(param).then(res=>{
					if (res.state == 200) {
						this.$Router.replace({
							path: '/standard/super/superPay',
							query: {
								payType: this.payDetail.payType
							}
						})
					}else if(res.state == 267){
						this.showModel = true
						this.waitTiPay.payTypeValue = res.data.payTypeValue
						this.waitTiPay.payType = res.data.payType
						this.waitTiPay.paySn = res.data.paySn
						let consponse = this.payList.find(item=>item.payType==res.data.payType);
						if(consponse){
							this.waitTiPay.needPay = consponse.needPay
						}
					}
				})
			},
			// 支付订单弹窗
			openModel() {
				this.showModel = true;
			},
			// 关闭支付订单弹窗
			closeModel(type) {
				if(type=='cancel'){
					this.$request({
						url:'v3/member/front/memberSuper/pay/cancelPay',
						method:'POST',
						data:{
							paySn:this.waitTiPay.paySn
						}
					}).then(res=>{
						this.$api.msg(res.msg)
					})
				}
				this.showModel = false;
			},
			// 支付
			submitModel() {
				this.$Router.replace({
					path: '/standard/super/superPay',
					query: {
						payType: this.waitTiPay.payType,
					}
				})
			},
			// 页面跳转
			navTo(path) {
				this.$Router.push(path);
			},
		},
	}
</script>

<style lang="scss">
	page {
		background: #F5F5F5;
	}
	.super_pay {
		position: relative;
		overflow: auto;
		padding-bottom: 232rpx;
		.pay_item {
			width: 710rpx;
			margin: 20rpx auto;
			padding: 30rpx 20rpx;
			background: #FFFFFF;
			border-radius: 20rpx;
			.pay_title {
				color: #0F0F18;
				font-size: 30rpx;
				font-family: PingFang SC;
				font-weight: bold;
				margin-bottom: 30rpx;
			}
			.pay_types {
				position: relative;
				width: 670rpx;
				min-height: 115rpx;
				margin-top: 40rpx;
				margin-bottom: 40rpx;
				padding-left: 20rpx;
				padding-right: 30rpx;
				padding-top:10rpx;
				padding-bottom:10rpx;
				background: #FFFFFF;
				border-radius: 10rpx;
				box-shadow: 0 0 10rpx 0 rgba(79, 74, 63, 0.15);
				overflow: hidden;
				&.active {
					background: #FCF2D9;
					border: 2rpx solid #DEB553;
				}
				.pay_types_title {
					color: #0F0F18;
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
				}
				.pay_types_right {
					color: #DEAB53;
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
					.pay_types_price {
						display: flex;
						align-items: baseline;
						height: 72rpx;
						span {
							&:nth-child(2) {
								font-size: 56rpx;
								font-weight: 800;
								margin-left: 6rpx;
								margin-right: 6rpx;
							}
							&:last-child{
								font-size: 28rpx;
								font-family: PingFang SC;
								font-weight: 500;
								color: #0F0F18;
							}
						}
					}
					.pay_types_origin {
						color: #A0A0AB;
						font-size: 24rpx;
						margin-right: 50rpx;
						text:first-child{
							color:#e2231a;
						}
					}
				}
				.pay_type_checked {
					position: absolute;
					right: 0;
					bottom: 0;
					width: 51rpx;
					height: 50rpx;
				}
			}
			.pay_owns {
				width: 100%;
				flex-wrap: wrap;
				.pay_owns_item {
					width: 200rpx;
					height: 140rpx;
					image {
						width: 64rpx;
						height: 64rpx;
						margin-bottom: 18rpx;
					}
					span {
						color: #666666;
						font-size: 24rpx;
						font-family: PingFang SC;
						font-weight: 500;
					}
				}
			}
		}
		.pay_bottom {
			position: fixed;
			bottom: 0;
			z-index: 1;
			width: 750rpx;
			height: 212rpx;
			background-color: #FFFFFF;
			padding-top: 54rpx;
			padding-bottom: 30rpx;
			.pay_btn {
				width: 660rpx;
				height: 90rpx;
				line-height: 86rpx;
				color: #562D0E;
				font-size: 30rpx;
				font-family: PingFang SC;
				font-weight: bold;
				text-align: center;
				background: linear-gradient(90deg, #FAD496, #F3BD75);
				border-radius: 45rpx;
				align-items: baseline;
				&.gray {
					line-height: 48px;
					filter: grayscale(1);
				}
				span {
					color: #562D0E;
					font-size: 30rpx;
					font-family: PingFang SC;
					&:nth-child(2) {
						color: #e40202;
						margin-left: 10rpx;
					}
					&:nth-child(3) {
						position: relative;
						color: #e40202;
						font-size: 52rpx;
						font-weight: bold;
						span {
							position: absolute;
							top: -30rpx;
							left: 36rpx;
							z-index: 9;
							width: 206rpx;
							height: 48rpx;
							line-height: 40rpx;
							color: #FFFFFF;
							font-size: 24rpx;
							font-family: PingFang SC;
							font-weight: 500;
							background-position: center;
							background-repeat: no-repeat;
							background-size: contain;
						}
					}
				}
			}
			.pay_tips {
				color: #666666;
				font-size: 26rpx;
				font-family: PingFang SC;
				font-weight: 400;
				margin-top: 24rpx;
				span {
					color: #c39131;
				}
			}
		}
		.pay_model {
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			z-index: 99;
			background-color: rgba(0, 0, 0, .5);
			
			.pay_model_info {
				width: 660rpx;
				font-family: PingFang SC;
				font-weight: 500;
				background: #FFFFFF;
				border-radius: 20rpx;
				
				.pay_model_title {
					color: #0F0F18;
					font-size: 34rpx;
					font-weight: bold;
					margin-top: 46rpx;
				}
				.pay_model_tips {
					color: #666666;
					font-size: 28rpx;
					margin-top: 23rpx;
					margin-bottom: 48rpx;
				}
				.pay_model_detail {
					position: relative;
					width: 580rpx;
					height: 113rpx;
					font-family: PingFang SC;
					font-weight: 500;
					background: #FCF2D9;
					border-radius: 10rpx;
					overflow: hidden;
					.pay_model_detail_tips {
						position: absolute;
						top: -32rpx;
						left: -66rpx;
						width: 170rpx;
						height: 100rpx;
						line-height: 148rpx;
						color: #FFFFFF;
						text-align: center;
						background-color: #DEB553;
						transform: rotate(-45deg) scale(.68);
					}
					.pay_model_detail_title {
						color: #0F0F18;
						font-size: 28rpx;
						margin-left: 90rpx;
					}
					.pay_model_detail_price {
						color: #B58848;
						font-size: 28rpx;
						margin-right: 30rpx;
					}
				}
				.pay_model_btn {
					margin-top: 54rpx;
					margin-bottom: 56rpx;
					.pay_model_cancle,
					.pay_model_pay {
						width: 220rpx;
						height: 64rpx;
						line-height: 64rpx;
						color: #252939;
						font-size: 26rpx;
						font-family: PingFang SC;
						font-weight: 500;
						text-align: center;
						background: linear-gradient(90deg, #F8D9AD, #E9B87A);
						border-radius: 32rpx;
					}
					.pay_model_cancle {
						margin-right: 50rpx;
					}
					.pay_model_pay {}
				}
			}
			
			.pay_model_close {
				width: 56rpx;
				height: 56rpx;
				margin-top: 80rpx;
			}
		}
	}
</style>