<template>
	<view class="" v-if="loaded">
		<view v-if="memberConfig.super_is_enable==1">
			<view >
				<view class="super_tips"
					:style="'background-image:url('+ (memberConfig.super_mobile_page?memberConfig.super_mobile_page:superBg) +')'">
					<view v-if="userCenterData.isSuper==0" class="super_tips_tit flex_column_center_center">
						<span class="super_tips_tit_sm">开通{{memberConfig.super_custom_name?memberConfig.super_custom_name:'超级会员'}}</span>
						<span class="super_tips_tit_lg">专属特权</span>
					</view>
					<view v-else class="super_info" :style="'background-image:url('+ imgUrl +'super/head_bg.png)'">
						<view class="super_info_top flex_row_start_start">
							<view class="super_info_top_left">
								<image class="super_info_top_img" :src="imgUrl + 'super/head_top.png'" mode="aspectFit">
								</image>
								<image class="super_info_top_head" mode="aspectFit" :src="userCenterData.memberAvatar ?
								userCenterData.memberAvatar : imgUrl + 'super/head_none.png'"></image>
							</view>
							<view class="super_info_top_info flex_column_around_start">
								<view class="super_info_top_name">
									{{userCenterData.memberNickName || userCenterData.memberName}}
								</view>
								<view class="super_info_top_time">{{userCenterData.superExpirationDay}}到期</view>
							</view>
						</view>
						<view class="super_info_btn" @click="navTo('/standard/super/pay')">立即续费</view>
						<view class="super_info_bot flex_row_start_center" @click="navTo('/standard/super/history')">
							<image class="super_info_bot_tot" :src="imgUrl + 'super/head_total.png'" mode="aspectFit">
							</image>
							<span class="super_info_bot_text">累计已省</span>
							<span
								class="super_info_bot_num">￥<span>{{userCenterData.totalSaveAmount}}</span></span>
							<image class="super_info_bot_back" :src="imgUrl + 'member/right_down.png'" mode="aspectFit">
							</image>
						</view>
					</view>
					<view class="super_tips_main">
						<view class="flex_row_center_center" v-if="(memberConfig.super_discount_enabled == 1 && Number(memberConfig.super_discount) > 0)||(memberConfig.super_integral_multiple_enabled == 1 && Number(memberConfig.super_integral_multiple) > 0)">
							<view class="super_tips_item flex_column_center_center"
								v-if="memberConfig.super_discount_enabled == 1 && Number(memberConfig.super_discount) > 0">
								<image :src="imgUrl + 'super/tips1.png'"></image>
								<span>购物打{{memberConfig.super_discount}}折</span>
							</view>
							<view class="super_tips_item flex_column_center_center"
								v-if="memberConfig.super_integral_multiple_enabled == 1 && Number(memberConfig.super_integral_multiple) > 0">
								<image :src="imgUrl + 'super/tips2.png'"></image>
								<span>{{memberConfig.super_integral_multiple}}倍领积分</span>
							</view>
						</view>
						<view class="flex_row_center_center">
							<view class="super_tips_item flex_column_center_center"
								v-if="memberConfig.super_coupon_enabled == 1">
								<image :src="imgUrl + 'super/tips3.png'"></image>
								<span>专属优惠券</span>
							</view>
							<view class="super_tips_item flex_column_center_center"
								v-if="memberConfig.super_birthday_coupon_enabled == 1">
								<image :src="imgUrl + 'super/tips4.png'"></image>
								<span>生日大礼包</span>
							</view>
							<view class="super_tips_item flex_column_center_center"
								v-if="memberConfig.super_freight_coupon_enabled == 1">
								<image :src="imgUrl + 'super/tips5.png'"></image>
								<span>运费优惠券</span>
							</view>
						</view>
					</view>
				</view>
				<view class="super_main flex_column_start_center">
					<view v-for="(item,index) in mainList" :key="index" class="super_main_item">
						<view class="super_main_nav" :style="'background-image:url('+ navBg +')'">
							<span class="super_main_nav_title">{{item.title}}</span>
							<span class="super_main_nav_tips">{{item.tip}}</span>
						</view>

						<view class="super_main_img flex_row_center_center"
							v-if="item.type=='super_coupon'||item.type=='super_freight_coupon'">
							<view class="back_img" :style="{backgroundImage:'url('+ imgUrl + item.img+')'}">
								<view class="flex_row_start_center"
									:class="item.type=='super_coupon'?'block_class_5':'block_class_7'">
									<view class="block_class_5_section1 flex_row_center_center">
										<text class="price_small sort_big" v-if="item.value>0">￥</text>
										<text class="price_big sort_big" v-if="item.value>0">{{item.value}}</text>
										<text class="price_small sort_big" v-else>免运费</text>
									</view>
									<view class="block_class_5_section1 flex_column_center_center">
										<view class="flex_row_center_center block_class_5_text">
											<text class="dot"></text>
											<text style="margin: 0 10rpx;">{{ memberConfig.super_custom_name }}</text>
											<text class="dot"></text>
										</view>
										<view class="block_class_5_text" style="margin-top: 20rpx;">
											{{item.type=='super_freight_coupon'?'领运费券':'专属礼券'}}
										</view>
									</view>
								</view>
							</view>
						</view>


						<view class="super_main_img flex_row_center_center" v-else-if="item.type=='super_integral_multiple'">
							<view class="back_img" :style="{backgroundImage:'url('+ imgUrl + item.img+')'}">
								<view class="block_class_3 flex_row_between_center">
									<view class="block_class_3_reg1">
										<view class="reg1_text1">普通会员购物</view>
										<view>
											<text class="reg1_text1">积分为</text>
											<text class="reg1_text2 sort_big">1倍</text>
										</view>
									</view>

									<view class="block_class_3_reg1">
										<view class="reg1_text1 dark">开通{{ memberConfig.super_custom_name }}</view>
										<view>
											<text class="reg1_text1 dark">积分为</text>
											<text
												class="reg1_text2 sort_big dark">{{ memberConfig.super_integral_multiple }}倍</text>
										</view>
									</view>
								</view>
							</view>
						</view>



						<view class="super_main_img flex_row_center_center" v-else>
							<image :src="imgUrl + item.img" mode="aspectFit"></image>
						</view>
					</view>
				</view>
				<view v-if="userCenterData.isSuper==0" class="super_btn" @click="navTo('/standard/super/pay')">立即开通</view>
			</view>

		</view>
		<view v-else>
			<notOpen></notOpen>
		</view>
	</view>

</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex';
	import notOpen from '@/components/not_open'
	export default {
		components: {
			notOpen
		},
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				superBg: getApp().globalData.imgUrl + 'super/tips_bg.png',
				navBg: getApp().globalData.imgUrl + 'super/main_nav.png',
				mainList: [],
				loaded: false
			}
		},
		computed: {
			...mapState(['userCenterData', 'memberConfig'])
		},
		onLoad() {
			this.getMemberConfig([
				'super_mobile_page',
				'super_is_enable',
				'super_discount_enabled',
				'super_discount',
				'super_integral_multiple_enabled',
				'super_integral_multiple',
				'super_coupon_enabled',
				'super_birthday_coupon_enabled',
				'super_freight_coupon_enabled',
				'super_custom_name'
			]).then(async (res) => {
				this.loaded = true
				let partial_right_res = await this.getRights()
				let partial_right = (partial_right_res.state==200&&partial_right_res.data)?partial_right_res.data:{}
				
				if (res.super_custom_name) {
					uni.setNavigationBarTitle({
						title: res.super_custom_name
					})
				} else {
					uni.setNavigationBarTitle({
						title: '超级会员专场'
					})
				}

				if (res.super_discount_enabled == 1) {
					this.mainList.push({
						title: `购物打${res.super_discount}折`,
						type: 'super_discount',
						tip: `自营商品全场${res.super_discount}折，同享其他优惠`,
						img: 'super/main1_1.png'
					})
				}

				if (res.super_integral_multiple_enabled == 1) {
					this.mainList.push({
						title: `${res.super_integral_multiple}倍领积分`,
						type: 'super_integral_multiple',
						tip: '购物下单、订单评价赠送积分翻倍',
						img: 'super/main2_1.png'
					})
				}
				
				if (res.super_coupon_enabled == 1&&Number(partial_right.superAmount)>0) {
					this.mainList.push({
						title: '专属优惠券',
						type: 'super_coupon',
						value:Number(partial_right.superAmount),
						tip: `赠送${this.memberConfig.super_custom_name?this.memberConfig.super_custom_name:'超级会员'}专属优惠券礼包`,
						img: 'super/main3_1.png'
					})
				}

				if (res.super_birthday_coupon_enabled == 1) {
					this.mainList.push({
						title: '生日大礼包',
						type: 'super_birthday_coupon',
						tip: `${this.memberConfig.super_custom_name?this.memberConfig.super_custom_name:'超级会员'}生日专享大礼包`,
						img: 'super/main4.png'
					})
				}

				if (res.super_freight_coupon_enabled == 1&&partial_right.freightAmount) {
					this.mainList.push({
						title: '运费优惠券',
						type: 'super_freight_coupon',
						value:isNaN(Number(partial_right.freightAmount))?0:Number(partial_right.freightAmount),
						tip: '运费不用愁',
						img: 'super/main5_1.png'
					})
				}

			})
			this.getRights()
		},
		methods: {
			...mapActions(['getMemberConfig']),
			navTo(path) {
				this.$Router.push(path);
			},
			preView() {
				let urls = (this.userCenterData.memberAvatar ? [this.userCenterData.memberAvatar] : [this.imgUrl +
					'super/head_none.png'
				])
				uni.previewImage({
					urls
				});
			},
			
			getRights(){
				return this.$request({
					url:'v3/member/front/memberSetting/rights'
				})
			}
		}
	}
</script>

<style lang="scss">
	page {
		background: #F3F4F6;
	}

	.super_tips {
		position: relative;
		width: 750rpx;
		height: 880rpx;
		background-size: cover;
		background-position: center;
		background-repeat: no-repeat;
		overflow: hidden;

		.super_tips_tit {
			color: #FFECE2;
			font-family: PingFang SC;
			font-weight: 500;
			text-align: center;
			text-shadow: -2rpx 18rpx 40rpx rgba(9, 8, 8, 0.85);
			margin-top: 146rpx;
			background: linear-gradient(180deg, #FFEEE4 0%, #FFC1A1 100%);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;

			.super_tips_tit_sm {
				font-size: 48rpx;
			}

			.super_tips_tit_lg {
				font-size: 80rpx;
				font-weight: bold;
				margin-top: 30rpx;
			}
		}

		.super_info {
			position: relative;
			width: 720rpx;
			height: 332rpx;
			background-position: center;
			background-repeat: no-repeat;
			background-size: contain;
			margin: 40rpx auto;
			overflow: hidden;

			.super_info_top {
				margin-top: 40rpx;
				margin-left: 40rpx;

				.super_info_top_left {
					position: relative;
					width: 94rpx;
					height: 94rpx;
					margin-right: 20rpx;

					.super_info_top_img {
						position: absolute;
						top: -26rpx;
						left: -44rpx;
						width: 144rpx;
						height: 80rpx;
					}

					.super_info_top_head {
						width: 94rpx;
						height: 94rpx;
						border-radius: 50%;
						overflow: hidden;
					}
				}

				.super_info_top_info {
					height: 94rpx;
					font-family: PingFang SC;

					.super_info_top_name {
						color: #181414;
						font-size: 32rpx;
						font-weight: bold;
					}

					.super_info_top_time {
						color: #684625;
						font-size: 24rpx;
						font-weight: 500;
					}
				}
			}

			.super_info_btn {
				position: absolute;
				top: 76rpx;
				right: 18rpx;
				width: 140rpx;
				height: 48rpx;
				line-height: 48rpx;
				color: #FFE3B0;
				font-size: 26rpx;
				font-family: PingFang SC;
				font-weight: 500;
				text-align: center;
				background: linear-gradient(90deg, #976F3D, #A37A48);
				border-radius: 24rpx 0 0 24rpx;
				padding-left: 6rpx;
			}

			.super_info_bot {
				display: inline-block;
				// min-width: 365rpx;
				height: 70rpx;
				line-height: 70rpx;
				margin-top: 50rpx;
				margin-left: 18rpx;
				padding-right: 16rpx;
				background: rgba(255, 255, 255, .2);
				border-radius: 0 35rpx 35rpx 0;

				.super_info_bot_tot {
					position: relative;
					bottom: 0rpx;
					width: 23rpx;
					height: 22rpx;
					margin-left: 27rpx;
					margin-right: 12rpx;
				}

				.super_info_bot_text {
					position: relative;
					bottom: 2rpx;
					color: #6E5739;
					font-size: 24rpx;
					font-family: PingFang SC;
					font-weight: 500;
					margin-right: 12rpx;
				}

				.super_info_bot_num {
					color: #321F19;
					font-size: 24rpx;
					font-family: PingFang SC;
					font-weight: 500;
					margin-right: 24rpx;

					span {
						font-size: 36rpx;
						font-weight: bold;
					}
				}

				.super_info_bot_back {
					width: 10rpx;
					height: 17rpx;
					margin-right: 20rpx;
					margin-bottom: 4rpx;
				}
			}
		}

		.super_tips_main {
			position: absolute;
			left: 50%;
			bottom: 155rpx;
			transform: translateX(-50%);
			z-index: 9;

			.super_tips_item {
				margin: 40rpx 40rpx 0;

				image {
					width: 92rpx;
					height: 92rpx;
				}

				span {
					line-height: 29rpx;
					color: #F3C4A0;
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 400;
					white-space: nowrap;
					margin-top: 22rpx;
					opacity: 0.8;
				}
			}
		}

	}

	.super_main {
		position: relative;
		top: -70rpx;
		overflow: hidden;
		background: #F3F4F6;
		border-top-left-radius: 30rpx;
		border-top-right-radius: 30rpx;
		padding-bottom: calc(128rpx + env(safe-area-inset-bottom));

		.super_main_item {
			width: 710rpx;
			background: #FFFFFF;
			border-radius: 10px;
			margin-top: 22rpx;

			.super_main_nav {
				width: 710rpx;
				height: 90rpx;
				line-height: 90rpx;
				padding: 0 24rpx;
				background-position: center;
				background-repeat: no-repeat;
				background-size: cover;

				.super_main_nav_title {
					color: #362B26;
					font-size: 30rpx;
					font-family: PingFang SC;
					font-weight: bold;
				}

				.super_main_nav_tips {
					color: #1A1C23;
					font-size: 26rpx;
					font-family: PingFang SC;
					font-weight: 400;
					margin-left: 34rpx;
				}
			}

			.super_main_img {
				width: 710rpx;
				height: 300rpx;
				padding: 24rpx;

				image {
					max-width: 100%;
					max-height: 100%;
				}

				.back_img {
					width: 100%;
					height: 100%;
					background-position: center center;
					background-size: contain;
					background-repeat: no-repeat;
					position: relative;
				}
			}
		}
	}

	.sort_big {
		font-family: PingFang SC;
		font-weight: bold;
		color: #D7A968;
	}

	.font72 {
		font-size: 72rpx;
	}

	.font60 {
		font-size: 60rpx;
	}

	.block_class_5,
	.block_class_7 {
		position: absolute;
		left: 40rpx;
		top: 62rpx;
		width: 500rpx;
		height: 145rpx;

		&.block_class_7 {
			top: 52rpx;
			.block_class_5_section1{
				width: 240rpx;
			}
		}

		.price_small {
			font-size: 41rpx;
		}

		.price_big {
			font-size: 70rpx;
		}

		&_section1 {
			width: 250rpx;
		}

		&_text {
			font-size: 25rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #F2C99D;
		}

		.dot {
			width: 4rpx;
			height: 4rpx;
			background: #F7CA93;
			border-radius: 50%;
		}
	}

	.block_class_3 {

		position: absolute;
		top: 40rpx;
		left: 46rpx;
		width: 540rpx;
		height: 205rpx;

		.block_class_3_reg1 {
			.reg1_text1 {
				font-size: 26rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #FFFFFF;
				margin-bottom: 15rpx;
			}

			.reg1_text2 {
				font-size: 38rpx;
				color: #F7C681;
			}
		}
	}

	.dark {
		color: #25273A !important;
	}

	.super_btn {
		width: 750rpx;
		height: calc(108rpx + env(safe-area-inset-bottom));
		line-height: 108rpx;
		color: #663403;
		font-size: 38rpx;
		font-family: PingFang SC;
		font-weight: bold;
		text-align: center;
		background: linear-gradient(-90deg, #F3CC9F 0%, #ECAE78 100%);
		box-shadow: 0 0 10rpx 0 rgba(101, 101, 103, 0.08);
		position: fixed;
		bottom: 0;
		padding-bottom: env(safe-area-inset-bottom)
	}
</style>