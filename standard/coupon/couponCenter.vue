<!-- 领券中心 -->
<template>
	<view :style="mix_diyStyle">
	<view class="my_coupon">
		<!-- 随机优惠券 start -->
		<view :class="{ hide: !rondomMod, random_coupon: true }"
			style=" position: fixed; width: 750rpx; height: 100vh; background: rgba(0, 0, 0, 0.6); z-index: 10;">
			<view class="random_coupon_bg" :style="{ backgroundImage: 'url(' + imgUrl + 'coupon/random_bg.png)' }"
				@click="goMyCoupon()">
				<view class="random_coupon_price">{{ $L('￥') }}{{ rondomDes.publishValue }}</view>
				<view class="random_coupon_des">{{ rondomDes.couponContent }}</view>
				<view class="close_btn" :style="{ backgroundImage: 'url(' + imgUrl + 'coupon/close.png)' }"
					@click.stop="close"></view>
			</view>
		</view>
		<!-- 随机优惠券 end -->
		<view class="coupon_center_bg">
			<!-- app-1-start -->
			<!-- #ifdef APP -->
			<uni-nav-bar fixed="true" color="#fff" status-bar="true" left-icon="back" title="领券中心" @clickLeft="$back"
				mode="center" backgroundColor="transparent" v-if="!searchShow"></uni-nav-bar>
			<uni-nav-bar fixed="true" status-bar="true" left-icon="back" title="领券中心" @clickLeft="$back" mode="center"
				v-else></uni-nav-bar>
			<!-- #endif -->
			<!-- app-1-end -->
			<image :src="imgUrl + 'coupon/coupon_bg.png'" mode="aspectFit"></image>
			<!-- #ifdef MP -->
			<view class="" :style="{top:menuButtonTop,position:'fixed'}" v-if="!searchShow">
				<view class="nav-bar" :style="{height:menuButtonHeight}">
					<image :src="imgUrl+'tshou/back_icon.png'" mode="" @click="$back"></image>
					<view class="">领券中心</view>
				</view>
			</view>
			<view class="nav-bars" :style="{paddingTop:menuButtonTop,position:'fixed'}" v-else>
				<view class="nav-bar" :style="{height:menuButtonHeight}">
					<image :src="imgUrl+'fanhui1.png'" mode="" @click="$back"></image>
					<view class="">领券中心</view>
				</view>
			</view>
			<!-- #endif -->
		</view>
		<!-- #ifdef MP -->
		<view class="my_coupon_nav">
			<view class="my_coupon_nav_pre_wrap">
				<view class="my_coupon_nav_pre" :class="{ active: categoryId == '-1' }" @click="handleNav('-1')">
					{{ $L('精选') }}
				</view>
				<view class="my_coupon_nav_pre" :class="{ active: categoryId == '-2' }" @click="handleNav('-2')">
					{{ $L('全部') }}
				</view>
				<view class="my_coupon_nav_pre" :class="{ active: categoryId == item.categoryId }"
					@click="handleNav(item.categoryId)" v-for="(item, index) in categoryList" :key="index">
					{{ item.categoryName }}
				</view>
			</view>
		</view>
		<view class="my_coupon_nav my_coupon_navs" v-if="searchShow" :style="{top:menuButtonHeights+'px'}">
			<view class="my_coupon_nav_pre_wrap">
				<view class="my_coupon_nav_pre" :class="{ active: categoryId == '-1' }" @click="handleNav('-1')">
					{{ $L('精选') }}
				</view>
				<view class="my_coupon_nav_pre" :class="{ active: categoryId == '-2' }" @click="handleNav('-2')">
					{{ $L('全部') }}
				</view>
				<view class="my_coupon_nav_pre" :class="{ active: categoryId == item.categoryId }"
					@click="handleNav(item.categoryId)" v-for="(item, index) in categoryList" :key="index">
					{{ item.categoryName }}
				</view>
			</view>
		</view>
		<!-- #endif -->
		<!-- #ifdef H5 -->
		<view class="my_coupon_nav">
			<view class="my_coupon_nav_pre_wrap">
				<view class="my_coupon_nav_pre" :class="{ active: categoryId == '-1' }" @click="handleNav('-1')">
					{{ $L('精选') }}
				</view>
				<view class="my_coupon_nav_pre" :class="{ active: categoryId == '-2' }" @click="handleNav('-2')">
					{{ $L('全部') }}
				</view>
				<view class="my_coupon_nav_pre" :class="{ active: categoryId == item.categoryId }"
					@click="handleNav(item.categoryId)" v-for="(item, index) in categoryList" :key="index">
					{{ item.categoryName }}
				</view>
			</view>
		</view>
		<view class="my_coupon_nav my_coupon_navs" v-if='searchShow' :style="{top:0+'px'}">
			<view class="my_coupon_nav_pre_wrap">
				<view class="my_coupon_nav_pre" :class="{ active: categoryId == '-1' }" @click="handleNav('-1')">
					{{ $L('精选') }}
				</view>
				<view class="my_coupon_nav_pre" :class="{ active: categoryId == '-2' }" @click="handleNav('-2')">
					{{ $L('全部') }}
				</view>
				<view class="my_coupon_nav_pre" :class="{ active: categoryId == item.categoryId }"
					@click="handleNav(item.categoryId)" v-for="(item, index) in categoryList" :key="index">
					{{ item.categoryName }}
				</view>
			</view>
		</view>
		<!-- #endif -->
		<!-- app-2-start -->
		<!-- #ifdef APP -->
		<view class="my_coupon_nav">
			<view class="my_coupon_nav_pre_wrap">
				<view class="my_coupon_nav_pre" :class="{ active: categoryId == '-1' }" @click="handleNav('-1')">
					{{ $L('精选') }}
				</view>
				<view class="my_coupon_nav_pre" :class="{ active: categoryId == '-2' }" @click="handleNav('-2')">
					{{ $L('全部') }}
				</view>
				<view class="my_coupon_nav_pre" :class="{ active: categoryId == item.categoryId }"
					@click="handleNav(item.categoryId)" v-for="(item, index) in categoryList" :key="index">
					{{ item.categoryName }}
				</view>
			</view>
		</view>
		<view class="my_coupon_nav my_coupon_navs" v-if='searchShow' :style="{top:statusBarHeight+'px'}">
			<view class="my_coupon_nav_pre_wrap">
				<view class="my_coupon_nav_pre" :class="{ active: categoryId == '-1' }" @click="handleNav('-1')">
					{{ $L('精选') }}
				</view>
				<view class="my_coupon_nav_pre" :class="{ active: categoryId == '-2' }" @click="handleNav('-2')">
					{{ $L('全部') }}
				</view>
				<view class="my_coupon_nav_pre" :class="{ active: categoryId == item.categoryId }"
					@click="handleNav(item.categoryId)" v-for="(item, index) in categoryList" :key="index">
					{{ item.categoryName }}
				</view>
			</view>
		</view>
		<!-- #endif -->
		<!-- app-2-end -->
		<view class="my_coupon_list" v-if="couponList && !noData">
			<view class="my_coupon_pre" v-for="(item, index) in couponList" :key="index">
				<view class="coupon_pre_top">
					<view class="coupon_qiu1"></view>
					<view class="coupon_qiu2"></view>
					<view class="coupon_pre_left_o">
						<view class="coupon_pre_left">
							<view class="coupon_pre_left_type">{{ item.couponTypeValue }}</view>
							
							<view class="coupon_pre_price" :class="{coupon_pre_price_high: item.publishValue.toString().length > 6}" v-if="item.couponType==4">
								<block v-if="Number(item.publishValue)>0">
									<text class="unit">{{ $L('¥ ') }}</text>
									<text class="price_int">{{$getPartNumber(item.publishValue, 'int')}}</text>
									<text class="price_int"
										v-if="item.publishValue.toString().indexOf('.') != -1">{{ $getPartNumber(item.publishValue, 'decimal') }}</text>
								</block>
								<block v-else>
									<text class="price_int">免运费</text>
								</block>
							</view>
							
							<view class="coupon_pre_price" :class="{coupon_pre_price_high: item.publishValue.toString().length > 6}" v-else-if="item.couponType != 2">
								<text class="unit">{{ $L('¥ ') }}</text>
								<text class="price_int">{{$getPartNumber(item.publishValue, 'int')}}</text>
								<text class="price_int"
									v-if="item.publishValue.toString().indexOf('.') != -1">{{ $getPartNumber(item.publishValue, 'decimal') }}</text>
							</view>
							<view class="coupon_pre_price" v-if="item.couponType == 2">
								<view class=""></view>
								<text class="price_int">{{filters.toSplit(filters.toFixNum(item.publishValue, 1))[0]}}</text>.
								<text class="price_decimal">{{filters.toSplit(filters.toFixNum(item.publishValue, 1))[1]}}</text>
								<text class="price_decimal">{{ $L('折') }}</text>
							</view>
							
						</view>
						<view class="coupon_pre_cen">
							<view class="coupon_pre_title">{{ item.couponContent }}</view>
							<view class="coupon_pre_time">{{ item.effectiveStart }}~{{ item.effectiveEnd }}</view>
						</view>

					</view>
					<view class="coupon_pre_right coupon_state_no" v-if="item.receivedState == 3">
						<view class="coupon_pre_right_btn coupon_pre_btn">{{ $L('已抢完') }}</view>
					</view>
					<view class="coupon_pre_right" v-else-if="item.receivedState == 2">
						<view class="coupon_progress" v-if="item.receivedState != 3">
							<view class="progress_con">
								<progress :percent="item.robbedRate" stroke-width="3" :activeColor="diyStyle_var['--color_coupon_main']"
									backgroundColor="#FFFFFF" border-radius="2px" />
							</view>
							{{ $L('已抢') }}{{ item.robbedRate }}%
						</view>
						<view class="coupon_pre_right_btn coupon_pre_btn" @click="goGoodsList(item)">{{ $L('已领') }}
						</view>
					</view>
					<view class="coupon_pre_right" v-else-if="item.receivedState == 1">
						<view class="coupon_progress" v-if="item.receivedState != 3">
							<view class="progress_con">
								<progress :percent="item.robbedRate" stroke-width="3" :activeColor="diyStyle_var['--color_coupon_main']"
									backgroundColor="#FFFFFF" border-radius="2px" />
							</view>
							{{ $L('已抢') }}{{ item.robbedRate }}%
						</view>
						<view class="coupon_pre_right_btn" @click="goReceive(item)">{{ $L('立即领取') }}</view>
					</view>
				</view>
				<view class="coupon_rules"
					v-if="item.description!=null&&item.description.length<=22&&item.isOpen == false">
					<view class="coupon_rules_title coupon_rules_title_o">
						{{ $L('使用规则') }}：{{ item.description }}
					</view>
				</view>
				<view class="coupon_rules" @click="descriptionOpen(item.couponId)"
					v-if="item.description!=null&&item.description.length>22&&item.isOpen == false">
					<view class="coupon_rules_title coupon_rules_title_o">
						{{ $L('使用规则') }}：{{ item.description }}
						<image :src="item.isOpen? imgUrl + 'coupon/up.png': imgUrl + 'coupon/down.png'" mode="aspectFit"></image>
					</view>
				</view>
				<view class="coupon_rules" @click="descriptionOpen(item.couponId)" v-if="item.isOpen == true">
					<view class="coupon_rules_title_o">
						{{ $L('使用规则') }}：{{ item.description }}
						<image :src="item.isOpen? imgUrl + 'coupon/up.png': imgUrl + 'coupon/down.png'" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			<loadingState v-if="loadingState == 'first_loading' || couponList.length > 0" :state="loadingState" />
		</view>
		<view class="no_data" v-if="noData">
			<image :src="imgUrl + 'no_coupon.png'" mode="aspectFit"></image>
			<text>{{ $L('暂无优惠券') }}~</text>
		</view>
		<loginPop ref="loginPop"></loginPop>
	</view>
	</view>
</template>
<script>
	import loadingState from '@/components/loading-state.vue'
	import notOpen from '@/components/not_open.vue'
	import filters from '../../utils/filter.js'
	import uniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue'
	import {
		mapState
	} from 'vuex'
	export default {
		components: {
			loadingState,
			notOpen,
			uniNavBar
		},
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				loadingState: 'first_loading',
				pageSize: 10,
				// #ifdef MP
				menuButtonHeight: uni.getMenuButtonBoundingClientRect().height + 'px',
				menuButtonTop: uni.getMenuButtonBoundingClientRect().top + 'px',
				menuButtonHeights: uni.getMenuButtonBoundingClientRect().height + uni.getMenuButtonBoundingClientRect()
					.top,
				// #endif
				current: 1,
				couponList: [], //优惠券列表
				categoryList: [], //分类列表
				categoryId: '-1', //当前点击的nav，默认第一项
				noData: false, //无数据
				loading: false, //是否加载数据
				hasMore: true, //是否还有数据
				goReceiveBg: getApp().globalData.imgUrl + 'coupon/coupon_pre_bg.png', //立即领取背景
				finishReceiveBg: getApp().globalData.imgUrl + 'coupon/finishReceiveBg.png', //已抢完背景
				hasReceiveBg: getApp().globalData.imgUrl + 'coupon/hasReceiveBg.png', //已领取背景
				rondomMod: false, //随机弹框
				activity_open: false,
				rondomDes: {},
				filters,
				searchShow: false,
				scrollTopH: '',
				coupon_height: 0,
				statusBarHeight: uni.getSystemInfoSync().statusBarHeight + 40
			}
		},
		computed: {
			...mapState(['hasLogin', 'userInfo'])
		},
		async onLoad(options) {
			setTimeout(() => {
				uni.setNavigationBarTitle({
					title: this.$L('领券中心')
				})
			}, 0);

			this.getCouponList()
		},
		mounted() {
			let that = this
			let querys = wx.createSelectorQuery().in(this)
			querys.select('.coupon_center_bg').boundingClientRect(data => {
				if (data) {
					that.coupon_height = data.height
				}
			}).exec();

		},
		onReachBottom() {
			if (this.hasMore) {
				this.getMoreData()
			}
		},
		//获取滚动条的当前位置
		onPageScroll: function(e) {
			// this.tips_show = false
			this.scrollTopH = e.scrollTop;
			// if (e.scrollTop > 170) {
			// #ifdef MP 
			if (e.scrollTop > this.coupon_height - this.menuButtonHeights) {
				this.searchShow = true
			} else {
				this.searchShow = false
			}
			// #endif
			// #ifdef H5
			if (e.scrollTop > this.coupon_height) {
				this.searchShow = true
			} else {
				this.searchShow = false
			}
			// #endif
			//app-3-start
			// #ifdef APP
			if (e.scrollTop > this.coupon_height - this.statusBarHeight) {
				this.searchShow = true
			} else {
				this.searchShow = false
			}
			// #endif
			//app-3-end
		},
		methods: {
			//获取领券中心数据
			getCouponList(categoryId) {
				this.categoryId = categoryId ? categoryId : this.categoryId
				let param = {}
				param.url = 'v3/promotion/front/coupon/couponCenter'
				param.method = 'GET'
				param.data = {}
				param.data.current = this.current
				param.data.pageSize = this.pageSize
				if (this.categoryId == '-1') {
					param.data.isSelected = true
				} else if (this.categoryId == '-2') {
					param.data.isSelected = false
				} else {
					param.data.isSelected = false
					param.data.categoryId = this.categoryId //分类id
				}

				this.$request(param)
					.then((res) => {
						if (res.state == 200) {
							let result = res.data
							this.categoryList = result.categoryList
							if (result.couponList.length == 0) {
								this.noData = true
								this.couponList = result.couponList
							} else {
								if (this.current == 1) {
									this.couponList = result.couponList
								} else {
									this.couponList = this.couponList.concat(result.couponList)
								}
								this.noData = false
							}
							this.couponList.forEach((item, index) => {
								item.isOpen = false
								if (item.receivedState == 3) {
									item.couponBg = this.finishReceiveBg
								} else if (item.receivedState == 2) {
									item.couponBg = this.hasReceiveBg
								} else if (item.receivedState == 1) {
									item.couponBg = this.goReceiveBg
								}
							})
							this.hasMore = this.$checkPaginationHasMore(res.data.pagination) //是否还有数据
							if (this.hasMore) {
								this.current++
								this.loadingState = 'allow_loading_more'
							} else {
								this.loadingState = 'no_more_data'
							}
						} else {
							this.$api.msg(res.msg)
						}
					})
					.catch((e) => {
						//异常处理
					})
			},
			handleNav(categoryId) {
				this.categoryId = categoryId
				this.current = 1
				this.getCouponList(categoryId)
			},
			//规则展开
			descriptionOpen(couponId) {
				this.couponList.map((item) => {
					if (item.couponId == couponId) {
						if (item.description != '') {
							item.isOpen = !item.isOpen
							this.$forceUpdate()
						}
					}
				})
			},
			//加载更多事件
			getMoreData() {
				if (this.hasMore) {
					this.getCouponList()
				}
			},
			//立即领取
			goReceive(item) {
				
				if(!this.hasLogin){
					this.$refs.loginPop.openLogin('no_replace')
					return
				}
				
				
				let couponId = item.couponId
				let param = {}
				param.url = 'v3/promotion/front/coupon/receiveCoupon'
				param.method = 'GET'
				param.data = {}
				param.data.couponId = couponId
				this.$request(param)
					.then((res) => {
						if (res.state == 200) {
							this.$api.msg(this.$L('领取成功!'))
							let result = res.data
							this.current = 1
							this.getCouponList()
							if (item.couponType == 3) {
								//随机优惠券
								this.rondomMod = true
								this.rondomDes = result
							}

							const pages = getCurrentPages() //当前页面栈
							if (pages.length > 1) {
								const beforePage = pages[pages.length - 2] //获取上一个页面实例对象
								beforePage.$vm.getCouponList() //触发上个面中的方法 获取优惠券列表*getCouponList为上个页面的方法*
							}
						} else {
							this.$api.msg(res.msg)
							this.getCouponList()
						}
					})
					.catch((e) => {
						//异常处理
					})
			},
			//去优惠券对应的商品列表
			goGoodsList(item) {
				if (item.useState == '2') {
					this.$Router.push({
						path: '/standard/product/list',
						query: {
							couponId: item.couponId
						}
					})
				}
			},
			//关闭领取随机优惠券弹框
			close() {
				this.rondomMod = false
			},
			//去我的优惠券列表页面
			goMyCoupon() {
				this.$Router.push('/standard/coupon/myCoupon')
			}
		}
	}
</script>

<style lang="scss">
	page {
		background: $bg-color-split;
	}

	.my_coupon {
		width: 750rpx;
		margin: 0 auto;
		overflow-x: hidden;

		.random_coupon.hide {
			display: none;
		}

		.random_coupon {
			display: flex;
			justify-content: center;
			padding-top: 250rpx;

			.random_coupon_bg {
				width: 598rpx;
				height: 804rpx;
				background-size: 100% 100%;
				padding-top: 330rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				position: relative;

				.random_coupon_price {
					font-size: 68rpx;
					font-family: PangMenZhengDao;
					font-weight: 400;
					color: #d41e04;
				}

				.random_coupon_des {
					font-size: 30rpx;
					font-family: Source Han Sans CN;
					font-weight: 400;
					color: #e52308;
					line-height: 34rpx;
				}

				.close_btn {
					position: absolute;
					right: 10rpx;
					top: 22rpx;
					z-index: 20;
					width: 57rpx;
					height: 57rpx;
					background-size: 100% 100%;
				}
			}
		}

		.coupon_center_bg {
			width: 750rpx;
			height: 587rpx;
			background: #fff;
			text-align: center;
			position: relative;

			image {
				/* #ifndef H5 */
				position: absolute;
				left: 0;
				right: 0;
				top: 0;
				/* #endif */
				width: 750rpx;
				height: 587rpx;
			}
		}

		.my_coupon_nav {
			display: flex;
			flex-wrap: nowrap;
			align-items: center;
			background: #ffffff;
			height: 78rpx;
			padding: 0 20rpx;
			margin-bottom: 22rpx;

			.my_coupon_nav_pre_wrap {
				width: 715rpx;
				display: flex;
				flex-wrap: nowrap;
				align-items: center;
				overflow-x: auto;

				.my_coupon_nav_pre {
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #333333;
					height: 78rpx;
					line-height: 78rpx;
					margin-right: 77rpx;
					min-width: max-content;

					&:last-child {
						margin-right: 20rpx;
					}
				}
			}

			.active {
				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: var(--color_coupon_main) !important;
				position: relative;
				border-bottom: 8rpx solid var(--color_coupon_main);
			}
		}

		.my_coupon_navs {
			position: fixed;
			z-index: 999;
		}

		.my_coupon_list {
			margin: 0 20rpx;
			width: 710rpx;
			height: 100%;

			.my_coupon_pre {
				margin-bottom: 20rpx;
				position: relative;
				background: #fff;
				border-radius: 20rpx;
				overflow: hidden;

				.coupon_pre_top {
					width: 710rpx;
					height: 190rpx;
					background-size: 100% 100%;
					display: flex;
					align-items: center;
					position: relative;

					.coupon_qiu1 {
						width: 16rpx;
						height: 16rpx;
						background: #f5f5f5;
						opacity: 1;
						position: absolute;
						left: -8rpx;
						bottom: -8rpx;
						border-radius: 50%;
						z-index: 9;
					}

					.coupon_qiu2 {
						width: 16rpx;
						height: 16rpx;
						background: #f5f5f5;
						opacity: 1;
						position: absolute;
						right: -8rpx;
						bottom: -8rpx;
						border-radius: 50%;
						z-index: 9;
					}

					.coupon_pre_left_o {
						display: flex;
						align-items: center;
						width: 498rpx;
						background: var(--color_coupon_opacity);
						height: 100%;
					}

					.coupon_pre_left {
						display: flex;
						flex-direction: column;
						width: 203rpx;
						align-items: center;

						.coupon_pre_left_type {
							font-size: 24rpx;
							font-family: PingFang SC-Medium, PingFang SC;
							font-weight: 500;
							color: var(--color_coupon_main);
							margin-bottom: 5rpx;
						}

						.coupon_pre_price {
							font-size: 36rpx;
							font-family: Source Han Sans CN;
							font-weight: bold;
							color: var(--color_coupon_main);
							display: flex;
							align-items: baseline;

							text:nth-child(2) {
								font-size: 55rpx;
								font-family: PingFang SC-Bold, PingFang SC;
								font-weight: bold;
								color: var(--color_coupon_main);
							}

							.price_int {
								text-align: center;
								word-break: break-all;

							}
						}

						.coupon_pre_price_high {
							position: relative;
							left: 2rpx;
							top: 14rpx;
							margin-top: 8rpx;

							text:nth-child(2) {
								line-height: 40rpx;
							}
						}

						.coupon_pre_active {
							font-size: 24rpx;
							font-family: Source Han Sans CN;
							font-weight: 400;
							color: #f6130e;
							line-height: 31rpx;
							text-align: center;
							margin-top: 20rpx;
						}
					}

					.coupon_pre_cen {
						display: felx;
						flex-direction: column;
						flex: 1;

						// padding-left: 44rpx;
						.coupon_pre_title {
							font-size: 30rpx;
							font-family: PingFang SC;
							font-weight: bold;
							color: #111111;
							line-height: 31rpx;
						}

						.coupon_pre_time {
							font-size: 24rpx;
							font-family: PingFang SC;
							font-weight: 500;
							color: #333333;
							line-height: 31rpx;
							margin: 21rpx 0 17rpx;
						}

						.coupon_pre_rules {
							display: flex;
							align-items: center;

							text {
								font-size: 24rpx;
								font-family: PingFang SC;
								font-weight: 500;
								color: #999999;
								line-height: 31rpx;
							}

							image {
								width: 12rpx;
								height: 7rpx;
								margin-left: 20rpx;
							}
						}
					}

					.coupon_pre_right {
						// width: 252rpx;
						flex: 1;
						box-sizing: border-box;
						font-size: 24rpx;
						font-family: Source Han Sans CN;
						font-weight: 400;
						text-align: center;
						height: 100%;
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;

						.coupon_pre_right_btn {
							width: 140rpx;
							height: 52rpx;
							background: var(--color_coupon_main);
							border-radius: 26rpx;
							opacity: 1;
							display: flex;
							align-items: center;
							justify-content: center;
							font-size: 24rpx;
							font-family: PingFang SC-Medium, PingFang SC;
							font-weight: 500;
							color: #FFFFFF;
							margin-top: 26rpx;
						}

						.coupon_pre_btn {
							background: #999999;
						}
					}

					.coupon_state_no {
						padding: 0;
						text-align: center;
					}
				}

				.coupon_rules {
					width: 710rpx;
					padding: 20rpx 43rpx;
					box-sizing: border-box;
					font-size: 23rpx;
					font-family: PingFang SC-Regular, PingFang SC;
					font-weight: 400;
					color: #999999;
					line-height: 30rpx;
					background: #ffffff;
					border-top: 1rpx dashed #f2f2f2;
					border-radius: 0 0 20rpx 20rpx;
					position: relative;

					image {
						width: 24rpx;
						height: 16rpx;
						position: absolute;
						top: 31rpx;
						right: 20rpx;
					}

					.coupon_rules_title {
						width: 490rpx;
						margin-bottom: 10rpx;
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
					}

					.coupon_rules_title_o {
						width: 606rpx;
						margin-bottom: 10rpx;
					}
				}

				.coupon_type {
					position: absolute;
					top: 0;
					left: 0;
					padding: 0 5rpx;
					height: 30rpx;
					background: linear-gradient(0deg,
							#ff3000 0%,
							#fb3e31 0%,
							#ff3728 0%,
							#ff142f 100%);
					font-size: 20rpx;
					font-family: Source Han Sans CN;
					font-weight: 400;
					color: #ffffff;
					line-height: 30rpx;
					text-align: center;
					border-radius: 15rpx 0 15rpx 0;
				}

				.coupon_progress {
					width: 100%;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 20rpx;
					font-family: PingFang SC-Medium, PingFang SC;
					font-weight: 500;
					color: var(--color_coupon_main);
					line-height: 31rpx;

					.progress_con {
						width: 100rpx;
						margin-top: 5rpx;
						border-radius: 5rpx;
						margin-right: 8rpx;

						progress {
							border: 1px solid var(--color_coupon_main);
							border-radius: 5rpx;
						}
					}
				}
			}
		}

		.no_data {
			display: flex;
			flex-direction: column;
			align-items: center;
			padding-top: 140rpx;

			image {
				width: 380rpx;
				height: 280rpx;
			}

			text {
				font-size: 26rpx;
				font-family: Source Han Sans CN;
				font-weight: 400;
				color: #999999;
				margin: 30rpx 0;
			}

			.go_coupon_center {
				width: 160rpx;
				height: 54rpx;
				background: #f5eaea;
				border-radius: 27rpx;
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #ef242f;
				text-align: center;
				line-height: 54rpx;
			}
		}
	}


	.nav-bar {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: flex-start;
		padding-left: 20rpx;
	}

	.nav-bar image {
		width: 19rpx !important;
		height: 33rpx !important;
		margin-top: 5rpx !important;
		position: initial !important;
	}

	.nav-bar view {
		font-size: 36rpx;
		font-family: PingFang SC;
		color: #FFFFFF;
		margin-left: 21rpx;
	}

	.nav-bars {
		z-index: 999;
		background: #fff;
		width: 100%;

		image {
			width: 19rpx !important;
			height: 33rpx !important;
			margin-top: 5rpx !important;
			position: initial !important;
		}

		view {
			font-size: 36rpx;
			font-family: PingFang SC;
			color: #000;
			margin-left: 21rpx;
		}
	}
</style>
