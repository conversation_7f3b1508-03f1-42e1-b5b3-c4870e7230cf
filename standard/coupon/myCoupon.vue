<template>
    <view class="container" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }">
        <ktabbar id="ktabbar" :bgImg="imgUrl + 'default_bg.jpg'" :title="'我的卡券'" />
        <view class="my_coupon">
            <view class="my_coupon_nav">
                <view class="my_coupon_nav_pre" :class="{ active: useState == '1' }" @click="handleNav('1')">
                    {{ $L('待使用') }}
                </view>
                <view class="my_coupon_nav_pre" :class="{ active: useState == '2' }" @click="handleNav('2')">
                    {{ $L('已使用') }}
                </view>
                <view class="my_coupon_nav_pre" :class="{ active: useState == '3' }" @click="handleNav('3')">
                    {{ $L('已失效') }}
                </view>
            </view>
            <scroll-view scroll-y @scrolltolower="loadMore" class="coupon_scrollview" :style="{ height: `calc(100vh - ${statusbarHeight || '180rpx'} - 100rpx)` }">
                <view v-if="couponList.length" class="coupon_con">
                    <view class="my_coupon_list">
                        <view class="my_coupon_pre" v-for="(item, index) in couponList" :key="index" @click="goCouponDetail(item)">
                            <view class="coupon_pre_top">
                                <view class="coupon_pre_left">
                                    <view class="coupon_pre_price" v-if="item.couponType == 4" :class="{ coupon_pre_price_high: item.publishValue.toString().length > 6 }">
                                        <block v-if="Number(item.publishValue) > 0">
                                            <text class="unit">{{ $L('¥ ') }}</text>
                                            <text class="price_int">
                                                {{ $getPartNumber(item.publishValue, 'int') }}
                                            </text>
                                            <text class="price_int" v-if="item.publishValue.toString().indexOf('.') != -1">
                                                {{ $getPartNumber(item.publishValue, 'decimal') }}
                                            </text>
                                        </block>
                                        <block v-else>
                                            <text class="price_freight">免运费</text>
                                        </block>
                                    </view>
                                    <view
                                        v-else-if="item.couponType != 2"
                                        class="coupon_pre_price"
                                        :class="{ grey: item.useState != 1, coupon_pre_price_high: item.publishValue.toString().length > 6 }"
                                    >
                                        <!-- 1满减券 3 随机金额券 -->
                                        <block v-if="item.couponType != 5">
                                            <text class="unit">{{ $L('¥') }}</text>
                                            <text class="price_int">
                                                {{ $getPartNumber(item.publishValue, 'int') }}
                                            </text>
                                            <text class="price_decimal">
                                                {{ $getPartNumber(item.publishValue, 'decimal') }}
                                            </text>
                                        </block>
                                        <!-- 5 抵用券 -->
                                        <block v-else>
                                            <text class="unit">免费享受</text>
                                        </block>
                                    </view>
                                    <view v-else class="coupon_pre_price" :class="{ grey: item.useState != 1 }">
                                        <view class=""></view>
                                        <text class="price_int">
                                            {{ filters.toSplit(filters.toFixNum(item.publishValue, 1))[0] }}
                                        </text>
                                        .
                                        <text class="price_decimal">
                                            {{ filters.toSplit(filters.toFixNum(item.publishValue, 1))[1] }}
                                        </text>
                                        <text class="price_decimal">{{ $L('折') }}</text>
                                    </view>
                                    <view v-if="item.couponType != 5" class="coupon_pre_active" :class="{ grey_con: item.useState != 1 }">
                                        {{ item.couponContent }}
                                    </view>
                                </view>
                                <view class="coupon_pre_cen">
                                    <view class="coupon_pre_title">{{ item.couponName }}</view>
                                    <view class="coupon_pre_time">{{ item.effectiveStart }}~{{ item.effectiveEnd }}</view>
                                </view>
                            </view>
                            <view class="flex_row_between_center coupon_footer">
                                <view class="coupon_pre_rules footer_left" @click.stop="descriptionOpen(item.couponMemberId)">
                                    <text>{{ $L('使用规则') }}</text>
                                    <image :src="item.isOpen ? imgUrl + 'coupon/up.png' : imgUrl + 'coupon/down.png'" mode="aspectFit"></image>
                                </view>
                                <view class="footer_right">
                                    <view class="to_use" :class="{ disabled: item.useState != 1 }" @click.stop="goGoodsList(item)">去使用</view>
                                </view>
                            </view>
                            <view class="coupon_rules" v-if="item.isOpen == true">
                                <view class="coupon_rules_title">{{ item.description }}</view>
                            </view>
                            <view class="coupon_type" :class="{ grey_type: item.useState != 1 }">
                                {{ item.couponTypeValue }}
                            </view>
                        </view>
                        <loadingState v-if="loadingState == 'first_loading' || couponList.length > 0" :state="loadingState" />
                    </view>
                </view>
                <view class="no_data" v-else>
                    <image :src="imgUrl + 'no_coupon.png'" mode="aspectFit"></image>
                    <text>{{ $L('暂无优惠券，去领券中心看看吧') }}~</text>
                    <view class="go_coupon_center" @click="goCouponCenter">
                        {{ $L('领券中心') }}
                    </view>
                </view>
            </scroll-view>
        </view>
        <!-- 生成二维码的画布 不显示在视窗内 -->
        <view class="uqrcode">
            <canvas id="qrcode" style="width: 300px; height: 300px" canvas-id="qrcode" />
        </view>
        <!-- 核销码弹窗 -->
        <uniPopup ref="qrcode" type="center" @change="handleChange">
            <view class="coupon-qrcode">
                <view class="pop-close" @click="$refs.qrcode.close()">
                    <uni-icons type="clear" size="30"></uni-icons>
                </view>
                <view class="pop-header"></view>
                <view class="pop-main">
                    <image :src="tempQRcode" mode="aspectFit" class="qrcode_img"></image>
                    <view class="qrcode-text">核销请出示此二维码</view>
                </view>
            </view>
        </uniPopup>
        <!-- 手动使用弹窗 -->
        <uniPopup ref="usePopRef" type="center">
            <view class="coupon-use">
                <view class="pop-main">
                    <view class="">确认使用{{ openCoupon.couponName }}{{ openCoupon.couponTypeValue }}</view>
                </view>
                <view class="pop-footer">
                    <view class="concel" @tap="$refs.usePopRef.close()">取消</view>
                    <view class="confirm" @tap="confirmUse()">使用</view>
                </view>
            </view>
        </uniPopup>
    </view>
</template>
<script>
import ktabbar from '@/components/ktabbar.vue';
import uqrcode from './uqrcode.js';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import loadingState from '@/components/loading-state.vue';
import filters from '@/utils/filter.js';
import { mapState } from 'vuex';
export default {
    components: {
        loadingState,
        uniPopup,
        ktabbar
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            useState: '1', //使用状态
            pageSize: 10,
            current: 1,
            couponList: [], //优惠券列表
            tempQRcode: '', //优惠券二维码临时路径
            openCoupon: '', //
            noData: false, //无数据
            hasMore: false, //是否还有数据
            loadingState: 'first_loading',
            goReceiveBg: getApp().globalData.imgUrl + 'coupon/coupon_pre_bg.png', //未使用
            finishReceiveBg: getApp().globalData.imgUrl + 'coupon/finishReceiveBg.png', //已使用，已过期,
            showState: false,
            filters
        };
    },
    computed: {
        ...mapState(['hasLogin', 'userInfo']),
        statusbarHeight() {
            let size = '';
            // #ifdef MP-WEIXIN
            size = uni.getWindowInfo().statusBarHeight + 44 + 'px';
            // #endif
            return size; // 默认返回0，实际应用中可以根据需要调整
        }
    },
    onLoad() {
        this.getCouponList();
    },

    onShow() {
        if (this.showState) {
            this.getCouponList();
            this.showState = false;
        }
    },
    methods: {
        // 触底加载更多
        loadMore() {
            if (this.hasMore) {
                this.getCouponList();
            }
        },
        // 卡券详情
        goCouponDetail(item) {
            if (item.useState == 1) {
                this.$Router.push({ path: '/standard/coupon/couponDetail', query: { couponId: item.couponId } });
            }
        },
        //生成二维码
        makeQrCode(info) {
            return uqrcode.make(
                {
                    canvasId: 'qrcode',
                    size: 300,
                    margin: 10,
                    text: info
                },
                this
            );
        },
        // 手动使用优惠券
        confirmUse() {
            this.$refs.usePopRef.close();
            return false;
            if (this.openCoupon.couponCode) {
                this.$refs.usePopRef.close();
                uni.showLoading({
                    mask: true
                });
                let param = {};
                param.url = 'v3/promotion/front/coupon/list';
                param.method = 'GET';
                param.data = {};
                param.data.code = this.openCoupon.couponCode;
                this.$request(param)
                    .then((res) => {
                        uni.hideLoading();
                        if (res && res.code == 200) {
                            uni.showToast({
                                title: '使用成功',
                                icon: 'none'
                            });
                        } else {
                            uni.showToast({
                                icon: 'error',
                                title: res.message
                            });
                        }
                        // 刷新数据
                        this.current = 1;
                        this.getCouponList();
                        uni.pageScrollTo({
                            scrollTop: 0
                        });
                    })
                    .catch((err) => {
                        uni.hideLoading();
                    });
            }
        },
        // 券核销弹窗关闭
        handleChange(e) {
            let { show } = e;
            if (!show) {
                // 刷新数据
                this.current = 1;
                this.loadingState = 'loading';
                this.getCouponList();
                uni.pageScrollTo({
                    scrollTop: 0
                });
            }
        },
        //获取优惠券列表
        getCouponList() {
            let param = {};
            param.url = 'v3/promotion/front/coupon/list';
            param.method = 'GET';
            param.data = {};
            param.data.current = this.current;
            param.data.pageSize = this.pageSize;
            param.data.useState = this.useState;
            this.$request(param)
                .then((res) => {
                    if (res.state == 200) {
                        let result = res.data;
                        if (this.current == 1) {
                            this.couponList = result.list;
                        } else {
                            this.couponList = this.couponList.concat(result.list);
                        }
                        this.couponList.forEach((item, index) => {
                            if (item.useState == 1) {
                                item.couponBg = this.goReceiveBg;
                            } else {
                                item.couponBg = this.finishReceiveBg;
                            }
                        });
                        this.hasMore = this.$checkPaginationHasMore(res.data.pagination); //是否还有数据
                        if (this.hasMore) {
                            this.current++;
                            this.loadingState = 'allow_loading_more';
                        } else {
                            this.loadingState = 'no_more_data';
                        }
                    } else {
                        this.$api.msg(res.msg);
                    }
                })
                .catch((e) => {
                    //异常处理
                });
        },
        //点击nav导航
        handleNav(type) {
            this.useState = type;
            this.current = 1;
            this.getCouponList();
            uni.pageScrollTo({
                scrollTop: 0
            });
        },
        //去领券中心页面
        goCouponCenter() {
            this.showState = true;
            this.$Router.push('/standard/coupon/couponCenter');
        },
        //规则展开
        descriptionOpen(couponMemberId) {
            this.couponList.map((item) => {
                if (item.couponMemberId == couponMemberId) {
                    if (item.description != '') {
                        item.isOpen = !item.isOpen;
                        this.$forceUpdate();
                    }
                } else {
                    item.isOpen = false;
                }
            });
        },
        //去优惠券对应的商品列表
        goGoodsList(item) {
            if (item.useState == 1) {
                // 非商城使用券
                if (item.isShoppingUse != 1) {
                    // 核销券
                    if (item.isShoppingUse == 0) {
                        this.makeQrCode(item.couponCode).then((res) => {
                            this.tempQRcode = res.tempFilePath;
                            this.$refs.qrcode.open();
                        });
                    }
                    // 手动使用券
                    if (item.isShoppingUse == 2) {
                        this.openCoupon = item;
                        this.$refs.usePopRef.open();
                    }
                    return;
                }
                let params = {};
                if (item.storeId > 0) {
                    this.$Router.push({
                        path: '/standard/store/shopHomePage',
                        query: {
                            vid: item.storeId
                        }
                    });
                    return;
                }
                if (item.useType == 1) {
                    params.lowPrice = item.limitQuota;
                }
                if (item.useType == 2 && item.goodsIds) {
                    params.goodsIds = item.goodsIds;
                } else if (item.useType == 3 && item.cateIds) {
                    params.categoryId = item.cateIds;
                }
                this.$Router.push({
                    path: '/standard/product/list',
                    query: {
                        source: 'coupon',
                        ...params
                    }
                });
            }
        }
    }
};
</script>

<style lang="scss">
.container {
    width: 750rpx;
    min-height: 100vh;
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    // 背景图片固定，不滚动
    background-attachment: fixed;
    .my_coupon {
        width: 750rpx;
        margin: 0 auto;

        .my_coupon_center {
            background: #ffffff;

            image {
                width: 717rpx;
                height: 245rpx;
            }
        }

        .my_coupon_list {
            padding-top: 20rpx;

            .my_coupon_pre {
                width: 710rpx;
                margin-bottom: 20rpx;
                position: relative;
                margin: 0 auto 20rpx;
                background-color: #fff;
                box-shadow: 0 0 10rpx 0 rgba(0, 0, 0, 0.1);
                border-radius: 40rpx;
                padding: 30rpx 20rpx;

                .coupon_pre_top {
                    background-size: 100% 100%;
                    display: flex;
                    align-items: center;
                    position: relative;
                    padding-bottom: 20rpx;

                    &::after {
                        content: '';
                        border-bottom: 1px dashed #cfd1dd;
                        height: 0;
                        width: calc(100%);
                        position: absolute;
                        left: 0;
                        bottom: 0;
                    }
                    .coupon_pre_left {
                        position: relative;
                        display: flex;
                        flex-direction: column;
                        width: 203rpx;
                        align-items: center;

                        .coupon_pre_price {
                            font-size: 20rpx;
                            font-family: Source Han Sans CN;
                            font-weight: bold;
                            color: var(--color_coupon_main);
                            line-height: 31rpx;
                            display: flex;
                            align-items: baseline;

                            text:nth-child(2) {
                                font-size: 48rpx;
                                font-family: Source Han Sans CN;
                                font-weight: bold;
                                color: var(--color_coupon_main);
                                line-height: 31rpx;
                            }

                            .price_int {
                                text-align: center;
                                word-break: break-all;
                            }
                        }

                        .coupon_pre_price_high {
                            position: relative;
                            left: 2rpx;
                            top: 14rpx;
                            margin-top: 8rpx;

                            text:nth-child(2) {
                                line-height: 40rpx;
                            }
                        }

                        .coupon_pre_active {
                            font-size: 24rpx;
                            font-family: Source Han Sans CN;
                            font-weight: 400;
                            color: var(--color_coupon_main);
                            line-height: 31rpx;
                            text-align: center;
                            margin-top: 20rpx;
                        }

                        .grey {
                            color: #333333;

                            .price_int {
                                color: #333333 !important;
                            }
                        }

                        .grey_con {
                            color: #999999;
                        }
                    }

                    .coupon_pre_cen {
                        position: relative;
                        display: felx;
                        flex-direction: column;
                        flex: 1;
                        padding-left: 44rpx;

                        .coupon_pre_title {
                            font-size: 30rpx;
                            font-family: PingFang SC;
                            font-weight: bold;
                            color: #111111;
                            line-height: 31rpx;
                        }

                        .coupon_pre_time {
                            font-size: 24rpx;
                            font-family: PingFang SC;
                            font-weight: 500;
                            color: #333333;
                            line-height: 31rpx;
                            margin: 21rpx 0 17rpx;
                        }
                    }

                    .coupon_address {
                        width: 100%;
                        display: flex;
                        align-items: flex-start;
                        justify-content: space-between;

                        .address {
                            width: calc(100% - 40rpx);
                            font-size: 24rpx;
                            line-height: 1.4em;
                            color: #666666;
                        }

                        .icon {
                            width: 30rpx;
                            height: 30rpx;
                        }
                    }
                }

                .coupon_rules {
                    position: relative;
                    width: 100%;
                    padding: 20rpx 43rpx;
                    box-sizing: border-box;
                    font-size: 22rpx;
                    font-family: PingFang SC;
                    font-weight: 500;
                    color: #666666;
                    line-height: 30rpx;
                    background: #ffffff;
                    border-radius: 0 0 15rpx 15rpx;

                    .coupon_rules_title {
                        position: relative;
                    }
                }

                .coupon_type {
                    position: absolute;
                    top: 0;
                    left: 0;
                    padding: 0 20rpx;
                    height: 30rpx;
                    background: var(--color_coupon_main);
                    font-size: 20rpx;
                    font-family: Source Han Sans CN;
                    font-weight: 400;
                    color: #ffffff;
                    line-height: 30rpx;
                    text-align: center;
                    border-radius: 40rpx 0 40rpx 0;
                }

                .grey_type {
                    background: linear-gradient(0deg, #a9a28c 0%, #b1b7b0 0%, #9ea59d 0%, #9fa19e 0%, #6c6d74 100%);
                    color: #ffffff;
                }
            }

            .coupon_pre_rules {
                display: flex;
                align-items: center;

                text {
                    font-size: 24rpx;
                    font-family: PingFang SC;
                    font-weight: 500;
                    color: #999999;
                    line-height: 31rpx;
                }

                image {
                    width: 12rpx;
                    height: 7rpx;
                    margin-left: 20rpx;
                }
            }
        }

        .no_data {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-top: 50rpx;

            image {
                width: 380rpx;
                height: 280rpx;
            }

            text {
                font-size: 26rpx;
                font-family: Source Han Sans CN;
                font-weight: 400;
                color: #999999;
                margin: 30rpx 0;
            }

            .go_coupon_center {
                width: 160rpx;
                height: 54rpx;
                background: $color1;
                border-radius: 27rpx;
                font-size: 28rpx;
                font-family: PingFang SC;
                font-weight: 500;
                color: #fff;
                text-align: center;
                line-height: 54rpx;
            }
        }
    }
    .my_coupon_nav {
        padding-left: 10%;
        padding-right: 10%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        z-index: 100;
        width: 750rpx;
        box-sizing: border-box;
        height: 100rpx;
        .my_coupon_nav_pre {
            font-size: 32rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #333333;
            line-height: 39rpx;
        }

        .active {
            font-size: 32rpx;
            font-family: PingFang SC;
            font-weight: bold;
            color: var(--color_coupon_main);
            line-height: 39rpx;
            border-bottom: 6rpx solid var(--color_coupon_main);
        }
    }
    .coupon_scrollview {
        height: calc(100vh - var(--bar-height) - 88rpx);
    }
    .coupon_footer {
        padding: 20rpx 0 0 0;

        .footer_left {
            width: 30%;
        }

        .footer_right {
            width: 70%;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            row-gap: 20rpx;
        }

        .to_use {
            padding: 10rpx 25rpx;
            border-radius: 10rpx;
            background-color: $color1;
            color: #fff;
            font-size: 24rpx;
            line-height: 1.5em;

            &.disabled {
                background-color: #ccc;
                color: #9e9e9e;
            }
        }
    }
}
.uqrcode {
    position: absolute;
    left: 10000px;
    visibility: hidden;
    height: 0;
    overflow: hidden;
}

.coupon-qrcode {
    background-color: #fff;
    border-radius: 15rpx;
    padding: 30rpx 50rpx;
    position: relative;
    width: 600rpx;
    margin: 0 auto;

    .pop-close {
        position: absolute;
        right: 10rpx;
        top: 10rpx;
    }

    .pop-header {
        padding: 10rpx;
        // min-height: 100rpx;
    }

    .pop-main {
        margin-top: 20rpx;
        display: flex;
        flex-direction: column;
        align-items: center;

        .qrcode_img {
            width: 450rpx;
            height: 450rpx;
        }

        .qrcode-text {
            margin-top: 15rpx;
            width: 100%;
            font-size: 24rpx;
            text-align: center;
            color: #6c6d74;
        }
    }
}

.coupon-use {
    width: 600rpx;
    background-color: #fff;
    border-radius: 15rpx;
    padding: 30rpx 0 0 0;
    position: relative;

    .pop-close {
        position: absolute;
        right: 10rpx;
        top: 10rpx;
    }

    .pop-header {
        padding: 10rpx;
        text-align: center;
        font-size: 32rpx;
        // min-height: 100rpx;
    }

    .pop-main {
        font-size: 28rpx;
        text-align: center;
        padding: 30rpx 50rpx;
        color: #656565;
    }

    .pop-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-top: 1px solid #f3f2f2;

        .concel {
            width: 50%;
            height: 80rpx;
            line-height: 80rpx;
            text-align: center;
            color: #656565;
            border-right: 1px solid #f3f2f2;
        }

        .confirm {
            width: 50%;
            height: 90rpx;
            line-height: 90rpx;
            text-align: center;
            color: #2a82e4;
        }
    }
}

.price_freight {
    font-size: 30rpx;
}
</style>
