<template>
    <view class="container" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }">
        <ktabbar :bgImg="imgUrl + 'default_bg.jpg'" :title="'卡券详情'" />
        <view class="coupon-card">
            <!-- 状态标签 -->
            <view class="coupon-status">
                {{ item.useState == 1 ? '待使用' : '已使用' }}
            </view>
            <!-- 卡券主体内容 -->
            <view class="coupon-content">
                <!-- 左侧价值/折扣信息 -->
                <view class="coupon-left">
                    <!-- 折扣券 -->
                    <view v-if="item.couponType == 2" class="coupon_pre_price">
                        <view class=""></view>
                        <text class="price_int">
                            {{ filters.toSplit(filters.toFixNum(item.publishValue, 1))[0] }}
                        </text>
                        .
                        <text class="price_decimal">
                            {{ filters.toSplit(filters.toFixNum(item.publishValue, 1))[1] }}
                        </text>
                        <text class="price_decimal">{{ $L('折') }}</text>
                    </view>
                    <!-- 运费券 -->
                    <view v-else-if="item.couponType == 4" class="value-container">
                        <block v-if="Number(item.publishValue) > 0">
                            <view class="text-free">free</view>
                        </block>
                        <block v-else>
                            <view class="text-free">免费</view>
                        </block>
                    </view>
                    <!-- 其他券种 -->
                    <view v-else class="value-container">
                        <block v-if="item.couponType == 5">
                            <view class="text-free">免费</view>
                        </block>
                        <block v-else>
                            <text class="unit">¥</text>
                            <view class="big-text">{{ item.publishValue.toString().split('.')[0] }}</view>
                        </block>
                    </view>
                </view>

                <!-- 中间卡券信息 -->
                <view class="coupon-middle">
                    <view class="coupon-name">{{ item.couponName }}</view>
                    <view class="coupon-date">{{ item.effectiveStart }}-{{ item.effectiveEnd }}</view>
                    <view class="coupon-limit">*{{ item.useTypeValue }}</view>
                </view>
            </view>

            <!-- 二维码区域 -->
            <view class="qrcode-section" v-if="tempQRcode">
                <image class="qrcode-img" :src="tempQRcode" mode="aspectFit"></image>
                <view class="qrcode-text">核销码</view>
            </view>

            <!-- 横向分割线 -->
            <view class="coupon-divider"></view>

            <!-- 使用说明区域 -->
            <view class="usage-section">
                <view class="usage-title">使用说明</view>
                <view class="usage-list">
                    <view class="usage-item">1.本优惠券使用后，不与其他门店其他优惠同享</view>
                    <view class="usage-item">2.每人每次仅支持使用一张优惠券。</view>
                    <view class="usage-item">3.仅适用立马经销商店铺，详情与店家沟通</view>
                    <view class="usage-item">4.可转赠他人使用。</view>
                    <view class="usage-item">5.门店具有最终解释权</view>
                </view>
            </view>
        </view>
        <!-- 生成二维码的画布 不显示在视窗内 -->
        <view class="uqrcode">
            <canvas id="qrcode" style="width: 300px; height: 300px" canvas-id="qrcode"></canvas>
        </view>
        <!-- 核销码弹窗 -->
        <uniPopup ref="qrcode" type="center" @change="handleChange">
            <view class="coupon-qrcode">
                <view class="pop-close" @click="$refs.qrcode.close()">
                    <uni-icons type="clear" size="30"></uni-icons>
                </view>
                <view class="pop-header"></view>
                <view class="pop-main">
                    <image :src="tempQRcode" mode="aspectFit" class="qrcode_img"></image>
                    <view class="qrcode-text">核销请出示此二维码</view>
                </view>
            </view>
        </uniPopup>
        <!-- 手动使用弹窗 -->
        <uniPopup ref="usePopRef" type="center">
            <view class="coupon-use">
                <view class="pop-main">
                    <view class="">确认使用{{ openCoupon.couponName }}{{ openCoupon.couponTypeValue }}</view>
                </view>
                <view class="pop-footer">
                    <view class="concel" @tap="$refs.usePopRef.close()">取消</view>
                    <view class="confirm" @tap="confirmUse()">使用</view>
                </view>
            </view>
        </uniPopup>
    </view>
</template>
<script>
import ktabbar from '@/components/ktabbar.vue';
import uqrcode from './uqrcode.js';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import filters from '@/utils/filter.js';
import { mapState } from 'vuex';
export default {
    components: {
        uniPopup,
        ktabbar
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            tempQRcode: '', // 存储二维码图片
            item: {
                cateIds: null,
                couponCode: 'LI2AOMCZUL',
                couponContent: '满1000元打8.5折,最多优惠150元',
                couponId: 9,
                couponMemberId: 20,
                couponName: '免费洗车券',
                couponType: 2,
                couponTypeValue: '折扣券',
                description:
                    '仅限 立马米朵长续航电动摩托车60V20Ah铅酸成人男女通勤踏板电瓶车、立马M8远航版长续航电动摩托72V20Ah铅酸成人通勤踏板智能电瓶车、立马麦乐简易款电动自行车新国标男女士通用代步车48V12AH电瓶车、立马电动摩托车【新品热销】、使用',
                effectiveEnd: '2025.07.12',
                effectiveStart: '2025.06.12',
                goodsIds: '100001580006,100001580002,100001550003,100001580003',
                limitQuota: 1000,
                plusQualification: '可与店铺优惠券共用。',
                publishValue: 8.5,
                storeId: 0,
                storeName: '',
                useState: 1,
                useType: 2,
                useTypeValue: '仅适用于门店',
                isOpen: false
            },
            filters,
            openCoupon: {} // 当前操作的卡券
        };
    },
    computed: {
        ...mapState(['hasLogin', 'userInfo'])
    },
    onLoad(options) {
        // 这里使用固定数据，已经在data中初始化
        this.getCouponDetail();
    },
    methods: {
        /**
         * 获取优惠券详情
         */
        getCouponDetail() {
            // 实际项目中，这里应该是通过API获取优惠券详情
            // 示例代码：
            // const param = {};
            // param.url = 'v3/promotion/coupon/memberCouponDetail';
            // param.data = {
            //     couponMemberId: this.$Route.query.id
            // };
            // this.$request(param).then(res => {
            //     if (res.state == 200) {
            //         this.item = res.data;
            //         // 获取详情后生成二维码
            //         this.generateQrCode();
            //     }
            // });
        },

        /**
         * 生成优惠券核销二维码
         */
        generateQrCode() {
            // 构造二维码数据，包含优惠券ID和类型等信息
            const qrCodeData = JSON.stringify({
                couponId: this.item.couponId,
                couponMemberId: this.item.couponMemberId,
                couponType: this.item.couponType,
                code: this.item.couponCode,
                timestamp: new Date().getTime()
            });

            // 使用uqrcode库生成二维码
            this.makeQrCode(qrCodeData)
                .then((res) => {
                    this.tempQRcode = res;
                })
                .catch((err) => {
                    console.error('二维码生成失败', err);
                    // 如果生成失败，可以使用默认图片
                    this.tempQRcode = '';
                });
        },

        /**
         * 前往使用优惠券
         * @param {Object} item - 优惠券信息
         */
        goGoodsList(item) {
            if (item.useState !== 1) {
                this.$api.msg('该优惠券不可用');
                return;
            }
            if (item.couponType === 5) {
                // 服务类优惠券，直接弹出确认使用弹窗
                this.openCoupon = item;
                this.$refs.usePopRef.open();
                return;
            }

            if (item.useType === 2 && item.goodsIds) {
                // 指定商品使用
                this.$Router.push({
                    path: '/pages/shop/list',
                    query: {
                        goodsIds: item.goodsIds,
                        couponId: item.couponMemberId
                    }
                });
            } else if (item.useType === 3 && item.storeId) {
                // 指定店铺使用
                this.$Router.push({
                    path: '/pages/shop/shopDetail',
                    query: {
                        storeId: item.storeId,
                        couponId: item.couponMemberId
                    }
                });
            } else {
                // 通用券
                this.$Router.push({
                    path: '/pages/shop/list',
                    query: {
                        couponId: item.couponMemberId
                    }
                });
            }
        },
        /**
         * 生成二维码
         * @param {String} info - 二维码内容
         * @returns {Promise} - 返回生成的二维码图片
         */
        makeQrCode(info) {
            return new Promise((resolve, reject) => {
                try {
                    uqrcode.make(
                        {
                            canvasId: 'qrcode',
                            size: 300,
                            margin: 10,
                            text: info,
                            success: (res) => {
                                // 生成二维码成功
                                resolve(res);
                            },
                            fail: (err) => {
                                reject(err);
                            }
                        },
                        this
                    );
                } catch (error) {
                    reject(error);
                }
            });
        },

        /**
         * 券核销弹窗关闭
         * @param {Object} e - 事件对象
         */
        handleChange(e) {
            let { show } = e;
            if (!show) {
                // 刷新数据
                this.getCouponDetail();
            }
        },

        /**
         * 确认使用优惠券
         */
        confirmUse() {
            // 这里实现优惠券使用逻辑
            const param = {};
            param.url = 'v3/promotion/coupon/useCoupon';
            param.data = {
                couponMemberId: this.openCoupon.couponMemberId
            };

            this.$api.showLoading('处理中...');

            // 模拟API请求
            setTimeout(() => {
                this.$api.hideLoading();
                this.$refs.usePopRef.close();
                this.$api.msg('使用成功');

                // 更新状态
                this.item.useState = 2;

                // 3秒后返回上一页
                setTimeout(() => {
                    uni.navigateBack();
                }, 3000);
            }, 1500);

            // 实际项目中应该使用以下代码
            // this.$request(param).then(res => {
            //     this.$api.hideLoading();
            //     if (res.state == 200) {
            //         this.$refs.usePopRef.close();
            //         this.$api.msg('使用成功');
            //         this.item.useState = 2;
            //         setTimeout(() => {
            //             uni.navigateBack();
            //         }, 3000);
            //     } else {
            //         this.$api.msg(res.msg);
            //     }
            // }).catch(() => {
            //     this.$api.hideLoading();
            // });
        }
    }
};
</script>

<style lang="scss">
.container {
    width: 750rpx;
    min-height: 100vh;
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    // 背景图片固定，不滚动
    background-attachment: fixed;

    // 卡券卡片样式
    .coupon-card {
        width: 94%;
        margin: 0 auto;
        background: #ffffff;
        border-radius: 40rpx;
        box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
        overflow: hidden;
        position: relative;
        margin-top: 30rpx;
        padding-bottom: 30rpx;
    }

    // 卡券状态标签
    .coupon-status {
        width: 100%;
        text-align: center;
        padding: 50rpx 0;
        font-size: 36rpx;
        font-weight: 600;
        color: #333333;
    }

    // 卡券主体内容
    .coupon-content {
        display: flex;
        padding: 20rpx 30rpx 30rpx;
        position: relative;
    }

    // 左侧价值/折扣信息
    .coupon_pre_price {
        font-size: 20rpx;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: var(--color_coupon_main);
        line-height: 31rpx;
        display: flex;
        align-items: baseline;

        text:nth-child(2) {
            font-size: 48rpx;
            font-family: Source Han Sans CN;
            font-weight: bold;
            color: var(--color_coupon_main);
            line-height: 31rpx;
        }

        .price_int {
            text-align: center;
            word-break: break-all;
        }
    }

    .coupon-left {
        width: 200rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        padding-right: 30rpx;
        position: relative;

        &:after {
            content: '';
            position: absolute;
            right: 0;
            top: 10%;
            height: 80%;
            width: 1px;
            background: #eeeeee;
        }

        .value-container {
            display: flex;
            align-items: baseline;
        }

        .big-text {
            font-size: 72rpx;
            font-weight: bold;
            color: #333;
        }

        .small-text {
            font-size: 32rpx;
            color: #333;
            display: flex;
            flex-direction: column;
        }

        .text-free {
            font-size: 60rpx;
            font-weight: bold;
            color: #333;
        }

        .unit {
            font-size: 36rpx;
            margin-right: 8rpx;
        }
    }

    // 中间卡券信息
    .coupon-middle {
        flex: 1;
        padding-left: 30rpx;
        display: flex;
        flex-direction: column;
        justify-content: center;

        .coupon-name {
            font-size: 32rpx;
            font-weight: 500;
            color: #333;
            margin-bottom: 16rpx;
        }

        .coupon-date {
            font-size: 26rpx;
            color: #666;
            margin-bottom: 12rpx;
        }

        .coupon-limit {
            font-size: 24rpx;
            color: #999;
        }
    }

    // 二维码区域
    .qrcode-section {
        padding: 30rpx;
        display: flex;
        flex-direction: column;
        align-items: center;

        .qrcode-img {
            width: 360rpx;
            height: 360rpx;
            background: #f5f5f5;
        }

        .qrcode-text {
            margin-top: 20rpx;
            font-size: 28rpx;
            color: #333;
        }
    }

    // 横向分割线
    .coupon-divider {
        height: 1rpx;
        background: #eeeeee;
        margin: 10rpx 30rpx 30rpx;
        position: relative;
    }

    // 使用说明区域
    .usage-section {
        padding: 0 40rpx;

        .usage-title {
            font-size: 30rpx;
            font-weight: 600;
            color: #333;
            margin-bottom: 20rpx;
        }

        .usage-list {
            .usage-item {
                font-size: 26rpx;
                color: #666;
                line-height: 1.8;
                margin-bottom: 10rpx;
            }
        }
    }

    .my_coupon {
        width: 750rpx;
        margin: 0 auto;

        .my_coupon_nav {
            padding-left: 10%;
            padding-right: 10%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: fixed;
            top: var(--bar-height);
            z-index: 100;
            width: 750rpx;
            box-sizing: border-box;

            .my_coupon_nav_pre {
                font-size: 32rpx;
                font-family: PingFang SC;
                font-weight: 500;
                color: #333333;
                line-height: 39rpx;
                padding: 26rpx 0;
            }

            .active {
                font-size: 32rpx;
                font-family: PingFang SC;
                font-weight: bold;
                color: var(--color_coupon_main);
                line-height: 39rpx;
                border-bottom: 6rpx solid var(--color_coupon_main);
            }
        }

        .coupon_con {
            padding-top: 88rpx;
        }

        .my_coupon_center {
            background: #ffffff;

            image {
                width: 717rpx;
                height: 245rpx;
            }
        }

        .my_coupon_list {
            padding-top: 20rpx;

            .my_coupon_pre {
                width: 710rpx;
                margin-bottom: 20rpx;
                position: relative;
                margin: 0 auto 20rpx;
                background-color: #fff;
                box-shadow: 0 0 10rpx 0 rgba(0, 0, 0, 0.1);
                border-radius: 40rpx;
                padding: 30rpx 20rpx;

                .coupon_pre_top {
                    background-size: 100% 100%;
                    display: flex;
                    align-items: center;
                    position: relative;
                    padding-bottom: 20rpx;

                    &::after {
                        content: '';
                        border-bottom: 1px dashed #cfd1dd;
                        height: 0;
                        width: calc(100%);
                        position: absolute;
                        left: 0;
                        bottom: 0;
                    }

                    .coupon_pre_left {
                        position: relative;
                        display: flex;
                        flex-direction: column;
                        width: 203rpx;
                        align-items: center;

                        .coupon_pre_price {
                            font-size: 20rpx;
                            font-family: Source Han Sans CN;
                            font-weight: bold;
                            color: var(--color_coupon_main);
                            line-height: 31rpx;
                            display: flex;
                            align-items: baseline;

                            text:nth-child(2) {
                                font-size: 48rpx;
                                font-family: Source Han Sans CN;
                                font-weight: bold;
                                color: var(--color_coupon_main);
                                line-height: 31rpx;
                            }

                            .price_int {
                                text-align: center;
                                word-break: break-all;
                            }
                        }

                        .coupon_pre_price_high {
                            position: relative;
                            left: 2rpx;
                            top: 14rpx;
                            margin-top: 8rpx;

                            text:nth-child(2) {
                                line-height: 40rpx;
                            }
                        }

                        .coupon_pre_active {
                            font-size: 24rpx;
                            font-family: Source Han Sans CN;
                            font-weight: 400;
                            color: var(--color_coupon_main);
                            line-height: 31rpx;
                            text-align: center;
                            margin-top: 20rpx;
                        }

                        .grey {
                            color: #333333;

                            .price_int {
                                color: #333333 !important;
                            }
                        }

                        .grey_con {
                            color: #999999;
                        }
                    }

                    .coupon_pre_cen {
                        position: relative;
                        display: felx;
                        flex-direction: column;
                        flex: 1;
                        padding-left: 44rpx;

                        .coupon_pre_title {
                            font-size: 30rpx;
                            font-family: PingFang SC;
                            font-weight: bold;
                            color: #111111;
                            line-height: 31rpx;
                        }

                        .coupon_pre_time {
                            font-size: 24rpx;
                            font-family: PingFang SC;
                            font-weight: 500;
                            color: #333333;
                            line-height: 31rpx;
                            margin: 21rpx 0 17rpx;
                        }
                    }

                    .coupon_address {
                        width: 100%;
                        display: flex;
                        align-items: flex-start;
                        justify-content: space-between;

                        .address {
                            width: calc(100% - 40rpx);
                            font-size: 24rpx;
                            line-height: 1.4em;
                            color: #666666;
                        }

                        .icon {
                            width: 30rpx;
                            height: 30rpx;
                        }
                    }
                }

                .coupon_rules {
                    position: relative;
                    width: 100%;
                    padding: 20rpx 43rpx;
                    box-sizing: border-box;
                    font-size: 22rpx;
                    font-family: PingFang SC;
                    font-weight: 500;
                    color: #666666;
                    line-height: 30rpx;
                    background: #ffffff;
                    border-radius: 0 0 15rpx 15rpx;

                    .coupon_rules_title {
                        position: relative;
                    }
                }

                .coupon_type {
                    position: absolute;
                    top: 0;
                    left: 0;
                    padding: 0 20rpx;
                    height: 30rpx;
                    background: var(--color_coupon_main);
                    font-size: 20rpx;
                    font-family: Source Han Sans CN;
                    font-weight: 400;
                    color: #ffffff;
                    line-height: 30rpx;
                    text-align: center;
                    border-radius: 40rpx 0 40rpx 0;
                }

                .grey_type {
                    background: linear-gradient(0deg, #a9a28c 0%, #b1b7b0 0%, #9ea59d 0%, #9fa19e 0%, #6c6d74 100%);
                    color: #ffffff;
                }
            }

            .coupon_pre_rules {
                display: flex;
                align-items: center;

                text {
                    font-size: 24rpx;
                    font-family: PingFang SC;
                    font-weight: 500;
                    color: #999999;
                    line-height: 31rpx;
                }

                image {
                    width: 12rpx;
                    height: 7rpx;
                    margin-left: 20rpx;
                }
            }
        }

        .no_data {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-top: 180rpx;

            image {
                width: 380rpx;
                height: 280rpx;
            }

            text {
                font-size: 26rpx;
                font-family: Source Han Sans CN;
                font-weight: 400;
                color: #999999;
                margin: 30rpx 0;
            }

            .go_coupon_center {
                width: 160rpx;
                height: 54rpx;
                background: $color1;
                border-radius: 27rpx;
                font-size: 28rpx;
                font-family: PingFang SC;
                font-weight: 500;
                color: #fff;
                text-align: center;
                line-height: 54rpx;
            }
        }
    }

    .coupon_footer {
        padding: 20rpx 0 0 0;

        .footer_left {
            width: 30%;
        }

        .footer_right {
            width: 70%;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            row-gap: 20rpx;
        }

        .to_use {
            padding: 10rpx 25rpx;
            border-radius: 10rpx;
            background-color: $color1;
            color: #fff;
            font-size: 24rpx;
            line-height: 1.5em;

            &.disabled {
                background-color: #ccc;
                color: #9e9e9e;
            }
        }
    }
}
.uqrcode {
    position: absolute;
    left: 10000px;
    visibility: hidden;
    height: 0;
    overflow: hidden;
}

.coupon-qrcode {
    background-color: #fff;
    border-radius: 15rpx;
    padding: 30rpx 50rpx;
    position: relative;
    width: 600rpx;
    margin: 0 auto;

    .pop-close {
        position: absolute;
        right: 10rpx;
        top: 10rpx;
    }

    .pop-header {
        padding: 10rpx;
        // min-height: 100rpx;
    }

    .pop-main {
        margin-top: 20rpx;
        display: flex;
        flex-direction: column;
        align-items: center;

        .qrcode_img {
            width: 450rpx;
            height: 450rpx;
        }

        .qrcode-text {
            margin-top: 15rpx;
            width: 100%;
            font-size: 24rpx;
            text-align: center;
            color: #6c6d74;
        }
    }
}

.coupon-use {
    width: 600rpx;
    background-color: #fff;
    border-radius: 15rpx;
    padding: 30rpx 0 0 0;
    position: relative;

    .pop-close {
        position: absolute;
        right: 10rpx;
        top: 10rpx;
    }

    .pop-header {
        padding: 10rpx;
        text-align: center;
        font-size: 32rpx;
        // min-height: 100rpx;
    }

    .pop-main {
        font-size: 28rpx;
        text-align: center;
        padding: 30rpx 50rpx;
        color: #656565;
    }

    .pop-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-top: 1px solid #f3f2f2;

        .concel {
            width: 50%;
            height: 80rpx;
            line-height: 80rpx;
            text-align: center;
            color: #656565;
            border-right: 1px solid #f3f2f2;
        }

        .confirm {
            width: 50%;
            height: 90rpx;
            line-height: 90rpx;
            text-align: center;
            color: #2a82e4;
        }
    }
}

.price_freight {
    font-size: 30rpx;
}
</style>
