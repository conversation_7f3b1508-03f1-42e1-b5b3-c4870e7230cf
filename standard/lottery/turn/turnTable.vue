<template>
	<view class="turnTable_center_con">
		<view class="turnTable_center bg_style flex_row_center_center" :style="{ backgroundImage: 'url(' + imgUrl + 'turn_plate.png)' }" :animation="animationData">
			<view class="turn_con flex_row_center_center">
				<view class="turn_item" v-for="(item, index) in finalPrizeList" :key="index">
					<view class="turn_item_info" :style="{ transform: 'rotate(' + item.turn + ')' }">
						<view class="turn_item_name flex_column_center_center">
							<block v-if="item.prizeId > -1">
								<image :src="item.prizeImageUrl" mode="aspectFit" v-if="item.prizeImageUrl"></image>
								<image :src="imgUrl + (item.prizeType == 1 ? 'icon_int.png' : 'icon_cou.png')" mode="aspectFit" v-else></image>
							</block>
							<block v-else>
								<image :src="item.prizeImageUrl" mode="aspectFit" v-if="item.prizeImageUrl"></image>
								<image :src="imgUrl + 'card_smile.png'" mode="aspectFit" v-else></image>
							</block>
							<view class="turn_item_name_text">
								<!-- <text>{{item.prizeName}}</text> -->
								<!-- <jyfParser :html="item.prizeName"></jyfParser> -->
								<rich-text :nodes="item.prizeName"></rich-text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view
			class="turn_cursor bg_style"
			:style="{
				backgroundImage: 'url(' + (btnState ? btnImgAva : btnImgDis) + ')'
			}"
			@click="doDraw"
		></view>
	</view>
</template>

<script>
import jyfParser from "@/components/jyf-parser/jyf-parser.vue";
export default {
	data() {
		return {
			imgUrl: getApp().globalData.imgUrl + "lottery/",
			current: 0,
			list: Array.from(
				{
					length: 12
				},
				(v, k) => k
			).map((item) => Object(item)),
			animationData: {},
			runTimes: 5000,
			runDegs: 0,
			finalPrizeList: [],
			awardIndex: 0,
			rollOn: false,
			isCreated: false
		};
	},
	inject: ["relotDetail", "getAcDetail"],
	props: ["userInt", "drawId"],
	computed: {
		getlotDetail() {
			return this.isCreated ? this.relotDetail() : {};
		},
		reactAcDetail() {
			return this.isCreated ? this.getAcDetail() : {};
		},
		btnImgAva() {
			if (this.isCreated) {
				let { availableButtonImageUrl } = this.relotDetail();
				return availableButtonImageUrl ? availableButtonImageUrl : this.imgUrl + "turn_cursor.png";
			}
		},
		btnImgDis() {
			if (this.isCreated) {
				let { chanceOutButtonImageUrl } = this.relotDetail();
				return chanceOutButtonImageUrl ? chanceOutButtonImageUrl : this.imgUrl + "turn_cursor_dis.png";
			}
		},
		btnState() {
			if (this.isCreated) {
				let { integralUse } = this.relotDetail();
				let { acState } = this.getAcDetail();
				if (acState.flag != 1) {
					return false;
				} else {
					if (integralUse == 0 && this.userInt.remainNum == 0) {
						return false;
					} else if (integralUse > 0 && (this.userInt.remainNum == 0 || this.userInt.integral < integralUse)) {
						return false;
					} else {
						return true;
					}
				}
			}
		}
	},
	components: {
		jyfParser
	},

	created() {
		this.isCreated = true;
	},
	mounted() {
		let len = 12;
		let turnNum = 1 / len;
		let { startTime, endTime, losePrizeImageUrl, drawPrizeVOList, losePrizeDescription } = this.getlotDetail;
		let tmp = [];
		let leg = 12 - drawPrizeVOList.length * 2 + drawPrizeVOList.length;
		for (let i = 0; i < 6; i++) {
			tmp.push({
				prizeImageUrl: losePrizeImageUrl,
				prizeName: losePrizeDescription ? losePrizeDescription : this.$L("谢谢参与"),
				prizeId: -1,
				turn: (i * 2 + 0.5) * turnNum + "turn"
			});
			if (drawPrizeVOList[i]) {
				tmp.push({
					...drawPrizeVOList[i],
					turn: (i * 2 + 1.5) * turnNum + "turn"
				});
			} else {
				tmp.push({
					...drawPrizeVOList[i % drawPrizeVOList.length],
					turn: (i * 2 + 1.5) * turnNum + "turn"
				});
			}
		}
		tmp.map((item) => {
			item.prizeName = item.prizeName.replace(/\d/g, (d) => {
				return '<div style="transform:rotate(90deg);margin:0">' + d + "</div>";
			});
		});
		this.finalPrizeList = tmp;
	},

	beforeDestroy() {
		this.rollOn = false;
	},

	methods: {
		doDraw() {
			if (this.reactAcDetail.acState.flag != 1) {
				return;
			}

			if (this.userInt.remainNum == 0) {
				this.$api.msg(this.$L("您的抽奖次数已用完"));
				return;
			} else if (this.userInt.integral < this.getlotDetail.integralUse) {
				this.$api.msg(this.$L("您的抽奖积分不足"));
				return;
			}

			if (this.rollOn) {
				return;
			}
			this.rollOn = true;

			this.$request({
				url: "v3/promotion/front/draw/doDraw",
				method: "POST",
				data: {
					drawId: this.drawId
				}
			}).then((res) => {
				if (res.state == 200) {
					this.lotResult = res.data;
					if (this.lotResult.isPrize == 1) {
						this.awardIndex = this.finalPrizeList.findIndex((item) => item.prizeId == this.lotResult.prizeId);
					} else {
						this.awardIndex = this.finalPrizeList.findIndex((item) => item.prizeId == -1);
					}
					this.turnOn();
				} else {
					this.$api.msg(res.msg);
					this.rollOn = false;
				}
			});
		},

		turnOn() {
			let _this = this;
			let randonDeg = Math.ceil(Math.random() * 15); //在转至目标区域时，用随机度数，随机指向目标区域内任意的位置
			this.runDegs = this.runDegs + (360 - (this.runDegs % 360)) + (360 * this.list.length - (this.awardIndex + 1) * (360 / this.list.length)) + randonDeg;
			//旋转角度--- index 是 顺时针排列，旋转方向也是顺时针，所以必须旋转 index占比角度的互补角度(360*n - index的占比角度)才能转到index
			var animationRun = uni.createAnimation({
				duration: _this.runTimes,
				timingFunction: "ease"
			});
			animationRun.rotate(this.runDegs).step();
			this.animationData = animationRun.export();
			setTimeout(() => {
				this.rollOn = false;
				this.$emit("res", "complete", this.lotResult);
			}, _this.runTimes + 300);
		}
	}
};
</script>

<style lang="scss">
.bg_style {
	background-position: center center;
	background-repeat: no-repeat;
	background-size: contain;
}

.turnTable_center_con {
	position: relative;
	// margin-top: 10rpx;
	margin-top: 35rpx;
	//app-1-start
	/* #ifdef APP-PLUS */
	margin-top: calc(10rpx - var(--status-bar-height));
	/* #endif */
	//app-1-end
	.turn_cursor {
		max-width: 160rpx;
		max-height: 200rpx;
		min-height: 160rpx;
		min-width: 120rpx;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -58%);
	}
}

.turnTable_center {
	width: 720rpx;
	height: 720rpx;

	.turn_con {
		width: 570rpx;
		height: 570rpx;
		position: relative;

		.turn_item {
			position: absolute;
			width: 570rpx;
			height: 570rpx;
			font-weight: 600;
			text-shadow: 0 1px 1px rgba(255, 255, 255, 0.6);
			overflow: hidden;

			.turn_item_info {
				position: relative;
				display: block;
				margin: 0 auto;
				text-align: center;
				transform-origin: 50% 285rpx;
				font-size: 24rpx;

				.turn_item_name {
					image {
						width: 52rpx;
						height: 52rpx;
					}
				}

				.turn_item_name_text {
					line-height: 24rpx;
					width: 28rpx;
					font-size: 22rpx;
					overflow: hidden;
					word-break: break-all;
					color: #ca1518;
					.verti_text {
						writing-mode: vertical-rl;
					}
				}
			}
		}
	}
}
</style>
