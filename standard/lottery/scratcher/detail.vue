<template>
	<view :style="mix_diyStyle">
		<view
			class="scratcher_main"
			:style="{
				backgroundImage: 'url(' + backgroundImg + ')',
				minHeight: screenHeight + 'px'
			}"
		>
			<view class="scratcher_main_con">
				<view class="scratcher_title bg_style">
					<!-- :style="{backgroundImage:'url('+imgUrl+'scratcher_title.png)'}" -->
					<view class="right_intro">
						<view class="right_item" @click="open">{{ $L('活动详情') }}</view>
					</view>
				</view>
				<view class="scra_st flex_row_center_center">
					<view class="flex_row_center_center">
						{{ reactgetAcDetail.acState && reactgetAcDetail.acState.flag == 1 ? reactgetAcDetail.acState.desc + myInt.remainNum : reactgetAcDetail.acState.desc }}
					</view>
				</view>
				<view class="">
					<scratTable @res="res" ref="scratTable" :userInt="myInt" :drawId="drawId"></scratTable>
				</view>

				<view :class="['self_info', lotDetail.integralUse > 0 ? 'flex_row_between_center' : 'flex_row_center_center']">
					<view class="info_btn" v-if="lotDetail.integralUse > 0">{{ $L('我的积分') }}：{{ myInt.integral }}{{ $L('积分') }}</view>
					<view class="info_btn" @click="toRec">{{ $L('我的中奖记录') }}</view>
				</view>

				<view class="scratcher_bot" v-if="reactgetAcDetail.acState && reactgetAcDetail.acState.flag == 1">
					<view class="scratcher_bot_con bg_style">
						<view class="roll_in_list flex_column_between_center">
							<view class="flex_row_center_center roll_title_con">
								<image :src="imgUrl + 'dot_left.png'" mode="aspectFit"></image>
								<view class="roll_title">{{ $L('中奖名单') }}</view>
								<image :src="imgUrl + 'dot_left.png'" mode="aspectFit"></image>
							</view>
							<view
								class="roll_swiper flex_row_center_center bg_style_cover"
								:style="{
									backgroundImage: 'url(' + imgUrl + 'scra_print_bg.png)'
								}"
							>
								<swiper
									:indicator-dots="false"
									:autoplay="true"
									:interval="5000"
									:duration="1000"
									:disable-touch="true"
									:vertical="true"
									circular
									@change="change"
									display-multiple-items="3"
								>
									<swiper-item v-for="(item, index) in lotList" :key="index">
										<view :class="{ roll_swiper_item: true, sel: index == current }">
											{{ $L('恭喜') }} {{ item.memberNameHide }} {{ $L('获得') }}{{ item.prizeName }}
										</view>
									</swiper-item>

									<block v-if="3 - lotList.length > 0">
										<swiper-item v-for="i in 3 - lotList.length" :key="i"></swiper-item>
									</block>
								</swiper>
							</view>
						</view>
					</view>
				</view>
			</view>

			<popInfo ref="popInfo" @revive="revive"></popInfo>
		</view>
	</view>
</template>

<script>
import { arrCom } from '@/utils/common.js';
import scratTable from './scratTable.vue';
import popInfo from '../component/popInfo.vue';
export default {
	components: {
		popInfo,
		scratTable
	},
	props: {
		lotDetail: {
			type: Object
		},
		drawId: {
			type: Number
		}
	},
	inject: ['getAcDetail'],
	computed: {
		reactgetAcDetail() {
			return this.isCreated ? this.getAcDetail() : {};
		},
		backgroundImg() {
			if (this.isCreated) {
				let { backgroundImageUrl } = this.lotDetail;
				return backgroundImageUrl ? backgroundImageUrl : this.imgUrl + 'scra_lotte_bg.png';
			}
		}
	},
	data() {
		return {
			imgUrl: getApp().globalData.imgUrl + 'lottery/',
			current: 0,
			lotList: [],
			lotResult: {},
			rollRes: '',
			myInt: {},
			screenHeight: 0,
			timer: null,
			isCreated: false
		};
	},
	created() {
		this.isCreated = true;
	},
	mounted() {
		this.getlotRecAsync();
		this.getlotInt();
		this.screenHeight = uni.getSystemInfoSync().windowHeight;
	},
	beforeDestroy() {
		clearInterval(this.timer);
	},
	methods: {
		change(e) {
			this.current = e.detail.current + 1;
		},
		res(seq, lotResult) {
			if (this.lotDetail.integralUse > 0) this.myInt.integral -= this.lotDetail.integralUse;
			if (this.myInt.remainNum > 0) this.myInt.remainNum -= 1;
			if (lotResult.prizeType == 1) this.myInt.integral += parseInt(lotResult.description);
			setTimeout(() => {
				this.$refs.popInfo.open(lotResult, lotResult.isPrize == 1 ? 'roll_on' : 'roll_off');
			}, 500);
		},
		open() {
			this.$refs.scratTable.canvasShow = false;
			this.$refs.popInfo.open({}, 'acDetail');
		},
		revive() {
			this.$refs.scratTable.canvasShow = true;
			this.$nextTick(() => {
				this.$refs.scratTable.initDrawCanvas();
			});
		},

		async getlotRecAsync() {
			if (this.reactgetAcDetail.acState.flag != 1) {
				return;
			}

			this.lotList = await this.getlotRec();
			this.timer = setInterval(async () => {
				let res = await this.getlotRec();
				if (res.length) {
					this.lotList.push(...arrCom(this.lotList, res, 'winId'));
				}
			}, 6000);
		},
		getlotRec() {
			return new Promise((resolve) => {
				this.$request({
					url: 'v3/promotion/front/draw/winList',
					data: {
						drawId: this.drawId
					}
				}).then((res) => {
					if ((res.state = 200)) {
						resolve(res.data);
					}
				});
			});
		},
		getlotInt() {
			this.$request({
				url: 'v3/promotion/front/draw/integral',
				data: {
					drawId: this.drawId
				}
			}).then((res) => {
				if ((res.state = 200)) {
					this.myInt = res.data;
				}
			});
		},
		toRec() {
			this.$Router.push({
				path: '/standard/lottery/lotRec',
				query: {
					drawId: this.drawId
				}
			});
		}
	}
};
</script>

<style lang="scss" scoped>
uni-swiper {
	display: block;
	width: 636rpx;
	height: 170rpx;
}

uni-swiper-item {
	display: flex;
	align-items: center;
	justify-content: center;
}

/* #ifdef MP */
swiper {
	width: 636rpx !important;
	height: 170rpx !important;
}

swiper-item {
	display: flex;
	align-items: center;
	justify-content: center;
}

/* #endif */

.bg_style {
	background-position: center center;
	background-repeat: no-repeat;
	background-size: contain;
}

.bg_style_cover {
	background-position: center center;
	background-repeat: no-repeat;
	background-size: 100% 100%;
}

.scratcher_main {
	margin: 0;
	//app-1-start
	/* #ifdef APP-PLUS */
	padding-top: calc(var(--status-bar-height) + 90rpx);
	/* #endif */
	//app-1-end
	/* #ifndef APP-PLUS */
	padding-top: 90rpx;
	/* #endif */
	background-position: center center;
	background-repeat: no-repeat;
	background-size: 100% 100%;
	/* #ifdef H5 */
	height: 100vh;
	/* #endif */

	.scratcher_main_con {
		margin-top: 20rpx;
		position: relative;
	}

	.scra_st {
		margin-bottom: 32rpx;

		view {
			min-width: 320rpx;
			padding: 0 10rpx;
			height: 50rpx;
			background: linear-gradient(90deg, #ffe155 0%, #f4e878 0%, #fff7b7 52%, #f3e578 100%);
			box-shadow: 0px 6px 3px 0px rgba(145, 41, 1, 0.41);
			text-align: center;
			line-height: 50rpx;
			border-radius: 25px;
			font-size: 30rpx;
			font-family: SourceHanSansCN;
			font-weight: 600;
			color: #d00908;
		}
	}

	.self_info {
		padding: 0 70rpx;

		.info_btn {
			font-size: 24rpx;
			font-family: SourceHanSansCN;
			font-weight: 600;
			color: #a95e01;
			background: #fede99;
			border-radius: 25px;
			padding: 10rpx 30rpx;
		}
	}

	.scratcher_title {
		margin-top: 30rpx;
		height: 315rpx;
	}

	.right_intro {
		display: flex;
		justify-content: flex-end;

		/* #ifdef MP */
		padding-top: 50rpx;
		/* #endif */

		.right_item {
			width: 141rpx;
			height: 50rpx;
			background: #8d0d0c;
			border-radius: 25rpx 0 0 25rpx;
			text-align: center;
			line-height: 50rpx;
			font-size: 26rpx;
			font-family: SourceHanSansCN;
			font-weight: 600;
			color: #ffffff;
		}
	}

	.scratcher_bot {
		padding: 0 40rpx;

		.scratcher_bot_con {
			height: 330rpx;
			padding: 20rpx 0;
			position: relative;
		}

		.bot_instruct {
			padding: 0 72rpx;

			view:first-child {
				text-align: center;
				font-size: 26rpx;
				font-family: SourceHanSansCN;
				font-weight: bold;
				color: #ab0100;
			}

			view:last-child {
				margin-top: 6rpx;
				font-size: 18rpx;
				font-family: SourceHanSansCN;
				font-weight: 400;
				color: #d13912;
			}
		}

		.roll_in_list {
			padding-top: 40rpx;
			width: 100%;

			.roll_title_con {
				image {
					width: 100rpx;
					height: 7px;

					&:last-child {
						transform: rotate(180deg);
					}
				}
			}

			.roll_title {
				padding-left: 8rpx;
				letter-spacing: 6px;
				text-align: center;
				font-size: 36rpx;
				font-family: zcoolqingkehuangyouti;
				font-weight: 600;
				color: #ffffff;
				margin: 0 10rpx;
			}

			.roll_swiper {
				margin-top: 20rpx;
				width: 640rpx;
				height: 200rpx;
			}

			.roll_swiper_item {
				text-align: center;
				font-size: 24rpx;
				font-family: SourceHanSansCN;
				font-weight: 400;
				color: #ffffff;
				opacity: 0.8;

				&.sel {
					color: #ffffff;
					font-weight: 600;
					font-size: 26rpx;
				}
			}
		}
	}
}
</style>
