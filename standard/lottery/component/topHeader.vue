<template>
  <view class="top_header">
    <view class="fixed_top_bar"></view>
    <!-- #ifdef MP -->
      <view class="" :style="{
          paddingTop:statusBarHeight,
        }">
      </view>
      <view class="header_v header_o" :style="{height:menuButtonHeight,top:menuButtonTop,width:menuButtonleft}">
        <image :src="backs" class="header_o_img" @click="back"></image>
        <image :src="imgUrl + 'lotte_share.png'" @click="openShareVo"></image>
      </view>
    <!-- #endif -->
      <!-- #ifndef MP -->
      <view class="header_v">
        <image :src="imgUrl + 'lotte_back.png'" @click="back"></image>
        <image :src="imgUrl + 'lotte_share.png'" @click="openShareVo"></image>
      </view>
      <!-- #endif -->
  </view>
</template>

<script>
export default {
  data() {
    return {
      imgUrl: getApp().globalData.imgUrl + 'lottery/',
      // #ifdef MP
      backs:getApp().globalData.imgUrl+'index/back.png',
      statusBarHeight:uni.getSystemInfoSync().statusBarHeight+'px',
      menuButtonHeight:uni.getMenuButtonBoundingClientRect().height+'px',
      menuButtonTop:uni.getMenuButtonBoundingClientRect().top+'px',
      menuButtonleft:uni.getMenuButtonBoundingClientRect().left+'px',
      // #endif
    }
  },
  methods: {
    openShareVo() {
      this.$emit('openShare')
    },
    back() {
      const page = getCurrentPages()
      if (page.length > 1) {
        this.$Router.back(1)
      } else {
        // this.$Router.replace('/pages/index/index')
        uni.switchTab({
          url: '/pages/index/index'
        })
        this.$Router.pushTab('/pages/index/index')
      }
    }
  }
}
</script>

<style lang="scss">
.top_header {
  position: absolute;
  top: 0;
  width: 100%;
}

//app-1-start
/* #ifdef APP-PLUS */
.fixed_top_bar {
  width: 100%;
  height: var(--status-bar-height);
  top: 0;
  z-index: 9999;
}
/* #endif */
//app-1-end

.header_v {
  padding: 20rpx 20rpx 0rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  image {
    width: 50rpx;
    height: 50rpx;
  }
}
.header_o{
  z-index: 999;
  .header_o_img{
    width: 15rpx;
    height: 28rpx;
  }
  padding: 0;
  position: fixed;
  padding: 0 20rpx;
}
</style>
