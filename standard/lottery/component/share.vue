<template>
	<view class="">
		<uni-popup ref="popShare" type="bottom" @change="popChange">
			<view class="popShare">
				<view class="tri_share">
					<!-- app-1-start -->
					<!-- #ifdef APP-PLUS -->
					<view class="img_con flex_column_between_center" @click="sldShare(0, 'WXSceneSession')">
						<image :src="imgUrl + 'rank/wx_share.png'" mode="aspectFit"></image>
						<text>{{ $L('微信好友') }}</text>
					</view>
					<view class="img_con flex_column_between_center" @click="sldShare(0, 'WXSenceTimeline')">
						<image :src="imgUrl + 'rank/pyq_share.png'" mode="aspectFit"></image>
						<text>{{ $L('朋友圈') }}</text>
					</view>
					<!-- #endif -->
					<!-- app-1-end -->
					<!-- wx-1-start -->
					<!-- #ifdef MP-WEIXIN -->
					<button open-type="share" @click="$refs.popShare.close()" class="shareButton">
						<view class="img_con flex_column_between_center">
							<image :src="imgUrl + 'rank/wx_share.png'" mode="aspectFit"></image>
							<text>{{ $L('微信好友') }}</text>
						</view>
					</button>
					<!-- #endif -->
					<!-- wx-1-end -->
					<!-- #ifdef H5 -->
					<block v-if="$isWeiXinBrower()">
						<view class="img_con flex_column_between_center" @tap.stop="sldShareBrower(1)">
							<image :src="imgUrl + 'rank/wx_share.png'" mode="aspectFit"></image>
							<text>{{ $L('微信好友') }}</text>
						</view>
						<view class="img_con flex_column_between_center" @tap.stop="sldShareBrower(2)">
							<image :src="imgUrl + 'rank/pyq_share.png'" mode="aspectFit"></image>
							<text>{{ $L('朋友圈') }}</text>
						</view>
					</block>
					<!-- #endif -->
				</view>
				<view class="can_con" @click="$refs.popShare.close()">{{$L('取消')}}</view>
			</view>
		</uni-popup>

		<block v-if="$isWeiXinBrower()">
			<!-- 微信浏览器分享提示  start-->
			<view class="wx_brower_share_mask flex_row_center_center" v-if="showWeiXinBrowerTip">
				<view class="wx_brower_share_top_wrap">
				    <image :src="imgUrl+'wx_share_tip.png'" mode="widthFix" @tap="closeShareModel" class="wx_brower_share_img"></image>
				</view>
			</view>
			<!-- 微信浏览器分享提示  end-->
		</block>
		
		<block v-else>
			<!-- 非微信浏览器分享提示  start-->
			<view class="wx_brower_share_mask flex_row_center_center" v-if="showWeiXinBrowerTip" @click="showWeiXinBrowerTip = false">
				<view class="qr_code">
					<view class="">邀请好友来扫一扫</view>
					<yuanqiQrCode ref="yuanqiQRCode" :text="window.location.href" :size="400" :borderSize="15"></yuanqiQrCode>
				</view>
			</view>
			<!-- 非微信浏览器分享提示  end-->
		</block>
	</view>
</template>

<script>
	import yuanqiQrCode from "@/components/yuanqi-qr-code/yuanqi-qr-code.vue"
	export default {
		components:{
			yuanqiQrCode
		},
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				showWeiXinBrowerTip: false,
				window
			}
		},
		props: ['shareData'],
		methods: {
			open() {
				// #ifdef H5
				if (this.$isWeiXinBrower()) {
					this.$refs.popShare.open()
				} else {
					this.showWeiXinBrowerTip = true
				}
				// #endif
				// #ifdef APP-PLUS||MP
				this.$refs.popShare.open()
				// #endif
			},
			sldShare(type, scene) {
				this.$refs.popShare.close()
				let shareData1 = {}
				let {
					shareData
				} = this
				if (type == 0) {
					shareData1.href =
						getApp().globalData.apiUrl + shareData.path.substring(1)
					shareData1.title = shareData.title
					shareData1.summary = shareData.desc
					shareData1.imageUrl = shareData.imageUrl
				} else if (type == 2) {
					shareData1.imageUrl = shareData.imageUrl
				}
				this.$weiXinAppShare(type, scene, shareData1)
			},
			sldShareBrower(type) {
				this.showWeiXinBrowerTip = true
				this.$refs.popShare.close()
			},
			popChange(e) {
				uni.$emit('openShare', e.show)
			},
			closeShareModel() {
				this.share_model = false;
				this.showWeiXinBrowerTip = false;
			}
		}
	}
</script>

<style lang="scss">
	.popShare {
		border-radius: 10px 10px 0px 0px;
		background: #ebebeb;
		padding: 20rpx;
		height: 354rpx;
		
		

		.tri_share {
			display: flex;
			justify-content: space-evenly;
			margin-top: 50rpx;

			.img_con {
				width: 108rpx;

				image {
					width: 108rpx;
					height: 108rpx;
				}

				text {
					margin-top: 16rpx;
					font-size: 24rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #8c8c8c;
				}
			}
		}

		.can_con {
			width: 100%;
			height: 68rpx;
			background: #ffffff;
			border-radius: 10px;
			text-align: center;
			line-height: 68rpx;
			font-size: 28rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #4c4c4c;
			margin-top: 40rpx;
		}
	}

	.shareButton {
		display: flex;
		flex-direction: column;
		align-items: center;
		background: transparent;
		border-radius: 0;
		height: auto;
		line-height: unset;
		padding: 0;
		margin: 0;

		&::after {
			border-width: 0;
		}
	}

	/* 微信浏览器分享提示 start */
	.wx_brower_share_mask {
		width: 750rpx;
		height: 100vh;
		background-color: rgba(0, 0, 0, 0.45);
		position: fixed;
		z-index: 99999;
		top: 0;
	}

	::-webkit-scrollbar {
		display: none;
	}
	
	.qr_code{
		border-radius: 20rpx;
		background-color:#fff;
		padding: 20rpx;
		view{
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: rgba(102, 102, 102, 1);
			text-align: center;
		}
	}

	scroll-view ::-webkit-scrollbar {
		display: none !important;
		width: 0 !important;
		height: 0 !important;
		-webkit-appearance: none;
		background: transparent;
	}

	.wx_brower_share_top_wrap {
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: flex-end;
		align-items: flex-start;
		margin-top: 10rpx;
	}

	.wx_brower_share_top_wrap .wx_brower_share_img {
		width: 450rpx;
		height: 150rpx;
		margin-right: 30rpx;
	}

	.share_h5 {
		width: 100% !important;
		height: 100% !important;
	}

	uni-image>img {
		opacity: unset;
		object-fit: contain;
	}

	.share_h5_operate_img {
		width: 440rpx !important;
		height: 120rpx !important;
	}

	.share_h5_close_img {
		width: 50rpx !important;
		height: 50rpx !important;
	}

	.share_h5_img_bottom {
		width: 50rpx !important;
		height: 200rpx !important;
	}

	/* 微信浏览器分享提示 end */
</style>
