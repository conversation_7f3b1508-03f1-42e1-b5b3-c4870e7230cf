<template>
	<view class="cardTable">
		<view
			class="card_item bg_style flex_column_center_center"
			:class="{ animt: index == indexFlag }"
			:style="{
				backgroundImage: 'url(' + imgUrl + (showFlag == index ? 'card_blank' : btnState ? 'card_g_com' : 'card_g_com_dis') + '.png)'
			}"
			v-for="(item, index) in cardArr"
			:key="index"
			@click="doDraw(index)"
		>
			<view class="card_item_inner bg_style bg_style flex_column_center_center" v-if="index == showFlag">
				<view class="card_item_inner_text bg_style" :style="{ backgroundImage: 'url(' + prizeImg(lotResult) + ')' }"></view>
				<view :style="{ transform: lotResult.fontSize }">{{ lotResult.prizeName }}</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			imgUrl: getApp().globalData.imgUrl + 'lottery/',
			current: 0,
			cardArr: Array.from(
				{
					length: 12
				},
				(v, k) => k
			).map((item) => item + 1),
			indexFlag: -1,
			showFlag: -1,
			lotResult: {},
			rollOn: false,
			isCreated: false
		};
	},
	inject: ['relotDetail', 'getAcDetail'],
	props: ['userInt', 'drawId'],
	computed: {
		getlotDetail() {
			return this.isCreated ? this.relotDetail() : {};
		},
		reactgetAcDetail() {
			return this.isCreated ? this.getAcDetail() : {};
		},
		btnState() {
			if (this.isCreated) {
				let { integralUse } = this.relotDetail();
				let { acState } = this.getAcDetail();
				if (acState.flag != 1) {
					return false;
				} else {
					if (integralUse == 0 && this.userInt.remainNum == 0) {
						return false;
					} else if (integralUse > 0 && (this.userInt.remainNum == 0 || this.userInt.integral < integralUse)) {
						return false;
					} else {
						return true;
					}
				}
			}
		}
	},
	beforeDestroy() {
		this.rollOn = false;
	},
	created() {
		this.isCreated = true;
	},
	methods: {
		change(e) {
			this.current = e.detail.current + 1;
		},
		open() {
			this.$refs.popInfo.open({}, 'acDetail');
		},
		openShare() {},

		doDraw(index) {
			if (this.reactgetAcDetail.acState.flag != 1) {
				return;
			}

			if (this.userInt.remainNum == 0) {
				this.$api.msg(this.$L('您的抽奖次数已用完'));
				return;
			} else if (this.userInt.integral < this.getlotDetail.integralUse) {
				this.$api.msg(this.$L('您的抽奖积分不足'));
				return;
			}

			if (this.rollOn) {
				return;
			}
			this.rollOn = true;

			this.$request({
				url: 'v3/promotion/front/draw/doDraw',
				method: 'POST',
				data: {
					drawId: this.drawId
				}
			}).then((res) => {
				if (res.state == 200) {
					this.lotResult = res.data;
					if (this.lotResult.prizeName.length >= 6) {
						this.lotResult.fontSize = 'scale(0.8)';
					} else {
						this.lotResult.fontSize = 'scale(1)';
					}

					this.openCard(index);
				} else {
					this.$api.msg(res.msg);
					this.rollOn = false;
				}
			});
		},
		openCard(index) {
			this.indexFlag = index;
			setTimeout(() => {
				this.indexFlag = -1;
				this.showFlag = index;
				this.rollOn = false;
				this.$emit('res', 'complete', this.lotResult);
			}, 1200);
		},
		prizeImg(lotResult) {
			if (lotResult.isPrize == 1) {
				if (lotResult.prizeType == 1) {
					return lotResult.prizeImage ? lotResult.prizeImage : this.imgUrl + 'icon_int.png';
				} else {
					return lotResult.prizeImage ? lotResult.prizeImage : this.imgUrl + 'icon_cou.png';
				}
			} else {
				return lotResult.losePrizeImage ? lotResult.losePrizeImage : this.imgUrl + 'card_smile.png';
			}
		}
	}
};
</script>

<style lang="scss">
.bg_style {
	background-position: center center;
	background-repeat: no-repeat;
	background-size: contain;
}

.cardTable {
	display: flex;
	flex-wrap: wrap;
	padding: 20rpx 20rpx 20rpx 36rpx;
	.card_item {
		width: 144rpx;
		height: 170rpx;
		margin-bottom: 10rpx;

		.card_item_inner {
			// width:100rpx;
			// height:100rpx;
			.card_item_inner_text {
				margin-bottom: 5rpx;
				width: 76rpx;
				height: 76rpx;
			}

			view:last-child {
				font-size: 22rpx;
				font-family: SourceHanSansCN;
				font-weight: 500;
				color: #bc480c;
			}
		}
	}
}

.animt {
	animation: turn 1.2s;
}

@keyframes turn {
	0% {
		transform: perspective(150px) rotateY(0deg);
	}

	50% {
		transform: perspective(150px) rotateY(0deg);
	}

	100% {
		transform: perspective(150px) rotateY(360deg);
	}
}
</style>
