<template>
  <view class="grid_table">
    <view
      class="grid_table_con bg_style"
      :style="{ backgroundImage: 'url(' + imgUrl + 'grid_bg_s.png)' }"
    >
      <view
        class="grid_times"
        v-if="
          reactAcDetail.acState &&
          (reactAcDetail.acState.flag == 0 || reactAcDetail.acState.flag == 2)
        "
      >
        <text>{{ reactAcDetail.acState.desc }}</text>
        <text>{{ reactAcDetail.acState.time }}</text>
      </view>
      <view class="grid_times" v-else>
        <text>{{ reactAcDetail.acState.desc }}{{ userInt.remainNum }}</text>
      </view>

      <view class="grid_center" v-if="prizeList.length">
        <block v-for="(item, index) in prizeList" :key="index">
          <view
            :class="{
              grid_item: true,
              bg_style: true,
              flex_column_center_center: true,
              rollOn: rollOn && index != countDownM
            }"
            :style="{
              backgroundImage:
                'url(' +
                imgUrl +
                (index == countDownM ? 'grid_item_bg_sel' : 'grid_item_bg') +
                '.png)'
            }"
          >
            <block v-if="item.prizeId > -1">
              <image
                :src="item.prizeImageUrl"
                mode="aspectFit"
                v-if="item.prizeImageUrl"
              ></image>
              <image
                :src="
                  imgUrl +
                  (item.prizeType == 1 ? 'icon_int.png' : 'icon_cou.png')
                "
                mode="aspectFit"
                v-else
              ></image>
            </block>
            <block v-else>
              <image
                :src="item.prizeImageUrl"
                mode="aspectFit"
                v-if="item.prizeImageUrl"
              ></image>
              <image
                :src="imgUrl + 'card_smile.png'"
                mode="aspectFit"
                v-else
              ></image>
            </block>

            <text>{{ item.prizeName }}</text>
          </view>
        </block>
        <view
          class="grid_item_btn bg_style"
          id="grid_item_btn"
          :style="{
            backgroundImage: 'url(' + (btnState ? btnImgAva : btnImgDis) + ')'
          }"
          @click="doDraw"
        >
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      imgUrl: getApp().globalData.imgUrl + 'lottery/',
      current: 0,
      countDownM: -1,
      timer: null,
      count: 0,
      totalCount: 4,
      finalIndex: 7,
      speed: 80,
      rollOn: false,
      acState: {},
      lotResult: {},
      isCreated: false
    }
  },
  inject: ['relotDetail', 'getAcDetail'],
  props: ['userInt', 'drawId'],

  created() {
    this.isCreated = true
  },
  computed: {
    getlotDetail() {
      return this.isCreated ? this.relotDetail() : {}
    },
    reactAcDetail() {
      return this.isCreated ? this.getAcDetail() : {}
    },
    btnState() {
      if (this.isCreated) {
        let { integralUse } = this.getAcDetail()
        let { acState } = this.getAcDetail()

        if (acState && acState.flag != 1) {
          return false
        } else {
          if (integralUse == 0 && this.userInt.remainNum == 0) {
            return false
          } else if (
            integralUse > 0 &&
            (this.userInt.remainNum == 0 || this.userInt.integral < integralUse)
          ) {
            return false
          } else {
            return true
          }
        }
      }
    },
    btnImgAva() {
      let { availableButtonImageUrl } = this.isCreated ? this.relotDetail() : {}
      return availableButtonImageUrl
        ? availableButtonImageUrl
        : this.imgUrl + 'twe_roll.png'
    },
    btnImgDis() {
      let { chanceOutButtonImageUrl } = this.isCreated ? this.relotDetail() : {}
      return chanceOutButtonImageUrl
        ? chanceOutButtonImageUrl
        : this.imgUrl + 'twe_roll_dis.png'
    },
    prizeList() {
      if (this.isCreated) {
        let {
          startTime,
          endTime,
          losePrizeImageUrl,
          drawPrizeVOList,
          losePrizeDescription
        } = this.relotDetail()
        let tmp = []
        let len = 6
        for (let i = 0; i < len; i++) {
          if (drawPrizeVOList && drawPrizeVOList[i]) {
            tmp.push(drawPrizeVOList[i])
          } else {
            tmp.push(drawPrizeVOList[i % drawPrizeVOList.length])
          }
          tmp.push({
            prizeImageUrl: losePrizeImageUrl,
            prizeName: losePrizeDescription
              ? losePrizeDescription
              : this.$L('谢谢参与'),
            prizeId: -1
          })
        }
        return tmp
      }
    }
  },

  beforeDestroy() {
    this.rollOn = false
  },

  methods: {
    change(e) {
      this.current = e.detail.current + 1
    },
    open() {
      this.$refs.popInfo.open()
    },
    openShare() {},

    doDraw() {
      if (this.reactAcDetail.acState.flag != 1) {
        return
      }

      if (this.userInt.remainNum == 0) {
        this.$api.msg(this.$L('您的抽奖次数已用完'))
        return
      } else if (this.userInt.integral < this.getlotDetail.integralUse) {
        this.$api.msg(this.$L('您的抽奖积分不足'))
        return
      }

      if (this.rollOn) {
        return
      }
      this.rollOn = true

      this.$request({
        url: 'v3/promotion/front/draw/doDraw',
        method: 'POST',
        data: {
          drawId: this.drawId
        }
      }).then((res) => {
        if (res.state == 200) {
          this.lotResult = res.data
          if (this.lotResult.isPrize == 1) {
            this.finalIndex = this.prizeList.findIndex(
              (item) => item.prizeId == this.lotResult.prizeId
            )
          } else {
            this.finalIndex = this.prizeList.findIndex(
              (item) => item.prizeId == -1
            )
          }

          if (this.finalIndex < 0) {
            this.$api.msg(this.$L('奖项发生变化'))
            return
          }
          this.rollFn()
        } else {
          this.$api.msg(res.msg)
          this.rollOn = false
        }
      })
    },

    rollFn() {
      if (this.userInt.remainNum == 0) {
        this.$api.msg(this.$L('您的抽奖次数已用完'))
        return
      }

      this.rollOn = true
      this.countDownM++
      //startIndex是最后一个时一圈走完，重新开始
      if (this.countDownM >= 12) {
        this.countDownM = 0
        this.count++
      }
      // 当跑的圈数等于设置的圈数，且活动的index值是奖品的位置时停止
      if (
        this.count >= this.totalCount &&
        this.countDownM === this.finalIndex
      ) {
        this.count = 0
        this.speed = 80
        clearInterval(this.timer)
        this.rollOn = false
        this.$emit('res', 'complete', this.lotResult)
      } else {
        //重新开始一圈
        if (this.count >= this.totalCount - 1) {
          this.speed += 20
        }
        this.timer = setTimeout(() => {
          this.rollFn()
        }, this.speed)
      }
    }
  }
}
</script>

<style lang="scss">
.bg_style {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
}

.grid_table {
  .grid_table_con {
    height: 754rpx;
    position: relative;
    padding: 20rpx 20rpx 50rpx 20rpx;

    .grid_times {
      font-size: 28rpx;
      font-family: SourceHanSansCN;
      font-weight: bold;
      text-align: center;
      font-family: SourceHanSansCN;
      font-weight: 600;
      color: #ffffff;
      -webkit-text-stroke: 2rpx #fc1d1c;
      text-stroke: 2rpx #fc1d1c;
    }

    .rollOn::after {
      width: 144rpx;
      height: 144rpx;
      position: absolute;
      top: 0;
      right: 0;
      background-color: #000000;
      opacity: 0.2;
      z-index: 55;
      content: '';
      border-radius: 11px;
    }

    .grid_item {
      width: 146rpx;
      height: 146rpx;
      position: absolute;

      image {
        width: 54rpx;
        height: 54rpx;
        margin-bottom: 10rpx;
      }

      text {
        text-align: center;
        font-size: 20rpx;
        font-family: SourceHanSansCN;
        font-weight: 600;
        color: #fe6e1a;
		transform: scale(0.8);
      }

      &:nth-child(1),
      &:nth-child(2),
      &:nth-child(3),
      &:nth-child(4) {
        top: 46rpx;
      }

      &:nth-child(4),
      &:nth-child(5),
      &:nth-child(6),
      &:nth-child(7) {
        right: 66rpx;
      }

      &:nth-child(7),
      &:nth-child(8),
      &:nth-child(9),
      &:nth-child(10) {
        bottom: 56rpx;
      }

      &:nth-child(1),
      &:nth-child(12),
      &:nth-child(11),
      &:nth-child(10) {
        left: 66rpx;
      }

      &:nth-child(12),
      &:nth-child(5) {
        top: calc(146rpx + 48rpx);
      }

      &:nth-child(11),
      &:nth-child(6) {
        bottom: calc(146rpx + 56rpx);
      }

      &:nth-child(2),
      &:nth-child(9) {
        left: calc(66rpx + 146rpx);
      }

      &:nth-child(2),
      &:nth-child(9) {
        left: calc(66rpx + 146rpx);
      }

      &:nth-child(3),
      &:nth-child(8) {
        right: calc(66rpx + 146rpx);
      }
    }

    .grid_center {
      padding: 46rpx 66rpx;
      display: flex;
      flex-wrap: wrap;
      position: relative;
      height: 100%;
      width: 100%;
    }

    .grid_item_btn {
      position: absolute;
      left: 50%;
      top: 50%;
      transform-origin: center;
      transform: translate(-50%, -50%);
      width: 282rpx;
      height: 282rpx;

      &:active {
        transform: translate(-50%, -50%) scale(1.1, 1.1);
      }
    }

    .grid_desc {
      position: absolute;
      bottom: 30rpx;
      font-size: 22rpx;
      font-family: SourceHanSansCN;
      color: #bf4001;
      text-align: center;
      height: 50rpx;
      line-height: 50rpx;
    }
  }
}
</style>
