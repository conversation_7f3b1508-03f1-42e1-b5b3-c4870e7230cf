<template>
	<view class="store-container">
		<view class="store-list">
			<view class="store-item" v-for="store in store_list" :key="store.storeId" @tap="goShopHome(store.storeId)">
				<view class="store-logo">
					<image :src="store.storeLogoUrl" mode="aspectFill"></image>
					<view class="storeScale">
						<text v-if="store.storeScale == '品牌'" class="brand_1 tag">品牌</text>
						<text v-if="store.storeScale == '连锁'" class="brand_2 tag">连锁</text>
						<text v-if="store.storeScale == '自营'" class="brand_3 tag">自营</text>
					</view>
				</view>
				<view class="info_des">
					<view class="info_top">
						<view class="row-left">
							<text class="store-name">{{ store.storeName }}</text>
							<view class="store-rate">
								<text style="line-height: 1">{{ formatRate(store.serviceScore) }}</text>
								<uni-rate :readonly="true" :value="Number(store.serviceScore)" activeColor="#907449" size="12" margin="2"></uni-rate>
							</view>
						</view>
						<view class="row-right">
							<view class="store-tag">
								<block v-for="(tag, index) in store.storeItemList" :key="index">
									{{ index == 0 ? tag : '&nbsp;|&nbsp;' + tag }}
								</block>
							</view>
							<view class="store-sale" v-if="store.showOnly != 1">
								<text>已售：{{ store.orderFinishedCount }}</text>
							</view>
						</view>
					</view>
					<view class="info_bot">
						<view class="row-left">
							<view class="info_address">
								<view class="info_time">
									<text class="time_lable">营业时间</text>
									<text class="time_value">{{ store.startTime }}-{{ store.endTime }}</text>
								</view>
								<view>地址：{{ store.address }}</view>
							</view>
							<view class="info_service_type">
								<text class="tag" v-if="store.supportPickup">到店</text>
								<text class="tag" v-if="store.supportTakeaway">外卖</text>
								<text class="tag" v-if="store.supportMailing">快递</text>
								<text class="tag" v-if="store.supportTakeawayTwo">外送自提</text>
							</view>
						</view>
						<view class="row-right">
							<view class="">
								<text class="tag" v-if="store.businessState == 2">打烊</text>
								<text class="tag" v-else-if="store.showOnly == 1">仅展示</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="allow_loading_more flex_row_center_center" v-if="!noMore" @tap="getMoreData()">
				<text class="tip">加载中...</text>
			</view>
			<view class="no_more_data" v-else>暂无更多，看看别的~</view>
		</view>
	</view>
</template>

<script>
import { mapState } from 'vuex';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import uniRate from '@/components/uni-rate/uni-rate.vue';
export default {
	data() {
		return {
			imgUrl: getApp().globalData.imgUrl,
			store_list: [],
			// 店铺筛选
			noMore: false,
			queryParams: {
				internal:1,//只获取员工店铺
				current: 1,
				pageSize: 10,
				sort: 1 //排序条件：1-销量排序，2-人气排序，默认1
			}
		};
	},
	computed: {
		...mapState(['hasLogin', 'userCenterData']),
		client() {
			//app-2-start
			// #ifdef APP-PLUS
			return 'app';
			// #endif
			//app-2-end

			// #ifdef MP
			return 'mp';
			// #endif

			// #ifdef H5
			return 'h5';
			// #endif
		}
	},
	mounted() {
		// 判断员工身份
		if (this.userCenterData.isEmployee) {
			this.getStoreList();
		} else {
			this.$Router.replaceAll({
				path: '/pages/index/index'
			});
		}
	},
	methods: {
		//去店铺页面
		goShopHome(storeId) {
			this.$Router.push({
				path: '/standard/store/shopHomePage',
				query: {
					vid: storeId
				}
			});
		},
		// 获取员工店铺列表
		getStoreList() {
			if (this.noMore) return;
			let param = {};
			this.noMore = false;
			param.data = Object.assign({}, this.queryParams);
			param.url = '/v3/seller/front/store/list';
			param.method = 'GET';
			this.$request(param).then((res) => {
				if (res.state == 200) {
					// 当前页为第一页是 清空原数据
					if (res.data.pagination.current == 1) this.store_list = [];
					const list = res.data.list.map((e) => {
						e.storeItemList = e.storeItem ? e.storeItem.split(',') : [];
						return e;
					});
					this.store_list.push(...list);
					if (res.data.pagination.total <= res.data.pagination.pageSize * res.data.pagination.current) {
						this.noMore = true;
					} else {
						this.queryParams.current++;
					}
				}
				uni.hideLoading();
			});
		},
		// 格式化评分
		formatRate(val) {
			if (String(val).indexOf('.') != -1) {
				return val;
			} else {
				return val + '.0';
			}
		},
		getMoreData() {
			if (this.noMore) return;
			this.queryParams.current++;
			uni.showLoading();
			this.getStoreList();
		}
	},
	// 页面上拉触底
	onReachBottom() {
		this.getStoreList();
	}
};
</script>

<style lang="scss">
.store-container {
	padding-bottom: 50rpx; //底部安全距离
}
.flex_row_center_center {
	height: 70rpx;
}
// 分类弹窗
.category-content {
	width: 92vw;
	height: auto;
	background-color: #fff;
	border-radius: 20rpx;
	overflow: hidden;
	.filter-title {
		padding-top: 30rpx;
		font-size: $fs-base;
		color: #0f5c7b;
		text-align: center;
	}
	.store-fillter {
		box-sizing: border-box;
		background-color: #fff;
		padding: 50rpx;
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		gap: 28rpx;
		.cg-item {
			width: 30%;
			text-align: center;
			padding: 10rpx 20rpx;
			font-size: 28rpx;
			background-color: #f5f7fa;
			color: #000;
			display: inline-block;
			border-radius: 10rpx;
			&.current {
				background: linear-gradient(270deg, rgba(55, 223, 226, 1) 0%, rgba(54, 131, 254, 1) 100%);
				color: #fff;
			}
		}
	}
	.pop-bottom {
		display: flex;
		align-items: center;
		border-top: 1px solid #f1f1f1;
		.button {
			height: 100rpx;
			line-height: 100rpx;
			width: 50%;
			text-align: center;
			font-size: $fs-base;
			&:first-child {
				border-right: 1px solid #f1f1f1;
			}
			&.confirm {
				color: $color1;
			}
		}
	}
}
.store-category {
	overflow-x: scroll;
	width: 100%;
	margin: 38rpx 0 29rpx 0;
	padding: 0 4%;
	white-space: nowrap;
	display: flex;
	align-items: center;
	::-webkit-scrollbar {
		width: 0;
	}
	.tag-item {
		display: inline-block;
	}
	.big-tag {
		display: inline-block;
		margin-right: 20rpx;
		padding: 0rpx 20rpx;
		font-size: $fs-m;
		color: rgba(15, 92, 123, 1);
		background-color: #fff;
		border-radius: 8rpx;
		gap: 6rpx;
		.tag-icon {
			width: 23rpx;
			height: 23rpx;
		}
		&.selected {
			color: #fff;
			background: linear-gradient(90deg, rgba(54, 127, 255, 1) 0%, rgba(55, 237, 222, 1) 100%), rgba(255, 255, 255, 1);
		}
	}
}
.store-category-search {
	width: 100%;
	padding: 35rpx 0;
	// margin-top: 20rpx;
	background-color: #fcfcfc;
	border-radius: 20rpx;
	overflow: hidden;
	.store-main {
		width: 100%;
		margin: 0 auto;
	}
	.navbar {
		display: flex;
		align-items: center;
		gap: 30rpx;
		width: 90%;
		margin: 0 auto;
		.nav-item {
			color: #0f5c7b;
			font-size: $fs-m;
			// width: 150rpx;
			display: flex;
			align-items: center;
			gap: 4px;
			line-height: 2em;
			padding: 0 20rpx;
		}
		.iconfont {
			font-size: 19rpx;
			transform: rotateZ(90deg);
			margin-top: 6rpx;
		}
	}
}
.store-list {
	min-height: 30vh;
	margin-top: 30rpx;
	.store-item {
		padding: 15rpx;
		width: 92%;
		margin: 0 auto;
		display: flex;
		border-radius: 20rpx;
		background-color: #fff;
		box-shadow: 0px 2px 12px rgba(0, 0, 0, 0.08);
		align-items: center;
		margin-bottom: 20rpx;
		.store-logo {
			height: 192rpx;
			width: 192rpx;
			border-radius: 20rpx;
			overflow: hidden;
			position: relative;
			image {
				width: 100%;
				height: 100%;
			}
			.storeScale {
				position: absolute;
				top: 0;
				left: 0;
				.tag {
					font-size: 18rpx;
					color: #fff;
					height: 40rpx;
					border-radius: 20rpx;
					width: 60rpx;
					text-align: center;
					line-height: 40rpx;
					display: block;
				}
				.brand_1 {
					background-color: #ff5733;
				}
				.brand_2 {
					background-color: #d4aa61;
				}
				.brand_3 {
					background-color: #40c1ce;
				}
			}
		}
		.info_des {
			width: calc(100% - 192rpx);
			padding-left: 20rpx;
			.info_top {
				display: flex;
				// align-items: flex-start;
				align-items: center;
				justify-content: space-between;
				.row-left {
					width: 60%;
				}
				.row-right {
					width: 36%;
				}
				.store-name {
					display: inline-block;
					width: 100%;
					color: #0f5c7b;
					font-size: 30rpx;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
				.store-rate {
					font-size: $fs-m;
					font-weight: 700;
					display: flex;
					align-items: center;
					gap: 10rpx;
					color: #907449;
					margin-top: 6rpx;
				}
				.store-tag {
					width: 100%;
					font-size: 20rpx;
					font-weight: 700;
					line-height: 1;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
					.tag {
						display: inline-block;
						padding: 0 10rpx;
						&:not(:first-child) {
							border-left: 1px solid #0f5c7b;
						}
						&:first-child {
							padding: 0;
							padding-right: 10rpx;
						}
						&:last-child {
							padding: 0;
							padding-left: 10rpx;
						}
					}

					color: #0f5c7b;
					text-align: right;
				}
				.store-sale {
					margin-top: 10rpx;
					font-size: 20rpx;
					color: #907449;
					text-align: right;
				}
			}
			.info_bot {
				// height: 120rpx;
				padding-top: 20rpx;
				display: flex;
				align-items: flex-end;
				justify-content: space-between;
				.row-left {
					height: 100%;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					flex: 0.7;
				}
				.row-right {
					display: flex;
					height: 100%;
					flex-direction: column;
					align-items: flex-end;
					.tag {
						font-size: $fs-m;
						color: #0f5c7b;
					}
				}
				.info_address {
					font-size: 20rpx;
					color: #0f5c7b;
					margin-bottom: 4px;
					.info_time {
						display: flex;
						align-content: center;
						gap: 8rpx;
						margin-bottom: 6rpx;
						.time_lable {
							background: linear-gradient(90deg, #367fff 0%, #37edde 100%), #deffeb;
							color: #fff;
							border-radius: 8rpx;
							text-align: center;
							height: 30rpx;
							line-height: 30rpx;
							width: 100rpx;
						}
						.time_value {
							height: 30rpx;
							line-height: 30rpx;
						}
					}
				}
				.info_service_type {
					font-size: 20rpx;
					color: #0f5c7b;
					font-weight: 700;
					line-height: 1em;
					.tag {
						display: inline-block;
						padding: 0 10rpx;
						&:not(:first-child) {
							border-left: 1px solid #0f5c7b;
						}
						&:last-child {
							padding: 0;
							padding-left: 10rpx;
						}
						&:first-child {
							padding: 0;
							padding-right: 10rpx;
						}
					}
				}
			}
		}
	}
}
.show-dialog {
	animation: 100ms showDialog linear forwards;
}

.hide-dialog {
	animation: 100ms hideDialog linear forwards;
}

@keyframes hideDialog {
	0% {
		opacity: 1;
	}

	25% {
		opacity: 0.75;
	}

	50% {
		opacity: 0.5;
	}

	75% {
		opacity: 0.25;
	}

	100% {
		opacity: 0;
	}
}

@keyframes showDialog {
	0% {
		opacity: 0;
	}

	25% {
		opacity: 0.25;
	}

	50% {
		opacity: 0.5;
	}

	75% {
		opacity: 0.75;
	}

	100% {
		opacity: 1;
	}
}
.allow_loading_more {
	height: 80rpx;
	.iconfont {
		color: #bbb;
		font-size: 19rpx;
		transform: rotateZ(90deg);
		margin-top: 6rpx;
	}
	.allow_loading_icon {
		width: 30rpx;
		height: 30rpx;
		margin-right: 12rpx;
		color: var(--color_main);
	}

	.tip {
		color: #bbb;
		font-size: 24rpx;
	}

	.loading_more_icon {
		width: 46rpx;
		height: 46rpx;
	}
}
.no_more_data {
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	height: 80rpx;
	text-align: center;
	line-height: 80rpx;
	color: #bbb;
	font-size: 24rpx;
	width: 750rpx;
}
</style>
