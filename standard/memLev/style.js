import {
	set16ToRgb
} from '@/diyStyle/index.js'
import store from '@/store/index.js'
export default {
	computed:{
		mix_level_style(){
			const diyStyle = this.diyStyle_var
			const colorMainDiy = (op) => set16ToRgb(diyStyle['--color_main'], op)
			const member_level_colorSet = {
				'--level_fade_btn': `linear-gradient(90deg, ${colorMainDiy(0.65)} 0%, ${colorMainDiy(0.65)} 0%, ${colorMainDiy(0.85)} 100%)`,
				'--level_line': colorMainDiy(0.7),
				'--level_halo': colorMainDiy(0.08),
				'--level_halo_2': colorMainDiy(0.06),
				'--level_bg': `linear-gradient(90deg, ${colorMainDiy(0.6)} 0%, ${colorMainDiy(0.8)} 100%)`,
				'--level_border': colorMainDiy(0.7),
				'--level_circle': colorMainDiy(0.99),
				'--level_circle_shadow': colorMainDiy(0.26),
				'--level_eq_dot': colorMainDiy(0.8),
				'--level_eq_dot_halo': colorMainDiy(0.6),
				'--color_main':colorMainDiy(1),
				'--color_price':diyStyle['--color_price'],
			}
			let style_s = Object.keys(member_level_colorSet).map(key => `${key}:${member_level_colorSet[key]};`)
			return style_s.join('')
		}
	},
}