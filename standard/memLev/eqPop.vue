<template>
	<view class="equity_pop">
		<uni-popup type="center" ref="equity">
			<view class="eq_d">
				<view class="eq_d_title">
					<text>权益详情</text>
				</view>
				<view class="eq_dD flex_column_start_start">
					<view class="cp_bg flex_row_center_center" :style="{backgroundImage:'url('+imgUrl1+'cp_bg.png)'}">
						<view class="cp_type">
							{{curCoupon.couponTypeValue}}
						</view>
						<view class="cp_left flex_column_center_center">
							<view class="price_c" v-if="curCoupon.couponType!==4">
								<text class="p_s" v-if="curCoupon.couponType!=2">￥</text>
								<text class="p_b">{{curCoupon.publishValue||curCoupon.randomMax}}</text>
								<text class="p_s" v-if="curCoupon.couponType==2">%</text>
							</view>
							<view class="price_c" v-else>
								<text class="p_b cp_name">{{curCoupon.couponName}}</text>
							</view>
							<text class="cp_desc">{{curCoupon.couponContent}}</text>
						</view>
						<view class="cp_right flex_column_center_center">
							<view class="desc_1">{{curCoupon.description}}</view>
							<view class="desc_2">{{curCoupon.effectiveStart}}-{{curCoupon.effectiveEnd}}</view>
						</view>
					</view>
					<view class="cp_bg_copied" :style="{backgroundImage:'url('+imgUrl1+'cp_bg.png)'}"></view>
					<view class="desc_p">
						共{{curCpLength}}张，领取后可去我的优惠券查看
					</view>
				</view>
				<view class="eq_d_but_con flex_row_center_center">
					<view class="eq_d_but" @click="$refs.equity.close()">确定</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import uniPopup from '@/components/uni-popup/uni-popup.vue'
	export default {
		props: {
			data_cp: {
				type: Object,
				default: () => {}
			},

			cpLen: {
				type: Number,
				default: 0
			}
		},

		watch: {
			
			data_cp:{
				handler(val) {
					this.curCoupon = new Proxy(val, {
						get(target, prop) {
							if((prop=='effectiveStart'||prop=='effectiveEnd')&&target[prop]){
								let res = target[prop].split(':')
								res.pop()
								return res.join(':')
							}
							return target[prop];
						}
					})
				},
				deep:true
			},
			cpLen(val) {
				this.curCpLength = val
			}
		},
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				imgUrl1: getApp().globalData.imgUrl + 'member_level/',
				curCoupon: {},
				curCpLength: 0
			}
		},
		mounted() {
			this.curCoupon = this.data_cp
			
			this.curCpLength = this.cpLen
		},
		methods: {
			open() {
				this.$refs.equity.open()
			}
		}
	}
</script>

<style lang="scss">
	.eq_d {
		width: 590rpx;
		height: 500rpx;
		background: #FFFFFF;
		box-shadow: 0px 0px 27rpx 6rpx rgba(85, 85, 85, 0.3);
		border-radius: 20rpx;
		padding-top: 26rpx;
		padding-left: 36rpx;
		padding-right: 36rpx;
		position: relative;

		.eq_d_title {
			text-align: center;
			font-size: 36rpx;
			font-family: PingFang;
			font-weight: 500;
			color: #000000;
			position: relative;

			image {
				position: absolute;
				right: 0px;
				width: 46rpx;
				height: 46rpx;
			}
		}

		.eq_dD {
			margin-top: 45rpx;
			position: relative;

			.cp_bg {
				width: 514rpx;
				height: 192rpx;
				background-position: center center;
				background-repeat: no-repeat;
				background-size: cover;
				z-index: 4;
				position: relative;
				.cp_left {
					width: 189rpx;

					.price_c {
						color: #604E44;

						.p_s {
							font-size: 30rpx;
							font-family: PingFang SC;
							font-weight: bold;
						}

						.p_b {
							font-size: 70rpx;
							font-family: Source Han Sans CN;
							font-weight: 800;
							&.cp_name{
								display: inline-block;
								width: 200rpx;
								font-size: 30rpx;
								overflow: hidden;
								display: -webkit-box;
								-webkit-box-orient: vertical;
								-webkit-line-clamp: 2;
								text-overflow: ellipsis;
								word-break: break-all;
								text-align: center;
								margin-bottom: 10rpx;
							}
						}


					}

					.cp_desc {
						font-size: 22rpx;
						font-family: Source Han Sans CN;
						font-weight: 400;
						text-align: center;
					}
				}

				.cp_right {
					width: 324rpx;

					.desc_1 {
						font-size: 34rpx;
						font-family: Source Han Sans CN;
						font-weight: 500;
						color: #604E44;
						overflow: hidden;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 2;
						text-overflow: ellipsis;
					}

					.desc_2 {
						margin-top: 8rpx;
						font-size: 23rpx;
						font-family: Source Han Sans CN;
						font-weight: 400;
						color: #604E44;
						white-space: nowrap;
						transform: scale(0.75);
					}
				}
				
				.cp_type{
					position: absolute;
					top: 0;
					left: 0;
					height: 36rpx;
					padding: 0 10rpx;
					background: var(--level_bg);
					border-radius: 9rpx 0px 9rpx 0px;
					line-height: 36rpx;
					font-size: 22rpx;
					font-weight: 400;
					color: #FFF;
				}

			}

			.cp_bg_copied {
				width: 514rpx;
				height: 192rpx;
				background-position: center center;
				background-repeat: no-repeat;
				background-size: cover;
				position: absolute;
				left: 10rpx;
				top: 6rpx;
			}


			.desc_p {
				margin-top: 10rpx;
				font-size: 26rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #999999;
			}
		}


		.eq_d_but {
			margin-top: 50rpx;
			width: 180rpx;
			height: 66rpx;
			background: var(--level_bg);
			border-radius: 33rpx;
			line-height: 66rpx;
			text-align: center;
			color: #fff;
		}
	}

	// asd
</style>
