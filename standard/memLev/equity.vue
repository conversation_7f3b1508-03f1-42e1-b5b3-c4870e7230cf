<template>
	<view class="all_equity" :style="mix_level_style">
		<view class="">
			
		<view class="eq_item" v-for="(item,index) in lvData" :key="index">
			<view class="circle_icon_wrap">
				<!-- <view class="dots"></view> -->
				<!-- <image :src="imgUrl1+'dots.png'" mode="aspectFit" class="dots"></image> -->
				<view class="eq_dot_xs">
					
				</view>
				
				<view class="progress_line"></view>
			</view>
			<view class="progress_text">
				<view class="eq_text flex_row_start_center">
					<view class="text_1">{{item.levelName}}</view>
					<view class="text_2">成长值达到{{item.growthValue}}</view>
				</view>
				<view class="cur_lv" v-if="cur_lvId==item.levelId">
					<text>当前成长值</text>
					<text class="text_0">1300</text>
				</view>
				<view class="eq_list flex_row_between_center">
					<view class="eq_item flex_column_center_center" @click="open_cp(item,'couponList')" v-if="item.couponEnable==1">
						<!-- <image :src="imgUrl1+'coupon.png'" mode="aspectFit"></image> -->
						<view class="eq_icon">
							<text class="iconfont iconyouhuiquan1"></text>
						</view>
						<text class="eq_item_text">优惠券</text>
					</view>
					<view class="eq_item flex_column_center_center" @click="open_cp(item,'freightCouponList')" v-if="item.freightCouponEnable==1">
						<!-- <image :src="imgUrl1+'coupon_fee.png'" mode="aspectFit"></image> -->
						<view class="eq_icon">
							<text class="iconfont iconyunfeiquan"></text>
						</view>
						<text class="eq_item_text">运费券</text>
					</view>
					<view class="eq_item flex_column_center_center" v-if="item.integralMultipleEnable==1">
						<!-- <image :src="imgUrl1+'integral.png'" mode="aspectFit"></image> -->
						<view class="eq_icon">
							<text class="iconfont iconjifen"></text>
						</view>
						<text class="eq_item_text">{{item.integralMultipleValue}}倍积分</text>
					</view>
				</view>
			</view>
		</view>
		</view>
		<eqPop ref="eqPop" :data_cp="ch_cp"  :cpLen="chCp_len"></eqPop>
	</view>
</template>

<script>
	import eqPop from './eqPop.vue'
	import style from './style'
	export default{
		mixins:[style],
		components:{
			eqPop
		},
		data(){
			return{
				imgUrl1:getApp().globalData.imgUrl+'member_level/',
				lvData:[],
				cur_lvId:0,
				ch_cp:{},
				chCp_len:0,
			}
		},
		
		onLoad() {
			this.cur_lvId = this.$Route.query.cur_lvId
			this.getData()
		},
		
		methods:{
			getData() {
				this.$request({
					url: 'v3/member/front/memberLevel/levelList',
				}).then(res => {
					if (res.state == 200) {
						this.lvData = res.data.sort((a,b)=>a.level-b.level)
					}
				})
			},
			
			open_cp(obj,prop){
				this.ch_cp = obj[prop][0]
				this.chCp_len = obj[prop].length
				this.$refs.eqPop.open()
			},
		}
	}
</script>

<style lang="scss">
	
	.eq_dot_xs {
	  width: 32rpx;
	  min-height: 32rpx;
	  background: var(--level_circle_shadow);
	  position: relative;
	  border-radius: 50%;
	
	  &::before {
	    content: '';
	    width: 20rpx;
	    height: 20rpx;
	    border-radius: 50%;
	    background-color: var(--level_line);
	    position: absolute;
	    left: 50%;
	    top: 50%;
	    transform: translate(-12rpx, -10rpx);
	  }
	}
	
	.eq_icon{
		width: 90rpx;
		height: 90rpx;
		border-radius: 50%;
		background: var(--level_halo);
		display: flex;
		align-items: center;
		justify-content: center;
		text{
			font-size: 55rpx;
			color: var(--level_border);
		}
	}
	
	
	.all_equity{
		padding: 0 20rpx;
		padding-top: 50rpx;
		.eq_item{
			display: flex;
			align-items: flex-start;
			position: relative;
			margin-top: -2rpx;
			
			&:last-child{
				.circle_icon_wrap{
					justify-content: flex-start;
					.progress_line{
						display: none;
					}
					
				} 
			}
			
			.circle_icon_wrap {
				width: 40rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				margin-right: 37rpx;
				position: absolute;
				top: 0;
				height: 100%;
			
				.dots {
					width: 32rpx;
					height: 32rpx;
					border-radius: 50%;
					// background: #FE6847;
					
				}
				.progress_line {
					height: 100%;
					width: 2rpx;
					margin-top: -2rpx;
					background: var(--level_line);
					margin-right: 0rpx;
				}
			}
		
			.progress_text {
				white-space: pre-wrap;
				font-size: 26rpx;
				color: #999999;
				font-weight: 500;
				margin-left: 70rpx;
				padding-bottom: 70rpx;
				.eq_text{
					.text_1{
						font-size: 32rpx;
						font-family: PingFang SC;
						font-weight: bold;
						color: #000000;
					}
					
					.text_2{
						margin-left: 20rpx;
						font-size: 26rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #666666;
					}
				}
				
				.cur_lv{
					font-size: 26rpx;
					font-family: PingFang SC;
					margin-top: 20rpx;
					.text_0{
						color: #FE6E40;
					}
				}
				
				.eq_list {
					margin-top: 36rpx;
				
					.eq_item {
						margin-right: 90rpx;
						&:last-child{
							margin-right: 0rpx;
						}
						image {
							width: 90rpx;
							height: 90rpx;
						}
				
						.eq_item_text {
							margin-top: 20rpx;
							font-size: 24rpx;
							font-family: PingFang SC;
							font-weight: 500;
							color: #505050;
						}
					}
				}
			}
		
		}
		
	}
</style>