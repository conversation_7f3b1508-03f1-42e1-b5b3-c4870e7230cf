<!-- 购物车 -->
<template>
    <view class="page" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }">
        <ktabbar :bgImg="imgUrl + 'default_bg.jpg'" :title="'购物车'" />
        <view @click="closeMask" class="container">
            <view class="cart_header" v-if="tempList && tempList.storeCartGroupList && tempList.storeCartGroupList.length > 0">
                <view class="cart_header_btn" @click="manageCart">{{ topRightText }}</view>
            </view>
            <!-- 空白页 start-->
            <view
                v-if="empty && tempList.invalidList.length == 0 && tempList && tempList.storeCartGroupList && tempList.storeCartGroupList.length == 0"
                class="flex_column_start_center empty_part"
            >
                <image class="img" :src="imgUrl + 'no_cart.png'" />
                <text class="tip_con">{{ $L('这里空空如也~快去商品中心加购商品吧！') }}</text>
                <view class="ope_btn flex_row_center_center" @click="goGoodsList()">
                    {{ $L('马上去逛逛') }}
                </view>
            </view>
            <!-- 空白页 end-->
            <view v-if="tempList.storeCartGroupList">
                <view class="cart-list">
                    <view class="shop_list_wrap">
                        <view class="shop_item" v-for="(item1, index1) in tempList.storeCartGroupList" :key="index1">
                            <view class="shop_item_top">
                                <text
                                    :class="{ item_check: true, iconfont: true, iconziyuan33: item1.checkedAll && !item1.lackAll }"
                                    @click="changeSelectState('shop', item1.storeId, item1.checkedAll)"
                                ></text>
                                <text
                                    :class="{ iconfont: true, iconziyuan43: !item1.checkedAll && !item1.lackAll }"
                                    @click="changeSelectState('shop', item1.storeId, item1.checkedAll)"
                                ></text>
                                <text :class="{ stock_not_icon: true, iconfont: true, iconziyuan43: item1.lackAll }"></text>
                                <view style="display: flex; align-items: center" @click.stop="toShopDetail(item1.storeId)">
                                    <image :src="imgUrl + 'shop/shop_icon.png'" mode="aspectFit" class="shop_icon"></image>
                                    <view class="shop_name">{{ item1.storeName }}</view>
                                    <image :src="imgUrl + 'shop/to_right.png'" mode="aspectFit" class="to_right_icon"></image>
                                </view>
                                <!-- <image :src="imgUrl + 'shop/coupon.png'" mode="aspectFit" class="coupon_icon"
									@click="getShopCoupon(item1.storeId)" v-if="!editFlag && item1.hasCoupon"></image> -->
                                <view class="coupon_icon" v-if="!editFlag && item1.hasCoupon" @click="getShopCoupon(item1.storeId)">{{ $L('领券') }}</view>
                            </view>
                            <!-- 购物车列表 -->
                            <view v-for="(item2, index2) in item1.promotionCartGroupList" :key="index2">
                                <view class="discount_left" v-if="item2.promotionDes">
                                    <view class="discount_icon">{{ $L('满减') }}</view>
                                    <view class="promotion_text">
                                        <view class="discount_text">
                                            <jyfParser :html="item2.promotionDes" :isAll="true"></jyfParser>
                                        </view>
                                    </view>
                                </view>
                                <view class="discount_activity" v-for="(item, index) in item2.cartList" :key="index">
                                    <!-- 换促销活动 -->
                                    <!-- <view class="discount_activity_wrap" v-if="!editFlag && item.promotionList && item.promotionList.length>0">
									<view class="discount_left">
										<view class="discount_icon">满减</view>
										<view class="promotion_text">
											<view class="discount_text"></view>
											<view class="discount_text">{{item2.promotionDes}}</view>
										</view>
									</view>
									<view class="discount_right" @click="showPromotionBox(item.cartId,item.productImage,item.productPrice,item.specValues,item.promotionList,item.promotionId)">
										<text>换促销</text>
										<image :src="imgUrl+'shop/to_right.png'" mode="aspectFit" class="see_more"></image>
									</view>
								</view> -->
                                    <view class="cart-item" @longpress="showOperate(item.cartId, item.productId)">
                                        <!-- 长按商品出现蒙层及操作 start -->
                                        <view
                                            v-show="(curOperateId == item.cartId && is_show_mask == true) || (curOperateId == item.productId && is_show_mask == true)"
                                            class="mask flex_row_center_center"
                                        >
                                            <view class="move flex_row_center_center" @click.stop="operateCartGoods('move', item.cartId)" v-if="item.isShowCollectBtn == true">
                                                {{ $L('移入收藏夹') }}
                                            </view>
                                            <view
                                                class="del flex_row_center_center"
                                                @click.stop="operateCartGoods('del', item.cartId, item.productId)"
                                                :style="{ marginLeft: item.isShowCollectBtn == true ? '90rpx' : '0' }"
                                            >
                                                {{ $L('删除商品') }}
                                            </view>
                                        </view>
                                        <!-- 长按商品出现蒙层及操作 end -->
                                        <view class="image-wrapper flex_row_start_center">
                                            <div
                                                @click="changeSelectState('goods', item.cartId, item.isChecked, item.goodsId, item.productId)"
                                                style="height: 100%; display: flex; align-items: center"
                                            >
                                                <text :class="{ item_check: true, iconfont: true, iconziyuan33: item.isChecked && item.productState != 3 }"></text>

                                                <text
                                                    :class="{
                                                        iconfont: true,
                                                        iconziyuan43: !item.isChecked && item.productState != 3
                                                    }"
                                                ></text>
                                            </div>
                                            <!-- <text :class="{stock_not_icon:true,iconfont:true, iconziyuan43:item.productState==3}"></text> -->
                                            <text :class="{ stock_not_icon: true, iconfont: true, iconziyuan43: item.productState == 3 }"></text>
                                            <view
                                                class="goods-img"
                                                :style="{ backgroundImage: 'url(' + item.productImage + ')' }"
                                                @click.stop="goGoodsDetail(item.productId, item.goodsId)"
                                            ></view>
                                        </view>

                                        <view class="item-right">
                                            <view class="right_top" @click.stop="goGoodsDetail(item.productId, item.goodsId)">
                                                <text :class="item.productState == 3 ? 'title stock_not_enough' : 'title'">{{ item.goodsName }}</text>
                                                <text class="attr" v-if="item.specInfo">{{ item.specInfo }}</text>
                                            </view>
                                            <view class="goods_spec">{{ item.specValues }}</view>
                                            <view class="right_bottom flex_row_between_center">
                                                <view class="right_bottom_left">
                                                    <view
                                                        class="trade_promotion_btn"
                                                        v-if="!editFlag && item.promotionList && item.promotionList.length > 0"
                                                        @click="
                                                            showPromotionBox(
                                                                item.cartId,
                                                                item.productImage,
                                                                item.productPrice,
                                                                item.specValues,
                                                                item.promotionList,
                                                                item.promotionId,
                                                                item.promotionType
                                                            )
                                                        "
                                                    >
                                                        <text>{{ $L('换促销') }}</text>
                                                        <image :src="imgUrl + 'arrow_bottom.png'" mode="aspectFit"></image>
                                                    </view>
                                                    <view class="price_wrap" :class="{ price_wrap_hide: item.isShowBigNumBox == false, price_wrap_super: item.isSupper }">
                                                        <text class="unit">¥</text>
                                                        <text class="price_int">{{ item.isShowCloseBtn == true ? '' : $getPartNumber(item.productPrice, 'int') }}</text>
                                                        <text class="price_decimal">
                                                            {{ item.isShowCloseBtn == true ? '' : $getPartNumber(item.productPrice, 'decimal') }}
                                                            <text
                                                                class="price_super_img"
                                                                :style="'background-image:url(' + imgUrl + 'super/super_price.png)'"
                                                                v-if="item.isSupper && parseInt(item.productPrice) < 10"
                                                            >
                                                                会员价
                                                            </text>
                                                        </text>
                                                    </view>
                                                </view>
                                                <view :class="item.productState == 3 ? 'goods_num_wrap' : 'exceed_price_wrap'">
                                                    <view class="goods_num_box" v-if="item.isShowBigNumBox == true && !editFlag" @click="changeBuyNum(item.cartId, item.productId)">
                                                        *{{ item.buyNum }}
                                                    </view>
                                                    <block v-if="item.isShowBigNumBox == false && !editFlag">
                                                        <view class="uni-numbox">
                                                            <view
                                                                @click="_calcValue('minus', item, item.cartId, item.productId, item.productStock, item.productPrice)"
                                                                class="uni-numbox__minus"
                                                            >
                                                                <text class="uni-numbox--text" :class="{ 'uni-numbox--disabled': item.buyNum <= 1 }">-</text>
                                                            </view>
                                                            <input
                                                                @blur="_calcValue('blur', item, item.cartId, item.productId, item.productStock, item.productPrice)"
                                                                class="uni-numbox__value"
                                                                type="number"
                                                                v-model="item.buyNum"
                                                                maxlength="5"
                                                                @input="restictInput(item)"
                                                            />
                                                            <view
                                                                @click="_calcValue('plus', item, item.cartId, item.productId, item.productStock, item.productPrice)"
                                                                class="uni-numbox__plus"
                                                            >
                                                                <text
                                                                    class="uni-numbox--text"
                                                                    :class="{ 'uni-numbox--disabled': item.buyNum >= Math.min(item.productStock, 99999) }"
                                                                >
                                                                    +
                                                                </text>
                                                            </view>
                                                        </view>
                                                    </block>
                                                    <block v-if="item.isShowCloseBtn == true && !editFlag">
                                                        <image :src="closeImg" mode="aspectFit" class="close_img" @click="closeBigPriceBox(item.cartId, item.productId)"></image>
                                                    </block>

                                                    <view class="stock_not" v-if="item.productState == 3 && !editFlag">
                                                        <!-- {{$L('还剩')}}{{item.productStock}}{{$L('件')}} -->
                                                        {{ $L('库存不足') }}
                                                    </view>
                                                </view>
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>

                    <!-- 失效购物车列表 -->
                    <view class="invalid_list_wrap" v-if="tempList.invalidList.length > 0">
                        <view class="invalid_list_title">
                            <text>{{ $L('失效商品') }}{{ tempList.invalidList.length }}{{ $L('件') }}</text>
                            <text @click="clearFailureGoods('open')">{{ $L('清空失效商品') }}</text>
                        </view>

                        <view class="invalid_list_content">
                            <view class="invalid_list_item" v-for="(item2, index2) in tempList.invalidList" :key="index2">
                                <view class="invalid_icon">{{ $L('失效') }}</view>
                                <view class="invalid_img" @click.stop="goGoodsDetail(item2.productId, item2.goodsId)">
                                    <image :src="item2.productImage" mode="aspectFit"></image>
                                </view>
                                <view class="invalid_goods_wrap">
                                    <view class="invalid_goods_name" @click.stop="goGoodsDetail(item2.productId, item2.goodsId)">
                                        {{ item2.goodsName }}
                                    </view>
                                    <text class="invalid_goods_spec" v-if="item2.specValues">{{ item2.specValues }}</text>
                                    <!-- <view class="invalid_goods_text">{{$L('商品已下架')}}</view> -->
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                <!-- 底部菜单栏 -->
                <view class="action-section flex_row_between_center" v-if="tempList.storeCartGroupList && tempList.storeCartGroupList.length > 0">
                    <view class="checkbox flex_row_start_center" v-if="currentIndex != 0 || editFlag">
                        <text :class="{ item_check: true, iconfont: true, iconziyuan33: tempList.checkedAll }" @click="check(tempList.checkedAll)"></text>
                        <text :class="{ iconfont: true, iconziyuan43: !tempList.checkedAll }" @click="check(tempList.checkedAll)"></text>
                        <text class="check_all_tit">{{ $L('全选') }}</text>
                    </view>
                    <!-- 去结算样式 start -->
                    <template v-if="!editFlag">
                        <view class="total-box flex_row_start_center">
                            <text>{{ $L('合计：') }}</text>
                            <view class="price_wrap">
                                <text class="unit">¥</text>
                                <text class="price_int">{{ $getPartNumber(tempList.totalAmount, 'int') }}</text>
                                <text class="price_decimal">{{ $getPartNumber(tempList.totalAmount, 'decimal') }}</text>
                            </view>
                        </view>
                        <button class="no-border confirm-btn flex_row_center_center" @click="createOrder">
                            {{ $L('结算') }}
                            <text class="settle_num">({{ tempList.totalCheck }})</text>
                        </button>
                    </template>
                    <!-- 去结算样式 end -->
                    <!-- 点击管理之后的样式 start -->
                    <template v-if="editFlag">
                        <view class="flex_row_end_center">
                            <view class="del_more flex_row_center_center" @click="batchDelete('open')">{{ $L('删除所选') }}</view>
                        </view>
                    </template>
                    <!-- 点击管理之后的样式 end -->
                </view>
            </view>
            <!-- 换促销弹框 -->
            <uni-popup ref="popup" type="bottom" @touchmove.stop.prevent="moveHandle">
                <view class="promotion_box">
                    <block v-for="(item, index) in promotion_goods_info" :key="index">
                        <view class="promotion_goods_wrap">
                            <view class="promotion_goods_img_wrap">
                                <image :src="item.productImage" mode="aspectFit" class="promotion_goods_img"></image>
                            </view>
                            <view class="promotion_goods_right">
                                <view class="promotion_goods_price">
                                    <view class="promotion_goods_price_now">
                                        <text class="small_price">￥</text>
                                        <text class="big_price">{{ $getPartNumber(item.productPrice, 'int') }}</text>
                                        <text class="small_price">{{ $getPartNumber(item.productPrice, 'decimal') }}</text>
                                    </view>
                                    <!-- <view class="promotion_goods_price_old">￥866.66</view> -->
                                </view>

                                <view class="promotion_goods_spec">{{ $L('已选：') }}{{ item.specValues == '' ? $L('默认规格') : item.specValues }}</view>
                                <image :src="imgUrl + 'close2.png'" mode="aspectFit" class="close_icon" @click="closePromotionBox"></image>
                            </view>
                        </view>
                        <scroll-view class="promotion_rules_wrap" scroll-y="true">
                            <view class="promotion_rules_title">{{ $L('修改促销') }}</view>
                            <view
                                class="promotion_rule_item"
                                v-for="(item2, index2) in promotion_list"
                                :key="index2"
                                @click="changePromotion(index2, item2.promotionDes, item2.goodsPromotionId, item2.promotionType, item2.promotionId)"
                            >
                                <text :class="{ item_check: true, iconfont: true, iconziyuan33: true }" v-if="goodsPromotionId == item2.goodsPromotionId"></text>
                                <text :class="{ iconfont: true, iconziyuan43: true }" v-else></text>
                                <view class="promotion_text">
                                    <jyfParser :html="item2.promotionDes" :isAll="true"></jyfParser>
                                </view>
                            </view>
                        </scroll-view>
                    </block>
                    <view class="confirm_btn_wrap">
                        <view class="confirm_btn" @click="confirmChangePromotion">{{ $L('确定') }}</view>
                    </view>
                </view>
            </uni-popup>

            <!-- 优惠券弹框 start -->
            <uni-popup ref="couponPopup" type="bottom" @touchmove.stop.prevent="moveHandle">
                <view class="coupon_model">
                    <view class="coupon_model_title">
                        <text>{{ $L('领取优惠券') }}</text>
                        <image :src="imgUrl + 'goods_detail/close.png'" mode="aspectFit" @click="closeCouponBox"></image>
                    </view>
                    <!-- 有优惠券 -->
                    <scroll-view class="coupon_model_list" scroll-y="true" v-if="coupon_list.length > 0">
                        <view class="my_coupon_pre" v-for="(item, index) in coupon_list" :key="index">
                            <view class="coupon_pre_top">
                                <image :src="imgUrl + 'coupon/coupon_pre_img_bg.png'" mode="" class="coupon_pre_top_bg_img"></image>
                                <svgGroup
                                    type="to_youhuiquanjuchi"
                                    width="190"
                                    height="190"
                                    px="rpx"
                                    class="to_youhuiquan"
                                    v-if="item.isReceive == 1"
                                    :color="diyStyle_var['--color_coupon_main']"
                                ></svgGroup>
                                <svgGroup
                                    type="to_youhuiquanjuchi"
                                    width="190"
                                    height="190"
                                    px="rpx"
                                    class="to_youhuiquan"
                                    v-if="item.isReceive == 2"
                                    :color="diyStyle_var['--color_coupon_opacity']"
                                ></svgGroup>
                                <svgGroup type="to_youhuiquanjuchi" width="190" height="190" px="rpx" class="to_youhuiquan" v-if="item.isReceive == 3" color="#dddddd"></svgGroup>
                                <view class="coupon_pre_left">
                                    <!-- 固定券 start -->
                                    <view class="coupon_pre_price" :class="{ coupon_pre_price_high: item.publishValue.toString().length > 6 }" v-if="item.couponType == 1">
                                        <text class="unit">¥</text>
                                        <text class="price_int" v-if="item.publishValue.toString().indexOf('.') != -1 && item.publishValue.toString().split('.')[1] > 0">
                                            {{ item.publishValue }}
                                        </text>
                                        <text class="price_int" v-else>{{ $getPartNumber(item.publishValue, 'int') }}</text>
                                    </view>
                                    <!-- 固定券 end -->
                                    <!-- 折扣券 start -->
                                    <view class="coupon_pre_price" v-if="item.couponType == 2">
                                        <view class=""></view>
                                        <text class="price_int">{{ filters.toSplit(filters.toFixNum(item.publishValue, 1))[0] }}</text>
                                        .
                                        <text class="price_decimal">{{ filters.toSplit(filters.toFixNum(item.publishValue, 1))[1] }}</text>
                                        <text class="price_decimal">{{ $L('折') }}</text>
                                    </view>
                                    <!-- 折扣券 end -->
                                    <!-- 随机券 start -->
                                    <view class="coupon_pre_price" v-if="item.couponType == 3">
                                        <text class="unit">¥</text>
                                        <text class="price_int">{{ $getPartNumber(item.randomMax, 'int') }}</text>
                                    </view>
                                    <!-- 随机券 end -->
                                    <view class="coupon_pre_active">{{ item.couponContent }}</view>
                                </view>
                                <view class="coupon_pre_cen">
                                    <view class="coupon_pre_title">{{ item.useTypeValue }}</view>
                                    <view class="coupon_pre_time">{{ item.effectiveStart }}~{{ item.effectiveEnd }}</view>
                                    <view class="coupon_pre_rules" @click="descriptionOpen(item.couponId)">
                                        <text>{{ $L('使用规则') }}</text>
                                        <image :src="item.isOpen ? imgUrl + 'coupon/up.png' : imgUrl + 'coupon/down.png'" mode="aspectFit"></image>
                                    </view>
                                </view>
                                <view class="coupon_pre_right" v-if="item.isReceive == 3">{{ $L('已抢完') }}</view>
                                <view class="coupon_pre_right" v-if="item.isReceive == 2">{{ $L('已领') }}</view>
                                <view class="coupon_pre_right" v-if="item.isReceive == 1" @click="goReceive(item)">
                                    {{ $L('立即领取') }}
                                </view>
                            </view>
                            <view class="coupon_rules" v-if="item.isOpen == true">
                                <view class="coupon_rules_title">{{ $L('使用规则:') }}{{ item.description }}</view>
                            </view>
                            <view class="coupon_type">{{ item.couponTypeValue }}</view>
                            <view class="coupon_progress" v-if="item.isReceive != 3">
                                {{ $L('已抢') }}{{ item.receivePercent }}%
                                <view class="progress_con">
                                    <progress
                                        :percent="item.receivePercent"
                                        stroke-width="3"
                                        activeColor="#FFFFFF"
                                        :backgroundColor="diyStyle_var['--color_coupon_main']"
                                        border-radius="2px"
                                    />
                                </view>
                            </view>
                        </view>
                    </scroll-view>

                    <!-- 无优惠券 -->
                    <view class="empty_coupon_wrap">
                        <image :src="imgUrl + 'no_coupon.png'" mode="aspectFit" class="empty_coupon_img"></image>
                        <view class="empty_coupon_text">{{ $L('该店铺暂无优惠券~') }}</view>
                    </view>
                </view>
            </uni-popup>
            <!-- 优惠券弹框 end -->
            <!-- 商品全部，部分无货弹窗 start-->
            <view id="store_no_good" v-if="store_show_no_good" @touchmove.stop.prevent="moveHandle">
                <view class="content">
                    <view class="content_title">
                        <text>{{ no_good_info.stateValue }}</text>
                        <image @tap="hide_good_dialog" :src="imgUrl + 'order/store_no_good_cancel.png'" mode="aspectFit"></image>
                    </view>
                    <view class="good_list">
                        <view v-for="(item, index) in no_good_info.productList" :key="index" class="good_item">
                            <image :src="item.image" mode="aspectFit"></image>
                            <view class="good_info">
                                <view class="good_name">
                                    {{ item.goodsName }}
                                </view>
                                <view class="good_spec">
                                    <text>{{ item.specValues }}</text>
                                </view>
                                <text class="num">*{{ item.buyNum }}</text>
                            </view>
                        </view>
                    </view>
                    <view class="part_no_goods_another">
                        <view class="return" @click="refreshCartInfo">
                            {{ $L('确定') }}
                        </view>
                    </view>
                </view>
            </view>
            <!-- 商品全部，部分无货弹窗 end-->
            <uni-popup ref="clearPopup" type="dialog">
                <uni-popup-dialog
                    type="input"
                    :title="$L('提示')"
                    :content="$L('确定清空所有失效商品?')"
                    :duration="2000"
                    @confirm="clearFailureGoods('confirm')"
                ></uni-popup-dialog>
            </uni-popup>

            <!-- 商品全部，部分无货弹窗 end-->
            <uni-popup ref="batchDelPopup" type="dialog">
                <uni-popup-dialog type="input" :title="$L('提示')" :content="$L(`确定删除选中商品?`)" :duration="2000" @confirm="batchDelete('confirm')"></uni-popup-dialog>
            </uni-popup>
        </view>
    </view>
</template>
<script>
import ktabbar from '@/components/ktabbar.vue';
import { mapState, mapMutations } from 'vuex';
import uniNumberBox from '@/components/uni-number-box/uni-number-box.vue';
import recommendGoods from '@/components/recommend-goods.vue';
import uniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue';
import jyfParser from '@/components/jyf-parser/jyf-parser.vue';
import filters from '../../utils/filter.js';
//库存input change事件的防抖定时器
var storeAntiShakeTimer;
export default {
    components: {
        ktabbar,
        uniNumberBox,
        recommendGoods,
        uniNavBar,
        uniPopup,
        uniPopupDialog,
        jyfParser
    },
    computed: {
        ...mapState(['hasLogin', 'cartData', 'userInfo', 'userCenterData'])
    },
    data() {
        return {
            editFlag: false, //是否编辑标识
            total: 0, //总价格
            allChecked: false, //全选状态  true|false
            empty: false, //空白页现实  true|false
            cartList: [],
            topRightText: '', //导航右侧文字，没有数据的话不显示
            nav_left_icon: '', //底部tab进入的话为空，否则为back
            curOperateId: '', //当前长按的商品购物车id,登录时为cartId，未登录时为productId
            tempList: {},
            isShowBigNumBox: -1, //是否展示价格过长样式
            isShow: true, //重载页面
            closeImg: getApp().globalData.imgUrl + 'recharge_fail.png',
            isShowCloseBtn: false, //是否展示数量加减关闭按钮
            showPrice: '',
            ifOnShow: false, //从其他页面进入时重载当前页面
            token: '',
            isDisabled: false, //结算按钮是否禁用
            imgUrl: getApp().globalData.imgUrl,
            settings: [], //配置信息
            is_show_mask: true,
            is_show_empty: false, //是否显示空页面
            is_checked: true,
            is_show_more_rules: false, //是否展开优惠券使用规则
            coupon_list: [], //优惠券列表
            pageCurrent: 1, //优惠券列表，页
            pageSize: 10, //优惠券列表 每页的条数
            goReceiveBg: getApp().globalData.imgUrl + 'coupon/coupon_pre_bg.png', //立即领取背景
            finishReceiveBg: getApp().globalData.imgUrl + 'coupon/finishReceiveBg.png', //已抢完背景
            hasReceiveBg: getApp().globalData.imgUrl + 'coupon/hasReceiveBg.png', //已领取背景
            promotion_goods_info: [], //换促销商品信息
            promotion_list: [], //促销列表
            currIndex: 0,
            cartId: '',
            promotionDes: '',
            goodsPromotionId: '',
            promotionId: '',
            promotionType: '',
            store_show_no_good: false,
            no_good_info: '',
            filters,
            curPromotionCartItem: {},
            basic_site_name: '',
            currentIndex: 0, // 0 到店，1外卖，2外送自提， 3快递
            cartNumPickup: 0, // 购物车到店数量
            cartNumEmail: 0, // 购物车邮寄数量
            cartNumTakeaway: 0, // 购物车外卖数量
            cartNumTakeawayTwo: 0 //外送自提数量
        };
    },
    onHide() {
        // this.ifOnShow = true
    },
    onShow() {
        this.getCartData();
        this.editFlag = false;
        //wx-1-start
        //统计埋点方法--针对微信小程序
        // #ifdef MP-WEIXIN
        let url = getApp().globalData.apiUrl.substring(0, getApp().globalData.apiUrl.length - 1);
        this.$sldStatEvent({ behaviorType: 'pv', pageUrl: url + '/pages/cart/cart', referrerPageUrl: '' });
        // #endif
        //wx-1-end
        this.getCartNum();
    },
    onLoad(option) {
        if (this.$Route.query != undefined && this.$Route.query.source && this.$Route.query.source != 'tabbar') {
            this.nav_left_icon = 'back';
        }
        uni.showLoading({
            title: this.$L('加载中！'),
            icon: 'none'
        });
        if (this.hasLogin) {
            this.getCartData();
        } else {
            this.initCartData();
        }
        this.ifOpen();
        this.token = this.userInfo.access_token;
        this.getSettings();
    },
    onPullDownRefresh() {
        if (this.hasLogin) {
            this.getCartData();
        } else {
            this.initCartData();
        }
    },

    watch: {
        //显示空白页
        tempList(e) {
            if (e) {
                let empty = e.storeCartGroupList.length == 0 ? true : false;
                if (this.empty !== empty) {
                    this.empty = empty;
                }
            } else {
                this.empty = true;
            }
        }
    },
    // 监听顶部管理按钮
    onNavigationBarButtonTap(e) {},
    onPageScroll(e) {
        if (e.scrollTop > 0) {
            this.closeMask();
        }
    },
    methods: {
        ...mapMutations(['login', 'operateCartData']),
        changeTab(index) {
            this.currentIndex = index;
            if (this.hasLogin) {
                this.getCartData();
            } else {
                this.initCartData();
            }
        },
        ifOpen() {
            let param = {};
            param.data = {};
            param.data.names = 'basic_site_name';
            param.url = 'v3/system/front/setting/getSettings';
            param.method = 'GET';
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    this.basic_site_name = res.data[0];
                }
            });
        },
        moveHandle() {},
        // 隐藏商品无货弹窗
        hide_good_dialog() {
            this.store_show_no_good = false;
        },
        refreshCartInfo() {
            this.store_show_no_good = false;
            this.getCartData();
        },
        handleState(tempList) {
            let list = tempList.storeCartGroupList;
            let shop_stock_out = 0; //店铺库存不足数
            // tempList.totalAmount = 0
            list.map((item) => {
                item.promotionCartGroupList.map((item1) => {
                    if (item1.promotionDes) {
                        item1.promotionDes = item1.promotionDes.replace(/<(.+?)>/g, function (num) {
                            return "<text style='color: $color1'>" + num.slice(1, num.length - 1) + '</text>';
                        });
                        item1.promotionDes = item1.promotionDes.replace(/x[\d]/g, function (num) {
                            return "<text style='color: $color1'>" + num + '</text>';
                        });
                    }
                    item1.cartList.map((item2) => {
                        let price = item2.productPrice.toString().split('.')[0];
                        item2.isShowCollectBtn = true;
                        // if(item2.isChecked == 1){
                        // 	tempList.totalAmount += item2.productPrice * item2.buyNum
                        // }
                        if (price.toString().length > 5 && this.isShowBigNumBox != item2.cartId) {
                            item2.isShowBigNumBox = true;
                            item2.isShowCloseBtn = false;
                        } else if (price.toString().length > 5 && this.isShowBigNumBox == item2.cartId) {
                            item2.isShowBigNumBox = false;
                            item2.isShowCloseBtn = true;
                        } else {
                            item2.isShowBigNumBox = false;
                            item2.isShowCloseBtn = false;
                        }
                        // if(item2.productState == 3){
                        // 	shop_stock_out++
                        // }
                        // if(item1.cartList.length == shop_stock_out){
                        // 	item.shopForbidden = true
                        // }else{
                        // 	item.shopForbidden = false
                        // }
                    });
                });
            });
            tempList.isShowCollectBtn = true;
            if (list.length > 0) {
                if (!this.editFlag) {
                    this.topRightText = this.$L('管理');
                } else {
                    this.topRightText = this.$L('完成');
                }
            } else {
                this.topRightText = '';
                // this.$forceUpdate();
            }
            this.tempList = tempList;
            this.$forceUpdate();
            this.checkNum();
        },
        initCartData() {
            //从缓存获取购物车信息
            this.tempList = uni.getStorageSync('cart_list_Email') == undefined ? {} : uni.getStorageSync('cart_list_Email');
            if (this.tempList != '') {
                this.tempList.storeCartGroupList.map((item) => {
                    item.promotionCartGroupList.map((item1) => {
                        if (item1.promotionDes) {
                            item1.promotionDes = item1.promotionDes.map((i) =>
                                i.replace(/<(.+?)>/g, function (num) {
                                    return "<text style='color: $color1'>" + num.slice(1, num.length - 1) + '</text>';
                                })
                            );
                            item1.promotionDes = item1.promotionDes.map((i) =>
                                i.replace(/x[\d]/g, function (num) {
                                    return "<text style='color: $color1'>" + num + '</text>';
                                })
                            );
                        }
                        item1.cartList.map((item2) => {
                            let price = item2.productPrice.toString().split('.')[0];
                            item2.isShowCloseBtn = false;
                            if (price.toString().length > 5) {
                                item2.isShowBigNumBox = true;
                            } else {
                                item2.isShowBigNumBox = false;
                            }
                        });
                    });
                });
                this.tempList.isShowCollectBtn = false;
                if (this.tempList.storeCartGroupList.length > 0) {
                    this.topRightText = '管理';
                } else {
                    this.topRightText = '';
                }
                this.tempList = this.tempList;
                this.$forceUpdate();
                this.calcTotal();
            } else {
                this.tempList = {
                    storeCartGroupList: [],
                    invalidList: []
                };
            }
            uni.stopPullDownRefresh();
            uni.hideLoading();
        },
        //从购物车获取数据
        getCartData() {
            let isPickup = 0;
            // isPickup 0邮寄 1到店 2外卖 3外送自提
            this.$request({
                url: 'v3/business/front/cart/cartList',
                method: 'GET',
                data: {
                    isPickup
                }
            }).then((res) => {
                uni.stopPullDownRefresh();
                if (res.state == 200) {
                    this.tempList = res.data;
                    this.operateCartData(res.data);
                    let checkedLen = 0;
                    this.handleState(this.tempList);
                    let list = res.data.storeCartGroupList;
                    uni.hideLoading();
                } else {
                    this.$api.msg(res.msg);
                    uni.hideLoading();
                }
            });
        },
        //请求数据
        async loadData() {
            let list = await this.$api.json('cartList');
            let cartList = list.map((item) => {
                item.checked = true;
                return item;
            });
            this.cartList = cartList;
            this.calcTotal(); //计算总价
        },
        //监听image加载完成
        onImageLoad(key, index) {
            this.$set(this[key][index], 'loaded', 'loaded');
        },
        //监听image加载失败
        onImageError(key, index) {
            this[key][index].image = getApp().globalData.imgUrl + 'errorImage.jpg';
        },
        navToLogin() {
            let url = this.$Route.path;
            const query = this.$Route.query;
            uni.setStorageSync('fromurl', {
                url,
                query
            });
            this.$Router.push('/pages/public/login');
        },
        //确认订单前，检验商品是否可结算
        testConfirmOrder() {
            let param = {};
            param.url = 'v3/business/front/orderOperate/check';
            param.method = 'POST';
            param.data = {};
            param.header = {
                'Content-Type': 'application/json'
            };
            param.data.isCart = true;
            let isPickup = 0;
            // isPickup 0邮寄 1到店 2外卖 3外送自提
            param.data.isPickup = 0;
            this.$request(param)
                .then((res) => {
                    if (res.state == 267) {
                        this.no_good_info = res.data;
                        this.store_show_no_good = true;
                    } else if (res.state == 200) {
                        this.$Router.push({
                            path: '/order/confirmOrder',
                            query: {
                                orderType: 1,
                                ifcart: 1,
                                isPickup: isPickup
                            }
                        });
                    }
                })
                .catch((e) => {
                    //异常处理
                });
        },
        //全选状态处理 type, index
        check(isCheckedAll) {
            if (this.hasLogin) {
                //登录
                let cartIds = '';
                this.tempList.storeCartGroupList.map((item) => {
                    item.promotionCartGroupList.map((item1) => {
                        item1.cartList.map((item2) => {
                            cartIds += item2.cartId + ',';
                        });
                    });
                });
                cartIds = cartIds.substring(0, cartIds.length - 1);
                let param = {};
                param.url = 'v3/business/front/cart/checkedCarts';
                param.method = 'POST';
                param.data = {
                    cartIds,
                    checked: isCheckedAll == 0 ? 1 : 0,
                    isPickup: 0
                };
                this.$request(param).then((res) => {
                    if (res.state == 200) {
                        this.tempList = res.data;
                        this.handleState(this.tempList);
                    } else {
                        this.$api.msg(res.msg);
                    }
                });
            }
        },
        // 点击商品、店铺状态处理
        changeSelectState(type, cartId, isChecked, goodsId, productId) {
            if (this.hasLogin && !this.editFlag) {
                //登录
                let cartIds = '';
                let storeId = cartId;
                if (type == 'goods') {
                    //商品
                    cartIds = cartId;
                } else {
                    // 店铺
                    this.tempList.storeCartGroupList.map((item) => {
                        if (item.storeId == storeId) {
                            item.promotionCartGroupList.map((item1) => {
                                item1.cartList.map((item2) => {
                                    cartIds += item2.cartId + ',';
                                });
                            });
                        }
                    });
                    cartIds = cartIds.substring(0, cartIds.length - 1);
                }

                let param = {};
                param.url = 'v3/business/front/cart/checkedCarts';
                param.method = 'POST';
                param.data = {
                    cartIds: cartIds,
                    checked: isChecked == 1 ? 0 : 1,
                    isPickup: 0
                };
                let _this = this;
                this.$request(param).then((res) => {
                    if (res.state == 200) {
                        _this.tempList = res.data;
                        _this.checkNum();
                        this.handleState(res.data);
                    } else {
                        this.$api.msg(res.msg);
                    }
                });
            }
        },
        _calcValue(type, item, cartId, productId, productStock, productPrice) {
            let maxMin = Math.min(productStock, 99999);
            let num = item.buyNum;
            switch (type) {
                case 'plus': {
                    if (productStock == 0) {
                        this.$api.msg('库存不足');
                        return;
                    }

                    item.buyNum++;
                    if (item.buyNum > maxMin) {
                        this.$api.msg('超出购买范围');
                        item.buyNum = maxMin;
                    }
                    break;
                }

                case 'minus': {
                    item.buyNum--;
                    if (item.buyNum < 1) {
                        item.buyNum = 1;
                        uni.showToast({
                            title: '商品不能再减少了',
                            icon: 'none'
                        });
                    }
                    break;
                }

                case 'blur': {
                    if (item.buyNum == '') {
                        return;
                    }

                    if (item.buyNum > maxMin) {
                        this.$api.msg('超出购买范围');
                        item.buyNum = maxMin;
                    } else if (item.buyNum != '' && item.buyNum < 1) {
                        item.buyNum = 1;
                        uni.showToast({
                            title: '商品不能再减少了',
                            icon: 'none'
                        });
                    }

                    break;
                }
            }
            // isPickup 0邮寄 1到店 2外卖 3外送自提
            let isPickup = 0;
            if (storeAntiShakeTimer) {
                clearTimeout(storeAntiShakeTimer);
            }
            storeAntiShakeTimer = setTimeout(() => {
                if (this.userInfo.access_token) {
                    //登录
                    let param = {};
                    param.url = 'v3/business/front/cart/changeNum';
                    param.method = 'POST';
                    param.data = {
                        cartId,
                        number: item.buyNum > 1 ? item.buyNum : 1,
                        isPickup: isPickup
                    };
                    this.$request(param).then((res) => {
                        if (res.state == 200) {
                            if (item.isChecked == 1) {
                                this.tempList.totalAmount = res.data.totalAmount;
                            }
                        } else {
                            this.$api.msg(res.msg);
                            item.buyNum = 1;
                            this.changeNumAgain(item.buyNum, cartId);
                        }
                    });
                } else {
                    //未登录
                    this.tempList.storeCartGroupList.map((item) => {
                        item.promotionCartGroupList.map((item1) => {
                            item1.cartList.map((item2) => {
                                if (item2.productId == productId) {
                                    item2.buyNum = num - 0;
                                }
                            });
                        });
                    });
                    this.calcTotal();
                    uni.setStorage({
                        key: 'cart_list_Email',
                        data: this.tempList,
                        success: function () {
                            //更新购物车数量和购物车数据
                        }
                    });
                }
            }, 450);

            this.$forceUpdate();
        },

        changeNumAgain(number, cartId) {
            this.$request({
                url: 'v3/business/front/cart/changeNum',
                method: 'POST',
                data: {
                    cartId,
                    number
                }
            }).then((res) => {
                if (res.state == 200) {
                    this.operateCartData(res.data);
                }
            });
        },
        //删除
        deleteCartItem(index) {
            let list = this.cartList;
            let row = list[index];
            let id = row.id;

            this.cartList.splice(index, 1);
            this.calcTotal();
            uni.hideLoading();
        },
        // 批量删除
        batchDelete(type) {
            let cartIds = '';
            let tempArr = [];
            let unLoginArr = [];
            let checkedNum = 0;
            this.tempList.storeCartGroupList.map((item) => {
                item.promotionCartGroupList.map((item1) => {
                    item1.cartList.map((item2) => {
                        if (item2.isChecked == 1) {
                            checkedNum++;
                            cartIds += item2.cartId + ',';
                            tempArr.push(item2.cartId);
                            unLoginArr.push(item2.productId);
                        }
                    });
                });
            });

            switch (type) {
                case 'open': {
                    if (checkedNum == 0) {
                        uni.showToast({
                            title: this.$L('请选择删除的商品！'),
                            icon: 'none'
                        });
                    } else {
                        this.bath;
                        this.$refs.batchDelPopup.open();
                    }
                    break;
                }
                case 'confirm': {
                    this.$refs.batchDelPopup.close();
                    if (this.hasLogin) {
                        //登录时批量删除
                        cartIds = cartIds.substring(0, cartIds.length - 1);
                        let param = {};
                        param.url = 'v3/business/front/cart/deleteCarts';
                        param.method = 'POST';
                        param.data = {
                            cartIds,
                            isPickup: 0
                        };
                        this.$request(param).then((res) => {
                            if (res.state == 200) {
                                uni.showToast({
                                    title: this.$L('删除成功！')
                                });
                                this.checkNum();
                                this.getCartData();
                                this.getCartNum();
                            }
                        });
                    }
                    break;
                }
            }
        },
        // 计算结算数量及改变按钮样式
        checkNum() {
            let checkedLen = 0;
            this.tempList.storeCartGroupList.map((item) => {
                item.promotionCartGroupList.map((item1) => {
                    item1.cartList.map((item2) => {
                        if (item2.isChecked == 1 && item2.productState == 1) {
                            checkedLen++;
                        }
                    });
                });
            });
            this.isDisabled = checkedLen == 0 ? true : false;
        },
        //计算总价
        calcTotal() {
            // 未登录时计算合计价格,结算数量
            let subTotal = 0;
            let checkedNum = 0;
            this.tempList.storeCartGroupList.map((item) => {
                item.promotionCartGroupList.map((item1) => {
                    item1.cartList.map((item2) => {
                        if (item2.isChecked == 1) {
                            subTotal += item2.buyNum * Number(item2.productPrice);
                            checkedNum++;
                        }
                    });
                });
            });
            this.tempList.totalAmount = subTotal.toFixed(2);
            this.tempList.totalCheck = checkedNum;
            uni.setStorage({
                key: 'cart_list_Email',
                data: this.tempList,
                success: function () {
                    // this.isShow = false
                    // this.isShow = true
                }
            });
        },
        //创建订单
        createOrder() {
            let _this = this;
            if (this.hasLogin) {
                //登录跳转确认下单页
                let settle_num = 0;
                this.tempList.storeCartGroupList.map((item) => {
                    item.promotionCartGroupList.map((item1) => {
                        item1.cartList.map((item2) => {
                            if (item2.isChecked == 1 && item2.productState == 1) {
                                settle_num++;
                            }
                        });
                    });
                });
                if (settle_num == 0) {
                    uni.showToast({
                        title: _this.$L('您还没有选中商品'),
                        icon: 'none'
                    });
                } else {
                    this.testConfirmOrder();
                }
            } else {
                //未登录提示登录
                uni.showModal({
                    title: _this.$L('提示'),
                    content: _this.$L('需要先登录才能下单哦~'),
                    confirmText: _this.$L('去登录'),
                    cancelText: _this.$L('我再看看'),
                    success: (res) => {
                        if (res.confirm) {
                            let url = _this.$Route.path;
                            const query = _this.$Route.query;
                            uni.setStorageSync('fromurl', {
                                url,
                                query
                            });
                            _this.$Router.push('/pages/public/login');
                        }
                    }
                });
            }
        },
        //马上去逛逛事件
        goGoodsList() {
            this.$Router.push(`/standard/product/list`);
        },
        //管理购物车数据
        manageCart() {
            this.editFlag = !this.editFlag;
            if (this.tempList && this.tempList.storeCartGroupList && this.tempList.storeCartGroupList.length > 0) {
                if (this.editFlag) {
                    this.topRightText = this.$L('完成');
                    this.is_show_mask = false;
                    this.tempList.storeCartGroupList.map((item) => {
                        item.promotionCartGroupList.map((item1) => {
                            item1.cartList.map((item2) => {
                                if (item2.productState == 3) {
                                    item2.productState = 1;
                                    item2.isChecked = 1;
                                    this.$forceUpdate();
                                }
                            });
                        });
                    });
                } else {
                    this.tempList.storeCartGroupList.map((item) => {
                        item.promotionCartGroupList.map((item1) => {
                            item1.cartList.map((item2) => {
                                item2.isChecked = 0;
                                item.checkedAll = 0;
                            });
                        });
                    });
                    this.calcTotal();
                    uni.setStorage({
                        key: 'cart_list_Email',
                        data: this.tempList,
                        success: function () {}
                    });
                    this.topRightText = this.$L('管理');
                    this.is_show_mask = true;
                    this.tempList.storeCartGroupList.map((item) => {
                        item.promotionCartGroupList.map((item1) => {
                            item1.cartList.map((item2) => {
                                if (item2.buyNum > item2.productStock) {
                                    item2.productState = 3;
                                    item2.isChecked = 0;
                                }
                            });
                        });
                    });
                }
                this.checkNum();
            } else {
                this.topRightText = '';
            }
        },
        //商品长按事件
        showOperate(cartId, productId) {
            if (this.topRightText == this.$L('完成')) {
                return;
            }
            this.is_show_mask = true;
            if (this.token) {
                this.curOperateId = cartId;
            } else {
                this.curOperateId = productId;
            }
        },
        /*
         *操作商品事件
         * type:move移入收藏夹，del删除商品
         */
        operateCartGoods(type, id, productId) {
            if (this.hasLogin && !this.editFlag) {
                //登录
                let param = {};
                param.method = 'POST';
                if (type == 'move') {
                    param.url = 'v3/business/front/cart/moveToCollection';
                    param.data = {
                        key: this.userInfo.access_token,
                        cartIds: id,
                        isPickup: 0
                    };
                } else {
                    param.url = 'v3/business/front/cart/deleteCarts';
                    param.data = {
                        key: this.userInfo.access_token,
                        cartIds: id,
                        isPickup: 0
                    };
                }
                this.$request(param).then((res) => {
                    if (res.state == 200) {
                        if (type == 'del') {
                            uni.showToast({
                                title: this.$L('删除成功！'),
                                icon: 'none'
                            });
                        } else {
                            this.curOperateId = '';
                            uni.showToast({
                                title: this.$L('收藏成功！'),
                                icon: 'none'
                            });
                            this.tempList = res.data;
                        }
                        if (this.tempList.storeCartGroupList.length == 0) {
                            this.topRightText = '';
                        }
                        this.handleState(res.data);
                        this.checkNum();
                        this.getCartNum();
                        // this.isShow = false
                        // this.isShow = true
                    }
                });
            } else {
                //未登录删除
                this.tempList &&
                    this.tempList.storeCartGroupList &&
                    this.tempList.storeCartGroupList.map((item, index) => {
                        item.promotionCartGroupList.map((item1) => {
                            item1.cartList.map((item2, index2) => {
                                if (item2.productId == productId) {
                                    item1.cartList.splice(index2, 1);
                                }
                            });
                            if (item1.cartList.length == 0) {
                                item.promotionCartGroupList = [];
                            }
                            if (item.promotionCartGroupList.length == 0) {
                                this.tempList.storeCartGroupList.splice(index, 1);
                            }
                        });
                    });
                if (!this.hasLogin) {
                    if (this.tempList.storeCartGroupList.length == 0) {
                        uni.removeStorage({
                            key: 'cart_list_Email',
                            success: function (res) {
                                // this.isShow = false
                                // this.isShow = true
                            }
                        });
                    } else {
                        uni.setStorage({
                            key: 'cart_list_Email',
                            data: this.tempList,
                            success: function () {
                                //更新购物车数量和购物车数据
                                // this.isShow = false
                                // this.isShow = true
                            }
                        });
                    }
                }

                this.calcTotal();
                uni.showToast({
                    title: this.$L('删除成功！')
                });
                this.getCartNum();
                this.tempList = this.tempList;
            }
        },
        //进入商品详情页
        goGoodsDetail(productId, goodsId) {
            this.$Router.push({
                path: '/standard/product/detail',
                query: {
                    productId,
                    goodsId
                }
            });
        },
        // 商品价格过长修改购买数量
        changeBuyNum(cartId, productId) {
            this.isShowBigNumBox = cartId;
            this.tempList &&
                this.tempList.storeCartGroupList &&
                this.tempList.storeCartGroupList.map((item) => {
                    if (this.hasLogin) {
                        item.promotionCartGroupList.map((item1) => {
                            item1.cartList.map((item2) => {
                                if (item2.cartId == cartId) {
                                    item2.isShowBigNumBox = false;
                                    item2.isShowCloseBtn = true;
                                    this.$forceUpdate();
                                }
                            });
                        });
                    } else {
                        if (item.productId == productId) {
                            item.isShowBigNumBox = false;
                            item.isShowCloseBtn = true;
                            this.$forceUpdate();
                        }
                    }
                });

            this.tempList &&
                this.tempList.storeCartGroupList &&
                this.tempList.storeCartGroupList.map((item) => {
                    if (this.hasLogin) {
                        item.promotionCartGroupList.map((item1) => {
                            item1.cartList.map((item2) => {
                                if (item.cartId == cartId) {
                                    this.showPrice = item2.productPrice;
                                    item2.productPrice = item2.productPrice.toString().split('.')[0].toString().substr(0, 4) + '...';
                                }
                            });
                        });
                    } else {
                        if (item.productId == productId) {
                            this.showPrice = item.productPrice;
                            item.productPrice = item.productPrice.toString().split('.')[0].toString().substr(0, 4) + '...';
                        }
                    }
                });
        },
        // 编辑数量关闭数量加减
        closeBigPriceBox(cartId, productId) {
            this.isShowBigNumBox = -1;
            this.tempList.storeCartGroupList.map((item) => {
                item.promotionCartGroupList.map((item1) => {
                    item1.cartList.map((item2) => {
                        if (this.hasLogin) {
                            if (item2.cartId == cartId) {
                                item2.isShowBigNumBox = true;
                                item2.isShowCloseBtn = false;
                            }
                        } else {
                            if (item2.productId == productId) {
                                item2.isShowBigNumBox = true;
                                item2.isShowCloseBtn = false;
                            }
                        }
                    });
                });
            });
            this.getCartData();
        },
        // 清空失效商品
        clearFailureGoods(type) {
            switch (type) {
                case 'open': {
                    this.$refs.clearPopup.open();
                    break;
                }
                case 'confirm': {
                    this.$refs.clearPopup.close();
                    let _this = this;
                    let param = {};
                    param.url = 'v3/business/front/cart/emptyInvalid';
                    param.method = 'POST';
                    param.data = {
                        isPickup: 0
                    };
                    _this.$request(param).then((res) => {
                        if (res.state == 200) {
                            _this.tempList.invalidList = [];

                            uni.showToast({
                                title: this.$L('清除成功！')
                            });
                        }
                    });
                    break;
                }
            }
        },
        reload_cart(val) {
            if (this.hasLogin) {
                this.getCartData();
            } else {
                this.initCartData();
            }
            this.getCartNum();
        },
        //手机号免密登录是否开启接口
        getSettings() {},
        // 点击关闭长按蒙层
        closeMask(e) {
            this.is_show_mask = false;
        },
        addToCart(val) {
            const key = 'cart_list_Email';
            uni.setStorageSync(key, val);
            this.initCartData();
            this.$forceUpdate();
            this.getCartNum();
        },
        //获取购物车数据
        getCartNum() {
            if (this.hasLogin) {
                this.getCartNumSingle();
                let param = {};
                param.url = 'v3/business/front/cart/cartNum';
                param.method = 'GET';
                param.data = {};
                this.$request(param)
                    .then((res) => {
                        if (res.state == 200) {
                            if (res.data > 0) {
                                uni.setTabBarBadge({
                                    index: 3,
                                    text: res.data.toString()
                                });
                            } else {
                                uni.hideTabBarRedDot({
                                    index: 3
                                });
                            }
                        } else {
                            this.$api.msg(res.msg);
                        }
                    })
                    .catch((e) => {
                        //异常处理
                    });
            }
        },
        //获取自提邮寄购物车数据
        getCartNumSingle() {
            let param = {};
            param.url = 'v3/business/front/cart/cartNumNew';
            param.method = 'GET';
            param.data = {};
            // param.data.key = this.userInfo.access_token;
            this.$request(param)
                .then((res) => {
                    if (res.state == 200) {
                        this.cartNumPickup = res.data.isPickup1;
                        this.cartNumEmail = res.data.notPickup0;
                        this.cartNumTakeaway = res.data.notPickup2;
                        this.cartNumTakeawayTwo = res.data.notPickup3;
                    } else {
                        this.$api.msg(res.msg);
                    }
                })
                .catch((e) => {
                    //异常处理
                });
        },
        // 是否展示空页面
        IsShowEmptyPage() {
            if (this.tempList && this.tempList.cartInfoList.length > 0 && tempList.invalidList && tempList.invalidList.length > 0) {
                this.is_show_empty = false;
            } else {
                this.is_show_empty = true;
            }
        },
        // 打开换促销弹框
        showPromotionBox(cartId, productImage, productPrice, specValues, promotionList, promotionId, promotionType) {
            this.promotion_goods_info = [];
            this.promotion_goods_info.push({
                productImage: productImage,
                productPrice: productPrice,
                specValues: specValues,
                promotionId: promotionId
            });

            let findProIm = promotionList.findIndex((proIm) => proIm.promotionId == promotionId && proIm.promotionType == promotionType);

            if (findProIm > -1) {
                this.goodsPromotionId = promotionList[findProIm].goodsPromotionId;
            } else {
                this.goodsPromotionId = '';
            }

            this.promotionType = promotionType;
            let tmpList = JSON.parse(JSON.stringify(promotionList)); //此操作是为了脱离双向绑定

            tmpList.map(
                (i) =>
                    (i.promotionDes = i.promotionDes.replace(/<(.+?)>/g, function (num) {
                        return "<text style='color: $color1'>" + num.slice(1, num.length - 1) + '</text>';
                    }))
            );
            tmpList.map(
                (i) =>
                    (i.promotionDes = i.promotionDes.replace(/x[\d]/g, function (num) {
                        return "<text style='color: $color1'>" + num + '</text>';
                    }))
            );
            this.promotion_list = tmpList;
            this.cartId = cartId;
            this.$refs.popup.open();
        },
        // 关闭换促销弹框
        closePromotionBox() {
            this.$refs.popup.close();
        },
        // 关闭优惠券弹框
        closeCouponBox() {
            this.$refs.couponPopup.close();
        },
        packUpCoupon() {
            this.is_show_more_rules = !this.is_show_more_rules;
        },
        // 关注店铺
        careStore() {
            this.$Router.push('/standard/store/attentionStore');
        },
        // 去店铺详情
        toShopDetail(storeId) {
            this.$Router.push({
                path: '/standard/store/shopHomePage',
                query: {
                    vid: storeId
                }
            });
        },
        // 打开优惠券弹框,获取店铺优惠券列表
        getShopCoupon(storeId) {
            if (this.hasLogin) {
                this.$refs.couponPopup.open();
                let param = {};
                param.url = 'v3/promotion/front/coupon/storeCouponList?storeId=' + storeId;
                param.method = 'GET';
                this.$request(param).then((res) => {
                    if (res.state == 200) {
                        this.coupon_list = res.data.list;
                        this.coupon_list.forEach((item, index) => {
                            item.isOpen = false;
                            if (item.isReceive == 3) {
                                item.couponBg = this.finishReceiveBg;
                            }
                            if (item.isReceive == 2) {
                                item.couponBg = this.hasReceiveBg;
                            }
                            if (item.isReceive == 1) {
                                item.couponBg = this.goReceiveBg;
                            }
                        });
                    }
                });
            } else {
                let urls = this.$Route.path;
                const query = this.$Route.query;
                uni.setStorageSync('fromurl', {
                    url: urls,
                    query
                });
                this.$Router.push('/pages/public/login');
            }
        },
        // 领取优惠券
        applyCoupon(isReceive, couponId) {
            if (isReceive == 1) {
                //已领取
                uni.showToast({
                    title: this.$L('您已领取该优惠券'),
                    icon: 'none'
                });
            } else if (isReceive == 2) {
                //已抢完
                uni.showToast({
                    title: this.$L('该优惠券已抢完'),
                    icon: 'none'
                });
            } else {
                //立即领取
                let _this = this;
                let param = {};
                param.url = 'v3/promotion/front/coupon/receiveCoupon?couponId=' + couponId;
                param.method = 'GET';
                this.$request(param).then((res) => {
                    if (res.state == 200) {
                        uni.showToast({
                            title: _this.$L('领取成功！')
                        });
                        setTimeout(() => {
                            _this.getShopCoupon(_this.storeId);
                        }, 700);
                    }
                });
            }
        },
        //规则展开
        descriptionOpen(couponId) {
            this.coupon_list.map((item) => {
                if (item.couponId == couponId) {
                    if (item.description != '') {
                        item.isOpen = !item.isOpen;
                        this.$forceUpdate();
                    }
                }
            });
        },

        restictInput(item) {
            let str = String(item.buyNum);
            if (str.indexOf('.') > -1) {
                item.buyNum = parseInt(item.buyNum);
            }
        },

        //立即领取
        goReceive(item) {
            let couponId = item.couponId;
            let param = {};
            param.url = 'v3/promotion/front/coupon/receiveCoupon';
            param.method = 'GET';
            param.data = {};
            param.data.couponId = couponId;
            this.$request(param)
                .then((res) => {
                    if (res.state == 200) {
                        let result = res.data;
                        this.$api.msg(this.$L('领取成功!'));
                        this.getShopCoupon(item.storeId);
                    } else {
                        this.$api.msg(res.msg);
                        this.getShopCoupon(item.storeId);
                    }
                })
                .catch((e) => {
                    //异常处理
                });
        },
        // 修改促销活动
        changePromotion(index, promotionDes, goodsPromotionId, promotionType, promotionId) {
            this.currIndex = index;
            this.promotionDes = promotionDes;
            this.goodsPromotionId = goodsPromotionId;
            this.promotionType = promotionType;
            this.promotionId = promotionId;
        },
        // 确定修改促销活动
        confirmChangePromotion() {
            let param = {};
            param.url = 'v3/business/front/cart/changePromotion';
            param.method = 'POST';
            param.data = {
                cartId: this.cartId,
                promotionDescription: this.promotionDes.replace(/<text style='color: $color1'>|<\/text>/g, ''),
                promotionId: this.promotionId,
                promotionType: this.promotionType
            };
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    uni.showToast({
                        title: this.$L('修改成功！'),
                        icon: 'none'
                    });
                    this.tempList = res.data;
                    let storeCartGroupList = this.tempList.storeCartGroupList;
                    storeCartGroupList &&
                        storeCartGroupList.map((item) => {
                            item.promotionCartGroupList.map((item1) => {
                                if (item1.promotionDes) {
                                    item1.promotionDes = item1.promotionDes.replace(/<(.+?)>/g, function (num) {
                                        return "<text style='color: $color1'>" + num.slice(1, num.length - 1) + '</text>';
                                    });
                                    item1.promotionDes = item1.promotionDes.replace(/x[\d]/g, function (num) {
                                        return "<text style='color: $color1'>" + num + '</text>';
                                    });
                                }

                                item1.cartList.map((item2) => {
                                    item2.isShowBigNumBox = false;
                                });
                            });
                        });
                    this.$refs.popup.close();
                    this.$forceUpdate();
                }
            });
        }
    }
};
</script>

<style lang="scss">
.page {
    width: 750rpx;
    min-height: 100vh;
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    // 背景图片固定，不滚动
    background-attachment: fixed;

    .container {
        padding-bottom: 120rpx;
    }
    .cart_header {
        padding: 20rpx 30rpx;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        .cart_header_btn {
            font-size: 28rpx;
            position: relative;
        }
    }
}

.cart-list {
    padding: 0 20rpx;
    box-sizing: border-box;
    width: 750rpx;
    margin: 0 auto;
}

/* 购物车列表项 */
.cart-item {
    display: flex;
    position: relative;
    height: 200rpx;
    margin-top: 20rpx;

    .mask {
        position: absolute;
        z-index: 4;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 15rpx;

        .move,
        .del {
            width: 200rpx;
            height: 80rpx;
            border-radius: 40rpx;
            color: #fff;
            font-size: 28rpx;
        }

        .move {
            background: #949494;
        }

        .del {
            background: $color1;
            margin-left: 90rpx;
        }
    }

    .image-wrapper {
        flex-shrink: 0;
        position: relative;
        background-color: #fff;

        .goods-img {
            background-size: contain;
            background-position: center center;
            background-repeat: no-repeat;
            width: 200rpx;
            height: 200rpx;
            overflow: hidden;
            background-color: #f8f6f7;
            border-radius: 14rpx;
            margin-left: 20rpx;
        }
    }

    .checkbox {
        position: absolute;
        left: -16rpx;
        top: -16rpx;
        z-index: 8;
        font-size: 44rpx;
        line-height: 1;
        padding: 4rpx;
        color: $font-color-disabled;
        background: #fff;
        border-radius: 50px;
    }

    .item-right {
        flex: 1;
        overflow: hidden;
        position: relative;
        padding-left: 20rpx;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .right_top {
        }

        .goods_spec {
            font-size: 26rpx;
            color: #666;
            /* overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap; */
        }

        .title {
            color: $main-font-color;
            font-size: 26rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            word-break: break-word;
            line-height: 36rpx;
        }

        .attr {
            font-size: 24rpx;
            line-height: 28rpx;
            color: $main-third-color;
            background-color: #f8f8f8;
            padding: 3rpx 7rpx;
            border-radius: 6rpx;
        }

        .price {
            height: 50rpx;
            line-height: 50rpx;
        }

        .right_bottom {
            width: 399rpx;
            bottom: 0;

            .right_bottom_left {
                .trade_promotion_btn {
                    text {
                        font-size: 24rpx;
                        font-family: PingFang SC;
                        font-weight: 500;
                        color: #666666;
                    }

                    image {
                        width: 20rpx;
                        height: 12rpx;
                        margin-left: 5rpx;
                    }
                }

                .price_wrap {
                    height: 50rpx;

                    &.price_wrap_hide {
                        width: 188rpx;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        overflow: hidden;
                    }

                    &.price_wrap_super {
                        .unit,
                        .price_decimal,
                        .price_int {
                            color: #242846;
                        }
                    }

                    .unit,
                    .price_decimal {
                        font-size: 24rpx;
                        font-weight: 600;
                        color: $color1;
                    }

                    .price_int {
                        font-weight: 600;
                        font-size: 34rpx;
                        color: $color1;
                    }

                    .price_decimal {
                        position: relative;

                        .price_super_img {
                            position: absolute;
                            bottom: 4rpx;
                            display: inline-block;
                            width: 102rpx;
                            height: 34rpx;
                            line-height: 36rpx;
                            color: #cfb295;
                            font-size: 20rpx;
                            font-family: PingFang SC;
                            font-weight: 500;
                            text-align: center;
                            text-indent: 6rpx;
                            background-position: center;
                            background-repeat: no-repeat;
                            background-size: cover;
                            margin-left: 6rpx;
                        }
                    }
                }
            }

            .goods_num_box {
                height: 50rpx;
                font-size: 24rpx;
                font-weight: 600;
                text-align: center;
                line-height: 50rpx;
                color: #2d2d2d;
                border: 1px solid #f2f2f2;
                border-radius: 6px;
                padding: 0 15rpx;
                margin-right: 20rpx;
            }
        }
    }

    .del-btn {
        padding: 4rpx 10rpx;
        font-size: 34rpx;
        height: 50rpx;
        color: $font-color-light;
    }
}

/* 底部栏 */
.action-section {
    margin: 0 auto;
    /* #ifdef H5 */
    margin-bottom: calc(env(safe-area-inset-bottom) + 90rpx);
    /* #endif */
    position: fixed;
    left: 0rpx;
    bottom: 0rpx;
    right: 0;
    z-index: 95;
    display: flex;
    align-items: center;
    height: 100rpx;
    padding: 0 40rpx;
    background: #fff;
    box-shadow: 0px 0px 10px 0px rgba(153, 153, 153, 0.1);
    width: 750rpx;

    .checkbox {
        .iconfont {
            color: #bbbbbb;
            font-size: 32rpx;
        }

        .check_all_tit {
            color: #949494;
            font-size: 26rpx;
            margin-left: 15rpx;
        }
    }

    .clear-btn {
        position: absolute;
        left: 26rpx;
        top: 0;
        z-index: 4;
        width: 0;
        height: 52rpx;
        line-height: 52rpx;
        padding-left: 38rpx;
        font-size: $font-base;
        color: #fff;
        background: $font-color-disabled;
        border-radius: 0 50px 50px 0;
        opacity: 0;
        transition: 0.2s;

        &.show {
            opacity: 1;
            width: 120rpx;
        }
    }

    .total-box {
        flex: 1;
        padding-right: 40rpx;
        font-size: 26rpx;
        color: #2d2d2d;

        .price_wrap {
            .unit,
            .price_decimal {
                font-size: 24rpx;
                font-weight: bold;
                color: $color1;
                margin-top: 4rpx;
            }

            .price_int {
                font-weight: bold;
                font-size: 34rpx;
                line-height: 34rpx;
                color: $color1;
                margin-left: 2rpx;
            }
        }

        .coupon {
            font-size: $font-sm;
            color: $font-color-light;

            text {
                color: $font-color-dark;
            }
        }
    }

    .confirm-btn {
        width: 200rpx;
        height: 70rpx;
        background: $color1;
        border-radius: 35rpx;
        font-size: 28rpx;
        color: #fff;

        .settle_num {
            font-size: 28rpx;
        }
    }

    .move_collect {
        width: 200rpx;
        height: 60rpx;
        background: #ffffff;
        border: 1px solid var(--color_vice);
        border-radius: 30rpx;
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: var(--color_vice);
        line-height: 60rpx;
    }

    .del_more {
        width: 200rpx;
        height: 60rpx;
        border: 1px solid $color1;
        border-radius: 30rpx;
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: $color1;
        line-height: 60rpx;
        margin-left: 20rpx;
    }
}

/* 复选框选中状态 */
.action-section .checkbox.checked,
.cart-item .checkbox.checked {
    color: $uni-color-primary;
}

.empty_part {
    display: flex;
    flex: 1;
    width: 100%;
    height: 586rpx;
    background: #fff;
    margin-top: 20rpx;

    .img {
        width: 380rpx;
        height: 280rpx;
        margin-bottom: 37rpx;
        margin-top: 88rpx;
    }

    .tip_con {
        color: $main-third-color;
        font-size: 26rpx;
    }

    .ope_btn {
        color: $color1;
        font-size: 28rpx;
        padding: 0 25rpx;
        height: 54rpx;
        background: var(--color_halo);
        border-radius: 27rpx;
        margin-top: 20rpx;
    }
}

.invalid_list_wrap {
    background-color: #fff;
    margin-top: 20rpx;
    border-radius: 15rpx;
    padding: 0 20rpx;

    .invalid_list_title {
        width: 100%;
        height: 80rpx;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
    }

    .invalid_list_title text:nth-child(1) {
        font-size: 30rpx;
        color: #333;
    }

    .invalid_list_title text:nth-child(2) {
        font-size: 24rpx;
        color: $color1;
    }

    .invalid_list_content {
        width: 100%;
        padding-bottom: 20rpx;

        .invalid_list_item {
            padding-top: 20rpx;
            display: flex;
            align-items: center;

            .invalid_icon {
                width: 97rpx;
                height: 34rpx;
                font-size: 24rpx;
                text-align: center;
                font-weight: 600;
                color: #2d2d2d;
                border-radius: 6rpx;
                background-color: #ddd;
                margin-right: 15rpx;
                line-height: 34rpx;
            }

            .invalid_img {
                margin-right: 20rpx;

                image {
                    width: 200rpx;
                    height: 200rpx;
                    border-radius: 14rpx;
                }
            }

            .invalid_goods_wrap {
                width: 100%;
                height: 200rpx;
                display: flex;
                flex-direction: column;
                position: relative;

                .invalid_goods_name {
                    font-size: 28rpx;
                    /* height:80rpx; */
                    color: #999;
                    font-weight: 600;
                    text-overflow: -o-ellipsis-lastline;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    word-break: break-all;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                }

                .invalid_goods_text {
                    position: absolute;
                    bottom: 28rpx;
                    font-size: 24rpx;
                    color: #333;
                    font-weight: 600;
                }

                .invalid_goods_spec {
                    font-size: 12px;
                    line-height: 14px;
                    color: #999;
                    background-color: #f8f8f8;
                    padding: 1px 3px;
                    -webkit-border-radius: 3px;
                    border-radius: 3px;
                    margin-top: 8rpx;
                    width: fit-content;
                }
            }
        }
    }
}

.close_img {
    width: 36rpx;
    height: 36rpx;
    margin-left: 6rpx;
}

.iconfont {
    font-size: 32rpx;
    color: #bbbbbb;

    &.item_check {
        color: $color1 !important;
    }
}

.disabled_btn {
    width: 200rpx;
    height: 70rpx;
    border-radius: 35rpx;
    font-size: 28rpx;
    background: #adadad !important;

    .settle_num {
        font-size: 28rpx;
        /* margin-top: 7rpx; */
        /* padding-bottom: 6rpx; */
    }
}

.step {
    width: 175rpx;
    /* #ifdef MP-BAIDU */
    width: 185rpx;
    /* #endif */
}

.step ::v-deep .uni-numbox__minus,
.step ::v-deep .uni-numbox__value,
.step ::v-deep .uni-numbox__plus {
    border-color: #f2f2f2;
}

.stock_not {
    font-size: 24rpx;
    color: #ff0d24;
    margin-right: 20rpx;
    margin-top: 8rpx;
}

.stock_not_enough {
    color: #666666;
}

.stock_not_icon {
    color: #eeeeee;
}

.promotion_box {
    width: 750rpx;
    height: 900rpx;
    background-color: #fff;
    border-radius: 15rpx 15rpx 0 0;
    padding: 0 20rpx;
    box-sizing: border-box;

    .promotion_goods_wrap {
        border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
        display: flex;
        padding: 20rpx 0;

        .promotion_goods_img_wrap {
            width: 180rpx;
            height: 180rpx;
            border-radius: 15rpx;
            margin-right: 20rpx;
            background-color: #eee;

            .promotion_goods_img {
                width: 180rpx;
                height: 180rpx;
                border-radius: 15rpx;
            }
        }

        .promotion_goods_right {
            width: 100%;
            position: relative;

            .promotion_goods_price {
                display: flex;
                align-items: flex-end;

                .promotion_goods_price_now {
                    color: $color1;
                    font-weight: bold;
                    margin-right: 20rpx;

                    .small_price {
                        font-size: 24rpx;
                    }

                    .big_price {
                        font-size: 34rpx;
                    }
                }

                .promotion_goods_price_old {
                    font-size: 28rpx;
                    color: #999;
                    text-decoration: line-through;
                }
            }

            .promotion_goods_spec {
                font-size: 28rpx;
                color: #333;
                text-overflow: -o-ellipsis-lastline;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                line-clamp: 2;
                -webkit-box-orient: vertical;
                position: absolute;
                left: 0;
                bottom: 20rpx;
            }

            .close_icon {
                width: 30rpx;
                height: 30rpx;
                position: absolute;
                right: 0;
                top: 0;
            }
        }
    }

    .promotion_rules_wrap {
        height: 472rpx;
        overflow-y: scroll;

        .promotion_rules_title {
            margin-top: 30rpx;
            font-size: 32rpx;
            color: #333;
            font-weight: bold;
        }

        .promotion_rule_item {
            margin-top: 45rpx;
            display: flex;
            align-items: center;

            .promotion_text {
                width: 100%;
                overflow: hidden;
                display: inline-block;
                font-size: 28rpx;
                color: #333;
                margin-left: 24rpx;
            }
        }
    }

    .confirm_btn_wrap {
        width: 750rpx;
        position: fixed;
        /* #ifdef H5 */
        bottom: calc(100rpx + env(safe-area-inset-bottom));
        /* #endif */
        /* #ifdef MP||APP-PLUS */
        bottom: 0;
        /* #endif */

        left: 0;
        height: 100rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        box-shadow: 0px 0px 20px 0px rgba(86, 86, 86, 0.08);

        .confirm_btn {
            width: 690rpx;
            height: 70rpx;
            color: #fff;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 28rpx;
            background: $color1;
            border-radius: 35rpx;
        }
    }
}

/* 领取优惠券弹框 */
.coupon_box {
    height: 900rpx;
    background-color: #f5f5f5;
    border-radius: 15rpx 15rpx 0 0;

    .coupon_title {
        height: 90rpx;
        display: flex;
        align-items: center;
        font-size: 32rpx;
        color: #333;
        font-weight: bold;
        justify-content: space-between;
        padding: 0 20rpx 0 30rpx;
        box-sizing: border-box;
        background-color: #fff;

        .close_icon {
            width: 30rpx;
            height: 30rpx;
        }
    }

    .coupon_content_wrap {
        height: 810rpx;
        overflow-y: scroll;
        padding: 0 20rpx 120rpx 20rpx;
        box-sizing: border-box;

        .coupon_item {
            width: 100%;
            margin-top: 20rpx;

            .coupon_top {
                height: 180rpx;
                display: flex;
                align-items: center;
                background-color: #fff7f5;
                border-radius: 15rpx;
                position: relative;

                .coupon_price {
                    width: 200rpx;
                    font-size: 60rpx;
                    color: $color1;
                    font-weight: bold;
                    display: flex;
                    justify-content: center;
                }

                .coupon_rules_wrap {
                    .coupon_rule {
                        font-size: 34rpx;
                        color: #333;
                        font-weight: bold;
                    }

                    .coupon_time {
                        font-size: 28rpx;
                        color: #999;
                        margin-top: 18rpx;
                        font-weight: 400;
                    }
                }

                .have_apply {
                    width: 135rpx;
                    height: 50rpx;
                    position: absolute;
                    right: 28rpx;
                    top: 50%;
                    transform: translateY(-50%);
                }
            }

            .coupon_bottom {
                font-size: 26rpx;
                color: #333;
                padding: 20rpx 30rpx;
                box-sizing: border-box;
                background-color: #fff;
                border-radius: 15rpx;
                position: relative;

                .rule_icon {
                    width: 28rpx;
                    height: 28rpx;
                    position: absolute;
                    right: 30rpx;
                    top: 50%;
                    transform: translateY(-50%);
                }
            }
        }
    }
}

.coupon_rule_text {
    width: 581rpx;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.shop_list_wrap {
    .shop_item {
        margin-top: 20rpx;
        background-color: #fff;
        border-radius: 15rpx;
        box-sizing: border-box;

        .shop_item_top {
            display: flex;
            align-items: center;
            position: relative;
            padding-bottom: 20rpx;
            border-bottom: 1rpx solid #f2f2f2;
            padding: 20rpx;
            box-sizing: border-box;

            .shop_icon {
                width: 34rpx;
                height: 32rpx;
                margin: 0 10rpx 0 20rpx;
            }

            .shop_name {
                font-size: 28rpx;
                color: #2d2d2d;
                font-weight: 600;
                margin-right: 10rpx;
            }

            .to_right_icon {
                width: 13rpx;
                height: 22rpx;
            }

            .coupon_icon {
                width: 62rpx;
                height: 30rpx;
                position: absolute;
                top: 50%;
                right: 0;
                transform: translateY(-50%);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 22rpx;
                background: var(--color_halo);
                color: $color1;
                border-bottom-left-radius: 30rpx;
                border-top-left-radius: 30rpx;
            }
        }
    }
}

.discount_activity {
    padding: 0 20rpx 20rpx;
    box-sizing: border-box;
}

.discount_left {
    margin-left: 50rpx;
    display: flex;
    align-items: center;
    margin-top: 24rpx;
    margin-bottom: 22rpx;

    .discount_icon {
        width: 62rpx;
        height: 30rpx;
        font-size: 24rpx;
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        background: $color1;
        border-radius: 15rpx;
        margin-right: 10rpx;
    }

    .promotion_text {
        width: 550rpx;
        height: 36rpx;
        display: flex;
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        line-clamp: 1;
        -webkit-box-orient: vertical;

        .discount_text {
            font-size: 26rpx;
            color: #2d2d2d;
            line-height: 36rpx;
        }
    }
}

.discount_activity_wrap {
    display: flex;
    justify-content: space-between;
    margin-top: 20rpx;
}

/* 优惠券弹框 start */
.coupon_model {
    width: 100%;
    height: 900rpx;
    background: #f5f5f5;
    border-radius: 15rpx 15rpx 0 0;

    .coupon_model_title {
        width: 100%;
        height: 100rpx;
        border-radius: 15rpx 15rpx 0 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 12rpx 0 30rpx;
        box-sizing: border-box;
        position: absolute;
        z-index: 10;
        top: 0;
        background: #ffffff;
        border-bottom: 1rpx solid #f2f2f2;

        text {
            font-size: 32rpx;
            font-family: PingFang SC;
            font-weight: bold;
            color: #333333;
            line-height: 32rpx;
        }

        image {
            width: 46rpx;
            height: 46rpx;
        }
    }

    .coupon_model_list {
        box-sizing: border-box;
        height: 880rpx;
        width: 750rpx;
        overflow-x: hidden;
        padding: 120rpx 20rpx 0 20rpx;
        /* #ifdef H5*/
        padding-bottom: 100rpx;
        /* #endif */
        box-sizing: border-box;

        .my_coupon_pre {
            margin-bottom: 20rpx;
            position: relative;

            .coupon_pre_top {
                width: 710rpx;
                height: 190rpx;
                background-size: 100% 100%;
                display: flex;
                align-items: center;
                position: relative;

                .coupon_pre_top_bg_img {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 585rpx;
                    height: 190rpx;
                }

                .to_youhuiquan {
                    position: absolute;
                    right: -26rpx;
                    top: 0;
                }

                .coupon_pre_left {
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    width: 203rpx;
                    align-items: center;

                    .coupon_pre_price {
                        font-size: 20rpx;
                        font-family: Source Han Sans CN;
                        font-weight: bold;
                        color: var(--color_coupon_main);
                        line-height: 31rpx;
                        display: flex;
                        align-items: baseline;

                        text:nth-child(2) {
                            font-size: 48rpx;
                            font-family: Source Han Sans CN;
                            font-weight: bold;
                            color: var(--color_coupon_main);
                            line-height: 31rpx;
                        }

                        .price_int {
                            text-align: center;
                            word-break: break-all;
                        }
                    }

                    .coupon_pre_price_high {
                        position: relative;
                        left: 2rpx;
                        top: 14rpx;
                        margin-top: 8rpx;

                        text:nth-child(2) {
                            line-height: 40rpx;
                        }
                    }

                    .coupon_pre_active {
                        font-size: 24rpx;
                        font-family: Source Han Sans CN;
                        font-weight: 400;
                        color: var(--color_coupon_main);
                        line-height: 31rpx;
                        text-align: center;
                        margin-top: 20rpx;
                    }
                }

                .coupon_pre_cen {
                    position: relative;
                    display: felx;
                    flex-direction: column;
                    flex: 1;
                    padding-left: 44rpx;

                    .coupon_pre_title {
                        font-size: 30rpx;
                        font-family: PingFang SC;
                        font-weight: bold;
                        color: #111111;
                        line-height: 31rpx;
                    }

                    .coupon_pre_time {
                        font-size: 24rpx;
                        font-family: PingFang SC;
                        font-weight: 500;
                        color: #333333;
                        line-height: 31rpx;
                        margin: 21rpx 0 17rpx;
                    }

                    .coupon_pre_rules {
                        display: flex;
                        align-items: center;

                        text {
                            font-size: 24rpx;
                            font-family: PingFang SC;
                            font-weight: 500;
                            color: #999999;
                            line-height: 31rpx;
                        }

                        image {
                            width: 12rpx;
                            height: 7rpx;
                            margin-left: 20rpx;
                        }
                    }
                }

                .coupon_pre_right {
                    position: relative;
                    width: 130rpx;
                    box-sizing: border-box;
                    font-size: 24rpx;
                    font-family: Source Han Sans CN;
                    font-weight: 400;
                    color: #ffffff;
                    text-align: center;
                }
            }

            .coupon_rules {
                width: 710rpx;
                padding: 20rpx 43rpx;
                box-sizing: border-box;
                font-size: 22rpx;
                font-family: PingFang SC;
                font-weight: 500;
                color: #666666;
                line-height: 30rpx;
                background: #ffffff;
                border-top: 1rpx solid #f2f2f2;
                border-radius: 0 0 15rpx 15rpx;

                .coupon_rules_title {
                    margin-bottom: 10rpx;
                }
            }

            .coupon_type {
                position: absolute;
                top: 0;
                left: 0;
                padding: 0 5rpx;
                height: 30rpx;
                background: var(--color_coupon_main);
                font-size: 20rpx;
                font-family: Source Han Sans CN;
                font-weight: 400;
                color: #ffffff;
                line-height: 30rpx;
                text-align: center;
                border-radius: 15rpx 0 15rpx 0;
            }

            .coupon_progress {
                position: absolute;
                width: 130rpx;
                top: 10rpx;
                right: 0rpx;
                display: flex;
                flex-direction: column;
                align-items: center;
                font-size: 18rpx;
                font-family: Source Han Sans CN;
                font-weight: 400;
                color: #ffffff;
                line-height: 31rpx;

                .progress_con {
                    width: 84rpx;
                    margin-top: 5rpx;
                    border-radius: 5rpx;

                    progress {
                        border: 1rpx solid #ffffff;
                        border-radius: 5rpx;
                    }
                }
            }
        }
    }
}

/* 空优惠券 */
.empty_coupon_wrap {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-top: 260rpx;
    box-sizing: border-box;

    .empty_coupon_img {
        width: 380rpx;
        height: 280rpx;
        margin-bottom: 30rpx;
    }

    .empty_coupon_text {
        font-size: 26rpx;
        color: #999;
    }
}

/* 优惠券弹框 end */
.goods_num_wrap {
    text-align: right;
}

.exceed_price_wrap {
    display: flex;
    align-items: center;
}

#store_no_good {
    display: flex;
    align-items: center;
    justify-content: center;
}

#store_no_good {
    position: fixed;
    top: 0;
    left: 0;
    width: 750rpx;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 999;
    right: 0;
    margin: 0 auto;
}

#store_no_good .content {
    width: 580rpx;
    height: 773rpx;
    background-color: white;
    border-radius: 15rpx;

    .content_title {
        margin-top: 24rpx;
        margin-bottom: 24rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 30rpx;
        font-size: 32rpx;
        color: #2d2d2d;

        image {
            width: 22rpx;
            height: 22rpx;
        }
    }

    .good_list {
        height: 593rpx;
        overflow-y: scroll;
        width: 100%;

        .good_item {
            display: flex;
            align-items: center;
            padding: 30rpx;
            border-top: 1rpx solid #f2f2f2;

            image {
                width: 70rpx;
                height: 70rpx;
                border-radius: 6rpx;
            }

            .good_info {
                margin-left: 20rpx;
                position: relative;

                .good_name {
                    width: 382rpx;
                    font-size: 26rpx;
                }

                .good_spec {
                    margin-top: 20rpx;
                    font-size: 22rpx;
                }

                .num {
                    position: absolute;
                    bottom: 0rpx;
                    right: 0rpx;
                    font-size: 24rpx;
                    color: #333333;
                }
            }
        }
    }

    .part_no_goods {
        width: 520rpx;
        height: 60rpx;
        font-size: 30rpx;
        color: white;

        display: flex;
        align-items: center;
        margin: 0 auto;
        border-radius: 30rpx;
        margin-top: 15rpx;

        .return {
            width: 50%;
            height: 60rpx;
            line-height: 60rpx;
            background-color: #ff8809;
            text-align: center;
            border-radius: 30rpx 0 0 30rpx;
        }

        .remove {
            width: 50%;
            height: 60rpx;
            line-height: 60rpx;
            background-color: #f90208;
            text-align: center;
            border-radius: 0 30rpx 30rpx 0;
        }
    }
}

.part_no_goods_another {
    width: 520rpx;
    height: 60rpx;
    font-size: 30rpx;
    color: white;
    display: flex;
    align-items: center;
    margin: 0 auto;
    border-radius: 30rpx;
    background-color: #f90208;

    .return {
        width: 100%;
        text-align: center;
    }
}

.uni-numbox {
    /* #ifndef APP-NVUE */
    display: flex;
    /* #endif */
    flex-direction: row;
    height: 50rpx;
    line-height: 50rpx;
    width: 194rpx;
}

.uni-numbox__value {
    background-color: $uni-bg-color;
    width: 90rpx;
    height: 50rpx;
    text-align: center;
    font-size: 24rpx;
    border-width: 1px;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.1);
}

.uni-numbox__minus {
    /* #ifndef APP-NVUE */
    display: flex;
    /* #endif */
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 50rpx;
    height: 50rpx;
    /* #ifdef MP-TOUTIAO */
    height: 52rpx;
    /* #endif */
    // line-height: $box-line-height;
    // text-align: center;
    font-size: 20px;
    color: $uni-text-color;
    background-color: #fff;
    border-width: 1px;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.1);
    border-top-left-radius: $uni-border-radius-base;
    border-bottom-left-radius: $uni-border-radius-base;
    border-right-width: 0;
}

.uni-numbox__plus {
    /* #ifndef APP-NVUE */
    display: flex;
    /* #endif */
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 50rpx;
    height: 50rpx;
    height: 50rpx;
    /* #ifdef MP-TOUTIAO */
    height: 52rpx;
    /*#endif*/
    border-width: 1px;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.1);
    border-top-right-radius: 6rpx;
    border-bottom-right-radius: 6rpx;
    background-color: #fff;
    border-left-width: 0;
}

.uni-numbox--text {
    font-size: 30rpx;
    color: #2d2d2d;
}

.uni-numbox--disabled {
    color: #949494;
}

.uni-input-input {
    font-size: 24rpx !important;
    color: #2d2d2d;
}
</style>
