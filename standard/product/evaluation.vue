<!-- 商品评价页面 -->
<template>
	<view :style="mix_diyStyle">
  <view class="evaluation" v-if="statistics">
    <!-- 商品评价数量 start -->
    <view class="evaluation_num">
      <view class="evaluation_title">
        <view class="evaluation_title_left">
          {{ $L('商品评价') }}<text>（{{ statistics.commentsCount }}）</text>
        </view>
        <view class="evaluation_title_right">
          {{ $L('好评率') }}<text>{{ statistics.highPercent }}</text>
        </view>
      </view>
      <view class="evaluation_con">
        <view
          class="evaluation_nav_pre"
          :class="{ active: curnav == 0 || !curnav }"
          @click="getEvaluation('', 0)"
        >
          {{ $L('全部') }}（{{
            statistics.commentsCount ? statistics.commentsCount : 0
          }}）
        </view>
        <view
          class="evaluation_nav_pre"
          :class="{ active: curnav == 1 }"
          @click="getEvaluation('hasPic', 1)"
        >
          {{ $L('有图') }}（{{
            statistics.hasPicCount ? statistics.hasPicCount : 0
          }}）
        </view>
        <view
          class="evaluation_nav_pre"
          :class="{ active: curnav == 2 }"
          @click="getEvaluation('high', 2)"
        >
          {{ $L('好评') }}（{{
            statistics.highCount ? statistics.highCount : 0
          }}）
        </view>
        <view
          class="evaluation_nav_pre"
          :class="{ active: curnav == 3 }"
          @click="getEvaluation('middle', 3)"
        >
          {{ $L('中评') }}（{{
            statistics.middleCount ? statistics.middleCount : 0
          }}）
        </view>
        <view
          class="evaluation_nav_pre"
          :class="{ active: curnav == 4 }"
          @click="getEvaluation('low', 4)"
        >
          {{ $L('差评') }}（{{
            statistics.lowCount ? statistics.lowCount : 0
          }}）
        </view>
      </view>
    </view>
    <!-- 商品评价数量 end -->
    <!-- 商品评价列表 start -->
    <view class="evaluation_list" v-if="evaluationList.length > 0">
      <view
        class="evaluation_pre"
        v-for="(item, index) in evaluationList"
        :key="index"
      >
        <view class="evaluation_pre_top">
          <view class="evaluation_pre_avator">
            <image :src="item.memberAvatar" mode="aspectFill"></image>
          </view>
          <view class="evaluation_pre_name">
            {{ filters.toAnonymous(item.memberName) }}
          </view>
          <!-- 只读状态 -->
          <uni-rate
            :readonly="true"
            :value="item.score"
            active-color="var(--color_main)"
            disabledColor="#ccc"
            :size="18"
          />
        </view>
        <view class="evaluation_pre_time"
          >{{ item.createTime }} {{ item.specValues }}</view
        >
        <view class="evaluation_des" v-if="item.content">{{
          item.content
        }}</view>
        <text class="evaluation_des" v-else>{{
          item.score >= 4
            ? $L('好评')
            : item.score < 2
            ? $L('差评')
            : $L('中评')
        }}</text>
        <view class="evaluation_pre_images">
          <image
            :src="item1"
            mode="aspectFit"
            @click="previewImg(item.images, item1, $event)"
            v-for="(item1, index1) in item.images"
            :key="index1"
          ></image>
        </view>
        <view class="business_reply" v-if="item.replyContent">
          <view class="business_reply_tips">{{ $L('商家回复') }}</view>
          <view class="business_reply_content">{{ item.replyContent }}</view>
        </view>
      </view>
      <loadingState
        v-if="loadingState == 'first_loading' || evaluationList.length > 0"
        :state="loadingState"
      />
    </view>
    <view class="no_data" v-if="evaluationList.length == 0 && !loading">
      <image :src="imgUrl + 'empty_evaluate.png'" mode="aspectFit"></image>
      <text>{{ $L('该商品暂无评价') }}</text>
    </view>
    <!-- 商品评价列表 end -->
  </view>
	</view>
</template>
<script>
import loadingState from '@/components/loading-state.vue'
import { mapState } from 'vuex'
import filters from '../../utils/filter.js'
export default {
  components: {
    loadingState
  },
  data() {
    return {
      current: 1, //当前为第1页
      loadingState: 'first_loading',
      pageSize: 20,
      loading: false, //是否加载数据
      hasMore: true, //是否还有数据
      productId: '', //货品id
      statistics: {}, //商品评价信息
      evaluationList: [], //评价列表
      curnav: 0, //评价默认显示全部
      imgUrl: getApp().globalData.imgUrl,
      loading: false,
      filters
    }
  },
  computed: {
    ...mapState(['hasLogin', 'userInfo'])
  },
  async onLoad(options) {
    setTimeout(()=>{
      uni.setNavigationBarTitle({
        title: this.$L('商品评价')
      })    
    },0);
    
    this.productId = this.$Route.query.productId //货品id
    this.getEvaluation()
  },
  methods: {
    //获取评论信息
    getEvaluation(type, curnav) {
      uni.showLoading({
        title: this.$L('加载中')
      })
      let that = this
      that.curnav = curnav
      that.loading = true
      let param = {}
      param.url = 'v3/goods/front/goods/comment'
      param.method = 'GET'
      param.data = {}
      param.data.productId = that.productId
      param.data.pageSize = that.pageSize //分页大小 默认 ==20
      param.data.current = that.current //当前页面位置 默认 ==1
      param.data.type = type ? type : '' //评价等级[(好评)high,(中评)middle,(差评)low]，（有图）hasPic
      this.loadingState =
        this.loadingState == 'first_loading' ? this.loadingState : 'loading'
      this.$request(param)
        .then((res) => {
          if (res.state == 200) {
            this.statistics = res.data
            if (this.current == 1) {
              this.evaluationList = res.data.list
            } else {
              this.evaluationList = this.evaluationList.concat(res.data.list)
            }
            this.hasMore = this.$checkPaginationHasMore(res.data.pagination) //是否还有数据
            if (this.hasMore) {
              this.current++
              this.loadingState = 'allow_loading_more'
            } else {
              this.loadingState = 'no_more_data'
            }
            that.loading = false
            uni.hideLoading()
          } else {
            that.loading = false
            uni.hideLoading()
            this.$api.msg(res.msg)
          }
        })
        .catch((e) => {
          //异常处理
        })
    },
    //触底
    onReachBottom() {
      if (this.hasMore) {
        this.getEvaluation()
      }
    },
    //多张图片点击预览
    previewImg(images, curImg, e) {
      let src = curImg
      let imgList = images
      imgList &&
        imgList.map((item, index) => {
          imgList[index] = item
        })
      uni.previewImage({
        current: src,
        // 当前显示图片的http链接
        urls: imgList, // 需要预览的图片http链接列表
        indicator: 'number',
        loop: true
      })
    }
  }
}
</script>

<style lang="scss">
.evaluation {
  width: 750rpx;
  height: 100%;
  margin: 0 auto;
  .evaluation_num {
    border-top: 20rpx solid #f8f8f8;
    .evaluation_title {
      width: 100%;
      display: flex;
      justify-content: space-between;
      padding: 20rpx;
      box-sizing: border-box;
      align-items: center;
      .evaluation_title_left {
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: 600;
        color: #333333;
        line-height: 45rpx;
        text {
          color: #949494;
          margin-left: 20rpx;
          font-weight: 400;
        }
      }
      .evaluation_title_right {
        font-size: 22rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #949494;
        line-height: 45rpx;
        text {
          color: #333333;
          margin-left: 10rpx;
        }
      }
    }
    .evaluation_con {
      display: flex;
      flex-wrap: wrap;
      padding: 0 20rpx;
      box-sizing: border-box;
      .evaluation_nav_pre {
        width: 180rpx;
        height: 50rpx;
        background: #f5f5f5;
        border-radius: 25rpx;
        box-sizing: border-box;
        font-size: 24rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #333333;
        line-height: 50rpx;
        text-align: center;
        margin-right: 20rpx;
        margin-bottom: 20rpx;
      }
      .active {
        width: 180rpx;
        height: 50rpx;
        background:var(--color_halo);
        border-radius: 25rpx;
        font-size: 24rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color:var(--color_main);
        line-height: 50rpx;
        box-sizing: border-box;
        text-align: center;
      }
    }
  }
  .evaluation_list {
    border-top: 20rpx solid #f8f8f8;
    padding-bottom: 20rpx;
    .evaluation_pre {
      padding: 20rpx 20rpx 20rpx 20rpx;
      // margin-left: 20rpx;
      box-sizing: border-box;
      border-bottom: 1rpx solid #f5f5f5;
      .evaluation_pre_top {
        display: flex;
        align-items: center;
        .evaluation_pre_avator {
          width: 50rpx;
          height: 50rpx;
          border-radius: 50%;
          image {
            width: 50rpx;
            height: 50rpx;
            border-radius: 50%;
          }
        }
        .evaluation_pre_name {
          font-size: 26rpx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #333333;
          line-height: 45rpx;
          margin: 0 20rpx;
        }
      }
      .evaluation_pre_time {
        font-size: 22rpx;
        font-family: PingFang SC;
        font-weight: 400;
        color: #949494;
        line-height: 39rpx;
      }
      .evaluation_des {
        width: 710rpx;
        font-size: 26rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #333333;
        line-height: 39rpx;
        word-break: break-all;
        padding: 20rpx 0;
      }
      .evaluation_pre_images {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        image {
          width: 223rpx;
          height: 223rpx;
          border-radius: 15rpx;
          margin-right: 20rpx;
          margin-bottom: 20rpx;
        }
        image:nth-child(3n) {
          margin-right: 0;
        }
      }
      .business_reply {
        width: 710rpx;
        margin: 0 auto;
        background: #f8f8f8;
        border-radius: 6px;
        padding: 23rpx 19rpx 19rpx 17rpx;
        box-sizing: border-box;
        .business_reply_tips {
          font-size: 26rpx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #2d2d2d;
          line-height: 36rpx;
        }
        .business_reply_content {
          font-size: 26rpx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #666666;
          line-height: 36rpx;
          white-space: pre-line;
          word-break: break-all;
        }
      }
    }
  }
  .no_data {
    border-top: 20rpx solid #f8f8f8;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 100rpx;
    image {
      width: 380rpx;
      height: 280rpx;
    }
    text {
      font-size: 26rpx;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #999999;
      margin-top: 30rpx;
    }
  }
}
</style>
