<template>
	<view :style="mix_diyStyle">
		<view class="content">
			<view class="fixed_top_status_bar"></view>
			<view class="top_part">
				<!-- 搜索头部分 start -->
				<view class='sea_input_part'>
					<!-- #ifndef MP-->
					<text class="back_icon iconfont iconziyuan2" @click="navBack"></text>
					<!-- #endif -->
					<view class="search_center" @click="search">
						<image class="search_icon" :src="imgUrl+'search.png'"></image>
						<input disabled class='sea_input' type='text' :value="searchParams.keyword"
							:placeholder="$L('请输入关键词')"></input>
						<image v-if='searchParams.keyword' class='clear_content' v-show="searchParams.keyword"
							:src="imgUrl+'input_clear.png'" @click.stop="clearInput" />
					</view>
				</view>
				<!-- 搜索头部分 end -->
				<!-- 头部搜索nav start -->
				<view class="navbar">
					<view class="nav-item complex"
						:class="{current: filterIndex === 0||filterIndex === 3||filterIndex === 4||filterIndex === 5}"
						@click="showSelect">
						<text>{{$L('综合')}}</text>
						<text class="iconfont iconziyuan14"></text>
					</view>
					<view class="nav-item" :class="{current: filterIndex === 1}" @click="tabClick(1)">
						{{$L('销量')}}
					</view>
					<view class="nav-item" :class="{current: filterIndex === 2}" @click="tabClick(2)">
						{{$L('店铺')&&$L('店铺')}}
					</view>
					<view class="nav-item default" v-if="showStoreList">
						<text>{{$L('筛选')&&$L('筛选')}}</text>
						<text class="filter_icon iconfont iconshaixuan"></text>
					</view>
					<view v-else class="nav-item" @click="toggleCateMask('show')">
						<text>{{$L('筛选')&&$L('筛选')}}</text>
						<text class="filter_icon iconfont iconshaixuan"></text>
					</view>
					<text v-if="showStoreList" class="cate_item iconfont iconliebiao1  default"></text>
					<text v-else
						:class="{cate_item:loadingState, iconfont:loadingState, iconfenlei1:gridFlag,iconliebiao1:!gridFlag,grid_icon:gridFlag}"
						@click="tabShowStyle"></text>
				</view>
			</view>
			<!-- 头部搜索nav end -->

			<view
				v-if="(loadingState!='first_loading'&&!showStoreList&&goodsList.length == 0)||(loadingState!='first_loading'&&showStoreList&&store_list.length==0)"
				class="empty_part flex_column_start_center">
				<image :src="imgUrl+'empty_goods.png'" />
				<text>{{$L('暂无数据')}}</text>
			</view>
			<view class="pricearea" :hidden="pricearea" @tap.stop="showSelect" id="pricearea-wrap"
				catchtouchmove="touchmove">

				<view class="pri-content">
					<view :class="'pri-item ' + (seleindex==0?'current':'')" data-order="" data-key="" data-index="0"
						@tap.stop="changeSort(0)">
						<text>{{$L('综合排序')}}</text>
						<svgGroup type="checked" width="18" height="16" :color="diyStyle_var['--color_price']"
							v-if="seleindex==0">
						</svgGroup>
					</view>
					<view :class="'pri-item ' + (seleindex==5?'current':'')" data-index="5" @tap.stop="changeSort(5)">
						<text>{{$L('人气排序从高到低')}}</text>
						<svgGroup type="checked" width="18" height="16" :color="diyStyle_var['--color_price']"
							v-if="seleindex==5">
						</svgGroup>
					</view>
					<view :class="'pri-item ' + (seleindex==4?'current':'')" data-index="4" @tap.stop="changeSort(4)">
						<text>{{$L('价格排序从高到低')}}</text>
						<svgGroup type="checked" width="18" height="16" :color="diyStyle_var['--color_price']"
							v-if="seleindex==4">
						</svgGroup>
					</view>
					<view :class="'pri-item ' + (seleindex==3?'current':'')" data-index="3" @tap.stop="changeSort(3)">
						<text>{{$L('价格排序从低到高')}}</text>
						<svgGroup type="checked" width="18" height="16" :color="diyStyle_var['--color_price']"
							v-if="seleindex==3">
						</svgGroup>
					</view>
					<view :class="'pri-item ' + (seleindex==6?'current':'')" data-index="6" @tap.stop="changeSort(6)">
						<text>{{$L('距离由近到远')}}</text>
						<svgGroup type="checked" width="18" height="16" :color="diyStyle_var['--color_price']"
							v-if="seleindex==6">
						</svgGroup>
					</view>
				</view>

			</view>
			<scroll-view v-if="goodsList.length&&!showStoreList" class="goods_part" scroll-y
				@scrolltolower='getMoreData'>
				<view v-if='gridFlag' class="goods_list_another flex_row_start_start">
					<block v-for="(list,listIdx) in waterFall" :key="listIdx">
						<view class="waterFall">
							<goodsListItemV v-for="(item,index) in list" :goods_info="item" :key='index' :isSuper="userCenterData.isSuper==1?true:false"/>
						</view>
					</block>
				</view>
				<view v-if='!gridFlag' class="goods_list flex_row_start_start">
					<goodsItemH v-for="(item,index) in goodsList" :goods_info="item" :key='index' :isSuper="userCenterData.isSuper==1?true:false"/>
				</view>
				<loadingState v-if="loadingState == 'first_loading'||goodsList.length > 0" :state='loadingState' />
			</scroll-view>
			<scroll-view v-if="showStoreList&&store_list.length>0" class="shop_lists" scroll-y
				@scrolltolower='getMoreStoreData'>
				<view class="" v-if="showStoreList&&store_list.length>0">
					<storeItem v-for="(item, index) in store_list" :key="index" :store_info='item' :store_index="index">
					</storeItem>
					<loadingState v-if="loadingState == 'first_loading'||store_list.length > 0" :state='loadingState' />
				</view>
			</scroll-view>
			<view class="cate-mask" :class="cateMaskState===0 ? 'none' : cateMaskState===1 ? 'show' : ''"
				@click="toggleCateMask">
				<view class="cate-content" @click.stop.prevent="stopPrevent" @touchmove.stop.prevent="stopPrevent">
					<view class="filter_title">
						{{$L('筛选')}}
					</view>
					<scroll-view scroll-y class="cate-list flex_column_start_start">
						<view class="wrap">
							<view class="part price_part">
								<view class="title flex_row_between_center">
									<text class="left">{{$L('价格区间')}}</text>
								</view>
								<view class="detail price_area flex_row_start_center">
									<input type='digit' :value="searchParams.lowPrice" class="amount low"
										:placeholder="$L('最低价')" placeholder-class="price_input_placeholder"
										data-key='lowPrice' @input="inputPrice('lowPrice',$event)"
										@blur="blurPrice('lowPrice')" />

									<input type='digit' :value="searchParams.highPrice" class="amount hight"
										:placeholder="$L('最高价')" placeholder-class="price_input_placeholder"
										data-key='highPrice' @input="inputPrice('highPrice',$event)"
										@blur="blurPrice('highPrice')" />

								</view>
							</view>
							<!-- 分类 -->
							<view v-if='cateList.length>0' class="part" style="margin-top: 20rpx;">
								<view class="title flex_row_between_center">
									<text class="left">{{$L('分类')}}</text>
									<text v-if="cateList.length>cateLimit||cateLimit==100"
										:class="{iconfont:true, iconziyuan11:true,up:cateFlag=='up',down:cateFlag=='down',}"
										@click="viewMoreCate"></text>
									<view v-if="cateList.length>cateLimit||cateLimit==100" class="attributr_sel_con">
										<text class="attributr_sel_text">{{selectCateText}}</text>
									</view>
								</view>
								<view class="detail flex_row_start_center">
									<template v-for="(itemCat,index) in cateList">
										<view :key="index" v-if="index<cateLimit"
											:class="{item:true, sel:searchParams.cateId == itemCat.categoryId}"
											@click="selCat('cateId',itemCat.categoryId,itemCat.categoryName)">
											<text class="brand_name">{{itemCat.categoryName}}</text>
										</view>
									</template>
								</view>
							</view>
							<!-- 品牌 selectBrandText-->
							<view class="part" style="margin-top: 20rpx;" v-if="brandList.length>0">
								<view class="title flex_row_between_center">
									<text class="left">{{$L('品牌')}}</text>
									<text v-if="brandList.length>brandLimit||brandLimit==100"
										:class="{iconfont:true, iconziyuan11:true,up:brandFlag=='up',down:brandFlag=='down',}"
										@click="viewMoreBrand"></text>
									<view v-if="brandList.length>brandLimit||brandLimit==100" class="attributr_sel_con">
										<text class="attributr_sel_text" v-for="(item,index) in selectBrandText"
											:key="index">{{item}}
											<text v-if="(index+1)!=selectBrandText.length">,</text></text>
									</view>
								</view>
								<view class="detail flex_row_start_center">
									<template v-for="(item,index) in brandList">
										<view v-if='index<brandLimit' :key="index"
											:class="{item:true, sel:searchParams.brandId.indexOf(item.brandId)>-1}"
											@click="selBrand(item.brandId,item.brandName)"><text
												class="brand_name">{{item.brandName}}</text></view>
									</template>
								</view>
							</view>
							<!-- 商品属性 -->
							<view class="part" v-for="(attribute,index) in attributeList" style="margin-top: 20rpx;"
								v-if="attributeList.length>0" :key="index">
								<view class="title flex_row_between_center">
									<text class="left">{{attribute.attributeName}}</text>
									<!-- <text  :class="{iconfont:true, iconziyuan11:true,up:arrowFlag=='up',down:arrowFlag=='down',}" -->
									<text
										v-if="attribute.attributeValueList.length>attribute.showLimit||attribute.showLimit==100"
										:class="{iconfont:true, iconziyuan11:true,up:attribute.showFlag=='up',down:attribute.showFlag=='down',}"
										@click="viewMoreAttridute(attribute)"></text>
									<view
										v-if="attribute.attributeValueList.length>attribute.showLimit||attribute.showLimit==100"
										class="attributr_sel_con">
										<text class="attributr_sel_text"
											v-for="(item,index) in attribute.select_text_list"
											:key="index">{{item}}<text
												v-if="(index+1)!=attribute.select_text_list.length">,</text></text>
									</view>
								</view>
								<view class="detail flex_row_start_center">
									<view v-for="(item,index1) in attribute.attributeValueList" :key="index1">
										<view v-if="index1<attribute.showLimit"
											:class="{item:true, sel:attribute.select_text_list.indexOf(item.attributeValue)>-1}"
											@click="selAttribude(attribute,item.attributeValue,item.valueId)"><text
												class="brand_name">{{item.attributeValue}}</text></view>
									</view>
									<!-- <view v-if='brandLimit==17' :class="{item:true}" @click="viewBrand"><text class="brand_name">查看全部品牌</text></view> -->
								</view>
							</view>

						</view>
					</scroll-view>
					<view class="bottom flex_row_end_center">
						<text class="btn reset flex_row_center_center" @click="resetSel">{{$L('重置')}}</text>
						<text class="btn confirm flex_row_center_center" @click="confirmSel">{{$L('确认')}}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	import filters from "@/utils/filter.js"
	import goodsListItemV from "../components/goods_list_item_v.vue";
	import storeItem from "../components/store_item.vue";
	import goodsItemH from "../components/goods_item_h.vue";
	import loadingState from "@/components/loading-state.vue";
	import {mapState} from 'vuex'
	export default {
		components: {
			goodsListItemV,
			goodsItemH,
			loadingState,
			storeItem
		},
		
		data() {
			return {
				midPrice: '', //筛选切换用
				imgUrl: getApp().globalData.imgUrl,
				source: '', //页面来源（从哪个页面进入的），如果是搜素页，点击该页顶部搜素的时候会返回上级页面，否则会跳转
				searchParams: {
					brandId: [], //选中的品牌，多选
					categoryIds: '', //选中的分类
					lowPrice: '', //筛选的最低价
					highPrice: '', //筛选的最高价
					attribudeId: '',
					keyword: '',
					storeId: '',
					goodsIds: '',
					cateId: ''
				}, //搜索的参数
				stopPullDownRefresh: false, //是否下拉刷新中
				cateMaskState: 0, //分类面板展开状态
				headerPosition: "fixed",
				filterIndex: 0,
				priceOrder: 0, //1 价格从低到高 2价格从高到低
				cateList: [], //分类列表
				brandList: [], //品牌列表
				brandLimit: 6, //筛选条件展示
				cateLimit: 6, //筛选条件展示
				arrowFlag: '',
				goodsList: [],
				loadingState: 'first_loading',
				pageSize: 10,
				current: 1,
				loading: false, //是否加载数据
				hasMore: true, //是否还有数据
				gridFlag: false, //是否网格形式展示，默认item形式展示
				options: {},
				pricearea: true,
				seleindex: 0,
				store_list: [],
				store_info: {
					pageSize: 10,
					current: 1,
					hasMore: false
				},
				showStoreList: false,
				attributeList: [], //商品属性列表
				cateFlag: 'up',
				brandFlag: 'up',
				selectCateText: '', //已选分类名称
				selectBrandText: [], //已选品牌列表
				keyword: '',
				filters
			};
		},

		watch: {
			cateMaskState() {
				if (this.cateMaskState == 1) {
					this.searchParams.cateId = this.searchParams.categoryIds;
				}
			},
		},

		computed: {
			...mapState(['userCenterData','locationObj']),
		waterFall() {
			let newArray = [];
			let len = this.goodsList.length / 2
			const list1 = this.goodsList.filter((item,index)=>index%2==0)
			newArray.push(list1)
			const list2 = this.goodsList.filter((item,index)=>index%2==1)
			newArray.push(list2)
			return newArray;
		}
		},

		onLoad(options) {
			this.options = options;
			if (this.$Route.query.keyword) { //关键词
				this.searchParams.keyword = decodeURIComponent(decodeURIComponent(this.$Route.query.keyword))
			}
			if (this.$Route.query.goodsIds) { //关键词
				this.searchParams.goodsIds = decodeURIComponent(this.$Route.query.goodsIds)
			}
			if (this.$Route.query.storeId) { //店铺id
				this.searchParams.storeId = decodeURIComponent(this.$Route.query.storeId)
			}
			if (this.$Route.query.categoryId) { //接受分类
				this.searchParams.categoryIds = this.$Route.query.categoryId;
				if (!this.$Route.query.source) { //如果是优惠券进来的分类，不调取筛选条件中的品牌等
					this.getcateList(this.$Route.query.categoryId);
				}
			}
			if (this.$Route.query.brandId) { //品牌id
				let {brandId} = this.$Route.query
				if(brandId.toString().indexOf(",")>-1){
					this.searchParams.brandId = decodeURIComponent(brandId).split(",")
				}else{
					this.searchParams.brandId = [brandId]
				}
			}
			if (this.$Route.query.storeInnerLabelId) { //店铺内分类id
				this.searchParams.storeInnerLabelId = decodeURIComponent(this.$Route.query.storeInnerLabelId)
			}

			if (this.$Route.query.lowPrice) { //店铺内分类id
				this.searchParams.lowPrice = this.$Route.query.lowPrice
			}

			this.getGoodsList();

			uni.$on('uploadCollect', (e) => {
				let followNumber = this.store_list[e.index].followNumber;
				followNumber = (e.state ? followNumber + 1 : followNumber - 1)
				this.store_list[e.index].followNumber = followNumber;
			})
		},

		onShow() {},

		onUnload() {
			uni.$off('uploadCollect')
		},

		onPageScroll(e) {
			//兼容iOS端下拉时顶部漂移
			if (e.scrollTop >= 0) {
				this.headerPosition = "fixed";
			} else {
				this.headerPosition = "absolute";
			}
		},
		//下拉刷新
		onPullDownRefresh() {
			this.current = 1;
			this.stopPullDownRefresh = true; //下拉刷新状态
			this.getGoodsList();
		},
		methods: {
			changeSort(index) {
				this.seleindex = index
				this.filterIndex = index
				this.pricearea = !this.pricearea
				this.current = 1
				this.getGoodsList()
			},
			getGoodsList() {


				if (this.current == 1) {
					uni.showLoading({
						title: this.$L('加载中'),
						icon: 'none'
					})

				}

				//处理属性选择
				let attributeInfo = []
				this.attributeList.map(item => {
					if (item.select_list.length > 0) {
						item.select_list.map(item => {
							attributeInfo.push(item)
						})
					}
				})
				const { location } = this.locationObj;
				let param = {};
				param.url = 'v3/goods/front/goods/goodsList';
				param.data = {};
				param.data.pageSize = this.pageSize;
				param.data.current = this.current;
				param.data.sort = this.filterIndex;
				const locationArr = location?location.split(','):[];
				if(locationArr.length > 0){
					const lng = locationArr[0];
					const lat = locationArr[1];
					param.data.lng = lng;
					param.data.lat = lat;
				}
				

				if (this.searchParams.categoryIds) {
					param.data.categoryIds = this.searchParams.categoryIds
				}

				if (attributeInfo.length > 0) {
					param.data.attributeInfo = attributeInfo.join(',')
				}
				if (this.searchParams.highPrice) {
					param.data.highPrice = this.searchParams.highPrice
				}
				if (this.searchParams.lowPrice) {
					param.data.lowPrice = this.searchParams.lowPrice;
				}

				if (this.searchParams.lowPrice && this.searchParams.highPrice) {
					if (Number(this.searchParams.lowPrice) > Number(this.searchParams.highPrice)) {
						let tmp = this.searchParams.lowPrice
						this.searchParams.lowPrice = this.searchParams.highPrice
						this.searchParams.highPrice = tmp
						param.data.highPrice = this.searchParams.highPrice
						param.data.lowPrice = this.searchParams.lowPrice
					}
				} else if (!this.searchParams.lowPrice && this.searchParams.highPrice) {
					delete this.searchParams.lowPrice
				} else if (this.searchParams.lowPrice && !this.searchParams.highPrice) {
					delete this.searchParams.highPrice
				}


				if (this.searchParams.keyword) {
					param.data.keyword = this.searchParams.keyword
				}
				if (this.searchParams.storeId) {
					param.data.storeId = this.searchParams.storeId
				}
				if (this.searchParams.goodsIds) {
					param.data.goodsIds = this.searchParams.goodsIds
				}
				if (this.searchParams.brandId.length) {
					param.data.brandIds = this.searchParams.brandId
				}
				if (this.searchParams.storeInnerLabelId) {
					param.data.storeInnerLabelId = this.searchParams.storeInnerLabelId
				}
				this.loadingState = this.loadingState == 'first_loading' ? this.loadingState : 'loading';
				this.$request(param).then(res => {
					if (res.state == 200) {
						if (this.current == 1) {
							this.goodsList = res.data.list;
						} else {
							this.goodsList = this.goodsList.concat(res.data.list);
						}
						this.hasMore = this.$checkPaginationHasMore(res.data.pagination); //是否还有数据
						if (this.hasMore) {
							this.current++;
							this.loadingState = 'allow_loading_more';
						} else {
							this.loadingState = 'no_more_data';
						}
						uni.hideLoading()
					} else {
						uni.hideLoading()
						//错误提示
					}
					if (this.stopPullDownRefresh) {
						this.stopPullDownRefresh = false;
						uni.stopPullDownRefresh();
					}
				})
			},

			//加载更多事件
			getMoreData() {
				if (this.hasMore) {
					this.getGoodsList();
				}

			},
			getMoreStoreData() {
				if (this.store_info.hasMore) {
					this.getShopList();
				}
			},
			//切换页面展示风格  
			tabShowStyle() {
				this.gridFlag = !this.gridFlag;
			},

			//加载商品 ，带下拉刷新和上滑加载
			async loadData(type = 'add', loading) {
				//没有更多直接返回
				if (type === 'add') {
					if (this.loadingType === 'nomore') {
						return;
					}
					this.loadingType = 'loading';
				} else {
					this.loadingType = 'more'
				}

				let goodsList = await this.$api.json('goodsList');
				if (type === 'refresh') {
					this.goodsList = [];
				}
				//筛选，测试数据直接前端筛选了
				if (this.filterIndex === 1) {
					goodsList.sort((a, b) => b.sales - a.sales)
				}
				if (this.filterIndex === 2) {
					goodsList.sort((a, b) => {
						if (this.priceOrder == 1) {
							return a.price - b.price;
						}
						return b.price - a.price;
					})
				}

				this.goodsList = this.goodsList.concat(goodsList);

				//判断是否还有下一页，有是more  没有是nomore(测试数据判断大于20就没有了)
				this.loadingType = this.goodsList.length > 20 ? 'nomore' : 'more';
				if (type === 'refresh') {
					if (loading == 1) {
						uni.hideLoading()
					} else {
						uni.stopPullDownRefresh();
					}
				}
			},
			//筛选点击
			tabClick(index) {
				if (this.filterIndex === index) {
					return;
				}
				this.filterIndex = index;
				//根据筛选条件重新请求
				this.loadingState = 'first_loading';
				this.goodsList = [];
				this.current = 1;
				if (index == 2) {
					this.getShopList()
					this.showStoreList = true
				} else {
					this.getGoodsList();
					this.showStoreList = false
				}

			},

			//获取店铺列表
			getShopList(index) {
				uni.showLoading({
					title: this.$L('加载中'),
					icon: 'none'
				})
				let param = {};
				param.url = 'v3/seller/front/store/list';
				param.data = {};
				param.data.pageSize = this.store_info.pageSize;
				param.data.current = this.store_info.current;
				param.data.keyword = this.searchParams.keyword
				param.data.categoryId = this.searchParams.categoryIds
				this.loadingState = this.loadingState == 'first_loading' ? this.loadingState : 'loading';
				this.$request(param).then(res => {
					if (res.state == 200) {
						if (this.store_info.current == 1) {
							this.store_list = res.data.list;
						} else {
							this.store_list = this.store_list.concat(res.data.list);
						}
						this.store_info.hasMore = this.$checkPaginationHasMore(res.data.pagination); //是否还有数据
						if (this.store_info.hasMore) {
							this.store_info.current++;
							this.loadingState = 'allow_loading_more';
						} else {
							this.loadingState = 'no_more_data';
						}
						uni.hideLoading()
					} else {
						uni.hideLoading()
						this.$api.msg(res.msg)
						//错误提示
					}
					if (this.stopPullDownRefresh) {
						this.stopPullDownRefresh = false;
						uni.stopPullDownRefresh();
					}
				})

			},

			showSelect() {
				const {
					pricearea
				} = this;
				this.pricearea = !pricearea
			},
			//显示分类面板
			toggleCateMask(type) {
				let timer = type === 'show' ? 10 : 300;
				let state = type === 'show' ? 1 : 0;
				this.cateMaskState = 2;
				this.pricearea = true
				setTimeout(() => {
					this.cateMaskState = state;
				}, timer)
			},

			//详情
			navToDetailPage(item) {
				//测试数据没有写id，用title代替
				let id = item.title;
				this.$Router.push({
					path: '/pages/product/product',
					query: {
						id
					}
				})
			},
			stopPrevent() {},
			//点击顶部搜索事件
			search() {
				let pages = getCurrentPages(); //获取所有页面栈实例列
				let prevPage = pages[pages.length - 2]; //上一页页面实例
				if (prevPage && prevPage.route == 'pages/search/search') {
					prevPage.$vm.input_val = this.searchParams.keyword;
					this.$Router.back(1)
				} else {
					this.$Router.push('/pages/search/search')
				}
			},
			navBack() {
				this.$Router.back(1)
			},
			//查看更多品牌
			viewMoreBrand() {
				let curFlag = this.brandFlag;
				if (curFlag == '' || curFlag == 'down') {
					this.brandFlag = 'up';
				} else if (curFlag == 'up') {
					this.brandFlag = 'down';
				}
				this.brandLimit = this.brandLimit == 6 ? 100 : 6
			},
			//查看更多分类
			viewMoreCate() {
				let curFlag = this.cateFlag;
				if (curFlag == '' || curFlag == 'down') {
					this.cateFlag = 'up';
				} else if (curFlag == 'up') {
					this.cateFlag = 'down';
				}
				this.cateLimit = this.cateLimit == 6 ? 100 : 6
			},
			//查看更多属性
			viewMoreAttridute(attribute) {
				let curFlag = attribute.showFlag;
				if (curFlag == '' || curFlag == 'down') {
					attribute.showFlag = 'up';
				} else if (curFlag == 'up') {
					attribute.showFlag = 'down';
				}
				attribute.showLimit = attribute.showLimit == 6 ? 100 : 6
			},

			//选择分类
			selCat(type, id, name) {
				this.selectCateText = name
				if (this.searchParams[type] == id) {
					//选中的话取消选中
					this.searchParams[type] = '';
				} else {
					this.searchParams[type] = id;
				}
				this.getcateList(id)
			},
			//选择品牌
			selBrand(id, name) {
				let tmp_data = this.searchParams.brandId;
				let position = tmp_data.indexOf(id);
				if (position > -1) {
					//选中的话取消选中
					this.searchParams.brandId.splice(position, 1);
					this.selectBrandText.splice(position, 1)
				} else {
					if (this.searchParams.brandId == 5) {
						uni.showToast({
							title: this.$L('品牌最多选择五条'),
							icon: 'none'
						})
						return
					}
					this.selectBrandText.push(name)
					this.searchParams.brandId.push(id);
				}
			},
			//选择属性
			selAttribude(attribute, attributeValue, attributeValueId) {
				let attributedata = ''
				let position = attribute.select_text_list.indexOf(attributeValue);
				if (position > -1) {
					//选中的话取消选中
					attribute.select_text_list.splice(position, 1);
					attribute.select_list.splice(position, 1)
				} else {
					if (attribute.select_text_list.length == 5) {
						uni.showToast({
							title: this.$L('属性值最多选择五条'),
							icon: 'none'
						})
						return
					}
					attributedata = attribute.attributeName + '_' + attributeValue

					attribute.select_list.push(attributeValueId)
					attribute.select_text_list.push(attributeValue)
				}
			},
			//根据二级分类获取三级分类
			getcateList(id) {
				this.$request({
					url: 'v3/goods/front/goods/category/screenList',
					data: {
						categoryId: id
					},
				}).then(res => {
					if (res.state == 200) {
						if (res.data.categoryList != null) {
							this.cateList = res.data.categoryList;
							this.cateList.map(item => {
								if (item.categoryId == this.searchParams.categoryIds) {
									this.selectCateText = item.categoryName
								}
							})
						}
						if (res.data.brandList != null) {
							this.brandList = res.data.brandList;
						}

						if (res.data.attributeList != null) {
							this.attributeList = res.data.attributeList;
							this.attributeList.map(item => {
								this.$set(item, 'select_list', [])
								this.$set(item, 'select_text_list', [])
								this.$set(item, 'showFlag', 'up')
								this.$set(item, 'showLimit', 6)
							})
						}
					} else {
						this.$api.msg(res.msg);
					}
				}).catch((e) => {
					//异常处理
				})
			},
			//重置筛选条件
			resetSel() {
				this.searchParams.categoryIds = this.$Route.query.categoryId; //路径里带过来的
				this.cateMaskState = 0
				this.searchParams.lowPrice = ''
				this.searchParams.highPrice = ''
				this.searchParams.brandId = []
				this.selectBrandText = []
				this.attributeList.map(item => {
					item.select_text_list = []
					item.select_list = []
				})
				this.getGoodsList();
			},
			//弹层的确认事件
			confirmSel() {
				this.searchParams.categoryIds = this.searchParams.cateId;
				//关闭弹层
				this.toggleCateMask();
				this.goodsList = [];
				this.current = 1;
				this.getGoodsList();
			},
			//价格输入事件
			inputPrice(key, e) {

				this.searchParams[key] = e.detail.value;
			},


			blurPrice(key) {
				let tmp = String(this.searchParams[key]).split('.')
				if (tmp[1] && tmp[1].length >= 2) {
					this.searchParams[key] = Number(this.searchParams[key]).toFixed(2);
					return
				}
			},




			//清空输入框的值
			clearInput(e) {
				this.searchParams.keyword = '';
				this.$forceUpdate();
				const pages = getCurrentPages();
				const prevPage = pages[pages.length - 2];
				if (prevPage) {
					prevPage.$vm.input_val = '';
					prevPage.$vm.SAList = []
				}
				this.$Router.back(1)
			},
		},
	}
</script>

<style lang="scss">
	page,
	.content {
		background: $bg-color-split;
		display: flex;
		width: 750rpx;
		flex: 1;
		margin: 0 auto;
	}

	uni-page-body {
		display: flex;
		height: 100%;
	}

	.sea_input_part {
		position: fixed;
		display: flex;
		align-items: center;
		height: 88rpx;
		background-color: #fff;
		width: 750rpx;
		padding-right: 20rpx;
		top: 0;
		right: 0;
		left: 0;
		margin: 0 auto;
		//app-1-start
		/* #ifdef APP-PLUS  */
		top: var(--status-bar-height);
		/* #endif */
		//app-1-end
		.back_icon {
			padding-left: 20rpx;
		}

		.sea_input {
			flex: 1;
			height: 65rpx;
			font-size: 28rpx;
			color: #333;
			background-color: #f5f5f5;
		}

		.search_center {
			display: flex;
			align-items: center;
			border: none;
			flex: 1;
			height: 65rpx;
			margin-left: 20rpx;
			padding-left: 20rpx;
			overflow: hidden;
			border-radius: 32.5rpx;
			background-color: #f5f5f5;

			.search_icon {
				width: 31rpx;
				height: 31rpx;
				margin-right: 13rpx;
				margin-top: 2rpx;
			}
		}

		.clear_content {
			width: 45rpx !important;
			height: 45rpx !important;
			margin-right: 15rpx !important;
		}

		.sea_btn {
			font-size: 28rpx;
			color: #2D2D2D;
			padding: 10rpx 25rpx;
			flex-shrink: 0;
		}

		&:after {
			position: absolute;
			content: '';
			left: 0;
			bottom: 0;
			width: 100%;
			height: 1rpx;
			background-color: #eee;
			transform: scaleY(0.5);
		}
	}

	.top_part {
		top: var(--window-top);
		position: fixed;
		z-index: 9;
	}

	.navbar {
		position: fixed;
		left: 0;
		right: 0;
		margin: 0 auto;
		top: var(--window-top);
		display: flex;
		width: 750rpx;
		height: 80rpx;
		background: #fff;
		z-index: 10;
		margin-top: 87rpx;
		//app-2-start
		/* #ifdef APP-PLUS  */
		margin-top: calc(var(--status-bar-height) + 87rpx);
		/* #endif */
		//app-2-end
		.complex .iconfont {
			font-size: 16rpx;
			transform: scale(0.5, 0.5);
		}

		.nav-item {
			flex: 1;
			display: flex;
			justify-content: center;
			align-items: center;
			height: 100%;
			font-size: 30upx;
			color: $font-color-dark;
			position: relative;

			.iconfont {
				color: $main-font-color;

				&.active {
					color: var(--color_main);
				}
			}

			.synthesize_icon {
				font-size: 16rpx;
				transform: scale(0.5, 0.5)
			}

			.filter_icon {
				font-size: 24rpx;
				margin-left: 9rpx;
				transform: scale(0.8, 0.8);
				margin-top: 6rpx;
			}

			&.current {
				// color: var(--color_main);
				color: var(--color_price) !important;

				.iconziyuan14 {
					color: var(--color_price) !important
				}

				&:after {
					content: '';
					position: absolute;
					left: 50%;
					bottom: 0;
					transform: translateX(-50%);
					width: 120upx;
					height: 0;
				}
			}
		}

		.p-box {
			display: flex;
			flex-direction: column;
			font-size: 16rpx;
			transform: scale(0.3, 0.3);
			margin-left: -9rpx;

			.price_up {
				margin-bottom: 4rpx;
			}

			.price_down {
				margin-top: 4rpx;
			}
		}

		.cate_item {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 100%;
			width: 80upx;
			position: relative;
			font-size: 55rpx;
			color: $main-font-color;

			&.grid_icon {
				font-size: 36rpx;
			}

			&:after {
				content: '';
				position: absolute;
				left: 0;
				top: 50%;
				transform: translateY(-50%);
				border-left: 1px solid rgba(0, 0, 0, .1);
				width: 0;
				height: 36upx;
			}
		}
	}

	.goods_part {
		position: absolute;
		top: 166rpx;
		height: calc(100vh - 168rpx);
		//app-3-start
		/* #ifdef APP-PLUS  */
		top: calc(var(--status-bar-height) + 168rpx);
		/* #endif */
		//app-3-end
		/* #ifdef APP-PLUS||MP */
		padding-bottom: constant(safe-area-inset-bottom);
		/*兼容 IOS<11.2*/
		padding-bottom: env(safe-area-inset-bottom);
		/*兼容 IOS>11.2*/
		/* #endif */

	}

	.goods_list {
		flex-wrap: wrap;
		width: 750rpx;
	}

	.goods_list_another {
		margin-top: 20rpx;
		flex-wrap: wrap;
		width: 750rpx;
	}


	/* 分类 */
	.wrap {
		display: flex;
		flex-direction: column;
		height: 100%;
	}

	.cate-mask {
		position: fixed;
		left: 0;
		right: 0;
		margin: 0 auto;
		top: 0;
		//app-4-start
		/* #ifdef APP-PLUS */
		top: var(--status-bar-height);
		/* #endif */
		//app-4-end
		bottom: 0;
		width: 750rpx;
		background: rgba(0, 0, 0, 0);
		z-index: 90;
		transition: .3s;
		overflow: hidden;

		.cate-content {
			width: 600rpx;
			height: 100%;
			background: $bg-color-split;
			float: right;
			transform: translateX(100%);
			transition: .3s;

			.bottom {
				position: fixed;
				left: 0;
				bottom: 0;
				right: 0;
				height: 100rpx;
				background: #fff;
				box-shadow: 0px 0px 15rpx 0px rgba(86, 86, 86, 0.1);

				.btn {
					width: 150rpx;
					height: 50rpx;
					border-radius: 25rpx;
				}

				.reset {
					border: 1rpx solid #ADADAD;
					color: #333333;
					font-size: 26rpx;
				}

				.confirm {
					background: var(--color_main);
					// box-shadow: 0px 3rpx 15rpx 0px rgba(215, 163, 53, 0.2);
					color: #fff;
					font-size: 26rpx;
					margin-right: 30rpx;
					margin-left: 20rpx;
				}
			}
		}

		&.none {
			display: none;
		}

		&.show {
			background: rgba(0, 0, 0, .4);

			.cate-content {
				transform: translateX(0);
			}
		}
	}

	.cate-list {
		height: calc(100vh - 98rpx - 100rpx);
		//app-5-start
		/* #ifdef APP-PLUS */
		height: calc(100vh - 98rpx - 110rpx - var(--status-bar-height));
		/* #endif */
		//app-5-end
		.part {
			padding: 30rpx;
			background: #fff;

			.title {
				margin-bottom: 10rpx;

				.left {
					color: $main-font-color;
					font-size: 28rpx;
					font-weight: bold;
				}

				.iconfont {
					font-size: 16rpx;
					transform: rotate(90deg);
					-ms-transform: rotate(90deg);
					/* IE 9 */
					-webkit-transform: rotate(90deg);

					/* Safari and Chrome */
					&.up {
						-webkit-animation: moveUp 0.6 ease-in-out 0.2s forwards;
					}

					&.down {
						-webkit-animation: moveDown 0.6 ease-in-out 0.2s forwards;
					}

					@-webkit-keyframes moveUp {
						0% {
							-webkit-transform: rotate(90deg);
						}

						100% {
							-webkit-transform: rotate(360deg);
						}
					}

					@-webkit-keyframes moveDown {
						0% {
							-webkit-transform: rotate(360deg);
						}

						100% {
							-webkit-transform: rotate(495deg);
						}
					}
				}
			}

			.detail {
				flex-wrap: wrap;
				position: relative;

				&.price_area:after {
					position: absolute;
					content: '';
					width: 25rpx;
					height: 1rpx;
					background-color: rgba(45, 45, 45, .4);
					margin: 10rpx 38rpx 0 258rpx;
				}

				.item {
					display: flex;
					justify-content: center;
					align-items: center;
					background-color: $bg-color-split;
					height: 70rpx;
					border-radius: 35rpx;
					width: 167rpx;
					color: #2D2D2D;
					font-size: 22rpx;
					margin-right: 14rpx;
					margin-top: 20rpx;
					line-height: 70rpx;
					overflow: hidden;

					.brand_name {
						max-width: 147rpx;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}

					&:nth-child(3n+3) {
						margin-right: 0;
					}

					&.sel {
						color: var(--color_main);
						background-color: var(--color_halo);
					}
				}

				.amount {
					margin-top: 20rpx;
					text-align: center;
					width: 220rpx;
					height: 70rpx;
					background: $bg-color-split;
					border-radius: 35rpx;
					font-size: 24rpx;
					color: #2D2D2D;

					&.hight {
						margin-left: 100rpx;
					}
				}

				.price_input_placeholder {
					color: $main-third-color;
					font-size: 22rpx;
					text-align: center;
				}
			}

			&.price_part {
				// flex: 1;
				// padding-bottom: 130rpx;
			}
		}
	}

	/* 商品列表 */
	.goods-list {
		display: flex;
		flex-wrap: wrap;
		padding: 0 30upx;
		background: #fff;

		.goods-item {
			display: flex;
			flex-direction: column;
			width: 48%;
			padding-bottom: 40upx;

			&:nth-child(2n+1) {
				margin-right: 4%;
			}
		}

		.image-wrapper {
			width: 100%;
			height: 330upx;
			border-radius: 3px;
			overflow: hidden;

			image {
				width: 100%;
				height: 100%;
				opacity: 1;
			}
		}

		.title {
			font-size: $font-lg;
			color: $font-color-dark;
			line-height: 80upx;
		}

		.price-box {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding-right: 10upx;
			font-size: 24upx;
			color: $font-color-light;
		}

		.price {
			font-size: $font-lg;
			color: $uni-color-primary;
			line-height: 1;

			&:before {
				content: '￥';
				font-size: 26upx;
			}
		}
	}

	.empty_part {
		margin-top: 276rpx;
		width: 100%;

		image {
			width: 380rpx;
			height: 280rpx;
		}

		text {
			color: $main-third-color;
			font-size: 26rpx;
			margin-top: 57rpx;
		}

		button {
			width: 245rpx;
			height: 66rpx;
			background: rgba(252, 28, 28, .05);
			border-radius: 33rpx;
			color: var(--color_main);
			font-size: 30rpx;
			font-weight: bold;
			margin-top: 29rpx;
		}

		uni-button:after {
			border-radius: 200rpx;
			border-color: #fff;
		}
	}

	.fixed_top_status_bar {
		position: fixed;
		//app-6-start
		/* #ifdef APP-PLUS */
		height: var(--status-bar-height);
		/* #endif */
		//app-6-end
		/* #ifndef APP-PLUS */
		height: 0;
		/* #endif */
		top: 0;
		left: 0;
		right: 0;
		z-index: 99;
		background: #fff;
	}

	.pricearea {
		position: fixed;
		//app-7-start
		/* #ifdef APP-PLUS */
		top: var(--status-bar-height);
		/* #endif */
		//app-7-end
		/* #ifndef APP-PLUS */
		top: 0;
		/* #endif */
		left: 0;
		right: 0;
		width: 750rpx;
		height: 100vh;
		padding-top: 159rpx;
		box-sizing: border-box;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 8;
		margin: 0 auto;
	}

	.pricearea .pri-content {
		padding: 25rpx 25rpx 15rpx 36rpx;
		background-color: #F8F8F8;
		border-radius: 0 0 14rpx 14rpx;
		z-index: 100;
	}

	.pricearea .pri-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 80rpx;
		color: #2D2D2D;
		font-size: 28rpx;
	}

	.pricearea .pri-item.current {
		color: var(--color_price);
	}

	.pricearea .pri-item.current image {
		width: 23rpx;
		height: 16rpx;
	}

	.shop_lists {
		//app-8-start
		/* #ifdef APP-PLUS */
		margin-top: calc(168rpx + var(--status-bar-height));
		/* #endif */
		//app-8-end
		/* #ifndef APP-PLUS */
		margin-top: 168rpx;
		/* #endif */

		width: 100%;
		background: #F5F5F5;
		padding: 20rpx 0;
		box-sizing: border-box;
		height: 100%;


	}



	.shop_pre {
		width: 710rpx;
		background: rgba(255, 255, 255, 1);
		border-radius: 15rpx;
		margin: 0 auto;
		box-sizing: border-box;
		margin-bottom: 20rpx;
		padding: 0 21rpx;
	}

	.pre_top {
		display: flex;
		width: 100%;
		height: 140rpx;
		justify-content: space-between;
		align-items: center;
	}

	.shop_left {
		display: flex;
		align-items: center;
	}

	.shop_avatar_img {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		margin-right: 20rpx;
		background: #F8F8F8;
	}

	.shop_avatar {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		margin-right: 20rpx;
	}

	.shop_des {
		display: flex;
		flex-direction: column;
	}

	.des_top {
		display: flex;
		margin-bottom: 20rpx;
	}

	.shop_name {
		max-width: 323rpx;
		font-size: 30rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: rgba(45, 45, 45, 1);
		line-height: 32rpx;
		white-space: nowrap;
		text-overflow: ellipsis;
		overflow: hidden;
		word-break: break-all;
	}

	.shop_type {
		width: 56rpx;
		height: 30rpx;
		background: var(--color_main);
		border-radius: 15rpx;
		font-size: 22rpx;
		font-family: PingFang SC;
		font-weight: 600;
		color: #FFFFFF;
		line-height: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.des_bottom {
		display: flex;
	}

	.popularity {
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 600;
		color: #999999;
		line-height: 32rpx;
		margin-left: 10rpx;
	}

	.line {
		height: 24rpx;
		color: #999999;
		font-size: 24rpx;
		margin: 0 15rpx;
	}

	.payer_number {
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 600;
		color: #999999;
		line-height: 32rpx;
	}

	.go_shop {
		width: 100rpx;
		height: 50rpx;
		border: 1rpx solid #FF0000;
		border-radius: 25rpx;
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: 600;
		color: #FB1B1B;
		line-height: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.pre_content {
		display: flex;
	}

	.commodity {
		width: 216rpx;
		height: 216rpx;
		position: relative;
		margin-right: 10rpx;
		margin-bottom: 21rpx;
		background: #F5F5F5;
		border-radius: 10rpx;
	}

	.commodity:nth-of-type(3) {
		margin-right: 0;
	}

	.commodity_images {
		width: 216rpx;
		height: 216rpx;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
		background: #F8F8F8;
	}

	.commodity_img {
		width: 216rpx;
		height: 216rpx;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
	}

	.commodity_price {
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 600;
		color: #FFFFFF;
		line-height: 24rpx;
		position: absolute;
		width: 100%;
		bottom: 0;
		z-index: 10;
		text-align: center;
		height: 36rpx;
		background: rgba(0, 0, 0, 0.3);
		border-radius: 0 0 10rpx 10rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.commodity_price text:nth-child(1),
	.commodity_price text:nth-last-child(1) {
		font-size: 20rpx;
	}

	.filter_title {
		margin-top: 40rpx;
		margin: 28rpx;
	}

	.default {
		color: #dddddd !important;
	}

	.default .iconfont {
		color: #dddddd !important;
	}

	.attributr_sel_con {
		position: absolute;
		right: 65rpx;
		width: 370rpx;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		color: red;
		text-align: right;
		font-size: 18rpx;

	}
</style>