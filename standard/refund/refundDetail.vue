<!-- 退款详情页面 -->
<template>
	<scroll-view class="container" scroll-y v-if="allData && isShow" :style="mix_diyStyle">
		<view class="main_content">
			<!--
				退货退款状态：100-买家申请仅退款；101-买家申请退货退款；102-买家退货给商家；200-商家同意退款申请；201-商家同意退货退款申请；202-商家拒绝退款申请(退款关闭/拒收关闭)；203-商家确认收货；300-平台确认退款(已完成)
				afsType 售后服务端类型，1-退货退款单（需关联处理退款金额），2-换货单，3-仅退款单
			-->

			<!-- 退款状态 start -->
			<view class="order_state_box">
				<view class="order_state">
					<!-- 等待商家处理 待发货 - 退款中 start-->
					<block v-if="allData.state == 100 || allData.state == 101">
						<view class="state_title">
							<image :src="imgUrl+'order_new/handle.png'" mode="aspectFit"></image>
							<text>{{$L('等待商家处理')}}</text>
						</view>
						<view class="state_time" v-if="deadline">{{$L('还剩')}}{{deadline}}{{$L('自动确认')}}</view>
					</block>
					<!-- 等待商家处理 待发货 - 退款中 end-->

					<!-- 等待商家收货  start-->
					<block v-if="allData.state == 102">
						<view class="state_title">
							<image :src="imgUrl+'order_new/handle.png'" mode="aspectFit"></image>
							<text>{{$L('等待商家收货')}}</text>
						</view>
						<view class="state_time" v-if="deadline">{{$L('还剩')}}{{deadline}}{{$L('自动确认')}}</view>
					</block>
					<!-- 等待商家收货  end-->

					<!-- 商家同意退款申请  start-->
					<block v-if="allData.state == 200">
						<view class="state_title">
							<image :src="imgUrl+'order_new/handle.png'" mode="aspectFit"></image>
							<text>{{$L('等待平台审核退款')}}</text>
						</view>
					</block>
					<!-- 商家同意退款申请  end-->

					<!-- 卖家同意申请  start   -->
					<block v-if="allData.state == 201">
						<view class="state_title">
							<image :src="imgUrl+'order_new/apply.png'" mode="aspectFit"></image>
							<view class="">
								<text>{{allData.stateValue}}</text>
								<view class="sub_text">{{$L('请退货并填写物流信息')}}</view>
							</view>

						</view>
						<view class="state_time" v-if="deadline">{{$L('还剩')}}{{deadline}}{{$L('自动取消退款申请')}}</view>
					</block>
					<!-- 卖家同意申请  end-->

					<!-- 卖家拒绝申请  start-->
					<block v-if="allData.state == 202">
						<view class="state_title">
							<!-- <image :src="imgUrl+'goods_detail/poster_share_close.png'" mode="aspectFit"></image> -->
							<image :src="imgUrl+'order_new/trade_close.png'" mode="aspectFit"></image>
							<text>{{allData.stateValue}}</text>
						</view>
						<view class="state_time refund_reason" v-if="allData.refuseReason">{{allData.refuseReason}}
						</view>
					</block>
					<!-- 卖家拒绝申请  end-->

					<!-- 商家确认收货 等待平台同意 start-->
					<block v-if="allData.state == 203">
						<view class="state_title">
							<image :src="imgUrl+'order_new/handle.png'" mode="aspectFit"></image>
							<text>{{allData.stateValue}}</text>
						</view>
					</block>
					<!-- 商家确认收货  end-->

					<!-- 退款退货  退款成功 -->
					<block v-if="allData.state == 300">
						<view class="state_title">
							<image :src="imgUrl+'order_new/success.png'" mode="aspectFit"></image>
							<text>{{$L('退款成功')}}</text>
						</view>
						<view class="state_time" v-if="allData.completeTime">{{allData.completeTime}}</view>
					</block>
				</view>
				<!-- 退款状态 end -->

				<!-- 退货地址 及 换货地址 -->
				<view class="refund_address"
					v-if="allData.storeAddress && (allData.afsType == 1 || allData.afsType == 2)">
					<svgGroup type="location" width="34" height="38" px="rpx" :color="diyStyle_var['--color_main']"
						class="refund_address_map"></svgGroup>
					<view class="refund_address_des">
						<text class="refund_address_title">{{sourceType == '' ? '退' : '换'}}{{$L('货地址')}}</text>
						<view class="refund_address_con">
							<text>{{allData.storeContactName}}</text>
							<text>{{allData.storeTelphone}}</text>
						</view>
						<view class="refund_address_info">{{allData.storeAddress}}</view>
					</view>
				</view>

				<!-- 退款 退款金额 start -->
				<view class="refund_amount" v-if="allData.afsType == 1 || allData.afsType == 3 || sourceType == ''">
					<view class="refund_amount_left">{{$L('退款金额')}}</view>
					<view class="refund_amout_right">
						<!-- <text class="sum">{{$L('总计')}}:</text> -->
						<view class="refund_amout_price">
							<text class="unit">¥</text>
							<text class="price_int">{{$getPartNumber(allData.returnMoneyAmount,'int')}}</text>
							<text class="price_decimal">{{$getPartNumber(allData.returnMoneyAmount,'decimal')}}</text>
						</view>
					</view>
				</view>
				<!-- 退款 退款金额 end -->


				<!-- 换货 换货数量 start -->
				<view class="refund_amount" v-if="allData.afsType == 2 || sourceType == 'exchange'">
					<view class="refund_amount_left">{{$L('换货数量')}}</view>
					<view class="refund_amout_right">
						<view class="refund_amout_price">
							<text>{{replacementNum}}</text>
						</view>
					</view>
				</view>
				<!-- 换货 换货数量 end -->

				<view class="speed_det" @click="progressDetails">
					<text>{{$L('进度详情')}}</text>
					<image :src="imgUrl+'order-detail/to_right.png'" mode="aspectFit"></image>
				</view>
			</view>

			<!-- 平台审核 及审核备注 start -->
			<view class="platform_audit" v-if="allData.state == 200 || allData.state == 203 || allData.state == 300">
				<view class="platform_audit_status">
					<text>{{$L('平台审核')}}</text>
					<text>{{allData.platformAudit ? allData.platformAudit : '--'}}</text>
				</view>
				<view class="platform_audit_remark">
					<text>{{$L('审核备注')}}</text>
					<view class="platform_audit_remark_con">
						<text>{{allData.platformRemark ? allData.platformRemark : '--'}}</text>
					</view>
				</view>
			</view>
			<!-- 平台审核 及审核备注 end -->

			<!-- 填写物流单号 start 201商家审核通过，才能发货-->
			<view class="logistics_bill" v-if="(allData.afsType == 1 || allData.afsType == 2) && allData.state == 201">
				<text @click="goLogisticsBill">{{$L('填写物流单号')}} >></text>
			</view>
			<!-- 填写物流单号 end -->

			<!-- 退款失败，平台拒绝审核，重新申请 202 start -->
			<view class="logistics_bill" v-if="allData.isCanReapply">
				<text @click="goApplyRefund" class="re_apply_refund">{{$L('重新申请')}}</text>
			</view>
			<!-- 退款失败，平台拒绝审核，重新申请 start -->

			<!-- 等待商家收货并退款 -->
			<view class="refund_logisticsBill" @click="lookLogistics"
				v-if="allData.afsType == 1 && (allData.state == 102 || allData.state == 203 || allData.state == 300)">
				<text>{{$L('退货物流')}}</text>
				<image :src="imgUrl+'order-detail/to_right.png'" mode="aspectFit"></image>
			</view>

			<!-- 订单内商品信息 start -->
			<view class="order_goods">
				<view class="order_goods_title">{{$L('退款信息')}}</view>
				<view class="goods_list">
					<view class="goods_pre" @click="goProductDetail(allData.productId)">
						<view class="goods_image">
							<!-- <coverImage :src="allData.productImage" width="200" height="200" class="image"></coverImage> -->
							<view class="image" :style="'background-image:url('+allData.productImage+')'"></view>
						</view>
						<view class="goods_pre_right">
							<view class="goods_des">
								<view class="goods_name">{{allData.goodsName}}</view>

								<view class="goods_spec">
									<view class="after_sale_spec_right">{{$L("数量")}}：*{{allData.returnNum}}</view>
									<view class="after_sale_spec_left" v-if="allData.specValues">
										{{$L("规格")}}：{{allData.specValues ? allData.specValues : ''}}</view>
								</view>
								<view class="goods_prices">
									<view class="goods_price">
										<text class="unit">¥</text>
										<text class="price_int">{{$getPartNumber(allData.productPrice,'int')}}</text>
										<text
											class="price_decimal">{{$getPartNumber(allData.productPrice,'decimal')}}</text>
									</view>
								</view>
							</view>

						</view>
					</view>
				</view>
			</view>
			<!-- 订单内商品信息 end -->

			<!-- 退款信息  start-->
			<view class="order_des">
				<view class="order_des_pre">
					<text>{{$L('退款原因')}}：</text>
					<text>{{allData.applyReasonContent}}</text>
				</view>
				<view class="order_des_pre">
					<text style="white-space: nowrap;">{{$L('退款说明')}}：</text>
					<text>{{allData.afsDescription==''?'--':allData.afsDescription}}</text>
				</view>
				<view class="refund_voucher" v-if="applyImageList && applyImageList.length > 0">
					<text>{{$L('退款凭证')}}：</text>
					<view class="refund_img_wrap">
						<view class="refund_vouhcer_list">
							<view class="refund_voucher_pre" v-for="(item,index) in applyImageList" :key="index">
								<image :src="item" mode="aspectFit" @click="preRefundVoucher(index)"></image>
							</view>
						</view>
					</view>
				</view>
				<view class="order_des_pre">
					<text>{{$L('退款金额')}}：</text>
					<text>￥{{allData.returnMoneyAmount}}</text>
				</view>
				<view class="order_des_pre">
					<text>{{$L('申请时间')}}：</text>
					<text>{{allData.applyTime}}</text>
				</view>
				<view class="order_des_pre">
					<text>{{$L('退款编号')}}：</text>
					<text>{{allData.afsSn}}</text>
				</view>
			</view>
			<!-- 退款信息  end-->

		</view>

	</scroll-view>

</template>
<script>
	import {
		mapState
	} from 'vuex'
	import recommendGoods from '@/components/recommend-goods.vue'
	import uniPopup from '@/components/uni-popup/uni-popup.vue'
	import uniPopupMessage from '@/components/uni-popup/uni-popup-message.vue'
	import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue'
	let startY = 0,
		moveY = 0,
		pageAtTop = true
	export default {
		components: {
			recommendGoods,
			uniPopup,
			uniPopupMessage,
			uniPopupDialog
		},
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				coverTransform: 'translateY(0px)',
				coverTransition: '0s',
				moving: false,
				afsSn: '', //退款单号
				allData: {}, //订单详细信息
				orderProductList: [], //订单商品列表
				cancelList: [], //取消原因列表
				current: '0', //取消原因当前点击的是第0项
				reasonId: -1, //取消原因当前点击的原因id
				sourceType: '', //从订单详情页面进入， 来源是换货 :'exchange'     或退货 :''
				deadline: '', //截止日期(根据售后状态，待卖家审核--审核截止时间；待买家发货--发货截止时间；待商家收货--收货退款截止时间)
				isShow: false, //数据是否已完全加载
				orderState: '', //订单状态来源
				applyImageList: [], //退款凭证
				returnLogList: [], //退款日志列表
				secInterval: ''
			}
		},
		async onLoad(option) {
			//退款单号
			this.afsSn = this.$Route.query.afsSn
			this.sourceType = this.$Route.query.sourceType
			this.orderState = this.$Route.query.orderState
			this.getOrderDetail()
		},
		// #ifndef MP
		onNavigationBarButtonTap(e) {
			const index = e.index
			if (index === 0) {
				this.navTo('/newPages/set/set')
			} else if (index === 1) {
				//app-1-start
				// #ifdef APP-PLUS
				const pages = getCurrentPages()
				const page = pages[pages.length - 1]
				const currentWebview = page.$getAppWebview()
				currentWebview.hideTitleNViewButtonRedDot({
					index
				})
				// #endif
				//app-1-end
				this.$Router.push('/newPages/notice/notice')
			}
		},
		// #endif
		computed: {
			...mapState(['hasLogin', 'userCenterData'])
		},
		methods: {
			navTo(url) {
				if (!this.hasLogin) {
					let urls = this.$Route.path
					const query = this.$Route.query
					uni.setStorageSync('fromurl', {
						url: urls,
						query
					})
					url = '/pages/public/login'
				}
				this.$Router.push(url)
			},

			//获取订单详情信息
			getOrderDetail() {
				let that = this
				let param = {}
				param.url =
					that.sourceType == 'exchange' ?
					'v1/front/member/afterSale/replacementDetail' :
					'v3/business/front/after/sale/detail' //获取退货详情接口
				param.method = 'GET'
				param.data = {}
				param.data.afsSn = that.afsSn
				that
					.$request(param)
					.then((res) => {
						if (res.state == 200) {
							let result = res.data
							that.orderProductList = result.orderProductList
							that.applyImageList = result.applyImageList
							that.returnLogList = result.returnLogList
							that.allData = result || {}
							that.isShow = true
							that.countup()
						} else {
							this.$api.msg(res.msg)
						}
					})
					.catch((e) => {
						//异常处理
					})
			},
			//计算时间差
			countup() {
				let that = this
				let endTime = that.allData.deadline //结束时间
				let endStrs = endTime.split(' ')
				let endTimeStamp = that.strtotime(endStrs[0], endStrs[1]) //结束时间时间戳(毫秒)
				that.countDown(endTimeStamp)
			},
			//倒计时
			countDown(endTimeStamp) {
				let that = this
				that.secInterval = setInterval(() => {
					let currentTimestamp = new Date().getTime() //当前时间时间戳 （毫秒数）
					let diffrentTimeStamp = endTimeStamp - currentTimestamp //相差时间 毫秒数
					if (diffrentTimeStamp == 0) {
						that.deadline = ''
						clearInterval(that.secInterval)
						that.getOrderDetail()
					} else if (diffrentTimeStamp > 0) {
						//将时间戳转换为天，时，分，秒 并倒计时
						that.deadline = that.formatDuring(diffrentTimeStamp)
					} else {
						that.deadline = ''
					}
				}, 1000)
			},
			//将标准格式（2014-08-02 11:23:12）转化为时间戳  函数   参数：time_str为（2014-08-02）   fix_time为（11:23:12）
			strtotime(time_str, fix_time) {
				let time = new Date().getTime()
				if (time_str) {
					let str = time_str.split('-')
					if (3 === str.length) {
						let year = str[0] - 0
						let month = str[1] - 0 - 1
						var day = str[2] - 0
						if (fix_time) {
							let fix = fix_time.split(':')
							if (3 === fix.length) {
								let hour = fix[0] - 0
								let minute = fix[1] - 0
								time = new Date(year, month, day, hour, minute).getTime()
							}
						} else {
							time = new Date(year, month, day).getTime()
						}
					}
				}
				return time
			},
			//将时间戳转换为时分秒
			formatDuring(mss) {
				let days = parseInt(mss / (1000 * 60 * 60 * 24))
				let hours = parseInt((mss % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
				let minutes = parseInt((mss % (1000 * 60 * 60)) / (1000 * 60))
				let seconds = ((mss % (1000 * 60)) / 1000).toFixed(0)
				if (days > 0) {
					return (
						days +
						this.$L(' 天 ') +
						hours +
						this.$L(' 小时 ') +
						minutes +
						this.$L(' 分钟 ')
					)
				} else if (hours > 0) {
					return hours + this.$L(' 小时 ') + minutes + this.$L(' 分钟 ')
				} else if (minutes > 1) {
					return minutes + this.$L(' 分钟 ')
				} else {
					//如果剩 1分钟之内就不让显示
					return ''
				}
			},
			//去填写物流单号页面
			goLogisticsBill() {
				this.$Router.push({
					path: '/order/logisticsBill',
					query: {
						afsSn: this.afsSn
					}
				})
			},
			//去查看物流页面
			lookLogistics() {
				this.$Router.push({
					path: '/order/lookLogistics',
					query: {
						afsSn: this.allData.afsSn,
						type: 'afs'
					}
				})
			},
			//去进度详情页面
			progressDetails() {
				this.$Router.push({
					path: '/standard/refund/progressDetail',
					query: {
						afsSn: this.afsSn
					}
				})
			},
			//退款失败，重新申请退款
			goApplyRefund() {
				let that = this
				let {
					orderSn,
					orderProductId
				} = this.allData
				this.$request({
					url: 'v3/business/front/after/sale/apply/applyInfo',
					data: {
						orderSn,
						orderProductId
					}
				}).then((res) => {
					if (res.state == 200) {
						if (that.orderState == 20) {
							//待发货直接进入申请退款页面
							this.$Router.replace({
								path: '/standard/refund/applyRefund',
								query: {
									isPickup:that.allData.isPickup,
									orderProductId,
									sourceType: 'orderDetail'
								}
							})
						} else if (that.orderState == 30 || that.orderState == 40) {
							//待收货和待评价进入到选择服务页面
							this.$Router.replace({
								path: '/standard/refund/selectService',
								query: {
									isPickup:that.allData.isPickup,
									orderProductId: that.allData.orderProductId,
									orderSn: that.allData.orderSn
								}
							})
						} else if(that.orderState == 31){
							const {orderProduct:{afsButton}} = res.data
							if(afsButton==100){
								this.$Router.push({
									path: '/standard/refund/applyRefund',
									query: {
										orderSn,
										orderProductId,
										isPickup:that.allData.isPickup,
										sourceType: 'refundDetail',
									}
								})
							}else{
								this.$Router.push({
									path: '/standard/refund/selectService',
									query: {
										orderSn,
										isPickup:that.allData.isPickup,
										orderProductId,
										sourceType: 'refundDetail',
									}
								})
							}
						}
					} else {
						this.$api.msg(res.msg)
					}
				})
			},
			// 退款凭证图片预览
			preRefundVoucher(index) {
				uni.previewImage({
					current: this.applyImageList[index],
					urls: this.applyImageList
				})
			},
			//去商品详情页
			goProductDetail(productId) {
				this.$Router.push({
					path: '/standard/product/detail',
					query: {
						productId
					}
				})
			}
		}
	}
</script>
<style lang='scss'>
	page {
		background: $bg-color-split;
		padding: 0 20rpx;
		padding-top: 20rpx;
		box-sizing: border-box;
	}

	.container {
		box-sizing: border-box;
		display: flex;
		flex: 1;
		/* height: 100vh; */
		position: relative;
		/* background-color: #FFFFFF; */
		/* width: 750rpx; */
		margin: 0 auto;

		.main_content {
			width: 100%;

			.order_state {
				/* app-2-start */
				/* #ifdef APP-PLUS */
				/* padding-top: calc(var(--status-bar-height)); */
				/* #endif */
				/* app-2-end */
				background-color: #fff;
				border-bottom: 1rpx solid #E6E6E6;
				margin-left: 20rpx;
				margin-right: 20rpx;
				background-repeat: no-repeat;
				background-size: cover;
				height: 177rpx;
				/* app-3-start */
				/* #ifdef APP-PLUS */
				/* height: calc(var(--status-bar-height) + 360rpx); */
				/* #endif */
				/* app-3-end */
				box-sizing: border-box;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				border-radius: 20rpx 20rpx 0 0;

				.state_title {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 100%;

					.sub_text {
						font-size: 22rpx;
						margin-top: 6rpx;
					}

					image {
						width: 45rpx;
						height: 45rpx;
						margin-right: 20rpx;
					}

					text {
						font-size: 38rpx;
						font-family: PingFang SC;
						font-weight: bold;
						color: #000;
						line-height: 32rpx;
					}
				}

				.await {
					margin-bottom: 139rpx;
				}

				.state_time {
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #666666;
					line-height: 32rpx;
					margin: 27rpx 0 0;
				}

				.refund_reason {
					width: 640rpx;
					text-overflow: -o-ellipsis-lastline;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					line-clamp: 2;
					-webkit-box-orient: vertical;
					margin-bottom: 0;
					text-align: center;
				}
			}

			.refund_address {
				display: flex;
				align-items: flex-start;
				border-bottom: 20rpx solid #F5F5F5;
				padding: 37rpx;
				box-sizing: border-box;

				.refund_address_map {
					width: 34rpx;
					height: 34rpx;
					margin-right: 30rpx;
				}

				.refund_address_des {
					display: flex;
					flex-direction: column;

					.refund_address_title {
						font-size: 28rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #666666;
						line-height: 42rpx;
					}

					.refund_address_con {
						display: flex;
						align-items: center;
						font-size: 30rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #333333;
						line-height: 45rpx;
						margin: 38rpx 0 29rpx;

						text:nth-child(2) {
							color: #666666;
							margin-left: 40rpx;
						}
					}

					.refund_address_info {
						font-size: 30rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #333333;
						line-height: 42rpx;
						word-break: break-all;
					}
				}
			}

			.refund_amount {
				display: flex;
				justify-content: space-between;
				align-items: center;
				height: 100rpx;
				background-color: #fff;
				margin-left: 20rpx;
				margin-right: 20rpx;
				box-sizing: border-box;
				border-bottom: 1rpx solid #E6E6E6;

				.refund_amount_left {
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #343434;
					line-height: 45rpx;
				}

				.refund_amout_right {
					display: flex;
					align-items: center;

					.sum {
						font-size: 28rpx;
						font-family: PingFang SC;
						font-weight: 400;
						color: #949494;
						line-height: 45rpx;
					}

					.refund_amout_price {
						font-size: 24rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: var(--color_price);
						/* line-height: 45rpx; */
						display: inline-block;

						/* text:nth-child(2) {
							font-size: 26rpx;
						} */
						.unit {
							font-size: 24rpx;
						}

						.price_int {
							font-size: 34rpx;
						}

						.price_decimal {
							font-size: 24rpx;
						}
					}
				}
			}

			.speed_det {
				display: flex;
				align-items: center;
				width: 100%;
				height: 100rpx;
				justify-content: space-between;
				padding: 0 20rpx;
				box-sizing: border-box;
				background: #fff;
				border-radius: 0 0 20rpx 20rpx;

				text {
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #343434;
					line-height: 45rpx;
				}

				image {
					width: 14rpx;
					height: 24rpx;
				}
			}

			.platform_audit {
				/* border-top: 20rpx solid #F5F5F5; */
				margin-top: 20rpx;
				background: #fff;
				border-radius: 20rpx;

				.platform_audit_status {
					display: flex;
					justify-content: space-between;
					padding: 0 20rpx;
					height: 100rpx;
					align-items: center;

					text {
						font-size: 28rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #343434;
						line-height: 45rpx;
					}
				}

				.platform_audit_remark {
					display: flex;
					justify-content: space-between;
					margin-right: 20rpx;
					padding-top: 20rpx;
					padding-bottom: 20rpx;
					margin-left: 20rpx;
					align-items: flex-start;
					border-top: 1rpx solid #E6E6E6;

					text {
						font-size: 28rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #343434;
						line-height: 45rpx;
					}

					.platform_audit_remark_con {
						flex: 1;
						word-break: break-all;
						text-align: right;
						padding-left: 20rpx;

						text {
							display: inline-block;
							text-align: left;
						}
					}

					text:nth-child(2) {
						/* width: 560rpx; */
						/* flex: 1;
						word-break: break-all;
						text-align: right;
            padding-left: 20rpx; */
					}
				}
			}

			.logistics_bill {
				padding: 20rpx 20rpx 20rpx 0;
				background: #fff;
				box-sizing: border-box;
				display: flex;
				justify-content: flex-end;
				padding-left: 20rpx;
				/* border-top: 1rpx solid #E6E6E6; */
				margin-top: 20rpx;
				border-radius: 20rpx;

				text {
					width: 304rpx;
					height: 68rpx;
					background: #FFFFFF;
					border: 1rpx solid #313131;
					font-size: 32rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #F40903;
					line-height: 68rpx;
					text-align: center;
					border-radius: 34rpx;
					font-size: 26rpx;
					color: #333333;
				}

				.re_apply_refund {
					width: 140rpx;
					height: 56rpx;
					border: 1rpx solid #313131;
					border-radius: 28rpx;
					/* width: 176rpx;
					height: 60rpx; */
					background: #FFFFFF;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}

			.refund_logisticsBill {
				height: 100rpx;
				padding: 0 20rpx;
				box-sizing: border-box;
				display: flex;
				justify-content: space-between;
				align-items: center;
				background: #fff;
				border-radius: 20rpx;
				/* border-top: 20rpx solid #F5F5F5; */
				margin-top: 20rpx;

				text {
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #343434;
					line-height: 45rpx;
				}

				image {
					width: 14rpx;
					height: 24rpx;
				}
			}

			.order_goods {
				/* border-top: 20rpx solid #F5F5F5; */
				margin-top: 20rpx;
				background: #fff;
				border-radius: 20rpx 20rpx 0 0;

				.order_goods_title {
					font-size: 30rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #343434;
					line-height: 74rpx;
					padding-left: 20rpx;
					box-sizing: border-box;
				}

				.goods_list {
					padding-top: 20rpx;
					border-bottom: 1rpx dashed #F2F2F2;

					.goods_pre {
						display: flex;
						padding: 0 20rpx;
						box-sizing: border-box;
						margin-bottom: 22rpx;

						.goods_image {
							width: 200rpx;
							height: 200rpx;
							background: #F3F3F3;
							border-radius: 14px;

							.image {
								background-position: center center;
								background-repeat: no-repeat;
								background-size: cover;
								width: 200rpx;
								height: 200rpx;
								border-radius: 14rpx;
							}
						}

						.goods_pre_right {
							display: flex;
							justify-content: space-between;
							/* width: 585rpx; */
							flex: 1;

							.goods_des {
								margin-left: 25rpx;
								padding-top: 8rpx;
								box-sizing: border-box;

								.goods_name {
									width: 310rpx;
									font-size: 28rpx;
									font-family: PingFang SC;
									font-weight: 500;
									color: #343434;
									line-height: 39rpx;
									text-overflow: -o-ellipsis-lastline;
									overflow: hidden;
									text-overflow: ellipsis;
									display: -webkit-box;
									-webkit-line-clamp: 2;
									line-clamp: 2;
									-webkit-box-orient: vertical;
								}

								.goods_spec {
									font-size: 24rpx;
									font-family: PingFang SC;
									font-weight: 400;
									color: #949494;
									line-height: 30rpx;
									margin-top: 20rpx;
									display: flex;
									justify-content: flex-start;

									.after_sale_spec_left {
										color: #999999;
									}

									.after_sale_spec_right {
										color: #939393;
										margin-right: 20rpx;
									}
								}
							}

							.goods_prices {
								display: flex;
								flex-direction: column;
								justify-content: center;
								align-items: flex-start;
								margin-top: 32rpx;

								.goods_price {
									white-space: nowrap;

									text {
										display: inline-block;
										font-family: PingFang SC;
										font-weight: 500;
										color: #343434;
										line-height: 30rpx;
									}

									.unit {
										font-size: 24rpx;
									}

									.price_int {
										font-size: 32rpx;
										font-weight: bold;
									}

									.price_decimal {
										font-size: 24rpx;
										font-weight: 600;
									}
								}

								.goods_num {
									font-size: 24rpx;
									font-family: PingFang SC;
									font-weight: 500;
									color: #2D2D2D;
									line-height: 30rpx;
								}

								.refund_btn {
									padding: 12rpx 15rpx;
									box-sizing: border-box;
									border: 1rpx solid #2D2D2D;
									border-radius: 25rpx;
									font-size: 26rpx;
									line-height: 26rpx;
									font-family: PingFang SC;
									font-weight: 400;
									color: #333333;
									margin-top: 22rpx;
								}
							}
						}
					}
				}
			}

			.order_des {
				box-sizing: border-box;
				padding-top: 30rpx;
				padding-bottom: 10rpx;
				background: #fff;
				margin-bottom: 20rpx;
				border-radius: 0 0 20rpx 20rpx;

				.order_des_pre {
					font-size: 26rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #9A9A9A;
					line-height: 24rpx;
					display: flex;
					margin-bottom: 30rpx;
					padding: 0 20rpx;
					box-sizing: border-box;

					text:nth-child(1) {
						margin-top: 4rpx;
					}

					text:nth-child(2) {
						/* width: 542rpx; */
						text-overflow: ellipsis;
						word-break: break-all;
						line-height: 30rpx;
						flex: 1;
					}
				}

				.refund_voucher {
					display: flex;
					margin-bottom: 30rpx;
					padding-left: 20rpx;
					padding-right: 20rpx;

					text {
						font-size: 26rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #9A9A9A;
						line-height: 24rpx;
						white-space: nowrap;
						display: flex;
						flex-shrink: 0;
						margin-bottom: 30rpx;
					}

					.refund_img_wrap {
						max-width: 600rpx;
						margin-right: 20rpx;
					}

					.refund_vouhcer_list {
						max-width: 580rpx;
						padding-right: 20rpx;
						box-sizing: border-box;
						display: flex;
						flex-wrap: nowrap;
						overflow-x: auto;

						.refund_voucher_pre {
							width: 156rpx;
							height: 156rpx;
							margin-right: 10rpx;

							image {
								width: 156rpx;
								height: 156rpx;
							}
						}
					}
				}
			}

			.share_btn {
				width: 100%;
				height: 100rpx;
				background: #FFFFFF;
				border-top: 1rpx solid #EDEDED;
				display: flex;
				align-items: center;
				padding: 0 118rpx;
				box-sizing: border-box;
				justify-content: space-between;

				.share_btn_pre {
					display: flex;
					align-items: center;

					image {
						width: 28rpx;
						height: 28rpx;
						margin-right: 9rpx;
					}

					image:nth-of-type(2) {
						width: 24rpx;
						height: 29rpx;
					}

					text {
						font-size: 26rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #333333;
						line-height: 24rpx;
					}
				}
			}
		}
	}

	.order_state_box {
		background: #fff;
		border-radius: 20rpx;
		overflow: hidden;
	}
</style>