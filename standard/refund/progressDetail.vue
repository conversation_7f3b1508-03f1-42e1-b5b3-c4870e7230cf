<!-- 售后进度详情 -->
<template>
	<view class="container" :style="mix_diyStyle">
		<view class="gap"></view>
		<view class="progress_content">
			<view class="progress_content_item" v-for="(item, index1) in returnLogList" :key="index1">
				<view class="progress_date_wrap">
					<view class="progress_date">{{ formatDate(item.createTime) }}</view>
					<view class="progress_time">{{ formatTime(item.createTime) }}</view>
				</view>
				<view class="circle_icon_wrap">
					<view class="iconfont iconziyuan33" v-if="index1 == 0"></view>
					<view class="circle_icon" v-else></view>
					<view class="progress_line"></view>
				</view>
				<view class="progress_text" :class="{ progress_text_active: index1 == 0 }">
					<view class="title">{{ item.content }}</view>
					<view class="address" v-if="item.state == 201">
						<view>{{ $L('退货地址：') }}</view>
						<view>
							{{ returnAllData.storeContactName }}{{ ' '}}{{ returnAllData.storeTelphone }}{{ ' '}}{{ returnAllData.storeAreaInfo }}{{ ' '}}{{ returnAllData.storeAddress }}
						</view>
					</view>
					<view class="remark" v-if="index1 == 0 && returnAllData.platformRemark">
						<view></view>
						<view>{{ $L('平台审核备注：')}}{{ returnAllData.platformRemark }}</view>
					</view>
					<block v-if="index1 == returnLogList.length - 1">
						<view class="journal_time" v-if="returnAllData.returnMoneyAmount">
							{{ $L('退款金额：') }}{{ returnAllData.returnMoneyAmount }}
						</view>
						<view class="journal_time" v-if="returnAllData.returnNum">
							{{ $L('申请件数：') }}{{ returnAllData.returnNum }}
						</view>
						<view class="journal_time" v-if="returnAllData.returnNum">
							{{ $L('退款原因') }}：{{ returnAllData.applyReasonContent }}
						</view>
						<view class="journal_time" v-if="returnAllData.afsDescription">
							{{ $L('问题描述：') }}{{ returnAllData.afsDescription }}
						</view>
						<view class="journal_time" v-if="returnAllData.applyImageList.length">
							<view>{{ $L('退款凭证：') }}</view>
							<view class="image_con">
								<block v-for="(item, index) in returnAllData.applyImageList" :key="index">
									<view class="image" :style="{backgroundImage:'url(' + returnAllData.applyImageList[index] + ')'}" @click="viewImage(index)"></view>
								</block>
							</view>
						</view>
					</block>
					<view class="journal_time">{{ $L('时间：') }}{{ item.createTime }}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex'
	export default {
		data() {
			return {
				after_sale_bg: getApp().globalData.imgUrl + 'order/after_sale_bg.png',
				to_right: getApp().globalData.imgUrl + 'order/right.png',
				icon: getApp().globalData.imgUrl + 'order/icon.png',
				concatUrl: getApp().globalData.imgUrl + 'order/concat.png',
				phoneUrl: getApp().globalData.imgUrl + 'order/phone.png',
				afsSn: '', // 售后单号
				state: '', // 退换货状态
				returnLogList: [],
				imgUrl: getApp().globalData.imgUrl,
				returnAllData: {}
			}
		},
		computed: {
			...mapState(['userInfo'])
		},
		onLoad(option) {
			setTimeout(() => {
				uni.setNavigationBarTitle({
					title: this.$L('进度详情')
				})
			}, 0);

			this.afsSn = this.$Route.query.afsSn
			this.state = this.$Route.query.state
			this.loadData()
		},
		methods: {
			// 售后详情
			loadData() {
				let param = {}
				if (this.state == 2) {
					//换货详情
					param.url = 'v1/front/member/afterSale/replacementDetail'
				} else {
					// 仅退款、退货退款
					param.url = 'v3/business/front/after/sale/detail'
				}
				param.method = 'GET'
				param.data = {}
				param.data.afsSn = this.afsSn
				this.$request(param)
					.then((res) => {
						if (res.state == 200) {
							this.returnLogList = res.data.returnLogList.reverse()
							this.returnAllData = res.data
						} else {
							this.$api.msg(res.msg)
						}
					})
					.catch((e) => {
						//异常处理
					})
			},
			// 日期时间格式化
			formatDate(val) {
				let date = val.split(' ')[0]
				let date1 = date.split('-')[1]
				let date2 = date.split('-')[2]
				return date1 + '-' + date2
			},
			formatTime(val) {
				let time = val.split(' ')[1]
				return time.split(':')[0] + ':' + time.split(':')[1]
			},

			viewImage(index) {
				uni.previewImage({
					// 预览图片  图片路径必须是一个数组 => ["http://192.168.100.251:8970/6_1597822634094.png"]
					current: index,
					urls: this.returnAllData.applyImageList,
					longPressActions: {
						//长按保存图片到相册
						itemList: [this.$L('保存图片')],
						success: (data) => {
							uni.saveImageToPhotosAlbum({
								//保存图片到相册
								filePath: this.returnAllData.applyImageList[index],
								success: function() {
									uni.showToast({
										icon: 'success',
										title: this.$L('保存成功')
									})
								},
								fail: (err) => {
									uni.showToast({
										icon: 'none',
										title: this.$L('保存失败，请重新尝试')
									})
								}
							})
						},
						fail: (err) => {}
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	page {
		height: 100%;
	}

	.container {
		width: 750rpx;
		margin: 0 auto;

		// .gap {
		// 	width: 100%;
		// 	height: 20rpx;
		// 	background-color: #F8F8F8;
		// }

		.progress_content {
			margin-top: 20rpx;
			background-color: #fff;

			.progress_content_item {
				position: relative;
				display: flex;
				align-items: flex-start;
				padding-left: 30rpx;

				.progress_date_wrap {
					margin-right: 10rpx;
					display: flex;
					flex-direction: column;
					align-items: flex-end;
					white-space: nowrap;

					.progress_date {
						font-weight: 600;
						font-size: 28rpx;
						color: #949494;
					}

					.progress_time {
						font-size: 22rpx;
						color: #949494;
					}
				}

				.circle_icon_wrap {
					margin-top: 4rpx;
					width: 40rpx;
					display: flex;
					flex-direction: column;
					align-items: center;
					margin-right: 37rpx;
					position: absolute;
					top: 0;
					left: 120rpx;
					height: 100%;

					.circle_icon {
						width: 24rpx;
						height: 24rpx;
						min-height: 24rpx;
						max-height: 24rpx;
						background: #ffffff;
						border: 2rpx solid var(--color_main);
						border-radius: 50%;
						margin-left: 12rpx;
						margin-right: 10rpx;
					}
					
					.iconziyuan33{
						color:var(--color_main);
					}

					.circle_active {
						width: 40rpx;
						max-height: 40rpx;
					}

					.progress_line {
						height: 100%;
						width: 1rpx;
						top: 0;
						background: #dddddd;
					}
				}

				.progress_text {
					white-space: pre-wrap;
					font-size: 26rpx;
					color: #999999;
					font-weight: 500;
					width: 520rpx;
					margin-left: 60rpx;
					margin-bottom: 40rpx;

					.title {
						font-weight: 600;
					}

					.remark,
					.address {
						margin-top: 10rpx;
						display: flex;
						word-break: break-all;
						font-size: 26rpx !important;
						color: #999999;

						view:first-child {
							white-space: nowrap;
						}
					}
				}

				.progress_text_active {
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #333333;
					line-height: 32rpx;
					.address{
						color: #333333;	
					}
				}
			}
		}
	}

	.big_price {
		font-size: 34rpx;
	}

	.small_price {
		font-size: 24rpx;
	}

	.journal_time {
		word-break: break-all;
		display: flex;
		margin-top: 10rpx;

		view:first-child {
			white-space: nowrap;
		}

		.image_con {
			display: flex;
			flex-wrap: wrap;

			.image {
				width: 84rpx;
				height: 84rpx;
				background-position: center center;
				background-repeat: no-repeat;
				background-size: cover;
				margin-left: 10rpx;
				margin-bottom: 10rpx;
			}
		}
	}
</style>