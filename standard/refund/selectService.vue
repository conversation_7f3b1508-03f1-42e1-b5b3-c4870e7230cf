<!-- 选择服务页面 -->
<template>
	<view class="select_service" :style="mix_diyStyle">
		<view class="order_goods">
			<view class="goods_list">
				<view class="goods_pre" @click="goProductDetail(orderProduct.productId, orderProduct.goodsId)">
					<view class="goods_image">
						<image :src="orderProduct.productImage" mode="aspectFit"></image>
					</view>
					<view class="goods_des">
						<view class="goods_name">{{ orderProduct.goodsName }}</view>
						<view class="goods_spec" v-if="orderProduct.specValues">{{ orderProduct.specValues }}</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 服务方式 start -->
		<view class="uni-list service_list">
			<template v-for="(item, index) in servicelList">
				<!-- 退货退款仅支持快递单 -->
				<view class="service_pre" v-if="(item.value == '1' && isPickup == 0) || item.value != 1" :key="index" @click="goApplyRefund(item.value)">
					<view class="service_pre_left">
						<view class="service_des">
							<view class="service_des_top">
								<text class="service_pre_tip"></text>
								<text class="service_pre_title">{{ item.title }}</text>
							</view>
							<view class="servece_pre_content">{{ item.content }}</view>
						</view>
					</view>
					<image :src="imgUrl + 'goods_detail/right_down.png'" mode="aspectFit" class="service_right"></image>
				</view>
			</template>
		</view>
		<!-- 服务方式 end -->
	</view>
</template>

<script>
import { mapState } from 'vuex';
import recommendGoods from '@/components/recommend-goods.vue';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import uniPopupMessage from '@/components/uni-popup/uni-popup-message.vue';
import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue';
let startY = 0,
	moveY = 0,
	pageAtTop = true;
export default {
	components: {
		recommendGoods,
		uniPopup,
		uniPopupMessage,
		uniPopupDialog
	},
	data() {
		return {
			imgUrl: getApp().globalData.imgUrl,
			coverTransform: 'translateY(0px)',
			coverTransition: '0s',
			moving: false,
			isPickup: 0,
			afsSn: '', //退款单号
			allData: {}, //订单详细信息
			orderProduct: {}, //订单商品列表
			current: '0', //选择服务当前点击的是第0项
			servicelList: [
				{
					title: this.$L('仅退款(无需退货)'),
					content: this.$L('没收到货，或与卖家协商同意不用退货仅退款'),
					value: '0'
				},
				{
					title: this.$L('退货退款'),
					content: this.$L('已收到货，需要退还收到的货物'),
					value: '1'
				}
				// ,{
				// 	title:'换货',
				// 	content:'商品存在质量问题，联系卖家协商换货',
				// 	value:'2'
				// },
			],
			orderSn: '', //订单号
			orderProductId: '', //订单中商品列表中商品的id
			orderOrder: {}, //订单信息
			orderListLen: 1 //同订单，订单length
		};
	},
	async onLoad(option) {
		setTimeout(() => {
			uni.setNavigationBarTitle({
				title: this.$L('选择服务')
			});
		}, 0);

		//退款单号
		this.isPickup = this.$Route.query.isPickup;
		this.afsSn = this.$Route.query.afsSn;
		this.orderSn = this.$Route.query.orderSn;
		this.orderProductId = this.$Route.query.orderProductId;
		this.getOrderDetail();
		this.orderListLen = this.$Route.query.orderListLen;
	},
	// #ifndef MP
	onNavigationBarButtonTap(e) {
		const index = e.index;
		if (index === 0) {
			this.navTo('/newPages/set/set');
		} else if (index === 1) {
			//app-1-start
			// #ifdef APP-PLUS
			const pages = getCurrentPages();
			const page = pages[pages.length - 1];
			const currentWebview = page.$getAppWebview();
			currentWebview.hideTitleNViewButtonRedDot({
				index
			});
			// #endif
			//app-1-end
			this.$Router.push('/newPages/notice/notice');
		}
	},
	// #endif
	computed: {
		...mapState(['hasLogin', 'userInfo', 'userCenterData'])
	},
	methods: {
		initData() {},

		/**
		 * 统一跳转接口,拦截未登录路由
		 * navigator标签现在默认没有转场动画，所以用view
		 */
		navTo(url) {
			if (!this.hasLogin) {
				let urls = this.$Route.path;
				const query = this.$Route.query;
				uni.setStorageSync('fromurl', {
					url: urls,
					query
				});
				url = '/pages/public/login';
			}
			this.$Router.push(url);
		},

		/**
		 *  会员卡下拉和回弹
		 *  1.关闭bounce避免ios端下拉冲突
		 *  2.由于touchmove事件的缺陷（以前做小程序就遇到，比如20跳到40，h5反而好很多），下拉的时候会有掉帧的感觉
		 *    transition设置0.1秒延迟，让css来过渡这段空窗期
		 *  3.回弹效果可修改曲线值来调整效果，推荐一个好用的bezier生成工具 http://cubic-bezier.com/
		 */
		coverTouchstart(e) {
			if (pageAtTop === false) {
				return;
			}
			this.coverTransition = 'transform .1s linear';
			startY = e.touches[0].clientY;
		},
		coverTouchmove(e) {
			moveY = e.touches[0].clientY;
			let moveDistance = moveY - startY;
			if (moveDistance < 0) {
				this.moving = false;
				return;
			}
			this.moving = true;
			if (moveDistance >= 80 && moveDistance < 100) {
				moveDistance = 80;
			}

			if (moveDistance > 0 && moveDistance <= 80) {
				this.coverTransform = `translateY(${moveDistance}px)`;
			}
		},
		coverTouchend() {
			if (this.moving === false) {
				return;
			}
			this.moving = false;
			this.coverTransition = 'transform 0.3s cubic-bezier(.21,1.93,.53,.64)';
			this.coverTransform = 'translateY(0px)';
		},
		//获取订单详情信息 申请退换货
		getOrderDetail() {
			let that = this;
			let param = {};
			param.url = 'v3/business/front/after/sale/apply/applyInfo';
			param.method = 'GET';
			param.data = {};
			param.data.orderSn = that.orderSn;
			param.data.orderProductId = that.orderProductId;
			that.$request(param)
				.then((res) => {
					if (res.state == 200) {
						let result = res.data;
						that.orderProduct = result.orderProduct;
						that.orderOrder = result.order;
						that.allData = result || {};
						that.loadFlag = true;
					} else {
						this.$api.msg(res.msg);
					}
				})
				.catch((e) => {
					//异常处理
				});
		},
		//去申请退款页面
		goApplyRefund(value) {
			let that = this;
			this.servicelList.map((item, index) => {
				if (item.value == value) {
					this.current = index;
				}
			});
			let query = {
				serviceType: that.current,
				sourceType: 'selecTService'
			};
			if (that.orderProduct.orderProductId) {
				query.orderProductId = that.orderProduct.orderProductId;
			}
			if (that.orderListLen) {
				query.orderListLen = that.orderListLen;
			}
			this.$Router.push({
				path: '/standard/refund/applyRefund',
				query
			});
		},
		//去商品详情页
		goProductDetail(productId, goodsId) {
			this.$Router.push({
				path: '/standard/product/detail',
				query: {
					productId,
					goodsId
				}
			});
		}
	}
};
</script>

<style lang="scss">
page {
	background: $bg-color-split;
}
.select_service {
	width: 750rpx;
	margin: 0 auto;
	background: #f5f5f5;
	.order_goods {
		border-top: 20rpx solid #f5f5f5;
		background-color: #ffffff;
		.goods_list {
			padding: 20rpx 0;
			.goods_pre {
				display: flex;
				padding: 0 20rpx;
				box-sizing: border-box;
				.goods_image {
					width: 200rpx;
					height: 200rpx;
					background: #f3f3f3;
					border-radius: 14px;
					image {
						width: 200rpx;
						height: 200rpx;
						border-radius: 14rpx;
					}
				}
				.goods_des {
					margin-left: 25rpx;
					padding-top: 8rpx;
					box-sizing: border-box;
					.goods_name {
						width: 340rpx;
						font-size: 28rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #343434;
						line-height: 39rpx;
						text-overflow: -o-ellipsis-lastline;
						overflow: hidden;
						text-overflow: ellipsis;
						display: -webkit-box;
						-webkit-line-clamp: 2;
						line-clamp: 2;
						-webkit-box-orient: vertical;
					}
					.goods_spec {
						font-size: 24rpx;
						font-family: PingFang SC;
						font-weight: 400;
						color: #949494;
						line-height: 30rpx;
						margin-top: 20rpx;
					}
				}
			}
		}
	}
	.service_list {
		background-color: #ffffff;
		border-top: 20rpx solid #f5f5f5;
		.service_pre {
			height: 120rpx;
			display: flex;
			align-items: center;
			margin-left: 20rpx;
			box-sizing: border-box;
			justify-content: space-between;
			border-bottom: 1rpx solid #eeeeee;
			.service_pre_left {
				display: flex;
				align-items: center;
				.service_des {
					display: flex;
					flex-direction: column;
					.service_des_top {
						display: flex;
						align-items: center;
						.service_pre_tip {
							background: var(--color_main);
							width: 10rpx;
							border-radius: 50%;
							height: 10rpx;
							margin-right: 20rpx;
						}
						.service_pre_title {
							font-size: 28rpx;
							font-family: PingFang SC;
							font-weight: 500;
							color: #343434;
							line-height: 45rpx;
						}
					}
					.servece_pre_content {
						font-size: 24rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #9a9a9a;
						line-height: 45rpx;
						padding-left: 30rpx;
					}
				}
			}
			.service_right {
				width: 46rpx;
				height: 46rpx;
			}
		}
	}
}
</style>
