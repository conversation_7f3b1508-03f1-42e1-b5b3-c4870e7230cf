<template>
	<view :style="mix_diyStyle">
		<view>
			<block v-if="!openState">
				<notOpen></notOpen>
			</block>
			<block v-else>
				<!-- app-1-start -->
				<!-- #ifdef APP-PLUS -->
				<view class="fixed_top_bar"></view>
				<!-- #endif -->
				<!-- app-1-end -->
				<!-- #ifdef MP -->
				<view class="nav-bars_top">
					<view class="nav-bars" :style="{paddingTop:menuButtonTop}">
						<view class="nav-bar" :style="{height:menuButtonHeight}">
							<image :src="img_url+'tshou/back_icon.png'" mode="" @click="$back"></image>
							<view class="">好物预售</view>
						</view> 
						<view class="" style="padding-top: 10rpx;width: 100%;height: 1rpx;"></view>
					</view>        
				</view>
				<view class="nav-bars" :style="{paddingTop:menuButtonTop}">
					<view class="nav-bar" :style="{height:menuButtonHeight}">
						<image :src="img_url+'tshou/back_icon.png'" mode="" @click="$back"></image>
						<view class="">好物预售</view>
					</view>
					<view class="" style="padding-top: 10rpx;width: 100%;height: 1rpx;"></view>
				</view>
				<view class="nav_label nav_label_mp">
					<!-- #endif -->
					<!-- #ifndef MP -->
					<view class="nav_label nav_label_h5">
						<view class="back_icon1" @click="goBack">
							<image :src="img_url + 'store/white_arrow_l.png'" mode="aspectFit"></image>
						</view>
						<!-- #endif -->

						<scroll-view scroll-x class="nav">
							<view class="nav_item" @tap="changeNav(0)"
								:class="{ on: curPresellLabelId == 0 ,'nav_item_o':mpFlag==1,'on_o':mpFlag==1&&curPresellLabelId == 0}">
								<text>{{ $L('首页') }}</text>
							</view>
							<view v-for="(item, index) in labelList" :key="index"
								:class="{'nav_item':true,'on':curPresellLabelId == item.presellLabelId,'on_o':mpFlag==1&&curPresellLabelId == item.presellLabelId}"
								:style="{color:color}" @tap="changeNav(item.presellLabelId)">
								<text>{{ item.presellLabelName }}</text>
							</view>
						</scroll-view>
					</view>
					<view class="goods_list" v-if="goodsList.length">
						<navigator v-for="(item, index) in goodsList" :key="index" class="goods_item" :url="'/standard/product/detail?productId=' +item.productId +'&type=presale'" hover-class="none">
							<view class="item_left">
								<!-- <coverImage :src="item.goodsImage" class="image"></coverImage> -->
								<view class="image" :style="'background-image:url(' + item.goodsImage + ')'"></view>
							</view>
							<view class="item_right">
								<text class="goods_name">{{ item.goodsName }}</text>
								<view class="goods_info">
									<view class="goods_price">
										<view class="now_price">
											<text class="small_price">￥</text>
											<text class="big_price">{{filters.toSplit(filters.toFix(item.presellPrice))[0]}}.</text>
											<text class="small_price">{{filters.toSplit(filters.toFix(item.presellPrice))[1]}}</text>
										</view>
										<view class="old_price">￥{{ item.productPrice }}</view>
									</view>
								</view>
								<view class="presale_btn_wrap">
									<view class="presale_num_wrap">
										<!-- <image :src="iconImg" mode="aspectFit" class="iconImg"></image> -->
										<svgGroup type="to_pre" width="29" height="34" px="rpx"
											:color="diyStyle_var['--color_presell_main']" class="iconImg">
										</svgGroup>
										<text>{{ $L('已预定') }}{{ item.saleNum }}人</text>
									</view>
									<view class="goods_btn">{{ $L('立即预定') }}</view>
								</view>
							</view>
						</navigator>
					</view>
					<loadingState v-if="loadingState == 'first_loading' || goodsList.length > 0" :state="loadingState" />
					<view class="empty" v-if="!goodsList.length && loading">
						<image :src="img_url + 'empty_goods.png'"></image>
						<text>{{ $L('暂无商品') }}</text>
					</view>
					<view class="top_wrap" v-show="isShowTopBtn == true">
						<image :src="topImg" mode="aspectFit" @click="top"></image>
					</view>
					<common :title="$L('预售')" :gids="gids" v-if="gids.length"></common>
			</block>
		</view>
	</view>
</template>

<script>
	import loadingState from '@/components/loading-state.vue'
	import notOpen from '@/components/not_open.vue'
	import filters from '../../../utils/filter.js'
	import uniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue'
	const app = getApp()
	export default {
		data() {
			return {
				list: [],
				active: '0',
				autoplay: true,
				interval: 5000,
				duration: 1000,
				// #ifdef MP
				menuButtonHeight: uni.getMenuButtonBoundingClientRect().height + 'px',
				menuButtonTop: uni.getMenuButtonBoundingClientRect().top + 'px',
				menuButtonHeights: uni.getMenuButtonBoundingClientRect().height + uni.getMenuButtonBoundingClientRect()
					.top,
				// #endif
				indicatorDots: true,
				img_url: app.globalData.imgUrl,
				loading: false,
				home_info: '',
				pn: 1,
				hasmore: true,
				isShowTopBtn: false,
				bgImg: getApp().globalData.imgUrl + 'preSale/presale_bg.png',
				iconImg: getApp().globalData.imgUrl + 'preSale/icon4.png',
				topImg: getApp().globalData.imgUrl + 'preSale/top.png',
				gids: [],
				current: 1,
				pageSize: 10,
				labelList: [],
				goodsList: [],
				curPresellLabelId: 0, //当前选中的labelId
				loadingState: 'first_loading',
				openState: true,
				filters,
				nav_left_icon: 'back', //底部tab进入的话为空，否则为back
				statusBarHeightuni: '',
				navBackground: 'linear-gradient(90deg, #EA0191, #F40678)',
				mpFlag: -1,
				color: '#FFF',
				statusBarHeight: 20
			}
		},

		components: {
			loadingState,
			notOpen,
			uniNavBar
		},
		props: {},
		onLoad: function(options) {
			this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight + 20,
				// #ifdef MP
				this.mpFlag = 1
			this.color = '#000'
			// #endif
			this.getPreSaleList()
		},

		onReachBottom() {
			if (this.hasmore) {
				this.getPreSaleList()
			}
		},
		onShow() {
			// #ifdef MP
			this.mpFlag = 1
			this.color = '#000'
			// #endif
			// #ifndef MP
			this.mpFlag = 0
			this.color = '#fff'
			// #endif
		},

		methods: {
			goBack() {
				const page = getCurrentPages()
				if (page.length > 1) {
					this.$Router.back(1)
				} else {
					this.$Router.replace('/pages/index/index')
				}
			},
			//获取预售列表
			getPreSaleList() {
				let that = this
				let param = {}
				param.url = 'v3/promotion/front/preSell/list'
				param.method = 'GET'
				param.data = {}
				param.data.current = that.current
				param.data.pageSize = that.pageSize
				param.data.labelId = that.curPresellLabelId
				that.loadingState =
					that.loadingState == 'first_loading' ? that.loadingState : 'loading'
				that
					.$request(param)
					.then((res) => {
						this.loading = true
						if (res.state == 200) {
							this.openState = true
							let result = res.data
							that.labelList = result.labelList
							if (that.current == 1) {
								that.goodsList = result.goodsList
							} else {
								that.goodsList = that.goodsList.concat(result.goodsList)
							}
							that.hasMore = that.$checkPaginationHasMore(res.data.pagination)
							if (that.hasMore) {
								that.current++
								that.loadingState = 'allow_loading_more'
							} else {
								that.loadingState = 'no_more_data'
							}
						} else if (res.state == 258) {
							this.openState = false
							that.loadingState = 'no_more_data'
						} else {
							that.loadingState = 'no_more_data'
						}
					})
					.catch((e) => {
						//异常处理
					})
			},
			bigPrice(val) {
				return val.split('.')[0]
			},
			smallPrice(val) {
				return val.split('.')[1]
			},
			changeNav(presellLabelId) {
				let that = this
				that.curPresellLabelId = presellLabelId
				that.current = 1
				that.getPreSaleList()
			},
			// 获取滚动距离
			onPageScroll(e) {
				//根据距离顶部距离是否显示回到顶部按钮
				if (e.scrollTop > 600) {
					//当距离大于600时显示回到顶部按钮
					this.isShowTopBtn = true
				} else {
					//当距离小于600时隐藏回到顶部按钮
					this.isShowTopBtn = false
				}
			},
			// 回到顶部
			top() {
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 300
				})
			},
			// goBack() {
			//   this.$Router.back(1)
			// }
		}
	}
</script>
<style lang="scss">
	/* addons/pages/presaleIndex.wxss */
	page {
		background-color: #f5f5f5;
		/* #ifndef MP */
		padding-top: 82rpx;
		/* #endif */
		width: 750rpx;
		margin: 0 auto;
	}

	/* #ifdef H5 */
	page {
		padding-top: 0rpx;
	}
	/* #endif */
	
	//app-2-start
	/* #ifdef APP-PLUS */
	.fixed_top_bar {
		width: 100%;
		height: var(--status-bar-height);
		background: linear-gradient(90deg, #EA0191, #F40678);
		position: fixed;
		top: 0;
		z-index: 9999;
	}
	/* #endif */
	//app-2-end

	.nav_label {
		display: flex;
		position: relative;
		position: fixed;
		/* #ifndef MP */
		top: 0;
		/* #endif */
		//app-3-start
		/* #ifdef APP-PLUS */
		top: calc(var(--status-bar-height));
		/* #endif */
		//app-3-end
		left: 0;
		right: 0;
		margin: 0 auto;
		width: 750rpx;
		height: 88rpx;
		z-index: 9999;
	}
  
  .nav_label_h5{
    background: var(--color_presell_main_bg);
  }
  
	.nav {
		height: 88rpx;
		display: block;
		white-space: nowrap;
		overflow: hidden;
		z-index: 9999;
		right: 0;
	}

	.back_icon1 {
		display: flex;
		align-items: center;
		padding-left: 10rpx;

		image {
			width: 52rpx;
			height: 49rpx;
		}
	}

	.nav_item {
		display: inline-block;
		line-height: 86rpx;
		text-align: center;
		color: #fff;
		font-size: 30rpx;
		padding: 0 20rpx;
	}

	.nav_item_o {
		color: #333333;
	}

	.nav_item.on {
		font-size: 30rpx;
	}

	.nav_item.on text {
		font-weight: bold;
		font-size: 32rpx;
		display: inline-block;
		padding: 0 10rpx;
		line-height: 50rpx;
	}

	.nav_item.on_o text {
		font-weight: bold;
		font-size: 32rpx;
		display: inline-block;
		padding: 0 10rpx;
		line-height: 50rpx;
		color: var(--color_presell_main);
		position: relative;

		&::before {
			content: '';
			width: 88%;
			height: 3rpx;
			background: var(--color_presell_main);
			position: absolute;
			left: 50%;
			transform: translate(-50%);
			bottom: -10rpx;
			box-sizing: border-box;
			// margin: 0 10rpx;
		}
	}

	.goods_list {
		padding: 0 20rpx;
		background-color: white;
		//app-4-start
		/* #ifdef APP-PLUS */
		padding-top: var(--status-bar-height);
		/* #endif */
		//app-4-end
		/* #ifdef H5 */
		padding-top: 88rpx;
		/* #endif */
		/* #ifdef MP */
		padding-top: 88rpx;
		/* #endif */
	}

	.goods_list .goods_item {
		height: 334rpx;
		display: flex;
		align-items: center;
		border-bottom: 1rpx solid #f2f2f2;
	}

	.goods_list navigator:nth-last-child(1) {
		border-bottom: none;
	}

	.goods_item .item_left {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 294rpx;
		height: 294rpx;
	}

	.goods_item .item_left .image {
		background-position: center center;
		background-repeat: no-repeat;
		background-size: cover;
		width: 294rpx;
		height: 294rpx;
		border-radius: 15rpx;
	}

	.goods_item .item_right {
		flex: 1;
		display: flex;
		min-height: 200rpx;
		flex-direction: column;
		margin-left: 20rpx;
		justify-content: space-between;
		height: 100%;
		padding-bottom: 20rpx;
		box-sizing: border-box;
		position: relative;
	}

	.goods_item .item_right .goods_name {
		margin-top: 43rpx;
		font-size: 28rpx;
		color: #2d2d2d;
		font-weight: 600;
		line-height: 40rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		word-break: break-all;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
	}

	.item_right .goods_info {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.goods_info .goods_price {
		display: flex;
		align-items: flex-end;
		position: absolute;
		bottom: 100rpx;
	}

	.now_price {
		color: var(--color_presell_main);
		font-weight: bold;
	}

	.now_price .small_price {
		font-size: 24rpx;
	}

	.now_price .big_price {
		font-size: 34rpx;
	}

	.goods_price .old_price {
		color: #999;
		font-size: 24rpx;
		text-decoration: line-through;
		margin-left: 10rpx;
		padding-bottom: 4rpx;
	}

	.presale_btn_wrap {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 50rpx;
		color: #333;
		font-size: 24rpx;
		border-radius: 25rpx;
		position: absolute;
		bottom: 40rpx;
		left: 0;
	}

	.presale_num_wrap {
		display: flex;
		align-items: center;
		height: 46rpx;
		color: #333333;
		font-size: 25rpx;
		border-radius: 25rpx 0 0 25rpx;
		border: 1rpx solid var(--color_presell_main);
		padding-right: 40rpx;
		padding-left: 20rpx;
		white-space: nowrap;
	}

	.presale_num_wrap .iconImg {
		width: 29rpx;
		height: 34rpx;
		margin-right: 10rpx;
	}

	.goods_btn {
		min-width: 140rpx;
		height: 46rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 0 25rpx 25rpx 25rpx;
		color: #fff;
		font-size: 26rpx;
		background: var(--color_presell_main_bg);
		margin-left: -30rpx;
	}

	.empty {
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		height: 70vh;
	}

	.empty image {
		width: 380rpx;
		height: 280rpx;
		margin-bottom: 30rpx;
	}

	.empty text {
		color: #333;
		font-size: 28rpx;
	}

	.top_wrap {
		position: fixed;
		right: 46rpx;
		bottom: 66rpx;
		width: 85rpx;
		height: 85rpx;
	}

	.top_wrap image {
		width: 85rpx;
		height: 85rpx;
	}

	.nav-bar {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: flex-start;
		padding-left: 20rpx;
	}

	.nav-bar image {
		width: 19rpx !important;
		height: 33rpx !important;
		margin-top: 5rpx !important;
		position: initial !important;
	}

	.nav-bar view {
		font-size: 36rpx;
		font-family: PingFang SC;
		color: #FFFFFF;
		margin-left: 21rpx;
	}
  .nav-bars_top{
    position: fixed;
    background: #fff;
     width: 750rpx;
     z-index: 99;
  }

	.nav-bars {
		z-index: 999;
		background:var(--color_presell_main_bg);
		width: 100%;
		top: 0;
	}
  .nav_label_mp{
    background: #fff;
  }
</style>