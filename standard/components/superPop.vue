<template>
	<view class="">
		<uniPopup ref="popUp" type="bottom">
			<view class="">
				<view class="super_top">
					<view class="back_view">
						<view mode="aspectFit"></view>
					</view>
					<view class="super_top_text" :style="{backgroundImage:'url('+imgUrl+'super/crown_icon.png)'}">
						<text>{{ $L('会员专享') }}</text>
					</view>
					<image :src="imgUrl + 'pinGroup/close.png'" mode="aspectFit" @click="close"></image>
				</view>
			</view>
			<view style="background-color: #fff;">
				<view class="super_text_container">
					<view class="super_text_1">实际优惠金额以结算页下单结果为准。</view>
					<view class="super_text_2">
						<text>付费会员专属优惠</text>
						<view class="image">
							
						</view>
					</view>
				</view>
				<scroll-view scroll-y="true" class="super_list" @touchmove.stop.prevent="moveHandle">
					<view style="padding: 0 30rpx;">
						<view v-for="(item, index) in superList" :key="index" class="list">
							<view class="wrapper_1 flex_row_start_center" :style="{backgroundImage:'url('+imgUrl+'super/super_detail_banner.png)'}">
								<view class="flex_column_start_start">
									<view class="super-box">
										<text class="super-content">{{ item.label }}</text>
										<text v-if="item.extra" class="super-extra">{{item.extra}}</text>
									</view>
								</view>
							</view>
							<view class="wrapper_right">
								<text class="super">可省{{ item.value }}元</text>
							</view>
						</view>
					</view>
				</scroll-view>
				<view class="other_super" v-if="userCenterData.isSuper==0">
					<navigator url="/standard/super/index?type=0">
						<view class="other_btn" @click="chooseArea">立即开通付费会员</view>
					</navigator>
				</view>
			</view>
		</uniPopup>
	</view>
</template>

<script>
	import uniPopup from '@/components/uni-popup/uni-popup.vue'
	import {
		mapState,
		mapActions
	} from 'vuex'
	export default {
		components: {
			uniPopup
		},
		props: {
			saveAmount: Object
		},
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				superList: [],
				configObject: {}
			}
		},
		mounted() {
			this.getMemberConfig([
				'super_is_enable',
				'super_discount',
				'super_discount_enabled',
				'super_freight_coupon_enabled',
				'super_coupon_enabled',
			]).then(res => {
				this.configObject = res
			})
		},
		computed: {
			...mapState(['memberConfig', 'userCenterData'])
		},
		methods: {
			...mapActions(['getMemberConfig']),
			open() {
				this.handleConfig()
				this.$refs.popUp.open()
			},
			close(){
				this.$refs.popUp.close()
			},
			handleConfig() {
				let tmpList = []
				let {
					configObject,
					saveAmount
				} = this
				if (configObject['super_discount_enabled'] == 1 && saveAmount.discountAmount > 0) {
					tmpList.push({
						label: `会员购物享${configObject['super_discount']}折优惠`,
						value: saveAmount.discountAmount
					})
				}

				if (saveAmount.superPriceAmount > 0) {
					tmpList.push({
						label: `会员专享商品优惠价`,
						value: saveAmount.superPriceAmount
					})
				}

				if (configObject['super_freight_coupon_enabled'] == 1 && saveAmount.freightCouponAmount > 0) {
					let tmpObj = {
						label: `运费券`,
						value: saveAmount.freightCouponAmount,
						extra: ''
					}
					if (saveAmount.freightCouponNum > 0 && configObject['super_is_enable'] == 0) {
						tmpObj.extra = `共${saveAmount.freightCouponNum}张，购买付费会员后立即到账`
					}
					tmpList.push(tmpObj)
				}
				if (configObject['super_coupon_enabled'] == 1 && saveAmount.exclusiveCouponAmount > 0) {
					let tmpObj = {
						label: `会员专属优惠券`,
						value: saveAmount.exclusiveCouponAmount,
						extra: ''
					}
					if (saveAmount.exclusiveCouponNum > 0 && configObject['super_is_enable'] == 0) {
						tmpObj.extra = `共${saveAmount.exclusiveCouponNum}张，购买付费会员后立即到账`
					}
					tmpList.push(tmpObj)
				}
				this.superList = tmpList
			}
		},



	}
</script>

<style lang="scss">
	.super_top {
		padding: 20rpx 30rpx;
		border-radius: 30rpx 30rpx 0 0;
		display: flex;
		justify-content: space-between;
		align-items: center;
		background: #fff;
		margin-bottom: -2rpx;
		
		.super_top_text{
			font-size: 34rpx;
			font-family: PingFang SC;
			font-weight: bold;
			color: #0F0F18;
			white-space: nowrap;
			width: 71rpx;
			height: 61rpx;
			background-position: center center;
			background-repeat: no-repeat;
			background-size: cover;
			text-align: center;
			padding-top: 24rpx;
			text{
				margin-left: -35rpx;
			}
		}

		image {
			width: 30rpx;
			height: 30rpx;
		}
	}

	.back_view {
		display: flex;
		align-items: center;

		view {
			width: 30rpx;
			height: 30rpx;
		}

		text {
			font-size: 28rpx;
		}
	}

	.super_list {
		width: 750rpx;
		height: 480rpx;
		margin: 0 auto;
		z-index: 150;
		background-color: #fff;
	}

	.super_list_con {
		border-radius: 5px 5px 0;
	}

	.wrapper_1 {
		height: 100rpx;
		width: 418rpx;
		background-position: center center;
		background-repeat: no-repeat;
		background-size: contain;
		padding-left: 21rpx;
		
		.iconfont {
			color: $main-color;
			font-size: 32rpx;
			margin-right: 30rpx;
		}

		image {
			width: 36rpx;
			height: 38rpx;
			margin-right: 22rpx;
		}
		
		.super-box{
			.super-content{
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #6F4F3A;
			}
			
			.super-extra{
				font-size: 22rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #AA8B69;
			}
		}
	}

	.wrapper_right {
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #5F4337;
		
	}

	.super-box {
		display: flex;
		align-items: center;

		.super {
			font-size: 28rpx;
			color: #333;
			line-height: 38rpx;
			margin-top: 5rpx;
			word-break: break-all;
			max-width: 570rpx;
		}

		.tag {
			width: 63rpx;
			height: 30rpx;
			margin-left: 20rpx;
			margin-right: 0rpx;
		}
	}

	.super_on {
		color: #fb4444 !important;
	}

	.list {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 10rpx 30rpx 10rpx 10rpx;
		background: linear-gradient(90deg, #FEDEAA, #FCF2D9);
		border-radius: 10rpx;
		position: relative;
		margin-bottom: 20rpx;
	}

	.super_text_1 {
		font-size: 26rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #BE9253;
	}

	.super_text_2 {
		font-style: normal;
		font-size: 30rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #333333;
		margin-top: 30rpx;
		.image{
			width: 235rpx;
			height: 16rpx;
			background: linear-gradient(180deg, rgba(251,229,195,0.7) 0%, rgba(251,228,195,0) 100%);
			opacity: 0.8;
		}
	}

	.super_text_container {
		padding: 20rpx;
	}

	.other_super {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		height: 130rpx;
		background: #fff;

		.other_btn {
			width: 660rpx;
			height: 90rpx;
			background: linear-gradient(90deg, #EDCA98, #E9B87A);
			border-radius: 45rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 30rpx;
			font-family: PingFang SC;
			font-weight: bold;
			color: #562D0E;
		}
	}
</style>