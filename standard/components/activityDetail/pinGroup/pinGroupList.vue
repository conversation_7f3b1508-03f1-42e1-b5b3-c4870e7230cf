<template>
	<view>

		<!-- 拼团 查看更多 start  拼单-->
		<div class="goods-detail-team teams" v-if="pinTeam.length>0">
			<view class="make_group">
				<text class="make_group_num"><text class="pin_title">{{$L('拼团')}}</text>{{joinedNum}}{{$L('人正在拼单,可直接参与')}}</text>
				<view class="make_groip_more" @tap="make_group_more_fun">
					<text>{{$L('查看更多')}}></text>
				</view>
			</view>
			<block v-for="(pin_team_item, i) in pinTeam" :key="i">
				<block>
					<view v-if="i<=1" class="make_group_content">
						<view class="make_group_content_left">
							<view class="make_group_content_left">
								<image :src="pin_team_item.avatar" class="make_group_avator"></image>
								<view :data-id="pin_team_item.id" class="make_group_name">
									{{pin_team_item.member_name}}
								</view>
							</view>
						</view>
						<view class="make_group_right">
							<view class="make_group_right1">
								<div class="make_group_right1">
									<span
										class="make_group_missing">{{$L('还差')}}<text>{{pin_team_item.missingNum}}</text>人</span>
									<view class="make_group_end">
										{{$L('距结束')}}<text>{{pin_team_item._distanceEndTime}}</text>
									</view>
								</div>
							</view>
							<view class="make_group_right2" v-if="pin_team_item.isSelf" @tap="handleJoinGroup"
								:data-teamid="pin_team_item.spellTeamId"
								:data-isown="pin_team_item.isSelf ? true : false">{{$L('我的团')}}</view>
							<view class="make_group_right2" v-else @tap="handleJoinGroup"
								:data-isown="pin_team_item.isSelf? true : false"
								:data-teamid="pin_team_item.spellTeamId">{{$L('去参团')}}</view>
						</view>
					</view>
				</block>
			</block>
		</div>
		<!-- 拼团 查看更多 end -->


		<uni-popup type="center" ref="pinlistModel">
			<view class="join_group" v-if="join_group" @touchmove.stop.prevent="moveHandle">
				<view class="join_group_title">
					<text>{{$L('参与拼单')}}</text>
					<image :src="imgUrl + 'pinGroup/close.png'" @tap="hidden_mask"></image>
				</view>
				<view class="join_group_content">
					<view class="join_group_des">
						<view class="group_des_title">{{$L('参与')}}{{pinDetail.leaderMemberName}}{{$L('的拼单')}}</view>
						<view class="group_des_miss">{{$L('还差')}}<text>{{pinDetail.missingNum}}</text>{{$L('人')}}</view>
					</view>
					<view class="join_group_time">
						<text>{{$L('距离结束')}}</text>
						<text>{{pinDetail._distanceEndTime}}</text>
					</view>
					<view class="join_group_pro">
						<view v-for="(item, index) in pinDetail.memberList" :key="index" class="join_group_pre"
							v-if="index < 1">
							<image :src="item.memberAvatar"></image>
							<text v-if="item.isLeader == 1">{{$L('团长')}}</text>
						</view>
						<view class="add_group">
							<image :src="imgUrl + 'pinGroup/add_group.png'"></image>
						</view>
					</view>
					<!-- <image class="handleAddGroup" v-if="!pinDetail.isSelf" :data-id="pinDetail.id"
						@tap="switchSpecifications" data-type="join_group" :src="imgUrl + 'pinGroup/join_group.png'">
					</image> -->
          <view class="handleAddGroup" v-if="!pinDetail.isSelf" :data-id="pinDetail.id" @tap="switchSpecifications">{{$L('参加拼团>')}}</view>
				</view>
			</view>

			<view class="make_order_more" v-if="make_group_more" @touchmove.stop.prevent="moveHandle">
				<view class="make_order_title">
					<text>{{$L('正在拼单')}}</text>
					<image :src="imgUrl + 'close2.png'" @tap="hidden_mask"></image>
				</view>
				<scroll-view class="make_order_content" scroll-y>
					<view v-for="(pin_team_item, index) in pinTeam" :key="index" class="make_order_pre">
						<view class="make_order_pre_left">
							<image class="make_order_image" :src="pin_team_item.avatar"></image>
							<view class="make_order_des">
								<view class="make_order_des_top">
									<text>{{pin_team_item.member_name}}</text>
									<text>{{$L('还差')}}{{pin_team_item.missingNum}}{{$L('人')}}</text>
								</view>
								<view class="make_order_des_bot">
									{{$L('剩余')}} <text id="make_order_des_pre">{{pin_team_item._distanceEndTime}}</text>
								</view>
							</view>
						</view>
						<view class="my_group" v-if="pin_team_item.isSelf" @tap="handleJoinGroup"
							:data-teamid="pin_team_item.spellTeamId" :data-isown="pin_team_item.is_own ? true : false">
							{{$L('我的团')}}
						</view>
						<view class="my_group" v-else @tap="handleJoinGroup" :data-teamid="pin_team_item.spellTeamId"
							:data-isown="pin_team_item.isSelf ? true : false">{{$L('去参团')}}</view>
					</view>
				</scroll-view>
			</view>
		</uni-popup>
	</view>

</template>

<script>
	export default {
		props: ['goodsId', 'spellId'],
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				pinTeam: [],
				pinDetail: {},
				join_group: false,
				make_group_more: false,
				joinedNum: ''
			}
		},
		created() {
			this.getPinTeam()
		},
		methods: {

			getPinTeam() {
				let {
					goodsId,
					spellId
				} = this
				let params = {
					url: 'v3/promotion/front/spell/teamList',
					method: 'GET',
					data: {
						goodsId,
						spellId
					}
				}
				this.$request(params).then(res => {
					if (res.state == 200) {
						this.pinTeam = res.data.list.map(i => {
							let obj = {
								...i,
								avatar:i.memberList[0].memberAvatar,
								member_name:i.memberList[0].memberName,
								_distanceEndTime:'00:00:00:00'
							}
							return obj
						})
						if (res.data.list[0]) {
							this.joinedNum = res.data.list[0].joinedNum;
						}
						this.calTime(this.pinTeam)
					}
				})
			},

			calTime(zbcp) {
				zbcp.map(item => {
					let counttime = item.distanceEndTime;
					let days = 0
					let hours = 0
					let minutes = 0
					let seconds = 0
					this.secInterval = setInterval(() => {
						if (counttime == 0) {
							clearInterval(this.secInterval);
						} else {
							counttime--;
							days = parseInt(counttime / 60 / 60 / 24) > 0 ? parseInt(counttime / 60 / 60 /
								24) : 0;
							hours = parseInt(counttime / 60 / 60 % 24) > 0 ? parseInt(counttime / 60 / 60 %
								24) : 0;
							minutes = parseInt(counttime / 60 % 60) > 0 ? parseInt(counttime / 60 % 60) :
								0;
							seconds = parseInt(counttime % 60) > 0 ? parseInt(counttime % 60) : 0;
							let arr = [days, hours, minutes, seconds].map(i =>
								i.toString().length > 1 ? i : '0' + i
							)
							item._distanceEndTime = arr.join(':')
						}
					}, 1000)
				})
			},

			switchSpecifications() {
				this.$emit('handleJoinGroup', {
					pinState: 3
				})
				this.close()
				this.join_group = false
				this.make_group_more = false
			},

			hidden_mask() {
				this.$emit('handleJoinGroup', {
					pinState: 2
				})
				this.join_group = false
				this.make_group_more = false
				this.close()
			},
			handleJoinGroup(e) {
				let spellTeamId = e.currentTarget.dataset.teamid
				this.$emit('handleJoinGroup', {
					pinState: 1,
					spellTeamId
				})
				this.join_group = true
				this.make_group_more = false

				this.open()
				this.pinDetail = this.pinTeam.find(i => i.spellTeamId == spellTeamId)
				this.$forceUpdate()
			},

			make_group_more_fun() {
				this.open()
				this.join_group = false
				this.make_group_more = true
			},

			open() {
				this.$refs.pinlistModel.open()
			},

			close(){
				this.$refs.pinlistModel.close()
			}
		}
	}
</script>

<style lang="scss">
	.goods-detail-team {
		background: #fff;
		margin-top: 17.94rpx;
		padding: 20rpx 20rpx 0;
		box-sizing: border-box;
	}

	.goods-detail-team>.teams_h4 {
		font-size: 27.6rpx;
		color: #333;
	}

	.goods-detail-team .teams_a {
		display: block;
		border: 1px #ee1b21 solid;
		border-radius: 100px;
		height: 49px;
		width: 100%;
		overflow: hidden;
		margin-top: 15px;
	}

	.pin_title {
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #666666;
		margin-right: 36rpx;
	}

	.goods-detail-team b {
		width: 100%;
		display: block;
		width: 100%;
		text-align: center;
		font-size: 25.3rpx;
		color: #888;
		font-weight: normal;
		margin-top: 10px;
	}

	.goods-detail-team .teams_a image {
		float: left;
		width: 45px;
		height: 45px;
		margin: 2px;
		border-radius: 92rpx;
	}

	.goods-detail-team .teams_a .team_item_h2,
	.goods-detail-team .teams_a .team_item_h4 {
		font-size: 27.6rpx;
		line-height: 15px;
		display: block;
	}

	.goods-detail-team .teams_a .team_item_h3,
	.goods-detail-team .teams_a .team_item_h5 {
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #333333;
		line-height: 36rpx;
	}

	.make_group_end {
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #333333;
		line-height: 36rpx;
	}

	.make_group_end text {
		color: #999999;
	}

	.goods-detail-team .teams_a .team_item_h3 {
		width: 100%;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		line-height: 19px;
	}

	.goods-detail-team .teams_a .fl {
		float: left;
		width: 21%;
		padding: 5px 0 5px 4px;
	}

	.goods-detail-team .teams_a .team_item_h6,
	.goods-detail-team .teams_a .fr {
		float: right;
	}

	.goods-detail-team .teams_a .fr {
		text-align: right;
		padding: 12rpx 10rpx 12rpx 0;
		height: 49px;
		line-height: 49px;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		box-sizing: border-box;
	}

	.goods-detail-team .teams_a .team_item_h6 {
		line-height: 49px;
		background: var(--color_spell_main);
		color: #fff;
		display: block;
		padding: 0 15px;
		font-size: 32.2rpx;
	}

	.goods-detail-team .teams_a .team_item_h6:before {
		content: '去参团';
		display: inline-block;
	}

	.goods-detail-team .teams_a .team_item_h6:after {
		display: inline-block;
		margin-left: 10px;
		width: 5px;
		content: ' ';
		height: 5px;
		border-left: 1px #fff solid;
		border-top: 1px #fff solid;
		transform: rotate(135deg);
		vertical-align: middle;
	}

	.goods-detail-team .teams_a.on .team_item_h6:before {
		content: '已选择';
	}

	.goods-detail-team .teams_a .team_item_h6.own:before {
		content: '我的团';
	}

	.goods-detail-team .teams_a .team_item_h6.oth:before {
		content: '其他团';
	}

	.goods-detail-team .teams_a .team_item_h4 {
		color: var(--color_spell_main);
	}

	.goods-detail-team .teams_a .team_item_h5 {
		line-height: 12px;
	}

	.make_group {
		display: flex;
		align-items: center;
		width: 100%;
		justify-content: space-between;
	}

	.make_group_num {
		font-size: 26rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: rgba(45, 45, 45, 1);
		line-height: 45rpx;
	}

	.make_groip_more {
		display: flex;
		align-items: center;
	}

	.make_groip_more text {
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: var(--color_spell_main);
	}

	.make_groip_more image {
		width: 12rpx;
		height: 20rpx;
	}

	.make_group_content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		height: 125rpx;
		border-bottom: 1rpx solid #E5E5E5;
	}

	.make_group_content:nth-last-of-type(1) {
		border-bottom: 0;
	}

	.make_group_content_left {
		display: flex;
		align-items: center;
	}

	.make_group_avator {
		width: 64rpx;
		height: 64rpx;
		background: rgba(208, 208, 208, 1);
		border-radius: 50%;
	}

	.make_group_name {
		font-size: 32rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: rgba(45, 45, 45, 1);
		line-height: 45rpx;
		margin-left: 20rpx;
	}

	.make_group_missing {
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: rgba(51, 51, 51, 1);
		line-height: 36rpx;
	}

	.make_group_missing text {
		color: var(--color_spell_main);
	}

	.make_group_right {
		display: flex;
		align-items: center;
	}

	.make_group_right1 {
		display: flex;
		flex-direction: column;
		align-items: flex-end;
		margin-right: 20rpx;
	}

	.make_group_right2 {
		width: 120rpx;
		height: 50rpx;
		background: var(--color_spell_main);
		border-radius: 25rpx;
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: rgba(255, 255, 255, 1);
		line-height: 50rpx;
		text-align: center;
	}

	.make_rroup_time {
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: rgba(51, 51, 51, 1);
		line-height: 36rpx;
	}

	.make_rroup_time text {
		color: #999999;
	}

	.join_group {
		width: 580rpx;
		min-height: 415rpx;
		background: rgba(255, 255, 255, 1);
		border-radius: 15rpx;
		position: fixed;
		z-index: 100;
		left: 50%;
		top: 50%;
		margin-left: -290rpx;
		margin-top: -207rpx;
	}

	.join_group_title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 30rpx;
		box-sizing: border-box;
		height: 80rpx;
		border-bottom: 1rpx solid #F7F7F7;
	}

	.join_group_title text {
		font-size: 32rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #2D2D2D;
		line-height: 45rpx;
	}

	.join_group_title image {
		width: 20rpx;
		height: 20rpx;
	}

	.join_group_content {
		padding-top: 38rpx;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.join_group_des {
		display: flex;
		align-items: center;
	}

	.group_des_title {
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: rgba(45, 45, 45, 1);
		line-height: 45rpx;
		margin-right: 30rpx;
	}

	.group_des_miss {
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #999;
		line-height: 45rpx;
	}

	.group_des_miss text {
		color: var(--color_spell_main);
	}

	.join_group_time {
		display: flex;
		align-items: center;
		margin-top: 19rpx;
	}

	.join_group_time text:nth-child(1) {
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: rgba(148, 148, 148, 1);
		line-height: 45rpx;
	}

	.join_group_time text:nth-child(2) {
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: var(--color_spell_main);
		line-height: 45rpx;
		margin-left: 20rpx;
	}

	.join_group_pro {
		display: flex;
		align-items: center;
		margin: 30rpx auto;
	}

	.join_group_pre {
		position: relative;
	}

	.join_group_pre image {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
	}

	.join_group_pre text {
		position: absolute;
		left: 10rpx;
		bottom: 13rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 65rpx;
		height: 31rpx;
		color: rgba(255, 255, 255, 1);
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 500;
		background: var(--color_spell_main);
		border-radius: 13rpx;
		transform: scale(0.84) translateY(12rpx);
	}

	.add_group,
	.add_group image {
		width: 80rpx;
		height: 80rpx;
	}

	.handleAddGroup {
		width: 240rpx;
		height: 50rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #fff;
		background: var(--color_spell_main);
		border-radius: 25rpx;
    margin-bottom: 30rpx;
	}

	.make_order_more {
		width: 580rpx;
		height: 773rpx;
		background: rgba(255, 255, 255, 1);
		border-radius: 15rpx;
		position: fixed;
		z-index: 100;
		left: 50%;
		top: 50%;
		margin-left: -290rpx;
		margin-top: -386rpx;
	}

	.make_order_title {
		width: 100%;
		height: 80rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 30rpx;
		box-sizing: border-box;
		border-bottom: 1rpx solid #F7F7F7;
	}

	.make_order_title text {
		font-size: 32rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: rgba(45, 45, 45, 1);
		line-height: 45rpx;
	}

	.make_order_title image {
		width: 30rpx;
		height: 30rpx;
	}

	.make_order_content {
		width: 100%;
		height: 630rpx;
	}

	.make_order_pre {
		width: 100%;
		height: 95rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;
		box-sizing: border-box;
		border-bottom: 1rpx solid #F5F5F5;
	}

	.make_order_image {
		width: 64rpx;
		height: 64rpx;
		border-radius: 50%;
		margin-right: 19rpx;
	}

	.make_order_des {
		display: flex;
		flex-direction: column;
	}

	.make_order_des_top {
		display: flex;
	}

	.make_order_des_top text:nth-child(1) {
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: rgba(45, 45, 45, 1);
		line-height: 45rpx;
	}

	.make_order_des_top text:nth-child(2) {
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #999999;
		line-height: 45rpx;
		margin-left: 10rpx;
	}

	.make_order_des_bot {
		font-size: 22rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: rgba(153, 153, 153, 1);
		line-height: 45rpx;
	}

	.my_group {
		width: 120rpx;
		height: 50rpx;
		background: var(--color_spell_main);
		border-radius: 25rpx;
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: rgba(248, 248, 248, 1);
		line-height: 50rpx;
		text-align: center;
	}

	.group_pre_num {
		display: fixed;
		bottom: 0;
		z-index: 250;
		font-size: 22rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: rgba(153, 153, 153, 1);
		line-height: 45rpx;
		text-align: center;
	}

	.make_order_pre_left {
		display: flex;
		align-items: center;
	}
</style>
