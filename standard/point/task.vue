<template>
    <view class="page" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }">
        <ktabbar :bgImg="imgUrl + 'default_bg.jpg'" :title="'积分任务'" />

        <view class="container">
            <!-- 车主任务 -->
            <view class="task-section">
                <view class="section-title">车主任务</view>
                <view v-for="(task, index) in ownerTasks" :key="task.id">
                    <view class="task-item">
                        <view class="task-info">
                            <text class="task-name">{{ task.name }}</text>
                            <view class="task-points">
                                <image class="point-icon" src="/static/mp-qq/task/point.png" mode="aspectFit"></image>
                                <text>+{{ task.points }}积分</text>
                            </view>
                        </view>
                        <view class="task-action">
                            <view :class="'task-btn ' + task.status" @click="handleTaskClick(task)">
                                {{ getTaskButtonText(task) }}
                            </view>
                        </view>
                    </view>
                    <view class="task-divider"></view>
                </view>
            </view>

            <!-- 每月任务 -->
            <view class="task-section">
                <view class="section-title">每月任务</view>
                <view v-for="(task, index) in monthlyTasks" :key="task.id">
                    <view class="task-item">
                        <view class="task-info">
                            <text class="task-name">{{ task.name }}
                                <text v-if="task.progress" class="task-progress">（{{ task.progress.current }}/{{ task.progress.total }}次）</text>
                            </text>
                            <view class="task-points">
                                <image class="point-icon" src="/static/mp-qq/task/point.png" mode="aspectFit"></image>
                                <text>每天+{{ task.points }}积分</text>
                            </view>
                        </view>
                        <view class="task-action">
                            <view :class="'task-btn ' + task.status" @click="handleTaskClick(task)">
                                {{ getTaskButtonText(task) }}
                            </view>
                        </view>
                    </view>
                    <view v-if="index < monthlyTasks.length - 1" class="task-divider"></view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import ktabbar from '@/components/ktabbar.vue';
export default {
    components: {
        ktabbar
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            // 车主任务
            ownerTasks: [],
            // 每月任务
            monthlyTasks: []
        };
    },
    onLoad() {
        // 直接加载真实数据
        this.loadTaskData();
    },
    onPullDownRefresh() {
        // 下拉刷新
        this.loadTaskData();
    },
    methods: {
        // 加载任务数据
        loadTaskData() {
            // 显示全局loading
            uni.showLoading({
                title: '加载中...',
                mask: true
            });

            this.$request({
                url: 'v3/member/front/member/memberIntegralTask',
                method: 'GET'
            }).then(res => {
                uni.hideLoading(); // 隐藏全局loading
                uni.stopPullDownRefresh(); // 停止下拉刷新

                if (res.state == 200) {
                    // 处理API返回的数据
                    this.processTaskData(res.data);
                } else {
                    // API返回错误
                    this.$api.msg(res.msg || '获取任务数据失败');
                }
            }).catch(err => {
                uni.hideLoading(); // 隐藏全局loading
                uni.stopPullDownRefresh(); // 停止下拉刷新
                console.error('加载任务数据失败:', err);
                this.$api.msg('网络错误，请稍后重试');
            });
        },

        // 处理API返回的任务数据
        processTaskData(data) {
            try {
                if (data && Array.isArray(data)) {
                    // 清空现有数据
                    this.ownerTasks = [];
                    this.monthlyTasks = [];

                    // 处理每个任务
                    data.forEach(task => {
                        const processedTask = {
                            id: task.type,
                            name: task.name,
                            points: task.integral,
                            status: task.status === 1 ? 'completed' : 'available',
                            type: task.type
                        };

                        // 如果有进度信息，添加进度
                        if (task.nowNum !== null && task.totalNum !== null) {
                            processedTask.progress = {
                                current: task.nowNum,
                                total: task.totalNum
                            };
                        }

                        // 根据type分类：1-4是车主任务，5-10是每月任务
                        if (task.type >= 1 && task.type <= 4) {
                            this.ownerTasks.push(processedTask);
                        } else if (task.type >= 5 && task.type <= 10) {
                            this.monthlyTasks.push(processedTask);
                        }
                    });

                    console.log('任务数据加载成功:', {
                        ownerTasks: this.ownerTasks,
                        monthlyTasks: this.monthlyTasks
                    });
                } else {
                    // 如果数据格式不正确，使用默认数据
                    console.warn('API返回数据格式不正确:', data);
                    this.useDefaultData();
                }
            } catch (error) {
                console.error('处理任务数据时出错:', error);
                this.useDefaultData();
            }
        },



        // 获取任务按钮文字
        getTaskButtonText(task) {
            return task.status === 'completed' ? '已完成' : '去完成';
        },
        // 处理任务点击
        handleTaskClick(task) {
            if (task.status === 'completed') {
                this.$api.msg('任务已完成');
                return;
            }
            // 根据任务类型跳转到相应页面
            this.navigateToTask(task);
        },
        // 跳转到任务页面
        navigateToTask(task) {
            switch (task.type) {
                case 1: // 每日签到
                    this.$api.msg('跳转到签到页面');
                    break;
                case 2: // 绑定车辆
                    this.$api.msg('跳转到绑定车辆页面');
                    break;
                case 3: // 三包激活
                    this.$api.msg('跳转到三包激活页面');
                    break;
                case 4: // 到店维修
                    this.$api.msg('跳转到到店维修页面');
                    break;
                case 5: // 浏览商品好物
                    this.$Router.push('/standard/product/list');
                    break;
                case 6: // 购买商城订单
                    this.$Router.push('/standard/product/list');
                    break;
                case 7: // 社区内容发布
                    this.$api.msg('跳转到社区发布页面');
                    break;
                case 8: // 社区内容分享
                    this.$api.msg('跳转到社区分享页面');
                    break;
                case 9: // 社区评论发布
                    this.$api.msg('跳转到社区评论页面');
                    break;
                case 10: // 社区账号关注
                    this.$api.msg('跳转到社区关注页面');
                    break;
                default:
                    this.$api.msg('功能开发中...');
            }
        }
    }
};
</script>

<style lang="scss">
.page {
    padding-bottom: 50rpx;
    width: 750rpx;
    min-height: 100vh;
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    // 背景图片固定，不滚动
    background-attachment: fixed;

    .container {
        width: 92%;
        margin: 0 auto;
        margin-top: 50rpx;
        background: #fff;
        border-radius: 40rpx;
        padding: 40rpx 0rpx;
        display: flex;
        flex-direction: column;
        row-gap: 40rpx;

        .task-section {
            .section-title {
                font-size: 32rpx;
                font-weight: 600;
                color: #333;
                text-align: center;
                margin-bottom: 30rpx;
                position: relative;

                &::after {
                    content: '';
                    position: absolute;
                    bottom: -10rpx;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 60rpx;
                    height: 4rpx;
                    background: rgba(199, 14, 45, 1);
                    border-radius: 2rpx;
                }
            }

            .task-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 15rpx 30rpx;
                margin-bottom: 20rpx;

                &:last-child {
                    margin-bottom: 0;
                }
            }

            .task-divider {
                height: 1rpx;
                background-color: rgba(242, 242, 242, 1);
                margin-left: 60rpx;
                margin-bottom: 20rpx;
                margin-right: 30rpx;
            }

            .task-info {
                flex: 1;

                .task-name {
                    display: block;
                    font-size: 28rpx;
                    color: rgba(33, 33, 33, 1);
                    font-weight: 500;
                    margin-bottom: 10rpx;

                    .task-progress {
                        color: rgba(33, 33, 33, 1);
                        font-weight: 400;
                    }
                }

                .task-points {
                    display: flex;
                    align-items: center;
                    font-size: 24rpx;
                    color: rgba(199, 14, 45, 1);
                    font-weight: 500;

                    .point-icon {
                        width: 24rpx;
                        height: 24rpx;
                        margin-right: 8rpx;
                    }
                }
            }

            .task-action {
                .task-btn {
                    padding: 8rpx 15rpx;
                    border-radius: 60rpx;
                    font-size: 24rpx;
                    text-align: center;
                    min-width: 120rpx;
                    border: 2rpx solid transparent;

                    &.completed {
                        background: transparent;
                        border: 2rpx solid rgba(0, 0, 0, 0.5);
                        color: rgba(0, 0, 0, 0.5);
                    }

                    &.available {
                        // background: rgba(33, 33, 33, 1);
                        color: rgba(33, 33, 33, 1);
                        border: 2rpx solid rgba(33, 33, 33, 1);
                        // box-shadow: 0px 4rpx 20rpx 0px rgba(33, 33, 33, 0.3);
                    }
                }
            }
        }
    }
}
</style>
