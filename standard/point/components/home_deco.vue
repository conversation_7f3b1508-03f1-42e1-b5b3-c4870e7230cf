<template>
	<view :class="isCookie == true ? 'container prevent' : 'container_int'">
		<!-- 小程序头部兼容 -->
		<view class="mp-search-box" v-if="is_show_top == true"
			:style="{ background: pure_bg_color, marginTop: statusBarFlag == 1 ? statusBarHeight : 'auto' }">
			<view class="mp-search-header" v-if="statusBarFlag == 1"
				:style="{ height: statusBarHeight, background: pure_bg_color }"></view>
			<view class="return_con" @click="back">
				<image class="return" :src="imgUrl + 'point/return.png'" mode="aspectFit"></image>
			</view>
			<!-- #ifdef MP -->
			<view class="title">
				<image v-if="activity_open" class="search searchs" @click="goSearch" :src="imgUrl + 'point/search.png'"
					mode="aspectFit"></image>
				积分商城
			</view>
			<view class="search"></view>
			<!-- #endif -->
			<!-- app-1-start -->
			<!-- #ifdef APP -->
			<view class="title">
				{{ $L('积分商城') }}
			</view>
			<image v-if="activity_open" class="search" @click="goSearch" :src="imgUrl + 'point/search.png'"
				mode="aspectFit"></image>
			<!-- #endif -->
			<!-- app-1-end -->

			<!-- #ifdef H5 -->
			<view class="left_h5" @tap="goSearch">
				<image :src="imgUrl + 'svideo/search_icon.png'" mode="aspectFit"></image>
				<text>{{ $L('请输入关键词') }}</text>
			</view>
			<!-- #endif -->
		</view>
		<!-- 头部分类 -->
		<tab-menu :backGround="pure_bg_color" :tabInfo="sort_nav_list" @getChildList="getChildList"
			v-if="activity_open && is_show_top == true && home_is_show_top_cat" ref="tabMenu"></tab-menu>
		<!-- 首页装修 -->
		<view v-if="activity_open && is_show_index == true && isShow == true"
			:class="isShowTab == true ? (home_is_show_top_cat ? 'deco_wrap' : 'deco_wrap_no_top_cat') : 'deco_wrap2'"
			:style="{
				marginTop: is_show_top == false ? '110rpx' : '',
				marginTop: isIos ? '-22rpx' : 0,
				marginTop: statusBarFlag == 1 ? 'calc(' + statusBarHeight + ' + 154rpx)' : 'auto'
			}">
			<view class="index_deco" v-for="(decoItem, decoIndex) in deco_info" :key="decoIndex">
				<!-- 头部轮播 -->
				<view class="carousel-section deco_carousel-section" style="padding-top: 10rpx"
					v-if="decoItem.type == 'top_cat_nav' && decoItem.data.length > 0">
					<!-- 背景色区域 -->
					<view
						:class="decoItem.swiper_bg_style == 1 ? 'titleNview-background top_swiper_style1' : 'titleNview-background top_swiper_style2'"
						:style="{
							background: titleNViewBackground,
							borderRadius: decoItem.swiper_bg_style == 1 ? 'border_radius' : '0'
						}"></view>
					<uni-swiper-dot :current="swiperCurrent" :info="decoItem.data" mode="dot" :dotsStyles="dotsStyles">
						<swiper class="carousel" circular @change="swiperChange" autoplay="true" :style="{
								margin: isIos ? '10rpx' : 0,
								width: '710rpx',
								height: (decoItem.height / decoItem.width) * 710 + 'rpx'
							}">
							<swiper-item v-for="(item, index) in decoItem.data" :key="index" class="carousel-item">
								<image :src="item.img" mode="aspectFit"
									@click="skipTo(item.url_type, item.url, item.info.productId || item.info.defaultProductId, item.info.pid, item.info.labelName, item, 'swiper')" />
							</swiper-item>
						</swiper>
					</uni-swiper-dot>
				</view>
				<!-- 头部轮播 -->
				<!-- 公告 -->
				<view class="notice_box" v-if="decoItem.type == 'gonggao' && decoItem.is_show == true">
					<!-- 公告样式一 -->
					<view class="notice_wrap1" v-if="decoItem.type == 'gonggao' && decoItem.show_style == 'one'"
						@click="skipTo(decoItem.url_type, decoItem.url, decoItem.info.productId || decoItem.info.defaultProductId, decoItem.info.pid, decoItem.info.labelName)">
						<image :src="noticeImg1" mode="aspectFit" class="notice_img1"></image>
						<view class="notice_content_wrap">
							<uni-notice-bar :scrollable="true" :single="true" :text="decoItem.text" color="#666"
								backClass="backColorClass1" />
						</view>
						<view class="notice_wrap1_line"></view>
						<view class="notice_more">>></view>
					</view>
					<!-- 公告样式二 -->
					<view class="notice_wrap2" v-if="decoItem.type == 'gonggao' && decoItem.show_style == 'two'"
						@click="skipTo(decoItem.url_type, decoItem.url, decoItem.info.productId || decoItem.info.defaultProductId, decoItem.info.pid, decoItem.info.labelName)">
						<image :src="noticeImg2" mode="aspectFit" class="notice_img2"></image>
						<view class="notice_content_wrap2">
							<uni-notice-bar :scrollable="true" :single="true" :text="decoItem.text" color="#fff"
								backClass="backColorClass2" />
						</view>
						<view class="notice_wrap2_line"></view>
						<view class="notice_more">>></view>
					</view>
				</view>

				<view class="nav_wrap" v-if="decoItem.type == 'nav' && decoItem.is_show == true">
					<!-- 导航样式一、二（图标在上/不显示图标） -->
					<view class="cate-section"
						v-if="(decoItem.style_set == 'nav' && decoItem.icon_set == 'up') || (decoItem.icon_set == 'no-icon' && decoItem.is_show == true)">
						<view class="cate-item" v-for="(item, index) in decoItem.data" :key="index"
							@click="skipTo(item.url_type, item.url, item.info.productId || item.info.defaultProductId, item.info.pid, item.info.labelName)">
							<image :src="item.img" v-if="decoItem.icon_set == 'up'" mode="aspectFit"></image>
							<text>{{ filters.toSubstring(item.name, 0, 9) }}</text>
						</view>
					</view>

					<!-- 导航样式三 （图标文字左右显示）-->
					<view class="cate-section"
						v-if="decoItem.style_set == 'nav' && decoItem.icon_set == 'left' && decoItem.is_show == true"
						style="justify-content: flex-start; padding: 20rpx 2rpx">
						<view class="cate-item2" v-for="(item, index) in decoItem.data" :key="index"
							@click="skipTo(item.url_type, item.url, item.info.productId || item.info.defaultProductId, item.info.pid, item.info.labelName)">
							<image :src="item.img" style="margin-right: 10rpx" mode="aspectFit"></image>
							<view class="cate_name">{{ filters.toSubstring(item.name, 0, 9) }}</view>
						</view>
					</view>

					<!-- 导航分组 -->
					<view class="nav_group" v-if="decoItem.style_set == 'tag-nav' && decoItem.is_show == true">
						<view class="nav_group_item" v-for="(item, index) in decoItem.data" :key="index"
							@click="skipTo(item.url_type, item.url, item.info.productId || item.info.defaultProductId, item.info.pid, item.info.labelName)">
							<image :src="item.img" mode="aspectFit"></image>
							<view class="nav_group_name">{{ item.name }}</view>
						</view>
					</view>
				</view>

				<!-- 客服 -->
				<view class="service_wrap" v-if="decoItem.type == 'kefu' && decoItem.is_show == true"
					@click="callUp(decoItem.tel)">
					<image :src="telImg" mode="aspectFit"></image>
					<text>{{ decoItem.text }}{{ decoItem.tel }}</text>
				</view>

				<!-- 富文本 -->
				<view class="rich_text_wrap" v-if="decoItem.type == 'fuwenben' && decoItem.is_show == true">
					<jyfParser :isAll="true" :html="decoItem.text"></jyfParser>
				</view>

				<!-- 图片组合 -->
				<view class="combination_wrap" v-if="decoItem.type == 'tupianzuhe'">
					<!-- 图片组合0123 -->
					<view v-if="decoItem.type == 'tupianzuhe' && decoItem.sele_style < 4"
						style="background-color: #fff">
						<view class="modules-slide">
							<view :class="'image-list style' + decoItem.sele_style" v-if="decoItem.sele_style < 3">
								<view
									:class="decoItem.sele_style == 2 ? 'combination_style no_margin_right flex_row_start_start tupianzuhe2' : 'space_between combination_style'"
									:style="{
										display: decoItem.sele_style < 2 ? 'block' : 'flex'
									}">
									<view v-for="(childitem, childindex) in decoItem.data" :key="childindex"
										:class="decoItem.sele_style < 2 ? 'combine1' : 'combine2'" :style="{
											marginTop: decoItem.sele_style == 1 ? '20rpx' : '0',
											marginRight: decoItem.sele_style < 3 ? '0' : '20rpx',
											marginBottom: decoItem.sele_style < 3 ? '0' : '20rpx'
										}">
										<view class="flex_column_start_center"
											v-if="decoItem.sele_style == 0 || decoItem.sele_style == 1">
											<image v-if="decoItem.sele_style == 0" @click="
													skipTo(
														childitem.url_type,
														childitem.url,
														childitem.info.productId || childitem.info.defaultProductId,
														childitem.info.pid,
														childitem.info.labelName
													)
												" mode="aspectFit" :src="childitem.img" :style="{
													display: 'block',
													width: '750rpx',
													height: (750 * childitem.height) / childitem.width + 'rpx'
												}"></image>
											<image v-if="decoItem.sele_style == 1" @click="
													skipTo(
														childitem.url_type,
														childitem.url,
														childitem.info.productId || childitem.info.defaultProductId,
														childitem.info.pid,
														childitem.info.labelName
													)
												" mode="aspectFit" :src="childitem.img" :style="{
													display: 'block',
													width: '710rpx',
													height: (710 * childitem.height) / childitem.width + 'rpx',
													'margin-bottom': childindex == decoItem.data.length - 1 ? '20rpx' : 0
												}"></image>
										</view>

										<view class="flex_row_center_center combine3" v-if="decoItem.sele_style == 2"
											:style="{
												height:
													childindex % 2 == 0
														? (345 * childitem.height) / childitem.width + 'rpx'
														: (345 * decoItem.data[childindex - 1].height) / decoItem.data[childindex - 1].width + 'rpx',
												'margin-left': '20rpx'
											}">
											<image @click="
													skipTo(
														childitem.url_type,
														childitem.url,
														childitem.info.productId || childitem.info.defaultProductId,
														childitem.info.pid,
														childitem.info.labelName
													)
												" mode="aspectFit" :src="childitem.img" style="width: 100%; height: 100%"></image>
										</view>
									</view>
								</view>
							</view>

							<view :class="'image-list style' + decoItem.sele_style" v-if="decoItem.sele_style == 3">
								<view class="combination_style no_margin_right2"
									style="display: flex; margin-bottom: 20rpx; flex-wrap: wrap">
									<view v-for="(childitem, childindex) in decoItem.data" :key="childindex"
										class="combine2">
										<view class="combine4" :style="{
												height:
													childindex % 3 == 0
														? ((690 / 3) * childitem.height) / childitem.width + 'rpx'
														: childindex % 3 == 1
														? ((690 / 3) * decoItem.data[childindex - 1].height) / decoItem.data[childindex - 1].width + 'rpx'
														: ((690 / 3) * decoItem.data[childindex - 2].height) / decoItem.data[childindex - 2].width + 'rpx'
											}">
											<image @click="
													skipTo(
														childitem.url_type,
														childitem.url,
														childitem.info.productId || childitem.info.defaultProductId,
														childitem.info.pid,
														childitem.info.labelName
													)
												" mode="aspectFit" :src="childitem.img"></image>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!--图片组合4  -->
					<view v-if="decoItem.type == 'tupianzuhe' && decoItem.sele_style == 4"
						style="background-color: #fff">
						<view class="modules-slide">
							<view class="image-ad clearfix images-tpl">
								<view style="display: flex">
									<view class="tupianzuhe04_left flex_row_center_center" @click="
											skipTo(
												decoItem.data[0].url_type,
												decoItem.data[0].url,
												decoItem.data[0].info.productId || decoItem.data[0].info.defaultProductId,
												decoItem.data[0].info.pid
											),
												decoItem.data[0].info.labelName
										">
										<image mode="aspectFit" :src="decoItem.data[0].img"></image>
									</view>
									<view style="display: flex; flex-direction: column; justify-content: space-between">
										<view class="tupianzuhe04_right_item flex_row_center_center" @click="
												skipTo(
													decoItem.data[1].url_type,
													decoItem.data[1].url,
													decoItem.data[1].info.productId || decoItem.data[1].info.defaultProductId,
													decoItem.data[1].info.pid,
													decoItem.data[1].info.labelName
												)
											">
											<image mode="aspectFit" :src="decoItem.data[1].img"></image>
										</view>
										<view class="tupianzuhe04_right_item flex_row_center_center" @click="
												skipTo(
													decoItem.data[2].url_type,
													decoItem.data[2].url,
													decoItem.data[2].info.productId || decoItem.data[2].info.defaultProductId,
													decoItem.data[2].info.pid,
													decoItem.data[2].info.labelName
												)
											">
											<image mode="aspectFit" :src="decoItem.data[2].img"></image>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 图片组合5 6 display: flex;flex-wrap:wrap;-->
					<view v-if="(decoItem.type == 'tupianzuhe' && decoItem.sele_style == 5) || decoItem.sele_style == 6"
						style="background-color: #fff">
						<view class="modules-slide">
							<view class="image-ad clearfix images-tpl" style="padding-top: 0">
								<view class="combine5_wrap" style="display: flex; flex-wrap: wrap; width: 100%"
									v-if="decoItem.sele_style == 5">
									<view v-for="(childitem, childindex) in decoItem.data" :key="childindex"
										class="combine5 flex_row_center_center" @click="
											skipTo(
												childitem.url_type,
												childitem.url,
												childitem.info.productId || childitem.info.defaultProductId,
												childitem.info.pid,
												childitem.info.labelName
											)
										" :style="{
											width: childindex == 0 || childindex == 3 ? 230 + 'rpx' : 460 + 'rpx',
											height: '230rpx',
											marginTop: '20rpx',
											marginLeft: '20rpx',
											backGround: 'red'
										}">
										<image mode="aspectFit" :src="childitem.img" style="width: 100%; height: 100%">
										</image>
									</view>
								</view>

								<view class="" v-if="decoItem.sele_style == 6" style="display: flex">
									<view class="combine6" style="margin-left: 20rpx">
										<view class="flex_row_center_center" :style="{
												width: '345rpx',
												height: 345 / 2 + 'rpx',
												'flex-shrink': 0,
												'margin-bottom': '20rpx'
											}" @click="
												skipTo(
													decoItem.data[0].url_type,
													decoItem.data[0].url,
													decoItem.data[0].info.productId || decoItem.data[0].info.defaultProductId,
													decoItem.data[0].info.pid,
													decoItem.data[0].info.labelName
												)
											">
											<image :src="decoItem.data[0].img" mode="aspectFit"
												style="width: 100%; height: 100%"></image>
										</view>
										<view class="flex_row_center_center" :style="{
												width: '345rpx',
												height: '345rpx',
												'flex-shrink': 0
											}" @click="
												skipTo(
													decoItem.data[1].url_type,
													decoItem.data[1].url,
													decoItem.data[1].info.productId || decoItem.data[1].info.defaultProductId,
													decoItem.data[1].info.pid,
													decoItem.data[1].info.labelName
												)
											">
											<image :src="decoItem.data[1].img" mode="aspectFit"
												style="width: 100%; height: 100%"></image>
										</view>
									</view>
									<view class="combine6">
										<view class="flex_row_center_center" :style="{
												width: '345rpx',
												height: '345rpx',
												'flex-shrink': 0,
												'margin-bottom': '20rpx'
											}" @click="
												skipTo(
													decoItem.data[2].url_type,
													decoItem.data[2].url,
													decoItem.data[2].info.productId || decoItem.data[2].info.defaultProductId,
													decoItem.data[2].info.pid,
													decoItem.data[2].info.labelName
												)
											">
											<image :src="decoItem.data[2].img" mode="aspectFit"
												style="width: 100%; height: 100%"></image>
										</view>
										<view class="flex_row_center_center" :style="{
												width: '345rpx',
												height: 345 / 2 + 'rpx',
												'flex-shrink': 0
											}" @click="
												skipTo(
													decoItem.data[3].url_type,
													decoItem.data[3].url,
													decoItem.data[3].info.productId || decoItem.data[3].info.defaultProductId,
													decoItem.data[3].info.pid,
													decoItem.data[3].info.labelName
												)
											">
											<image :src="decoItem.data[3].img" mode="aspectFit"
												style="width: 100%; height: 100%"></image>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 图片组合7-->
					<view v-if="decoItem.sele_style == 7" data-index="index" style="background-color: #fff">
						<view class="modules-slide">
							<view class="image-ad images-tpl"
								style="display: flex; justify-content: flex-start; align-items: center; padding-top: 0">
								<view :style="{
										display: 'flex',
										'flex-wrap': 'wrap',
										width: (670 / 3) * 2 + 61 + 'rpx'
									}">
									<view class="flex_row_center_center"
										v-for="(childitem, childindex) in decoItem.data" v-if="childindex < 4"
										:key="childindex" @click="
											skipTo(
												childitem.url_type,
												childitem.url,
												childitem.info.productId || childitem.info.defaultProductId,
												childitem.info.pid,
												childitem.info.labelName
											)
										" :style="{
											'margin-left': '20rpx',
											width: 670 / 3 + 'rpx',
											height: 670 / 3 + 'rpx',
											marginTop: '20rpx',
											'flex-shrink': 0
										}">
										<image mode="aspectFit" :src="childitem.img" style="width: 100%; height: 100%">
										</image>
									</view>
								</view>
								<view class="flex_row_center_center" v-if="decoItem.data[4]" @click="
										skipTo(
											decoItem.data[4].url_type,
											decoItem.data[4].url,
											decoItem.data[4].info.productId || decoItem.data[4].info.defaultProductId,
											decoItem.data[4].info.pid,
											decoItem.data[4].info.labelName
										)
									" :style="{
										'margin-top': '20rpx',
										width: 670 / 3 + 'rpx',
										height: (670 / 3) * 2 + 20 + 'rpx'
									}">
									<image mode="aspectFit" :src="decoItem.data[4].img"
										style="width: 100%; height: 100%"></image>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 搭配 -->
				<view class="match_wrap" v-if="decoItem.type == 'dapei' && decoItem.is_show == true">
					<view class="match_top">
						<view class="match_top_title" v-if="decoItem.dapei_title">{{ decoItem.dapei_title }}</view>
						<view class="match_image_wrap flex_row_center_center">
							<image :src="decoItem.dapei_img" mode="aspectFit" class="match_image" :style="{
									width: '710rpx',
									height: (710 * decoItem.height) / decoItem.width + 'rpx'
								}"></image>
						</view>
						<view class="match_top_text" v-if="decoItem.dapei_desc">{{ decoItem.dapei_desc }}</view>
					</view>
					<view class="match_main_wrap">
						<view class="match_main" v-if="decoItem && decoItem.data && decoItem.data.info">
							<view class="match_item" v-for="(item, index) in decoItem.data.info" :key="index"
								@click="toGoodsDetail(item.productId || item.defaultProductId, item.goodsId)">
								<view class="match_goods_img">
									<image :src="item.mainImage" mode="aspectFit"></image>
								</view>
								<view class="match_goods_name">{{ item.goodsName }}</view>
								<view class="match_goods_price">
									<text class="small_price">￥</text>
									<text
										class="big_price">{{ filters.toSplit(filters.toFix(item.goodsPrice))[0] }}</text>
									.
									<text
										class="small_price">{{ filters.toSplit(filters.toFix(item.goodsPrice))[1] }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 辅助线 -->
				<view class="subline_wrap" v-if="decoItem.type == 'fzx' && decoItem.is_show == true" :style="{
						paddingLeft: decoItem.lrmargin * 2 + 'rpx',
						paddingRight: decoItem.lrmargin * 2 + 'rpx',
						marginTop: decoItem.tbmargin * 2 + 'rpx',
						marginBottom: decoItem.tbmargin * 2 + 'rpx'
					}">
					<view class="subline" :style="{
							height: decoItem.tbmargin + 'px',
							borderBottomColor: decoItem.color,
							borderBottomStyle: decoItem.val
						}"></view>
					<view :style="{ height: decoItem.tbmargin + 'px' }"></view>
				</view>

				<!-- 轮播图 -->
				<view class="carousel_bottom_wrap" v-if="decoItem.type == 'lunbo' && decoItem.is_show == true"
					style="padding: 0">
					<swiper class="carousel carousel_bottom" circular autoplay="true" :style="{
							width: decoItem.width + 'rpx',
							height: decoItem.height + 'rpx'
						}">
						<swiper-item v-for="(item, index) in decoItem.data" :key="index" class="carousel-item"
							style="padding: 0"
							@click="skipTo(item.url_type, item.url, item.info.productId || item.info.defaultProductId, item.info.pid, item.info.labelName)">
							<image :src="item.img" class="carousel_img" mode="aspectFit" />
						</swiper-item>
					</swiper>
				</view>

				<!-- 推荐商品样式一 -->
				<view class="recommend_goods_wrap"
					v-if="decoItem.type == 'tuijianshangpin' && decoItem.show_style == 'small'" style="padding: 0">
					<view :style="{
							backgroundColor: decoItem.border_style == 'border_none_grey_bg' ? 'f8f8f8' : '#fff',
							paddingLeft: decoItem.page_margin * 2 + 'rpx',
							paddingRight: decoItem.page_margin * 2 + 'rpx',
							paddingTop: '20rpx'
						}" class="rec_goods_wrap">
						<recommendItemV :goods_info="item" :show_sale="decoItem.isshow_sales == 1 ? true : false"
							:icon_type="decoItem.cart_icon_type" :height="225" :border_radius="decoItem.border_radius"
							:border_style="decoItem.border_style" :goods_margin="decoItem.goods_margin"
							:page_margin="decoItem.page_margin" v-for="(item, index) in decoItem.data.info" :key="index"
							@click="skipTo(decoItem.type, item.gid, item.info)" :isSearchGoods="false"></recommendItemV>
					</view>
				</view>
				<!-- 推荐商品样式二 -->
				<view class="recommend_goods_wrap"
					v-if="decoItem.type == 'tuijianshangpin' && decoItem.show_style == 'list' && decoItem.is_show == true"
					:style="{
						paddingLeft: decoItem.page_margin + 'px',
						paddingRight: decoItem.page_margin + 'px',
						marginTop: '20rpx',
						backgroundColor: decoItem.border_style == 'border_none_grey_bg' ? 'f8f8f8' : '#fff'
					}">
					<view class="rec_goods_wrap">
						<view class="recommend_goods1" v-for="(item, index) in decoItem.data.info" :key="index" :style="{
								borderRadius: decoItem.border_radius + 'px',
								border: decoItem.border_style == 'border_eee' ? '1rpx solid #eee' : '',
								boxShadow: decoItem.border_style == 'card-shadow' ? 'rgba(93, 113, 127, 0.08) 0px 2px 8px' : '',
								marginBottom: decoItem.goods_margin + 'px'
							}">
							<view class="recommend_goods_img1"
								@click="toGoodsDetail(item.productId || item.defaultProductId, item.goodsId)">
								<image :src="item.mainImage" mode="aspectFill"
									:style="{ borderRadius: border_radius2 }"></image>
							</view>
							<view class="recommend_goods_right" :style="{ width: 'calc(750rpx - 440rpx)' }">
								<view class="recommend_goods_name"
									@click="toGoodsDetail(item.productId || item.defaultProductId, item.goodsId)">
									{{ item.goodsName }}
								</view>

								<view class="goods_des">{{ item.goodsBrief }}</view>

								<view :class="decoItem.isshow_sales == 1 ? '' : 'hide_sold_wrap'">
									<view class="recommend_goods_price" :style="{
											position: decoItem.isshow_sales == 1 ? 'absolute' : 'static'
										}">
										<view class="goods_price" v-if="item.marketPrice">
											<text>￥</text>
											<text>{{ $getPartNumber(item.marketPrice, 'int') }}</text>
											<text>{{ $getPartNumber(item.marketPrice, 'decimal') }}</text>
										</view>
										<view class="goods_price_point" v-if="item.integralPrice || item.cashPrice">
											<text
												v-if="item.integralPrice">{{ item.integralPrice }}{{ $L('积分') }}</text>
											<text v-if="item.integralPrice && item.cashPrice">+</text>
											<text
												v-if="item.cashPrice && item.integralPrice.toString().length + item.cashPrice.toString().length <= 14">
												{{ $L('￥') }}{{ filters.toFix(item.cashPrice) }}
											</text>
											<text
												v-if="item.integralPrice.toString().length + item.cashPrice.toString().length > 14">{{ $L('￥') }}...</text>
										</view>
									</view>
									<view class="recommend_goods_bottom" :style="{
											position: decoItem.isshow_sales == 1 ? 'absolute' : 'static',
											width: decoItem.isshow_sales == 1 ? '100%' : 'auto'
										}">
										<view class="have_sold" v-if="decoItem.isshow_sales == 1">
											{{ $L('已售') }}{{ item.actualSales }}{{ $L('件') }}
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				<!-- 推荐商品样式三 -->
				<view class="recommend_goods_wrap"
					v-if="decoItem.type == 'tuijianshangpin' && decoItem.show_style == 'big' && decoItem.is_show == true"
					:style="{
						paddingLeft: decoItem.page_margin + 'px',
						paddingRight: decoItem.page_margin + 'px',
						marginTop: 0,
						backgroundColor: decoItem.border_style == 'border_none_grey_bg' ? 'f8f8f8' : '#fff'
					}">
					<view class="recommend_goods2" v-for="(item, index) in decoItem.data.info" :key="index" :style="{
							borderRadius: decoItem.border_radius + 'px',
							border: decoItem.border_style == 'border_eee' ? '1rpx solid #eee' : '',
							boxShadow: decoItem.border_style == 'card-shadow' ? 'rgba(93, 113, 127, 0.08) 0px 2px 8px' : '',
							marginBottom: decoItem.goods_margin + 'px'
						}">
						<view class="recommend_goods_img2"
							@click="toGoodsDetail(item.productId || item.defaultProductId, item.goodsId)">
							<image :src="item.mainImage" mode="aspectFill" :style="{ borderRadius: border_radius1 }">
							</image>
						</view>
						<view class="recommend_goods_bottom2" :style="{ borderRadius: border_radius3 }">
							<view class="recommend_goods_name2"
								@click="toGoodsDetail(item.productId || item.defaultProductId, item.goodsId)">
								{{ item.goodsName }}
							</view>

							<view class="goods_des">{{ item.goodsBrief }}</view>

							<view class="goods_bottom" :style="{
									flexDirection: decoItem.isshow_sales == 1 ? '' : 'row',
									justifyContent: decoItem.isshow_sales == 1 ? '' : 'space-between'
								}">
								<view class="goods_price_point" v-if="item.integralPrice || item.cashPrice">
									<text v-if="item.integralPrice">{{ item.integralPrice }}{{ $L('积分') }}</text>
									<text v-if="item.integralPrice && item.cashPrice">+</text>
									<text
										v-if="item.cashPrice && item.integralPrice.toString().length + item.cashPrice.toString().length <= 14">
										{{ $L('￥') }}{{ filters.toFix(item.cashPrice) }}
									</text>
									<text
										v-if="item.integralPrice.toString().length + item.cashPrice.toString().length > 14">{{ $L('￥') }}...</text>
								</view>
								<view class="recommond_goods3_wrap">
									<view class="have_sold" v-if="decoItem.isshow_sales == 1">
										{{ $L('已售') }}{{ item.actualSales }}{{ $L('件') }}
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 辅助空白 -->
				<view class="blank_wrap" v-if="decoItem.type == 'fzkb' && decoItem.id && decoItem.is_show == true"
					:style="{
						backgroundColor: decoItem.color,
						height: decoItem.text + 'px'
					}"></view>
				<!-- tab切换 -->
				<sort-list :nav_list="decoItem.data" :isShowSale="false" :borderRadius="decoItem.border_radius"
					v-if="decoItem.type == 'more_tab' && decoItem.is_show == true" :card_height="225"></sort-list>
			</view>
		</view>
		<view class="carousel-section laber_banner_list_class"
			v-if="activity_open && tab_index > 0 && laber_banner_list.length > 0">
			<!-- 背景色区域 -->
			<view
				:class="swiper_bg_style == 1 ? 'titleNview-background top_swiper_style1' : 'titleNview-background top_swiper_style2'"
				:style="{
					background: titleNViewBackground,
					borderRadius: swiper_bg_style == 1 ? 'border_radius' : '0'
				}"></view>
			<uni-swiper-dot mode="dot" :dotsStyles="dotsStyles">
				<swiper class="carousel" circular @change="labelSwiperChange" autoplay="true" :style="{
						margin: isIos ? '10rpx' : 0,
						width: '710rpx',
						height: (laber_banner_list[0].height / laber_banner_list[0].width) * 710 + 'rpx'
					}">
					<swiper-item v-for="(item, index) in laber_banner_list" :key="index" class="carousel-item" @click="
							skipTo(
								item.link_type,
								item.link_value,
								item.info ? item.info.productId || item.info.defaultProductId : '',
								item.info ? item.info.pid : '',
								item.info ? item.info.labelName : '',
								item,
								'swiper'
							)
						">
						<image :src="item.imgUrl" mode="aspectFit" />
					</swiper-item>
				</swiper>
			</uni-swiper-dot>
		</view>
		<!-- 顶部分类切换 -->
		<view :class="{
				sort_sub_wrap: true,
				laber_banner_list_class: laber_banner_list && laber_banner_list.length == 0
			}" :style="{ marginTop: 'calc(160rpx + ' + statusBarHeight + ')' }"
			v-if="activity_open && tab_index != 0 && sort_obj.children.length > 0">
			<!-- 此处有问题 -->
			<view class="sort_sub_top1" v-if="sort_obj && sort_obj.children && tab_index != 0">
				<block v-for="(item, index) in sort_obj.children" :key="index">
					<view class="sort_sub_item" @click="goGoodsList(item.labelName, item.labelId)" v-if="
							sort_obj.children.length < 5 ||
							(index < 4 && sort_obj.children.length >= 5 && sort_obj.children.length < 10) ||
							(index < 9 && sort_obj.children.length >= 10)
						">
						<view class="sort_sub_img">
							<image :src="item.image" mode="aspectFit"></image>
						</view>
						<view class="sort_sub_name">{{ filterFun(item.labelName) }}</view>
					</view>
				</block>
				<view class="see_more_wrap" @click="goGoodsList('more', sort_obj.labelId)"
					v-if="sort_obj.children.length >= 5">
					<view class="more_icon_circle">
						<image :src="imgUrl + 'index/more.png'" mode="aspectFit" class="more_icon"></image>
					</view>
					<view class="see_more_text">{{ $L('查看更多') }}</view>
				</view>
			</view>
			<view class="sort_sub_goods" v-if="tab_index != 0">
				<recommendItemV :goods_info="item" :show_sale="false" :icon_type="1" :border_radius="8" :height="225"
					v-for="(item, index) in sort_obj.goodsList" :key="index" :isSearchGoods="false"></recommendItemV>
			</view>
		</view>
		<!-- <recommendItemList ref="recommendList"></recommendItemList> -->
		<!-- 空首页装修 -->
		<!-- <view class="empty_sort_page" v-if="activity_open && tab_index == 0 && deco_info == null"
			style="padding-top: 25vh">
			<image :src="imgUrl + 'empty_fitUp.png'" mode="aspectFit" class="empty_img"></image>
			<view class="empty_text">{{ $L('首页暂未装修') }}</view>
		</view> -->
		<!-- 分类空页面 -->
		<view class="empty_sort_page" v-if="activity_open && tab_index > 0 && is_show_empty == true">
			<image :src="imgUrl + 'empty_goods.png'" mode="aspectFit" class="empty_img"></image>
			<view class="empty_text">{{ $L('该分类暂无相关商品') }}</view>
		</view>
		<!-- 首页开屏框 start -->
		<view :class="pointIsCookie ? 'open_screen show-dialog' : 'open_screen hide-dialog'"
			v-if="pointIsCookie && home_page_img.length > 0 && home_page_img[0].imgUrl">
			<view :style="{ width: width + 'rpx', height: height + 'rpx' }" class="open_screen_con"
				@click="gotoGoods_detail">
				<view class="con_img" @click.stop="close_openScreen">
					<image :src="openscnImg" mode="aspectFit"></image>
				</view>
				<image class="open_screen_con_img" :src="home_page_img[0].imgUrl" mode="aspectFit"></image>
			</view>
		</view>
		<!-- 开屏框 end -->
		<!-- 活动未开启 start-->
		<notOpen v-if="!activity_open"></notOpen>
		<!-- 活动未开启 end -->
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex';
	import filters from '@/utils/filter.js';
	import tabMenu from './index-tab-menu.vue';
	import sortList from './index-sort-list.vue';
	import uniSwiperDot from './uni-swiper-dot/uni-swiper-dot.vue';
	import {
		decoType
	} from '@/utils/common.js';
	import goodsItemV from './goods_item_v.vue';
	import loadingState from './loading-state.vue';
	import recommendItemV from './recommend_item_v.vue';
	import recommendItemList from './recommend_list.vue';
	import notOpen from './not_open.vue';
	import jyfParser from '@/components/jyf-parser/jyf-parser.vue';
	export default {
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				titleNViewBackground: '',
				swiperCurrent: 0,
				swiperLength: 0,
				carouselList: [],
				statusBarHeight: uni.getSystemInfoSync().statusBarHeight + 'px',
				statusBarFlag: 1,
				goodsList: [],
				current: 0, //轮播图指示点
				dotsStyles: {
					selectedBackgroundColor: '#fff',
					width: 8,
					height: 8,
					selectedBorder: 'none',
					backgroundColor: 'rgba(255,255,255,0.4)',
					border: 'none',
					bottom: 8
				},
				noticeImg1: getApp().globalData.imgUrl + 'index/notice1.png',
				noticeImg2: getApp().globalData.imgUrl + 'index/notice2.png',
				// deco_info:[] ,//首页装修数据
				// home_page_img:[] , //首页开屏图列表
				dataObj: {}, //完整版首页装修数据
				border_radius: '', //顶部轮播背景圆角
				broadcastData1: [], //公告1滚动文字
				broadcastData2: [], //公告2滚动文字
				broadcastStyle1: {
					//滚动文字样式设置
					speed: 1, //每秒30px
					font_size: '24', //字体大小(rpx)
					text_color: '#666', //字体颜色
					back_color: 'linear-gradient(to right,rgba(250,244,244,0.2) 0%, rgba(255,244,244,1) 50%, rgba(250,244,244,0.2) 100%);' //背景色
				},
				broadcastStyle2: {
					//滚动文字样式设置
					speed: 1, //每秒30px
					font_size: '24', //字体大小(rpx)
					text_color: '#fff', //字体颜色
					back_color: '#3A3A3A' //背景色
				},
				telImg: getApp().globalData.imgUrl + 'index/mobile.png',
				sort_nav_list: [], //顶部分类列表
				sort_obj: {}, //二级分类列表+分类商品列表
				tab_index: 0, //分类切换下标
				is_show_index: true,
				adArr: [], //公告数组
				icon2: getApp().globalData.imgUrl + 'index/add2.png',
				icon3: getApp().globalData.imgUrl + 'index/add.png',
				icon4: getApp().globalData.imgUrl + 'index/add3.png',
				icon5: getApp().globalData.imgUrl + 'index/stop.png',
				client: '', //客户端类型
				isCookie: false,
				storeIsCookie: false,
				openscnImg: getApp().globalData.imgUrl + 'index/close_screen.png',
				isShowTab: true, //是否显示顶部分类
				sortLen: '', //二级分类长度
				isShow: true,
				is_show_empty: false, //是否展示分类空页面
				pure_bg_color: '', //顶部栏有弧度纯色
				border_radius1: '', //推荐商品二角度设置
				border_radius2: '', //推荐商品三角度设置
				border_radius3: '',
				top_bg: 'var(--color_integral_main)', //顶部状态栏颜色
				noData: false, //暂无数据
				is_show_empty_goods: false, //是否展示分类空商品页面
				//app-2-start
				// #ifdef APP-PLUS
				isIos: uni.getDeviceInfo().platform == 'ios', //是否ios手机
				// #endif
				//app-2-end
				// #ifndef APP-PLUS
				isIos: false,
				// #endif
				goods_info: {},
				laber_banner_list: [],
				pointIsCookie: false,
				swiper_bg_style: 2,
				filters
			};
		},
		components: {
			tabMenu,
			sortList,
			uniSwiperDot,
			goodsItemV,
			recommendItemV,
			recommendItemList,
			notOpen,
			jyfParser
		},
		props: [
			'is_show_top',
			'deco_info',
			'topic_name',
			'is_from_found',
			'home_is_show_top_cat',
			'home_page_img',
			'width',
			'height',
			'store_width',
			'store_height',
			'store_page_img',
			'activity_open'
		],
		computed: {
			...mapState(['hasLogin', 'userInfo', 'userCenterData'])
		},
		async mounted() {
			if (this.deco_info.length) {
				this.deco_info.map((item) => {
					if (item.type == 'top_cat_nav') {
						this.swiper_bg_style = item.swiper_bg_style;
					}
				});
			}
			this.getSortList();
			//首页装修开平图缓存
			let cookievalue = uni.getStorageSync('pointIsCookie');
			if (!cookievalue) {
				this.pointIsCookie = true;
				uni.setStorage({
					key: 'pointIsCookie',
					data: new Date().getTime()
				});
			} else {
				if (new Date().getTime() * 1 - cookievalue * 1 > 24 * 60 * 60 * 1000) {
					this.pointIsCookie = true;
					uni.setStorage({
						key: 'pointIsCookie',
						data: new Date().getTime()
					});
				} else {
					this.pointIsCookie = false;
				}
			}
		},
		watch: {
			deco_info(val) {
				console.log('watch', val);
				this.noData = val && val.length == 0 ? true : false;
				val &&
					val.map((item) => {
						if (item.type == 'tuijianshangpin' && item.show_style == 'big') {
							this.border_radius1 = item.border_radius + 'px' + ' ' + item.border_radius + 'px' + ' 0 0';
							this.border_radius3 = '0 0 ' + item.border_radius + 'px' + ' ' + item.border_radius + 'px';
						} else if (item.type == 'tuijianshangpin' && item.show_style == 'list') {
							this.border_radius2 = item.border_radius + 'px' + ' 0 0 ' + item.border_radius + 'px';
						}
					});
			}
		},
		methods: {
			getMoreData() {
				if (this.tab_index > 0) {
					this.$refs.recommendList.getMoreData();
				}
			},
			back() {
				this.$back();
			},
			goSearch() {
				this.$Router.push('/standard/point/search/search');
			},
			filterFun: function(value) {
				if (value && value.length > 4) {
					value = value.substring(0, 4);
				}
				return value;
			},
			//跳转商品列表
			goGoodsList(type, labelId) {
				this.$Router.push({
					path: '/standard/point/search/good_list',
					query: {
						labelId,
						type
					}
				});
			},
			//轮播图切换修改背景色
			swiperChange(e) {
				const index = e.detail.current;
				this.swiperCurrent = index;
				if (this.deco_info.length > 0) {
					this.deco_info.map((item) => {
						if (item.type == 'top_cat_nav') {
							this.pure_bg_color = item.data[index].bg_color;
							if (item.swiper_bg_style == 1) {
								this.titleNViewBackground = item.data[index].bg_color;
							} else {
								this.titleNViewBackground = 'linear-gradient(' + item.data[index].bg_color +
									' 0%,' + item.data[index].bg_color + ' 42%,#ffffff 100%) ';
							}
						}
					});
				}
			},
			labelSwiperChange(e) {
				const index = e.detail.current;
				this.swiperCurrent = index;
				this.pure_bg_color = this.laber_banner_list[index].bg_color;
				if (this.swiper_bg_style == 1) {
					this.titleNViewBackground = this.laber_banner_list[index].bg_color;
				} else {
					this.titleNViewBackground = 'linear-gradient(' + this.laber_banner_list[index].bg_color + ' 0%,' + this
						.laber_banner_list[index].bg_color + ' 42%,#ffffff 100%)';
				}
			},
			// 获取分类列表
			getSortList() {
				let param = {};
				param.url = 'v3/integral/front/integral/mall/list';
				param.method = 'GET';
				this.$request(param).then((res) => {
					if (res.state == 200) {
						this.sort_nav_list = res.data.list;
						// if (this.deco_info.length > 0) {
						this.sort_nav_list.unshift({
							labelName: this.$L('首页')
						});
						// }
					}
				});
			},
			// 获取二级分类及分类商品列表
			getChildList(list, index) {
				if (this.tab_index != index) {
					// 切换tab回到顶部
					uni.pageScrollTo({
						scrollTop: 0,
						duration: 0
					});
					this.sort_list = [];
				}
				this.tab_index = index;
				if (index > 0) {
					this.sort_obj = list;
					if (this.sort_obj.children.length) {
						this.sortLen = this.sort_obj.children.length;
						this.is_show_empty = false;
					} else {
						this.is_show_empty = true;
					}

					if (this.sort_obj.data) {
						this.laber_banner_list = JSON.parse(this.sort_obj.data).filter((i) => i.imgUrl);
					} else {
						this.laber_banner_list = [];
					}
					if (this.sortLen > 9) {
						this.sort_obj.children = this.sort_obj.children;
					}
					this.is_show_index = false;
				} else {
					this.is_show_index = true;
					if (this.deco_info[0].type == 'top_cat_nav' && this.deco_info[0].data.length) {
						this.swiperChange({
							detail: {
								current: 0
							}
						});
					}
				}
				console.log('sort_obj', this.sort_obj);
				this.isShow = true;
			},
			// 拨打客服电话
			callUp(tel) {
				uni.makePhoneCall({
					phoneNumber: tel.toString()
				});
			},
			toSearchPage() {
				this.$Router.push('/pages/search/search');
			},
			// 相关跳转
			skipTo(type, url, productId, pid, labelName, item, from) {
				if (type == 'url') {
					//跳转链接地址
					if (!url) {
						return;
					}
					if (!url) {
						return;
					}
					// #ifdef H5
					window.open(url);
					// #endif

					//app-3-start
					// #ifdef APP-PLUS
					plus.runtime.openURL(url); //这里默认使用外部浏览器打开而不是内部web-view组件打开
					// #endif
					//app-3-end

					// #ifdef MP
					this.$Router.push({
						path: '/pages/index/skip_to',
						query: {
							url
						}
					});
					// #endif
				} else if (type == 'goods') {
					//跳转商品详情页
					this.$Router.push({
						path: '/standard/point/product/detail',
						query: {
							productId,
							goodsId: url
						}
					});
				} else if (type == 'keyword') {
					// 关键词
					this.$Router.push({
						path: '/standard/point/search/search',
						query: {
							keyword: url
						}
					});
				} else if (type == 'category') {
					//跳转分类

					if (from == 'swiper') {
						this.$Router.push({
							path: '/standard/point/search/good_list',
							query: {
								labelId: item.info.labelId,
								type: labelName
							}
						});
					} else {
						this.$Router.push({
							path: '/standard/point/search/good_list',
							query: {
								labelId: url,
								type: labelName
							}
						});
					}
				}
			},
			// 跳转商品详情页
			toGoodsDetail(productId, goodsId) {
				let query = {
					productId
				};
				goodsId && (query.goodsId = goodsId);
				this.$Router.push({
					path: '/standard/point/product/detail',
					query
				});
			},
			// 加入购物车
			addCart(productId, goodsId, item) {
				const that = this;
				this.goods_info = item;
				if (this.userInfo.access_token) {
					let param = {};
					param.url = 'v3/business/front/cart/add';
					param.method = 'POST';
					param.data = {
						productId: productId,
						number: 1
					};
					this.$request(param).then((res) => {
						if (res.state == 200) {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							});
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none',
								duration: 700
							});
						}
					});
				} else {
					let cart_list = {
						storeCartGroupList: [{
							promotionCartGroupList: [{
								cartList: [{
									buyNum: 1,
									goodsId: this.goods_info.goodsId,
									productId: this.goods_info.productId || this.goods_info
										.defaultProductId,
									productImage: this.goods_info.goodsPic ? this.goods_info
										.goodsPic : this.goods_info.goodsImage,
									goodsName: this.goods_info.goodsName,
									isChecked: 1,
									productPrice: this.goods_info.goodsPrice
									// productStock: this.goods_info.productStock
								}]
							}],
							storeId: this.goods_info.storeId,
							storeName: this.goods_info.storeName,
							checkedAll: true
						}],
						checkedAll: true,
						invalidList: []
					};

					let local_cart_list = uni.getStorageSync('cart_list'); //购物车列表本地缓存
					if (local_cart_list) {
						let tmp_list1 = [];
						let tmp_list2 = [];
						cart_list.storeCartGroupList.forEach((item) => {
							item.promotionCartGroupList.forEach((item1) => {
								item1.cartList.forEach((item2) => {
									local_cart_list.storeCartGroupList.forEach((v) => {
										v.promotionCartGroupList.forEach((v1) => {
											v1.cartList.forEach((v2) => {
												if (v2.productId == item2
													.productId && v
													.storeId == item
													.storeId) {
													tmp_list1.push(v);
												}
											});
											tmp_list2 = local_cart_list
												.storeCartGroupList.filter((v) => {
													return v.storeId == item
														.storeId;
												});
										});
									});
								});
							});
						});
						if (tmp_list1.length > 0 && tmp_list2.length > 0) {
							//同一店铺同一商品
							local_cart_list.storeCartGroupList.map((item) => {
								item.promotionCartGroupList.map((item1) => {
									item1.cartList.map((item2) => {
										if (item2.productId == this.goods_info.productId && item
											.storeId == this.goods_info.storeId) {
											item2.buyNum += 1;
										}
									});
								});
							});
						} else if (tmp_list1.length == 0 && tmp_list2.length > 0) {
							//同一店铺不同商品
							local_cart_list.storeCartGroupList.map((item) => {
								if (item.storeId == this.goods_info.storeId) {
									item.promotionCartGroupList.map((item2) => {
										item2.cartList.push(cart_list.storeCartGroupList[0]
											.promotionCartGroupList[0].cartList[0]);
									});
								}
							});
						} else {
							//不同店铺不同商品
							local_cart_list.storeCartGroupList.push(cart_list.storeCartGroupList[0]);
						}

						// 未登录购物车展示数据
						uni.setStorage({
							key: 'cart_list',
							data: local_cart_list,
							success: function() {
								//更新购物车数量和购物车数据
							}
						});
					} else {
						uni.setStorage({
							key: 'cart_list',
							data: cart_list,
							success: function() {
								//更新购物车数量和购物车数据
							}
						});
					}
					uni.showToast({
						title: this.$L('加入购物车成功！'),
						icon: 'none'
					});
				}
			},
			//关闭首页广告屏
			close_openScreen() {
				this.pointIsCookie = false;
			},
			//点击广告屏跳转到详情页面
			gotoGoods_detail() {
				this.pointIsCookie = false;
				let osValue = this.home_page_img[0];
				this.diyNavToOfIntegral(osValue);
			},
			//积分商城模块跳转通用函数
			diyNavToOfIntegral(val) {
				if (val.link_type == 'url') {
					//链接地址,只有h5可以跳转外部链接，其他端都不可以
					// #ifdef H5
					window.location.href = val.link_value;
					// #endif

					//app-4-start
					// #ifdef APP-PLUS
					plus.runtime.openURL(val.link_value); //这里默认使用外部浏览器打开而不是内部web-view组件打开
					// #endif
					//app-4-end

					// #ifdef MP
					this.$Router.push({
						path: '/pages/index/skip_to',
						query: {
							url: val.link_value
						}
					});
					// #endif
				} else if (val.link_type == 'keyword') {
					//关键词
					this.$Router.push({
						path: '/standard/point/search/search',
						query: {
							keyword: encodeURIComponent(val.link_value),
							source: 'search'
						}
					});
				} else if (val.link_type == 'goods') {
					//商品
					this.$Router.push({
						path: '/standard/point/product/detail',
						query: {
							productId: val.info.productId
						}
					});
				} else if (val.link_type == 'category') {
					//商品分类
					this.$Router.push({
						path: '/standard/point/search/good_list',
						query: {
							labelId: val.info.labelId,
							type: val.info.labelName
						}
					});
				}
			}
		},

		// #ifndef MP
		// 标题栏input搜索框点击
		onNavigationBarSearchInputClicked: async function(e) {
			this.$Router.push(`/pages/search/search`);
		},
		// #endif
		//点击导航栏 buttons 时触发
		onNavigationBarButtonTap(e) {
			const index = e.index;
			if (index === 0) {
				this.$api.msg($L('点击了扫描'));
			} else if (index === 1) {
				//app-5-start
				// #ifdef APP-PLUS
				const pages = getCurrentPages();
				const page = pages[pages.length - 1];
				const currentWebview = page.$getAppWebview();
				currentWebview.hideTitleNViewButtonRedDot({
					index
				});
				// #endif
				//app-5-end
				this.$Router.push(`/pages/search/search`);
			}
		}
	};
</script>

<style lang="scss">
	::v-deep.uni-swiper-wrapper {
		border-radius: 5px !important;
	}

	.goods_price {
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 500;
		text-decoration: line-through;
		color: #999999;
		margin-bottom: 20rpx;
	}

	.goods_price_point {
		height: 35rpx;
		background: var(--color_integral_main);
		border-radius: 18px;
		padding: 0 13rpx;
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #ffffff;
		line-height: 35rpx;
		white-space: nowrap;
		display: inline-block;
	}

	.goods_des {
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #888888;
		line-height: 36rpx;
		box-sizing: border-box;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	// 开屏 -- start
	.container_int {
		width: 750rpx;
		margin: 0 auto;
		// position: relative;
		// background-color: #fff;
		/* #ifdef H5 */
		margin-top: -24rpx;
		/* #endif */

		/* #ifdef MP */
		// margin-top: 10rpx;
		/* #endif */
	}

	.mp-search-header {
		width: 100%;
		position: fixed;
		top: 0;
		background: var(--color_integral_main);
		left: 0;
	}

	.prevent {
		// width: 100vw;
		// // height: calc(100vh + 98rpx);
		// position: fixed;
		// top: 0;
		// left: 0;
		// overflow: hidden;
	}

	.show-dialog {
		animation: 100ms showDialog linear forwards;
	}

	.hide-dialog {
		animation: 100ms hideDialog linear forwards;
	}

	@keyframes hideDialog {
		0% {
			opacity: 1;
		}

		25% {
			opacity: 0.75;
		}

		50% {
			opacity: 0.5;
		}

		75% {
			opacity: 0.25;
		}

		100% {
			opacity: 0;
		}
	}

	@keyframes showDialog {
		0% {
			opacity: 0;
		}

		25% {
			opacity: 0.25;
		}

		50% {
			opacity: 0.5;
		}

		75% {
			opacity: 0.75;
		}

		100% {
			opacity: 1;
		}
	}

	.container_int .open_screen {
		width: 750rpx;
		height: 100vh;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		margin: 0 auto;
		background-color: rgba(0, 0, 0, 0.3);
		z-index: 99999;
	}

	.container_int .open_screen .open_screen_con {
		maring: 0 auto;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		border-radius: 15rpx;
		max-width: 600rpx !important;
	}

	.container_int .open_screen .open_screen_con .open_screen_con_img {
		width: 100%;
		height: 100%;
		background-size: contain;
		border-radius: 15rpx;
	}

	.container_int .open_screen .open_screen_con .con_img {
		width: 58rpx;
		height: 58rpx;
		position: absolute;
		top: -58rpx;
		right: -58rpx;
	}

	.open_screen_con .con_img image {
		width: 100%;
		height: 100%;
	}

	// 开屏 -- end

	/* #ifdef H5 */
	.deco_wrap {
		margin-top: calc(var(--status-bar-height) + 96rpx) !important;
	}

	/* #endif */
	/* #ifdef MP */
	.deco_wrap {
		// margin-top: calc(var(--status-bar-height) + 94rpx) !important;
		padding-top: 7rpx;
	}

	.deco_wrap2 {
		margin-top: 108rpx;
	}

	/* #endif */

	//app-6-start
	/* #ifdef APP-PLUS */
	.deco_wrap {
		margin-top: calc(var(--status-bar-height) + 155rpx) !important;
	}

	/* #endif */
	//app-6-end

	.container {
		// padding-top: 180rpx;
		padding-bottom: 20rpx;
		overflow-x: hidden;
		background-color: #f5f5f5;
	}

	//app-7-start
	/* #ifdef APP-PLUS */
	.container1 {
		width: 750rpx;
		padding-top: 0rpx !important;
		padding-bottom: 0;
	}

	/* #endif */
	//app-7-end

	.mp-search-box {
		position: fixed;
		top: 0;
		z-index: 9999;
		/* #ifdef H5 */
		margin-top: 10rpx !important;
		/* #endif */
		width: 750rpx;
		height: 44px;
		padding: 0 20rpx 0 20rpx;
		display: flex;
		box-sizing: border-box;
		align-items: center;
		background: var(--color_integral_main);

		.return_con {
			width: 59rpx;
			/* #ifdef MP */
			display: flex;
			align-items: center;

			/* #endif */
			.return {
				width: 17rpx;
				height: 29rpx;
			}

			padding-left: 20rpx;
		}

		.title {
			width: 663rpx;
			text-align: center;
			font-size: 36rpx;
			color: white;
			position: relative;

			.searchs {
				position: absolute;
				left: 25rpx;
				top: 10rpx;
			}
		}

		.search {
			width: 30rpx;
			height: 30rpx;
		}
	}

	page {
		.topic_top {
			padding-top: 0 !important;
		}

		.cate-section {
			position: relative;
			z-index: 5;
			//border-radius: 16upx 16upx 0 0;
			overflow-x: hidden;
		}

		.carousel-section {
			padding: 0 20rpx;
			box-sizing: border-box;
			background-color: #ffffff;

			.titleNview-placing {
				padding-top: 0;
				height: 0;
			}

			.carousel {
				.carousel-item {
					padding: 0;
				}
			}

			.swiper-dots {
				left: 45upx;
				bottom: 40upx;
			}
		}
	}

	.search_img {
		position: absolute;
		width: 30rpx;
		height: 36rpx;
		left: 36rpx;
		top: 50%;
		transform: translateY(-50%);
		z-index: 99;
	}

	page {
		background: #f5f5f5;
	}

	.m-t {
		margin-top: 16upx;
	}

	/* 头部 轮播图 */
	.carousel-section {
		position: relative;
		padding-top: 10px;

		.titleNview-placing {
			height: var(--status-bar-height);
			padding-top: 44px;
			box-sizing: content-box;
		}

		.titleNview-background {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100px;
			transition: 0.4s;
			border-radius: 0 0 30rpx 30rpx;
		}
	}

	.carousel {
		width: 100%;
		height: 280upx;

		.carousel-item {
			width: 100%;
			height: 100%;
			// padding: 0 28upx !important;
			overflow: hidden;
		}

		image {
			width: 100%;
			height: 100%;
			border-radius: 10upx;
			overflow: hidden;
		}
	}

	.swiper-dots {
		display: flex;
		position: absolute;
		left: 60upx;
		bottom: 15upx;
		width: 72upx;
		height: 36upx;
		background-image: url(data:image/png;base64,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);
		background-size: 100% 100%;

		.num {
			width: 36upx;
			height: 36upx;
			border-radius: 50px;
			font-size: 24upx;
			color: #fff;
			text-align: center;
			line-height: 36upx;
		}

		.sign {
			position: absolute;
			top: 0;
			left: 50%;
			line-height: 36upx;
			font-size: 12upx;
			color: #fff;
			transform: translateX(-50%);
		}
	}

	/* 分类 */
	.cate-section {
		display: flex;
		justify-content: space-around;
		align-items: center;
		// flex-wrap: wrap;
		padding: 20rpx 0;
		background: #fff;

		.cate-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			font-size: 26upx;
			color: #303133;
			flex: 1;

			image {
				overflow: visible;
			}
		}

		.cate-item2 {
			display: flex;
			align-items: center;
			font-size: 26upx;
			color: #303133;
		}

		.cate_name {
			// width: 78rpx;
		}

		/* 原图标颜色太深,不想改图了,所以加了透明度 */
		image {
			width: 88upx;
			height: 88upx;
			margin-bottom: 14upx;
			border-radius: 50%;
		}
	}

	.ad-1 {
		width: 100%;
		height: 210upx;
		padding: 10upx 0;
		background: #fff;

		image {
			width: 100%;
			height: 100%;
		}
	}

	.notice_box {
		margin: 20rpx 20rpx 0 20rpx;
		background: #ffffff;
		border-radius: 14rpx;

		// margin-bottom:20rpx;
		.notice_wrap1 {
			width: 100%;
			height: 80rpx;
			margin-top: 20rpx;
			background-color: #fff;
			display: flex;
			align-items: center;
			border-radius: 14rpx;

			// border-radius: 6px;
			.notice_img1 {
				width: 127rpx;
				height: 80rpx;
				border-radius: 6px 0 0 6px;
			}

			.notice_content_wrap {
				font-size: 28rpx;
				font-weight: 600;
				width: 530rpx;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
				margin-left: -16rpx;

				.notice_content_title {
					color: #e1261c;
				}

				.notice_content {
					color: #666666;
				}
			}

			.notice_wrap1_line {
				width: 1rpx;
				height: 34rpx;
				background-color: rgba(0, 0, 0, 0.1);
				margin-left: 6rpx;
			}

			.notice_more {
				width: 80rpx;
				text-align: center;
				font-size: 29rpx;
				color: #2e2e2e;
				font-weight: 600;
			}
		}

		.notice_wrap2 {
			width: 100%;
			height: 80rpx;
			// margin-bottom: 20rpx;
			background-color: #3a3a3a;
			display: flex;
			align-items: center;
			box-shadow: 1px 6px 19px 1px rgba(86, 86, 86, 0.1);

			// border-radius: 6px;
			.notice_img2 {
				width: 138rpx;
				height: 80rpx;
				border-radius: 6px 0 0 6px;
			}

			.notice_content_wrap2 {
				font-size: 26rpx;
				font-weight: 600;
				width: 510rpx;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
				color: #fff;
				margin-left: 5rpx;
			}

			.notice_wrap2_line {
				width: 1rpx;
				height: 38rpx;
				background-color: #fff;
				margin-left: 2rpx;
			}

			.notice_more {
				width: 80rpx;
				text-align: center;
				font-size: 26rpx;
				color: #fff;
				font-weight: 600;
			}
		}
	}

	.service_wrap {
		// width:100%;
		padding: 20rpx;
		font-size: 26rpx;
		color: #333;
		background-color: #fff;
		display: flex;
		align-items: center;
		margin: 20rpx 20rpx 0 20rpx;
		border-radius: 14rpx;

		image {
			width: 30rpx;
			height: 32rpx;
			margin-right: 10rpx;
		}
	}

	.rich_text_wrap {
		color: #333;
		font-size: 28rpx;
		background: #fff;
		box-sizing: border-box;
		margin-top: 20rpx;
		/* #ifdef H5 */
		padding: 0 8rpx;
		/* #endif */
		/* #ifndef H5 */
		padding: 30rpx;
		/* #endif */
	}

	.match_wrap {
		padding: 20rpx;
		box-sizing: border-box;
		margin-top: 20rpx;
		flex-direction: column;
		background-color: #fff;

		.match_top {
			display: flex;
			flex-direction: column;
			background-color: #fff;

			image {
				width: 100%;
			}

			.match_image_wrap {
				width: 100%;
				display: flex;
				justify-content: center;

				.match_image {
					margin: 0 auto;
				}
			}

			.match_top_title {
				text-align: center;
				padding-bottom: 20rpx;
				font-size: 32rpx;
				color: #333;
			}

			.match_top_text {
				padding: 20rpx 20rpx 0;
				font-size: 28rpx;
				color: #333;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
			}
		}

		.match_main_wrap {
			background-color: #fff;
			margin-top: 20rpx;
		}

		.match_main {
			display: flex;
			justify-content: space-between;
			overflow-x: scroll;
			box-sizing: border-box;

			.match_item {
				width: 222rpx;
				height: 370rpx;
				margin-right: 20rpx;
				background-color: #fff;
				border-radius: 15rpx;
				position: relative;

				.match_goods_img {
					width: 222rpx;
					height: 222rpx;
					background-color: #ccc;
					border-radius: 15rpx 15rpx 0 0;

					image {
						width: 222rpx;
						height: 222rpx;
						border-radius: 10rpx 10rpx 0 0;
					}
				}

				.match_goods_name {
					font-size: 28rpx;
					color: #333;
					text-overflow: -o-ellipsis-lastline;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					line-clamp: 2;
					-webkit-box-orient: vertical;
					box-sizing: border-box;
					padding: 10rpx 0rpx 0 0rpx;
				}

				.match_goods_price {
					font-size: 28rpx;
					color: rgb(255, 43, 32);
					font-weight: 600;
					position: absolute;
					bottom: 12rpx;
					// left: 20rpx;
				}
			}
		}
	}

	.subline_wrap {
		padding: 30rpx 0;
		background-color: #fff;

		// margin: 20rpx 20rpx 0 20rpx;
		.subline {
			width: 100%;
			border-bottom: 1px dotted #fff;
		}
	}

	.carousel_bottom_wrap {
		padding-bottom: 20rpx;
		background-color: #fff;
		display: flex;
		justify-content: center;
		margin-top: 20rpx;

		.carousel_bottom {
			width: 100%;
			height: 340rpx;
			margin-bottom: 20rpx;
			padding-top: 20rpx;
		}
	}

	.recommend_goods_wrap {
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;
		padding: 0 20rpx;
		margin-top: 20rpx;
	}

	.carousel_img {
		// height:340rpx !important;
	}

	.blank_wrap {
		margin: 0 20rpx;
		margin-top: 20rpx;
	}

	.nav_wrap {
		width: 100%;
		// padding: 0 20rpx;
		box-sizing: border-box;
		background-color: #fff;
	}

	.nav_wrap::-webkit-scrollbar {
		display: none;
	}

	.laber_banner_list_class {
		margin-top: 98rpx;
		/* #ifdef MP */
		margin-top: calc(var(--status-bar-height) + 102rpx);
		/* #endif */
		/* #ifdef MP-BAIDU */
		margin-top: 160rpx;
		/* #endif */
		//app-8-start
		/* #ifdef APP-PLUS */
		margin-top: 190rpx;
		/* #endif */
		//app-8-end
	}

	.sort_sub_wrap {
		width: 100%;
		background: #f5f5f5;

		.sort_sub_top1 {
			// width: 89.5%;
			display: flex;
			// border-radius: 10rpx;
			background-color: #fff;
			padding: 0rpx 20rpx 20rpx 20rpx;
			flex-wrap: wrap;
			box-sizing: border-box;

			.sort_sub_item {
				display: flex;
				flex-direction: column;
				margin-right: 40rpx;
				justify-content: center;
				align-items: center;
				margin-top: 20rpx;

				.sort_sub_img {
					width: 106rpx;
					height: 106rpx;
					border-radius: 50%;
					margin-bottom: 20rpx;

					image {
						width: 106rpx;
						height: 106rpx;
						border-radius: 50%;
					}
				}

				.sort_sub_name {
					font-size: 24rpx;
					color: #333;
					font-weight: 600;
					width: 96rpx;
					white-space: nowrap;
					text-overflow: ellipsis;
					overflow: hidden;
					word-break: break-all;
					text-align: center;
				}
			}
		}

		.sort_sub_top1>view:nth-child(5n) {
			margin-right: 0 !important;
		}

		//app-9-start
		/* #ifdef APP-PLUS*/
		&,
		.laber_banner_list {
			.sort_sub_top1 {
				padding-top: 20rpx;
			}
		}

		/* #endif*/
		//app-9-end

		.sort_sub_goods {
			width: 100%;
			padding: 0 20rpx;
			box-sizing: border-box;
			display: flex;
			justify-content: space-between;
			flex-wrap: wrap;
			margin-top: 20rpx;
		}

		.sort_sub_goods>view:nth-child(2n) {
			margin-right: 0 !important;
		}
	}

	// 图片组合样式
	.goods {
		border: none;
		background: #fff;
		margin-top: 0;
	}

	.index_block {
		width: 750rpx;
		clear: both;
		overflow: hidden;
		background: #fff;
		display: block;
	}

	.goods .content {
		background: #f0f2f5;
		clear: both;
		overflow: hidden;
		display: block;
	}

	.goods .goods-small.goods-item:nth-child(2n + 1) {
		padding-right: 8rpx;
	}

	.goods-small.goods-item {
		overflow: hidden;
		float: left;
		width: 50%;
		box-sizing: border-box;
		padding-bottom: 8rpx;
		position: relative;
		-moz-box-sizing: border-box;
		-webkit-box-sizing: border-box;
	}

	.goods-item navigator {
		display: block;
		background: #fff;
	}

	.goods-item-pic {
		vertical-align: middle;
		line-height: 0;
		display: table-cell;
		text-align: center;
		width: calc(50vw - 30rpx);
		height: calc(50vw - 30rpx);
	}

	.goods-item-pic image {
		width: calc(50vw - 30rpx);
		height: calc(50vw - 30rpx);
	}

	.goods-small .goods-item-name {
		height: 66rpx;
		font-size: 26rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		word-break: break-word;
		color: #232326;
		margin-top: 10rpx;
		line-height: 33rpx;
		margin-bottom: 6rpx;
		padding: 0 8rpx;
	}

	.goods-item-price {
		color: #f23030;
		display: inline-block;
		padding: 0 10rpx 0 8rpx;
		position: relative;
		top: 2rpx;
		height: 50rpx;
		line-height: 50rpx;
	}

	.goods-item-price .yens {
		font-size: 26rpx;
	}

	.goods-item-price .bigprice {
		font-size: 32rpx;
		font-weight: bold;
		display: inline-block;
	}

	.goods-big.goods-item {
		overflow: hidden;
		float: left;
		width: 100%;
		box-sizing: border-box;
		padding-bottom: 8rpx;
		position: relative;
		-moz-box-sizing: border-box;
		-webkit-box-sizing: border-box;
		padding: 16rpx 16rpx 0;
	}

	.goods-item-name {
		height: 66rpx;
		font-size: 26rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		word-break: break-word;
		color: #232326;
		margin-top: 10rpx;
		line-height: 34rpx;
		margin-bottom: 6rpx;
		padding: 0 8rpx;
	}

	.goods-big .goods-item-price {
		color: #f23030;
		display: inline-block;
		padding: 0 10rpx 0 8rpx;
		position: relative;
		top: 2rpx;
		height: 50rpx;
		line-height: 50rpx;
	}

	.goods-big .goods-item-pic image {
		width: 734rpx;
		height: 734rpx;
		padding: 0 8rpx;
	}

	.hide_title .goods-item .goods-item-name {
		display: none !important;
	}

	.hide_price .goods-item .goods-item-price {
		display: none !important;
	}

	.goods-list.goods-item .goods-item-pic {
		float: left;
		width: 214rpx !important;
		height: 214rpx !important;
	}

	.goods-list.goods-item {
		overflow: hidden;
		float: left;
		width: 100%;
		box-sizing: border-box;
		padding-bottom: 8rpx;
		position: relative;
		-moz-box-sizing: border-box;
		-webkit-box-sizing: border-box;
		background: #fff;
		margin-bottom: 16rpx;
	}

	.goods-list.goods-item .goods-item-pic image {
		width: 214rpx !important;
		height: 214rpx !important;
	}

	.goods-list .goods-item-name {
		padding-top: 40rpx;
	}

	.goods .new-content .goods-item.goods-list .goods-item-name {
		padding-top: 10rpx;
	}

	/*图片组合样式  */

	.modules-slide {
		display: block;
	}

	.modules-slide .image-list.style0,
	.modules-slide .image-list.style0 ul {
		padding: 0;
		margin: 0;
		box-sizing: border-box;
	}

	.modules-slide .image-list.style0,
	.modules-slide .image-list.style0 ul {
		padding: 0;
		margin: 0;
		box-sizing: border-box;
		display: block;
	}

	.modules-slide .image-list ul {
		overflow: hidden;
		box-sizing: border-box;
		display: block;
	}

	.modules-slide .image-list.style0 ul li {
		display: block;
		box-sizing: border-box;
	}

	.modules-slide .image-list ul li navigator {
		display: block;
	}

	.modules-slide .image-list ul li navigator image {
		width: 100%;
		background: #f1f1f1;
		display: block !important;
	}

	.modules-slide image {
		max-width: 100%;
		height: auto;
		vertical-align: middle;
		display: inline-block !important;
	}

	.modules-slide .image-list {
		overflow: hidden;

		.tupianzuhe2 {
			margin-bottom: 20rpx;
			flex-wrap: wrap;
		}
	}

	.modules-slide .image-list.style1 ul li {
		display: block;
		margin: 0 16rpx 16rpx;
	}

	.modules-slide .image-list.style1 ul li image {
		height: 100%;
	}

	.modules-slide .image-list ul li navigator image {
		width: 100%;
		background: #f1f1f1;
		display: block !important;
	}

	.modules-slide .image-list.style2 ul,
	.modules-slide .image-list.style3 ul {
		padding-right: 16rpx;
	}

	.modules-slide .image-list.style2 ul li {
		box-sizing: border-box;
		padding: 0 0 16rpx 16rpx;
		width: 50%;
		float: left;
	}

	.modules-slide .image-list.style3 ul li {
		float: left;
		width: 33.33333%;
		box-sizing: border-box;
		padding: 0 0 16rpx 16rpx;
	}

	.modules-slide .image-ad {
		padding: 20rpx 0;
		box-sizing: border-box;
		display: block;
		clear: both;
		overflow: hidden;

		.tupianzuhe04_left {
			width: calc((750rpx - 60rpx) / 2);
			height: calc((750rpx - 60rpx) / 2 + 20rpx);
			margin-right: 20rpx;
			margin-left: 20rpx;

			image {
				width: 100%;
				height: 100%;
			}
		}

		.tupianzuhe04_right_item {
			width: calc((750rpx - 60rpx) / 2);
			height: calc((750rpx - 60rpx) / 4);

			image {
				width: 100%;
				height: 100%;
			}
		}
	}

	.modules-slide .image-ad>div {
		float: left;
		width: 50%;
		box-sizing: border-box;
	}

	.modules-slide .image-ad div navigator {
		display: block;
		margin: 0 16rpx 16rpx 0;
		box-sizing: border-box;
	}

	.modules-slide .images-tpl image {
		width: 374rpx;
		vertical-align: middle;
		box-sizing: border-box;
	}

	.modules-slide .image-ad2 {
		margin: 0 16rpx 0 0;
		box-sizing: border-box;
		display: block;
		clear: both;
		overflow: hidden;
	}

	.modules-slide .image-ad2 .clearfix {
		display: block;
		clear: both;
		overflow: hidden;
	}

	.modules-slide .image-ad2 div:first-child navigator:nth-child(1),
	.modules-slide .image-ad2 div:first-child navigator:nth-child(1) image {
		width: 228rpx;
	}

	.modules-slide .image-ad2 div:first-child .big:nth-child(2),
	.modules-slide .image-ad2 div:first-child .big:nth-child(2) image {
		width: 473rpx;
	}

	.modules-slide .image-ad2 div navigator {
		display: block;
		float: left;
		margin: 0 0 16rpx 16rpx;
		box-sizing: border-box;
	}

	.modules-slide .images-tpl img {
		width: 100%;
		vertical-align: middle;
		box-sizing: border-box;
	}

	.modules-slide .image-ad2 div:last-child navigator:nth-child(1),
	.modules-slide .image-ad2 div:last-child navigator:nth-child(1) image {
		width: 473rpx;
	}

	.modules-slide .image-ad2 div:last-child navigator:nth-child(2),
	.modules-slide .image-ad2 div:last-child navigator:nth-child(2) image {
		width: 228rpx;
	}

	.modules-slide .image-ad3 {
		padding: 0 0 0 16rpx;
		box-sizing: border-box;
		display: block;
		clear: both;
		overflow: hidden;
	}

	.modules-slide .image-ad3 div {
		width: 367rpx;
		float: left;
		box-sizing: border-box;
	}

	.modules-slide .image-ad3 div image {
		width: 351rpx;
	}

	.modules-slide .image-ad3 div navigator {
		padding: 0 16rpx 16rpx 0;
		display: inline-block;
		box-sizing: border-box;
	}

	.modules-slide .image-ad4 {
		padding: 0 16rpx 16rpx 0;
		box-sizing: border-box;
		display: block;
	}

	.modules-slide .image-ad4 div {
		float: left;
		width: 33.33333%;
		box-sizing: border-box;
	}

	.modules-slide .image-ad4 div navigator {
		display: block;
		margin: 0 0 16rpx 16rpx;
	}

	.countdown {
		width: 100%;
		height: 49.2rpx;
		line-height: 49.2rpx;
		font-size: 39.4rpx;
	}

	.countdown .countdown-name {
		float: left;
		display: block;
		-webkit-transform: scale(0.8);
	}

	.countdown .countdown-main {
		display: block;
		-webkit-transform: scale(0.8);
	}

	.countdown .countdown-num {
		background-color: #000000;
		display: inline-block;
		padding: 0 0rpx;
		width: 25px;
		height: 32.2rpx;
		line-height: 32.2rpx;
	}

	.combine1 {
		display: flex !important;
		flex-direction: column !important;
		text-align: center;
	}

	.combine2 {
		display: flex !important;
	}

	// 推荐商品
	.recommend_goods1 {
		width: 100%;
		height: 350rpx;
		display: flex;
		background-color: #fff;
		border-radius: 20rpx;
		margin-bottom: 20rpx;
		border-radius: 20rpx;

		.recommend_goods_img1 {
			width: 350rpx;
			height: 350rpx;
			margin-right: 20rpx;

			image {
				width: 350rpx;
				height: 350rpx;
				background-color: #ccc;
			}
		}

		.recommend_goods_right {
			height: 350rpx;
			position: relative;
			flex: 1;

			.goods_des {
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #888888;
				line-height: 36rpx;
				box-sizing: border-box;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}

			.recommend_goods_name {
				font-size: 30rpx;
				margin-top: 20rpx;
				text-overflow: -o-ellipsis-lastline;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				line-clamp: 2;
				-webkit-box-orient: vertical;
			}

			.recommend_goods_price {
				position: absolute;
				bottom: 80rpx;
				left: 0;
				color: #fc1c1c;
			}

			.recommend_goods_bottom {
				width: 100%;
				position: absolute;
				bottom: 20rpx;
				left: 0;
				display: flex;
				justify-content: space-between;

				image {
					width: 42rpx;
					height: 42rpx;
					margin-right: 20rpx;
				}
			}
		}
	}

	.recommend_goods2 {
		display: flex;
		flex-direction: column;
		width: 100%;

		.recommend_goods_img2 {
			width: 100%;
			height: 702rpx;

			image {
				width: 100%;
				height: 702rpx;
				background-color: #ccc;
			}
		}

		.recommend_goods_bottom2 {
			width: 100%;
			height: 204rpx;
			padding: 20rpx;
			box-sizing: border-box;
			background-color: #fff;
			position: relative;

			.goods_des {
				margin: 14rpx 0rpx;
			}

			.recommend_goods_name2 {
				font-size: 30rpx;
				text-overflow: -o-ellipsis-lastline;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				line-clamp: 2;
				-webkit-box-orient: vertical;
			}

			.goods_bottom {
				width: 100%;
				// display: flex;
				flex-direction: column;

				image {
					width: 42rpx;
					height: 42rpx;
				}
			}
		}
	}

	.big_price {
		font-size: 34rpx;
	}

	.small_price {
		font-size: 24rpx;
	}

	.combination_style {
		display: flex;
	}

	.combination_wrap {
		width: 100%;
		background-color: #fff;
		box-sizing: border-box;
		margin-top: 20rpx;
	}

	.combine6 {
		display: flex;
		flex-direction: column;
		margin-right: 20rpx;
		margin-top: 20rpx;
	}

	.scan_img {
		width: 44rpx;
		height: 44rpx;
		position: absolute;
		left: 15rpx;
	}

	.msg_img {
		width: 50rpx;
		height: 50rpx;
		position: absolute;
		right: 20rpx;
		top: 50%;
		transform: translateY(-50%);

		image {
			width: 50rpx;
			height: 50rpx;
		}
	}

	.combine3 {
		width: calc((750rpx - 60rpx) / 2);
		margin-top: 20rpx;
	}

	.combine4 {
		width: calc((750rpx - 80rpx) / 3);
		margin-top: 20rpx;
		margin-left: 20rpx;

		image {
			width: 100%;
			height: 100%;
		}
	}

	.paddingTB20 {
		padding: 0 20rpx;
	}

	.no_margin_right {
		padding: 0px;
	}

	.no_margin_right>view:nth-child(2n) {
		margin-right: 0 !important;
	}

	.no_margin_right2>view:nth-child(3n) {
		margin-right: 0 !important;
	}

	.see_more_wrap {
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		margin-top: 14rpx;

		.more_icon_circle {
			width: 106rpx;
			height: 106rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			border-radius: 50%;
			background-color: #f8f8f8;
			margin-bottom: 20rpx;

			.more_icon {
				width: 52rpx;
				height: 14rpx;
			}
		}

		.see_more_text {
			font-size: 24rpx;
			color: #333;
			font-weight: 600;
		}
	}

	.carousel-section ::v-deep .uni-swiper__warp {
		// margin-top: calc(44px + 70rpx) !important;
	}

	//app-10-start
	/* #ifdef APP-PLUS */
	.laber_banner_list ::v-deep .uni-swiper__warp {
		padding-top: 14rpx;
	}

	/* #endif */
	//app-10-end

	/* #ifdef H5 */
	.carousel-section ::v-deep .uni-swiper__warp {
		margin-top: -2rpx !important;
		padding-top: 8rpx !important;
	}

	/* #endif */
	.combine5_wrap>view:nth-child(2n) {
		margin-right: 0 !important;
	}

	.mp-search-box ::v-deep .ser-input ::v-deep .uni-input-wrapper ::v-deep .uni-input-input {
		background-color: #fff;
	}

	.search_input {
		text-align: left;
	}

	.rec_goods_wrap {
		width: 750rpx;
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;
	}

	.city_wrap {
		font-size: 30rpx;
		display: flex;
		align-items: center;
		color: #fff;
		flex-shrink: 0;
		max-width: 58px;
		white-space: nowrap;
		text-overflow: ellipsis;
		overflow: hidden;
		display: inline-block;
	}

	.top_icon {
		width: 24rpx;
		height: 24rpx;
		margin-left: 11rpx;
		margin-right: 17rpx;
		margin-top: 2rpx;
		flex-shrink: 0;
	}

	.deco_wrap {
		width: 750rpx;
	}

	/* ----TAB切换：---- */
	.tab_nav {
		margin-top: 100rpx;
	}

	.tab_nav_scroll {
		white-space: nowrap;
	}

	.tab_nav_block {
		display: inline-block;
		width: 25%;
		text-align: center;
	}

	.tab_nav_block_t {
		color: #2d2d2d;
		font-size: 28rpx;
		padding-bottom: 14rpx;
		margin: 0 50rpx 4rpx 50rpx;
	}

	.tab_nav_block_on {
		color: #333333;
		font-weight: 700;
		font-size: 32rpx;
		margin-bottom: 8rpx;
	}

	.tab_nav_block_i {
		color: #999999;
		font-size: 22rpx;

		padding: 4rpx 0;
		margin: 5rpx 26rpx;
	}

	.tab_nav_block_on2 {
		background-color: #fa1c1c;
		color: #ffffff;
		border-radius: 16rpx;
	}

	// 短视频3d轮播
	.swiper-block {
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		height: 346rpx;
		margin: 0;
	}

	.slide-image {
		height: 320rpx;
		width: 520rpx;
		border-radius: 9rpx;
		box-shadow: 0px 0px 30rpx rgba(0, 0, 0, 0.2);
		margin: 0rpx 30rpx;
		z-index: 1;
	}

	.active1 {
		/* transform: scale(1.44); */
		transition: all 0.2s ease-in 0s;
		height: 100%;
		width: 100%;
		border-radius: 16rpx;
	}

	.active2 {
		width: 100%;
		height: 100%;
		border-radius: 16rpx;
	}

	// 空页面
	.empty_sort_page {
		width: 100%;
		// height: 100vh;
		background: #f5f5f5;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-top: 340rpx;

		.empty_img {
			width: 380rpx;
			height: 280rpx;
			margin-bottom: 32rpx;
		}

		.empty_text {
			font-size: 26rpx;
			color: #999;
		}
	}

	.scan,
	.message {
		margin-left: 18rpx;
		display: flex;
		align-items: center;
		flex-direction: column;
		flex-shrink: 0;

		image {
			width: 34rpx;
			height: 34rpx;
		}

		text {
			color: white;
			font-size: 20rpx;
			line-height: 22rpx;
			margin-top: 6rpx;
		}
	}

	.search_con {
		position: relative;
		flex: 1;
	}

	.svideo_person_num {
		width: 40rpx;
		height: 40rpx;
		border-radius: 0 0 50% 0;
	}

	.swiper-item {
		color: #fff;
		overflow: hidden;
		left: 40rpx !important;
		right: 20rpx;
		top: 20rpx;
		bottom: 20rpx;
		width: 80% !important;
		height: 280rpx !important;
		// transform: translate(80%, 0px) translateZ(0px);
	}

	.swiper-item1 {
		color: #fff;
		box-sizing: border-box;
		height: 346rpx;
		width: 346rpx !important;
		top: 0rpx;
		bottom: 0rpx;
		overflow: hidden;
		left: 80rpx;
	}

	// 专题页头部
	.topic_top_bar {
		width: 750rpx;
		height: 88rpx;
		background-color: #fff;
		display: flex;
		// justify-content: center;
		align-items: center;
		font-size: 30rpx;
		color: #2d2d2d;
		position: fixed;
		top: 0;
		/* #ifdef MP || APP-PLUS */
		padding-top: 110rpx;
		padding-bottom: 48rpx;
		/* #endif */
		left: 0;
		font-weight: 600;
		z-index: 99999;
		margin: 0 auto;
		right: 0;
	}

	.svideo2_wrap ::v-deep .uni-scroll-view ::v-deep .uni-scroll-view ::v-deep .uni-scroll-view-content>view:nth-last-child(1) {
		margin-right: 0 !important;
	}

	.top_swiper_style1 {
		background: var(--color_integral_main);
	}

	.top_swiper_style2 {
		background: linear-gradient(var(--color_integral_main) 0%, var(--color_integral_main) 42%, #fff 100%);
	}

	.svideo4_wrap ::v-deep .uni-scroll-view ::v-deep .uni-scroll-view ::v-deep .uni-scroll-view-content>view:nth-last-child(1) {
		margin-right: 0 !important;
	}

	.recommond_goods3_wrap {
		display: flex;
		justify-content: space-between;
	}

	.have_sold {
		font-size: 24rpx;
		color: #9a9a9a;
		margin-top: 10rpx;
	}

	.topic_back_icon {
		width: 60rpx;
		height: 38rpx;
		padding-left: 20rpx;
	}

	.topic_name {
		width: 100%;
		text-align: center;
		margin-left: -58rpx;
	}

	.hide_sold_wrap {
		width: 100%;
		position: absolute;
		bottom: 20rpx;
		left: 0;
		display: flex;
		justify-content: space-between;
	}

	// 导航分组
	.nav_group {
		padding: 0 86rpx;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;

		.nav_group_item {
			display: flex;
			align-items: center;
			justify-content: center;
			padding-top: 40rpx;
			margin-bottom: 40rpx;

			image {
				width: 90rpx;
				height: 90rpx;
				border-radius: 50%;
				margin-right: 20rpx;
			}

			.nav_group_name {
				font-size: 26rpx;
				color: #333;
			}
		}

		.nav_group_item:nth-last-child(1) {
			margin-right: 0;
		}
	}

	.nav_group>view:nth-child(2n) {
		margin-right: 0;
	}

	// 导航分组end
	.rec_goods_wrap>view:nth-child(2n) {
		margin-right: 0 !important;
	}

	.carousel-section ::v-deep .uni-swiper__warp ::v-deep .uni-swiper__dots-box ::v-deep .uni-swiper__dots-item {
		width: 16rpx !important;
	}

	.deco_wrap_no_top_cat {
		margin-top: 38rpx !important;
		/* #ifdef MP || APP-PLUS */
		margin-top: 94rpx !important;
		/* #endif */
	}

	.rich_text ::v-deep div .ql-align-center {
		text-align: center;
	}

	.rich_text ::v-deep div .ql-align-right {
		text-align: right;
	}

	.rich_text ::v-deep div .ql-align-left {
		text-align: left;
	}

	/* #ifdef MP*/
	.rich_text {
		text-align: center;
	}

	/* #endif */

	//app-11-start
	/* #ifdef APP-PLUS*/
	.deco_carousel-section {
		padding-top: 0 !important;
	}

	/* #endif */
	//app-11-end

	.left_h5 {
		width: 100%;
		height: 60rpx;
		border-radius: 30rpx;
		background: rgba(255, 255, 255, 0.2);
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;

		image {
			width: 50rpx;
			height: 50rpx;
			margin-top: 2rpx;
			margin-left: 10rpx;
		}

		text {
			font-size: 26rpx;
			color: #fff;
			margin-top: -2rpx;
		}
	}
</style>