<!-- 商品组件：竖直方向
	点击进入商品详情页
	加入购物车事件
-->
<template name="goodsItemV">
  <view
    class="goods_v_item flex_column_between_start"
    @click="goGoodsDetail(goods_info)"
    :style="{
      width:
        'calc((750rpx - ' +
        page_margin * 4 +
        'rpx - ' +
        goods_margin * 2 +
        'rpx)/2)',
      borderRadius: border_radius + 'px',
      border: border_style == 'border_eee' ? '1rpx solid #eee' : '',
      boxShadow:
        border_style == 'card-shadow'
          ? 'rgba(93, 113, 127, 0.08) 0px 2px 8px'
          : ''
    }"
  >
    <view class="goods_desc_top">
      <view
        class="goods-img"
        :style="{
          backgroundImage:
            'url(' + (goods_info.mainImage || goods_info.goodsImage) + ')',
          width:
            'calc((750rpx - ' +
            page_margin * 4 +
            'rpx - ' +
            goods_margin * 2 +
            'rpx)/2)',
          borderTopLeftRadius: border_radius + 'px',
          borderTopRightRadius: border_radius + 'px'
        }"
      ></view>
      <text class="goods-name">{{ goods_info.goodsName }}</text>
      <view class="goods_des">{{ goods_info.goodsBrief }}</view>
    </view>
    <view class="goods-price">
      <view class="left">
        <text class="unit">￥</text>
        <text class="price_int">{{
          $getPartNumber(goods_info.marketPrice, 'int')
        }}</text>
        <text class="price_decimal">{{
          $getPartNumber(goods_info.marketPrice, 'decimal')
        }}</text>
      </view>
      <view
        class="pre_bottom"
        :class="show_sale == true ? 'goods_item_bottom' : ''"
      >
        <text v-if="goods_info.integralPrice"
          >{{ goods_info.integralPrice }}{{ $L('积分') }}</text
        >
        <text v-if="goods_info.integralPrice && goods_info.cashPrice">+</text>
        <text
          v-if="
            goods_info.cashPrice &&
            goods_info.integralPrice.toString().length +
              goods_info.cashPrice.toString().length <=
              14
          "
          >{{ $L('￥') }}{{ filters.toFix(goods_info.cashPrice) }}</text
        >
        <text
          v-if="
            goods_info.integralPrice.toString().length +
              goods_info.cashPrice.toString().length >
            14
          "
          >{{ $L('￥') }}...</text
        >
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import filters from '../../../utils/filter.js'
export default {
  name: 'goodsItemV',
  data() {
    return {
      imgUrl: getApp().globalData.imgUrl,
      icon2: getApp().globalData.imgUrl + 'index/add2.png',
      icon3: getApp().globalData.imgUrl + 'index/add.png',
      icon4: getApp().globalData.imgUrl + 'index/add3.png',
      icon5: getApp().globalData.imgUrl + 'index/stop.png',
      icon_url: '', //加车图标
      goods_pic: '', //商品图片
      goods_sale: '', //销量
      filters
    }
  },
  computed: {
    ...mapState(['userInfo'])
  },
  props: {
    goods_info: {
      type: Object,
      value: {}
    },
    icon_type: {
      type: Number
    },
    show_sale: {
      type: Boolean
    },
    border_radius: {
      type: Number
    },
    border_style: {
      type: String
    },
    // 商品边距
    goods_margin: {
      type: Number,
      default: 10
    },
    // 页面边距
    page_margin: {
      type: Number,
      default: 10
    },
    height: {
      type: Number,
      default: 258
    }
  },
  onLoad() {},
  methods: {
    //进入商品详情页
    goGoodsDetail(goods_info) {
      this.$Router.push({
        path: '/standard/point/product/detail',
        query: {
		  productId:(goods_info.productId  || goods_info.integralProductId),
		  goodsId:goods_info.integralGoodsId
        }
      })
    },
    // 加入购物车
    addCart(productId) {
      if (this.userInfo.access_token) {
        //登录
        let param = {}
        param.url = 'v3/business/front/cart/add'
        param.method = 'POST'
        param.data = {
          productId: productId,
          number: 1
        }
        this.$request(param).then((res) => {
          if (res.state == 200) {
            uni.showToast({
              title: res.msg,
              icon: 'none'
            })
            this.$emit('reloadCartList', true)
          } else {
            uni.showToast({
              title: res.msg,
              icon: 'none',
              duration: 700
            })
          }
        })
      } else {
        //未登录
        let cart_list = {
          storeCartGroupList: [
            {
              promotionCartGroupList: [
                {
                  cartList: [
                    {
                      buyNum: 1,
                      goodsId: this.goods_info.goodsId,
                      productId:
                        this.goods_info.productId ||
                        this.goods_info.defaultProductId,
                      productImage: this.goods_info.goodsPic
                        ? this.goods_info.goodsPic
                        : this.goods_info.goodsImage,
                      goodsName: this.goods_info.goodsName,
                      isChecked: 1,
                      productPrice: this.goods_info.goodsPrice,
                      productStock: this.goods_info.productStock
                    }
                  ]
                }
              ],
              storeId: this.goods_info.storeId,
              storeName: this.goods_info.storeName,
              checkedAll: true
            }
          ],
          checkedAll: true,
          invalidList: []
        }

        let local_cart_list = uni.getStorageSync('cart_list') //购物车列表本地缓存
        if (local_cart_list) {
          let tmp_list1 = []
          let tmp_list2 = []
          cart_list.storeCartGroupList.forEach((item) => {
            item.promotionCartGroupList.forEach((item1) => {
              item1.cartList.forEach((item2) => {
                local_cart_list.storeCartGroupList.forEach((v) => {
                  v.promotionCartGroupList.forEach((v1) => {
                    v1.cartList.forEach((v2) => {
                      if (
                        v2.productId == item2.productId &&
                        v.storeId == item.storeId
                      ) {
                        tmp_list1.push(v)
                      }
                    })
                    tmp_list2 = local_cart_list.storeCartGroupList.filter(
                      (v) => {
                        return v.storeId == item.storeId
                      }
                    )
                  })
                })
              })
            })
          })
          if (tmp_list1.length > 0 && tmp_list2.length > 0) {
            //同一店铺同一商品
            local_cart_list.storeCartGroupList.map((item) => {
              item.promotionCartGroupList.map((item1) => {
                item1.cartList.map((item2) => {
                  if (
                    item2.productId == this.goods_info.productId &&
                    item.storeId == this.goods_info.storeId
                  ) {
                    item2.buyNum += 1
                  }
                })
              })
            })
          } else if (tmp_list1.length == 0 && tmp_list2.length > 0) {
            //同一店铺不同商品
            local_cart_list.storeCartGroupList.map((item) => {
              if (item.storeId == this.goods_info.storeId) {
                item.promotionCartGroupList.map((item2) => {
                  item2.cartList.push(
                    cart_list.storeCartGroupList[0].promotionCartGroupList[0]
                      .cartList[0]
                  )
                })
              }
            })
          } else {
            //不同店铺不同商品
            local_cart_list.storeCartGroupList.push(
              cart_list.storeCartGroupList[0]
            )
          }

          // 未登录购物车展示数据
          uni.setStorage({
            key: 'cart_list',
            data: local_cart_list,
            success: function () {
              //更新购物车数量和购物车数据
            }
          })
        } else {
          uni.setStorage({
            key: 'cart_list',
            data: cart_list,
            success: function () {
              //更新购物车数量和购物车数据
            }
          })
        }
        uni.showToast({
          title: this.$L('加入购物车成功！'),
          icon: 'none'
        })
        this.$emit('addCart', local_cart_list)
      }
    },
    changeImg(val) {
      return val.mainImage ? val.mainImage : val.goodsImage
    },
    saleNum(val) {
      if (val.actualSales) {
        return val.actualSales
      } else {
        return val.saleNum
      }
      // return val.actualSales?val.actualSales:val.saleNum
    }
  }
}
</script>

<style lang="scss">
.goods_v_item {
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  margin-bottom: 22rpx;
  display: flex;
  flex-direction: column;
  & {
    margin-right: 0 !important;
  }
  .goods_desc_top {
    .goods-img {
      background-size: cover;
      background-position: center center;
      background-repeat: no-repeat;
      height: calc((750rpx - 60rpx) / 2);
      overflow: hidden;
      background-color: #fff;
    }

    .goods-name {
      height: 173rpx;
      margin-top: 20rpx;
      font-size: 28rpx;
      color: $com-main-font-color;
      line-height: 40rpx;
      height: 80rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      word-break: break-word;
      padding: 0 20rpx;
    }
    .goods_des {
      font-size: 24rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #888888;
      line-height: 36rpx;
      padding: 0 20rpx;
      box-sizing: border-box;
      width: 355rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .goods-price {
    padding: 20rpx;
    width: 100%;
    /* margin-top: 15rpx; */
    display: flex;
    flex-direction: column;
    .left {
      width: 100%;
      color: $main-color;
      margin-bottom: 20rpx;
      .unit,
      .price_decimal,
      .price_int {
        font-size: 24rpx;
        font-family: PingFang SC;
        font-weight: 500;
        text-decoration: line-through;
        color: #999999;
      }
    }

    image {
      width: 42rpx;
      height: 42rpx;
    }
  }
}
.goods_item_bottom {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
  padding-bottom: 20rpx;
}
.have_sold_text {
  font-size: 24rpx;
  color: #9a9a9a;
}
.pre_bottom {
  height: 35rpx;
  background: var(--color_integral_main);
  border-radius: 18px;
  padding: 0 13rpx;
  font-size: 24rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #ffffff;
  line-height: 35rpx;
  white-space: nowrap;
}
</style>
