<template>
  <view class="content_sort">
    <view class="sort_wrap">
      <view class="sort_title_wrap">
        <view
          class="sort_title_item"
          v-for="(item, index) in nav_list"
          :key="index"
          @click="changeStyle(index)"
        >
          <view
            :class="
              currIndex == index
                ? item.sub_title != ''
                  ? 'active_item_another sort_title'
                  : 'active_item sort_title'
                : 'sort_title'
            "
            >{{ item.title }}</view
          >
          <view
            :class="currIndex == index ? 'active_sub_title' : 'sort_sub_title'"
            v-if="item.sub_title != ''"
            >{{ item.sub_title }}</view
          >
        </view>
      </view>

      <view v-if="recommendGoods.length" class="sort_goods_wrap">
        <view class="recommend-goods flex_row_start_start">
          <view
            v-for="(item, index1) in nav_list"
            :key="index1"
            class="goods_item_wrap"
          >
            <goodsItemV
              v-for="(goodsItem, index2) in item.info"
              :key="index2"
              transType="point"
              :goods_info="goodsItem"
              :icon_type="item.cart_icon_type"
              :show_sale="isShowSale"
              :border_radius="borderRadius"
              :height="card_height"
              v-if="currIndex == index1"
            />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import goodsItemV from './goods_item_v.vue'
import loadingState from '@/components/loading-state.vue'
export default {
  name: 'index_sort_list',
  data() {
    return {
      icon: getApp().globalData.imgUrl + 'index/icon.png',
      sortImg: getApp().globalData.imgUrl + 'index/sort.png',
      currIndex: 0,
      recommendGoods: [],
      loadingState: 'first_loading',
      pageSize: 10,
      current: 1,
      loading: false, //是否加载数据
      hasMore: true, //是否还有数据
      goods_list: []
    }
  },
  props: {
    arriveBotFlag: {
      type: Boolean,
      default: false
    },
    nav_list: {
      type: Array,
      default: []
    },
    isShowSale: {
      type: Boolean
    },
    borderRadius: {
      type: Number
    },
    card_height: {
      type: Number
    }
  },
  components: {
    goodsItemV,
    loadingState
  },
  mounted() {
    this.getData() //获取推荐商品数据
    if (this.nav_list.length > 0) {
      this.goods_list = this.nav_list[0].info
    }
  },
  methods: {
    changeStyle(index) {
      this.currIndex = index
      // this.goods_list = this.nav_list[index].info
    },
    getData() {
      this.loading = true
      let param = {}
      param.url = 'v3/goods/front/goods/recommendList'
      param.method = 'GET'
      param.data = {}
      param.data.queryType = 'cart'
      param.data.pageSize = this.pageSize
      param.data.current = this.current
      this.loadingState =
        this.loadingState == 'first_loading' ? this.loadingState : 'loading'
      this.$request(param).then((res) => {
        if (res.state == 200) {
          if (this.current == 1) {
            this.recommendGoods = res.data.list
          } else {
            this.recommendGoods = this.recommendGoods.concat(res.data.list)
          }
          this.hasMore = this.$checkPaginationHasMore(res.data.pagination) //是否还有数据
          if (this.hasMore) {
            this.current++
            this.loadingState = 'allow_loading_more'
          } else {
            this.loadingState = 'no_more_data'
          }
        } else {
          //错误提示
        }
        this.loading = false
      })
    },
    //页面到底部加载更多数据
    getMoreData() {
      if (this.hasMore) {
        this.getData()
      }
    }
  }
}
</script>

<style lang="scss">
.content_sort {
  display: flex;
  flex-direction: column;
  margin-top: 20rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  .sort_wrap {
    .sort_title_wrap {
      height: 85rpx;
      display: flex;
      padding-right: 40rpx;
      overflow-x: scroll;
      align-items: flex-end;
      margin-bottom: 25rpx;

      .sort_title_item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin: 0 32rpx;
        white-space: nowrap;
        box-sizing: border-box;
        .sort_title {
          font-size: 30rpx;
          color: #333;
          font-weight: bold;
        }

        .sort_sub_title {
          height: 32rpx;
          font-size: 24rpx;
          background: transparent;
          border-radius: 16rpx;
          color: #666;
          text-align: center;
          line-height: 32rpx;
          padding: 0 10rpx;
          margin-top: 6rpx;
        }
      }
    }
    .sort_title_wrap::-webkit-scrollbar {
      display: none;
    }
    .sort_title_wrap > view:nth-child(1) {
      margin-left: 0;
    }
  }
}
.active_item {
  font-size: 32rpx !important;
  font-weight: bold !important;
  padding-bottom: 6rpx;
  border-bottom: 6rpx solid #fc1c1c;
}
.active_item_another {
  font-size: 34rpx !important;
  font-weight: bold !important;
}

.active_sub_title {
  background: var(--color_integral_main);
  color: #fff;
  height: 32rpx;
  font-size: 24rpx;
  border-radius: 16rpx;
  text-align: center;
  line-height: 32rpx;
  padding: 0 10rpx;
  margin-top: 6rpx;
}
.list-scroll-content {
  height: 100vh;
}
.recommend-title {
  display: flex;
  justify-content: center;

  image {
    width: 387rpx;
    height: 34rpx;
    margin: 30rpx 0;
  }
}
.recommend-goods {
  flex-wrap: wrap;
  justify-content: space-between;
}
.sort_goods_wrap {
  width: 100%;
  box-sizing: border-box;
}
.goods_item_wrap {
  width: 100%;
  display: flex;
  justify-content: inherit;
  flex-wrap: wrap;
}
.goods_item_wrap > view:nth-child(2n) {
  margin-right: 0 !important;
}
/* #ifdef MP */
.goods_item_wrap {
  justify-content: space-between !important;
}
/* #endif */
</style>
