<!-- 空页面
	空图标可以自定义
	内容提示可以自定义
	空页面所占的高度也可以自自定义
-->
<template name="loadingState">
  <view
    class="allow_loading_more flex_row_center_center"
    v-if="state == 'allow_loading_more'"
  >
    <text class="allow_loading_icon iconfont iconshanghua"></text>
    <text class="tip">{{ $L('向上滑动浏览更多数据') }}</text>
  </view>
  <view
    class="common_loading flex_row_center_center"
    v-else-if="state == 'first_loading'"
    :style="{ marginTop: mTop }"
  >
    <image class="loading_icon" :src="imgUrl + 'loading_more.gif'" />
  </view>
  <view
    class="allow_loading_more flex_row_center_center"
    v-else-if="state == 'loading'"
  >
    <image class="loading_more_icon" :src="imgUrl + 'loading_more.gif'" />
    <text class="tip">{{ $L('数据加载中') }}...</text>
  </view>
  <!-- showEndFlag 默认为true，展示，设置为false，则不展示 -->
  <view class="no_more_data" v-else-if="state == 'no_more_data' && showEndFlag">
    {{ $L('数据加载完毕') }}~
  </view>
</template>

<script>
export default {
  name: 'loadingState',
  data() {
    return {
      imgUrl: getApp().globalData.imgUrl
    }
  },
  props: {
    state: {
      type: String,
      default: 'allow_loading_more'
    },
    mTop: {
      type: String,
      default: '200rpx'
    },
    showEndFlag: {
      type: Boolean,
      default: true
    }
  },
  methods: {}
}
</script>

<style lang="scss">
/* template:allow_loading_more start */
.allow_loading_more {
  height: 80rpx;

  .allow_loading_icon {
    width: 30rpx;
    height: 30rpx;
    margin-right: 12rpx;
    color: $main-color;
  }

  .tip {
    color: #bbb;
    font-size: 24rpx;
  }

  .loading_more_icon {
    width: 46rpx;
    height: 46rpx;
  }
}

/* template:allow_loading_more end */

/* template:loading start */
.common_loading {
  flex: 1;

  .loading_icon {
    width: 110rpx;
    height: 110rpx;
  }
}

/* template:loading end */

/* template:no_more_data start */
.no_more_data {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 80rpx;
  text-align: center;
  line-height: 80rpx;
  color: #bbb;
  font-size: 24rpx;
  width: 750rpx;
}

/* template:no_more_data end */
</style>
