<!-- 推荐商品列表组件 -->
<template name="recommendList">
  <view class="recommend">
    <view class="recommend_title flex_row_center_center">
      <image
        :src="imgUrl + 'point/goods/recommend_tips.png'"
        mode="aspectFit"
      ></image>
    </view>
    <view class="recommend_list">
      <view
        class="recommend_pre"
        v-for="(recommendItem, recommendIndex) in recommendedList"
        :key="recommendIndex"
      >
        <recommendItemV :goods_info="recommendItem"></recommendItemV>
      </view>
    </view>
    <loadingState :state="loadingState" />
  </view>
</template>

<script>
import recommendItemV from './recommend_item_v.vue'
import loadingState from './loading-state.vue'
export default {
  name: 'recommendList',
  components: {
    recommendItemV,
    loadingState
  },
  data() {
    return {
      imgUrl: getApp().globalData.imgUrl,
      recommendedList: [], //店铺推荐列表
      current: 1,
      pageSize: 10,
      loadingState: 'first_loading',
      hasMore: true
    }
  },
  mounted() {
    this.getRecommend()
  },
  methods: {
    //获取店铺推荐商品信息
    getRecommend() {
      let param = {}
      param.url = 'v3/integral/front/integral/mall/recommendList'
      param.method = 'GET'
      param.data = {}
      param.data.current = this.current
      param.data.pageSize = this.pageSize
      this.loadingState =
        this.loadingState == 'first_loading' ? this.loadingState : 'loading'
      this.$request(param)
        .then((res) => {
          if (res.state == 200) {
            let result = res.data
            if (this.current == 1) {
              this.recommendedList = result.list
            } else {
              this.recommendedList = this.recommendedList.concat(result.list)
            }
            this.hasMore = this.$checkPaginationHasMore(res.data.pagination) //是否还有数据
            if (this.hasMore) {
              this.current++
              this.loadingState = 'allow_loading_more'
            } else {
              this.loadingState = 'no_more_data'
            }
          } else {
            this.$api.msg(res.msg)
          }
        })
        .catch((e) => {
          //异常处理
        })
    },
    //加载更多
    getMoreData() {
      if (this.hasMore) {
        this.getRecommend()
      }
    }
  }
}
</script>

<style lang="scss">
/* 店铺推荐 start */
.recommend {
  .recommend_title {
    width: 750rpx;
    padding: 40rpx 0 26rpx;
    box-sizing: border-box;
    image {
      width: 193rpx;
      height: 30rpx;
    }
  }
  .recommend_list {
    display: flex;
    flex-wrap: wrap;
    padding: 0 20rpx;
    .recommend_pre {
      margin-right: 20rpx !important;
      /* #ifdef MP-ALIPAY */
      margin-right: 16rpx !important;
      /* #endif */
      &:nth-child(2n) {
        margin-right: 0 !important;
      }
    }
  }
}
/* 店铺推荐 end */
</style>
