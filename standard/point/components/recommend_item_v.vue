<!-- 推荐商品组件 -->
<template name="goodsItemV">
  <view
    class="goods_v_item flex_column_start_start"
    @click="goGoodsDetail(goods_info)"
    :style="{
      width:
        'calc((750rpx - ' +
        page_margin * 4 +
        'rpx - ' +
        goods_margin * 2 +
        'rpx)/2)',
      borderRadius: border_radius + 'px',
      border: border_style == 'border_eee' ? '1rpx solid #eee' : '',
      boxShadow:
        border_style == 'card-shadow'
          ? 'rgba(93, 113, 127, 0.08) 0px 2px 8px'
          : ''
    }"
  >
    <view class="goods_desc_top">
      <view
        class="goods-img"
        :style="{
          backgroundImage:
            'url(' + (goods_info.mainImage || goods_info.goodsImage) + ')',
          width:
            'calc((750rpx - ' +
            page_margin * 4 +
            'rpx - ' +
            goods_margin * 2 +
            'rpx)/2)',
          borderTopLeftRadius: border_radius + 'px',
          borderTopRightRadius: border_radius + 'px'
        }"
      ></view>
      <view class="goods-name" v-if="isSearchGoods">
        {{goods_info.goodsName}}
      </view>

      <view
        class="goods-name"
        v-else
        :style="{
          width:
            'calc((750rpx - ' +
            page_margin * 4 +
            'rpx - ' +
            goods_margin * 2 +
            'rpx)/2)',
          borderTopLeftRadius: border_radius + 'px',
          borderTopRightRadius: border_radius + 'px'
        }"
      >
        <text>{{ goods_info.goodsName }}</text>
      </view>

      <view
        class="goods_des"
        :style="{
          width:
            'calc((750rpx - ' +
            page_margin * 4 +
            'rpx - ' +
            goods_margin * 2 +
            'rpx)/2)',
          borderTopLeftRadius: border_radius + 'px',
          borderTopRightRadius: border_radius + 'px'
        }"
        >{{ goods_info.goodsBrief }}</view
      >
    </view>
    <view class="goods_desc_bottom">
      <view class="goods_price" v-if="goods_info.marketPrice">
        <text>{{ $L('￥') }}</text>
        <text>{{ this.$getPartNumber(goods_info.marketPrice, 'int') }}</text>
        <text>{{
          this.$getPartNumber(goods_info.marketPrice, 'decimal')
        }}</text>
      </view>
      <view
        class="goods_price_point"
        v-if="goods_info.integralPrice || goods_info.cashPrice"
      >
        <text v-if="goods_info.integralPrice"
          >{{ goods_info.integralPrice }}{{ $L('积分') }}</text
        >
        <text v-if="goods_info.integralPrice && goods_info.cashPrice">+</text>
        <text
          v-if="
            goods_info.cashPrice &&
            goods_info.integralPrice.toString().length +
              goods_info.cashPrice.toString().length <=
              14
          "
          >￥{{ filters.toFix(goods_info.cashPrice) }}</text
        >
        <text
          v-if="
            goods_info.integralPrice.toString().length +
              goods_info.cashPrice.toString().length >
            14
          "
          >￥...</text
        >
      </view>

      <view class="recommond_goods3_wrap">
        <view class="have_sold" v-if="show_sale"
          >{{ $L('已售') }}{{ goods_info.actualSales }}件</view
        >
      </view>
    </view>
  </view>
</template>
<script>
import { mapState } from 'vuex'
import filters from '../../../utils/filter.js'
export default {
  name: 'goodsItemV',
  data() {
    return {
      imgUrl: getApp().globalData.imgUrl,
      icon2: getApp().globalData.imgUrl + 'index/add2.png',
      icon3: getApp().globalData.imgUrl + 'index/add.png',
      icon4: getApp().globalData.imgUrl + 'index/add3.png',
      icon5: getApp().globalData.imgUrl + 'index/stop.png',
      icon_url: '', //加车图标
      goods_pic: '', //商品图片
      goods_sale: '', //销量
      filters
    }
  },
  computed: {
    ...mapState(['userInfo'])
  },
  props: {
    goods_info: {
      type: Object,
      value: {}
    },
    icon_type: {
      type: Number
    },
    border_radius: {
      type: Number
    },
    border_style: {
      type: String
    },
    // 商品边距
    goods_margin: {
      type: Number,
      default: 10
    },
    // 页面边距
    page_margin: {
      type: Number,
      default: 10
    },
    height: {
      type: Number,
      default: 258
    },
    isSearchGoods: {
      type: Boolean,
      default: false
    },
    show_sale: Boolean
  },
  components: {
  },
  methods: {
    //进入商品详情页
    goGoodsDetail(goods_info) {
      this.$Router.push({
        path: '/standard/point/product/detail',
        query: {
          productId: goods_info.defaultProductId
            ? goods_info.defaultProductId
            : goods_info.productId
        }
      })
    }
  }
}
</script>

<style lang="scss">
.goods_v_item {
  background: #fff;
  border-radius: 14rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
  width: 345rpx;
  height: 610rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 0 0 20rpx;
  box-sizing: border-box;

  .goods_desc_top {
    .goods-img {
      background-size: cover;
      background-position: center center;
      background-repeat: no-repeat;
      width: 345rpx;
      height: 345rpx;
      overflow: hidden;
      background-color: #fff;
    }

    .goods-name {
      height: 80rpx;
      margin-top: 20rpx;
      font-size: 28rpx;
      color: $com-main-font-color;
      line-height: 40rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      word-break: break-all;
      padding: 0 20rpx;
    }
    .goods_des {
      font-size: 24rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #888888;
      line-height: 36rpx;
      padding: 0 20rpx;
      box-sizing: border-box;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .goods_desc_bottom {
    padding-left: 20rpx;
    .goods_price {
      font-size: 24rpx;
      font-family: PingFang SC;
      font-weight: 500;
      text-decoration: line-through;
      color: #999999;
      margin-bottom: 20rpx;
    }
    .goods_price_point {
      height: 35rpx;
      background: var(--color_integral_main);
      border-radius: 18px;
      padding: 0 13rpx;
      font-size: 24rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 35rpx;
      white-space: nowrap;
    }
  }

  .have_sold {
    font-size: 24rpx;
    color: #9a9a9a;
    margin-top: 10rpx;
  }
}
</style>
