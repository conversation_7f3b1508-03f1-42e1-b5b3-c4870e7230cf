<template>
	<view :style="mix_diyStyle">
		<view class="sld_point_search" :style="mix_diyStyle">
			<!-- #ifndef APP-PLUS -->
			<view class="fixed_top_status_bar"></view>
			<!-- #endif -->
			<!-- 小程序头部兼容 -->
			<view :class="{ mp_search_box: true, fixedTop: isTop }">
				<view class="search_con">
					<!-- <image :src="imgUrl+'search.png'" mode="aspectFit" class="search_img"></image> -->
					<view class="search_img" :style="{ backgroundImage: 'url(' + imgUrl + 'search.png)' }"></view>
					<input confirm-type="search" @confirm="search" class="ser_input" type="text" v-model="keyword" :placeholder="$L('搜索')" placeholder-class="search_input" />
					<image class="clear_content" v-show="keyword" @click="clearInputVal" :src="imgUrl + 'input_clear.png'" />
				</view>
				<text class="search_btn" @click="backLastPage">{{ $L('取消') }}</text>
			</view>
			<view class="no_data_con" v-if="recommendedList.length == 0" :style="{ height: history_val.length == 0 ? '394rpx' : 'auto' }">
				<!-- 分类空页面 -->
				<block v-if="isSearch">
					<view class="empty_sort_page" v-if="is_show_empty">
						<image :src="imgUrl + 'empty_goods.png'" mode="aspectFit" class="empty_img"></image>
						<view class="empty_text">{{ $L('暂无数据') }}</view>
					</view>
				</block>

				<!-- 搜索历史 start -->
				<block v-else>
					<view class="no_history flex_row_center_center" v-if="history_val.length == 0">
						<view class="">
							{{ $L('暂无历史搜索记录~') }}
						</view>
					</view>
					<view class="search-item" v-if="history_val && history_val.length" style="padding-bottom: 0">
						<view class="search-title">
							<text>{{ $L('搜索历史') }}</text>
							<view class="del" @click="clearHistory">
								<image :src="imgUrl + 'del_search.png'" />
							</view>
						</view>
						<view class="search-con">
							<view class="item" v-for="(item, index) in history_val" :key="index" @click="btnSearch(item)">{{ item }}</view>
						</view>
					</view>
				</block>
				<!-- 搜索历史 end -->
			</view>
			<!-- 积分商城搜索列表 -->
			<view class="recommend_list" v-if="recommendedList.length">
				<view class="recommend_pre" v-for="(recommendItem, recommendIndex) in recommendedList" :key="recommendIndex">
					<recommendGoods :goods_info="recommendItem" :isSearchGoods="true"></recommendGoods>
				</view>
			</view>
			<!-- <recommendItemList v-if="!this.hasMore" ref="recommendList"></recommendItemList> -->
		</view>
	</view>
</template>

<script>
import { mapState } from 'vuex';
import recommendGoods from '../components/recommend_item_v.vue';
import loadingState from '../components/loading-state.vue';
import recommendItemList from '../components/recommend_list.vue';
export default {
	data() {
		return {
			imgUrl: getApp().globalData.imgUrl,
			keyword: '',
			recommendedList: [],
			loadingState: '',
			current: 1,
			pageSize: 10,
			hasMore: false,
			is_show_empty: false,
			labelId: '',
			history_val: [],
			isTop: false,
			isSearch: false
		};
	},
	components: {
		recommendGoods,
		loadingState,
		recommendItemList
	},
	computed: {
		...mapState(['hasLogin', 'userInfo', 'userCenterData'])
	},
	async mounted() {
		this.getHistoryList();
	},
	onReachBottom() {
		if (this.hasMore) {
			this.search();
		} else {
			this.$refs.recommendList.getMoreData();
		}
	},

	onPageScroll(e) {
		if (e.scrollTop > 100) {
			this.isTop = true;
		} else {
			this.isTop = false;
		}
	},
	onLoad(options) {
		if (this.$Route.query.labelId) {
			this.labelId = this.$Route.query.labelId;
		}
		if (this.$Route.query.keyword) {
			this.keyword = decodeURIComponent(decodeURIComponent(this.$Route.query.keyword));
			this.search();
		}
	},
	methods: {
		clearInputVal() {
			this.isSearch = false;
			this.recommendedList = [];
			this.keyword = '';
		},
		btnSearch(item) {
			this.keyword = item;
			this.search();
		},
		backLastPage() {
			this.$Router.back(1);
		},
		search() {
			uni.hideKeyboard();
			uni.showLoading();
			let param = {};
			param.data = {};
			param.data.keyword = this.keyword;
			if (this.labelId) {
				param.data.labelId = this.labelId;
			}

			param.url = 'v3/integral/front/integral/mall/goodsList';
			param.method = 'GET';
			this.$request(param).then((res) => {
				if (res.state == 200) {
					this.isSearch = true;
					this.setHistoryData();
					this.recommendedList = res.data.list;
					if (this.recommendedList.length == 0) {
						this.is_show_empty = true;
					} else {
						this.is_show_empty = false;
					}

					this.hasMore = this.$checkPaginationHasMore(res.data.pagination); //是否还有数据
					if (this.hasMore) {
						this.current++;
						this.loadingState = 'allow_loading_more';
					} else {
						this.loadingState = 'no_more_data';
					}
					uni.hideLoading();

					uni.pageScrollTo({
						duration: 100,
						scrollTop: 0
					});
				} else {
					uni.hideLoading();
					this.$api.msg(res.msg);
				}
			});
		},
		//设置缓存
		setHistoryData() {
			let { history_val, keyword } = this;
			let tmp_data = [...history_val];
			tmp_data.unshift(keyword);
			// 最多取12条，不重复且不为空的数据
			tmp_data = tmp_data.reduce((a, b) => {
				a.length < 12 && b && a.indexOf(b) == -1 ? a.push(b) : null;
				return a;
			}, []);
			let history_val_str = tmp_data.join('~');
			this.history_val = tmp_data;
			uni.setStorageSync('point_his_keyword', history_val_str);
		},
		//获取历史记录
		getHistoryList() {
			let history_data = uni.getStorageSync('point_his_keyword');
			if (history_data) {
				let his_array = history_data.split('~');
				let last_arr = [];
				for (var i = 0; i < his_array.length; i++) {
					!this.$checkSpace(his_array[i]) && last_arr.push(his_array[i]);
				}
				this.history_val = last_arr;
			}
		},
		//清除搜索历史
		clearHistory() {
			uni.removeStorageSync('point_his_keyword');
			this.history_val = [];
		}
	}
};
</script>

<style lang="scss">
.fixed_top_status_bar {
	position: fixed;
	//app-1-start
	/* #ifdef APP-PLUS */
	height: var(--status-bar-height);
	/* #endif */
	//app-1-end
	/* #ifndef APP-PLUS */
	height: 0;
	/* #endif */
	top: 0;
	left: 0;
	right: 0;
	z-index: 99;
	background: #fff;
}

page {
	background: #f5f5f5;
	width: 750rpx;
	margin: 0 auto;
}

.mp_search_box {
	display: flex;
	align-items: center;
	background-color: white;
	padding: 11rpx 0;
	transition: all 0.4s ease;

	.search_con {
		width: 631rpx;
		height: 65rpx;
		background: #f5f5f5;
		border-radius: 33rpx;
		padding: 0 22rpx;
		display: flex;
		align-items: center;
		margin-left: 20rpx;

		.ser_input {
			flex: 1;
			padding-left: 20rpx;
			/* #ifdef MP-TOUTIAO||MP-BAIDU */
			font-size: 26rpx;
			/* #endif */
			background: transparent;
		}

		.search_img {
			background-size: contain;
			background-position: center center;
			background-repeat: no-repeat;
			width: 30rpx;
			height: 30rpx;
		}

		.clear_content {
			width: 44rpx !important;
			height: 44rpx !important;
		}

		.search_input {
			font-size: 28rpx;
			color: #999999;
		}
	}

	.search_btn {
		margin-left: 20rpx;
		font-size: 30rpx;
		color: #333333;
	}
}

.fixedTop {
	position: fixed;
	top: 0;
	z-index: 1000;
	width: 100%;
}

.no_data_con {
	padding: 44rpx 30rpx;

	.no_history {
		color: #2d2d2d;
		font-size: 12px;
		width: 100%;
		height: 100%;
		text-align: center;
	}

	height: 554rpx;
	background-color: white;
}

.recommend_list {
	margin-top: 20rpx;
	display: flex;
	flex-wrap: wrap;
	padding: 0 20rpx;

	.recommend_pre {
		margin-right: 20rpx !important;
		/* #ifdef MP-ALIPAY */
		margin-right: 16rpx !important;

		/* #endif */
		&:nth-child(2n) {
			margin-right: 0 !important;
		}
	}
}

// 空页面
.empty_sort_page {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 100rpx;

	.empty_img {
		width: 380rpx;
		height: 280rpx;
		margin-bottom: 32rpx;
	}

	.empty_text {
		font-size: 26rpx;
		color: #999;
	}
}

.search-item {
	.search-title {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 48rpx;
		color: #2d2d2d;
		font-size: 28rpx;
		font-weight: bold;

		image {
			width: 48rpx;
			height: 48rpx;
		}
	}

	.search-con {
		display: flex;
		align-items: center;
		flex-wrap: wrap;

		.item {
			height: 50rpx;
			padding: 0 18rpx;
			color: #2d2d2d;
			line-height: 50rpx;
			font-size: 24rpx;
			background-color: #f5f5f5;
			border-radius: 25rpx;
			margin-right: 20rpx;
			margin-top: 20rpx;
			max-width: 274rpx;
			white-space: nowrap;
			text-overflow: ellipsis;
			overflow: hidden;
			word-break: break-all;
		}
	}
}
</style>
