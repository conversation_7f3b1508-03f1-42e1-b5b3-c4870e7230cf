<template>
	<view :style="mix_diyStyle">
  <view style="margintop: 20rpx" v-if="loadFlag" id="container_int">
    <!-- 地址 -->
    <navigator
      :url="`/newPages/address/list?source=1&sourceId=${
        orderAddress.addressId != undefined ? orderAddress.addressId : ''
      }`"
      class="address-section"
      v-if="isVG == 1"
    >
      <view class="order-content">
        <view class="cen" v-if="orderAddress.addressId != undefined">
          <view class="top flex_row_start_center">
            <view v-if="orderAddress.isDefault == 1" class="tag" >默认</view>
            <text>{{ orderAddress.addressAll }}</text>
          </view>
          <text class="address"
            >{{ orderAddress.addressAll }}{{ orderAddress.detailAddress }}</text
          >
          <view class="member_info flex_row_start_center">
            <text class="name">{{ orderAddress.memberName }}</text>
            <text class="mobile">{{ orderAddress.telMobile }}</text>
          </view>
        </view>
        <view
          class="empty_address flex_row_center_center"
          v-if="orderAddress.addressId == undefined"
        >
          <text class="add_icon">+</text>
          <text class="tit">{{ $L('新建收货地址') }}</text>
        </view>
        <text class="iconfont iconziyuan11"></text>
      </view>

      <image class="a-bg" :src="imgUrl + 'address_split_border.png'"></image>
    </navigator>

    <!-- 虚拟商品的预留信息 -->
    <view class="pre_message" v-else>
      <block v-if="virtualPre.length">
        <view
          class="pre_msg_item flex_row_start_center"
          v-for="(item, index) in virtualPre"
          :key="index"
        >
          <view class="msg_left flex_row_end_center"
            ><text style="color: red" v-if="item.isRequired == 1">*</text
            ><text>{{ item.reserveName }}</text
            >：</view
          >
          <view class="msg_right">
            <block v-if="item.reserveType == 1">
              <input
                type="number"
                :placeholder="`${$L('请输入')}${item.reserveName}`"
                v-model="item.reserveValue"
                maxlength="11"
                @blur="handleBlur"
              />
            </block>
            <block v-else-if="item.reserveType == 3">
              <input
                type="number"
                :placeholder="`${$L('请输入')}${item.reserveName}`"
                v-model="item.reserveValue"
                maxlength="50"
                @blur="handleBlur"
              />
            </block>

            <block v-else-if="item.reserveType == 5">
              <input
                type="text"
                :placeholder="`${$L('请输入')}${item.reserveName}`"
                v-model="item.reserveValue"
                maxlength="30"
                @blur="handleBlur"
              />
            </block>
            <block v-else>
              <input
                type="text"
                :placeholder="`${$L('请输入')}${item.reserveName}`"
                v-model="item.reserveValue"
                :maxlength="item.reserveType == 2 ? 18 : 50"
                @blur="handleBlur"
              />
            </block>
          </view>
        </view>
      </block>
    </view>
    <!-- 虚拟商品的预留信息 -->

    <view class="goods-section flex_column_start_start">
      <!-- 商品列表 -->
      <view class="store_list">
        <view class="product_con">
          <view class="g-item flex_row_start_start">
            <view
              class="image"
              :style="{
                backgroundImage: 'url(' + goodsData.product.goodsImage + ')'
              }"
            >
            </view>
            <view class="right flex_column_between_start">
              <view class="flex_column_start_start">
                <text class="title">{{ goodsData.product.goodsName }}</text>
              </view>
              <view class="goods_item_specs">
                <text
                  class="goods_item_spec"
                  v-if="goodsData.product.specValues"
                  >{{ goodsData.product.specValues }}</text
                >
                <text class="goods_item_buynum"
                  >*{{ goodsData.product.buyNum }}</text
                >
              </view>
              <view class="price-box">
                <text class="price_int"
                  >{{ goodsData.product.integralPrice }}{{ $L('积分') }}</text
                >
                <text class="unit"> + {{ $L('¥') }}</text>
                <text class="price_int">{{
                  $getPartNumber(goodsData.product.cashPrice, 'int')
                }}</text>
                <text class="price_decimal">{{
                  $getPartNumber(goodsData.product.cashPrice, 'decimal')
                }}</text>
              </view>
            </view>
          </view>
        </view>
        <view class="yt-list order_remark">
          <text class="title">{{ $L('订单备注') }}</text>
          <textarea
            :placeholder="$L('给商家留言,最多100字')"
            v-model="remark"
            :adjust-position="true"
            placeholder-class="placeholder"
            class="content message_textarea"
            maxlength="100"
            cursor-spacing="20"
            @focus="handleFocus"
            @blur="handleBlur"
          ></textarea>
        </view>
      </view>
    </view>

    <!-- 发票、平台优惠券 -->
    <view class="yt-list">
      <view class="yt-list-cell b-b">
        <text class="cell-tit clamp">{{ $L('商品金额') }}</text>
        <text class="cell-tip">
          <text class="big_price"
            >{{ goodsData.integralAmount }}{{ $L('积分') }}</text
          >
          <text class="unit"> +</text>
          <text class="big_price"
            >￥{{ (goodsData.cashAmount + '').split('.')[0] }}.</text
          >
          <text class="small_price">{{
            (goodsData.cashAmount + '').split('.')[1] != undefined
              ? (goodsData.cashAmount + '').split('.')[1]
              : '00'
          }}</text>
        </text>
      </view>
      <view class="yt-list-cell b-b">
        <text class="cell-tit clamp">{{ $L('积分抵扣') }}</text>
        <text
          class="cell-tip point"
          v-if="goodsData.integralAmount"
          @click="showSelectIntegral"
          >-{{ goodsData.integralAmount }}</text
        >
        <text class="cell-tip voice" v-else @click="showSelectIntegral">{{
          $L('不使用积分')
        }}</text>
        <text class="iconfont iconziyuan11" @click="showSelectIntegral"></text>
      </view>
      <view class="yt-list-cell b-b" @click="toInvoice">
        <text class="cell-tit clamp">{{ $L('发票') }}</text>
        <text
          class="cell-tip voice"
          v-if="invoice_info == '' || invoice_info == $L('不需要发票')"
          >{{ $L('不需要发票') }}</text
        >
        <view class="cell-tip voice" v-else
          ><text class="limVoice">{{ invoice_info }}</text
          ><text>{{
            invoice_content == 1 ? $L('商品明细') : $L('商品类别')
          }}</text>
        </view>
        <text class="iconfont iconziyuan11"></text>
      </view>
    </view>
    <!-- 金额明细 -->
    <view class="empty_h"></view>
    <!-- 底部 -->
    <view class="footer flex_row_end_center" v-if="isBottomShow">
      <view class="price-content flex_column_center_end">
        <view class="should_pay flex_row_end_end">
          <text class="tit">{{ $L('实付') }}：</text>
          <text class="big_price"
            >{{ goodsData.integralAmount }}{{ $L('积分') }}</text
          >
          <text class="big_price"> +￥</text>
          <text class="big_price"
            >{{ (goodsData.cashAmount + '').split('.')[0] }}.</text
          >
          <text class="small_price">{{
            (goodsData.cashAmount + '').split('.')[1] != undefined
              ? (goodsData.cashAmount + '').split('.')[1]
              : '00'
          }}</text>
        </view>
        <view class="promotion_total">
          {{ $L('当前积分') }}:
          <text class="current"> {{ goodsData.memberIntegral }}</text>
        </view>
      </view>
      <text
        v-if="
          goodsData.memberIntegral >= goodsData.integralAmount ||
          goodsData.integralAmount == 0
        "
        class="submit flex_row_center_center"
        @click="check()"
        >{{ $L('提交订单') }}</text
      >

      <text v-else class="submit flex_row_center_center not_enough">{{
        $L('积分不足')
      }}</text>
    </view>
    <!-- 优惠券弹框 start -->
    <uni-popup
      ref="integralModel"
      type="bottom"
      @touchmove.stop.prevent="moveHandle"
      class="popup_container"
    >
      <view class="integral_model">
        <view class="integral_model_title">
          <text>{{ $L('使用积分') }}</text>
          <image
            :src="imgUrl + 'goods_detail/close.png'"
            mode="aspectFit"
            @click="closeModel"
          ></image>
        </view>
        <view class="available_integral">
          <text>{{ $L('可用积分') }}：</text>
          <text>{{ goodsData.memberIntegral }}</text>
        </view>
        <scroll-view class="integral_model_list" scroll-y="true">
          <view
            class="integral_item"
            v-for="(item, index) in integral_list"
            :key="index"
            @click="selectIntegral(item, index)"
          >
            <view class="num">
              {{ $L('使用') }}<text>{{ item }}</text
              >{{ $L('积分') }}
            </view>

           <!-- <image
              v-if="integral_index === index"
              :src="imgUrl + 'point/selected.png'"
              mode="aspectFit"
            >
            </image>
            <image
              v-else
              :src="imgUrl + 'point/notselect.png'"
              mode="aspectFit"
            ></image> -->
            <text :class="{item_check:true, iconfont:true, iconziyuan33:integral_index===index}" v-if="integral_index===index"></text>
            <text :class="{iconfont:true, iconziyuan43:integral_index!==index}" v-else></text>
          </view>
        </scroll-view>
        <view class="btn_con">
          <view class="not_use_integral" @click="notUseintergral">
            {{ $L('不使用积分') }}
          </view>
          <view
            v-show="integral_index !== ''"
            class="use_integral"
            @click="useintergral"
          >
            {{ $L('立即使用') }}
          </view>
          <view
            v-show="integral_index === ''"
            style="background: var(--color_main);opacity: 0.6;"
            class="use_integral"
          >
            {{ $L('立即使用') }}
          </view>
        </view>
      </view>
    </uni-popup>
    <!-- 优惠券弹框 end -->

    <purchasePop
      ref="purchasePop"
      :exList="exceptionProList"
      :exState="exState"
      :exStateTxt="exStateTxt"
      @goNext="goNext"
      @delNext="delNext"
      type="point"
    ></purchasePop>
  </view>
	</view>
</template>

<script>
import { mapState } from 'vuex'
// #ifdef H5
const devicePlatform = uni.getDeviceInfo().platform
// #endif
import purchasePop from '@/components/purchasePop.vue'
export default {
  components: {
    purchasePop
  },
  data() {
    return {
      imgUrl: getApp().globalData.imgUrl,
      loadFlag: false,
      preParam: {}, //上一个页面的参数
      orderAddress: {}, //下单所用的地址信息
      goodsData: [], //商品数据
      allData: {}, //确认下单返回的所有数据信息
      maskState: 0, //优惠券面板显示状态
      desc: '', //备注
      payType: 1, //1微信 2支付宝
      isBottomShow: true, //底部是否显示
      windowHeight: '',
      remark: '',
      cartIds: '', //购物车id集合
      ifOnShow: false,
      invoice_info: '', //发票信息
      invoice_content: '',
      invoiceId: '', //发票id
      store_show_flag: false,
      currentStore: {},
      platformCouponList: [], //平台优惠券列表
      platformCouponCode: '',
      platformCouponCodeText: '',
      store_show_flag_platform: false,
      have_integral: '',
      store_show_no_good: false,
      no_good_info: {},
      timer: '',
      remark: '',
      integral_list: [],
      integral_index: 0,
      useIntegral: '',
      first_loading: true,
      isVatInvoice: true,
      //虚拟商品的相关字段
      isVG: null,
      virtualPre: [],
      reserveInfoList: [],

      exceptionProList: [],
      exState: 0,
      exStateTxt: ''
    }
  },
  async onLoad(option) {
    setTimeout(()=>{
      uni.setNavigationBarTitle({
        title: this.$L('确认订单')
      })    
    },0);
    
    //商品数据
    this.getAddressList()
    this.preParam = this.$Route.query
    uni.getSystemInfo({
      success: (res) => {
        this.windowHeight = res.windowHeight
      }
    })
    uni.onWindowResize((res) => {
      if (res.size.windowHeight < this.windowHeight) {
        this.isBottomShow = false
      } else {
        // #ifdef H5
        if (devicePlatform !== 'ios') {
          document.querySelector('.message_textarea textarea').blur()
          setTimeout(() => {
            this.isBottomShow = true
          }, 200)
        } else {
          this.isBottomShow = true
        }
        // #endif
        // #ifndef H5
        this.isBottomShow = true
        // #endif
      }
    })
  },
  computed: {
    ...mapState(['hasLogin', 'userInfo'])
  },
  onHide() {
    this.ifOnShow = true
  },
  onShow() {
    if (this.ifOnShow) {
      const is_need_invoice = uni.getStorageSync('is_need_invoice')
      const invoice_msg = uni.getStorageSync('invoice_info')
      if (is_need_invoice != '') {
        this.invoice_info = this.$L('不需要发票')
      }

      if (invoice_msg != '') {
        if (invoice_msg.invoice_title) {
          this.invoice_info = invoice_msg.invoice_title + ' '
          this.invoice_content = invoice_msg.invoice_content
        } else {
          this.invoice_info = invoice_msg.company_name + ' '
          this.invoice_content = invoice_msg.invoice_content
        }
        this.invoiceId = invoice_msg.invoiceId
      }
    }
    this.isBottomShow = true
  },
  methods: {
    notUseintergral() {
      this.useIntegral = 0
      this.integral_index = ''
      this.confirmOrder()
      this.closeModel()
    },
    useintergral() {
      this.confirmOrder()
      this.closeModel()
    },
    selectIntegral(integral, index) {
      this.integral_index = index
      this.useIntegral = integral
    },
    closeModel() {
      this.$refs.integralModel.close()
    },
    showSelectIntegral() {
      this.$refs.integralModel.open()
    },

    goNext() {
      this.submit()
    },

    delNext() {},

    moveHandle() {},
    //获取地址列表
    getAddressList() {
      this.$request({
        url: 'v3/member/front/memberAddress/list',
        method: 'GET'
      })
        .then((res) => {
          if (res.state == 200) {
            if (res.data.list.length > 0) {
              var default_address = res.data.list.filter(function (item) {
                return item.isDefault == 1
              })
              if (default_address.length > 0) {
                this.orderAddress = default_address[0]
              } else {
                this.orderAddress = res.data.list[0]
              }
            }

            this.confirmOrder()
          } else {
            this.confirmOrder()
            this.$api.msg(res.msg)
          }
        })
        .catch((e) => {
          //异常处理
        })
    },

    //用于切换地址，获取信息等
    confirmOrder() {
      const { preParam } = this
      let param = {}
      param.url = 'v3/integral/front/integral/orderOperate/confirm'
      param.method = 'POST'
      param.data = {}
      param.data.productId = Number(preParam.productId)
      param.data.number = Number(preParam.number)
      if (!this.first_loading) {
        param.data.useIntegral = this.useIntegral
      }
      this.$request(param).then((res) => {
        if (res.state == 200) {
          this.goodsData = res.data
          this.isVatInvoice = res.data.isVatInvoice
          //处理平台优惠券
          this.integral_list = res.data.integralList
          if (this.first_loading) {
            this.integral_index = res.data.integralList.length - 1
            this.useIntegral = res.data.integralList[
              res.data.integralList.length - 1
            ]
              ? res.data.integralList[res.data.integralList.length - 1]
              : res.data.integralAmount
            // 虚拟商品相关字段
            this.isVG = this.goodsData.isVirtualGoods
            this.virtualPre = this.goodsData.reserveNameList.map((item) => {
              item.reserveValue = ''
              return item
            })
          }

          this.loadFlag = true
          this.first_loading = false
        } else {
          this.$api.msg(res.msg)
        }
      })
    },

    check() {
      this.$request({
        url: 'v3/integral/front/integral/orderOperate/check',
        method: 'POST',
        data: {
          number: this.preParam.number,
          productId: this.goodsData.product.productId,
          cashPrice: this.goodsData.product.cashPrice,
          integralPrice: this.goodsData.product.integralPrice
        }
      }).then((res) => {
        if (res.state == 200) {
          this.submit()
        } else if (res.state == 267) {
          this.exceptionProList = res.data.productList
          this.exState = res.data.state
          this.exStateTxt = res.data.stateValue
          if (this.exState == 7) {
            this.$refs.purchasePop.open(0)
          } else if (this.exState == 5) {
            this.$refs.purchasePop.open(1)
          } else {
            this.$api.msg(res.data.stateValue)
          }
        } else {
          this.$api.msg(res.data.stateValue)
        }
      })
    },

    //提交订单
    submit() {
      //先判断已选地址是否为空
      if (!this.orderAddress.addressId) {
        uni.showToast({
          title: this.$L('请设置收货地址'),
          icon: 'none',
          duration: 1500
        })
        return
      }

      this.reserveInfoList = []
      if (this.isVG == 2 && this.virtualPre.length > 0) {
        for (let i = 0; i < this.virtualPre.length; i++) {
          let {
            reserveName,
            reserveType,
            reserveValue,
            reserveNameId,
            isRequired
          } = this.virtualPre[i]
          if (
            this.checkPreMsg(reserveType, reserveValue, reserveName, isRequired)
          ) {
            this.reserveInfoList.push({
              reserveName,
              reserveValue,
              reserveNameId
            })
          } else {
            return
          }
        }
      }

      // #ifdef H5
      this.order_from = 2
      // #endif

	  //app-1-start
      // #ifdef APP-PLUS
      switch (uni.getDeviceInfo().platform) {
        case 'android':
          this.order_from = 3
          break
        case 'ios':
          this.order_from = 4
          break
        default:
          break
      }
      // #endif
	  //app-1-end
	  //wx-1-start
      // #ifdef MP-WEIXIN
      this.order_from = 5
      // #endif
	  //wx-1-end
      // #ifdef MP-BAIDU
      this.order_from = 7
      // #endif

      // #ifdef MP-ALIPAY
      this.order_from = 8
      // #endif

      // #ifdef MP-TOUTIAO
      this.order_from = 9
      // #endif

      const { preParam } = this
      let param = {}
      param.url = 'v3/integral/front/integral/orderOperate/submit'
      ;(param.header = {
        'Content-Type': 'application/json'
      }),
        (param.method = 'POST')
      param.data = {}
      param.data.addressId = this.orderAddress.addressId
      param.data.orderFrom = this.order_from
      param.data.productId = JSON.parse(preParam.productId)
      param.data.number = preParam.number
      param.data.remark = this.remark
      param.data.invoiceId = this.invoiceId
      param.data.integral = this.useIntegral
      if (this.isVG == 2) {
        param.data.reserveInfoList = this.reserveInfoList
      }
      this.$request(param)
        .then((res) => {
          if (res.state == 200) {
            let need_pay = res.data.needPay
            let paySn = res.data.paySn
            this.$sldStatEvent({ behaviorType: 'buy',paySn })
            this.getPayInfo(paySn)
          } else {
            this.$api.msg(res.msg)
          }
        })
        .catch((e) => {
          //异常处理
        })
    },

    //获取支付信息
    getPayInfo(paySn) {
      this.$request({
        url: 'v3/integral/front/integral/orderPay/payInfo',
        data: {
          paySn: paySn,
          payFrom: 1
        }
      })
        .then((res) => {
          if (res.state == 200) {
            this.$Router.replace({
              path: '/standard/point/product/pay',
              query: { paySn, payMethodType: 'create' }
            })
          } else if (res.state == 267) {
            this.$api.msg(res.msg + this.$L(',2s后自动跳转订单列表'))
            setTimeout(() => {
              this.$Router.replace('/standard/point/order/list')
            }, 2000)
          }
        })
        .catch((e) => {})
    },

    //根据收货地址id获取总的运费
    changeAddress(selAddress) {
      this.orderAddress = selAddress
      this.confirmOrder(2)
    },
    //备注输入框聚焦
    handleFocus() {
      // #ifdef H5
      if (devicePlatform !== 'ios') {
        this.isBottomShow = false
      }
      // #endif
    },
    //失去焦点
    handleBlur() {
      this.isBottomShow = true
    },
    // 跳转我的发票页面
    toInvoice() {
      this.$Router.push({
        path: '/pages/invoice/myInvoice',
        query: { isVatInvoice: this.isVatInvoice }
      })
    },
    //校验预留信息
    //校验预留信息
    checkPreMsg(type, value, name, isRequired) {
      switch (type) {
        case 1: {
          if (isRequired == 1) {
            return this.$checkMobile(value, name)
          } else {
            let regMobile = /(1[3-9]\d{9}$)/
            if (value && !regMobile.test(value)) {
              this.$api.msg(`${this.$L('请输入正确的')}${name}!`)
              return false
            } else {
              return true
            }
          }

          break
        }
        case 2: {
          if (isRequired == 1) {
            return this.$checkIdentity(value, name)
          } else {
            if (value) {
              let reg18 =
                /^[1-9][0-9]{5}(18|19|20)[0-9]{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)[0-9]{3}([0-9]|(X|x))/
              let reg15 =
                /^[1-9][0-9]{5}[0-9]{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)[0-9]{2}[0-9]/
              if (reg18.test(value) || reg15.test(value)) {
                return true
              } else {
                this.$api.msg(`${this.$L('请输入正确的')}${name}`)
                return false
              }
            } else {
              return true
            }
          }

          break
        }

        case 3: {
          let regNum = /[0-9]+(.[0-9]+)?/
          if (isRequired == 1) {
            if (!value) {
              this.$api.msg(`${this.$L('请输入')}${name}`)
              return false
            } else if (!regNum.test(value)) {
              this.$api.msg(`${this.$L('请输入正确的')}${name}`)
              return false
            } else {
              return true
            }
          } else {
            return true
          }
          break
        }
        case 4: {
          if (isRequired == 1) {
            if (!value) {
              this.$api.msg(`${this.$L('请输入')}${name}`)
              return false
            } else {
              return true
            }
          } else {
            return true
          }
          break
        }
        case 5: {
          if (isRequired == 1) {
            return this.$checkEmail(value, name)
          } else {
            let reg =
              /^([a-zA-Z0-9]+[-_.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[-_.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,6}$/
            if (value && !reg.test(value)) {
              this.$api.msg(`${this.$L('请输入正确的')}${name}!`)
              return false
            } else {
              return true
            }
          }
          break
        }
      }
    }
  }
}
</script>

<style lang="scss">
page {
  background: $bg-color-split;
  padding-bottom: 100rpx;
  width: 750rpx;
  margin: 0 auto;
}

#container_int {
  /* #ifdef MP-BAIDU */
  padding-bottom: 100rpx;
  /* #endif */
}

.limVoice {
  display: block;
  max-width: 400rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 12rpx;
}

.address-section {
  background: #fff;
  position: relative;

  .order-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx;
    min-height: 185rpx;

    // height: 249rpx;
    .iconfont {
      color: $main-second-color;
      font-size: 24rpx;
    }

    .cen {
      .top {
        .tag {
        	width: 63rpx;
        	height: 32rpx;
        	margin-right: 20rpx;
        	background: var(--color_vice);
        	line-height: 32rpx;
        	text-align: center;
        	font-size: 24rpx;
        	color: #fff;
        	border-radius:4rpx
        }
        image {
          width: 63rpx;
          height: 30rpx;
          border-radius: 6rpx;
          margin-right: 20rpx;
        }

        text {
          color: #2d2d2d;
          font-size: 26rpx;
        }
      }

      .address {
        margin-top: 16rpx;
        margin-right: 20rpx;
        color: #2d2d2d;
        font-size: 32rpx;
        font-weight: bold;
        line-height: 40rpx;
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .member_info {
        margin-top: 15rpx;
        font-size: 28rpx;

        .name {
          color: #2d2d2d;
        }

        .mobile {
          color: $main-third-color;
          margin-left: 40rpx;
        }
      }
    }

    .empty_address {
      color: $main-font-color;
      font-size: 34rpx;
      flex: 1;

      .add_icon {
        margin-right: 20rpx;
        margin-top: -8rpx;
        font-size: 40rpx;
      }
    }
  }

  .cen {
    display: flex;
    flex-direction: column;
    flex: 1;
    font-size: 28rpx;
  }

  .a-bg {
    position: absolute;
    left: 0;
    bottom: 0;
    display: block;
    width: 100%;
    height: 7rpx;
  }
}

.goods-section {
  margin-top: 20rpx;
  background: #fff;
  box-sizing: border-box;

  .product_con {
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);

    .product_giveaway {
      color: #666666;
      background-color: #f8f8f8;
      padding: 6rpx 10rpx;
      font-size: 24rpx;
      width: fit-content;
      margin-bottom: 20rpx;
      margin-left: 20rpx;
      border-radius: 3px;
    }
  }

  .g-item {
    display: flex;
    padding: 20rpx 20rpx 20rpx 0;
    margin-left: 20rpx;
    width: calc(750rpx - 20rpx);

    .image {
      flex-shrink: 0;
      display: block;
      width: 220rpx;
      height: 220rpx;
      border-radius: 15rpx;
      background-size: cover;
      background-position: center center;
      background-repeat: no-repeat;
      background-color: #f8f6f7;
    }

    .right {
      flex: 1;
      padding: 15rpx 0 15rpx 24rpx;
      overflow: hidden;
      height: 220rpx;
    }

    .title {
      font-size: 28rpx;
      color: $main-font-color;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      word-break: break-word;
      line-height: 40rpx;
    }

    .spec {
      font-size: 24rpx;
      color: #949494;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      word-break: break-word;
      line-height: 40rpx;
    }

    .goods_item_specs {
      display: flex;
      align-items: center;
      font-size: 24rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #999999;
      line-height: 34rpx;

      .goods_item_spec {
        margin-right: 10rpx;
      }
    }

    .price-box {
      font-size: 30rpx;
      color: var(--color_price);

      .unit {
        font-weight: bold;
      }

      .price_int {
        margin-left: 4rpx;
        font-weight: bold;
      }

      .price_decimal {
        font-weight: bold;
      }
    }

    .step-box {
      position: relative;
    }
  }
}

.yt-list {
  margin-top: 20rpx;
  background: #fff;

  &.order_remark {
    padding: 10rpx 20rpx 20rpx;

    .title {
      color: $main-font-color;
      font-size: 28rpx;
      line-height: 32rpx;
    }

    .placeholder {
      color: $main-third-color;
      font-size: 24rpx;
    }

    .content {
      width: 100%;
      height: 103rpx;
      background: rgba(245, 245, 245, 1);
      border-radius: 6rpx;
      padding: 20rpx;
      color: $main-second-color;
      font-size: 26rpx;
      margin-top: 10rpx;
    }
  }
}

.store_info {
  margin-top: 0;
}

.store_list {
  width: 100%;
}

.yt-list-cell-giveaway {
  padding: 20rpx;
  line-height: 60rpx;

  &.b-b:after {
    left: 20rpx;
  }

  position: relative;

  .cell-tit {
    flex: 1;
    font-size: 26rpx;
    color: #333333;
    margin-right: 20rpx;
  }

  .giveaway_list {
    padding: 30rpx 20rpx;
    background: #f5f5f5;
    border-radius: 6px 6px 6px 6px;
    font-size: 24rpx;

    .giveaway_item {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .giveaway_item_left {
      display: flex;
      align-items: center;

      .giveaway_item_index {
        color: #999999;
      }

      .giveaway_item_name {
        color: #333333;
        width: 400rpx;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        display: inline-block;
      }
    }

    .giveaway_item_number {
      float: right;
    }
  }
}

.yt-list-cell {
  display: flex;
  align-items: center;
  padding: 20rpx;
  line-height: 60rpx;
  position: relative;

  .iconfont {
    color: #999999;
    font-size: 24rpx;
    margin-left: 19rpx;
  }

  &.cell-hover {
    background: #fafafa;
  }

  &.b-b:after {
    left: 20rpx;
  }

  .cell-icon {
    height: 32rpx;
    width: 32rpx;
    font-size: 22rpx;
    color: #fff;
    text-align: center;
    line-height: 32rpx;
    background: #f85e52;
    border-radius: 4rpx;
    margin-right: 12rpx;

    &.hb {
      background: #ffaa0e;
    }

    &.lpk {
      background: #3ab54a;
    }
  }

  .voice {
    color: #333333 !important;
    font-weight: 500 !important;
    display: flex;
  }

  .cell-more {
    align-self: center;
    font-size: 24rpx;
    color: $font-color-light;
    margin-left: 8rpx;
    margin-right: -10rpx;
  }

  .cell-tit {
    flex: 1;
    font-size: 28rpx;
    color: #666666;
    margin-right: 20rpx;
  }

  .cell-tip {
    font-size: 28rpx;
    color: var(--color_price);
    font-weight: 500;
    text-align: right;

    &.disabled {
      color: $font-color-light;
    }

    &.active {
      color: $base-color;
    }

    &.red {
      color: $base-color;
    }
  }

  &.desc-cell {
    .cell-tit {
      max-width: 90rpx;
    }
  }

  .desc {
    flex: 1;
    font-size: $font-base;
    color: $font-color-dark;
  }
}

/* 支付列表 */
.pay-list {
  padding-left: 40rpx;
  margin-top: 16rpx;
  background: #fff;

  .pay-item {
    display: flex;
    align-items: center;
    padding-right: 20rpx;
    line-height: 1;
    height: 110rpx;
    position: relative;
  }

  .icon-weixinzhifu {
    width: 80rpx;
    font-size: 40rpx;
    color: #6bcc03;
  }

  .icon-alipay {
    width: 80rpx;
    font-size: 40rpx;
    color: #06b4fd;
  }

  .icon-xuanzhong2 {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60rpx;
    height: 60rpx;
    font-size: 40rpx;
    color: $base-color;
  }

  .tit {
    font-size: 32rpx;
    color: $font-color-dark;
    flex: 1;
  }
}

.footer {
  position: fixed;
  left: 0;
  right: 0;
  margin: 0 auto;
  bottom: env(safe-area-inset-bottom);
  z-index: 995;
  width: 750rpx;
  height: 98rpx;
  font-size: 30rpx;
  background-color: #fff;
  z-index: 1;
  color: var(--color_price);

  .price-content {
    .should_pay {
      .tit {
        color: $main-font-color;
        font-size: 30rpx;
        line-height: 30rpx;
      }

      .unit,
      .small_price {
        font-size: 24rpx;
        font-weight: 500;
        line-height: 26rpx;
      }

      .big_price {
        font-size: 30rpx;
        font-weight: 500;
        line-height: 30rpx;
      }
    }

    .promotion_total {
      color: $main-font-color;
      font-size: 22rpx;
      margin-top: 8rpx;

      .current {
        color: var(--color_price);
        margin-left: 10rpx;
      }
    }
  }

  .submit {
    width: 219rpx;
    height: 100%;
    background: var(--color_main_bg);
    color: #fff;
    font-size: 28rpx;
    margin-left: 20rpx;

    &.not_enough {
      background-color: #999999;
    }
  }
}

/* 优惠券面板 */
.mask {
  display: flex;
  align-items: flex-end;
  position: fixed;
  left: 0;
  top: var(--window-top);
  bottom: 0;
  width: 100%;
  background: rgba(0, 0, 0, 0);
  z-index: 9995;
  transition: 0.3s;

  .mask-content {
    width: 100%;
    min-height: 30vh;
    max-height: 70vh;
    background: #f3f3f3;
    transform: translateY(100%);
    transition: 0.3s;
    overflow-y: scroll;
  }

  &.none {
    display: none;
  }

  &.show {
    background: rgba(0, 0, 0, 0.4);

    .mask-content {
      transform: translateY(0);
    }
  }
}

/* 优惠券列表 */
.coupon-item {
  display: flex;
  flex-direction: column;
  margin: 20rpx 24rpx;
  background: #fff;

  .con {
    display: flex;
    align-items: center;
    position: relative;
    height: 120rpx;
    padding: 0 30rpx;

    &:after {
      position: absolute;
      left: 0;
      bottom: 0;
      content: '';
      width: 100%;
      height: 0;
      border-bottom: 1px dashed #f3f3f3;
      transform: scaleY(50%);
    }
  }

  .left {
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex: 1;
    overflow: hidden;
    height: 100rpx;
  }

  .title {
    font-size: 32rpx;
    color: $font-color-dark;
    margin-bottom: 10rpx;
  }

  .time {
    font-size: 24rpx;
    color: $font-color-light;
  }

  .right {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 26rpx;
    color: $font-color-base;
    height: 100rpx;
  }

  .price {
    font-size: 44rpx;
    color: $base-color;

    &:before {
      content: '￥';
      font-size: 34rpx;
    }
  }

  .tips {
    font-size: 24rpx;
    color: $font-color-light;
    line-height: 60rpx;
    padding-left: 30rpx;
  }

  .circle {
    position: absolute;
    left: -6rpx;
    bottom: -10rpx;
    z-index: 10;
    width: 20rpx;
    height: 20rpx;
    background: #f3f3f3;
    border-radius: 100px;

    &.r {
      left: auto;
      right: -6rpx;
    }
  }
}

.empty_h {
  width: 750rpx;
  height: 20rpx;
}

.store_name {
  padding-left: 20rpx;
  padding-bottom: 30rpx;
  margin-top: 30rpx;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);

  image {
    width: 34rpx;
    height: 32rpx;
  }

  .store_name_text {
    font-size: 32rpx;
    color: #2d2d2d;
    font-weight: bold;
    margin-left: 10rpx;
  }

  .iconfont {
    // width: 13rpx;
    // height: 22rpx;
    font-size: 24rpx;
    margin-left: 10rpx;
  }
}

#store_red_wrap,
#store_no_good {
  position: fixed;
  top: 0;
  left: 0;
  width: 750rpx;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
  right: 0;
  margin: 0 auto;
}

.store_red {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 750rpx;
  height: 50vh;
  background-color: #fff;
  box-sizing: border-box;
}

.store_red .title {
  line-height: 110rpx;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #eee;
  font-size: 30rpx;
  color: #666;
}

.store_red_list {
  height: 470rpx;
  box-sizing: border-box;
  padding: 30rpx 30rpx 0;
}

.store_red_list .ticket-item {
  position: relative;
  display: block;
  margin-bottom: 30rpx;
  border-radius: 20rpx;
}

.ticket-item .line_left,
.ticket-item .line_right {
  position: absolute;
  top: 150rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #fff;
}

.ticket-item .line_left {
  left: -10rpx;
}

.ticket-item .line_right {
  right: -10rpx;
}

.store_red_list .ticket-item:last-child {
  margin: 0;
}

.store_red_list .circle_radio {
  display: flex;
  align-items: center;
  height: 160rpx;
  color: #fff;
  background: -webkit-gradient(
    linear,
    25% 100%,
    75% 100%,
    from(#ff8580),
    to(#ea5165)
  );
}

.circle_radio .red_item_wrap {
  display: flex;
  width: 100%;
  padding: 0 30rpx 0 50rpx;
  align-items: center;
  justify-content: space-between;
}

.circle_radio .red_item_wrap .red_h1 {
  font-size: 26rpx;
}

.circle_radio .red_item_wrap .red_h1 em {
  font-size: 54rpx;
}

.circle_radio .red_item_wrap .red_h2 {
  font-size: 26rpx;
}

.circle_radio .red_item_wrap .red_h2 em {
  display: block;
}

.circle_radio .red_item_wrap .red_h2 em:nth-child(2) {
  font-size: 20rpx;
  padding-top: 10rpx;
}

.circle_radio .red_item_wrap .red_h3 .red_h3_top {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
}

.red_h3 .red_h3_top image {
  width: 20rpx;
  height: 20rpx;
  margin-right: 15rpx;
}

.circle_radio .red_item_wrap .red_h3 .red_h3_bottom {
  padding: 10rpx 15rpx;
  font-size: 28rpx;
  color: #ec5667;
  background-color: #f9ccd1;
  margin-top: 18rpx;
  border-radius: 6rpx;
}

.ticket-item .red_p {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  font-size: 26rpx;
  color: #84292b;
  background: -webkit-gradient(
    linear,
    25% 100%,
    75% 100%,
    from(#d6706b),
    to(#c44654)
  );
  border-top: 3rpx dashed #fff;
}

.ticket-item .red_p image {
  width: 26rpx;
  height: 26rpx;
  margin-right: 10rpx;
}

#store_no_good {
  display: flex;
  align-items: center;
  justify-content: center;
}

#store_no_good .content {
  width: 580rpx;
  height: 773rpx;
  background-color: white;
  border-radius: 15rpx;

  .content_title {
    margin-top: 24rpx;
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30rpx;
    font-size: 32rpx;
    color: #2d2d2d;

    image {
      width: 22rpx;
      height: 22rpx;
    }
  }

  .good_list {
    height: 593rpx;
    overflow-y: scroll;
    width: 100%;

    .good_item {
      display: flex;
      align-items: center;
      padding: 30rpx;
      border-top: 1rpx solid #f2f2f2;

      image {
        width: 70rpx;
        height: 70rpx;
        border-radius: 6rpx;
      }

      .good_info {
        margin-left: 20rpx;
        position: relative;

        .good_name {
          width: 382rpx;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          font-size: 26rpx;
        }

        .good_spec {
          margin-top: 20rpx;
          font-size: 22rpx;
          color: #999999;
        }

        .num {
          position: absolute;
          bottom: 0rpx;
          right: 0rpx;
          font-size: 24rpx;
          color: #333333;
        }
      }
    }
  }

  .part_no_goods {
    width: 520rpx;
    height: 60rpx;
    font-size: 30rpx;
    color: white;

    display: flex;
    align-items: center;
    margin: 0 auto;
    border-radius: 30rpx;
    margin-top: 15rpx;

    .return {
      width: 50%;
      height: 60rpx;
      line-height: 60rpx;
      background-color: #ff8809;
      text-align: center;
      border-radius: 30rpx 0 0 30rpx;
    }

    .remove {
      width: 50%;
      height: 60rpx;
      line-height: 60rpx;
      background-color: #f90208;
      text-align: center;
      border-radius: 0 30rpx 30rpx 0;
    }
  }
}

.part_no_goods_another {
  width: 520rpx;
  height: 60rpx;
  font-size: 30rpx;
  color: white;
  display: flex;
  align-items: center;
  margin: 0 auto;
  border-radius: 30rpx;
  background-color: #f90208;

  .return {
    width: 100%;
    text-align: center;
  }
}

/* 优惠券弹框 start */
.integral_model {
  width: 100%;
  height: 900rpx;
  background: #f5f5f5;
  border-radius: 15rpx 15rpx 0 0;
  background-color: white;

  .integral_model_title {
    width: 100%;
    height: 100rpx;
    border-radius: 15rpx 15rpx 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12rpx 0 30rpx;
    box-sizing: border-box;
    z-index: 10;
    background: #ffffff;

    text {
      font-size: 32rpx;
      font-family: PingFang SC;
      font-weight: bold;
      color: #333333;
      line-height: 32rpx;
    }

    image {
      width: 46rpx;
      height: 46rpx;
    }
  }

  .available_integral {
    height: 80rpx;
    background: #f8f8f8;
    padding-left: 30rpx;
    line-height: 80rpx;
    font-size: 28rpx;
    color: #333333;

    text:nth-child(2) {
      font-weight: bold;
    }
  }

  .integral_model_list {
    box-sizing: border-box;
    height: 620rpx;
    width: 750rpx;
    overflow-x: hidden;
    box-sizing: border-box;

    .integral_item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 42rpx 30rpx 42rpx 32rpx;

      image {
        width: 32rpx;
        height: 32rpx;
      }

      .num {
        color: #666666;
        font-size: 28rpx;
      }

      text {
        color: var(--color_price);
      }
      .iconfont {
      	font-size: 32rpx;
      	color: #BBBBBB ;
      	margin-right: 20rpx;
      
      	&.item_check {
      		color: var(--color_main) !important;
      	}
      }
    }
  }

  .btn_con {
    width: 750rpx;
    height: 99rpx;
    background: #ffffff;
    box-shadow: 0px 0px 20rpx 0rpx rgba(86, 86, 86, 0.08);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    color: white;

    .not_use_integral {
      width: 343rpx;
      height: 70rpx;
      background: #bbbbbb;
      border-radius: 35rpx 0 0 35rpx;
      line-height: 70rpx;
      text-align: center;
    }

    .use_integral {
      width: 343rpx;
      height: 70rpx;
      background: var(--color_main);
      border-radius: 0 35rpx 35rpx 0;
      line-height: 70rpx;
      text-align: center;
    }
  }
}

/* 优惠券弹框 end */

.pre_message {
  background: #fff;

  .pre_msg_item {
    padding: 28rpx 22rpx;
    width: 100%;
    border-bottom: 2rpx solid #f2f2f2;

    .msg_left {
      font-size: 28rpx;
      font-family: MicrosoftYaHei;
      color: #333333;
      font-weight: 600;
      width: 200rpx;
      text-align: right;
      word-break: break-all;
    }

    .msg_right {
      margin-left: 20rpx;

      input {
        width: 500rpx;
      }
    }
  }
}
</style>
