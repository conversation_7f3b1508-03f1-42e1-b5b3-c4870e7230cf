<!-- 查看物流页面 -->
<template>
	<view :style="mix_diyStyle">
  <view class="look_logistics">
    <!-- 物流公司,及商品 start-->
    <view class="logistics_des">
      <view class="goods_image">
        <!-- <coverImage :src="logisticsInfo.goodsImage" width="200" height="200" class="image"></coverImage> -->
        <view
          class="image"
          :style="'background-image:url(' + logisticsInfo.goodsImage + ')'"
        ></view>
      </view>
      <view class="logistics_desc" v-if="logisticsInfo.type != '1'">
        <view class="logistics_type"
          >{{ $L('承运公司') }}：<text>{{
            logisticsInfo.expressName ? logisticsInfo.expressName : '--'
          }}</text></view
        >
        <!-- <view class="logistics_type">物流状态：<text>{{logisticsInfo.type ? logisticsInfo.type : '--'}}</text></view> -->
        <view class="logistics_type"
          >{{ $L('物流单号') }}：<text>{{
            logisticsInfo.expressNumber ? logisticsInfo.expressNumber : '--'
          }}</text></view
        >
      </view>
      <view class="logistics_desc" v-if="logisticsInfo.type == '1'">
        <view class="logistics_type"
          >{{ $L('联系人') }}：<text>{{
            logisticsInfo.expressName ? logisticsInfo.expressName : '--'
          }}</text></view
        >
        <!-- <view class="logistics_type">物流状态：<text>{{logisticsInfo.type ? logisticsInfo.type : '--'}}</text></view> -->
        <view class="logistics_type"
          >{{ $L('联系电话') }}：<text>{{
            logisticsInfo.expressNumber ? logisticsInfo.expressNumber : '--'
          }}</text></view
        >
      </view>
    </view>
    <!-- 物流公司,及商品 end-->

    <!-- 物流轨迹 start -->
    <view class="lofistics_info" v-if="logisticsRouteList.length > 0">
      <!-- 纵向排列 -->
      <uni-steps
        :options="logisticsRouteList"
        direction="column"
        :active="logisticsRouteList.length - 1"
      ></uni-steps>
    </view>
    <view
      class="no_data"
      v-if="logisticsRouteList.length == 0 && logisticsInfo.type != '1'"
    >
      <image
        :src="imgUrl + 'order-detail/no_logistics.png'"
        mode="aspectFit"
      ></image>
      <text>{{ $L('暂无物流信息，请耐心等待哦') }}~</text>
    </view>
    <!-- 物流轨迹 end -->
    <!-- 推荐商品 start-->
    <recommendList ref="recommendList"></recommendList>
    <!-- 推荐商品 end-->
  </view>
	</view>
</template>

<script>
import { mapState } from 'vuex'
import recommendGoods from '@/components/recommend-goods.vue'
import uniPopup from '@/components/uni-popup/uni-popup.vue'
import uniPopupMessage from '@/components/uni-popup/uni-popup-message.vue'
import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue'
import uniSteps from '@/components/uni-steps/uni-steps.vue' //步骤条
import recommendList from '../components/recommend_list.vue'
let startY = 0,
  moveY = 0,
  pageAtTop = true
export default {
  components: {
    recommendGoods,
    uniPopup,
    uniPopupMessage,
    uniPopupDialog,
    uniSteps,
    recommendList
  },
  data() {
    return {
      imgUrl: getApp().globalData.imgUrl,
      coverTransform: 'translateY(0px)',
      coverTransition: '0s',
      moving: false,
      logisticsInfo: {}, //物流信息
      logisticsRouteList: [] //物流轨迹信息
    }
  },
  async onLoad(option) {
    setTimeout(()=>{
       uni.setNavigationBarTitle({
        title: this.$L('查看物流')
      })   
    },0);
    
    //退款单号
    this.orderSn = this.$Route.query.orderSn
    this.productLeadLittle = this.$Route.query.productLeadLittle //商品图片
    this.initData()
    this.getLogisticsTrajectory()
  },
  // #ifndef MP
  onNavigationBarButtonTap(e) {
    const index = e.index
    if (index === 0) {
      this.navTo('/newPages/set/set')
    } else if (index === 1) {
	  //app-1-start
      // #ifdef APP-PLUS
      const pages = getCurrentPages()
      const page = pages[pages.length - 1]
      const currentWebview = page.$getAppWebview()
      currentWebview.hideTitleNViewButtonRedDot({
        index
      })
      // #endif
      //app-1-end
	  this.$Router.push('/newPages/notice/notice')
    }
  },
  // #endif
  computed: {
    ...mapState(['hasLogin', 'userInfo', 'userCenterData'])
  },
  onReachBottom() {
    this.$refs.recommendList.getMoreData()
  },
  methods: {
    initData() {},

    /**
     * 统一跳转接口,拦截未登录路由
     * navigator标签现在默认没有转场动画，所以用view
     */
    navTo(url) {
      if (!this.hasLogin) {
        let urls = this.$Route.path
        const query = this.$Route.query
        uni.setStorageSync('fromurl', {
          url: urls,
          query
        })
        url = '/pages/public/login'
      }
      this.$Router.push(url)
    },

    /**
     *  会员卡下拉和回弹
     *  1.关闭bounce避免ios端下拉冲突
     *  2.由于touchmove事件的缺陷（以前做小程序就遇到，比如20跳到40，h5反而好很多），下拉的时候会有掉帧的感觉
     *    transition设置0.1秒延迟，让css来过渡这段空窗期
     *  3.回弹效果可修改曲线值来调整效果，推荐一个好用的bezier生成工具 http://cubic-bezier.com/
     */
    coverTouchstart(e) {
      if (pageAtTop === false) {
        return
      }
      this.coverTransition = 'transform .1s linear'
      startY = e.touches[0].clientY
    },
    coverTouchmove(e) {
      moveY = e.touches[0].clientY
      let moveDistance = moveY - startY
      if (moveDistance < 0) {
        this.moving = false
        return
      }
      this.moving = true
      if (moveDistance >= 80 && moveDistance < 100) {
        moveDistance = 80
      }

      if (moveDistance > 0 && moveDistance <= 80) {
        this.coverTransform = `translateY(${moveDistance}px)`
      }
    },
    coverTouchend() {
      if (this.moving === false) {
        return
      }
      this.moving = false
      this.coverTransition = 'transform 0.3s cubic-bezier(.21,1.93,.53,.64)'
      this.coverTransform = 'translateY(0px)'
    },
    //获取物流轨迹接口
    getLogisticsTrajectory() {
      let that = this
      let param = {}
      param.url = 'v3/integral/front/integral/order/getTrace'
      param.method = 'GET'
      param.data = {}
      param.data.orderSn = that.orderSn //订单号
      that
        .$request(param)
        .then((res) => {
          if (res.state == 200) {
            let result = res.data
            that.logisticsInfo = result || {} //物流信息
            that.logisticsRouteList = result.routeList || [] //物流轨迹信息
            that.logisticsRouteList = that.logisticsRouteList.map(function (
              item
            ) {
              return {
                title: item.acceptTime,
                desc: item.acceptStation?item.acceptStation:item.remark,
              }
            })
            that.loadFlag = true
          } else {
            this.$api.msg(res.msg)
          }
        })
        .catch((e) => {
          //异常处理
        })
    }
  }
}
</script>

<style lang="scss">
page {
  background: $bg-color-split;
  width: 750rpx;
  margin: 0 auto;
}
.look_logistics {
  width: 100%;
  background: #f5f5f5;
  .logistics_des {
    border-top: 20rpx solid #f5f5f5;
    background-color: #ffffff;
    display: flex;
    padding: 20rpx;
    box-sizing: border-box;
    .goods_image {
      width: 200rpx;
      height: 200rpx;
      background: #f3f3f3;
      border-radius: 14rpx;
      .image {
        background-position: center center;
        background-repeat: no-repeat;
        background-size: cover;
        width: 200rpx;
        height: 200rpx;
        border-radius: 14rpx;
      }
    }
    .logistics_desc {
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin-left: 16rpx;
      .logistics_type {
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #949494;
        line-height: 32rpx;
        margin: 20rpx 0;
      }
    }
  }
  .lofistics_info {
    width: 710rpx;
    background: #ffffff;
    border-radius: 15rpx;
    margin: 20rpx;
  }
  .no_data {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    image {
     width: 380rpx;
     height: 280rpx;
      margin: 81rpx 0 43rpx;
    }
    text {
      font-size: 26rpx;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #9a9a9a;
    }
  }
  .recomment {
    margin-top: 60rpx;
  }
}
</style>
