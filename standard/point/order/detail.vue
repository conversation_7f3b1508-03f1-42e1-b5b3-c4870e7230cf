<!-- 订单详情页面 -->
<template>
	<view :style="mix_diyStyle">
		<scroll-view class="container" scroll-y @scrolltolower="getData" v-if="allData && isShow" :style="mix_diyStyle">
			<view class="main_content" :class="{ noPaddingBottom20: allData.orderState == 20 }" :style="{backgroundImage: 'url(' + imgUrl + 'point/goods/order_detail_bg.png)',backgroundSize: !isVirListEmp ? '750rpx 312rpx' : '750rpx 452rpx'}">
				<!-- 订单状态 start -->
				<view class="order_state">
					<!-- 待付款 start orderState == 10-->
					<block v-if="allData.orderState == 10">
						<view class="state_title">
							<image :src="imgUrl + 'order-detail/to_pay.png'" mode="aspectFit"></image>
							<text>{{ allData.orderStateValue }}</text>
						</view>
						<view class="state_time" v-if="stateTime">{{ $L('剩余') }}{{ stateTime }}{{ $L('自动关闭') }}</view>
					</block>
					<!-- 待付款 end-->
					<!-- 待发货  orderState == 20-->
					<block v-if="allData.orderState == 20">
						<view class="state_title await">
							<image :src="imgUrl + 'order-detail/delivered.png'" mode="aspectFit"></image>
							<text>{{ allData.orderStateValue }}</text>
						</view>
					</block>
					<!-- 待发货 end-->
					<!-- 待收货 -->
					<block v-if="allData.orderState == 30">
						<view class="state_title">
							<image :src="imgUrl + 'order-detail/wait_receive.png'" mode="aspectFit"
								style="width: 71rpx; height: 45rpx"></image>
							<text>{{ allData.orderStateValue }}</text>
						</view>
						<view class="state_time" v-if="stateTime">{{ $L('还剩') }}{{ stateTime }}{{ $L('自动确认') }}</view>
					</block>
					<!-- 待收货 end-->
					<!-- 已完成  start-->
					<block v-if="allData.orderState == 40">
						<view class="state_title await">
							<image :src="imgUrl + 'order-detail/success.png'" mode="aspectFit"
								style="width: 46rpx; height: 51rpx"></image>
							<text>{{ allData.orderStateValue }}</text>
						</view>
					</block>
					<!-- 已完成  end-->
					<!-- 取消订单，交易关闭 start orderState == 0-->
					<block v-if="allData.orderState == 0">
						<view class="state_title">
							<image :src="imgUrl + 'order-detail/to_pay.png'" mode="aspectFit"></image>
							<text>{{ allData.orderStateValue }}</text>
						</view>
						<view class="state_reason">{{ allData.cancelReasonContent }}</view>
						<!-- 注： allData.cancelRemark 不为空 时为平台取消  否则为会员取消 -->
						<view class="state_remark" v-if="allData.cancelRemark">{{allData.cancelRemark}}</view>
					</block>
					<!-- 取消订单，交易关闭 end-->
				</view>
				<!-- 订单状态 end -->

				<!-- 物流信息 start -->
				<view class="logistics_information" v-if="allData.orderState >= 30 &&allData.routeList &&allData.routeList.length > 0">
					<image :src="imgUrl + 'order-detail/wuliu.png'" mode="aspectFit" class="logistics_image"></image>
					<view class="logistics_des_right">
						<view class="logistics_info">
							<view class="logistics_des">{{allData.routeList[0].acceptStation ?allData.routeList[0].acceptStation : allData.routeList[0].remark}}</view>
							<view class="logistics_time">{{allData.routeList[0].acceptTime}}</view>
						</view>
						<image :src="imgUrl + 'order-detail/right_down.png'" mode="aspectFit" class="right_down"
							@click="lookLogistics()"></image>
					</view>
				</view>
				<!-- 物流信息 end -->

				<!-- 买家个人信息 start-->
				<view class="buyer_info" v-if="allData.isVirtualGoods==1">
					<svgGroup type="location" width="17" height="17" :color="diyStyle_var['--color_integral_main']"
						class="buyer_map"></svgGroup>
					<view class="info_det">
						<view class="info_detail">
							<view class="info_name">
								<text class="buyer_namer">{{allData.receiverName}}</text>
								<text class="buyer_phone">{{allData.receiverMobile}}</text>
							</view>
							<view class="info_address">{{allData.receiverAddress}}</view>
						</view>
					</view>
				</view>
				<!-- 买家个人信息 end-->

				<!-- 虚拟商品的预留信息start -->
				<view class="virtual_msg" v-if="isVirListEmp">
					<block v-for="(item, index) in allData.orderReserveList" :key="index">
						<view class="virtual_msg_item flex_row_start_start" v-if="item.reserveValue">
							<view class="msg_item_l">{{ item.reserveName }}:</view>
							<view class="msg_item_r">{{ item.reserveValue }}</view>
						</view>
					</block>
				</view>
				<!-- 虚拟商品的预留信息end -->

				<!-- 订单内商品信息 start -->
				<view class="order_goods">
					<view class="goods_list">
						<view>
							<view class="goods_pre" v-for="(item, index) in orderProductList" :key="index"
								@click="goProductDetail(item.productId, item.goodsId)">
								<view class="goods_image">
									<!-- <coverImage :src="item.productImage" width="200" height="200" class="image"></coverImage> -->
									<view class="image" :style="'background-image:url(' + item.productImage + ')'">
									</view>
								</view>
								<view class="goods_pre_right flex_column_between_start">
									<view class="goods_pre_top">
										<view class="goods_name">{{ item.goodsName }}</view>
										<view class="goods_des flex_row_between_end">
											<view class="goods_spec">{{item.specValues ? item.specValues : ''}}</view>
											<view class="goods_num">*{{ item.productNum }}</view>
										</view>
									</view>
									<view class="goods_prices flex_row_start_end">
										<view class="goods_prices_point">{{ item.integral }}{{ $L('积分') }}</view>
										<view class="goods_prices_point" v-if="item.integralPrice && item.cashAmount">+
										</view>
										<view class="goods_price" v-if="item.cashAmount">¥{{ $getPartNumber(item.cashAmount, 'int')}}{{ $getPartNumber(item.cashAmount, 'decimal') }}</view>
									</view>
								</view>
							</view>
							<!-- 订单内商品信息 end -->
							<view class="store_price_info no_top" v-if="allData.orderState != 10">
								<view class="actual_payment">
									<view class="actual_payment_title">
										<text>{{ $L('实付款') }}</text>
									</view>
									<view class="actual_payment_price">
										<text v-if="allData.integral">{{ allData.integral }}{{ $L('积分') }}</text>
										<text v-if="allData.integral && allData.cashAmount">+</text>
										<text v-if="allData.cashAmount">￥{{ allData.cashAmount }}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				<!-- 订单信息  start-->
				<view class="order_des">
					<view class="order_des_title">{{ $L('订单信息') }}</view>
					<view class="order_des_pre">
						<text>{{ $L('订单编号') }}：</text>
						<text>{{ allData.orderSn }}</text>
					</view>
					<block v-if="orderLogs && orderLogs.length > 0">
						<view class="order_des_pre" v-for="(item, index) in orderLogs" :key="index">
							<text>{{item.orderStateLog == 10? $L('创建时间：'): item.orderStateLog == 20? $L('付款时间：'): item.orderStateLog == 30? $L('发货时间：'): item.orderStateLog == 40? $L('完成时间：'): $L('取消时间：')}}</text>
							<text>{{ item.logTime }}</text>
						</view>
					</block>
				</view>
				<!-- 订单信息  end-->

				<!-- 拨打电话 start-->
				<view class="share_btn" @click="goCall()">
					<svgGroup type="to_phone" width="14" height="14" :color="diyStyle_var['--color_integral_main']">
					</svgGroup>
					<text>{{ $L('拨打电话') }}</text>
				</view>
				<!-- 拨打电话 end-->
				<!-- 推荐商品 start-->
				<!-- <view class="recomment">
					<recommendList ref="recommendList"></recommendList>
				</view> -->
				<!-- 推荐商品 end-->
			</view>
			<!-- 详情底部操作按钮 start-->
			<view class="order_det_bottom" v-if="allData.orderState != 20">
				<!-- 待付款 -->
				<block v-if="allData.orderState == 10">
					<view class="cancel_order" @click="cancelPopup()">{{$L('取消订单')}}</view>
					<view class="go_pay" @click="goPay">{{ $L('立即支付') }}</view>
				</block>
				<!-- 待收货 -->
				<block v-if="allData.orderState == 30">
					<view class="cancel_order" @click="lookLogistics()" v-if="allData.isVirtualGoods == 1">
						{{ $L('查看物流') }}</view>
					<view class="confirm_receipt" @click="confirmReceipt()">{{$L('确认收货')}}</view>
				</block>
				<!-- 订单完成 -->
				<block v-if="allData.orderState == 40">
					<view class="cancel_order" @click="lookLogistics()" v-if="allData.isVirtualGoods == 1">
						{{ $L('查看物流') }}</view>
				</block>
				<!-- 订单取消 -->
				<block v-if="allData.orderState == 0 || allData.orderState == 40">
					<view class="edit_address_btn" @click="delOrder()">{{$L('删除订单')}}</view>
				</block>
			</view>
			<!-- 详情底部操作按钮 end-->

			<!-- 取消订单选择原因弹框 -->
			<uni-popup ref="cancelPopup" type="bottom">
				<view class="cancel_popup">
					<view class="popup_top">
						<text>{{ $L('取消原因') }}</text>
						<image :src="imgUrl + 'order-detail/guanbi.png'" mode="aspectFit" @click="notCancel"></image>
					</view>
					<scroll-view class="uni-list cancel_list" scroll-x="true">
						<radio-group @change="radioChange">
							<label class="cancle_pre" v-for="(item, index) in cancelList" :key="index">
								<text>{{ item.content }}</text>
								<radio :value="item.value" :checked="item.value === current" color="#fc1c1c"
									style="transform: scale(0.8); margin-right: 0" />
							</label>
						</radio-group>
					</scroll-view>
					<view class="cancel_popup_btn">
						<text class="" @click="notCancel()">{{ $L('暂不取消') }}</text>
						<text class="" @click="confirmCancel()">{{ $L('确定取消') }}</text>
					</view>
				</view>
			</uni-popup>
		</scroll-view>
	</view>
</template>
<script>
	import {
		mapState
	} from 'vuex'
	import uniPopup from '@/components/uni-popup/uni-popup.vue'
	import uniPopupMessage from '@/components/uni-popup/uni-popup-message.vue'
	import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue'
	import recommendList from '../components/recommend_list.vue'
	let startY = 0,
		moveY = 0,
		pageAtTop = true
	export default {
		components: {
			uniPopup,
			uniPopupMessage,
			uniPopupDialog,
			recommendList
		},
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				coverTransform: 'translateY(0px)',
				coverTransition: '0s',
				moving: false,
				orderSn: '', //订单号
				parentSn: '',
				allData: {}, //订单详细信息
				orderProductList: [], //订单商品列表
				cancelList: [], //取消原因列表
				current: '0', //取消原因当前点击的是第0项
				reasonId: -1, //取消原因当前点击的原因id
				stateTime: '', //等待买家付款的剩余时间
				isShow: false,
				orderLogs: [], //订单日志
				secInterval: '', //定时器,
				isVirListEmp: false
			}
		},
		async onLoad(option) {
			//订单号
			this.orderSn = this.$Route.query.orderSn
		},
		onShow(option) {
			this.initData()
			this.getOrderDetail()
		},
		// #ifndef MP
		onNavigationBarButtonTap(e) {
			const index = e.index
			if (index === 0) {
				this.navTo('/newPages/set/set')
			} else if (index === 1) {
				//app-1-start
				// #ifdef APP-PLUS
				const pages = getCurrentPages()
				const page = pages[pages.length - 1]
				const currentWebview = page.$getAppWebview()
				currentWebview.hideTitleNViewButtonRedDot({
					index
				})
				// #endif
				//app-1-end
				this.$Router.push('/newPages/notice/notice')
			}
		},
		// #endif
		computed: {
			...mapState(['hasLogin', 'userInfo', 'userCenterData'])
		},
		methods: {
			initData() {},
			//去店铺
			goStore(storeid) {
				this.$Router.push({
					path: '/standard/store/shopHomePage',
					query: {
						vid: storeid
					}
				})
			},

			/**
			 * 统一跳转接口,拦截未登录路由
			 * navigator标签现在默认没有转场动画，所以用view
			 */
			navTo(url) {
				if (!this.hasLogin) {
					let urls = this.$Route.path
					const query = this.$Route.query
					uni.setStorageSync('fromurl', {
						url: urls,
						query
					})
					url = '/pages/public/login'
				}
				this.$Router.push(url)
			},

			/**
			 *  会员卡下拉和回弹
			 *  1.关闭bounce避免ios端下拉冲突
			 *  2.由于touchmove事件的缺陷（以前做小程序就遇到，比如20跳到40，h5反而好很多），下拉的时候会有掉帧的感觉
			 *    transition设置0.1秒延迟，让css来过渡这段空窗期
			 *  3.回弹效果可修改曲线值来调整效果，推荐一个好用的bezier生成工具 http://cubic-bezier.com/
			 */
			coverTouchstart(e) {
				if (pageAtTop === false) {
					return
				}
				this.coverTransition = 'transform .1s linear'
				startY = e.touches[0].clientY
			},
			coverTouchmove(e) {
				moveY = e.touches[0].clientY
				let moveDistance = moveY - startY
				if (moveDistance < 0) {
					this.moving = false
					return
				}
				this.moving = true
				if (moveDistance >= 80 && moveDistance < 100) {
					moveDistance = 80
				}

				if (moveDistance > 0 && moveDistance <= 80) {
					this.coverTransform = `translateY(${moveDistance}px)`
				}
			},
			coverTouchend() {
				if (this.moving === false) {
					return
				}
				this.moving = false
				this.coverTransition = 'transform 0.3s cubic-bezier(.21,1.93,.53,.64)'
				this.coverTransform = 'translateY(0px)'
			},
			//获取订单详情信息
			getOrderDetail() {
				uni.showLoading({
					title: this.$L('加载中')
				})
				let that = this
				let param = {}
				param.url = 'v3/integral/front/integral/order/detail'
				param.method = 'POST'
				param.data = {}
				param.data.orderSn = that.orderSn
				that
					.$request(param)
					.then((res) => {
						if (res.state == 200) {
							let result = res.data
							that.orderProductList = result.orderProductList
							that.orderLogs = result.orderLogList.reverse()
							that.allData = result || {}
							that.isShow = true
							that.parentSn = res.data.orderSn
							if (this.allData.orderReserveList.length) {
								this.isVirListEmp = this.allData.orderReserveList.some(
									(i) => i.reserveValue != ''
								)
							} else {
								this.isVirListEmp = false
							}
							uni.hideLoading()
							that.countup()
						} else {
							this.$api.msg(res.msg)
						}
					})
					.catch((e) => {
						//异常处理
					})
			},
			//计算时间差
			countup() {
				let that = this
				let createTime = that.allData.createTime
				let startStrs = createTime.split(' ')
				let createTimeStamp = that.strtotime(startStrs[0], startStrs[1]) //开始时间时间戳(毫秒)
				if (that.allData.orderState == 10) {
					//等待买家付款 ，24小时过期
					let endTimeStamp = createTimeStamp + 86400000 //结束时间时间戳   86400000是1天的
					that.countDown(endTimeStamp)
				} else if (that.allData.orderState == 30) {
					//等待买家收货
					let endTime = that.allData.autoReceiveTime //结束时间时间戳
					let endStrs = endTime.split(' ')
					let endTimeStamp = that.strtotime(endStrs[0], endStrs[1]) //开始时间时间戳(毫秒)
					that.countDown(endTimeStamp)
				}
			},
			//倒计时
			countDown(endTimeStamp) {
				let that = this
				that.secInterval = setInterval(() => {
					let currentTimestamp = new Date().getTime() //当前时间时间戳 （毫秒数）
					let diffrentTimeStamp = endTimeStamp - currentTimestamp //相差时间 毫秒数
					if (diffrentTimeStamp == 0) {
						that.stateTime = ''
						clearInterval(that.secInterval)
						that.getOrderDetail()
					} else if (diffrentTimeStamp > 0) {
						//将时间戳转换为天，时，分，秒 并倒计时
						that.stateTime = that.formatDuring(diffrentTimeStamp)
					} else {
						that.stateTime = ''
					}
				}, 1000)
			},
			//将标准格式（2014-08-02 11:23:12）转化为时间戳  函数   参数：time_str为（2014-08-02）   fix_time为（11:23:12）
			strtotime(time_str, fix_time) {
				let time = new Date().getTime()
				if (time_str) {
					let str = time_str.split('-')
					if (3 === str.length) {
						let year = str[0] - 0
						let month = str[1] - 0 - 1
						var day = str[2] - 0
						if (fix_time) {
							let fix = fix_time.split(':')
							if (3 === fix.length) {
								let hour = fix[0] - 0
								let minute = fix[1] - 0
								time = new Date(year, month, day, hour, minute).getTime()
							}
						} else {
							time = new Date(year, month, day).getTime()
						}
					}
				}
				return time
			},
			//将时间戳转换为时分秒
			formatDuring(mss) {
				let days = parseInt(mss / (1000 * 60 * 60 * 24))
				let hours = parseInt((mss % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
				let minutes = parseInt((mss % (1000 * 60 * 60)) / (1000 * 60))
				let seconds = ((mss % (1000 * 60)) / 1000).toFixed(0)
				if (days > 0) {
					return (
						days +
						this.$L(' 天 ') +
						hours +
						this.$L(' 小时 ') +
						minutes +
						this.$L(' 分钟 ')
					)
				} else if (hours > 0) {
					return hours + this.$L(' 小时 ') + minutes + this.$L(' 分钟 ')
				} else if (minutes > 1) {
					return minutes + this.$L(' 分钟 ')
				} else {
					//如果剩 1分钟之内就不让显示
					return ''
				}
			},
			//推荐商品
			getData() {
				this.$refs.recommendList.getMoreData()
			},
			//拨打电话
			goCall() {
				if (!this.allData.servicePhone) {
					uni.showToast({
						title: this.$L('商铺未设置客服电话'),
						icon: 'none'
					})
					return
				}
				uni.makePhoneCall({
					phoneNumber: this.allData.servicePhone
				})
			},
			//打开取消订单弹框
			cancelPopup() {
				this.$refs.cancelPopup.open()
				this.getCancelList()
			},
			//获取取消订单原因列表
			getCancelList() {
				let param = {}
				param.url = 'v3/system/front/reason/list'
				param.method = 'GET'
				param.data = {}
				param.data.type = 104
				this.$request(param)
					.then((res) => {
						if (res.state == 200) {
							this.cancelList = res.data || []
							this.cancelList &&
								this.cancelList.map((item, index) => (item.value = '' + index))
							this.reasonId = this.cancelList[0].reasonId
						} else {
							this.$api.msg(res.msg)
						}
					})
					.catch((e) => {
						//异常处理
					})
			},
			//取消原因单选框切换
			radioChange(e) {
				for (let i = 0; i < this.cancelList.length; i++) {
					if (this.cancelList[i].value === e.target.value) {
						this.reasonId = this.cancelList[i].reasonId
						break
					}
				}
			},
			//暂不取消订单
			notCancel() {
				this.$refs.cancelPopup.close()
				this.goRefresh()
			},
			//确定取消订单
			confirmCancel() {
				let that = this
				uni.showModal({
					title: that.$L('提示'),
					content: that.$L('确定取消该订单?'),
					success: function(res) {
						if (res.confirm) {
							let param = {}
							param.url = 'v3/integral/front/integral/orderOperate/cancel'
							param.method = 'POST'
							param.data = {}
							param.data.orderSn = that.parentSn
							param.data.reasonId = that.reasonId
							that
								.$request(param)
								.then((res) => {
									if (res.state == 200) {
										that.$api.msg(res.msg)
										that.$refs.cancelPopup.close()
										that.goRefresh()
									} else {
										that.$api.msg(res.msg)
									}
								})
								.catch((e) => {
									//异常处理
								})
						} else if (res.cancel) {
							that.$refs.cancelPopup.close()
						}
					}
				})
			},
			//去付款
			goPay() {
				this.$Router.push({
					path: '/standard/point/product/pay',
					query: {
						paySn: this.allData.paySn,
						payMethodType: 'orderDetail'
					}
				})
			},
			//查看物流
			lookLogistics() {
				this.$Router.push({
					path: '/standard/point/order/lookLogistics',
					query: {
						orderSn: this.orderSn
					}
				})
			},
			//更新当前页面方法
			goRefresh() {
				let pages = getCurrentPages()
				let currPage = pages[pages.length - 1] //当前页面
				let beforePage = pages[pages.length - 2] //上一页
				currPage.$vm.getOrderDetail() //更新当前页面数据
				beforePage.$vm.loadData() //更新上一页数据
			},
			//删除订单
			delOrder() {
				let that = this
				uni.showModal({
					title: that.$L('提示'),
					content: that.$L('确定删除该订单?'),
					success: function(res) {
						if (res.confirm) {
							let param = {}
							param.url = 'v3/integral/front/integral/orderOperate/delete'
							param.method = 'POST'
							param.data = {}
							param.data.orderSn = that.orderSn
							that
								.$request(param)
								.then((res) => {
									if (res.state == 200) {
										that.$api.msg(res.msg)
										that.goRefresh()
										that.$Router.back(1)
									} else {
										that.$api.msg(res.msg)
									}
								})
								.catch((e) => {
									//异常处理
								})
						} else if (res.cancel) {}
					}
				})
			},
			//确认收货
			confirmReceipt() {
				let that = this
				uni.showModal({
					title: that.$L('提示'),
					content: that.$L('确认收货？'),
					success: function(res) {
						if (res.confirm) {
							let param = {}
							param.url = 'v3/integral/front/integral/orderOperate/receive'
							param.method = 'POST'
							param.data = {}
							param.data.orderSn = that.orderSn
							that
								.$request(param)
								.then((res) => {
									if (res.state == 200) {
										that.$api.msg(res.msg)
										that.goRefresh()
										that.$Router.back(1)
									} else {
										that.$api.msg(res.msg)
									}
								})
								.catch((e) => {
									//异常处理
								})
						}
					}
				})
			},
			//去商品详情页
			goProductDetail(productId, goodsId) {
				this.$Router.push({
					path: '/standard/point/product/detail',
					query: {
						productId,
						goodsId
					}
				})
			}
		}
	}
</script>
<style lang="scss">
	page {
		background: $bg-color-split;
		width: 750rpx;
		margin: 0 auto;
	}

	.container {
		display: flex;
		flex: 1;
		width: 100%;
		height: 100vh;
		position: relative;
		background-color: #ffffff;

		.main_content {
			width: 100%;
			/* background: url('/static/order/detail_bg.png') no-repeat; */
			min-height: calc(var(--status-bar-height) + 452rpx);
			padding-top: var(--status-bar-height);
			background-repeat: no-repeat;
			background-size: 750rpx 452rpx;
			padding-bottom: 90rpx;

			&.noPaddingBottom20 {
				padding-bottom: 0rpx;
			}

			.order_state {
				/* #ifndef H5 */
				padding-top: 0rpx;
				/* #endif */
				/* #ifdef H5 */
				padding-top: 96rpx;
				/* #endif */
				box-sizing: border-box;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				width: 100%;
				//app-2-start
				/* #ifdef APP-PLUS */
				height: 220rpx;
				/* #endif */
				//app-2-end
				/* #ifndef APP-PLUS */
				height: 296rpx;
				/* #endif */
				margin-bottom: 19rpx;

				.state_title {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 100%;

					image {
						width: 45rpx;
						height: 45rpx;
						margin-right: 20rpx;
					}

					text {
						font-size: 38rpx;
						font-family: PingFang SC;
						font-weight: bold;
						color: #ffffff;
						line-height: 32rpx;
					}
				}

				.state_reason {
					font-size: 24rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #ffffff;
					line-height: 32rpx;
					margin: 20rpx 0;
				}

				.state_remark {
					font-size: 24rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #ffffff;
					line-height: 32rpx;
				}

				.await {
					margin-bottom: 139rpx;
				}

				.state_time {
					font-size: 24rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #ffffff;
					line-height: 32rpx;
					margin: 22rpx 0 32rpx;
				}
			}

			.logistics_information {
				display: flex;
				background: #ffffff;
				box-shadow: 1rpx 3rpx 30rpx 0rpx rgba(102, 102, 102, 0.1);
				width: 710rpx;
				margin: 0 auto;
				padding: 40rpx 0 30rpx 20rpx;
				border-radius: 15rpx 15rpx 0 0;
				border-bottom: 1rpx solid #f4f4f4;

				.logistics_image {
					width: 34rpx;
					height: 28rpx;
					margin-right: 18rpx;
				}

				.logistics_des_right {
					display: flex;
					align-items: center;

					.logistics_info {
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						width: 565rpx;

						.logistics_des {
							font-size: 28rpx;
							font-family: PingFang SC;
							font-weight: 500;
							color: #333333;
							line-height: 39rpx;
							width: 519rpx;
							text-overflow: -o-ellipsis-lastline;
							overflow: hidden;
							text-overflow: ellipsis;
							display: -webkit-box;
							-webkit-line-clamp: 2;
							line-clamp: 2;
							-webkit-box-orient: vertical;
							margin-bottom: 21rpx;
						}

						.logistics_time {
							font-size: 26rpx;
							font-family: PingFang SC;
							font-weight: 500;
							color: #949494;
							line-height: 45rpx;
						}
					}

					.right_down {
						width: 46rpx;
						height: 46rpx;
					}
				}
			}

			.buyer_info {
				width: 710rpx;
				background: #ffffff;
				box-shadow: 1rpx 3rpx 30rpx 0rpx rgba(102, 102, 102, 0.1);
				border-radius: 15rpx;
				margin: 0 auto;
				display: flex;
				padding: 41rpx 20rpx;
				box-sizing: border-box;

				.buyer_map {
					width: 34rpx;
					height: 34rpx;
					margin-right: 20rpx;
				}

				.info_det {
					display: flex;
					width: 100%;
					justify-content: space-between;

					.info_detail {
						display: flex;
						flex-direction: column;

						.info_name {
							display: flex;
							align-items: center;
							font-size: 28rpx;
							font-family: PingFang SC;
							color: #2e2e2e;
							line-height: 28rpx;

							.buyer_namer {
								color: #2e2e2e;
								font-weight: bold;
							}

							.buyer_phone {
								color: #666666;
								margin-left: 20rpx;
								font-weight: 600;
							}
						}

						.info_address {
							width: 528rpx;
							font-size: 28rpx;
							font-family: PingFang SC;
							font-weight: 500;
							color: #343434;
							line-height: 39rpx;
							margin-top: 28rpx;
						}
					}
				}
			}

			.virtual_msg {
				width: 710rpx;
				background: #ffffff;
				box-shadow: 1rpx 3rpx 30rpx 0rpx rgba(102, 102, 102, 0.1);
				border-radius: 8px;
				margin: 0 auto;
				padding: 20px;

				.virtual_msg_item {
					font-size: 26rpx;
					line-height: 50rpx;
					color: #333;

					.msg_item_r {
						margin-left: 10rpx;
					}
				}
			}

			.order_goods {
				.goods_list {
					padding: 20rpx 0 0 0;

					.goods_pre {
						display: flex;
						margin: 0 20rpx;
						box-sizing: border-box;
						padding-bottom: 22rpx;
						border-bottom: 1rpx solid #f2f2f2;

						.goods_image {
							width: 200rpx;
							height: 200rpx;
							background: #f3f3f3;
							border-radius: 14px;

							.image {
								background-position: center center;
								background-repeat: no-repeat;
								background-size: cover;
								width: 200rpx;
								height: 200rpx;
								border-radius: 14rpx;
							}
						}

						.goods_pre_right {
							display: flex;
							margin-left: 20rpx;
							width: 485rpx;
							padding: 9rpx 0 20rpx;

							.goods_pre_top {
								.goods_name {
									width: 485rpx;
									font-size: 28rpx;
									font-family: PingFang SC;
									font-weight: 500;
									color: #343434;
									line-height: 39rpx;
									text-overflow: -o-ellipsis-lastline;
									overflow: hidden;
									text-overflow: ellipsis;
									display: -webkit-box;
									-webkit-line-clamp: 2;
									line-clamp: 2;
									-webkit-box-orient: vertical;
								}

								.goods_des {
									padding-top: 8rpx;
									box-sizing: border-box;

									.goods_spec {
										font-size: 24rpx;
										font-family: PingFang SC;
										font-weight: 400;
										color: #949494;
										line-height: 30rpx;
										margin-top: 20rpx;
									}

									.goods_num {
										font-size: 24rpx;
										font-family: PingFang SC;
										font-weight: 500;
										color: #2d2d2d;
										line-height: 30rpx;
									}
								}
							}

							.goods_prices {
								width: 485rpx;
								font-size: 30rpx;
								font-family: PingFang SC;
								font-weight: bold;
								color: var(--color_integral_price);
								line-height: 30rpx;
							}
						}
					}
				}
			}

			.order_des {
				border-top: 20rpx solid #f5f5f5;
				padding: 29rpx 20rpx 0;
				box-sizing: border-box;

				.order_des_title {
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #343434;
					line-height: 24rpx;
					margin-bottom: 19rpx;
				}

				.order_des_pre {
					font-size: 26rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #9a9a9a;
					line-height: 24rpx;
					display: flex;
					margin-bottom: 17rpx;
					line-height: 30rpx;

					text:nth-child(1) {
						color: #9a9a9a;
					}

					text:nth-child(2) {
						color: #343434;
					}
				}
			}

			.share_btn {
				width: 100%;
				height: 100rpx;
				background: #ffffff;
				display: flex;
				align-items: center;
				border-top: 1rpx solid #f2f2f2;
				box-sizing: border-box;
				justify-content: center;

				image {
					width: 24rpx;
					height: 29rpx;
				}

				text {
					font-size: 26rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #333333;
					line-height: 24rpx;
					margin-left: 15rpx;
				}
			}

			.recomment {
				background: #f5f5f5;
				box-sizing: border-box;
				padding-top: 10rpx;
			}
		}

		.order_det_bottom {
			position: fixed;
			bottom: 0;
			width: 100%;
			height: 90rpx;
			background: #ffffff;
			box-shadow: 1rpx 1rpx 20rpx 0rpx rgba(86, 86, 86, 0.11);
			display: flex;
			justify-content: flex-end;
			align-items: center;
			padding-right: 20rpx;
			box-sizing: border-box;

			.edit_address_btn {
				width: 131rpx;
				height: 50rpx;
				border: 1rpx solid #eeeeee;
				border-radius: 25rpx;
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 400;
				color: #343434;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.cancel_order {
				width: 131rpx;
				height: 50rpx;
				border: 1rpx solid #eeeeee;
				border-radius: 25rpx;
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 400;
				color: #343434;
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0 10rpx;
			}

			.go_pay {
				width: 120rpx;
				height: 50rpx;
				background: var(--color_integral_main);
				box-shadow: 1rpx 3rpx 15rpx 0rpx var(--color_integral_halo);
				border-radius: 25rpx;
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 400;
				color: #ffffff;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.confirm_receipt {
				width: 140rpx;
				height: 50rpx;
				background: var(--color_integral_main);
				border-radius: 25rpx;
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 400;
				color: #ffffff;
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}
	}

	.cancel_popup {
		width: 100%;
		height: 700rpx;
		background: #ffffff;
		border-radius: 15rpx 15rpx 0 0;
		position: fixed;
		width: 100% !important;
		z-index: 20;
		bottom: 0;

		.popup_top {
			height: 100rpx;
			width: 100%;
			display: flex;
			padding: 0 39rpx;
			align-items: center;
			justify-content: space-between;
			border-bottom: 1rpx solid #f8f8f8;

			text {
				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #343434;
				line-height: 32rpx;
			}

			image {
				width: 30rpx;
				height: 30rpx;
			}
		}

		.cancel_list {
			padding-bottom: 128rpx;
			box-sizing: border-box;
			height: 600rpx;

			.cancle_pre {
				width: 100%;
				padding: 29rpx 40rpx;
				box-sizing: border-box;
				display: flex;
				justify-content: space-between;

				text {
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #666666;
					line-height: 32rpx;
				}
			}
		}

		.cancel_popup_btn {
			position: fixed;
			bottom: 40rpx;
			z-index: 30;
			display: flex;
			width: 100%;
			justify-content: center;

			text:nth-child(1) {
				width: 334rpx;
				height: 70rpx;
				background: var(--color_integral_vice);
				border-radius: 35rpx 0 0 35rpx;
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #ffffff;
				line-height: 32rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			text:nth-child(2) {
				width: 334rpx;
				height: 70rpx;
				background: var(--color_integral_main);
				border-radius: 0 35rpx 35rpx 0;
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #ffffff;
				line-height: 32rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}

	.store_name {
		padding-left: 20rpx;
		padding-bottom: 30rpx;
		margin-top: 30rpx;
		display: flex;
		align-items: center;

		image {
			width: 34rpx;
			height: 32rpx;
		}

		.store_name_text {
			font-size: 32rpx;
			color: #2d2d2d;
			font-weight: bold;
			margin-left: 10rpx;
		}

		.iconfont {
			// width: 13rpx;
			// height: 22rpx;
			font-size: 24rpx;
			margin-left: 10rpx;
		}
	}

	.store_price_info {
		.store_price_all {
			margin: 18rpx 0;
		}

		.freight {
			display: flex;
			padding: 0 20rpx;
			box-sizing: border-box;
			justify-content: space-between;

			.freight_title {
				font-size: 26rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #343434;
				line-height: 30rpx;
			}

			.freight_price {
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #2e2e2e;
				line-height: 30rpx;
			}
		}

		.actual_payment {
			display: flex;
			justify-content: space-between;
			padding: 0 20rpx;
			box-sizing: border-box;
			align-items: center;
			height: 90rpx;

			.actual_payment_title {
				display: flex;
				font-size: 26rpx;
				font-family: PingFang SC;
				font-weight: 500;
				line-height: 30rpx;
				color: #343434;

				text:nth-child(2) {
					color: #949494;
				}
			}

			.actual_payment_price {
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: var(--color_integral_main);
				line-height: 30rpx;
			}
		}
	}
</style>