<!-- 聊天消息中心列表页面 -->
<template>
    <view :style="mix_diyStyle">
        <view class="message_center">
            <!-- #ifdef MP -->
            <view class="message_center_nav-bar" :style="{ paddingTop: menuButtonTop }">
                <view class="nav-bar" :style="{ height: menuButtonHeight }">
                    <image :src="imgUrl + 'fanhui1.png'" mode="" @click="$back"></image>
                    <view class="">{{ $L('消息中心') }}</view>
                    <view class="nav-bar_close flex_row_center_center" @click="cleanUp">
                        <image :src="imgUrl + 'clearUnread.png'" mode=""></image>
                        <text class="">{{ $L('清除未读') }}</text>
                    </view>
                    <view class="nav-bar_close flex_row_center_center" style="margin-left: 38rpx" @click="goNoticeSet">
                        <image :src="imgUrl + 'messageSettings.png'" mode=""></image>
                        <text class="">{{ $L('设置') }}</text>
                    </view>
                </view>
            </view>
            <view class="" :style="{ paddingTop: menuButtonTop, paddingBottom: '20rpx', borderTop: '1rpx solid #f2f2f2' }">
                <view class="nav-bar" :style="{ height: menuButtonHeight }"></view>
            </view>
            <!-- #endif -->
            <!-- app-1-start -->
            <!-- #ifdef APP -->
            <view class="message_center_nav-bar nav-bar_app">
                <view class="nav-bar">
                    <image :src="imgUrl + 'fanhui1.png'" mode="" @click="$back"></image>
                    <view class="">{{ $L('消息中心') }}</view>
                    <view class="nav-bar_close flex_row_center_center" @click="cleanUp">
                        <image :src="imgUrl + 'clearUnread.png'" mode=""></image>
                        <text class="">{{ $L('清除未读') }}</text>
                    </view>
                    <view class="nav-bar_close flex_row_center_center" style="margin-left: 38rpx" @click="goNoticeSet">
                        <image :src="imgUrl + 'messageSettings.png'" mode=""></image>
                        <text class="">{{ $L('设置') }}</text>
                    </view>
                </view>
            </view>
            <view class="nav-bar_apps" style="padding-bottom: 20rpx">
                <view class="nav-bar"></view>
            </view>
            <!-- #endif -->
            <!-- app-1-end -->
            <!-- #ifdef H5 -->
            <view class="message_center_nav-bar nav-bar_apps nav-bar_h5 flex_row_between_center">
                <view class="nav-bar flex_row_end_center">
                    <view class="nav-bar_close flex_row_center_center" @click="cleanUp">
                        <image :src="imgUrl + 'clearUnread.png'" mode=""></image>
                        <text class="">{{ $L('清除未读') }}</text>
                    </view>
                    <view class="nav-bar_close flex_row_center_center" style="margin-left: 38rpx" @click="goNoticeSet">
                        <image :src="imgUrl + 'messageSettings.png'" mode=""></image>
                        <text class="">{{ $L('设置') }}</text>
                    </view>
                </view>
            </view>
            <!-- #endif -->
            <!-- 消息类型 start -->
            <view class="message_type_con">
                <view class="message_type_pre" v-for="(item, index) in noticeList" :key="index" @click="linkTo(item.tplTypeCode, item.msgNum)">
                    <view class="flex_row_start_center">
                        <image class="notice-icon" :src="system_news" mode="aspectFit" v-if="item.tplTypeCode == 'system_news'"></image>
                        <image class="notice-icon" :src="order_news" mode="aspectFit" v-if="item.tplTypeCode == 'order_news'"></image>
                        <image class="notice-icon" :src="assets_news" mode="aspectFit" v-if="item.tplTypeCode == 'assets_news'"></image>
                        <image class="notice-icon" :src="appointment_news" mode="aspectFit" v-if="item.tplTypeCode == 'appointment_news'"></image>
                        <image class="notice-icon" :src="after_sale_news" mode="aspectFit" v-if="item.tplTypeCode == 'after_sale_news'"></image>
                        <text>{{ item.msgName }}</text>
                        <text class="message_type_nums message_type_nums_9" v-if="item.msgNum > 0">{{ item.msgNum >= 99 ? '99+' : item.msgNum }}</text>
                    </view>
                    <view>
                        <image :src="imgUrl + 'order/right.png'" mode="widthFix" class="right-icon"></image>
                    </view>
                </view>
            </view>
            <!-- 消息类型 end -->
        </view>
    </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
export default {
    data() {
        return {
            // #ifdef MP
            menuButtonHeight: uni.getMenuButtonBoundingClientRect().height + 'px',
            menuButtonTop: uni.getMenuButtonBoundingClientRect().top + 'px',
            menuButtonleft: uni.getMenuButtonBoundingClientRect().left + 'px',
            // #endif
            imgUrl: getApp().globalData.imgUrl,
            assets_news: getApp().globalData.imgUrl + 'member/icon10.png',
            order_news: getApp().globalData.imgUrl + 'member/icon20.png',
            after_sale_news: getApp().globalData.imgUrl + 'member/icon30.png',
            system_news: getApp().globalData.imgUrl + 'member/icon60.png',
            appointment_news: getApp().globalData.imgUrl + 'member/icon70.png',
            setting_icon: getApp().globalData.imgUrl + 'member/receive_settings.png',
            loadingState: 'first_loading',
            noticeList: [],
            showState: false,
            leftNum: '0'
        };
    },
    onLoad() {
        setTimeout(() => {
            uni.setNavigationBarTitle({
                title: this.$L('消息中心')
            });
        }, 0);
    },
    computed: {
        ...mapState(['userInfo', 'userCenterData'])
    },
    onShow() {
        this.getNoticeNum();
    },
    methods: {
        ...mapMutations(['saveChatBaseInfo']),
        // 一键已读
        cleanUp() {
            let param = {};
            param.url = 'v3/msg/front/msg/read';
            param.method = 'POST';
            param.data = {};
            param.data.isAllRead = true;
            param.data.isPc = false;
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    this.getNoticeNum();
                    this.$api.msg(res.msg);
                } else {
                    this.$api.msg(res.msg);
                }
            });
        },
        //去消息设置页面
        goNoticeSet() {
            this.$Router.push('/newPages/notice/receivingSet');
        },
        //导航栏跳转页面
        linkTo(tplTypeCode, msgNum) {
            this.$Router.push({
                path: '/newPages/notice/notice',
                query: {
                    tplType: tplTypeCode
                }
            });
        },
        //获取消息未读数
        getNoticeNum() {
            let param = {};
            param.url = 'v3/msg/front/msg/msgListNum';
            param.method = 'GET';
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    this.noticeList = res.data.filter(e => e.tplTypeCode != 'assets_news');
                } else {
                    this.$api.msg(res.msg);
                }
            });
        }
    }
};
</script>

<style lang="scss">
page {
    background: #ffffff;
}

.message_center {
    width: 750rpx;

    /* 消息类型 start */
    .message_type_con {
        display: flex;
        flex-direction: column;
        padding: 16rpx 0;
        gap: 16rpx;

        .message_type_pre {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 24rpx 4%;
            background: #ffffff;
            border-radius: 12rpx;
            box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.06);

            .notice-icon {
                width: 56rpx;
                height: 56rpx;
            }

            text {
                flex: 1;
                margin: 0 16rpx;
                font-size: 30rpx;
                color: #333333;
                text-align: left;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .message_type_nums {
                margin-right: 12rpx;
            }
            .right-icon {
                width: 16rpx;
            }
        }
    }
    /* 消息类型 end */
}

.nav-bar {
    width: 100%;
    /* #ifndef H5 */
    display: flex;
    align-items: center;
    justify-content: flex-start;
    /* #endif */
    padding: 0 20rpx;
    z-index: 9;
}

.nav-bar image {
    width: 21rpx;
    height: 35rpx;
}

.nav-bar view {
    font-size: 36rpx;
    font-family: PingFang SC;
    color: #000;
    margin-left: 21rpx;
}
.message_center_nav-bar {
    /* #ifndef H5 */
    position: fixed;
    padding-bottom: 20rpx;
    /* #endif */
    /* #ifdef MP */
    border-top: 1rpx solid #f2f2f2;
    /* #endif */
    width: 100%;
    background: #fff;
    z-index: 999;
    .nav-bar {
        z-index: 999;
    }
    .nav-bar_close {
        image {
            width: 31rpx;
            height: 30rpx;
            margin-top: 2rpx;
        }
        text {
            margin-left: 5rpx;
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #999999;
        }
    }
}
.nav-bar_app {
    padding-top: calc(var(--status-bar-height) + 15rpx);
    .nav-bar {
        height: 50rpx;
    }
}
.nav-bar_apps {
    padding-top: calc(var(--status-bar-height) + 15rpx);
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid #f2f2f2;
    .nav-bar {
        width: 100%;
        height: 50rpx;
    }
}
.nav-bar_h5 {
    width: 750rpx;
    .nav-bar {
        flex: 1;
        height: 50rpx;
    }
}
</style>
