<template>
	<view :style="mix_diyStyle">
		<view class="shop_homepage">
			<!-- 透明遮罩层 -->
			<view class="transparent_mask" v-if="transparent_mask" @tap="hideMask"></view>
			<view class="content2">
				<!-- #ifdef MP -->
				<!-- `calc(${menuButtonTop} - 10rpx)` -->
				<view class="search_wrap" :style="{ paddingTop: menuButtonTop }" v-if="!searchShow">
					<view class="searchs search_mp" :style="{ height: menuButtonHeight, width: menuButtonleft }">
						<view class="search_mp_name">
							<image :src="imgurl + 'fanhui.png'" mode="" @click="$back"></image>
							<view class="more_tips">
								<image class="more" :src="imgurl + 'sanxian.png'" @tap="moreTips"></image>
								<block v-if="tips_show">
									<view class="triangle-up"></view>
									<view class="tips">
										<view
											v-for="(item, index) in tips"
											:key="index"
											class="tips_pre"
											@tap="handleLink"
											:data-link="item.tips_link"
											:open-type="item.type"
											:data-type="item.type"
											plain="true"
										>
											<image :src="item.tips_img"></image>
											<text>{{ item.tips_name }}</text>
										</view>
									</view>
								</block>
							</view>
						</view>
						<view class="left" @tap="goSearch">
							<image :src="imgurl + 'stord_ser1.png'" mode="aspectFit"></image>
							<text>{{ $L('搜索店铺内商品') }}</text>
						</view>
					</view>
				</view>
				<!-- #endif -->
				<!-- 搜索 -->
				<view class="fixed_top_status_bar" v-if="!searchShow"></view>
				<view class="search" v-if="!searchShow">
					<!-- #ifdef H5 || APP-PLUS -->
					<image class="top_w_b top_w_b_not" @tap="go_back" v-if="!shop_open" :src="imgUrl + 'to_right.png'"></image>
					<image class="top_w_b top_w_b12" @tap="go_back" v-else :src="imgUrl + 'fanhui.png'"></image>
					<!-- #endif -->
					<!-- #ifndef MP -->
					<view v-if="shop_open" class="search_input flex_row_start_center" @tap="goSearch">
						<text class="iconfont iconsousuo1"></text>
						<view class="">
							{{ $L('搜索店铺内商品') }}
						</view>
					</view>
					<view v-if="shop_open" class="more_tips">
						<image class="more" :src="imgUrl + 'store/more1.png'" @tap="moreTips"></image>
						<block v-if="tips_show">
							<view class="triangle-up"></view>
							<view class="tips">
								<button
									v-for="(item, index) in tips"
									:key="index"
									class="tips_pre"
									@tap="handleLink"
									:data-link="item.tips_link"
									:open-type="item.type"
									:data-type="item.type"
									plain="true"
								>
									<image :src="item.tips_img"></image>
									<text>{{ item.tips_name }}</text>
								</button>
							</view>
						</block>
					</view>
					<!-- #endif -->
				</view>
				<!-- 搜索 -->

				<view class="fixed_top_status_bar" :style="'opacity:' + scrollTopH / 100"></view>
				<!-- #ifndef MP -->
				<view class="search1" v-if="(!homePage || searchShow) && shop_open">
					<!-- #ifdef H5 || APP-PLUS -->
					<image class="top_w_b top_w_b12" @tap="go_back" :src="imgUrl + 'fanhui1.png'"></image>
					<!-- #endif -->
					<view class="search_input" @tap="goSearch">
						<text class="iconfont iconsousuo1"></text>
						<input :placeholder="$L('搜索店铺内商品')" disabled="true" />
					</view>
					<view class="more_tips">
						<image class="more" :src="imgUrl + 'store/more_black.png'" @tap="moreTips"></image>
						<block v-if="tips_show">
							<view class="triangle-up"></view>
							<view class="tips">
								<view
									v-for="(item, index) in tips"
									:key="index"
									class="tips_pre"
									@tap="handleLink"
									:data-link="item.tips_link"
									:open-type="item.type"
									:data-type="item.type"
									plain="true"
								>
									<image :src="item.tips_img"></image>
									<text>{{ item.tips_name }}</text>
								</view>
							</view>
						</block>
					</view>
				</view>
				<!-- #endif -->
				<!-- #ifdef MP -->
				<view class="search_wrap search_wrap1" :style="{ paddingTop: menuButtonTop }" v-if="(!homePage || searchShow) && shop_open">
					<view class="searchs search_mp" :style="{ height: menuButtonHeight, width: menuButtonleft }">
						<view class="search_mp_name">
							<image :src="imgurl + 'fanhui1.png'" mode="" @click="$back"></image>
							<view class="more_tips">
								<image class="more block" :src="imgurl + 'sanxian1.png'" @tap="moreTips"></image>
								<block v-if="tips_show">
									<view class="triangle-up"></view>
									<view class="tips">
										<view
											v-for="(item, index) in tips"
											:key="index"
											class="tips_pre"
											@tap="handleLink"
											:data-link="item.tips_link"
											:open-type="item.type"
											:data-type="item.type"
											plain="true"
										>
											<image :src="item.tips_img"></image>
											<text>{{ item.tips_name }}</text>
										</view>
									</view>
								</block>
							</view>
						</view>
						<view class="left" @tap="goSearch">
							<image :src="imgurl + 'stord_ser1.png'" mode="aspectFit"></image>
							<text>{{ $L('搜索店铺内商品') }}</text>
						</view>
					</view>
				</view>
				<!-- #endif -->
				<!-- 店铺信息-->
				<!-- #ifdef MP -->
				<view
					class="shop_des"
					v-if="shop_open"
					:style="
						'background-image:url(' +
						(homePage || searchShow || fenlei ? store_banner : '') +
						');background-size:100% 100%;background-repeat:no-repeat;padding-top:' +
						menuButtonHeights +
						'px;'
					"
				>
					<!-- #endif -->
					<!-- #ifndef MP -->
					<view
						class="shop_des"
						v-if="shop_open"
						:style="'background-image:url(' + (homePage || searchShow || fenlei ? store_banner : '') + ');background-size:100% 100%;background-repeat:no-repeat;'"
					>
						<!-- #endif -->
						<view class="des_top" v-if="homePage">
							<view class="shop_info" :style="'opacity:' + (1 - scrollTopH / 60)">
								<view class="info_left" @tap="handleStoreIntroduction" :data-vid="vender_detail.storeId">
									<image class="avat" :src="vender_detail.storeLogoUrl" mode="aspectFill"></image>
									<view class="info_des">
										<view class="info_top">
											<text>{{ vender_detail.storeName }}</text>
											<image :src="imgUrl + 'store/rightdown.png'"></image>
											<!-- <text v-if="vender_detail.isOwnShop == 1">{{ $L('自营') }}</text> -->
											<view class="storeScale">
												<text v-if="vender_detail.storeScale == '品牌'" class="brand_1 tag">品牌</text>
												<text v-if="vender_detail.storeScale == '连锁'" class="brand_2 tag">连锁</text>
												<text v-if="vender_detail.storeScale == '自营'" class="brand_3 tag">自营</text>
											</view>
										</view>
										<!-- <view class="info_bot">
										<image :src="imgUrl+'store/renqizhi.png'"></image>
										<text>{{$L('人气：')}}{{vender_detail.followNumber}}</text>
									</view> -->
									</view>
								</view>
								<!-- <view :class="is_favorites == 'false' ? 'info_right' : 'info_right1'" @tap="collect">
								{{is_favorites == 'false' ?$L('关注')  : $L('已关注')}}</view> -->
							</view>
							<!-- 店铺首页，全部分类，商品上新，优惠券 -->
							<view class="select_nav" v-if="!fenlei && !no_content_decor" :style="'opacity:' + (1 - scrollTopH / 160)">
								<view v-for="(item, idx) in navList" :key="idx" :class="{ noShow: idx == 2 }" class="nav_item" @tap="tabItemTap(idx)" :data-idx="idx">
									<!-- <image
									:src="idx == currentTabIndex || (currentTabIndex == 0 && index == 0) && isSelect ? item.selImgUrl : item.imgUrl">
								</image> -->
									<text
										:class="{
											selImgUrl: idx == currentTabIndex || (currentTabIndex == 0 && index == 0 && isSelect)
										}"
									>
										{{ item.name }}
									</text>
								</view>
							</view>
							<!-- 店铺未装修不显示店铺首页 -->
							<view class="select_nav" v-if="!fenlei && no_content_decor" :style="'opacity:' + (1 - scrollTopH / 160)">
								<view v-for="(item, idx) in navList1" :key="idx" :class="{ noShow: idx == 1 }" class="nav_item" @tap="tabItemTap(idx)" :data-idx="idx">
									<text
										:class="{
											selImgUrl: idx == currentTabIndex || (currentTabIndex == 0 && index == 0 && isSelect)
										}"
									>
										{{ item.name }}
									</text>
								</view>
							</view>
						</view>
						<view
							:class="homePage ? 'des_con' : 'des_con1'"
							v-if="!fenlei"
							:style="'overflow:' + (no_content_decor || no_content_productList || no_content_newProductList || no_content_coupon ? 'hidden' : 'auto')"
						>
							<!-- 店铺首页 -->
							<view class="home_decoration" v-if="home_decoration && !no_content_decor && is_load_home_dec">
								<home-deco
									:deco_info="deco_data"
									:store_width="width"
									:store_height="height"
									@showSpcPop="showSpcPop"
									is_show_top="is_show_top"
									:store_page_img="home_page_img"
									:home_is_show_top_cat="home_is_show_top_cat"
									:is_from_found="false"
									:store_id="vid"
								></home-deco>
								<!-- <view class="no_content" wx:if="{{no_content_decor}}">
							<image src="{{imgUrl + 'site/no_content.png'}}"></image>
							<text>店铺暂未装修~</text>
						</view> -->
							</view>
							<!-- 全部商品 -->
							<view class="all_commodities" :class="{ 'paddding-none': showOnly == 1 }" v-if="all_commodities">
								<!-- #ifdef MP -->
								<view :class="'all_commodities_nav ' + (searchShow ? 'nav1' : '')" :style="{ top: menuButtonHeights + 'px' }" v-if="false">
									<!-- #endif -->
									<!-- #ifndef MP -->
									<view :class="'all_commodities_nav ' + (searchShow ? 'nav1' : '')" v-if="false">
										<!-- #endif -->
										<view class="comprehensive" @tap="commoditiesNav" data-index="0">
											<text :class="'pre_title ' + (commodNavIdx == 0 ? 'active' : '')">{{ $L('综合') }}</text>
										</view>
										<view :class="'sales_volume ' + (commodNavIdx == 1 ? 'active' : '')" @tap="commoditiesNav" data-index="1" data-isAscendingOrder="0">
											{{ $L('销量') }}
										</view>
										<view class="price flex_row_center_center" @tap="commoditiesNav" data-index="2">
											<text :class="'pre_title ' + (commodNavIdx == 2 ? 'active' : '')">{{ $L('价格') }}</text>
											<view class="price_switch">
												<text
													class="iconfont iconziyuan14 rotate"
													:class="{
														sel: commodNavIdx == 2 && isAscendingOrder == 1
													}"
												></text>

												<text
													class="iconfont iconziyuan14"
													:class="{
														sel: commodNavIdx == 2 && isAscendingOrder != 1
													}"
												></text>
												<!-- <image class="xiala_up"
											:src="commodNavIdx == 2 && isAscendingOrder == 1 ? imgUrl+'store/xiala_up1.png' : imgUrl+'store/xiala_up.png'">
										</image>
										<image class="xiala_down"
											:src="commodNavIdx != 2 ? imgUrl+'store/xiala_down.png' : commodNavIdx == 2 && isAscendingOrder == 1 ? imgUrl+'store/xiala_down.png' : imgUrl+'store/xiala_down1.png'">
										</image> -->
											</view>
										</view>
										<view :class="'sales_volume ' + (commodNavIdx == 5 ? 'active' : '')" @tap="commoditiesNav" data-index="5">{{ $L('人气') }}</view>
										<view class="layout" @tap="layoutSwitch">
											<image :src="grid_list ? imgUrl + 'store/fenlei1.png' : imgUrl + 'store/fenlei2.png'"></image>
										</view>
									</view>
									<block>
										<!-- 商品列表 -->
										<view class>
											<view class="catshowgoods">
												<view class="catgoods-left">
													<!-- 店铺分类 -->
													<view
														class="catgoods-left-item"
														@click="clickLeftCat(item)"
														:class="{
															'active-item': curInnerLabelId == item.innerLabelId
														}"
														v-for="(item, index) in storeCatList"
														:key="index"
													>
														{{ item.innerLabelName }}
													</view>
												</view>
												<view class="catgoods-right">
													<template v-if="cat_goodsList.length > 0">
														<view class="right-goodsitem" :id="goodItem.goodsId" v-for="(goodItem, index) in cat_goodsList" :key="index">
															<image :src="goodItem.goodsImage" mode="" @click="goGoodsDetail(goodItem)" class="goodsitem-left"></image>
															<view class="goodsitem-right" @click="goGoodsDetail(goodItem)">
																<view class="flex_column_start_between">
																	<view class="goodsitem-right-tit">{{ goodItem.goodsName }}</view>
																	<view class="goodsitem-right-desc">{{ goodItem.goodsBrief }}</view>
																</view>
																<view class="goods-sales" v-if="showOnly != 1">
																	{{ $formatW(goodItem.saleNum) }}人付款
																	<span class="sty3" v-if="goodItem.supportTakeaway">外卖</span>
																	<span class="sty2" v-if="goodItem.supportMailing">快递</span>
																	<span class="sty1" v-if="goodItem.supportPickup">到店</span>
																</view>
																<view class="goodsitem-right-bottom">
																	<view class="left_super flex_column_start_start">
																		<view class="sell_price">
																			<text class="unit">¥</text>
																			<text class="price_int">{{ $getPartNumber(goodItem.goodsPrice, 'int') }}</text>
																			<text class="price_decimal">{{ $getPartNumber(goodItem.goodsPrice, 'decimal') }}</text>
																		</view>
																		<view class="super_original_price" v-if="goodItem.marketPrice">
																			<text>{{ $L('¥') }}</text>
																			<text>{{ $getPartNumber(goodItem.marketPrice, 'int') }}</text>
																			<text>{{ $getPartNumber(goodItem.marketPrice, 'decimal') }}</text>
																		</view>
																	</view>
																	<!-- <span>￥{{ goodItem.goodsPrice }}</span> -->
																	<image
																		v-if="showOnly != 1 && isBusinessTime"
																		:src="imgUrl + 'add.png'"
																		mode=""
																		@tap.stop="showSpcPop(goodItem)"
																	></image>
																</view>
															</view>
														</view>
													</template>
													<view class="no_content" v-else>
														<image :src="imgUrl + 'store/no_content.png'"></image>
														<text>暂无商品~</text>
													</view>
												</view>
											</view>
										</view>
										<!-- <view class="is_more">{{catgoods_hasmore ? $L('数据加载中...') : $L('数据加载完毕~')}}</view> -->
									</block>
									<!-- 无商品 -->
									<!-- 	<block>
								<view class="no_content">
									<image :src="imgUrl + 'store/no_content.png'"></image>
									<text>{{$L('暂无任何商品')}}~</text>
								</view>
							</block> -->
								</view>
								<!-- 商品上新 -->
								<view class="new_products" v-if="new_products">
									<block v-if="new_productList && new_productList.length">
										<!-- 商品列表 -->
										<view class>
											<view v-for="(item, index) in new_productList" :key="index" class="new_products_pre">
												<view class="new_products_top">
													<text class="line"></text>
													<view class="new_products_title">
														<image :src="imgUrl + 'store/shijian_title.png'"></image>
														<text>{{ item.onLineTime }}{{ $L('上新') }}</text>
													</view>
													<text class="line"></text>
												</view>
												<view class="all_commodities_list">
													<view
														v-for="(item1, index2) in item.goodsVOList"
														:key="index2"
														class="list_pre"
														@tap="goods_detail(item1.productId, item1.goodsId)"
													>
														<view class="pre_img">
															<!-- <image :src="item1.mainImage" mode='aspectFit'></image> -->
															<!-- <coverImage :src="item1.mainImage" width="345" height="345"></coverImage> -->
															<view class="image" :style="'background-image:url(' + item1.mainImage + ')'"></view>
														</view>
														<view class="pre_des">
															<view class="des_name">{{ item1.goodsName }}</view>
															<view class="des_info">{{ item1.goodsBrief }}</view>
															<!-- <view class="time_limited_discount">限时折扣</view> -->
															<!-- <view v-for="(item2, index) in item1.limited_activities" :key="index" class="discount">
														<view class="time_limited_discount" :style="'background:linear-gradient(to right,' + item2.color[0] + ',' + item2.color[1] + ');'">{{item2.name}}</view>
													</view> -->
															<view class="des_desc">
																<view class="commodity_price">
																	<text>{{ $L('￥') }}</text>
																	<text>{{ $getPartNumber(item1.goodsPrice, 'int') }}</text>
																	<text>{{ $getPartNumber(item1.goodsPrice, 'decimal') }}</text>
																</view>
																<text class="salenum" v-if="item1.goodsPrice < 999999.99">
																	{{ item1.actualSales + item1.virtualSales }}{{ $L('人已付款') }}
																</text>
															</view>
														</view>
													</view>
												</view>
											</view>
										</view>
										<view class="is_more" style="margin: 10rpx 0 30rpx">{{ hasmore ? $L('数据加载中...') : $L('数据加载完毕~') }}</view>
									</block>
									<block v-if="no_content_newProductList">
										<view class="no_content">
											<image :src="imgUrl + 'store/no_content.png'"></image>
											<text>{{ $L('暂无商品上新') }}~</text>
										</view>
									</block>
								</view>
								<!-- 优惠券 -->
								<view class="coupon" v-if="coupon">
									<block v-if="couponList && couponList.length">
										<!-- 优惠券列表 -->
										<view class>
											<view v-for="(item, index) in couponList" class="coupon_pre" :key="item.id">
												<view class="conpon_des" :style="'background-image:url(' + (imgUrl + 'store/coupon_bg.png') + '); background-size:100%'">
													<view class="conpon_des_top">
														<!-- <view class="{{item.receivedNum == item.limitReceive ? 'coupon_des_left1' : item.have == 0 ? 'coupon_des_left' : 'coupon_des_left1'}}">{{item.redinfo_money}}<text class="yuan">元</text></view> -->
														<view
															class="coupon_des_left"
															:style="item.publishValue && item.publishValue.length >= 6 ? 'word-break:break-word' : ''"
															v-if="item.couponType == 1"
														>
															{{ item.publishValue }}
															<text class="yuan">{{ $L('元') }}</text>
														</view>
														<view
															class="coupon_des_left"
															:style="item.publishValue && item.publishValue.length >= 6 ? 'word-break:break-word' : ''"
															v-else-if="item.couponType == 2"
														>
															{{ item.publishValue }}
															<text class="yuan">{{ $L('折') }}</text>
														</view>
														<view
															class="coupon_des_left"
															:style="item.publishValue && item.publishValue.length >= 6 ? 'word-break:break-word' : ''"
															v-else-if="item.couponType == 3"
														>
															{{ item.randomMax }}
															<text class="yuan">{{ $L('元') }}</text>
														</view>
														<!-- <view v-else-if="item.have < item.red_rach_max" class="coupon_des_left">{{item.limitQuota}}<text class="yuan">元</text></view> -->
														<!-- <view v-else class="coupon_des_left1">{{item.limitQuota}}<text class="yuan">元</text></view> -->
														<view class="coupon_des_con">
															<!-- {{item.receivedNum != item.limitReceive && item.have == 0}} -->
															<view
																class="progress-box"
																style="display: flex"
																v-if="item.receivedNum != item.publishNum && item.have < item.red_rach_max"
															>
																<progress
																	:percent="item.per"
																	backgroundColor="#FFFFFF"
																	activeColor="#FF0000"
																	font-size="22rpx"
																	border-radius="5rpx"
																	stroke-width="3"
																	style="
																		width: 60%;
																		height: 25%;
																		border: 1rpx solid #ff0000;
																		border-right: 2rpx solid #ff0000;
																		border-radius: 5rpx;
																	"
																></progress>
																<text class="progress_text">{{ $L('已抢') }}{{ item.per }}%</text>
															</view>
															<view class="full_reduction">{{ item.couponName }}</view>
															<view class="validity">{{ $L('有效期') }}：{{ item.publishStartTime }}-{{ item.publishEndTime }}</view>
														</view>
														<!-- <view class="coupon_des_right {{item.receivedNum == item.limitReceive ? 'coupon_des_right1' : item.have == 0 ? '' : 'coupon_des_right1'}}" bindtap="receive" data-redid="{{item.id}}">
												<text>{{item.receivedNum == item.limitReceive ? '已抢完' : item.have < item.red_rach_max ? '立即领取' : '已领取'}}</text>
												<text>{{item.have == 0 && item.receivedNum != item.limitReceive ? '已领取' : item.receivedNum == item.limitReceive ? '已抢完' : item.have < item.red_rach_max ? '立即领取' : '' }}</text>
											</view> -->
														<view class="coupon_des_right1" v-if="item.isReceive == 3">
															<text>{{ $L('已抢完') }}</text>
														</view>
														<view class="coupon_des_right" @tap="receive" :data-couponid="item.couponId" v-if="item.isReceive == 1">
															<text>{{ $L('立即领取') }}</text>
														</view>
														<view class="coupon_des_right1" v-if="item.isReceive == 2">
															<text>{{ $L('已领取') }}</text>
														</view>
													</view>
													<view class="conpon_des_bot">
														<view class="conpon_show" @tap="handleOpen">
															<view :class="'text ' + (conpon_show ? 'text2' : 'text1')">
																<text>{{ $L('使用规则') }}</text>
																：{{ item.useTypeValue }}
															</view>
														</view>
													</view>
												</view>
											</view>
										</view>
										<view class="is_more" style="margin-top: 20rpx">{{ hasmore ? $L('数据加载中...') : $L('数据加载完毕~') }}</view>
									</block>
									<block v-if="no_content_coupon">
										<view class="no_content">
											<image :src="imgUrl + 'no_coupon.png'"></image>
											<text>{{ $L('暂无优惠券') }}~</text>
										</view>
									</block>
								</view>
								<!-- 商家 -->
								<view class="business" v-if="business">
									<view class="location_box flex_column_start_start">
										<view class="lb_left flex_row_start_end" @click="tonavigation">
											<view class="lb_address">{{ vender_detail.areaInfo }}{{ vender_detail.address }}</view>
											<view class="lb_right flex_column_start_center" v-if="vender_detail.poiId">
												<image class="icon" mode="widthFix" :src="imgUrl + 'address.png'" />
												<!-- <text>导航</text> -->
											</view>
										</view>
										<view class="lb_left">
											<view class="lb_time">
												<image :src="imgUrl + 'business/clock.png'" mode=""></image>
												<text>{{ vender_detail.daysText }}</text>
											</view>
										</view>
									</view>
									<view class="business_info">
										<view class="business_time flex_row_between_center">
											<span>开店时间</span>
											<span style="color: #9a9a9a">{{ vender_detail.createTime }}</span>
										</view>
										<view class="business_time flex_row_between_center">
											<span>店铺电话</span>
											<span style="color: #9a9a9a">{{ vender_detail.servicePhone || '--' }}</span>
										</view>
										<view class="business_goods">
											<view>主营商品</view>
											<view class="business_intro_con">
												{{ vender_detail.mainBusiness ? vender_detail.mainBusiness : '--' }}
											</view>
										</view>
										<view class="business_intro" v-if="vender_detail.intro">
											<jyfParser :html="vender_detail.intro" style="width: 100%; overflow: hidden; flex-shrink: 0" :isAll="true"></jyfParser>
										</view>
									</view>
								</view>
							</view>
							<!-- 分类 -->
							<view class="fenlei" v-if="fenlei">
								<view>
									<view class="fenlei_lists" v-if="classifyList && classifyList.length > 0">
										<view v-for="(item, index) in classifyList" :key="index" class="fenlei_pre">
											<view class="fenlei_pre_top" :data-stc_id="item.innerLabelId" :data-stc_name="item.innerLabelName" @tap="handleProClas">
												<view class="fenlei_pre_title">{{ item.innerLabelName }}</view>
												<image :src="imgUrl + 'shop/to_right.png'" mode="aspectFit"></image>
											</view>
											<view class="fenlei_list" v-if="item && item.children && item.children.length > 0">
												<text
													v-for="(item1, index2) in item.children"
													:key="index2"
													:data-stc_id="item1.innerLabelId"
													:data-stc_name="item1.innerLabelName"
													@tap="handleProClas"
												>
													{{ item1.innerLabelName }}
												</text>
											</view>
										</view>
									</view>
									<view class="fenlei_lists" v-if="no_content_fenlei">
										<view class="no_fenlei">
											<image :src="imgUrl + 'store/no_product.png'"></image>
											<text>{{ $L('暂无商品分类') }}</text>
										</view>
									</view>
								</view>
								<!-- 推荐商品 -->
								<view class="all_commodities_list all_commodities_list1" v-if="recommend_productList && recommend_productList.length">
									<view class="commodities_list">
										<view v-for="(item, index) in recommend_productList" :key="index" class="list_pre" :data-gid="item.gid" @tap="goods_detail">
											<view class="pre_img">
												<image :src="item.goods_image_url" mode="aspectFit"></image>
											</view>
											<view class="pre_des">
												<view class="des_name">{{ item.goods_name }}</view>
												<view class="des_info">{{ item.goods_jingle }}</view>
												<!-- <view class="time_limited_discount">限时折扣</view> -->
												<view v-for="(item1, index2) in item.limited_activities" :key="index2" class="discount">
													<view
														class="time_limited_discount"
														:style="'background:linear-gradient(to right,' + item1.color[0] + ',' + item1.color[1] + ');'"
													>
														{{ item1.name }}
													</view>
												</view>
												<view class="des_desc">
													<view class="commodity_price">
														<text>{{ $L('￥') }}</text>
														<text>{{ filters.toSplit(item.goods_price)[0] }}</text>
														.
														<text>{{ filters.toSplit(item.goods_price)[1] }}</text>
													</view>
													<text class="salenum" v-if="item.goods_price < 999999.99">{{ item.goods_salenum }}{{ $L('人已付款') }}</text>
												</view>
											</view>
										</view>
									</view>
									<view class="is_more">{{ hasmore ? $L('数据加载中...') : $L('数据加载完毕~') }}</view>
								</view>
							</view>
						</view>
						<!-- 店铺不存在 -->
						<view class="empty_sort_page" v-if="!shop_open">
							<image :src="imgUrl + 'empty_goods.png'" mode="aspectFit" class="empty_img"></image>
							<view class="empty_text">{{ $L('店铺不存在或已关闭') }}~</view>
						</view>
					</view>
					<view class="action-section" v-if="showOnly != 1 && isBusinessTime">
						<view class="section-left">
							<view class="section-left-img" @click="createOrder">
								<image :src="imgUrl + 'cart.png'" mode="aspectFit"></image>
								<span class="section-left-tips">{{ cartNum }}</span>
							</view>
							<view class="price_wrap">
								<text class="unit">¥</text>
								<text class="price_int">{{ $getPartNumber(cartPrice, 'int') }}</text>
								<text class="price_decimal">{{ $getPartNumber(cartPrice, 'decimal') }}</text>
							</view>
						</view>
						<button class="no-border confirm-btn flex_row_center_center" @click="createOrder">
							{{ $L('结算') }}
						</button>
					</view>
					<!-- 底部tab栏 -->
					<!-- <view class="tabbar" v-if="shop_open" :style="'bottom:'+bottomSateArea"> -->
					<view class="tabbar" v-if="false" :style="'bottom:' + bottomSateArea">
						<view v-for="(item, idx) in tabBarList" :key="idx" class="tabbar_pre" @tap="hanbleTab" :data-idx="idx">
							<!-- <image :src="idx == tabcurrentTabIndex || index == 0 && tabisSelect ? item.selImgUrl : item.imgUrl">
					</image> -->

							<svgGroup
								:type="item.imgUrl"
								width="50"
								height="50"
								px="rpx"
								:color="idx == tabcurrentTabIndex || (index == 0 && tabisSelect) ? diyStyle_var['--color_main'] : '#333333'"
							></svgGroup>

							<text :class="idx == tabcurrentTabIndex || (index == 0 && tabisSelect) ? 'pre_sel' : ''">{{ item.name }}</text>
						</view>
						<view class="tabbar_pre" @tap="tokefu" :data-idx="idx">
							<image :src="imgUrl + 'store/chat_service.png'"></image>
							<text class="">{{ $L('客服') }}</text>
						</view>
					</view>

					<!-- 分享 -->
					<view class="select-wrap" catchtouchmove="touchmoveshare" v-if="shareWrap2">
						<view class="share-mode">
							<!-- app-1-start -->
							<!-- #ifdef APP-PLUS -->
							<view class="ul">
								<view @tap.stop="sldShare(0, 'WXSceneSession')" class="item">
									<image :src="imgUrl + 'svideo/wx_share.png'" mode="widthFix"></image>
									<text>{{ $L('微信好友') }}</text>
								</view>
								<view @tap.stop="sldShare(0, 'WXSenceTimeline')" class="item">
									<image :src="imgUrl + 'svideo/pyq_share.png'" mode="widthFix"></image>
									<text>{{ $L('微信朋友圈') }}</text>
								</view>
							</view>
							<!-- #endif -->
							<!-- app-1-end -->
							<!-- #ifdef H5 -->
							<view class="is_h5_share" v-if="!iswx">
								<view class="share-img" @tap="prevImg">
									<block v-if="iswx">
										<image class="poster_image" :src="share_img_h5" mode="widthFix"></image>
									</block>
									<block v-else>
										<canvas class="poster_image">
											<img :src="share_img_h5" width="100%" height="100%" style="-webkit-touch-callout: default" />
										</canvas>
									</block>
								</view>
								<image :src="imgUrl + 'store/fx_share_code.png'" mode="aspectFit" class="h5_share_tips"></image>
							</view>
							<view class="is_h5_public_share" v-if="iswx" @tap="closeShare">
								<image :src="imgUrl + 'wx_share_tip.png'" mode="widthFix" class="wx_share_img"></image>
							</view>

							<view class="close" @tap="closeShare" v-if="!iswx">
								<image :src="imgUrl + 'store/share_close2.png'"></image>
							</view>
							<!-- #endif -->
							<!-- #ifndef H5 -->
							<view class="close" @tap="closeShare">
								<image :src="imgUrl + 'store/share_close2.png'"></image>
							</view>
							<!-- #endif -->
						</view>
					</view>

					<!-- 底部站位栏 -->
					<view class="iphone_view" :style="'height:' + bottomSateArea"></view>

					<!-- 店铺首页开屏框 start -->
					<view
						:class="storeIsCookie == true ? 'open_screen show-dialog' : 'open_screen hide-dialog'"
						v-if="home_page_img && storeIsCookie == true && home_page_img.length > 0 && home_page_img[0].imgUrl"
					>
						<view class="open_screen_con" @click="gotoGoods_detail('store')">
							<view class="con_img" @click.stop="close_storeOpenScreen">
								<image :src="openscnImg"></image>
							</view>
							<image
								class="open_screen_con_img"
								:src="home_page_img[0].imgUrl"
								:style="{
									width: store_width + 'rpx',
									height: store_height + 'rpx'
								}"
								mode="aspectFit"
							></image>
						</view>
					</view>
					<!-- 开屏框 end -->
					<loginPop ref="loginPop" />
					<AddToCart ref="addtoCart" :storeId="vid" @getPreCartNum="getCartNum()" />
				</view>
			</view>
		</view>
	</view>
</template>
<script>
import filters from '../../utils/filter.js';
import jyfParser from '@/components/jyf-parser/jyf-parser';
import { openNavigator, checkTimeRange } from '@/utils/base.js';
import HomeDeco from '@/components/home_deco.vue';
import AddToCart from './addToCart.vue';
let imgUrl = getApp().globalData.imgUrl; //引用全局变量，图片域名
// 加载js
//引用全局变量，图片域名
// 加载js
import { getSceneParam, isWeiXinBrower } from '../../utils/common';
import { mapState, mapMutations } from 'vuex';

export default {
	data() {
		return {
			isBusinessTime: false, //是否在营业时间
			cartNum: 0,
			cartPrice: 0,
			curInnerLabelId: '', // 当前选择的商品分类
			cat_goodsList: [],
			catgoods_hasmore: true,
			cat_goodscurrent: 1,
			//店铺首页改版需求
			// #ifdef MP
			menuButtonHeight: uni.getMenuButtonBoundingClientRect().height + 'px',
			menuButtonTop: uni.getMenuButtonBoundingClientRect().top + 'px',
			menuButtonleft: uni.getMenuButtonBoundingClientRect().left + 'px',
			menuButtonHeights: uni.getMenuButtonBoundingClientRect().top + uni.getMenuButtonBoundingClientRect().height - 1,
			// #endif
			// index: '',
			bid: '',
			imgurl: getApp().globalData.imgUrl,
			vid: '',
			store_list: [],
			searchVal: '', //搜索框内的值
			searchList: [],
			show: false,
			searchShow: false,
			currentTabIndex: 0, //选项卡，默认综合排序
			isSelect: false,
			navList: [
				{
					name: this.$L('店铺首页'),
					imgUrl: imgUrl + 'store/qiyeyunicon.png',
					selImgUrl: imgUrl + 'store/qiyeyunicon_sel.png'
				},
				{
					name: this.$L('全部商品'),
					imgUrl: imgUrl + 'store/shangpin.png',
					selImgUrl: imgUrl + 'store/shangpin_sel.png'
				},
				{
					name: this.$L('商品上新'),
					imgUrl: imgUrl + 'store/iconfont37newtag.png',
					selImgUrl: imgUrl + 'store/shangxin_sel.png'
				},
				{
					name: this.$L('优惠券'),
					imgUrl: imgUrl + 'store/youhuiquan1.png',
					selImgUrl: imgUrl + 'store/youhuiquan.png'
				},
				{
					name: this.$L('商家'),
					imgUrl: imgUrl + 'store/youhuiquan1.png',
					selImgUrl: imgUrl + 'store/youhuiquan.png'
				}
			],
			navList1: [
				{
					name: this.$L('全部商品'),
					imgUrl: imgUrl + 'store/shangpin.png',
					selImgUrl: imgUrl + 'store/shangpin_sel.png'
				},
				{
					name: this.$L('商品上新'),
					imgUrl: imgUrl + 'store/iconfont37newtag.png',
					selImgUrl: imgUrl + 'store/shangxin_sel.png'
				},
				{
					name: this.$L('优惠券'),
					imgUrl: imgUrl + 'store/youhuiquan1.png',
					selImgUrl: imgUrl + 'store/youhuiquan.png'
				},
				{
					name: this.$L('商家'),
					imgUrl: imgUrl + 'store/youhuiquan1.png',
					selImgUrl: imgUrl + 'store/youhuiquan.png'
				}
			],
			tabBarList: [
				{
					name: this.$L('首页'),
					imgUrl: 'store_index'
				},
				{
					name: this.$L('商品'),
					imgUrl: 'store_goods'
				},
				{
					name: this.$L('分类'),
					imgUrl: 'store_category'
				}
			],
			showOnly: 1, //店铺是否为只展示
			is_favorites: '0', //是否被收藏
			vender_detail: {}, //店铺详情
			home_decoration: true, //店铺首页
			productList: [], //全部商品列表
			current: '1', //默认第一页
			all_commodities: false, //全部商品是否显示
			commodNavIdx: '0', //商品列表nav默认第一项
			isAscendingOrder: 1, //是否升序
			grid_list: true, //是否是两列grid_list布局
			new_products: false, //全部上新
			new_productList: [], //全部上新列表
			coupon: false, //优惠券
			couponList: [], //优惠券列表
			conpon_show: false, //优惠券规则展开
			business: false, //商家
			business_info: {}, //商家信息
			tabcurrentTabIndex: 0, //默认选中首页
			tabisSelect: false, //tabbar的状态
			homePage: true, //首页显示
			fenlei: false, //分类
			classifyList: [], //店铺分类
			isReceive: false, //优惠券是否已领取
			decorationData: [], //店铺装修数据
			imgUrl: getApp().globalData.imgUrl, //图片地址
			no_more: false, //没有更多数据了
			share_img: '', //点击右上角的分享，分享店铺图片
			share_name: '', //点击右上角分享，分享店铺的名称
			recommend_product: {}, //推荐商品
			recommend_productList: [], //推荐商品列表
			no_recomment_goods: false, //店铺无推荐商品
			no_content_decor: false, //店铺装修无数据
			no_content_productList: false, //店铺无商品列表数据
			no_content_newProductList: false, //店铺无上新列表数据
			no_content_coupon: false, //店铺无优惠券数据
			no_content_fenlei: false, //店铺无分类数据
			store_banner: '', //店铺首页banner图
			hasmore: true, //有无更多
			tips_show: false, //三点分享更多按钮
			tips: [
				{
					tips_img: imgUrl + 'store/shouye.png',
					tips_name: this.$L('首页'),
					tips_link: '/pages/index/index',
					type: 'switchTab'
				},
				{
					tips_img: imgUrl + 'store/leimupinleifenleileibie1.png',
					tips_name: this.$L('分类'),
					tips_link: '/pages/category/category',
					type: 'switchTab'
				},
				// {
				// 	tips_img: imgUrl + "store/shape.png",
				// 	tips_name: this.$L('分享'),
				// 	tips_link: '',
				// 	type: 'share'
				// },
				{
					tips_img: imgUrl + 'store/cart.png',
					tips_name: this.$L('购物车'),
					tips_link: '/pages/cart/cart',
					type: 'switchTab'
				},
				{
					tips_img: imgUrl + 'store/gerenzhongxin.png',
					tips_name: this.$L('个人中心'),
					tips_link: '/pages/user/user',
					type: 'switchTab'
				}
			],
			transparent_mask: false,
			key: '',
			index: -1,
			scrollTopH: 0,
			bottomSateArea: getApp().globalData.bottomSateArea, //iphone手机底部一条黑线的高度
			shareWrap2: false, // 展示分享弹层
			iswx: false, //是否微信浏览器
			share_img_h5: '',
			pageSize: 10,
			client: '',
			deco_data: [], //首页装修数据
			home_is_show_top_cat: false, //是否显示顶部分类，默认显示
			home_page_img: [],
			width: '',
			height: '',
			is_show_top: false,
			shop_open: true,
			is_load_home_dec: false,
			idx: '',
			filters,
			storeIndex: -1, //从店铺列表页进入获取列表下标
			store_width: 0,
			store_height: 0,
			storeIsCookie: '',
			openscnImg: getApp().globalData.imgUrl + 'index/close_screen.png',
			storeCatList: []
		};
	},
	components: {
		jyfParser,
		HomeDeco,
		AddToCart
	},
	props: {},
	onShow() {
		this.venderDetail(); //店铺详情
		this.iswx = this.$isWeiXinBrower();
	},
	computed: {
		...mapState(['userCenterData', 'hasLogin'])
	},
	async onLoad(options) {
		if (this.$Route.query.scene) {
			let url = decodeURIComponent(this.$Route.query.scene);
			this.vid = url;
		} else {
			this.vid = this.$Route.query.vid;
		}
		// 员工超市限制
		if (this.vid == 530002 && this.userCenterData?.empStoreId != 530002) {
			// 重定向至首页
			// this.$Router.replaceAll({
			// 	path: "/pages/index/index",
			// });
		}
		this.$sldStatEvent({
			behaviorType: 'spv',
			storeId: this.vid
		});
		await this.getShopHome(); //获取店铺首页装修
		this.iswx = this.$isWeiXinBrower();
		if (options && options.good_list) {
			if (this.no_content_decor) {
				this.tabItemTap(0);
			} else {
				this.tabItemTap(1);
			}
		}

		this.judgeScreenShow();
		this.getStoreCat();
		if (this.$Route.query.innerLabelId) this.curInnerLabelId = this.$Route.query.innerLabelId;
		if (this.$Route.query.goodsId) {
			this.pageSize = 1000;
			this.getCatGoodsList().then((res) => {
				const goodsId = this.$Route.query.goodsId;
				const scrollItem = document.getElementById(goodsId);
				// console.log(scrollItem);
				if (scrollItem) scrollItem.scrollIntoView();
			});
		} else {
			this.pageSize = 10;
			this.getCatGoodsList();
		}
		this.getCartNum();
	},

	// 加载更多
	onReachBottom(e) {
		let { all_commodities, new_products, coupon, fenlei, hasmore, catgoods_hasmore } = this;
		if (hasmore) {
			if (all_commodities) {
				this.getProductList(); //全部商品列表
			} else if (new_products) {
				this.getNewProductList(); //获取商品上新数据
			} else if (coupon) {
				this.getCouponList(); //获取优惠券列表
			} else if (fenlei) {
				this.getShopClassify(); //获取店铺分类

				// this.getRecommendProductList();
			}
		}
		if (catgoods_hasmore) {
			this.getCatGoodsList();
		}
	},

	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage: function (options) {
		return {
			title: this.vender_detail.storeName,
			path: `/standard/store/shopHomePage?vid=${this.$Route.query.vid}`,
			imageUrl: this.vender_detail.storeLogoUrl
		};
	},
	/**
	 * 用户点击右上角分享
	 */
	onShareTimeline: function (options) {
		return {
			title: this.vender_detail.storeName,
			query: this.vender_detail.shareLink.split('?')[1],
			imageUrl: this.vender_detail.storeLogoUrl
		};
	},
	methods: {
		...mapMutations(['saveChatBaseInfo']),
		showSpcPop(item) {
			this.$refs.addtoCart.showSpcPop(item);
		},
		//获取刷新购物车数据
		getCartNum(vid) {
			if (this.hasLogin) {
				let param = {};
				param.url = 'v3/business/front/cart/cartNum';
				param.method = 'GET';
				param.data = { storeId: vid ? vid : this.vid };
				// param.data.key = this.userInfo.access_token;
				this.$request(param)
					.then((res) => {
						if (res.state == 200) {
							this.cartNum = res.data;
						} else {
							this.$api.msg(res.msg);
						}
					})
					.catch((e) => {
						//异常处理
					});
				this.$request({
					url: 'v3/business/front/cart/cartList',
					method: 'GET',
					data: {
						storeId: vid ? vid : this.vid
					}
				}).then((res) => {
					if (res.state == 200) {
						this.cartPrice = res.data.totalAmount;
					} else {
						this.$api.msg(res.msg);
						uni.hideLoading();
					}
				});
			} else {
				this.getNoLoginCartNum();
			}
		},
		//获取未登录，购物车数量
		getNoLoginCartNum() {
			this.cartNum = 0;
			this.cartPrice = 0;
			let cart_list = uni.getStorageSync('cart_list_Pickup');
			if (cart_list && cart_list.storeCartGroupList) {
				cart_list.storeCartGroupList.map((item) => {
					if (item.storeId == this.vid) {
						item.promotionCartGroupList.map((item1) => {
							item1.cartList.map((item2) => {
								this.cartNum++;
								this.cartPrice += item2.buyNum * item2.productPrice;
							});
						});
					}
				});
			}
			let cart_list2 = uni.getStorageSync('cart_list_Email');
			if (cart_list2 && cart_list2.storeCartGroupList) {
				cart_list2.storeCartGroupList.map((item) => {
					if (item.storeId == this.vid) {
						item.promotionCartGroupList.map((item1) => {
							item1.cartList.map((item2) => {
								this.cartNum++;
								this.cartPrice += item2.buyNum * item2.productPrice;
							});
						});
					}
				});
			}
		},
		//进入商品详情页
		goGoodsDetail(goods_info) {
			this.$Router.push({
				path: '/standard/product/detail',
				query: {
					productId: goods_info.defaultProductId,
					goodsId: goods_info.goodsId
				}
			});
		},
		createOrder() {
			uni.switchTab({
				url: '/pages/cart/cart'
			});
		},
		// 获取全部商品列表
		getCatGoodsList() {
			return new Promise((resolve, reject) => {
				let that = this;
				let param = {};
				param.data = {};
				param.url = 'v3/goods/front/goods/goodsList';
				param.data.current = that.cat_goodscurrent;
				param.data.pageSize = this.pageSize;
				param.data.sort = 0;
				param.data.storeId = that.vid;
				if (that.curInnerLabelId) {
					param.data.storeInnerLabelId = that.curInnerLabelId;
				}
				that.$request(param).then((res) => {
					if (res.state == 200) {
						if (res.data.list.length == 0) {
							if (that.cat_goodscurrent == 1) {
								that.cat_goodsList = res.data.list;
							}
						} else {
							if (that.cat_goodscurrent == 1) {
								that.cat_goodsList = res.data.list;
							} else {
								that.cat_goodsList = that.cat_goodsList.concat(res.data.list);
							}
							let hasmore = this.$checkPaginationHasMore(res.data.pagination); //是否还有数据
							if (hasmore) {
								that.cat_goodscurrent++;
							}
							that.catgoods_hasmore = hasmore;
						}
						resolve();
					} else {
						resolve();
					}
				});
			});
		},
		clickLeftCat(item) {
			this.curInnerLabelId = item.innerLabelId;
			this.cat_goodscurrent = 1;
			this.catgoods_hasmore = true;
			this.getCatGoodsList();
		},
		// 获取店铺分类
		getStoreCat() {
			let that = this,
				vid = that.vid;
			let param = {};
			param.data = {};
			param.data.pageSize = 100
			param.data.storeId = vid;
			param.url = 'v3/seller/front/store/storeCategory';
			this.$request(param).then((res) => {
				if (res.state == 200) {
					this.storeCatList = res.data;
				} else {
					this.$api.msg(res.msg);
				}
			});
		},
		go_back() {
			this.$Router.back(1);
		},

		judgeScreenShow() {
			if (this.home_page_img != undefined) {
				if (this.home_page_img.length > 0) {
					let storeIsCookie = uni.getStorageSync('storeIsCookie' + this.vid);
					if (!storeIsCookie) {
						this.storeIsCookie = true;
						uni.setStorage({
							key: 'storeIsCookie' + this.vid,
							data: new Date().getTime()
						});
					} else {
						if (new Date().getTime() * 1 - storeIsCookie * 1 > 24 * 60 * 60 * 1000) {
							this.storeIsCookie = true;
							uni.setStorage({
								key: 'storeIsCookie' + this.vid,
								data: new Date().getTime()
							});
						} else {
							this.storeIsCookie = false;
						}
					}
				} else {
					uni.removeStorage({
						key: 'storeIsCookie' + this.vid,
						success: function (res) {}
					});
				}
			}
		},

		tokefu() {
			if (!this.hasLogin) {
				this.$refs.loginPop.openLogin();
				return;
			}

			let chatBaseInfo = {};
			chatBaseInfo.memberId = this.userCenterData.memberId;
			chatBaseInfo.memberName = this.userCenterData.memberName;
			chatBaseInfo.memberNickName = this.userCenterData.memberNickName;
			chatBaseInfo.memberAvatar = this.userCenterData.memberAvatar;
			chatBaseInfo.storeId = this.vender_detail.storeId;
			chatBaseInfo.storeLogo = this.vender_detail.storeLogoUrl;
			chatBaseInfo.storeName = this.vender_detail.storeName;
			chatBaseInfo.source = 'store';
			chatBaseInfo.showData = {};
			this.saveChatBaseInfo(chatBaseInfo);
			this.$Router.push({
				path: '/standard/chat/detail',
				query: {
					vid: this.vid
				}
			});
		},

		//三点更多分享
		moreTips() {
			let { tips_show } = this;
			(this.tips_show = !tips_show), (this.transparent_mask = true);
		},

		//隐藏透明遮罩层
		hideMask() {
			(this.transparent_mask = false), (this.tips_show = false);
		},

		//三点更多分享
		moreTips() {
			let { tips_show } = this;
			(this.tips_show = !tips_show), (this.transparent_mask = true);
		},

		//隐藏透明遮罩层
		hideMask() {
			(this.transparent_mask = false), (this.tips_show = false);
		},

		//三点分享链接
		handleLink(e) {
			let link = e.currentTarget.dataset.link;
			let type = e.currentTarget.dataset.type;

			if (type != 'share') {
				this.$Router.pushTab(link);
			} else {
				//点击了分享
				// #ifndef MP-WEIXIN
				this.shareWrap2 = true;
				this.$weiXinBrowerShare(1, {
					title: this.vender_detail.storeName,
					desc: this.$L('刚刚看到一个不错的店铺，快来看看~'),
					link: this.vender_detail.shareLink,
					imgUrl: this.vender_detail.storeLogoUrl
				});
				// #endif
			}
			this.tips_show = false;
		},
		//分享 type：分享类型 0 图文 2图片，scene 场景 WXSceneSession：分享朋友  WXSenceTimeline：分享朋友圈
		sldShare: function (type, scene) {
			let shareData = {};
			if (type == 0) {
				shareData.href = this.vender_detail.shareLink;
				shareData.title = this.vender_detail.storeName;
				shareData.summary = this.$L('刚刚看到一个不错的店铺，快来看看~');
				shareData.imageUrl = this.vender_detail.storeLogoUrl;
			} else if (type == 2) {
				shareData.imageUrl = this.storeLogoUrl;
			}
			this.$weiXinAppShare(type, scene, shareData);
			this.closeShare(); //关闭分享
		},

		closeShare() {
			this.shareWrap2 = false;
		},

		downloadImg() {
			let { shareImg } = this;
			let _this = this;
			wx.downloadFile({
				url: shareImg,
				success: (res) => {
					if (res.statusCode == 200) {
						wx.saveImageToPhotosAlbum({
							filePath: res.tempFilePath,
							success: (result) => {
								if (result.errMsg == 'saveImageToPhotosAlbum:ok') {
									wx.showToast({
										title: _this.$L('已保存到本地')
									});
									this.closeShare();
								} else {
									wx.showToast({
										title: _this.$L('保存失败'),
										icon: 'none'
									});
								}
							}
						});
					} else {
						wx.showToast({
							title: _this.$L('下载失败'),
							icon: 'none'
						});
					}
				}
			});
		},

		prevImg() {
			wx.previewImage({
				urls: [this.share_img_h5]
			});
		},

		//获取滚动条的当前位置
		onPageScroll: function (e) {
			this.tips_show = false;
			this.scrollTopH = e.scrollTop;

			// if (e.scrollTop > 170) {
			if (e.scrollTop > 140) {
				this.searchShow = true;
			} else if (this.fenlei && e.scrollTop > 80) {
				this.searchShow = true;
			} else {
				this.searchShow = false;
			}
		},

		//点击去搜索
		goSearch() {
			this.$Router.push({
				path: '/standard/store/productSearch',
				query: {
					vid: this.vid
				}
			});
		},

		//清空搜索输入框内容
		cancel() {
			this.searchVal = '';
			this.searchShow = false;
			(this.searchVal = ''), (this.searchShow = false);
			this.getProductList();
		},

		//去商品介绍页
		handleStoreIntroduction(e) {
			let vid = e.currentTarget.dataset.vid;
			this.$Router.push({
				path: '/standard/store/storeIntroduction',
				query: {
					vid
				}
			});
		},

		//获取店铺首页装修
		async getShopHome() {
			// #ifdef H5
			this.client = 'h5';
			// #endif

			//app-2-start
			// #ifdef APP-PLUS
			switch (uni.getDeviceInfo().platform) {
				case 'android':
					this.client = 'android';
					break;
				case 'ios':
					this.client = 'ios';
					break;
				default:
					break;
			}
			// #endif
			//app-2-end

			// #ifdef MP
			this.client = 'weixinXcx';
			// #endif
			let that = this;
			let { vid } = that;

			if (vid) {
				let param = {};
				param.data = {};
				param.data.storeId = vid;
				param.data.os = this.client;
				param.url = 'v3/system/front/deco/storeIndex';
				await this.$request(param)
					.then((res) => {
						this.is_load_home_dec = true;
						if (res.state == 200) {
							if (res.data.data != '' && res.data.data) {
								this.deco_data = JSON.parse(res.data.data);
								this.no_content_decor = false;
							} else {
								// this.tabItemTap(0)
								this.no_content_decor = true;
								this.deco_data = null;
								this.all_commodities = true;
								this.getProductList();
							}
							if (res.data.showTip != null) {
								this.home_page_img = JSON.parse(res.data.showTip);
								const { windowWidth, windowHeight } = uni.getSystemInfoSync();
								this.store_width = this.home_page_img[0].width || windowWidth * 0.75 * 1.8;
								this.store_height = this.home_page_img[0].height || windowHeight * 0.56 * 1.8;
							}
						}
					})
					.catch((e) => {
						//异常处理
					});
			}
		},

		// 获取全部商品列表
		getProductList() {
			// let that = this
			// let commodNavIdx = that.commodNavIdx
			// let isAscendingOrder = that.isAscendingOrder
			// let param = {}
			// param.data = {}
			// param.url = 'v3/goods/front/goods/goodsList'
			// if (that.searchVal) {
			// 	param.data.keyword = that.searchVal;
			// }
			// // isAscendingOrder
			// param.data.current = that.current
			// param.data.pageSize = this.pageSize
			// if (commodNavIdx == 2) {
			// 	if (isAscendingOrder == 1) {
			// 		param.data.sort = 3
			// 	} else {
			// 		param.data.sort = 4
			// 	}
			// } else {
			// 	param.data.sort = commodNavIdx
			// }
			// param.data.storeId = that.vid
			// that.$request(param).then(res => {
			// 	if (res.state == 200) {
			// 		if (res.data.list.length == 0) {
			// 			that.no_content_productList = true
			// 		} else {
			// 			if (that.current == 1) {
			// 				that.productList = res.data.list
			// 			} else {
			// 				that.productList = that.productList.concat(res.data.list);
			// 			}
			// 			let hasmore = this.$checkPaginationHasMore(res.data.pagination); //是否还有数据
			// 			if (hasmore) {
			// 				that.current++;
			// 			}
			// 			that.hasmore = hasmore
			// 		}
			// 	}
			// })
		},

		// 获取商品上新列表
		getNewProductList() {
			let that = this;
			let param = {};
			param.data = {};
			param.url = 'v3/goods/front/goods/newGoods';
			param.data.pageSize = that.pageSize;
			param.data.current = that.current;
			param.data.storeId = that.vid;
			if (this.searchVal) {
				data.keyword = this.searchVal;
			}
			that.$request(param).then((res) => {
				if (res.state == 200) {
					if (res.data.list.length == 0) {
						that.no_content_newProductList = true;
					} else {
						if (that.current == 1) {
							that.new_productList_tmp = res.data.list;
						} else {
							that.new_productList_tmp = that.new_productList_tmp.concat(res.data.list);
						}
						this.sortDate();
						let hasmore = this.$checkPaginationHasMore(res.data.pagination); //是否还有数据
						if (hasmore) {
							that.current++;
						}
						that.hasmore = hasmore;
					}
				} else {
					this.$api.msg(res.msg);
				}
			});
		},

		sortDate() {
			let rawList = this.new_productList_tmp;
			const sortDateObject = {};
			for (let index in rawList) {
				const onLineTime = rawList[index].onlineTime.split(' ')[0];
				sortDateObject[onLineTime] = sortDateObject[onLineTime] || [];
				sortDateObject[onLineTime].push(rawList[index]);
			}
			this.new_productList = Object.keys(sortDateObject).map((key) => ({
				onLineTime: key,
				goodsVOList: sortDateObject[key]
			}));
		},

		//优惠券列表
		getCouponList(pageIndex) {
			let that = this;
			let param = {};
			param.data = {};
			param.url = 'v3/promotion/front/coupon/storeCouponList';
			param.data.pageSize = 5;
			if (pageIndex >= 0) {
				that.current = pageIndex;
			}
			param.data.current = that.current;
			param.data.storeId = that.vid;
			that.$request(param).then((res) => {
				if (res.state == 200) {
					if (res.data.list.length == 0) {
						that.no_content_coupon = true;
					} else {
						if (res.data.list.length > 0) {
							// 处理优惠券价格返回类型为有小数点的字符串，去除小数点值为0的
							res.data.list.forEach((item) => {
								if (item.publishValue && item.publishValue.indexOf('.') != -1) {
									item.publishValue = item.publishValue.split('.')[1] - 0 > 0 ? item.publishValue : this.$getPartNumber(item.publishValue, 'int');
								}
							});
						}
						if (that.current == 1) {
							that.couponList = res.data.list;
						} else if (pageIndex >= 0) {
							//领取优惠券刷新当前页面
							res.data.list.forEach((item, index) => {
								that.couponList[pageIndex * 3 + index] = item;
							});
						} else {
							that.couponList = that.couponList.concat(res.data.list);
						}
						let hasmore = this.$checkPaginationHasMore(res.data.pagination); //是否还有数据
						if (hasmore) {
							that.current++;
						}
						that.hasmore = hasmore;
					}
				} else {
					this.$api.msg(res.msg);
				}
			});
		},

		//商户信息
		getBusinessInfo(pageIndex) {},

		//领取优惠券
		receive(e) {
			if (!this.hasLogin) {
				// this.$api.msg('请登录')
				this.$refs.loginPop.openLogin();
				return;
			}
			let that = this;
			let param = {};
			param.data = {};
			param.url = 'v3/promotion/front/coupon/receiveCoupon';
			param.data.couponId = e.currentTarget.dataset.couponid;
			that.$request(param).then((res) => {
				if (res.state == 200) {
					uni.showToast({
						title: that.$L('领取成功'),
						icon: 'none',
						duration: 1500 //持续的时间
					});
					let pageIndex = that.hasmore ? that.current - 1 : that.current;
					that.getCouponList(pageIndex);
				} else {
					this.$api.msg(res.msg);
				}
			});
		},

		//优惠券规则展开
		handleOpen() {
			this.conpon_show = !this.conpon_show;
		},

		//选项卡切换
		tabItemTap(idx) {
			//记录上次点击的对象的序号
			var oldidx = this.currentTabIndex; //记录当前点击的对象的序号

			var currentIdx = idx;
			this.hasmore = true;

			if (oldidx == currentIdx) {
				var isSelect = this.isSelect;
				this.currentTabIndex = currentIdx;
				this.isSelect = !isSelect;
			} else {
				this.currentTabIndex = currentIdx;
				this.isSelect = true;

				if (this.no_content_decor) {
					//无装修数据时：不显示装修模块，从全部商品开始显示
					if (currentIdx == 0) {
						this.home_decoration = true;
						this.homePage = true;
						this.coupon = false;
						this.business = false;
						this.all_commodities = this.no_content_decor ? true : false;
						this.new_products = false;
						this.fenlei = false;
						this.currentTabIndex = '0';
						this.current = '1';
						this.getProductList(); //全部商品列表
					}

					if (currentIdx == 1) {
						this.home_decoration = false;
						this.new_products = true;
						this.all_commodities = false;
						this.coupon = false;
						this.business = false;
						this.current = '1';
						this.getNewProductList(); //获取商品上新数据
					} else if (currentIdx == 2) {
						this.home_decoration = false;
						this.coupon = true;
						this.business = false;
						this.all_commodities = false;
						this.new_products = false;
						this.current = '1';
						this.getCouponList(); //获取优惠券列表
					} else if (currentIdx == 3) {
						this.home_decoration = false;
						this.coupon = false;
						this.business = true;
						this.all_commodities = false;
						this.new_products = false;
						this.current = '1';
						this.getBusinessInfo(); //获取商家信息
					} else {
						this.home_decoration = true;
						this.all_commodities = true;
						this.new_products = false;
						this.coupon = false;
						this.business = false;
						this.current = '1';
					}
				} else {
					//有装修数据时，默认显示装修模块
					if (currentIdx == 0) {
						this.home_decoration = true;
						this.new_products = false;
						this.all_commodities = false;
						this.coupon = false;
						this.business = false;
						this.current = '1';
					}

					if (currentIdx == 1) {
						this.home_decoration = false;
						this.all_commodities = true;
						this.new_products = false;
						this.coupon = false;
						this.business = false;
						this.current = '1';
						this.getProductList(); //全部商品列表
					} else if (currentIdx == 2) {
						this.home_decoration = false;
						this.new_products = true;
						this.all_commodities = false;
						this.coupon = false;
						this.business = false;
						this.current = '1';
						this.getNewProductList(); //获取商品上新数据
					} else if (currentIdx == 3) {
						this.home_decoration = false;
						this.coupon = true;
						this.business = false;
						this.all_commodities = false;
						this.new_products = false;
						this.current = '1';
						this.getCouponList(); //获取优惠券列表
					} else if (currentIdx == 4) {
						this.home_decoration = false;
						this.coupon = false;
						this.business = true;
						this.all_commodities = false;
						this.new_products = false;
						this.current = '1';
						this.getBusinessInfo(); //获取商家信息
					} else {
						this.home_decoration = true;
						this.all_commodities = false;
						this.new_products = false;
						this.coupon = false;
						this.current = '1';
					}
				}
			}

			if (currentIdx == 4) {
			}
		},

		//底部tabbar的切换
		hanbleTab(e) {
			this.hideMask();
			this.current = 1;
			//切换时回到页面的顶部
			if (uni.pageScrollTo) {
				//判断这个方法是否可用
				uni.pageScrollTo({
					scrollTop: 0
				});
			} else {
				uni.showModal({
					title: this.$L('提示'),
					content: this.$L('当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。')
				});
			}

			this.hasmore = true;
			//记录上次点击的对象的序号

			var oldidx = this.tabcurrentTabIndex; //记录当前点击的对象的序号

			var currentIdx = e.currentTarget.dataset.idx;

			if (oldidx == currentIdx) {
				var tabisSelect = this.tabisSelect;
				this.tabcurrentTabIndex = currentIdx;
				this.tabisSelect = !tabisSelect;
			} else {
				if (currentIdx != 3) {
					this.tabcurrentTabIndex = currentIdx;
					this.tabisSelect = true;
				}

				if (currentIdx == 0) {
					this.home_decoration = true;
					this.homePage = true;
					this.coupon = false;
					this.business = false;
					this.all_commodities = this.no_content_decor ? true : false;
					this.new_products = false;
					this.fenlei = false;
					this.currentTabIndex = '0';
					this.current = '1';
				} else if (currentIdx == 1) {
					this.home_decoration = false;
					this.homePage = false;
					this.coupon = false;
					this.business = false;
					this.all_commodities = true;
					this.new_products = false;
					this.fenlei = false;
					this.current = '1';
					this.getProductList(); //全部商品列表
				} else if (currentIdx == 2) {
					this.homePage = true;
					this.coupon = false;
					this.business = false;
					this.all_commodities = false;
					this.new_products = false;
					this.fenlei = true;
					this.getShopClassify(); //获取店铺分类
				} else {
					// this.getRecommendProductList(); //获取店铺推荐商品
					this.homePage = false;
					this.coupon = false;
					this.business = false;
					this.all_commodities = false;
					this.new_products = false;
					this.fenlei = false;
				}
			}
		},

		//商品列表tab切换
		commoditiesNav(e) {
			this.hasmore = true; //切换时回到页面的顶部

			if (uni.pageScrollTo) {
				//判断这个方法是否可用
				uni.pageScrollTo({
					scrollTop: 0
				});
			} else {
				uni.showModal({
					title: this.$L('提示'),
					content: this.$L('当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。')
				});
			}

			let that = this,
				commodNavIdx = e.currentTarget.dataset.index;
			that.commodNavIdx = commodNavIdx;
			that.current = 1;

			if (commodNavIdx == 2) {
				that.isAscendingOrder = that.isAscendingOrder == 1 ? 0 : 1;
			}
			that.getProductList();
		},

		// 获取店铺分类
		getShopClassify() {
			let that = this,
				vid = that.vid;
			let param = {};
			param.data = {};
			param.data.storeId = vid;
			param.url = 'v3/seller/front/store/storeCategory';
			this.$request(param)
				.then((res) => {
					if (res.state == 200) {
						if (res.data.length == 0) {
							this.no_content_fenlei = true;
						} else {
							//如果是初次加载，直接赋值，否则数据追加
							if (that.current == '1') {
								let classifyList = res.data;

								this.classifyList = classifyList;
							} else {
								let classifyList = res.data;
								this.classifyList = that.classifyList.concat(classifyList);
							}
						}
					} else {
						this.$api.msg(res.msg);
					}
				})
				.catch((e) => {
					//异常处理
				});
		},

		//去商品分类列表
		handleProClas(e) {
			let stc_id = e.currentTarget.dataset.stc_id; // let stc_name = e.currentTarget.dataset.stc_name;
			this.$Router.push({
				path: '/standard/store/productSearch',
				query: {
					storeInnerLabelId: stc_id,
					vid: this.vid
				}
			});
		},

		//布局切换
		layoutSwitch() {
			this.grid_list = !this.grid_list;
		},

		// 商品详情
		goods_detail(productId, goodsId) {
			this.$Router.push({
				path: '/standard/product/detail',
				query: {
					productId,
					goodsId
				}
			});
		},

		// 商铺首页详情
		venderDetail(e) {
			let that = this;
			let { vid } = that;

			if (vid) {
				let param = {};
				param.data = {};
				param.data.storeId = vid;
				param.url = 'v3/seller/front/store/detail';
				this.$request(param)
					.then((res) => {
						if (res.state == 200) {
							if (this.storeIndex >= 0) {
								res.data.followNumber = this.$Route.query.follow;
							}
							let vender_detail = res.data;
							this.showOnly = vender_detail.showOnly;
							this.isBusinessTime = checkTimeRange(vender_detail.days, vender_detail.startTime, vender_detail.endTime);
							this.vender_detail = vender_detail;
							this.share_img = vender_detail.storeLogoUrl;
							this.share_name = vender_detail.storeName;
							this.store_banner = vender_detail.storeBackdropUrl;
							this.share_img_h5 = 'data:image/png;base64,' + vender_detail.storeQRCode;
							this.is_favorites = vender_detail.isFollow;
							this.$weiXinBrowerShare(1, {
								title: this.vender_detail.storeName,
								desc: that.$L('刚刚看到一个不错的店铺，快来看看~'),
								link: this.vender_detail.shareLink,
								imgUrl: this.vender_detail.storeLogoUrl
							});
						} else if (res.state == 257) {
							this.shop_open = false;
						} else {
							this.$api.msg(res.msg);
						}
					})
					.catch((e) => {
						//异常处理
					});
			}
		},

		//进入店铺首页
		handleShopHomePage() {
			this.$Router.push('/pages/shopHomePage/shopHomePage');
		},

		// 收藏-取消收藏
		collect(e) {
			if (!this.hasLogin) {
				// this.$api.msg('请登录')
				this.$refs.loginPop.openLogin();
				return;
			}

			let that = this;
			let { vid, is_favorites } = that;
			let param = {};
			param.data = {};
			param.data.storeIds = vid;
			param.data.isCollect = is_favorites == 'false' ? 'true' : 'false';
			param.url = 'v3/member/front/followStore/edit';
			param.method = 'POST';
			this.$request(param).then((res) => {
				if (res.state == 200) {
					this.$api.msg(res.msg);
					this.is_favorites = this.is_favorites == 'false' ? 'true' : 'false';
					if (this.storeIndex != -1) {
						uni.$emit('uploadCollect', {
							index: this.storeIndex,
							state: is_favorites == 'false'
						});
					}
					this.vender_detail.followNumber = is_favorites == 'false' ? Number(this.vender_detail.followNumber) + 1 : Number(this.vender_detail.followNumber) - 1;
					if (this.is_favorites) {
						this.$sldStatEvent({
							behaviorType: 'fol',
							storeId: vid
						}); //统计埋点
					}
				} else {
					this.$api.msg(res.msg);
				}
			});
		},

		//获取搜索框内的值
		searchInput(e) {
			this.searchVal = e.detail.value;
			this.getProductList();
		},

		//搜索
		search() {
			this.getProductList();
		},

		getmore() {
			let that = this;

			if (!that.flag && that.search_hasmore) {
				that.search();
			}
		},

		back() {
			this.searchList = [];
			this.show = false;
			this.searchPn = 1;
			this.search_hasmore = true;
		},

		//关闭首页广告屏
		close_storeOpenScreen() {
			this.storeIsCookie = false;
		},

		//点击广告屏跳转到详情页面
		gotoGoods_detail(type) {
			if (type == 'home') {
				this.isCookie = false;
				let osValue = this.home_page_img[0];

				this.$diyNavTo(osValue, 'home');
			} else {
				this.storeIsCookie = false;
				let osValue = this.home_page_img[0];

				this.$diyNavTo(osValue, this.store_id);
			}
		},
		// 跳转到地图导航
		tonavigation() {
			let { vender_detail } = this;
			if (vender_detail.poiId) {
				openNavigator(vender_detail.poiId);
			}
			// uni.openLocation({
			// 	latitude: Number(vender_detail.lat), //目标纬度
			// 	longitude: Number(vender_detail.lat), //目标经度
			// 	name: vender_detail.areaInfo, //名称
			// 	address: vender_detail.address, //地址
			// 	success: function () {
			// 		console.log('success');
			// 	}
			// });
		}
	}
};
</script>
<style lang="scss">
/* pages/shopHomePage/shopHomePage.wxss */
.pt_88 {
	padding-top: 88rpx;
}

page {
	width: 100%;
	height: 100%;
	background-color: #f5f5f5;
	position: relative;
	margin: 0 auto;
}

.noShow {
	display: none !important;
}

.left_super {
	.sell_price {
		color: var(--color_price);
		font-weight: bold;

		.unit {
			font-size: 24rpx;
			font-weight: bold !important;
		}

		.price_int {
			font-size: 30rpx;
			margin-left: 4rpx;
			font-weight: bold !important;
		}

		.price_decimal {
			font-size: 24rpx;
			font-weight: bold !important;
		}
	}

	.super_original_price {
		font-size: 20rpx;
		font-family: PingFang SC;
		font-weight: 500;
		text-decoration: line-through;
		color: #666;
	}
}

.open_screen {
	width: 750rpx;
	height: calc(100vh);
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	margin: 0 auto;
	background-color: rgba(0, 0, 0, 0.3);
	z-index: 99999;

	.open_screen_con {
		maring: 0 auto;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		border-radius: 15rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		.open_screen_con_img {
			max-width: 580rpx !important;
			max-height: 776rpx !important;
			background-size: contain;
			border-radius: 15rpx;
		}

		.con_img {
			width: 58rpx;
			height: 58rpx;
			position: absolute;
			top: -58rpx;
			right: -58rpx;

			image {
				width: 100%;
				height: 100%;
			}
		}
	}
}

.shop_homepage {
	width: 750rpx;
	background: #f5f5f5;
	height: 100%;
	// position: absolute;
	margin: 0 auto;
}

/* 透明遮罩层 */
.transparent_mask {
	width: 100%;
	height: 100%;
	position: fixed;
	background-color: #ffffff;
	top: 0;
	left: 0;
	opacity: 0;
	z-index: 10;
}

.content2 {
	width: 100%;
	/* height: 754rpx; */
	/* background: black; */
	background-size: 100%;

	.container3 {
		padding-bottom: 60rpx;
	}
}

/* 搜索框 */
.search {
	width: 750rpx;
	height: 88rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 35rpx;
	box-sizing: border-box;
	position: fixed;
	/* #ifndef APP-PLUS */
	top: 0;
	/* #endif */
	//app-3-start
	/* #ifdef APP-PLUS */
	top: var(--status-bar-height);
	/* #endif */
	//app-3-end
	z-index: 10;
}

.search_input {
	width: 77%;
	height: 65rpx;
	display: flex;
	border: 2rpx solid transparent;
	align-items: center;
	padding-left: 20rpx;
	background: rgba(248, 248, 248, 1);
	border-radius: 33px;

	.iconsousuo1 {
		margin-right: 10rpx;
		// color: var(--color_main);
	}
}

.search_input1 {
	width: 78%;
	height: 65rpx;
	background: rgba(255, 255, 255, 1);
	border: 2rpx solid var(--color_main);
	border-radius: 33rpx;
}

.search_input image {
	width: 30rpx;
	height: 30rpx;
	margin-right: 15rpx;
}

.search_input input {
	font-size: 28rpx !important;
}

.search_input .cancel {
	width: 47rpx;
	height: 47rpx;
}

.search view {
	font-size: 28rpx;
	font-family: PingFang SC;
	font-weight: 400;
	color: rgba(148, 148, 148, 1);
}

/* 三点更多分享 */
.more_tips {
	position: relative;
}

.more {
	width: 50rpx;
	height: 50rpx;
}

.triangle-up {
	position: absolute;
	right: 15rpx;
	width: 0;
	height: 0;
	border-left: 15rpx solid transparent;
	border-right: 15rpx solid transparent;
	border-bottom: 20rpx solid #fcfcfc;
	/* transform: rotate(120deg); */
	transform: rotate(0deg);
	box-shadow: -2rpx 2rpx -1rpx 0rpx rgba(102, 102, 102, 0.1);
	z-index: 21;
}

.tips {
	position: absolute;
	z-index: 20;
	top: 77rpx;
	right: -15rpx;
	width: 226rpx;
	background: rgba(255, 255, 255, 1);
	box-shadow: 0px 0px 10rpx 0px rgba(102, 102, 102, 0.2);
	opacity: 0.94;
	border-radius: 15rpx;
	display: flex;
	flex-direction: column;
}

.tips_pre {
	width: 100%;
	height: 88rpx;
	display: flex;
	align-items: center;
	border-bottom: #e6e6e6;
	padding-left: 20rpx;
	box-sizing: border-box;
}

button::after {
	border: none;
}

button[plain] {
	border: none;
}

.tips_pre image {
	width: 50rpx;
	height: 50rpx;
	margin-right: 8rpx;
}

.tips_pre text {
	font-size: 26rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: rgba(51, 51, 51, 1);
	line-height: 32rpx;
}

/* 搜索框 */
.search1 {
	width: 750rpx;
	height: 88rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 35rpx;
	box-sizing: border-box;
	position: fixed;
	//app-4-start
	/* #ifdef APP-PLUS */
	top: var(--status-bar-height);
	/* #endif */
	//app-4-end
	/* #ifndef APP-PLUS */
	top: 0;
	/* #endif */
	z-index: 10;
	background: #ffffff;
}

.search1 .search_input {
	width: 77%;
	height: 65rpx;
	border: 2rpx solid var(--color_main);
	border-radius: 33rpx;
	display: flex;
	align-items: center;
	padding-left: 20rpx;
	background: #ffffff;
}

.search1 .search_input image {
	width: 30rpx;
	height: 30rpx;
	margin-right: 15rpx;
}

.search1 .search_input .cancel {
	width: 47rpx;
	height: 47rpx;
}

.search1 input {
	width: 440rpx;
	font-size: 24rpx;
	font-family: PingFang SC;
	font-weight: 400;
	color: rgba(148, 148, 148, 1);
	line-height: 32rpx;
}

.search1 .search_text {
	font-size: 30rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: rgba(45, 45, 45, 1);
	line-height: 32rpx;
}

/* 店铺详情 */
.shop_des {
	width: 100%;
	color: rgba(255, 255, 255, 1);
	/* #ifndef APP-PLUS */
	padding-top: 88rpx;
	/* #endif */
	//app-5-start
	/* #ifdef APP-PLUS */
	padding-top: calc(var(--status-bar-height) + 88rpx);
	/* #endif */
	//app-5-end
	box-sizing: border-box;
	background-size: 100%;
}

.des_top {
	width: 100%;
	/* height: 449rpx; */
	height: 246rpx;
}

.shop_info {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 31rpx 20rpx 39rpx;
	box-sizing: border-box;
}

.info_left {
	width: 100%;
	display: flex;
	align-items: center;
}

.info_left .avat {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	margin-right: 16rpx;
}

.info_des {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: space-between;
}

.info_top {
	display: flex;
	align-items: center;

	.storeScale {
		.tag {
			font-size: 18rpx !important;
			color: #fff;
			height: 40rpx;
			border-radius: 20rpx;
			width: 60rpx;
			text-align: center;
			line-height: 40rpx;
			display: block;
		}

		.brand_1 {
			background-color: #ff5733;
		}

		.brand_2 {
			background-color: #d4aa61;
		}

		.brand_3 {
			background-color: #40c1ce;
		}
	}
}

.info_top text:nth-of-type(1) {
	font-size: 30rpx;
	font-family: PingFang SC;
	font-weight: bold;
	color: rgba(255, 255, 255, 1);
	line-height: 32rpx;
}

.info_top image {
	width: 40rpx;
	height: 40rpx;
	margin-right: 14rpx;
}

.info_top text:nth-of-type(2) {
	width: 60rpx;
	height: 30rpx;
	background: var(--color_main);
	border-radius: 15rpx;
	font-size: 24rpx;
	font-family: PingFang SC;
	font-weight: 400;
	color: rgba(255, 255, 255, 1);
	line-height: 30rpx;
	text-align: center;
}

.info_bot {
	display: flex;
	align-items: center;
	margin-top: 13rpx;
}

.info_bot image {
	width: 40rpx;
	height: 40rpx;
	margin-right: 8rpx;
}

.info_bot text {
	font-size: 24rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: rgba(255, 255, 255, 1);
	line-height: 32rpx;
}

.info_right {
	width: 110rpx;
	height: 50rpx;
	background: var(--color_main_bg);
	border-radius: 25rpx;
	font-size: 26rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: rgba(255, 255, 255, 1);
	display: flex;
	align-items: center;
	justify-content: center;
}

.info_right1 {
	width: 110rpx;
	height: 50rpx;
	border: 4rpx solid rgba(255, 255, 255, 1);
	box-shadow: 0rpx 3rpx 8rpx 0rpx rgba(252, 28, 28, 0.2);
	border-radius: 25rpx;
	font-size: 20rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: rgba(255, 255, 255, 1);
	line-height: 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.select_nav {
	width: 100%;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.nav_item {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;

	text {
		font-size: 30rpx;
	}

	.selImgUrl {
		position: relative;
		font-weight: bold;
		font-size: 32rpx;

		&::after {
			content: '';
			width: 56rpx;
			background: #fff;
			border-radius: 2rpx;
			height: 4rpx;
			position: absolute;
			bottom: -15rpx;
			left: 50%;
			transform: translateX(-50%);
		}
	}
}

.nav_item image {
	width: 64rpx;
	height: 64rpx;
	margin-bottom: 10rpx;
}

.nav_item text {
	font-size: 30rpx;
	font-family: PingFang SC;
	font-weight: 400;
	color: rgba(255, 255, 255, 1);
	line-height: 32rpx;
}

.des_con {
	/* margin-top: -155rpx; */
	width: 750rpx;
	/* height: calc(100vh - 449rpx); */
	border-radius: 15rpx;
	position: absolute;
	z-index: 5;
	background: #f5f5f5;
}

.des_con1 {
	margin-top: 0;
	width: 100%;
	/* height: calc(100vh - 88rpx); */
	background: rgba(255, 255, 255, 1);
	border-radius: 15rpx;
	position: absolute;
	z-index: 5;
}

.all_commodities {
	width: 100%;
	box-sizing: border-box;
	// padding-bottom: 98rpx;
	padding-bottom: 160rpx;
	box-sizing: border-box;
	border-radius: 15rpx 15rpx 0 0;
	background: #f5f5f5;

	&.paddding-none {
		padding-bottom: 0;
	}
}

.all_commodities_nav {
	width: 750rpx;
	height: 90rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-left: 40rpx;
	box-sizing: border-box;
	background: #ffffff;
	border-radius: 15rpx 15rpx 0 0;
}

.nav1 {
	position: fixed;
	//app-6-start
	/* #ifdef APP-PLUS */
	top: calc(var(--status-bar-height) + 88rpx);
	/* #endif */
	//app-6-end
	/* #ifndef APP-PLUS */
	top: 88rpx;
	/* #endif */
	z-index: 5;
}

.comprehensive {
	display: flex;
	align-items: center;
}

.comprehensive .pre_title {
	font-size: 28rpx;
	font-family: PingFang SC;
	color: rgba(45, 45, 45, 1);
	line-height: 32rpx;
}

.all_commodities_nav .active {
	font-size: 30rpx;
	font-family: PingFang SC;
	font-weight: bold;
	color: var(--color_main);
	line-height: 32rpx;
}

.comprehensive image {
	width: 14rpx;
	height: 9rpx;
	margin-left: 10rpx;
}

.sales_volume {
	font-size: 28rpx;
	font-family: PingFang SC;
	color: rgba(45, 45, 45, 1);
	line-height: 32rpx;
	display: flex;
	align-items: center;
}

.price {
	display: flex;
}

.price .pre_title {
	font-size: 28rpx;
	font-family: PingFang SC;
	color: rgba(45, 45, 45, 1);
	line-height: 32rpx;
}

.price .active {
	font-size: 30rpx;
	font-family: PingFang SC;
	font-weight: bold;
	color: var(--color_main);
	line-height: 32rpx;
}

.price_switch {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	margin-left: 4rpx;

	.iconziyuan14 {
		font-size: 20rpx;
		color: #999;
		transform: scale(0.5);
		margin-bottom: -8rpx;

		&.rotate {
			transform: scale(0.5) rotate(180deg);
		}

		&.sel {
			color: var(--color_main);
		}
	}
}

.price_switch image {
	width: 14rpx;
	height: 9rpx;
}

.price_switch image:nth-child(1) {
	margin-bottom: 5rpx;
}

.sales_volume {
	font-size: 28rpx;
	font-family: PingFang SC;
	color: rgba(45, 45, 45, 1);
	line-height: 32rpx;
}

.layout {
	box-sizing: border-box;
	position: relative;
	padding: 0 26rpx 0 0;
	box-sizing: border-box;
}

.layout::before {
	content: '';
	width: 1rpx;
	height: 40rpx;
	position: absolute;
	top: 5rpx;
	left: -26rpx;
	background: rgba(187, 187, 187, 1);
}

.layout image {
	width: 50rpx;
	height: 50rpx;
}

/* 全部商品列表 */
/* list布局 */
.list {
	width: 100%;
	background: #f5f5f5;
	padding: 20rpx 20rpx 102rpx;
	box-sizing: border-box;
}

.list .list_pre {
	width: 100%;
	background: rgba(255, 255, 255, 1);
	border-radius: 15rpx;
	margin-right: 20rpx;
	margin-bottom: 20rpx;
	display: flex;
}

.list .pre_img {
	width: 294rpx;
	height: 294rpx;
	border-radius: 15rpx 0 0 15rpx;
	overflow: hidden;
}

.list .pre_img .image {
	background-position: center center;
	background-repeat: no-repeat;
	background-size: cover;
	width: 294rpx;
	height: 294rpx;
	border-radius: 15rpx 0 0 15rpx;
}

.list .pre_des {
	width: 394rpx;
}

.list .pre_des .time_limited_discount {
	/* width:106rpx; */
	padding: 0 11rpx;
	box-sizing: border-box;
	height: 30rpx;
	/* background:linear-gradient(45deg,rgba(255,108,0,1) 0%,rgba(255,192,83,1) 100%); */
	border-radius: 15rpx;
	font-size: 22rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: rgba(255, 255, 255, 1);
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 18rpx;
}

.list .list_pre .des_desc {
	margin-top: 43rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.list .list_pre .des_info {
	font-size: 24rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: rgba(102, 102, 102, 1);
	width: 310rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	height: 35rpx;
	margin-top: 20rpx;
}

.catshowgoods {
	background-color: #fff;
	color: #000000;
	display: flex;

	.catgoods-left {
		width: 184rpx;
		// height: 100%;
		// min-height: calc(100vh - 434rpx);
		background-color: #f5f5f5;

		.catgoods-left-item {
			height: 110rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 28rpx;
		}

		.active-item {
			background-color: #fff;
		}
	}

	.catgoods-right {
		flex: 1;
		// height: 100%;
		min-height: calc(100vh - 434rpx);
		padding-top: 40rpx;
		padding-left: 32rpx;

		.right-goodsitem {
			display: flex;
			padding-right: 28rpx;
			margin-bottom: 50rpx;

			.goodsitem-left {
				width: 160rpx;
				height: 160rpx;
				border-radius: 10rpx;
				margin-right: 18rpx;
			}

			.goodsitem-right {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				font-size: 26rpx;

				// padding: 10rpx 0;
				.goodsitem-right-tit {
					font-weight: bold;
					overflow: hidden;
					-webkit-line-clamp: 2;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					margin-bottom: 6rpx;
				}

				.goods-sales {
					color: #999999;
					font-size: 22rpx;
					font-family: PingFang SC;
					font-weight: 500;
					// margin-bottom: 18rpx;
					display: flex;
					align-items: center;

					span {
						height: 28rpx;
						text-align: center;
						line-height: 28rpx;
						margin-left: 8rpx;
						padding: 0 4rpx;
						color: #fff;
					}

					.sty1 {
						background-color: #00baad;
					}

					.sty2 {
						background-color: #2a82e4;
					}

					.sty3 {
						background-color: #ffc300;
					}
				}

				.goodsitem-right-desc {
					font-size: 20rpx;
					overflow: hidden;
					-webkit-line-clamp: 2;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-box-orient: vertical;
				}

				.goodsitem-right-bottom {
					display: flex;
					align-items: flex-end;
					justify-content: space-between;
					font-weight: bold;

					image {
						width: 42rpx;
						height: 42rpx;
						margin-right: 20rpx;
					}
				}
			}
		}
	}
}

/* grid网格布局 */
.all_commodities_list {
	background: #f5f5f5;
	padding: 20rpx 20rpx 0;
	box-sizing: border-box;
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	box-sizing: border-box;
	width: 750rpx;
}

.product1 {
	margin-top: 90rpx;
	padding-bottom: 4rpx !important;
}

.all_commodities_list .list_pre {
	width: 345rpx;
	/* height: 590rpx; */
	background: rgba(255, 255, 255, 1);
	border-radius: 15rpx;
	margin-right: 20rpx;
	margin-bottom: 20rpx;
	padding-bottom: 20rpx;
}

.all_commodities_list .list_pre:nth-of-type(2n) {
	margin-right: 0;
}

.all_commodities_list .list_pre .pre_img {
	width: 345rpx;
	height: 345rpx;
	border-radius: 15rpx 15rpx 0 0;
	overflow: hidden;
}

.all_commodities_list .list_pre .pre_img .image {
	background-position: center center;
	background-size: cover;
	background-repeat: no-repeat;
	width: 345rpx;
	height: 345rpx;
}

.all_commodities_list .pre_des {
	padding: 20rpx;
	height: 272rpx;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.list .list_pre .pre_des {
	padding: 20rpx 0 20rpx 20rpx;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.all_commodities_list .list_pre .des_name {
	width: 310rpx;
	font-size: 28rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: rgba(45, 45, 45, 1);
	text-overflow: -o-ellipsis-lastline;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	line-clamp: 2;
	-webkit-box-orient: vertical;
	font-weight: bold;
}

.list .list_pre .des_name {
	width: 374rpx;
	font-size: 28rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: rgba(45, 45, 45, 1);
	text-overflow: -o-ellipsis-lastline;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	line-clamp: 2;
	-webkit-box-orient: vertical;
	font-weight: bold;
}

.all_commodities_list .list_pre .des_info {
	font-size: 24rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: rgba(102, 102, 102, 1);
	width: 310rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	height: 39rpx;
	margin-top: 9rpx;
}

.list .list_pre .des_info {
	font-size: 24rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: rgba(102, 102, 102, 1);
	width: 374rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	height: 39rpx;
	margin-top: 9rpx;
}

.discount {
	display: flex;
}

.time_limited {
	width: 100%;
	display: flex;
	flex-wrap: wrap;
}

.list_pre .time_limited_discount {
	/* width:106rpx; */
	padding: 0 11rpx;
	box-sizing: border-box;
	height: 30rpx;
	/* background:linear-gradient(45deg,rgba(255,108,0,1) 0%,rgba(255,192,83,1) 100%); */
	border-radius: 15rpx;
	font-size: 22rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: rgba(255, 255, 255, 1);
	line-height: 39rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 15rpx;
}

.all_commodities_list .list_pre .des_desc {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 31rpx;
}

.list_pre .des_desc .commodity_price {
	font-size: 34rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: var(--color_price);
	font-weight: bold;
}

.commodity_price {
	font-size: 34rpx;
	font-family: PingFang SC;
	color: var(--color_price);
	line-height: 32rpx;
	font-weight: bold;
}

.commodity_price text:nth-child(1),
.commodity_price text:nth-last-child(1) {
	font-size: 22rpx;
}

.list_pre .des_desc .salenum {
	font-size: 22rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: rgba(153, 153, 153, 1);
}

/* 商品上新 */
.new_products {
	width: 100%;
	background: #f5f5f5;
	border-radius: 15rpx 15rpx 0 0;
	padding-bottom: 100rpx;
	box-sizing: border-box;
}

.new_products_pre {
	width: 100%;
	padding-top: 20rpx;
}

.new_products_pre:nth-of-type(1) {
	width: 100%;
	padding-top: 40rpx;
}

.new_products_top {
	display: flex;
	justify-content: center;
	align-items: center;
}

.new_products_top .line {
	width: 120rpx;
	height: 1rpx;
	background: rgba(187, 187, 187, 1);
}

.new_products_title {
	display: flex;
	align-items: center;
	margin: 0 10rpx;
}

.new_products_title image {
	width: 28rpx;
	height: 27rpx;
	margin-right: 9rpx;
}

.new_products_title text {
	font-size: 28rpx;
	font-family: PingFang SC;
	font-weight: bold;
	color: rgba(45, 45, 45, 1);
	line-height: 32rpx;
}

/* 商家 */
.business {
	width: 100%;
	padding: 20rpx 20rpx 160rpx;
	box-sizing: border-box;
	background: #f5f5f5;
	border-radius: 15rpx 15rpx 0 0;
	color: #000000;
	font-size: 28rpx;

	.location_box {
		row-gap: 30rpx;
		padding: 28rpx 28rpx 40rpx;
		background-color: #ffffff;
		border-radius: 6rpx;

		.lb_left {
			flex: 1;

			.lb_address {
				flex: 1;
			}

			.lb_time {
				display: flex;
				align-items: center;

				image {
					width: 24rpx;
					height: 24rpx;
					margin-right: 16rpx;
				}
			}
		}

		.lb_right {
			width: 100rpx;
			color: #397fd1;
			font-size: 24rpx;

			.icon {
				display: block;
				width: 40rpx;
				height: 40rpx;
				margin-bottom: 10rpx;
			}
		}
	}

	.business_info {
		margin-top: 18rpx;
		background-color: #fff;

		.business_time {
			height: 104rpx;
			padding: 0 16rpx 0 24rpx;
			border-bottom: 2rpx solid #efefef;
		}

		.business_goods {
			padding: 32rpx 24rpx;

			.business_intro_con {
				margin-top: 26rpx;
				color: #9a9a9a;
			}
		}

		.business_intro {
			padding: 32rpx 24rpx;
			border-top: 2rpx solid #efefef;
		}
	}
}

/* 优惠券 */
.coupon {
	width: 100%;
	padding: 20rpx 20rpx 160rpx;
	box-sizing: border-box;
	background: #f5f5f5;
	border-radius: 15rpx 15rpx 0 0;
}

.coupon_pre {
	width: 100%;
	width: 711rpx;
	margin-bottom: 20rpx;
	min-height: 257rpx;
}

.conpon_des {
	width: 100%;
	width: 711rpx;
	height: 257rpx;
}

.conpon_des_top {
	width: 711rpx;
	height: 195rpx;
	display: flex;
	align-items: center;
	margin: 0 10rpx;
}

.coupon_des_left {
	width: 161rpx;
	height: 195rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 48rpx;
	font-family: Adobe Heiti Std;
	font-weight: normal;
	color: var(--color_coupon_main);
	line-height: 45rpx;
	margin-left: 22rpx;
	text-align: center;
}

.coupon_des_left1 {
	width: 161rpx;
	height: 195rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 48rpx;
	font-family: Adobe Heiti Std;
	font-weight: normal;
	color: rgba(51, 51, 51, 1);
	line-height: 45rpx;
	margin-left: 18rpx;
}

.yuan {
	font-size: 24rpx;
	margin-left: 4rpx;
}

.coupon_des_con {
	width: 330rpx;
	display: flex;
	flex-direction: column;
	justify-content: center;
	margin-left: 27rpx;
}

.progress-box {
	align-items: center;
	/* height: 32rpx; */
}

.progress-box .progress_text {
	font-size: 22rpx;
	font-family: PingFang SC;
	font-weight: 500;
	font-style: italic;
	color: rgba(253, 51, 18, 1);
	line-height: 32rpx;
	margin-left: 20rpx;
}

.full_reduction {
	font-size: 28rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: rgba(51, 51, 51, 1);
	line-height: 45rpx;
}

.validity {
	font-size: 20rpx;
	font-family: PingFang SC;
	font-weight: 400;
	color: rgba(153, 153, 153, 1);
	line-height: 45rpx;
}

.coupon_des_right {
	display: flex;
	align-items: flex-start;
}

.coupon_des_right text {
	width: 140rpx;
	height: 50rpx;
	border-radius: 25px;
	background: var(--color_coupon_main_bg);
	font-size: 26rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: rgba(255, 255, 255, 1);
	line-height: 45rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.coupon_des_right1 text {
	width: 140rpx;
	height: 50rpx;
	background: rgba(153, 153, 153, 1);
	border-radius: 25rpx;
	font-size: 30rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: rgba(255, 255, 255, 1);
	line-height: 45rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.conpon_des_bot {
	width: 100%;
}

.conpon_show {
	display: flex;
	justify-content: space-between;
	padding: 20rpx;
	padding-left: 0;
	box-sizing: border-box;
}

.conpon_show .text {
	font-size: 22rpx;
	font-family: PingFang SC;
	font-weight: 400;
	color: rgba(51, 51, 51, 1);
	line-height: 30rpx;
	padding-left: 22rpx;
	width: 615rpx;
	height: auto;
	box-sizing: border-box;
	display: -webkit-box;
	word-break: break-all;
	-webkit-box-orient: vertical;
	/* 要显示多少行就改变line-clamp的数据,此处折叠起来显示一行*/
	-webkit-line-clamp: 1;
	overflow: hidden;
	text-overflow: ellipsis;
}

.conpon_show .text2 {
	display: -webkit-box;
}

.conpon_show .text1 {
	display: block;
}

.conpon_show image {
	width: 16rpx;
	height: 9rpx;
}

.conpon_rules {
	width: 100%;
	background: #ffffff;
	display: flex;
	flex-direction: column;
	padding-left: 22rpx;
	box-sizing: border-box;
}

.conpon_rules text {
	font-size: 20rpx;
	font-family: PingFang SC;
	font-weight: 400;
	color: rgba(51, 51, 51, 1);
	line-height: 30rpx;
}

/* 分类 */
.fenlei {
	width: 100%;
	/* height: 100%; */
	border-radius: 15rpx;
	position: absolute;
	left: 0;
	right: 0;
	z-index: 5;
}

.fenlei_lists {
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 100%;
	padding: 20rpx 0 30rpx 0;
	box-sizing: border-box;
	margin-top: -90rpx;
	background: #f5f5f5;
	padding-bottom: 100rpx;
	overflow: auto;
}

.fenlei_pre {
	width: 710rpx;
	background: rgba(255, 255, 255, 1);
	border-radius: 15rpx;
	padding: 17rpx 20rpx 0;
	box-sizing: border-box;
	margin-bottom: 20rpx;
}

.fenlei_pre_top {
	width: 100%;
	/* height: 71rpx; */
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 0 0 20rpx;
	box-sizing: border-box;
	position: relative;
	margin-bottom: 17rpx;
}

.fenlei_pre_title {
	font-size: 34rpx;
	font-family: PingFang SC;
	font-weight: bold;
	color: rgba(45, 45, 45, 1);
	box-sizing: border-box;
}

.fenlei_pre_title::before {
	position: absolute;
	top: 10rpx;
	left: 0rpx;
	bottom: 0;
	content: '';
	width: 4rpx;
	height: 30rpx;
	background: var(--color_main);
	border-radius: 2rpx;
}

.fenlei_pre_top image {
	width: 26rpx;
	height: 26rpx;
}

.fenlei_list {
	display: flex;
	background: #ffffff;
	width: 100%;
	margin-top: 21rpx;
	flex-wrap: wrap;
}

.fenlei_list text {
	font-size: 26rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: rgba(45, 45, 45, 1);
	width: 325rpx;
	height: 60rpx;
	line-height: 60rpx;
	background: rgba(242, 242, 242, 1);
	border-radius: 6rpx;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
	word-break: break-all;
	padding: 0 17rpx;
	box-sizing: border-box;
	margin-bottom: 20rpx;
	margin-right: 20rpx;
	text-align: center;
}

.fenlei_list text:nth-of-type(2n) {
	margin-right: 0;
}

.no_fenlei {
	box-sizing: border-box;
	margin-top: 200rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 126rpx;
}

.no_fenlei image {
	width: 238rpx;
	height: 171rpx;
}

.no_fenlei text {
	font-size: 24rpx;
	font-family: PingFang SC;
	font-weight: 400;
	color: rgba(51, 51, 51, 1);
	line-height: 24rpx;
	margin-top: 20rpx;
}

/* 推荐商品 */
.all_commodities_list1 {
	background: #f5f5f5;
	box-sizing: border-box;
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	padding: 0 0 100rpx;
	box-sizing: border-box;
}

.commodities_list {
	background: #f5f5f5;
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	padding: 0 20rpx 0 20rpx;
	box-sizing: border-box;
}

.no_more {
	width: 100%;
	display: flex;
	justify-content: center;
}

.action-section {
	position: fixed;
	bottom: 0;
	z-index: 10;
	width: 750rpx;
	// height: 100rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	box-sizing: border-box;
	background: rgba(255, 255, 255, 1);
	box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(86, 86, 86, 0.08);
	padding-top: 20rpx;
	padding-bottom: constant(safe-area-inset-bottom);
	/* 兼容 iOS < 11.2 */
	padding-bottom: env(safe-area-inset-bottom);

	/* 兼容 iOS >= 11.2 */
	.section-left {
		margin-left: 44rpx;
		display: flex;
		align-items: center;

		.section-left-img {
			position: relative;
			margin-right: 30rpx;

			image {
				width: 66rpx;
				height: 66rpx;
			}

			.section-left-tips {
				position: absolute;
				right: -4rpx;
				width: 26rpx;
				height: 26rpx;
				line-height: 26rpx;
				text-align: center;
				background-color: #ff1f0a;
				color: #fff;
				font-size: 24rpx;
				border-radius: 50%;
			}
		}
	}
}

.confirm-btn {
	width: 190rpx;
	height: 74rpx;
	background: var(--color_main_bg);
	border-radius: 40rpx;
	font-size: 28rpx;
	color: #fff;
	margin-right: 28rpx;
	margin-left: 0;

	.settle_num {
		font-size: 28rpx;
	}
}

.price_wrap {
	height: 50rpx;

	&.price_wrap_hide {
		width: 188rpx;
		white-space: nowrap;
		text-overflow: ellipsis;
		overflow: hidden;
	}

	&.price_wrap_super {
		.unit,
		.price_decimal,
		.price_int {
			color: #242846;
		}
	}

	.unit,
	.price_decimal {
		font-size: 24rpx;
		font-weight: 600;
		color: var(--color_price);
	}

	.price_int {
		font-weight: 600;
		font-size: 34rpx;
		color: var(--color_price);
	}

	.price_decimal {
		position: relative;

		.price_super_img {
			position: absolute;
			bottom: 4rpx;
			display: inline-block;
			width: 102rpx;
			height: 34rpx;
			line-height: 36rpx;
			color: #cfb295;
			font-size: 20rpx;
			font-family: PingFang SC;
			font-weight: 500;
			text-align: center;
			text-indent: 6rpx;
			background-position: center;
			background-repeat: no-repeat;
			background-size: cover;
			margin-left: 6rpx;
		}
	}
}

/* tabbar底栏 */
.tabbar {
	position: fixed;
	bottom: 0;
	z-index: 10;
	width: 750rpx;
	/*height: 98rpx+(constant(safe-area-inset-bottom)||env(safe-area-inset-bottom));*/
	display: flex;
	align-items: center;
	justify-content: space-around;
	box-sizing: border-box;
	background: rgba(255, 255, 255, 1);
	box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(86, 86, 86, 0.08);
	padding-bottom: constant(safe-area-inset-bottom);
	/* 兼容 iOS < 11.2 */
	padding-bottom: env(safe-area-inset-bottom);
	/* 兼容 iOS >= 11.2 */
}

.iphone_view {
	position: fixed;
	bottom: 0;
	z-index: 10;
	width: 100%;
	box-sizing: border-box;
	background: rgba(255, 255, 255, 1);
}

.tabbar_pre {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding-top: 6rpx;
	padding-bottom: 6rpx;
}

.tabbar_pre image {
	width: 50rpx;
	height: 50rpx;
}

.tabbar_pre text {
	font-size: 22rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: rgba(51, 51, 51, 1);
	line-height: 32rpx;
}

.tabbar_pre .pre_sel {
	color: var(--color_main);
}

/* 暂无数据 */
.no_data {
	font-size: 24rpx;
	color: black;
	text-align: center;
}

/* 店铺首页装修 */
.home_decoration {
	padding-bottom: 100rpx;
	box-sizing: border-box;
	border-radius: 15rpx 15rpx 0 0;
}

/* 无数据 */
.no_content {
	margin: 250rpx auto 0;
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.no_content image {
	width: 280rpx;
	height: 280rpx;
}

.no_content text {
	font-size: 24rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: rgba(51, 51, 51, 1);
	line-height: 32rpx;
	margin-top: 30rpx;
}

.no_coupon {
	width: 212rpx;
	height: 159rpx;
}

/* 加载更多，没有更多 */
.is_more {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 22rpx;
	color: #999999;
	line-height: 22rpx;
	margin: 10rpx 0 30rpx;
	background: #f5f5f5;
}

.fixed_top_status_bar {
	position: fixed;
	//app-7-start
	/* #ifdef APP-PLUS */
	height: var(--status-bar-height);
	/* #endif */
	//app-7-end
	/* #ifndef APP-PLUS */
	height: 0;
	/* #endif */
	top: 0;
	left: 0;
	right: 0;
	z-index: 99;
	background: #fff;
}

.search .top_w_b {
	height: 50rpx !important;
	width: 50rpx !important;
}

.top_w_b {
	height: 34rpx;
	width: 34rpx;
	margin: 0 0 0 20rpx;
	display: block;
}

/* #ifdef H5  */
.search {
	padding-left: 0;
}

.search1 {
	padding-left: 0;
}

/* #endif */

//app-8-start
/* #ifdef APP-PLUS  */
.top_w_b {
	margin-left: 0;
}

/* #endif */
//app-8-end

.select-wrap {
	position: fixed;
	top: 0;
	left: 0;
	width: 750rpx;
	height: 100vh;
	background-color: rgba(0, 0, 0, 0.45);
	z-index: 9999;
	right: 0;
	margin: 0 auto;
}

.select-wrap .share-mode {
	position: absolute;
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.select-wrap .share-mode .ul {
	display: flex;
	width: 100%;
	justify-content: space-evenly;
	position: fixed;
	bottom: 200rpx;
	z-index: 9999;
}

.share-mode .item {
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
	cursor: pointer;
	border: none;
	margin: 0;
	padding: 0;
	line-height: 1;
	background-color: transparent;
}

.share-mode .item::after {
	border: none;
}

.share-mode .item image {
	width: 106rpx;
	height: 0;
}

.share-mode .item text {
	color: #fff;
	font-size: 24rpx;
	margin-top: 30rpx;
}

.select-wrap .close {
	width: 750rpx;
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	position: fixed;
	bottom: 30rpx;
	z-index: 99999;
}

.select-wrap .close image {
	width: 30rpx;
	height: 30rpx;
	/* padding: 30rpx; */
}

.is_h5_public_share {
	margin-top: 100rpx;
}

.is_h5_share {
	width: 750rpx;
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}

.share-img {
	border-radius: 20%;
	width: 100%;
	display: flex;
	justify-content: center;
}

.poster_image {
	position: relative;
	display: inline-block;
	width: 640rpx;
	height: 640rpx;
	overflow: hidden;
}

.h5_share_tips {
	width: 400rpx;
	height: 100rpx;
	margin-top: 100rpx;
}

.wx_share_img {
	width: 500rpx;
	height: 500rpx;
	margin-left: 100rpx;
}

.search .top_w_b_not {
	height: 20px !important;
	width: 20px !important;
}

.empty_sort_page {
	width: 100%;
	// height: 100vh;
	background: #f5f5f5;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding-top: 250rpx;
}

.empty_img {
	width: 380rpx;
	height: 280rpx;
	margin-bottom: 32rpx;
}

.empty_text {
	font-size: 26rpx;
	color: #999;
}

.block {
	display: block;
}

.search_wrap {
	z-index: 99;
	position: fixed;
	top: 0;
	/* #ifdef H5 */
	padding-top: 28rpx !important;
	/* #endif */
	/* 原是 MP || APP-PLUS*/
	//app-9-start
	/* #ifdef APP-PLUS */
	padding-top: calc(var(--status-bar-height) + 20rpx);
	/* #endif */
	//app-9-end
	left: 0;
	right: 0;

	.searchs {
		width: 710rpx;
		height: 60rpx;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		/* #ifndef MP */
		margin: 20rpx 20rpx 0 20rpx;
		background: transpanrent;
		margin: 0 auto;
		/* #endif */
		z-index: 99;
	}

	.search_mp {
		padding: 0 25rpx 0 55rpx;
	}

	.search_mp .search_mp_name {
		width: 95rpx;
		font-size: 34rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #ffffff;
		margin-right: 30rpx;
		text-align: center;
		display: flex;
		align-items: center;

		image {
			width: 22rpx;
			height: 35rpx;
		}
	}

	.searchs .left {
		width: 300rpx;
		height: 60rpx;
		border-radius: 30rpx;
		// background: rgba(255, 255, 255, 0.2);
		background-color: #fff;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
	}

	.search_mp .left {
		// width: auto;
		flex: 1;
		height: 100%;
	}

	.searchs .left image {
		width: 50rpx;
		height: 50rpx;
		margin-top: 2rpx;
		margin-left: 27rpx;
	}

	.search_mp .left image {
		width: 35rpx !important;
		height: 35rpx !important;
		margin-right: 11rpx;
	}

	.searchs .left text {
		// color: #fff;
		color: #949494 !important;
		font-size: 28rpx;
		margin-top: -2rpx;
	}

	.searchs .search_text {
		font-size: 26rpx;
		color: #fff;
	}

	/* 三点更多分享 */
	.more_tips {
		position: relative;
	}

	.more {
		width: 36rpx !important;
		height: 28rpx !important;
		margin-left: 30rpx;
	}

	.triangle-up {
		position: absolute;
		right: 15rpx;
		width: 0;
		height: 0;
		border-left: 15rpx solid transparent;
		border-right: 15rpx solid transparent;
		border-bottom: 20rpx solid #fcfcfc;
		/* transform: rotate(120deg); */
		transform: rotate(0deg);
		box-shadow: -2rpx 2rpx -1rpx 0rpx rgba(102, 102, 102, 0.1);
		z-index: 21;
	}

	.tips {
		position: absolute;
		z-index: 20;
		top: 65rpx;
		// right: -15rpx;
		left: 50%;
		margin-left: -62rpx;
		width: 226rpx;
		background: rgba(255, 255, 255, 1);
		box-shadow: 0px 0px 10rpx 0px rgba(102, 102, 102, 0.2);
		opacity: 0.94;
		border-radius: 15rpx;
		display: flex;
		flex-direction: column;
	}

	.tips_pre {
		width: 100%;
		height: 88rpx;
		display: flex;
		align-items: center;
		border-bottom: #e6e6e6;
		padding-left: 20rpx;
		box-sizing: border-box;
	}

	button::after {
		border: none;
	}

	button[plain] {
		border: none;
	}

	.tips_pre image {
		width: 50rpx !important;
		height: 50rpx !important;
		margin-right: 8rpx;
	}

	.tips_pre text {
		font-size: 26rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: rgba(51, 51, 51, 1);
		line-height: 32rpx;
	}
}

.search_wrap1 {
	background: #fff;
	padding-bottom: 10rpx;

	.left {
		background: #f6f6f6 !important;

		text {
			color: #949494 !important;
		}
	}
}

.search .top_w_b12,
.top_w_b12 {
	width: 19rpx !important;
	height: 33rpx !important;
}
</style>
