<template>
	<view :style="mix_diyStyle">
	<view class="content">
    <!-- #ifdef H5 -->
		<view class="fixed_top_status_bar" :style="windowTop?'height:'+windowTop+'px':''"></view>
    <!-- #endif -->
		<view class="top_part">
			<!-- 搜索头部分 start -->
			<view class='sea_input_part' :style="windowTop?'top:'+windowTop+'px':''">
				<view class="search_center">
					<image class="search_icon" :src="imgUrl+'search.png'"></image>
					<input class='sea_input' type='text' :value="searchParams.keyword" :placeholder="$L('请输入店铺名称')"
						@input="inputChange" @confirm='search' maxlength="50"></input>
					<image class='clear_content' v-show="searchParams.keyword" @click="clearInputVal" :src="imgUrl+'input_clear.png'" />
				</view>
				<text class='sea_btn' @click="search">{{$L('搜索')}}</text>
			</view>
			<!-- 搜索头部分 end -->
		</view>

		<view v-if="loadingState!='first_loading'&&showStoreList&&store_list.length==0" class="empty_part flex_column_start_center">
			<image :src="imgUrl+'empty_goods.png'" />
			<text>{{$L('暂无数据')}}</text>
		</view>
		
		<scroll-view v-if="showStoreList&&store_list.length>0" class="shop_lists" scroll-y
			@scrolltolower='getMoreStoreData' :style="windowTop?'margin-top:'+windowTop+'px':''">
			<view class="" v-if="showStoreList&&store_list.length>0">
				<storeItem v-for="(item, index) in store_list" :key="index" :store_info='item' :store_index="index"></storeItem>
				<loadingState v-if="loadingState == 'first_loading'||store_list.length > 0" :state='loadingState' />
			</view>
		</scroll-view>
	</view>
	</view>
</template>
<script>
	import loadingState from "@/components/loading-state.vue";
	import storeItem from "../components/store_item.vue";
	export default {
		components: {
			loadingState,
			storeItem
		},
		data() {
			return {
				windowTop: 0, //h5顶部高度
				imgUrl: getApp().globalData.imgUrl,
				searchParams: {
					keyword: '',
					storeId: '',
				}, //搜索的参数
				stopPullDownRefresh: false, //是否下拉刷新中
				headerPosition: "fixed",
				loadingState: 'first_loading',
				current: 1,
				pageSize: 10,
				hasMore: true, //是否还有数据
				seleindex: 0,
				store_list: [],
				store_info: {
					pageSize: 10,
					current: 1,
					hasMore: false
				},
				showStoreList: false,
			};
		},
		onLoad(options) {
			// #ifdef H5
			let info = uni.getSystemInfoSync()
			this.windowTop = info.windowTop;
			// #endif

			this.getShopList();
			this.showStoreList = true;
		},
		onPageScroll(e) {
			//兼容iOS端下拉时顶部漂移
			if (e.scrollTop >= 0) {
				this.headerPosition = "fixed";
			} else {
				this.headerPosition = "absolute";
			}
		},
		//下拉刷新
		onPullDownRefresh() {
			this.current = 1;
			this.stopPullDownRefresh = true; //下拉刷新状态
			this.getShopList();
		},
		methods: {
			//获取店铺列表
			getShopList() {
				uni.showLoading({
					title: this.$L('加载中'),
					icon: 'none'
				})
				let param = {};
				param.url = 'v3/seller/front/store/list';
				param.data = {};
				param.data.current = this.store_info.current;
				param.data.pageSize = this.store_info.pageSize;
				if(this.searchParams.keyword){
					param.data.keyword = this.searchParams.keyword
				}
				this.loadingState = this.loadingState == 'first_loading' ? this.loadingState : 'loading';
				this.$request(param).then(res => {
					if (res.state == 200) {
						if (this.store_info.current == 1) {
							this.store_list = res.data.list;
						} else {
							this.store_list = this.store_list.concat(res.data.list);
						}
						this.store_info.hasMore = this.$checkPaginationHasMore(res.data.pagination); //是否还有数据
						if (this.store_info.hasMore) {
							this.store_info.current++;
							this.loadingState = 'allow_loading_more';
						} else {
							this.loadingState = 'no_more_data';
						}
						uni.hideLoading()
					} else {
						uni.hideLoading()
						this.$api.msg(res.msg)
						//错误提示
					}
					if (this.stopPullDownRefresh) {
						this.stopPullDownRefresh = false;
						uni.stopPullDownRefresh();
					}
				})
			},

			getMoreStoreData() {
				if (this.store_info.hasMore) {
					this.getShopList();
				}
			},

			inputChange(e) {
				this.searchParams.keyword = e.detail.value;
			},
			
			//搜索事件
			search() {
				this.store_info.current = 1;
				this.store_info.hasMore = true;
				this.getShopList();
			},
			
			//清空输入内容
			clearInputVal() {
				this.searchParams.keyword = '';
				this.search();
			},
		},
	}
</script>

<style lang="scss">
	page,
	.content {
		background: $bg-color-split;
		display: flex;
		width: 750rpx;
		flex: 1;
		margin: 0 auto;
	}

	uni-page-body {
		display: flex;
		height: 100%;
	}

	.sea_input_part {
		position: fixed;
		display: flex;
		align-items: center;
		height: 88rpx;
		background-color: #fff;
		width: 750rpx;
		top: 0;
		right: 0;
		left: 0;
		margin: 0 auto;

		.back_icon {
			padding-left: 20rpx;
		}
		
		.sea_input {
			flex: 1;
			height: 65rpx;
			font-size: 28rpx;
			color: #333;
			/* #ifdef MP-ALIPAY */
			background-color: #f5f5f5;
			/* #endif */
			
		}
		
		.search_center {
			display: flex;
			align-items: center;
			border: none;
			flex: 1;
			height: 65rpx;
			margin-left: 20rpx;
			padding-left: 20rpx;
			border-radius: 32.5rpx;
			background-color: #f5f5f5;
		
			.search_icon {
				width: 32rpx;
				height: 32rpx;
				margin-right: 13rpx;
        margin-bottom: 3rpx;
			}
		}
		
		.clear_content {
			width: 45rpx !important;
			height: 45rpx !important;
			margin-right: 15rpx !important;
		}
		
		.sea_btn {
			font-size: 28rpx;
			color: #2D2D2D;
			padding: 10rpx 25rpx;
			flex-shrink: 0;
		}

		&:after {
			position: absolute;
			content: '';
			left: 0;
			bottom: 0;
			width: 100%;
			height: 1rpx;
			background-color: #eee;
			transform: scaleY(0.5);
		}
	}

	.top_part {
		top: var(--window-top);
		position: fixed;
		z-index: 9;
	}

	.empty_part {
		margin-top: 276rpx;
		width: 100%;

		image {
			width: 380rpx;
			height: 280rpx;
		}

		text {
			color: $main-third-color;
			font-size: 26rpx;
			margin-top: 57rpx;
		}

		button {
			width: 245rpx;
			height: 66rpx;
			background: rgba(252, 28, 28, .05);
			border-radius: 33rpx;
			color: var(--color_main);
			font-size: 30rpx;
			font-weight: bold;
			margin-top: 29rpx;
		}

		uni-button:after {
			border-radius: 200rpx;
			border-color: #fff;
		}
	}

	.fixed_top_status_bar {
		position: fixed;
		//app-1-start
		/* #ifdef APP-PLUS */
		height: var(--status-bar-height);
		/* #endif */
		//app-1-end
		/* #ifndef APP-PLUS */
		height: 0;
		/* #endif */
		top: 0;
		left: 0;
		right: 0;
		z-index: 99;
		background: #fff;
	}

	.shop_lists {
		width: 100%;
		height: 100%;
		background: #F5F5F5;
		box-sizing: border-box;
		margin-top: 88rpx;
		padding: 20rpx 0;

	}
</style>
