<template>
	<view :style="mix_diyStyle">
	<view class="rank_detail" v-if="!loading&&rankAggrCate.length>0">
		<banner :bannerBg="bannerBg" :info="bannerInfo" :fixed="topFixed" :showHeader="showHeader" @share="openShare">
		</banner>
		<view :class="{category:true,cate_fixed:cateFixed}">
			<scroll-view scroll-x="true">
				<view class="cate_scroll">
					<view :class="{cate_item:true,cate_sel:cateSel==cateIdx}"
						v-for="(cate,cateIdx) in rankAggrCate" :key="cateIdx" @click="selCate(cate,cateIdx)">
						{{cate.categoryName}}</view>
				</view>
			</scroll-view>
			<view class="cate_tab" v-if="rankAggrType.length>1">
				<view :class="{tab_item:true,tab_sel:tabIdx==tabSel}" v-for="(tab,tabIdx) in rankAggrType"
					:key="tabIdx" @click="selType(tab,tabIdx)">{{tab.typeName}}</view>
			</view>
		</view>
		<view :class="{goods_list:true,fixed_margin:cateFixed}">
			<block v-for="(item,index) in rankAggrList" :key="index">
				<goodsItem2 :info="item"></goodsItem2>
			</block>
			<loadingState :state='loadingState' />
		</view>

		<uni-popup ref="popShare" type="bottom">
			<view class="popShare">
				<view class="tri_share">
					<!-- app-1-start -->
					<!-- #ifdef APP-PLUS -->
					<view class="img_con flex_column_between_center" @click="sldShare(0,'WXSceneSession')">
						<image :src="imgUrl+'rank/wx_share.png'" mode="aspectFit"></image>
						<text>{{$L('微信好友')}}</text>
					</view>
					<view class="img_con flex_column_between_center" @click="sldShare(0,'WXSenceTimeline')">
						<image :src="imgUrl+'rank/pyq_share.png'" mode="aspectFit"></image>
						<text>{{$L('朋友圈')}}</text>
					</view>
					<!-- #endif -->
					<!-- app-1-end -->
					<!-- wx-1-start -->
					<!-- #ifdef MP-WEIXIN -->
					<button open-type="share" @click="$refs.popShare.close()" class="shareButton">
						<view class="img_con flex_column_between_center">
							<image :src="imgUrl+'rank/wx_share.png'" mode="aspectFit"></image>
							<text>{{$L('微信好友')}}</text>
						</view>
					</button>
					<!-- #endif -->
					<!-- wx-1-end -->
					<!-- #ifdef H5 -->
					<block v-if="$isWeiXinBrower()">
						<view class="img_con flex_column_between_center" @tap.stop="sldShareBrower">
							<image :src="imgUrl+'rank/wx_share.png'" mode="aspectFit"></image>
							<text>{{$L('微信好友')}}</text>
						</view>
						<view class="img_con flex_column_between_center" @tap.stop="sldShareBrower">
							<image :src="imgUrl+'rank/pyq_share.png'" mode="aspectFit"></image>
							<text>{{$L('朋友圈')}}</text>
						</view>
					</block>
					<!-- #endif -->
					<view class="img_con flex_column_between_center" @click="copyLink">
						<image :src="imgUrl+'rank/copy_link.png'" mode="aspectFit"></image>
						<text>{{$L('复制链接')}}</text>
					</view>
				</view>
				<view class="can_con" @click="$refs.popShare.close()">{{$L('取消')}}</view>
			</view>
		</uni-popup>

		<!-- 微信浏览器分享提示  start-->
		<view class="wx_brower_share_mask" v-if="showWeiXinBrowerTip">
			<view class="wx_brower_share_top_wrap">
				<image :src="imgUrl+'wx_share_tip.png'" mode="widthFix" @tap="showWeiXinBrowerTip=false"
					class="wx_brower_share_img"></image>
			</view>
		</view>
		<!-- 微信浏览器分享提示  end-->

	</view>
	<view v-else-if="!loading&&rankAggrCate.length==0">
		<empty></empty>
	</view>
	</view>
</template>

<script>
	import loadingState from '@/components/loading-state.vue'
	import empty from './components/empty.vue'
	import { getCurLanguage } from '@/utils/base.js'
	import uniPopup from '@/components/uni-popup/uni-popup.vue'
	import banner from './components/banner.vue'
	import goodsItem2 from './components/goodsItem2.vue'
	import shareModel from '../components/shareModel.vue'
	import h5Copy from '@/static/h5/H5copy.js'
	export default {
		components: {
			banner,
			goodsItem2,
			shareModel,
			uniPopup,
			empty,
			loadingState
		},
		data() {
			return {
				cateSel: 0,
				tabSel: 0,
				queryParam: { current: 1, pageSize: 10 },
				rankAggrCate: [],
				rankAggrType: [],
				rankAggrList: [],
				bannerInfo: { rankName: getCurLanguage('排行榜'), description: getCurLanguage('官方权威数据') },
				topFixed: false,
				showHeader: false,
				cateFixed: false,
				imgUrl: getApp().globalData.imgUrl,
				shareDataAggr: {
					title: getCurLanguage('排行榜'),
					desc: getCurLanguage('商品太多不知道买什么？快来排行榜看看吧！'),
					path: '/standard/rank/aggr',
					link: getApp().globalData.apiUrl + 'standard/rank/aggr',
					imageUrl: getApp().globalData.imgUrl + 'rank/aggr_share.png',
					imgUrl: getApp().globalData.imgUrl + 'rank/aggr_share.png'
				},
				hasmore: false,
				page: {},
				loading: true,
				loadingState: 'first_loading',
				showWeiXinBrowerTip: false,
				bannerBg: getApp().globalData.imgUrl + 'rank/rank_bg.png',
			}
		},

		onLoad() {
			this.initCate()
			// #ifdef H5
			if (this.$isWeiXinBrower()) {
				this.$weiXinBrowerShare(1, {
					...this.shareDataAggr
				});
			}
			// #endif
			this.getSetting();
		},

		onUnload() {
			this.shareDataAggr = {}
		},

		onShareAppMessage: function () {
			return {
				...this.shareDataAggr
			};
		},

		onHide() {
			this.showWeiXinBrowerTip = false
		},

		onReachBottom() {
			if (this.hasmore) {
				this.loadRankList()
			}
		},

		onPageScroll(e) {
			if (e.scrollTop > 1) {
				this.topFixed = true
			} else {
				this.topFixed = false
			}

			if (e.scrollTop > 60) {
				this.showHeader = true
			} else {
				this.showHeader = false
			}

			if (e.scrollTop > 142) {
				this.cateFixed = true
			} else {
				this.cateFixed = false
			}
		},

		methods: {

			// 获取设置信息
			getSetting() {
				let that = this;
				let param = {};
				param.url = 'v3/system/front/setting/getSettings';
				param.method = 'GET';
				param.data = {};
				param.data.names = 'rank_background_image';
				this.$request(param).then(res => {
					if (res.state == 200) {
						if (res.data[0]) {
							that.bannerBg = res.data[0]
						}
					}
				})
			},

			initCate() {
				this.$request({
					url: 'v3/goods/front/goods/rank/categoryList'
				}).then(res => {
					this.loading = false
					if (res.state == 200) {
						this.rankAggrCate = res.data
						this.queryParam.categoryId = this.rankAggrCate[0].categoryId
					}
				}).then(() => {
					this.selCate(this.rankAggrCate[0], 0)
				})
			},

			initRank(type, callBack) {
				this.$request({
					url: 'v3/goods/front/goods/rank/list',
					data: {
						...this.queryParam
					}
				}).then(res => {
					if (res.state == 200) {
						this.page = res.data.pagination
						callBack(res.data[type])
					}
				})
			},
			selCate(cate, cateIdx) {
				this.queryParam.current = 1
				delete this.queryParam.typeId
				this.cateSel = cateIdx
				this.queryParam.categoryId = cate.categoryId
				this.initRank('typeList', (res) => {
					this.rankAggrType = res
					this.tabSel = 0
					this.loadRankList()
				})
			},
			selType(tab, tabIdx) {
				this.queryParam.current = 1
				this.tabSel = tabIdx
				this.queryParam.typeId = tab.typeId
				this.loadRankList()
			},

			loadRankList() {
				this.initRank('rankList', (res) => {
					if (this.queryParam.current == 1) {
						this.rankAggrList = res
					} else {
						this.rankAggrList = this.rankAggrList.concat(res)
					}
					if (this.$checkPaginationHasMore(this.page)) {
						this.loadingState = 'allow_loading_more'
						this.queryParam.current++
						this.hasmore = true
					} else {
						this.loadingState = 'no_more_data'
						this.hasmore = false
					}
				})
			},




			openShare() {
				this.$refs.popShare.open()
			},

			sldShareBrower() {
				this.$refs.popShare.close()
				this.showWeiXinBrowerTip = true;
				this.share_model = false;

			},

			sldShare(type, scene) {
				this.$refs.popShare.close()
				let shareData1 = {};
				let {
					shareDataAggr,
				} = this;
				if (type == 0) {
					shareData1.href = getApp().globalData.apiUrl + shareDataAggr.path;
					shareData1.title = shareDataAggr.title;
					shareData1.summary = shareDataAggr.desc;
					shareData1.imageUrl = shareDataAggr.imageUrl;
				} else if (type == 2) {
					shareData1.imageUrl = shareDataAggr.imageUrl;
				}
				this.$weiXinAppShare(type, scene, shareData1);
				this.closeShareModel(); //关闭分享
			},

			copyLink() {
				let col = getApp().globalData.apiUrl + 'standard/rank/aggr'
				// #ifdef H5
				const result = h5Copy(col)
				if (result === false) {
					this.$api.msg(this.$L('不支持'))
				} else {
					this.$api.msg(this.$L('已复制到剪贴板'))
				}
				// #endif

				// #ifdef APP-PLUS || MP-WEIXIN
				uni.setClipboardData({
					data: col,
					success: function () {
						this.$api.msg(this.$L('已复制到剪贴板'))
					}
				});
				// #endif
				this.$refs.popShare.close()
			}
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #F6F6F6;
	}

	.rank_detail {
		.goods_list {
			margin-top: -2rpx;
			padding: 20rpx;
			background-color: #F6F6F6;
		}

		.cate_fixed {
			position: fixed;
			/* #ifndef MP */
			top: calc(86rpx);
			/* #endif */
			/* #ifdef MP */
			top: 86rpx;
			/* #endif */
			z-index: 9999;
			width: 100%;
		}

		.fixed_margin {
			/* #ifndef MP */
			margin-top: calc(180rpx - 2rpx + var(--status-bar-height));
			/* #endif */
			/* #ifdef MP */
			margin-top: calc(180rpx - 2rpx);
			/* #endif */
		}

		.category {
			
			background-color: #F6F6F6;
			padding: 10rpx 20rpx 20rpx 20rpx;
			box-sizing: border-box;

			.cate_scroll {
				display: flex;
				align-items: center;
				overflow-x: scroll;
				width: 700rpx;
				height: 80rpx;

				.cate_item {
					font-size: 28rpx;
					position: relative;
					padding: 4rpx 28rpx;
					display: flex;
					box-sizing: border-box;
					flex-shrink: 0;
					color: #242424;
					justify-content: center;

				}

				.cate_sel {
					font-size: 36rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: 600;
				}


				.cate_sel:after {
					position: absolute;
					bottom: -10rpx;
					height: 4rpx;
					width: 20%;
					display: block;
					content: '';
					border-radius: 2px;
					background-color: #FFB800;
				}
			}

			.cate_tab {
				margin-top: 30rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				.tab_item {
					padding: 5rpx 15rpx;
					font-size: 28rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #A09A9A;
					border-radius: 20rpx;
					background-color: #fff;
					margin-right: 40rpx;

					&:last-child {
						margin-right: 0rpx;
					}

					&.tab_sel {
						background-color: #F1A81F !important;
						color: #fff !important;
					}
				}
			}
		}

	}

	.popShare {
		border-radius: 10px 10px 0px 0px;
		background: #EBEBEB;
		padding: 20rpx;
		height: 354rpx;

		.tri_share {
			display: flex;
			justify-content: space-evenly;
			margin-top: 50rpx;

			.img_con {
				width: 108rpx;

				image {
					width: 108rpx;
					height: 108rpx;
				}

				text {
					margin-top: 16rpx;
					font-size: 24rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #8C8C8C;
				}
			}

		}

		.can_con {
			width: 100%;
			height: 68rpx;
			background: #FFFFFF;
			border-radius: 10px;
			text-align: center;
			line-height: 68rpx;
			font-size: 28rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #4C4C4C;
			margin-top: 40rpx;
		}
	}

	.shareButton {
		display: flex;
		flex-direction: column;
		align-items: center;
		background: transparent;
		border-radius: 0;
		height: auto;
		line-height: unset;
		padding: 0;
		margin: 0;

		&::after {
			border-width: 0;
		}
	}

	/* 微信浏览器分享提示 start */
	.wx_brower_share_mask {
		width: 750rpx;
		height: 100vh;
		background-color: rgba(0, 0, 0, 0.45);
		position: fixed;
		z-index: 99999;
		top: 0;
	}

	::-webkit-scrollbar {
		display: none;
	}

	scroll-view ::-webkit-scrollbar {
		display: none !important;
		width: 0 !important;
		height: 0 !important;
		-webkit-appearance: none;
		background: transparent;
	}

	.wx_brower_share_top_wrap {
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: flex-end;
		align-items: flex-start;
		margin-top: 30rpx;
	}

	.wx_brower_share_top_wrap .wx_brower_share_img {
		width: 450rpx;
		height: 150rpx;
		margin-right: 30rpx;
	}

	.share_h5 {
		width: 100% !important;
		height: 100% !important
	}

	uni-image>img {
		opacity: unset;
		object-fit: contain;
	}

	.share_h5_operate_img {
		width: 440rpx !important;
		height: 120rpx !important;
	}

	.share_h5_close_img {
		width: 50rpx !important;
		height: 50rpx !important;
	}

	.share_h5_img_bottom {
		width: 50rpx !important;
		height: 200rpx !important;
	}

	/* 微信浏览器分享提示 end */
</style>