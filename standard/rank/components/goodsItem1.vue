<template>
	<view class="goods_item1" @click="toDetail">
		<view class="main_area">
			<!-- sss -->
			<view class="left_con">
				<view class="image" :style="{ backgroundImage: 'url(' + info.goodsImage + ')' }"></view>
				<view class="top_icon" :style="'background-image:url(' +imgUrl +'rank/top' +(index < 4 ? index + 1 : 4) +'.png)'">
					{{ index + 1 }}
				</view>
			</view>
			<view class="right_con">
				<view class="">
					<view class="goods_name">{{ info.goodsName }}</view>
				</view>

				<view class="pri_num">
					<view class="price">
						<view class="current">
							<text class="price_s">{{ $L('¥') }}</text>
							<text class="price_b">{{$getPartNumber(info.productPrice, 'int')}}</text>
							<text class="price_s">{{$getPartNumber(info.productPrice, 'decimal')}}</text>
						</view>
					</view>
					<view class="num">
						<svgGroup type="to_discount" width="28" height="28" px="rpx" color="#f1a81f"></svgGroup>
						<text>{{info.saleNum}}{{$L("人购买")}}</text>
					</view>
				</view>
			</view>
			<!-- ppp -->
		</view>
		<view class="reason" v-if="info.rankReason">{{ $L('上榜理由') }}: {{ info.rankReason }}</view>
	</view>
</template>

<script>
	export default {
		props: ['info', 'index', 'isDiy'],
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				topIcon: [
					`${getApp().globalData.imgUrl}rank/top1.png`,
					'${imgUrl}rank/top1.png'
				]
			}
		},
		methods: {
			toDetail() {
				let {
					productId,
					goodsId
				} = this.info
				this.$Router.push({
					path: '/standard/product/detail',
					query: {
						productId,
						goodsId
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	.goods_item1 {
		padding: 20rpx;
		border-radius: 6px;
		background-color: #fff;
		// display: flex;
		margin-bottom: 20rpx;

		.main_area {
			display: flex;
		}

		.reason {
			border-radius: 4px;
			margin-top: 20rpx;
			background: #ffe7e7;
			font-size: 20rpx;
			line-height: 32rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #545245;
			padding: 14rpx 24rpx;
			word-break: break-word;
			word-wrap: break-word;
		}

		.left_con {
			position: relative;

			.image {
				background-position: center center;
				background-repeat: no-repeat;
				background-size: cover;
				width: 260rpx;
				height: 260rpx;
				border-radius: 6px;
				background-color: #f7f7f7;
			}

			.top_icon {
				width: 56rpx;
				height: 56rpx;
				position: absolute;
				top: 0;
				left: 0;
				background-position: center center;
				background-repeat: no-repeat;
				background-size: cover;
				color: #fff;
				font-style: oblique;
				font-size: 22rpx;
				font-family: DIN-BoldItalic, DIN;
				font-weight: bold;
				color: #ffffff;
				padding-top: 24rpx;
				text-align: center;
				padding-right: 10rpx;
			}
		}

		.right_con {
			padding-left: 20rpx;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			width: 432rpx;

			.goods_name {
				font-size: 28rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: 500;
				color: #333333;
				word-break: break-all;
				font-weight: 600;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				text-overflow: ellipsis;
				overflow: hidden;
			}

			.pri_num {
				.price {
					display: flex;
					align-items: center;

					.current {
						color: #f30300;

						.price_s {
							font-size: 24rpx;
						}

						.price_b {
							font-size: 34rpx;
						}
					}

					.mark {
						font-size: 24rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #999999;
						margin-left: 20rpx;
					}
				}

				.num {
					max-width: 432rpx;
					display: inline;
					margin-top: 12rpx;
					background: #ffe9ed;
					border-radius: 13px;
					padding: 8rpx;
					padding-left: 16rpx;
					display: flex;
					align-items: center;

					image {
						width: 28rpx;
						height: 28rpx;
						margin-right: 8rpx;
					}

					text {
						font-size: 24rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #f32549;
					}
				}
			}
		}
	}
</style>