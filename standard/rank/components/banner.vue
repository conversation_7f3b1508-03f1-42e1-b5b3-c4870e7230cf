<template>
  <view class="rank_banner" :style="{backgroundImage:`url(${bannerBg})`}" >
    <view class="fixed_top_bar" :style="{height:statBarHeight+'px'}"></view>
    <!-- #ifdef MP -->
    <view :class="{ top_bar: true, fixed_top: fixed }" :style="{top:fixed?statBarHeight+'px':0}">
      <!-- #endif --> 
      <!-- #ifndef MP -->
    <view :class="{ top_bar: true, fixed_top: fixed }">
      <!-- #endif -->
      <view class="top_header">
        <view class="top_header_left flex_row_start_center">
          <image :src="imgUrl + 'index/back.png'" mode="aspectFit" @click="goBack"></image>
		  
		  <!-- #ifdef MP -->
		  <view class="top_white_space flex_row_start_center" @click.stop="$emit('share')">
		    <image :src="imgUrl + 'rank/rank_share.png'"></image>
		  </view>
		  <!-- #endif -->
		 
		  
        </view>
        <view :class="{ top_header_cen: true, header_show: showHeader }">{{info.rankName}}</view>
		
		<view class="top_white_space" @click.stop="$emit('share')">
			<!-- #ifndef MP -->
		    <image :src="imgUrl + 'rank/rank_share.png'"></image>
		    <!-- #endif -->
		</view>
		
		
       
      </view>
    </view>
    <view :class="{ banner_title: true, banner_by_fixed: fixed }">
      <view class="title_main">{{ info.rankName }}</view>
      <view class="title_sub">{{
        info.description ? info.description : ''
      }}</view>
    </view>
  </view>
</template>

<script>
export default {
  // props: ['fixed','info','showHeader'],
  props: {
    fixed: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: {}
    },
    showHeader: {
      type: Boolean,
      default: false
    },
    bannerBg: {
      String: String,
      default: getApp().globalData.imgUrl + 'rank/rank_bg.png'
    }
  },
  data() {
    return {
      imgUrl: getApp().globalData.imgUrl,
	  statBarHeight:20
    }
  },
  mounted() {
  	this.statBarHeight = uni.getSystemInfoSync().statusBarHeight
  },
  methods: {
    goBack() {
      this.$back()
    }
  }
}
</script>

<style lang="scss">
//app-1-start
/* #ifdef APP-PLUS */
.fixed_top_bar {
  width: 100%;
  height: var(--status-bar-height);
  position: fixed;
  top: 0;
  z-index: 9999;
}
/* #endif */
//app-1-end

.rank_banner {
  position: relative;
  //app-2-start
  /* #ifdef APP-PLUS */
  padding-top: calc(var(--status-bar-height) + 10rpx);
  /* #endif */
  //app-2-end
  width: 750rpx;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  height: 350rpx;
  //app-3-start
  /* #ifdef APP-PLUS */
  height: calc(350rpx + var(--status-bar-height) + 10rpx);
  /* #endif */
  //app-3-end
}

.fixed_top {
  position: fixed;
  top: var(--status-bar-height);
  padding: 20rpx;
  /* #ifdef MP */
  top: 0;
  padding-top: 14rpx;
  /* #endif */
  // background-color: #d20505;
  z-index: 999;
}

.banner_by_fixed {
  padding-top: 130rpx !important;
}

.header_show {
  opacity: 1 !important;
  animation: pageLoading 0.2s ease-in;
}

@keyframes pageLoading {
  0% {
    opacity: 0;
  }

  20% {
    opacity: 0.2;
  }

  40% {
    opacity: 0.4;
  }

  60% {
    opacity: 0.6;
  }

  80% {
    opacity: 0.9;
  }
  100% {
    opacity: 1;
  }
}

.top_bar {
  width: 100%;
  padding: 20rpx;
  /* #ifdef MP */
  padding-top: 14rpx;
  /* #endif */
  .top_header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .top_header_left {
      // width: 17rpx;
      // height: 29rpx;
      image {
        width: 17rpx;
        height: 29rpx;
      }
    }

    .top_header_cen {
      margin: 0 50rpx;
      font-size: 32rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      color: #ffffff;
      opacity: 0;
    }

    .top_white_space {
      width: 40rpx;
      height: 49rpx;
	  
	  /* #ifdef MP */
	  margin-left: 30rpx;
	  
	  /* #endif */

      image {
        width: 40rpx;
        height: 40rpx;
      }
    }
  }
}

.banner_title {
  width: 100%;
  padding-top: 20rpx;
  padding-bottom: 20rpx;
  text-align: center;

  .title_main {
    font-size: 60rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    background: linear-gradient(143deg, #ffffff 0%, #ffb431 100%);
    color: #ffffff;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    line-height: 90rpx;
    /* #ifdef MP || APP-PLUS */
    font-weight: 500;
    /* #endif */
    /* #ifdef H5 */
    font-weight: 600;
    /* #endif */
    word-break: break-all;
  }

  .title_sub {
    font-size: 24rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
    line-height: 54rpx;
    opacity: 0.8;
  }
}
</style>
