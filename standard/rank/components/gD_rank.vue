<template>
  <view
    class="detail_rank"
    :style="'background-image:url(' + imgUrl + 'rank/detail_rank.png)'"
    @click="navTo"
  >
    <swiper
      :indicator-dots="false"
      :autoplay="true"
      :interval="5000"
      :duration="1000"
      :disable-touch="true"
      :vertical="true"
      circular
      @change="change"
    >
      <swiper-item v-for="(item, index) in data" :key="index">
        <view class="rank_l">
          <image :src="imgUrl + 'rank/top_rank.png'" mode="aspectFit"></image>
          <view>{{ item.typeName }}</view>
          <view class="rank_l_text">
            <view
              >{{ item.rankName }}{{ $L('第')
              }}<text style="color: red">{{ item.goodsRank }}</text
              >{{ $L('名') }}</view
            >
          </view>
        </view>
        <view class="rank_r">
          <image :src="imgUrl + 'rank/red_ar.png'" mode="aspectFit"></image>
        </view>
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
import jyfParser from '@/components/jyf-parser/jyf-parser.vue'
export default {
  props: ['data'],
  components: {
    jyfParser
  },
  data() {
    return {
      imgUrl: getApp().globalData.imgUrl,
      current: 0
    }
  },
  methods: {
    navTo() {
      let rankId = this.data[this.current].rankId
      this.$Router.push({ path: '/standard/rank/detail', query: { rankId } })
    },
    change(e) {
      this.current = e.detail.current
    }
  }
}
</script>

<style lang="scss">
// test
uni-swiper {
  display: block;
  height: 90rpx;
  width: 750rpx;
}

uni-swiper-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.detail_rank {
  /* padding: 0 20rpx; */
  padding-left: 20rpx;
  padding-right: 10rpx;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  margin-bottom: 20rpx;
  /* #ifdef MP */
  swiper {
    height: 90rpx !important;
    width: 750rpx !important;
  }
  swiper-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  /* #endif */
  .rank_l {
    display: flex;
    align-items: center;

    image {
      width: 34rpx;
      height: 34rpx;
    }

    view:nth-child(2) {
      margin-left: 14rpx;
      margin-top: -4rpx;
      font-size: 32rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 800;
      color: #ff1d1b;
      white-space: nowrap;
    }

    .rank_l_text {
      margin-left: 16rpx;
      display: flex;
      align-items: center;
      width: 520rpx;
      overflow: hidden;

      uni-swiper {
        height: 90rpx;
        width: 100%;
        line-height: 90rpx;
      }

      uni-swiper-item {
        display: flex;
      }

      view:last-child {
        font-size: 28rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #555555;
        white-space: nowrap;
      }

      text {
        color: #ff1d1b;
      }
    }
  }

  .rank_r {
    image {
      margin-top: 8rpx;
      width: 38rpx;
      height: 38rpx;
    }
  }
}
</style>
