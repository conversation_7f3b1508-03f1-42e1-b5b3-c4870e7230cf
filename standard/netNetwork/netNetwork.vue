<template>
	<view :style="mix_diyStyle">
		<view class="not_network">
			<image class="not_network_bg" :src="imgUrl + 'not_network.png'" mode="aspectFit"></image>
			<view class="not_network_title">网络请求失败，请检查您的网络</view>
			<view class="not_network_btn" @click="refreshNetwork()">刷新</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				imgUrl: getApp().imgUrl,
				home: '/pages/index/index',
				category: '/pages/category/category',
				information: '/pages/index/information',
				cart: '/pages/cart/cart',
				user: '/pages/user/user',
				timer: '',
				timer: '',
			}
		},
		onLoad() {
			//app-1-start
			// #ifdef APP-PLUS
			var page = this.$mp.page.$getAppWebview(); //ios系统禁止左右切换上一页
			page.setStyle({
				popGesture: 'none'
			}); //ios系统禁止左右切换上一页
			this.getroute()
			// #endif
			//app-1-end
		},
		onShow() {
			uni.hideLoading();
		},
		onBackPress(e) {
			if (e.from == 'backbutton') {

				return true //阻止默认返回行为
			}
		},

		methods: {
			getroute() {
				const pages = getCurrentPages() //当前页面栈
				// 存储页面栈长度
				let route = ''
				let beforePage = ''
				// 判断页面栈长度是否大于1
				if (pages.length > 1) {
					beforePage = pages[pages.length - 2] //获取上一个页面实例对象
					route = '/' + beforePage.route
				}
				// tabber页面路径 根据项目tabber来填写更改
				let tabberRoute = [
					'/pages/index/index',
					'/pages/category/category',
					'/pages/index/information',
					'/pages/cart/cart',
					'/pages/user/user',
				];
				// 存储或更新页面栈长度
				uni.setStorageSync('routeNetwork', pages.length - 1)
				// 判断是否是tabber
				if (tabberRoute.join(',').indexOf(route) != -1) {
					// 存储tabber标识
					uni.setStorageSync('routeTabber', route)
				}
			},
			//刷新
			refreshNetwork() {
				// 显示loading 提示框
				uni.showLoading()
				const that = this;
				const pages = getCurrentPages() //当前页面栈
				let route = '' //变量
				let beforePage = ''
				if (pages.length > 1) {
					beforePage = pages[pages.length - 2] //获取上一个页面实例对象
					route = '/' + beforePage.route //上一个页面的路由赋值给route变量
				}
				// 判断是否有网络
				uni.getNetworkType({
					success: (res) => {
						// networkType网络类型
						// res.networkType 的值 wifi 2g 3g 4g 5g ethernet(有线网络) unknown(Android 下不常见的网络类型) none(无网络)
						if (res.networkType == 'none') {
							if (that.timer != '') {
								clearTimeout(that.timer)
							}
							that.timer = setTimeout(() => {
								uni.hideLoading();
								that.$api.msg('请连接网络')
							}, 1000)
						} else {
							if (that.timer != '') {
								clearTimeout(that.timer)
							}
							uni.hideLoading();
							// 判断是否有上一页
							if (pages.length > 1) {
								// 判断是否为tabber页 是就删除所有页面进入tabber页面
								if (that.home == route || that.category == route || that.information ==
									route || that.cart == route || that.user == route) {
									uni.reLaunch({
										url: route
									})
								} else {
									// 刷新点击标识
									uni.setStorageSync('netNetworkFlag', 1)
									// 存储上一页的路由
									uni.setStorageSync('netNetwork', beforePage.$page.fullPath)
									// 返回上一页
									uni.navigateBack()
								}
							} else {
								// 没有上一页直接返回首页
								uni.reLaunch({
									url: '/pages/index/index'
								})
							}
						}
					},
				})
			},
		}
	}
</script>

<style lang="scss">
	.not_network {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100vh;

		.not_network_bg {
			width: 236rpx;
			height: 236rpx;
		}

		.not_network_title {
			color: #828282;
			font-size: 24rpx;
			font-family: Microsoft YaHei;
			font-weight: 400;
			margin-top: 67rpx;
		}

		.not_network_btn {
			width: 184rpx;
			height: 66rpx;
			line-height: 66rpx;
			color: #828282;
			font-size: 24rpx;
			font-family: Microsoft YaHei;
			font-weight: 400;
			text-align: center;
			background: #FFFFFF;
			border: 2rpx solid #C3C3C3;
			border-radius: 32rpx;
			margin-top: 40rpx;
		}
	}
</style>