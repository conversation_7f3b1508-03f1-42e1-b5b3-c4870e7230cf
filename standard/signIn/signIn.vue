<template>
	<view :style="mix_diyStyle">
		<view class="page">
			<view class="main_content2" :style="{ backgroundImage: 'url(' + img_url + 'signIn/sign_ac_bg.png)' }" v-if="!firstLoading">
				<!-- 顶部bar -->
				<view class="top_bar" :style="{ paddingTop: `calc(${statusBarHeight}px + 16rpx)` }">
					<view class="top_header">
						<view class="top_header_left" @click="goBack">
							<!-- #ifndef MP-BAIDU -->
							<image :src="img_url + 'signIn/to_back.png'" mode="aspectFit"></image>
							<!-- #endif -->
							<!-- #ifdef MP-BAIDU -->
							<image src="" mode="aspectFit"></image>
							<!-- #endif -->
						</view>
						<view class="top_header_cen">{{ $L('签到中心') }}</view>
						<view class="top_white_space"></view>
					</view>
				</view>

				<block v-if="!firstLoading">
					<!-- 签到可用部分 -->
					<block v-if="JSON.stringify(signInfo) != '{}' && JSON.stringify(signInfo) != null">
						<!-- 签到未开始或签到进行中 -->
						<block v-if="acState < 3">
							<view class="mid_banner">
								<view class="banner_tip">
									<view class="s_sign_tip" @tap="openRule">
										<image :src="img_url + 'signIn/help_icon.png'" mode="aspectFit"></image>
										<text>{{ $L('活动规则') }}</text>
									</view>
								</view>
								<view class="banner_b_title">
									<text>{{ $L('每日签到领积分') }}</text>
								</view>
								<view class="banner_s_title">
									<image :src="img_url + 'signIn/ban_oral_left.png'" mode="aspectFit"></image>
									<text>{{ $L('连续累计签到 独享好礼翻倍') }}</text>
									<image :src="img_url + 'signIn/ban_oral_right.png'" mode="aspectFit"></image>
								</view>
								<!-- <view class="banner_time" v-if="JSON.stringify(signInfo)!='{}'">
								<view class="banner_time_tip">{{acState==1? $L('距离活动开始还有'):(acState==2?$L('距离活动结束还有'):$L('活动已结束'))}}</view>
								<view class="banner_time_count">
									<block v-if="countDown.day!=0">{{countDown.day}}{{$L('天')}}</block>
									{{countDown.hour}}
									<text>{{$L('小时')}}</text>
									{{countDown.minute}}
									<text>{{$L('分')}}</text>
									{{countDown.second}}
									<text>{{$L('秒')}}</text>
								</view>
							</view> -->
							</view>

							<view class="sign_ac_con">
								<view class="ac_con_top">
									<view class="cur_int">
										{{ $L('目前积分') }}
										<text>{{ my_points }}</text>
									</view>
									<view class="cur_int_tip" v-show="signInfo.continueNum > 0">{{ $L('连续签到') }}{{ signInfo.continueNum }}{{ $L('天可领取好礼') }}</view>
								</view>
								<view class="ac_con_bottom">
									<view class="sign_ac_day">
										<block>
											<view class="sign_ac_day_line1">
												<view class="day_bonus_img" v-for="(item, index) in 4" :key="index">
													<image :src="img_url + 'signIn/sign_bonus.png'" mode="aspectFit" :class="{ opa_class: signInfo.signNum < index + 1 }"></image>
													<text>{{ $L('第') }}{{ item + 1 }}{{ $L('天') }}</text>
												</view>
											</view>
											<view class="sign_ac_day_line1">
												<view class="day_bonus_img" v-for="(item, index) in 3" :key="index">
													<image :src="img_url + 'signIn/sign_bonus.png'" mode="aspectFit" :class="{ opa_class: signInfo.signNum < 5 + index }"></image>
													<text>{{ $L('第') }}{{ 5 + item }}{{ $L('天') }}</text>
												</view>
											</view>
										</block>
									</view>
									<block v-if="acState == 2">
										<view class="sign_ac_but sign_on" v-if="signInfo.isSign == 1" @tap="goSign">{{ $L('立即签到') }}</view>
										<view class="sign_ac_but sign_al" v-if="signInfo.isSign == 2">
											<text>{{ $L('今日已签到') }}&nbsp;&nbsp;{{ signInfo.integralPerSign > 0 ? $L('积分+') + signInfo.integralPerSign : '' }}</text>
										</view>
									</block>
									<view class="sign_ac_but sign_off" v-if="acState == 1">活动未开始</view>
									<view class="sign_fini">
										<block v-if="signInfo.continueNum > 0">
											<text v-if="signInfo.signNum > 2">
												您已连续签到{{ signInfo.signNum }}天，
												<block v-if="signInfo.integralPerSign > 0">累计积分{{ signInfo.signNum * signInfo.integralPerSign }}</block>
											</text>
											<text v-if="signInfo.signNum == signInfo.continueNum">
												<block v-show="signInfo.bonusIntegral > 0">获得连签奖励，积分+{{ signInfo.bonusIntegral }}</block>
												<block v-if="signInfo.bonusVoucher > 0">,获得连签奖励，优惠券{{ signInfo.bonusVoucherName }}</block>
											</text>
										</block>
									</view>
								</view>
								<view class="sign_ac_bottom" :style="{ backgroundImage: 'url(' + img_url + 'signIn/sign_ac_bottom.png)' }">
									<view class="share_con">
										<view class="share_con_top">
											<!-- <text>{{ $L('分享至好友') }}</text> -->
											<!-- <view class="share_but" @click="goShare">
												{{ $L('分享') }}
												<image :src="img_url + 'signIn/share_but.png'" mode="aspectFit"></image>
											</view> -->
										</view>

										<!-- <text>{{ $L('签到领积分规则') }}X</text> -->
									</view>
								</view>
							</view>
						</block>

						<!-- 签到结束 -->
						<block v-else>
							<view class="golden_bg flex_column_center_center" :style="{ backgroundImage: `url(${img_url}signIn/golden_bg.png)` }">
								<image :src="img_url + 'signIn/success.png'" mode=""></image>
								<view class="signAc_off">{{ $L('本轮签到已结束') }}</view>
								<view class="signAc_off_but" @click="goBack()">{{ $L('返回') }}</view>
							</view>
						</block>
					</block>

					<!-- 签到不可用部分 -->
					<block v-else>
						<view class="golden_bg flex_column_center_center" :style="{ backgroundImage: `url(${img_url}signIn/golden_bg.png)` }">
							<image :src="img_url + 'signIn/close.png'" mode=""></image>
							<view class="signAc_off">{{ $L('签到活动不可用') }}</view>
							<view class="signAc_off_but" @click="goBack()">{{ $L('返回') }}</view>
						</view>
					</block>
				</block>

				<uni-popup ref="rule" type="center">
					<view class="sign_rule">
						<view class="sign_rule_title">{{ $L('签到领积分规则') }}</view>
						<view class="sign_scroll_rule">
							<view class="sign_rule_one">
								<image :src="img_url + 'signIn/sign_rule1.png'" mode="aspectFit"></image>
								<text>{{ $L('每日签到获得积分奖励') }}</text>
							</view>
							<view class="sign_rule_one_text">
								<scroll-view scroll-y="true" class="scrollView">
									<text>{{ signInfo.bonusRules }}</text>
								</scroll-view>
							</view>
							<block v-if="JSON.stringify(signInfo) && JSON.stringify(signInfo.share) != '{}' && JSON.stringify(signInfo.share) != null">
								<view class="sign_rule_one">
									<image :src="img_url + 'signIn/sign_rule2.png'" mode="aspectFit"></image>
									<text>{{ $L('分享活动') }}</text>
								</view>
								<view class="sign_rule_one_text">
									<text>1.{{ $L('签到活动未开始前即可分享至好友') }};</text>
								</view>
							</block>
						</view>

						<view style="sign_but_rule_con">
							<view class="sign_but_rule" @click="$refs.rule.close()">{{ $L('知道了～') }}</view>
						</view>
					</view>
				</uni-popup>

				<!-- 分享弹框 start -->
				<view class="share_model" v-if="share_model" @touchmove.stop.prevent="moveHandle">
					<view class="share_model_list">
						<!-- #ifdef H5 -->
						<view class="share_model_pre" @tap.stop="sldShareBrower(1)" v-if="isWeiXinBrower">
							<image :src="img_url + 'goods_detail/wx_share.png'" mode="aspectFit"></image>
							<text>{{ $L('微信好友') }}</text>
						</view>
						<view class="share_model_pre" @tap.stop="sldShareBrower(2)" v-if="isWeiXinBrower">
							<image :src="img_url + 'goods_detail/wechat_moments.png'" mode="aspectFit"></image>
							<text>{{ $L('微信朋友圈') }}</text>
						</view>
						<!-- #endif -->
						<!-- wx-1-start -->
						<!-- #ifdef MP-WEIXIN -->
						<button open-type="share" class="share_model_pre">
							<image :src="img_url + 'goods_detail/wx_share.png'" mode="aspectFit"></image>
							<text>{{ $L('微信好友') }}</text>
						</button>
						<!-- #endif -->
						<!-- wx-1-end -->
						<!-- app-1-start -->
						<!-- #ifdef APP-PLUS -->
						<view class="share_model_pre" @tap.stop="sldShare(0, 'WXSceneSession')">
							<image :src="img_url + 'goods_detail/wx_share.png'" mode="aspectFit"></image>
							<text>{{ $L('微信好友') }}</text>
						</view>
						<view class="share_model_pre" @tap.stop="sldShare(0, 'WXSenceTimeline')">
							<image :src="img_url + 'goods_detail/wechat_moments.png'" mode="aspectFit"></image>
							<text>{{ $L('微信朋友圈') }}</text>
						</view>
						<!-- #endif -->
						<!-- app-1-end -->
					</view>
					<view class="share_model_close" @click="closeShareModel">
						<image :src="img_url + 'goods_detail/share_close.png'" mode="aspectFit"></image>
					</view>
				</view>
				<!-- 分享弹框 end -->
				<block v-if="isWeiXinBrower">
					<!-- 微信浏览器分享提示  start-->
					<view class="wx_brower_share_mask flex_row_center_center" v-if="showWeiXinBrowerTip">
						<view class="wx_brower_share_top_wrap">
							<image :src="img_url + 'wx_share_tip.png'" mode="widthFix" @tap="closeShareModel" class="wx_brower_share_img"></image>
						</view>
					</view>
					<!-- 微信浏览器分享提示  end-->
				</block>

				<block v-else>
					<!-- 非微信浏览器分享提示  start-->
					<view class="wx_brower_share_mask flex_row_center_center" v-if="showWeiXinBrowerTip" @click="showWeiXinBrowerTip = false">
						<view class="qr_code">
							<view class="">邀请好友来扫一扫</view>
							<yuanqiQrCode ref="yuanqiQRCode" :text="window.location.href" :size="400" :borderSize="15"></yuanqiQrCode>
						</view>
					</view>
					<!-- 非微信浏览器分享提示  end-->
				</block>
			</view>
		</view>
	</view>
</template>

<script>
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import uniSwiperDot from '@/components/uni-swiper-dot/uni-swiper-dot.vue';
import yuanqiQrCode from '@/components/yuanqi-qr-code/yuanqi-qr-code.vue';
import { mapState } from 'vuex';
export default {
	data() {
		return {
			img_url: getApp().globalData.imgUrl,
			bigDay: '00',
			month: '00',
			year: '0000',
			signInfo: {},
			my_points: 0,
			acState: 0,
			countDown: {
				day: 0,
				hour: 0,
				minute: 0,
				second: 0
			},
			secInterval: '',
			isHideMask: true,
			signCountTotal: 0,
			signMutipleDay: [],
			current: 0,
			isWeiXinBrower: false,
			share_model: false,
			showWeiXinBrowerTip: false,
			isReachContiBonus: false,
			signRule: false,
			firstLoading: true,
			statusBarHeight: uni.getSystemInfoSync().statusBarHeight,
			window
		};
	},
	onLoad() {
		if (!this.hasLogin) {
			uni.setStorageSync('fromurl', {
				url: '/standard/signIn/signIn'
			});
			this.$Router.push('/pages/public/login');
		}

		this.initData();
		// #ifdef H5
		this.isWeiXinBrower = this.$isWeiXinBrower();
		// #endif
	},
	onShow() {
		this.share_model = false;
		this.showWeiXinBrowerTip = false;
	},
	components: {
		uniSwiperDot,
		uniPopup,
		yuanqiQrCode
	},

	computed: {
		...mapState(['hasLogin'])
	},

	onShareAppMessage() {
		return {
			title: this.signInfo.share.shareTitle,
			desc: this.signInfo.share.shareDesc,
			path: '/standard/signIn/signIn',
			imageUrl: this.signInfo.share.shareImgUrl
		};
	},

	onShareTimeline() {
		return {
			title: this.signInfo.share.shareTitle,
			imageUrl: this.signInfo.share.shareImgUrl
		};
	},

	methods: {
		initData() {
			uni.showLoading({});
			this.getInfo();
			this.getUserPoints();
		},
		goBack() {
			const prePage = getCurrentPages()[getCurrentPages().length - 2];

			if (prePage && prePage.route == 'pages/index/index') {
				this.$Router.pushTab('/pages/index/index');
			} else if (prePage && prePage.route == 'pages/user/user') {
				this.$Router.pushTab('/pages/user/user');
			} else {
				this.$Router.back(1);
			}
		},
		//获取签到信息
		getInfo() {
			let params = {
				url: 'v3/promotion/front/sign/activity/detail'
			};
			this.$request(params).then((res) => {
				this.firstLoading = false;
				uni.hideLoading();
				if (res.state == 200) {
					const detail = res.data
					detail.bonusRules = detail.bonusRules.replace(/\\n/g,'\n')
					this.signInfo = detail;
					this.acState = detail.activityState;
					// this.countDay()
					// this.openTime()
				} else if (res.state == 255) {
					this.$api.msg(res.msg);
				}
			});
		},
		//获取用户积分
		getUserPoints() {
			let param = {};
			param.url = 'v3/member/front/integralLog/getMemberIntegral';
			param.method = 'GET';
			this.$request(param).then((res) => {
				if (res.state == 200) {
					this.my_points = res.data.memberIntegral;
				}
			});
		},
		//处理距离活动开始时间|距离活动结束时间
		openTime() {
			let now = new Date();
			let start = new Date(this.signInfo.startTime.replace(/-/g, '/'));
			let end = new Date(this.signInfo.endTime.replace(/-/g, '/'));
			if (this.acState == 1) {
				let diffTime = (start.getTime() - now.getTime()) / 1000;
				this.secInterval = setInterval(() => {
					if (diffTime == 0) {
						//倒计时结束，清除倒计时
						this.getInfo();
						clearInterval(this.secInterval);
					} else {
						diffTime--;
						let day = parseInt(diffTime / 60 / 60 / 24);
						let hours = parseInt((diffTime / 60 / 60) % 24);
						let minutes = parseInt((diffTime / 60) % 60);
						let seconds = parseInt(diffTime % 60);
						this.countDown.day = day;
						this.countDown.hour = hours;
						this.countDown.minute = minutes;
						this.countDown.second = seconds;
					}
				}, 1000);
			} else if (this.acState == 2) {
				let diffTime = (end.getTime() - now.getTime()) / 1000;
				this.secInterval = setInterval(() => {
					if (diffTime == 0) {
						//倒计时结束，清除倒计时
						this.getInfo();
						clearInterval(this.secInterval);
					} else {
						diffTime--;
						let day = parseInt(diffTime / 60 / 60 / 24);
						let hours = parseInt((diffTime / 60 / 60) % 24);
						let minutes = parseInt((diffTime / 60) % 60);
						let seconds = parseInt(diffTime % 60);
						this.countDown.day = day;
						this.countDown.hour = hours;
						this.countDown.minute = minutes;
						this.countDown.second = seconds;
					}
				}, 1000);
			}
		},
		//处理签到天数
		countDay() {
			let { continueNum, integralPerSign, memberSignInfoList } = this.signInfo;
			let now = new Date();
			let nowIdx = 0;
			let contiSignCount = 0;

			//在数组中找到当天的下标
			this.signContiDay = new Array(this.signInfo.memberSignInfoList.length).fill({
				day: 0,
				isSignIn: 0
			});
			this.signInfo.memberSignInfoList.some((i, index) => {
				let day = new Date(i.date.replace(/-/g, '/'));
				if (day.getFullYear() == now.getFullYear() && day.getMonth() == now.getMonth() && day.getDate() == now.getDate()) {
					nowIdx = index;
					return false;
				}
			});
			//如果有连续签到奖励，则计算当天之前连续签到几天
			if (continueNum > 0) {
				for (let i = nowIdx; i >= 0; i--) {
					if (this.signInfo.memberSignInfoList[i].signRecordState == 2) {
						contiSignCount++;
						continue;
					} else {
						break;
					}
				}
				//如果连续签到达到要求的天数则--
				if (contiSignCount == continueNum) {
					this.isReachContiBonus = true;
				} else if (contiSignCount > continueNum) {
					//如果已经完成连续签到，下一次签到重新开始算
					contiSignCount = contiSignCount - continueNum;
					this.isReachContiBonus = false;
				}
				this.signCountTotal = contiSignCount;
			}

			// 如果活动天数大于8天,则另外处理(大于8天,则需要用swiper滑动展示)
			if (memberSignInfoList.length > 8) {
				this.current = nowIdx < 8 ? 0 : parseInt((nowIdx + 1) / 8); //根据现在时间决定swiper的current
				this.formatMutipleDay();
			}
		},

		// 如果活动天数大于8天,则另外处理(大于8天,则需要用swiper滑动展示)
		formatMutipleDay() {
			let { memberSignInfoList } = this.signInfo;
			let splt = 8;
			let multiArr = [];
			let init = 0;
			let count = Math.ceil(memberSignInfoList.length / splt);
			for (let i = 0; i < count; i++) {
				multiArr.push(memberSignInfoList.slice(init, init + splt));
				init += splt;
			}
			this.signMutipleDay = multiArr;
		},

		hideMask() {
			this.isHideMask = true;
		},
		openRule() {
			this.$refs.rule.open();
		},
		//去签到
		goSign() {
			let source = 0;
			//app-2-start
			// #ifdef APP-PLUS
			if (uni.getDeviceInfo().platform === 'ios') {
				source = 4;
			} else if (uni.getDeviceInfo().platform === 'android') {
				source = 3;
			}
			// #endif
			//app-2-end

			//#ifdef H5
			source = 2;
			//#endif
			//wx-2-start
			//#ifdef MP-WEIXIN
			source = 5;
			//#endif
			//wx-2-end
			let param = {
				url: 'v3/promotion/front/sign/activity/doSign',
				method: 'POST',
				data: {
					source,
					signActivityId: this.signInfo.signActivityId
				}
			};
			let that = this;
			this.$request(param).then((res) => {
				if (res.state == 200) {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					});
					this.getInfo();
					this.getUserPoints();
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					});
					this.getInfo();
				}
			});
		},
		goShare() {
			// #ifdef H5
			if (this.isWeiXinBrower) {
				this.share_model = true;
			} else {
				this.showWeiXinBrowerTip = true;
			}
			// #endif

			// #ifdef MP-WEIXIN || APP-PLUS
			this.share_model = true;
			// #endif
		},

		sldShareBrower(type) {
			this.showWeiXinBrowerTip = true;
			this.share_model = false;
			this.$WXBrowserShareThen(type, {
				title: this.signInfo.share.shareTitle,
				desc: this.signInfo.share.shareDesc,
				link: getApp().globalData.apiUrl + 'standard/signIn/signIn',
				imgUrl: this.signInfo.share.shareImgUrl
			});
		},

		sldShare(type, scene) {
			let shareData = {};
			if (type == 0) {
				shareData.href = getApp().globalData.apiUrl + 'standard/signIn/signIn';
				shareData.title = this.signInfo.share.shareTitle;
				shareData.summary = this.signInfo.share.shareDesc;
				shareData.imageUrl = this.signInfo.share.shareImgUrl;
			} else if (type == 2) {
				shareData.imageUrl = '';
			}
			this.$weiXinAppShare(type, scene, shareData);
			this.closeShareModel(); //关闭分享
		},
		closeShareModel() {
			this.share_model = false;
			this.showWeiXinBrowerTip = false;
			this.poster = false;
		}
	}
};
</script>

<style lang="scss" scoped>
page {
}

.main_content2 {
	width: 750rpx;
	margin: 0 auto;
	background-repeat: no-repeat;
	background-position: center center;
	background-size: 100% 100%;
	position: relative;
	height: 100vh;

	.top_bar {
		width: 750rpx;
		// height: 178rpx;
		padding-top: 42rpx;
		/* #ifdef APP-PLUS||MP */
		padding-top: calc(var(--status-bar-height) + 42rpx);
		/* #endif */
		padding-right: 20rpx;
		width: 750rpx;
		z-index: 50;

		.top_header {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.top_header_left {
				padding-left: 20rpx;
				width: 59rpx;

				image {
					width: 17rpx;
					height: 29rpx;
				}
			}

			.top_header_cen {
				margin: 0 50rpx;
				font-size: 36rpx;
				font-family: PingFang SC;
				color: #ffffff;
			}

			.top_white_space {
				width: 30rpx;
				height: 29rpx;
				padding-right: 20rpx;
			}
		}

		.top_category {
			margin-top: 40rpx;

			.cate_wrap {
				display: -webkit-box;
				align-items: center;
				overflow-x: scroll;

				.category_item {
					color: #ffffff;
					padding: 0 20rpx;
					margin-left: 20rpx;
					height: 54rpx;

					&.boWhite {
						border-bottom: 4rpx solid #fff;
					}
				}
			}
		}
	}

	.mid_banner {
		padding: 24rpx;

		.banner_tip {
			margin-top: 20rpx;
			width: 100%;
			position: relative;
			height: 69rpx;

			.s_sign_tip {
				position: absolute;
				top: 0;
				right: -24rpx;
				width: 189rpx;
				height: 69rpx;
				background: rgba(0, 0, 0, 0.1);
				border-bottom-left-radius: 35rpx;
				border-top-left-radius: 35rpx;
				display: flex;
				justify-content: center;
				align-items: center;

				text {
					font-size: 30rpx;
					font-family: PingFang;
					font-weight: bold;
					color: #fff;
					opacity: 0.9;
				}

				image {
					width: 37rpx;
					height: 34rpx;
					margin-right: 10rpx;
				}
			}
		}

		.banner_b_title {
			margin-top: 26rpx;
			text-align: center;

			text {
				font-size: 98rpx;
				font-family: SourceHanSerifCN;
				font-weight: 800;
				color: #ffffff;
				text-shadow: #ff771e 2rpx 12rpx 2rpx;
			}
		}

		.banner_s_title {
			margin-top: 30rpx;
			display: flex;
			align-items: center;

			image {
				width: 178rpx;
				height: 10rpx;
			}

			text {
				font-size: 26rpx;
				font-family: SourceHanSansCN;
				font-weight: 500;
				color: #ffffff;
				margin: 0 10rpx;
			}
		}

		.banner_time {
			margin-top: 36rpx;
			color: #fff;

			.banner_time_tip {
				font-size: 26rpx;
				font-family: PingFang;
				font-weight: bold;
				text-align: center;
			}

			.banner_time_count {
				margin-top: 10rpx;
				font-size: 44rpx;
				font-family: PingFang;
				text-align: center;
				font-weight: bold;

				text {
					font-size: 28rpx;
				}
			}
		}
	}

	.sign_ac_con {
		padding: 42rpx 24rpx 0;
		margin: 0 auto;
		width: 700rpx;
		height: 720rpx;
		background: #ffffff;
		box-shadow: 0px 10rpx 26rpx 0px rgba(255, 130, 25, 0.18);
		border-radius: 20rpx;
		position: relative;

		.ac_con_top {
			.cur_int {
				text-align: center;
				font-size: 36rpx;
				font-family: PingFang;
				font-weight: 500;
				color: #2d2d2d;

				text {
					color: #ff8118;
				}
			}

			.cur_int_tip {
				margin-top: 27rpx;
				font-size: 26rpx;
				font-family: PingFang;
				font-weight: bold;
				color: #000000;
				opacity: 0.3;
				text-align: center;
			}
		}

		.ac_con_bottom {
			margin-top: 30rpx;
			height: 500rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
		}

		.sign_ac_day {
			width: 100%;

			.sign_ac_day_line1 {
				display: flex;
				flex-wrap: wrap;
				justify-content: space-around;
				margin-bottom: 20rpx;
			}

			.day_bonus_img {
				display: flex;
				flex-direction: column;
				align-items: center;

				image {
					width: 90rpx;
					height: 90rpx;
				}

				text {
					margin-top: 10rpx;
					font-size: 24rpx;
					font-family: PingFang;
					font-weight: 500;
					color: #333333;
				}
			}
		}

		.sign_ac_but {
			width: 580rpx;
			height: 80rpx;
			white-space: pre-wrap;
			border-radius: 40rpx;
			text-align: center;
			line-height: 80rpx;
			font-size: 34rpx;
			font-family: PingFang;
			font-weight: 500;
			color: #ffffff;
			margin: 20rpx auto 0;

			&.sign_on {
				background: #ff6f1a;
				box-shadow: 0px 6rpx 30rpx 0px rgba(170, 170, 170, 0.18);
			}

			&.sign_off {
				background: #999999;
			}

			&.sign_al {
				border: 1px solid #f65a0e;
				box-shadow: 0px 3px 15px 0px rgba(170, 170, 170, 0.18);
				color: #f65a0e;
			}
		}

		.sign_fini {
			margin: 20rpx;
			display: flex;
			flex-direction: column;
			text-align: center;
			font-size: 24rpx;
			font-family: PingFang;
			font-weight: 500;
			color: #ff6f1a;
		}
	}

	.sign_ac_bottom {
		position: absolute;
		width: 750rpx;
		margin-top: -100rpx;
		left: -26rpx;

		bottom: -150rpx;
		height: 230rpx;
		background-repeat: no-repeat;
		background-size: 100% 100%;
		background-position: center center;

		.share_con {
			position: absolute;
			bottom: 34rpx;
			right: 42rpx;

			.share_con_top {
				display: flex;
				align-items: center;

				text {
					font-size: 56rpx;
					font-style: italic;
					font-weight: bold;
					color: #fff;
					text-shadow: #ff771e 4rpx 0rpx 6rpx;
					margin-right: 20rpx;
					letter-spacing: 6rpx;
				}

				.share_but {
					width: 130rpx;
					height: 40rpx;
					background: #ffffff;
					border-radius: 20rpx;
					display: flex;
					align-items: center;
					justify-content: space-evenly;
					font-size: 28rpx;
					font-family: PingFang;
					font-weight: 500;
					color: #ff8016;

					image {
						width: 30rpx;
						height: 24rpx;
					}
				}
			}

			& > text {
				font-size: 24rpx;
				font-family: SourceHanSansCN;
				font-weight: 400;
				color: rgba(255, 255, 255, 0.8);
			}
		}
	}

	.sign_ac_off {
		width: 600rpx;
		height: 740rpx;
	}

	.golden_bg {
		width: 600rpx;
		height: 520rpx;

		margin: 10rem auto;
		background-position: center center;
		background-repeat: no-repeat;
		background-size: cover;

		image {
			width: 101rpx;
			height: 101rpx;
			margin-bottom: 42rpx;
		}

		.signAc_off {
			text-align: center;
			font-size: 48rpx;
			font-family: PingFang SC;
			font-weight: bold;
			color: #ff7c19;
			margin-bottom: 95rpx;
		}

		.signAc_off_but {
			white-space: pre-wrap;
			text-align: center;
			line-height: 68rpx;
			font-size: 34rpx;
			color: #ff7c19;
			width: 300rpx;
			height: 68rpx;
			border: 1px solid #ff8f21;
			border-radius: 34rpx;
		}
	}
}

.qr_code {
	border-radius: 20rpx;
	background-color: #fff;
	padding: 20rpx;
	view {
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: rgba(102, 102, 102, 1);
		text-align: center;
	}
}

.swiper-box {
	height: 330rpx !important;
}

.sign_but_rule_con {
	position: absolute;
	bottom: 10rpx;
	width: 100%;
	display: flex;
	justify-content: center;
}

.sign_but_rule {
	width: 320rpx;
	height: 80rpx;
	background: #ff7c19;
	border-radius: 40rpx;
	line-height: 80rpx;
	text-align: center;
	color: #fff;
	margin: 30rpx auto;
}

.sign_rule {
	width: 600rpx;
	height: 760rpx;
	background: #ffffff;
	box-shadow: 0px 0px 27rpx 6rpx rgba(85, 85, 85, 0.3);
	border-radius: 20rpx;
	padding-top: 36rpx;
	padding-left: 36rpx;
	padding-right: 36rpx;
	position: relative;

	.sign_rule_title {
		text-align: center;
		font-size: 36rpx;
		font-family: PingFang;
		font-weight: 500;
		color: #ff7c19;
	}

	.sign_scroll_rule {
		height: 500rpx;
	}

	.sign_rule_one {
		margin-top: 38rpx;
		display: flex;
		align-items: center;

		text {
			font-size: 28rpx;
			font-family: PingFang;
			font-weight: 500;
			color: #ff7c19;
		}

		image {
			margin-right: 10rpx;
			width: 39rpx;
			height: 35rpx;
		}
	}

	.sign_rule_one_text {
		margin-top: 21rpx;
		font-size: 24rpx;
		font-family: PingFang;
		font-weight: 400;
		color: #333333;
		line-height: 44rpx;
		word-break: break-all;

		.scrollView {
			max-height: 450rpx;
		}
	}

	.sign_but_rule_already {
		width: 320rpx;
		height: 80rpx;
		border: 2rpx solid #ff7c19;
		color: #ff7c19;
		line-height: 80rpx;
		text-align: center;
		margin: 30rpx auto;
		border-radius: 40rpx;
	}
}

.sign_ac_off {
	width: 600rpx;
	height: 740rpx;
}

.swiper-box {
	height: 330rpx;
}

.opa_class {
	opacity: 0.5;
}

.share_model {
	width: 750rpx;
	height: 100%;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	margin: 0 auto;
	background: rgba(0, 0, 0, 0.6);
	z-index: 100;
}

.share_model_list {
	display: flex;
	justify-content: space-around;
	padding: 0 50rpx;
	box-sizing: border-box;
	position: fixed;
	bottom: 150rpx;
	z-index: 110;
	width: 750rpx;

	.share_model_pre {
		display: flex;
		flex-direction: column;
		align-items: center;
		background: transparent;
		border-radius: 0;
		height: auto;
		line-height: auto;

		&::after {
			border-width: 0;
		}

		image {
			width: 105rpx;
			height: 105rpx;
		}

		text {
			font-size: 24rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ffffff;
			line-height: 36rpx;
			margin-top: 30rpx;
		}
	}
}

.share_model_close {
	width: 46rpx;
	height: 46rpx;
	bottom: 60rpx;
	position: fixed;
	z-index: 110;
	left: 0;
	right: 0;
	margin: 0 auto;

	image {
		width: 46rpx;
		height: 46rpx;
	}
}

/* 微信浏览器分享提示 start */
.wx_brower_share_mask {
	width: 750rpx;
	height: 100vh;
	background-color: rgba(0, 0, 0, 0.45);
	position: fixed;
	z-index: 99999;
	top: 0;
}

.wx_brower_share_top_wrap {
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: flex-end;
	align-items: flex-start;
	margin-top: 150rpx;
}

.wx_brower_share_top_wrap .wx_brower_share_img {
	width: 450rpx;
	height: 150rpx;
	margin-right: 80rpx;
}

.share_h5 {
	width: 100% !important;
	height: 100% !important;
}

uni-image > img {
	opacity: unset;
	object-fit: contain;
}

.share_h5_operate_img {
	width: 440rpx !important;
	height: 120rpx !important;
}

.share_h5_close_img {
	width: 50rpx !important;
	height: 50rpx !important;
}

.share_h5_img_bottom {
	width: 50rpx !important;
	height: 200rpx !important;
}

/* 微信浏览器分享提示 end */
</style>
