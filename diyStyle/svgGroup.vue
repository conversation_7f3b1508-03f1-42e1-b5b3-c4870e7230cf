<!--
 *  svg 图标组
 *  @zoucb 2023-3-27
 *  用于颜色需要动态变换的svg，新增的话就自定义type和进入svg代码
 **-->
<template>
	<view class="svgGroup">
		<image :src="svg_image" :style="{width:width+px,height:height+px}" mode='aspectFit'></image>
	</view>
</template>

<script>
	import {
		weBtoa
	} from '@/utils/base.js'
	import {
		svg_list
	} from './svg.js'
	export default {
		props: {
			type: {
				type: String,
				default: ''
			},
			color: String,
			width: Number | String,
			height: Number | String,
			px: {
				type: String,
				default: 'px'
			},
		},

		data() {
			return {
				svg_image_s: ''
			}
		},

		watch: {
			type: {
				handler(nv, ov) {
					this.type = nv
				},
				deep: true

			}
		},

		computed: {
			//使用svg转base64来兼容小程序
			svg_image() {
				const svg = svg_list[this.type]
				let newSvg = svg
				if (/<svg /.test(svg)) { // 先简单判断是一下否是一个svg
					if (this.color) {
						newSvg = svg.replace(/<svg /, `<svg fill="${this.color}" `); // 无默认色
					}
					return 'data:image/svg+xml;base64,' + weBtoa(newSvg); // 替换完之后再组合回去
				}
			}
		},

		mounted() {
			// this.svg_image = this.getSvg()
		},

		methods: {
			//使用svg转base64来兼容小程序
			getSvg() {
				const svg = svg_group[this.type]
				let newSvg = svg
				if (/<svg /.test(svg)) { // 先简单判断是一下否是一个svg
					if (this.color) {
						newSvg = svg.replace(/<svg /, `<svg fill="${this.color}" `); // 无默认色
					}
					return 'data:image/svg+xml;base64,' + weBtoa(newSvg); // 替换完之后再组合回去
				}
			}
		}
	}
</script>

<style scoped>
	.svgGroup {
		display: flex;
		justify-content: center;
		align-items: center;
		fill: #333;
	}

	image {
		margin: unset !important;
	}

	svg {}
</style>