<template>
    <view :style="mix_diyStyle" class="page">
        <view class="main_graRe" :style="'background-image:url(' + imgUrl + 'default_bg.jpg)'">
            <KTabbar :title="title" :showLeft="true" :bgColor="'transparent'" :bgImg="imgUrl + 'default_bg.jpg'" />
            <view class="title_part">
                <input class="input" autofocus :placeholder="$L('请输入文章标题不超过20字')" maxlength="20" v-model="live_name" />
            </view>
            <!-- 正文内容 -->
            <view class="content_part">
                <textarea class="content_con" :placeholder="$L('请输入正文内容~')" placeholder-class="placeholder" maxlength="500" v-model="video_desc"></textarea>
                <!-- 字数统计 -->
                <view class="text_count">{{ textCount }}/500</view>
            </view>
            <!--视频、 图片选择-->
            <view class="select_meta">
                <view class="video_select">
                    <view v-if="JSON.stringify(upload_video) == '{}' && video_flag !== '200'" class="cover" @tap="openspecModel">
                        <uv-icon name="plus" color="rgba(0,0,0,0.5)" size="28"></uv-icon>
                        <text class="cover_tip">{{ $L('上传短视频') }}</text>
                    </view>
                    <view class="cover" v-else>
                        <view class="cover_image" ref="videoWrapHls">
                            <video :disabled="false" :controls="false" :src="upload_video.video_url" id="myVideo" class="my_video"></video>
                            <view class="htz-image-upload-Item-video-fixed" @click="previewVideoSrcFlag = true"></view>
                            <view class="close_img" @click.stop="delVideo(index, item)">
                                <uv-icon class="icon" name="close-circle" size="18" color="#FFF"></uv-icon>
                            </view>
                        </view>
                    </view>
                </view>
                <view class="pic_part_sel">
                    <view class="cover" @tap="uploadCover" v-if="JSON.stringify(cover) == '{}'">
                        <uv-icon name="plus" color="rgba(0,0,0,0.5)" size="28"></uv-icon>
                        <text class="cover_tip">视频封面</text>
                    </view>
                    <view
                        class="cover_image"
                        v-if="JSON.stringify(cover) != '{}' && cover.video_url != ''"
                        :style="{ backgroundImage: 'url(' + cover.video_url + ')' }"
                        @click.stop="previewImage"
                    >
                        <view class="close_img" @click.stop="delCover(index, item)">
                            <uv-icon class="icon" name="close-circle" size="18" color="#FFF"></uv-icon>
                        </view>
                    </view>
                </view>
            </view>
            <!-- 话题 -->
            <view class="avator">
                <view class="left">
                    <text class="con">{{ $L('参与话题') }}</text>
                </view>
                <view class="right" @click="selectTopic">
                    <view class="picker">{{ $L('选择话题') }}</view>
                    <image class="arrow_r" :src="imgUrl + 'svideo/mem_arrow_r.png'"></image>
                </view>
            </view>
            <!-- 选中的话题 -->
            <view class="tag_part" v-if="selectTheme.themeId != undefined && selectTheme.themeId">
                <view class="tag">
                    <text class="icon">#</text>
                    <text class="tag_text">{{ selectTheme.themeName }}</text>
                    <image class="close" :src="imgUrl + 'input_clear.png'" @click="selectTheme = {}"></image>
                </view>
            </view>
            <view class="live_btn">
                <text @tap="$frequentyleClick(startLive)" class="live_btn_text btn_enable">{{ $L('发布') }}</text>
            </view>

            <view class="preview-full" v-if="previewVideoSrcFlag">
                <video :autoplay="true" :src="upload_video.video_url" :show-fullscreen-btn="false">
                    <cover-view class="preview-full-close" @click="previewVideoSrcFlag = false">×</cover-view>
                </video>
            </view>
        </view>
    </view>
</template>

<script>
import KTabbar from '@/components/ktabbar.vue';
import { mapState } from 'vuex';
export default {
    components: {
        KTabbar
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            title: this.$L('发布短视频'),
            //图片地址
            bgStyle: 'background-size:cover;background-position:top center;background-repeat: no-repeat;',
            //背景图片样式
            cover: {},
            //封面信息
            upload_video: {},
            //上传的短视频
            virtual_click_num: '',
            //虚拟观看数
            virtual_like_num: '',
            //虚拟人气数
            live_name: '',
            //标题
            video_desc: '',
            //视频简介
            theme_list: [],
            //推荐主题
            selectTheme: {},

            //平台设置信息
            video_id: '', //短视频id，编辑短视频用
            type: '',
            roleType: 1, // 默认会员 1   2：商家
            storeId: '',
            onOff: true,
            video_flag: '0', //上传成功标志 0初始值 200成功 用来判断遮罩
            videoCon: null,
            firstOpen: true, // //是否第一次加载页面,
            previewVideoSrcFlag: false
        };
    },

    props: {},

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function () {
        if (this.$Route.query.video_id) {
            this.video_id = this.$Route.query.video_id;
            this.getVideoDetail();
        }
        this.roleType = this.$Route.query.roleType;
        this.storeId = this.$Route.query.storeId;
        if (this.firstOpen) {
            // this.initData();
        }
    },

    onShow: function () {
        if (!this.firstOpen) {
            // this.initData();
        } else {
            this.firstOpen = false;
        }
    },
    onUnload() {
        //wx-1-start
        // #ifdef MP-WEIXIN
        let context = wx.createVideoContext('myVideo');
        context.exitPictureInPicture();
        // #endif
        //wx-1-end
    },
    onHide() {
        //wx-2-start
        // #ifdef MP-WEIXIN
        let context = wx.createVideoContext('myVideo');
        context.exitPictureInPicture();
        // #endif
        //wx-2-end
    },
    computed: {
        ...mapState(['userInfo']),
        textCount() {
            return this.video_desc.length || 0;
        }
    },
    methods: {
        // #ifndef APP-PLUS
        //short_video3-end
        openspecModel() {
            this.uploadVideo();
        },
        //short_video4-start
        // #endif
        initData() {
            this.getSetting();
        },

        // 返回上级页面
        goBack() {
            this.$Router.back(1);
        },
        //开始直播
        startLive() {
            let { live_name, cover, upload_video, video_desc, selectTheme, setting, video_id } = this;
            let that = this;
            if (live_name.trim() == '') {
                uni.showToast({
                    title: this.$L('请输入标题'),
                    icon: 'none'
                });
                return;
            }

            if (JSON.stringify(upload_video) == '{}') {
                uni.showToast({
                    title: this.$L('请上传短视频'),
                    icon: 'none'
                });
                return;
            }

            if (JSON.stringify(cover) == '{}') {
                uni.showToast({
                    title: this.$L('请上传封面图片'),
                    icon: 'none'
                });
                return;
            }

            // if (video_desc == '') {
            //     uni.showToast({
            //         title: this.$L('请输入视频简介'),
            //         icon: 'none'
            //     });
            //     return;
            // }
            let param = {};
            param.data = {};
            param.method = 'POST';
            param.data = {
                introduction: video_desc,
                themeId: selectTheme.themeId,
                videoName: live_name,
                videoPath: upload_video.video_path,
                type: 1,
                videoThemeImage: cover.video_path,
                width: cover.width,
                height: cover.height
            };
            if (video_id != '') {
                param.url = 'v3/video/front/video/editVideo';
                param.data.videoId = video_id;
            } else {
                param.url = 'v3/video/front/video/releaseVideo';
            }
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    uni.showToast({
                        title: this.video_id ? that.$L('编辑成功！') : that.$L('发布成功！'),
                        icon: 'none',
                        duration: 500
                    }).then(() => {
                        // this.$Router.replace('/extra/user/my')
                        uni.removeStorage({ key: 'selGoods' });

                        // #ifndef MP-TOUTIAO
                        let pages = getCurrentPages();
                        let flag = true;
                        let preIndex = '';
                        pages.forEach((item, index) => {
                            // 处理返回列表页 后退跳转层级重复问题
                            if (item.route == 'extra/user/my') {
                                if (!preIndex) {
                                    preIndex = index;
                                }
                            } else if (item.route == 'extra/svideo/svideoRelease') {
                                flag = false;
                                setTimeout(() => {
                                    uni.$emit('updateState');
                                    this.$Router.back(index - preIndex);
                                }, 1000);
                            }
                        });
                        if (flag) {
                            let prevPage = pages[pages.length - 2]; //上一个页面
                            if (prevPage) {
                                prevPage.$vm.initData();
                            }
                            setTimeout(() => {
                                uni.$emit('updateState');
                                this.$Router.back(1);
                            }, 1000);
                        }
                        // #endif

                        // #ifdef MP-TOUTIAO
                        setTimeout(() => {
                            this.$Router.back(1);
                        }, 1000);
                        // #endif
                    });
                } else {
                    this.$api.msg(res.msg);
                }
            });
        },
        //获取短视频详情
        getVideoDetail() {
            let { video_id } = this;

            let param = {};
            param.url = 'v3/video/front/video/detail';
            param.method = 'GET';
            param.data = {};
            param.data.videoId = video_id;
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    let result = res.data;
                    this.live_name = result.videoName;
                    this.video_desc = result.introduction;
                    this.selectLabel.labelId = result.labelId;
                    this.selectLabel.labelName = result.labelName;
                    this.cover.video_url = result.videoThemeImageUrl;
                    this.cover.video_path = result.videoThemeImage;
                    this.cover.height = result.height;
                    this.cover.width = result.width;
                    this.upload_video.video_url = result.videoPathUrl;
                    this.upload_video.video_path = result.videoPath;
                    this.selGoods = result.goodsList;
                    this.videoThemeImage = '';
                    this.video_flag = 200;
                }
            });
        },

        // 上传短视频
        uploadVideo() {
            let { upload_video } = this;
            let that = this;

            if (JSON.stringify(upload_video) != '{}') {
                return;
            }

            uni.chooseVideo({
                sourceType: ['album'],
                compressed: true,
                //默认压缩
                maxDuration: 60,
                camera: 'back',
                success(res) {
                    console.log('upload-video-success', res);
                    if (res.size > 52428800) {
                        uni.showToast({
                            title: that.$L('超出文件最大限制50m，请重新上传！'),
                            icon: 'none',
                            duration: 1500
                        });
                    } else {
                        setTimeout(() => {
                            uni.showLoading({
                                title: that.$L('上传中'),
                                mask: true
                            });
                        });
                        uni.uploadFile({
                            url: getApp().globalData.apiUrl + 'v3/oss/front/upload',
                            filePath: res.tempFilePath,
                            name: 'file',
                            formData: {
                                source: 'video'
                            },
                            header: {
                                Authorization: 'Bearer ' + that.userInfo.access_token
                            },
                            success: (resup) => {
                                // #ifndef MP-ALIPAY||MP-BAIDU
                                resup = JSON.parse(resup.data);
                                // #endif
                                // #ifdef MP-ALIPAY||MP-BAIDU
                                resup = resup.data;
                                // #endif
                                if (resup.state == 200) {
                                    that.video_flag = '200';
                                    that.upload_video = resup.data;
                                    that.upload_video.video_url = resup.data.url;
                                    that.upload_video.video_path = resup.data.path;
                                }
                            },
                            complete: (res) => {
                                uni.hideLoading();
                                // if (JSON.parse(res.data).state != 200) {
                                if (!res.data && res.errMsg) {
                                    uni.showToast({
                                        title: res.errMsg ? res.errMsg : JSON.parse(res.data).msg,
                                        icon: 'none',
                                        duration: 1000
                                    });
                                } else {
                                    //app-5-start
                                    // #ifdef APP-PLUS
                                    that.$refs.specModel.close();
                                    // #endif
                                    //app-5-end
                                    uni.showToast({
                                        title: that.$L('上传成功'),
                                        duration: 1000
                                    });
                                }
                            }
                        });
                    }
                },
                complete(res) {
                    console.log(res, 'res');
                }
            });
        },

        // 选封面
        uploadCover() {
            let { cover } = this;
            let that = this;
            let key = uni.getStorageSync('token');
            uni.chooseImage({
                count: 1,
                sizeType: ['original', 'compressed'],
                //可选择原图或压缩后的图片
                sourceType: ['album', 'camera'],
                //可选择性开放访问相册、相机
                success: (res) => {
                    uni.uploadFile({
                        url: getApp().globalData.apiUrl + 'v3/oss/front/upload',
                        filePath: res.tempFilePaths[0],
                        name: 'file',
                        formData: {
                            source: 'goods'
                        },
                        header: {
                            Authorization: 'Bearer ' + that.userInfo.access_token
                        },
                        success: (resup) => {
                            // #ifndef MP-ALIPAY||MP-BAIDU
                            resup = JSON.parse(resup.data);
                            // #endif
                            // #ifdef MP-ALIPAY||MP-BAIDU
                            resup = resup.data;
                            // #endif

                            if (resup.state == 200) {
                                // //获取到image-cropper实例
                                that.cover = resup.data;
                                that.cover.video_path = resup.data.path;
                                that.cover.video_url = resup.data.url;
                                that.cover.height = resup.data.height;
                                that.cover.width = resup.data.width;
                            }
                        }
                    });
                }
            });
        },

        //删除封面
        delCover() {
            this.cover = {};
        },

        //删除视频
        delVideo() {
            this.upload_video = {};
            this.video_flag = 0;
            // #ifdef H5
            this.player_video.src('');
            // #endif
        },

        //编辑内容
        editData(e, type) {
            this[type] = e.detail.value;
        },

        // 选择话题
        selectTopic() {
            this.$Router.push({ path: '/extra/topic/list', query: { action: 'select' } });
        },
        setTopic(value) {
            console.log('选择的话题', value);
            this.selectTheme = value;
        },
        // 获取设置信息
        getSetting() {
            let param = {};
            param.url = 'v3/video/front/video/setting/getSettingList';
            param.method = 'GET';
            param.data = {};
            param.data.str = 'bind_goods_num,member_bind_goods';
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    let result = res.data;
                }
            });
        },
        //图片预览
        previewImage(e) {
            const url = this.cover.video_url || '';
            if (url) {
                uni.previewImage({
                    urls: [url]
                });
            }
        }
    }
};
</script>
<style lang="scss">
page {
    background: #f5f5f5;
    .placeholedr {
        font-size: 26rpx;
        color: rgba(0, 0, 0, 0.5);
        z-index: 1;
        zoom: 1;
    }
}

.preview-full {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    z-index: 1002;

    video {
        width: 100%;
        height: 100%;
        z-index: 10;
    }

    .preview-full-close {
        position: fixed;
        left: 32rpx;
        top: 100rpx;
        width: 80rpx;
        height: 80rpx;
        line-height: 60rpx;
        text-align: center;
        z-index: 1003;
        /* 	background-color: #808080; */
        color: #fff;
        font-size: 65rpx;
        font-weight: bold;
    }
}
.main_graRe {
    width: 100%;
    min-height: 100vh;
    margin: 0;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    padding-bottom: env(safe-area-inset-bottom);
    background-attachment: fixed;

    .title_part {
        width: 92%;
        margin: 0 auto;
        padding: 40rpx 0rpx 20rpx 0rpx;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        box-sizing: content-box;
        border-bottom: 0.5px solid;
        border-color: rgba(0, 0, 0, 0.1);
        .input {
            width: 100%;
            color: #2d2d2d;
            font-size: $fs-base;
            border: none;
            outline: none;
        }
        textarea {
            width: 100%;
            padding-left: 11rpx;
            color: #2d2d2d;
            font-size: 30rpx;
        }
    }

    .content_part {
        width: 92%;
        margin: 0 auto;
        padding: 40rpx 0rpx 10rpx 0rpx;
        box-sizing: border-box;
        .content_con {
            height: 500rpx;
            font-size: $fs-base;
            line-height: 1.4;
            width: 100%;
        }
        .text_count {
            margin-top: 10rpx;
            width: 100%;
            text-align: right;
            font-size: $fs-m;
            color: #949494;
        }
    }
    .select_meta {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: flex-start;
        width: 92%;
        margin: 0 auto;
        box-sizing: border-box;
        column-gap: 20rpx;
        padding: 20rpx 0;
        .video_select {
            width: 32%;
            aspect-ratio: 1 / 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            .cover {
                width: 100%;
                height: 100%;
                background: rgb(255, 255, 255);

                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                position: relative;
                .cover_icon {
                    width: 80rpx;
                    height: 80rpx;
                }
                .cover_tip {
                    font-size: $fs-m;
                    color: #949494;
                    margin-top: 10rpx;
                }
            }
            .htz-image-upload-Item-video-fixed {
                position: absolute;
                top: 0;
                left: 0;
                bottom: 0;
                width: 100%;
                height: 100%;
                z-index: 4;
            }
            .cover_image {
                width: 100%;
                height: 100%;
                background: rgb(0, 0, 0, 0.3);
                position: relative;
                .my_video {
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                    z-index: 3;
                }
                .close_img {
                    width: 50rpx;
                    height: 50rpx;
                    position: absolute;
                    right: 0;
                    top: 0rpx;
                    z-index: 5;
                    border-radius: 0rpx 0 0rpx 50rpx;
                    background-color: #9794ff;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    .uv-icon {
                        margin-top: -8rpx;
                        margin-right: -8rpx;
                    }
                }
            }
        }
        .pic_part_sel {
            display: flex;
            overflow: auto;
            width: 32%;
            aspect-ratio: 1 / 1;
            flex-wrap: wrap;
            row-gap: 20rpx;
            column-gap: calc(8% / 3);
            .cover {
                width: 100%;
                height: 100%;
                background: rgb(255, 255, 255);
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                position: relative;
                .cover_icon {
                    width: 40rpx;
                    height: 40rpx;
                }

                .cover_tip {
                    font-size: $fs-m;
                    color: #949494;
                    margin-top: 10rpx;
                }
            }

            .cover_image {
                position: relative;
                width: 100%;
                height: 100%;
                background: rgb(0, 0, 0, 0.3);
                background-size: cover;
                background-position: center;
                background-repeat: no-repeat;
                .close_img {
                    width: 50rpx;
                    height: 50rpx;
                    position: absolute;
                    right: 0;
                    top: 0rpx;
                    z-index: 5;
                    border-radius: 0rpx 0 0rpx 50rpx;
                    background-color: #9794ff;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    .uv-icon {
                        margin-top: -8rpx;
                        margin-right: -8rpx;
                    }
                }
            }
        }
    }

    .avator {
        width: 92%;
        margin: 0 auto;
        padding: 40rpx 0rpx 20rpx 0rpx;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        border-top: 0.5px solid rgba(0, 0, 0, 0.1);
        margin-top: 25rpx;
        .left {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;

            .con {
                color: #2d2d2d;
                font-size: $fs-base;
            }
        }

        .right {
            min-width: 300rpx;
            display: flex;
            flex-direction: row;
            justify-content: flex-end;
            align-items: center;
            height: 100%;
            overflow: hidden;
            .picker {
                color: #949494;
                font-size: 24rpx;
                text-align: right;
            }

            .arrow_r {
                width: 40rpx;
                height: 42rpx;
                margin-left: 6rpx;
            }
        }
    }
    .tag_part {
        width: 92%;
        margin: 0 auto;
        padding: 0rpx 0rpx 20rpx;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .tag {
            display: inline-block;
            padding: 0rpx 0 0 20rpx;
            height: 50rpx;
            border-radius: 25rpx;
            color: #fff;
            font-size: $fs-base;
            background: var(--color_video_main);
            background: linear-gradient(141.55deg, #9794ff 0%, #ffc7c8 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            .icon {
                height: 30rpx;
                width: 30rpx;
                line-height: 30rpx;
                text-align: center;
                margin-right: 5px;
                border-radius: 50%;
                color: #9794ff;
                background-color: #fff;
            }
            .close {
                width: 46rpx;
                height: 46rpx;
                margin-left: 10rpx;
            }
        }
    }

    .live_btn {
        width: 92%;
        margin: 0 auto;
        margin-top: 80rpx;
        .btn_enable {
            background: #494949 !important;
            box-shadow: 0px 3px 10px 0px #aaaaaa;
        }

        .live_btn_text {
            width: 100%;
            font-size: $fs-base;
            color: #fff;
            line-height: 90rpx;
            height: 90rpx;
            background: #aaa;
            border-radius: 45rpx;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
        }
    }

    .live_user_tab_content {
        max-height: 900rpx;
        overflow: auto;
    }
    .desc_part {
        background: #fff;
        border-radius: 15rpx;
        margin: 26rpx 20rpx;
        box-sizing: border-box;
        padding: 20rpx;

        .desc_title {
            display: flex;
            align-items: center;

            .title {
                color: #2d2d2d;
                font-size: 32rpx;
            }
        }

        .desc_con {
            color: #949494;
            font-size: 28rpx;
            line-height: 32rpx;
            margin-top: 10rpx;
            width: 100%;
            padding-left: 12rpx;
            height: 80rpx;
        }
    }
}
</style>
