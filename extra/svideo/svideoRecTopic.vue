<template>
    <view class="page" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }">
        <ktabbar :title="themeInfo.themeName" />
        <!-- <view class="fixed_top_status_bar">
            <view class="top_status_bar_seat" :style="{ height: topBarHeight }"></view>
            <view class="custom-nav-taber" :style="{ backgroundImage: `url(${themeInfo.image})` }">
                <view class="nav_bar">
                    <image class="back_icon" :src="imgUrl + 'to_right.png'" mode="" @click="navBack" />
                    <view class="title white">{{ themeInfo.themeName }}</view>
                </view>
            </view>
        </view> -->

        <view class="live_list">
            <!-- 短视频列表item -->
            <block v-for="(item, index) in videoList" :key="index">
                <videoItem :detail="item" class="video_item"/>
            </block>
            <!-- <listVideoItem :videoList="videoList" :bgStyle="bgStyle" :listFavIcon="imgUrl + 'svideo/fav.png'" :listPlayNum="imgUrl + 'svideo/zx_play.png'" /> -->
            <!-- 数据加载完毕 -->
            <dataLoaded :showFlag="!hasmore && videoList.length > 0" />
            <!-- 数据加载中 -->
            <dataLoading :showFlag="hasmore && loading" />

            <!-- 页面loading -->
            <pageLoading :firstLoading="firstLoading" :loadingIcon="imgUrl + 'svideo/page_loading_icon.gif'" />

            <!-- 页面空数据 -->
            <emptyData :showFlag="!firstLoading && !videoList.length" :emptyIcon="imgUrl + 'live/live_list_empty_icon.png'" />

            <view class="empty_h"></view>
        </view>
    </view>
</template>

<script>
//短视频-推荐主题页面
import ktabbar from '@/components/ktabbar.vue';
import { checkPageHasMore, initNum } from '@/utils/live';
import videoItem from '@/components/videoItem.vue';
import listVideoItem from '../component/video/listVideoItem.vue';
import pageLoading from '../component/pageLoading.vue';
import emptyData from '../component/emptyData.vue';
import dataLoading from '../component/dataLoading.vue';
import dataLoaded from '../component/dataLoaded.vue';
export default {
    components: {
        ktabbar,
        videoItem,
        listVideoItem,
        pageLoading,
        emptyData,
        dataLoading,
        dataLoaded
    },
    data() {
        return {
            themeInfo: {
                image: '', // 主题图片
                themeName: '', // 主题名称
                themeId: '' // 主题ID
            },
            videoList: [], //短视频列表
            imgUrl: getApp().globalData.imgUrl, //图片地址
            hasmore: true, //是否还有数据，用于页面展示
            loading: false,
            current: 1,
            bgStyle: 'background-size:cover;background-position:center center;background-repeat: no-repeat;', //背景图片样式
            firstLoading: true, //是否初次加载，是的话展示页面居中的loading效果
            pageSize: 10
        };
    },
    computed: {
        // 获取顶部状态栏高度
        topBar() {
            return getApp().globalData.topBar;
        },
        // 获取顶部状态栏高度
        topBarHeight() {
            return uni.rpx2px(160) + 'px';
        }
    },
    //是否还有数据
    onReachBottom() {
        if (this.hasmore) {
            this.getList();
        }
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        this.theme_id = this.$Route.query.theme_id;
        this.title = this.$Route.query.theme_name || '推荐主题';
        this.getList();
    },
    methods: {
        //获取短视频列表
        getList() {
            this.loading = true;
            let { hasmore, firstLoading } = this;
            let param = {};
            param.data = {};
            param.data.pageSize = this.pageSize;
            param.data.current = this.current;
            param.data.themeId = this.theme_id;
            param.url = 'v3/video/front/video/themeDetail';
            param.method = 'GET';
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    let list = res.data.videoList;
                    this.themeInfo = res.data.themeInfo;
                    if (this.current == 1) {
                        this.videoList = list;
                    } else {
                        this.videoList = this.videoList.concat(list);
                    }

                    if (checkPageHasMore(res.data.pagination)) {
                        this.current++;
                    } else {
                        this.hasmore = false;
                        hasmore = false;
                    }
                } //初次加载的话更改状

                if (firstLoading) {
                    firstLoading = false;
                }
                this.loading = false;
                this.hasmore = hasmore;
                this.firstLoading = firstLoading;
            });
        }
    }
};
</script>
<style lang="scss">
.fixed_top_status_bar {
    .custom-nav-taber {
        position: fixed;
        z-index: 10;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        top: 0;
        // #ifdef MP
        padding-top: 98rpx;
        left: 0;
        width: 100%;
        // #endif
        // #ifdef H5
        padding-top: 80rpx;
        box-sizing: content-box;
        left: 50%;
        width: 750rpx;
        transform: translateX(-50%);
        // #endif
        background-repeat: no-repeat;
        background-position: top center;
        background-size: 100% auto;
        .nav_bar {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 64rpx;
        }
        .back_icon {
            position: absolute;
            top: 50%;
            left: 40rpx;
            transform: translateY(-50%);
            /* #ifdef MP-WEIXIN */
            width: 36rpx;
            height: 36rpx;
            /* #endif */
            /* #ifdef H5 */
            width: 40rpx;
            height: 40rpx;
            /* #endif */
            object-fit: contain;
        }
        .title {
            width: 350rpx;
            text-align: center;
            font-size: $fs-base;
            @extend .omitLine1;
            &.white {
                color: #fff;
            }
        }
    }

    .top_status_bar_seat {
        // #ifdef MP
        width: 100%;
        // #endif
        /* app-2-start */
        // #ifdef H5
        width: 750rpx;
        height: 260rpx;
        // #endif
    }
}
.page {
    width: 750rpx;
    min-height: 100vh;
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    // 背景图片固定，不滚动
    background-attachment: fixed;
    .theme_header {
        width: 100%;
        height: 300rpx;
        background-size: cover;
        background-position: center center;
        background-repeat: no-repeat;
        .theme_title {
            height: 100rpx;
            line-height: 100rpx;
            padding: 0 4%;
            font-size: 32rpx;
            font-weight: bold;
            color: #fff;
            backdrop-filter: blur(10px);
        }
    }
    .live_list {
        width: 92%;
        margin: 0 auto;
        margin-top: 50rpx;
        .video_item {
            margin-bottom: 20rpx;
        }
    }
}
.empty_h {
    height: 20rpx;
    width: 100%;
    background-color: 'transpanrent';
}
</style>
