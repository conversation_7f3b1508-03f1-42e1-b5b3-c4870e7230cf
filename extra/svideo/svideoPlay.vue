<template>
	<view :style="mix_diyStyle">
		<view v-if="loadEnd">
			<block v-if="videoList.length == 1">
				<view style="width: 750rpx; height: 100vh; overflow-y: hidden">
					<videoPlay @showVideoInfo="showVideoInfo" :id="'sldVideo_'" :video_id="videoList[0].video_id"
						:activeIndex="videoList[0].activeIndex" :author_id="videoList[0].author_id"
						:prevIndex="videoList[0].prevIndex" :nextIndex="videoList[0].nextIndex"
						:showHide="videoList[0].showHide" :preUpdateIndex="videoList[0].preUpdateIndex"></videoPlay>
				</view>

				<!-- 回放视频暂停/播放的弹层展示 -->
				<view class="video_control" v-if="(playFlag && showPauseBtn) || !playFlag" @tap="videoPlayControl">
					<image :src="showBtnIcn" :video_id="videoList[0].video_id"></image>
				</view>
			</block>
			<block v-else>
				<swiper class="swiper" vertical :interval="interval" :duration="duration"
					@animationfinish="animationfinish" @change="videoChange" :current="videoCurrent">
					<swiper-item v-for="(item, index) in videoList" :key="index">
						<view style="width: 750rpx; height: 100vh; overflow-y: hidden">
							<videoPlay @showVideoInfo="showVideoInfo" :id="'sldVideo_'" :video_id="item.video_id"
								:activeIndex="item.activeIndex" :author_id="item.author_id" :listIndex="index"
								:prevIndex="item.prevIndex" :nextIndex="item.nextIndex" :showHide="item.showHide"
								:preUpdateIndex="item.preUpdateIndex"></videoPlay>
						</view>

						<!-- 回放视频暂停/播放的弹层展示 -->
						<view class="video_control" v-if="(playFlag && showPauseBtn) || !playFlag"
							@tap="videoPlayControl">
							<image :src="showBtnIcn" :video_id="item.video_id"></image>
						</view>
					</swiper-item>
				</swiper>
			</block>
		</view>
	</view>
</template>

<script>
	import {
		checkPageHasMore
	} from '@/utils/live'
	import request from '@/utils/request'
	import videoPlay from '../component/video/videoPlay'

	export default {
		data() {
			return {
				theme_id: 0,
				//theme_id: 该主题下的所有视频
				author_id: 0,
				//author_id: 该作者下的所有视频
				type: '',
				//type: 我喜欢的视频
				video_id: 0,
				//当前短视频id
				playFlag: true,
				//视频是否播放
				showPauseBtn: false,
				//是否显示暂停按钮
				showBtnIcn: '',
				//视频播放控制层按钮图片
				activeIndex: 0,
				//当前播放的视频id
				videoUrl: [
					// 视频列表
					{
						now: ''
					}
				],
				videoId: [],
				// 所有视频id
				prevIndex: '',
				// 上个视频的id
				nextIndex: '', // 下个视频的id
				videoInfo: '',
				ShowHide: 'hide',
				preUpdateIndex: '', //是否需要更新上一个页面的数据 -- 取操作的列表下标

				current: 0,
				videoList: [],
				interval: 2000,
				duration: 500,
				pageCurrent: 1,
				pageSize: 5,
				hasMore: true,
				labelId: '',
				loadEnd: false,
				videoInfoList: [],
				videoCurrent: 0
			}
		},

		components: {
			videoPlay
		},
		props: {},
		//继续播放按钮图片

		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad(options) {
			if (this.$Route.query.index) {
				this.preUpdateIndex = this.$Route.query.index
				// this.videoCurrent = this.$Route.query.index
				uni.$emit('updateView', this.$Route.query.index) //更新浏览次数
			}
			if (this.$Route.query.scene) {
				let url = decodeURIComponent(this.$Route.query.scene)
				this.video_id = url.split('=')[1]
			}
			this.labelId = this.$Route.query.curLabelId
			this.activeIndex = Number(this.$Route.query.video_id)
			this.video_id = Number(this.$Route.query.video_id)
			this.author_id = Number(this.$Route.query.author_id)
			this.getVideoList()

			this.videoList.push({
				activeIndex: Number(this.$Route.query.video_id),
				video_id: Number(this.$Route.query.video_id),
				author_id: Number(this.$Route.query.author_id),
				prevIndex: '',
				nextIndex: '',
				showHide: 'hide',
				preUpdateIndex: this.preUpdateIndex
			})
		},
		onShow() {
			this.ShowHide = 'show'
			// #ifdef H5
			if (this.videoList[this.current].showHide == 'hide') {
				setTimeout(() => {
					let currentId = 'myVideo' + this.videoList[this.current].video_id
					let current_video = videojs(currentId)
					current_video.play()
				}, 900)
			}
			// #endif
			// #ifndef H5
			setTimeout(() => {
				this.videoList[0].showHide = 'show'
			}, 900)
			// #endif
		},
		onHide() {
			this.ShowHide = 'hide'
			// #ifdef H5
			let currentId = 'myVideo' + this.videoList[this.current].video_id
			let current_video = videojs(currentId)
			current_video.pause()
			// #endif
			// #ifndef H5
			this.videoList[0].showHide = 'hide'
			// #endif
		},
		onShareAppMessage: function() {
			return {
				title: this.videoInfo.videoName,
				path: `/extra/svideo/svideoPlay?video_id=${this.videoInfo.videoId}&curLabelId=${this.labelId}`,
				imageUrl: this.videoInfo.videoThemeImage
			}
		},
		onShareTimeline: function() {
			return {
				title: this.videoInfo.videoName,
				query: `video_id=${this.videoInfo.videoId}&curLabelId=${this.labelId}`,
				imageUrl: this.videoInfo.videoThemeImage
			}
		},
		methods: {

			videoChange(e) {
				this.videoCurrent = e.detail.current
				this.videoInfoList.forEach(item => {
					if (item.videoId == this.videoList[this.videoCurrent].video_id) {
						this.videoInfo = item
					}
				})
			},

			// 获取短视频列表数据
			getVideoList() {
				let param = {}
				param.url = 'v3/video/front/video/videoList'
				param.method = 'GET'
				param.data = {}
				param.data.videoId = this.video_id
				param.data.labelId = this.labelId
				param.data.current = this.pageCurrent
				param.data.pageSize = this.pageCurrent == 1 ? 4 : this.pageSize
				this.$request(param).then((res) => {
					this.loadEnd = true
					if (res.state == 200) {
						if (res.data && res.data.list && res.data.list.length > 0) {
							let arr = []
							res.data.list.forEach((item) => {
								arr.push({
									activeIndex: item.videoId,
									video_id: item.videoId,
									author_id: item.authorId,
									prevIndex: '',
									nextIndex: '',
									showHide: 'hide',
									preUpdateIndex: ''
								})
							})
							this.videoList = this.videoList.concat(arr)
						}
						this.hasMore = this.$checkPaginationHasMore(res.data.pagination) //是否还有数据
						if (this.hasMore) {
							this.pageCurrent++
						}
					}
				})
			},

			//设置短视频播放/暂停状态
			playVideo() {
				let _this = this
				// #ifdef H5
				let currentId = 'myVideo' + this.videoList[this.current].video_id // 获取当前视频id
				let current_video = videojs(currentId)
				current_video.play()
				// 获取视频列表
				this.videoList.forEach((item) => {
					// 获取json对象并遍历, 停止非当前视频
					let pauseId = 'myVideo' + item.video_id
					if (pauseId != currentId) {
						// 暂停其余视频
						let other_video = videojs(pauseId)
						other_video.pause()
						other_video.currentTime(0)
					}
				})
				// #endif
				// #ifndef H5
				let currentId = 'sldVideo_child' + this.videoList[this.current].video_id // 获取当前视频id
				this.videoList.forEach((item, index) => {
					// 获取json对象并遍历, 停止非当前视频
					let pauseId = 'sldVideo_child' + item.video_id
					if (pauseId == currentId) {
						_this.videoList[index].showHide = 'show'
					} else {
						_this.videoList[index].showHide = 'hide'
					}
				})
				// #endif
			},

			animationfinish(e) {
				if (this.current != e.detail.current) {
					this.current = e.detail.current
					this.playVideo()
					if (this.pageCurrent == this.videoList.length && this.hasMore) {
						this.getVideoList()
					}
				}
			},

			showVideoInfo(videoInfo) {
				this.videoInfoList.push(videoInfo)
				if (videoInfo.videoId == this.video_id) {
					this.videoInfo = videoInfo
				}
			},

			//视频暂停/继续播放事件
			videoPlayControl() {
				let {
					playFlag,
					showPauseBtn
				} = this
				this.videoContext = uni.createVideoContext('sldVideo')

				if (playFlag) {
					if (!showPauseBtn) {
						this.setData({
							showPauseBtn: true,
							showBtnIcn: this.pauseBtnIcon
						}) //3s后自动消失

						setTimeout(() => {
							this.setData({
								showPauseBtn: false
							})
						}, 3000)
					} else {
						this.videoContext.pause() //暂停播放

						this.setData({
							showPauseBtn: false,
							playFlag: false,
							showBtnIcn: this.playBtnIcon
						})
					}
				} else {
					this.videoContext.play() //开始播放

					this.setData({
						playFlag: true,
						showPauseBtn: false
					})
				}
			}
		}
	}
</script>
<style lang="scss">
	page {
		width: 750rpx;
		margin: 0 auto;
		position: relative;
	}

	.swiper {
		position: relative;
		width: 100%;
		//去除tabbar高度
		height: 100vh;
		background: #000000;

		.swiper-item {
			.info {
				z-index: 1;
				position: absolute;
				bottom: 60upx;
				color: white;
				text-indent: 1em;
				font-size: 30upx;
			}

			.audio {
				position: absolute;
				bottom: 20upx;
				z-index: 1;
				text-indent: 1em;
				color: white;
				font-size: 30upx;
				display: flex;
				width: 100%;
				flex-direction: row;
				justify-content: space-between;
				align-items: flex-end;

				@-webkit-keyframes move {
					0% {
						left: 100vw;
					}

					100% {
						left: -50vw;
					}
				}

				@-moz-keyframes move {
					0% {
						left: 100vw;
					}

					100% {
						left: -50vw;
					}
				}

				@-ms-keyframes move {
					0% {
						left: 100vw;
					}

					100% {
						left: -50vw;
					}
				}

				@keyframes move {
					0% {
						left: 100vw;
					}

					100% {
						left: -50vw;
					}
				}

				.text-group {
					position: relative;
					width: 50vw;
					height: 40upx;

					overflow: hidden;

					.text {
						position: absolute;
						top: 0upx;
						white-space: nowrap;
						/*文本不会换行，文本会在在同一行上继续*/
						-webkit-animation: 10s move infinite;
						-moz-animation: 10s move infinite;
						-ms-animation: 10s move infinite;
						animation: 10s move infinite;
						width: 50vw;
						left: 100vw;
					}
				}

				.icon {
					width: 60upx;
					height: 60upx;
					border-radius: 60upx;
					animation: turn 3s linear infinite;
					margin-right: 5vw;
					border: 10upx solid white;
				}

				/* 
					  turn : 定义的动画名称
					  1s : 动画时间
					  linear : 动画以何种运行轨迹完成一个周期
					  infinite :规定动画应该无限次播放
					 */
				@keyframes turn {
					0% {
						-webkit-transform: rotate(0deg);
					}

					25% {
						-webkit-transform: rotate(90deg);
					}

					50% {
						-webkit-transform: rotate(180deg);
					}

					75% {
						-webkit-transform: rotate(270deg);
					}

					100% {
						-webkit-transform: rotate(360deg);
					}
				}
			}

			.video {
				width: 100%;
				z-index: 0;
				height: calc(100vh - 120rpx);
			}

			.buttons {
				display: flex;
				flex-direction: column;
				position: absolute;
				right: 5vw;
				bottom: 12vh;
				color: white;
				text-align: center;
				justify-content: center;
				z-index: 1;

				.header_group {
					margin-bottom: 50upx;
					height: 90upx;
					width: 90upx;
					position: relative;

					.header {
						border: 2px solid white;
						margin: 0 auto;
						border-radius: 90upx;
						height: 90upx;
						width: 90upx;
					}

					.add {
						position: absolute;
						bottom: -30upx;
						margin: 0 auto;
						right: 0upx;
						background-color: #f15b6c;
						left: 0upx;
						width: 50upx;
						height: 50upx;
						font-size: 50upx;
						line-height: 50upx;
						border-radius: 50upx;
					}
				}

				.button {
					text-align: center;
					font-size: 25upx;

					.icon {
						margin: 20upx;
						width: 60upx;
					}
				}
			}
		}
	}
</style>