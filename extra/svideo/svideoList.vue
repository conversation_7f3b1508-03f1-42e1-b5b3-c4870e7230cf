<template>
	<view :style="mix_diyStyle">
  <view>
    <!-- <view class="" :style="{height:statusBarHeight,width:'100%'}"> -->
      
    <!-- </view> -->
    <block v-if="!openState">
      <notOpen></notOpen>
    </block>
    <block v-else>
	   
	   
	   
	   
       <view class="bg_1">
         
       </view>
      <!-- #ifdef MP -->
      <view class="search search_mp" :style="{height:menuButtonHeight,top:menuButtonTop,width:menuButtonleft}">
		<image :src="imgUrl + 'index/back.png'" mode="aspectFit" @click="$back()"></image>
        <view class="search_mp_name">短视频</view>
        <view class="left" @tap="goSearch">
          <image
            :src="imgUrl + 'svideo/search_icon.png'"
            mode="aspectFit"
          ></image>
          <text>{{ $L('请输入关键词') }}</text>
        </view>
      </view>
      <!-- #endif -->
     
	 
	 
	 
	 
	  <!-- #ifndef MP -->
      <view class="search" @tap="goSearch" :style="{top:'calc('+statusBarHeight+' + 20rpx)'}">
		<image :src="imgUrl + 'index/back.png'" mode="aspectFit" @click="$back"></image>
        <view class="left">
          <image
            :src="imgUrl + 'svideo/search_icon.png'"
            mode="aspectFit"
          ></image>
          <text>{{ $L('请输入关键词') }}</text>
        </view>
        <text class="search_text">{{ $L('搜索') }}</text>
      </view>
      <!-- #endif -->
        
      <view class="nav" v-if="labelList.length" :style="{top:'calc(80rpx + '+statusBarHeight+')'}">
        <view class="nav_mine">
          <view
            v-for="(item, index) in labelList"
            :key="index"
            :class="'nav_item ' + (curLabelId == item.labelId ? 'on' : '')"
            @tap="changeLabel"
            :data-id="item.labelId"
          >
            <text>{{ item.labelName }}</text>
            <text class="bottom_line" style="background-color: #fff"></text>
          </view>
        </view>
      </view>

      <scroll-view
        :scroll-y="labelList.length > 0"
        class="live_list"
        v-if="labelList.length"
        :style="{top:'calc(180rpx + '+statusBarHeight+')'}"
      >
        <swiper
          v-if="themeList.length > 0"
          class="rec_svideo"
          autoplay
          circular
        >
          <swiper-item v-for="(item, index) in themeList" :key="index">
            <image
              :data-themeId="item.themeId"
              :data-themeName="item.themeName"
              class="rec_img"
              :src="item.carouselImage"
              mode="aspectFit"
              @tap="goRecTopic"
            >
            </image>
          </swiper-item>
        </swiper>

        <!-- 短视频列表item -->
        <listVideoItem
          :videoList="videoList"
          :bgStyle="bgStyle"
          :listFavIcon="imgUrl + 'svideo/fav.png'"
          :listPlayNum="imgUrl + 'svideo/zx_play.png'"
          :curLabelId="curLabelId * 1"
          :authorId="author_id"
        />

        <!-- 数据加载完毕 -->
        <dataLoaded :showFlag="!hasmore && videoList && videoList.length > 0" />

        <!-- 数据加载中 -->
        <dataLoading :showFlag="!firstLoading && hasmore && loading" />
        <!-- 页面空数据 -->
        <emptyData
          :showFlag="!firstLoading && showEmpty"
          :emptyIcon="imgUrl + 'svideo/live_list_empty_icon.png'"
        />
        <!-- 页面loading -->
        <pageLoading
          :firstLoading="firstLoading"
          :loadingIcon="imgUrl + 'svideo/page_loading_icon.gif'"
        />
        <view class="empty_h" :style="'height:' + bottomSateArea"></view>
      </scroll-view>
      <!-- #ifndef MP -->
      <!-- 页面空数据 -->
      <emptyData
        class="empty_label"
        :showFlag="!labelList.length"
        :emptyIcon="imgUrl + 'svideo/live_list_empty_icon.png'"
      />
      <!-- #endif -->

      <!-- #ifdef MP -->
      <emptyData
        class="empty_label"
        v-if="!labelList.length"
        :showFlag="!labelList.length"
        :emptyIcon="imgUrl + 'svideo/live_list_empty_icon.png'"
      />
      <!-- #endif -->

      <!-- 进入短视频个人主页入口 -->
      <view
        class="personal_homepage"
        @tap="goLiveUserCenter"
        data-curTab="video"
      >
        <view class="personal_avator">
          <image :src="member_avatar" mode="aspectFit"></image>
        </view>
        <view class="my_video">{{ $L('我的视频') }}</view>
      </view>
    </block>
  </view>
	</view>
</template>

<script>
import { checkPageHasMore, initNum } from '@/utils/live'
import request from '@/utils/request'
import pageLoading from '../component/pageLoading.vue'
import emptyData from '../component/emptyData.vue'
import dataLoading from '../component/dataLoading.vue'
import dataLoaded from '../component/dataLoaded.vue'
import listVideoItem from '../component/video/listVideoItem.vue'
import notOpen from '@/components/not_open.vue'
import { mapState, mapMutations } from 'vuex'
export default {
  components: {
    pageLoading,
    emptyData,
    dataLoading,
    dataLoaded,
    listVideoItem,
    notOpen
  },
  data() {
    return {
      labelList: [],
      //标签列表
      videoList: [],
      //短视频列表
      themeList: [],
      //主题列表
      curLabelId: '',
      //当前选中的标签id
      imgUrl: getApp().globalData.imgUrl,
      //图片地址
      hasmore: true,
      //是否还有数据，用于页面展示
      loading: false,
      bgStyle:
        'background-size:cover;background-position:center center;background-repeat: no-repeat;',
      //背景图片样式
      firstLoading: true,
      //是否初次加载，是的话展示页面居中的loading效果，
      author_id: 0,
      statusBarHeight:uni.getSystemInfoSync().statusBarHeight+'px',
	  //#ifdef MP
      menuButtonHeight:uni.getMenuButtonBoundingClientRect().height+'px',
      menuButtonTop:uni.getMenuButtonBoundingClientRect().top+'px',
      menuButtonleft:uni.getMenuButtonBoundingClientRect().left+'px',
	  //#endif
      //作者id
      key: '',
      //登录的key值
      member_avatar: '', //默认作者头像
      pageSize: 10,
      current: 1,
      bottomSateArea: getApp().globalData.bottomSateArea, //iphone手机底部一条黑线的高度
      ifshow: false,
      showEmpty: false,
      openState: true
    }
  },
  computed: {
    ...mapState(['hasLogin', 'cartData', 'userInfo'])
  },
  props: {},
  //是否还有数据

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    setTimeout(()=>{
      uni.setNavigationBarTitle({
        title: this.$L('短视频列表')
      })
    },0);
    
    this.getLabelList()
  },
  onShow() {
    if (this.ifshow) {
      this.getVideoList()
    }
  },
  onHide() {
    this.ifshow = true
  },
  onReachBottom() {
    if (this.hasmore) {
      this.getVideoList()
    }
  },

  methods: {
    // 获取短视频分类
    getLabelList() {
      let { curLabelId, firstLoading } = this
      let param = {}
      param.data = {}
      param.url = 'v3/video/front/video/list'
      param.method = 'GET'
      this.$request(param).then((res) => {
        if (res.state == 200) {
          this.openState = true
          this.labelList = res.data.labelList
          this.member_avatar = res.data.memberAvatar
          if (this.labelList.length) {
            this.curLabelId = this.labelList[0].labelId
            this.getVideoList()
          }
          if (firstLoading) {
            firstLoading = false
          }
          this.firstLoading = firstLoading
        } else if (res.state == 258) {
          this.openState = false
        }
      })
    },

    //获取短视频列表
    getVideoList() {
      this.loading = true
      let { videoList, hasmore, firstLoading, themeList } = this
      let param = {}
      param.data = {}
      param.data.pageSize = this.pageSize
      param.data.current = this.current
      param.data.labelId = this.curLabelId
      param.url = 'v3/video/front/video/list'
      param.method = 'GET'
      this.$request(param).then((res) => {
        if (res.state == 200) {
          if (this.current == 1) {
            videoList = res.data.videoList
            themeList = res.data.themeList
          } else {
            videoList = videoList.concat(res.data.videoList)
          }
          if (checkPageHasMore(res.data.pagination)) {
            this.current++
          } else {
            this.hasmore = false
            hasmore = false
          }
        } //初次加载的话更改状

        if (firstLoading) {
          firstLoading = false
        }

        this.loading = false
        this.hasmore = hasmore
        this.videoList = videoList
        this.firstLoading = firstLoading
        this.themeList = themeList
        if (this.videoList.length == 0) {
          this.showEmpty = true
        } else {
          this.showEmpty = false
        }
      })
    },

    //标签点击事件
    changeLabel(e) {
      let id = e.currentTarget.dataset.id
      let { curLabelId } = this
      if (curLabelId == id) return
      this.curLabelId = id
      this.firstLoading = true
      this.videoList = []
      this.current = 1
      this.hasmore = true
      this.getVideoList(id)
    },

    //跳转搜索页面
    goSearch: function () {
      this.$Router.push('/extra/svideo/svideoSearch')
    },

    //进入推荐主题页面
    goRecTopic(e) {
      let tmp_data = e.currentTarget.dataset
      this.$Router.push({
        path: '/extra/svideo/svideoRecTopic',
        query: { theme_id: tmp_data.themeid, title: tmp_data.themename }
      })
    },

    //进入直播个人中心
    goLiveUserCenter(e) {
      var curTab = e.currentTarget.dataset.curtab

      if (!this.hasLogin) {
        getApp().globalData.goLogin(this.$Route)
      } else {
        this.$Router.push('/extra/user/my')
      }
    }
  }
}
</script>
<style lang="scss">
page {
  background-color: #f8f8f8;
  width: 750rpx;
  margin: 0 auto;
  position: relative;
  height: 100%;
}

.nav {
  position: fixed;
  top: 80rpx;
  left: 0;
  width: 750rpx;
  height: 84rpx;
  background-color: 'transpanrent';
  display: block;
  white-space: nowrap;
  overflow: hidden;
  z-index: 99;
  right: 0;
  margin: 0 auto;
}

.empty_h {
  /* height: 170rpx; */
  width: 100%;
  background-color: 'transpanrent';
}

.search {
  position: fixed;
  /* #ifdef H5 */
  top: 100rpx;
  /* #endif */
  /* #ifdef MP || APP-PLUS */
  top: 20rpx;
  /* #endif */
  left: 0;
  right: 0;
  width: 710rpx;
  height: 60rpx;
  display: flex;
  flex-direction: row;
  /* #ifndef MP */
    justify-content: space-between;
  
  margin: 20rpx 20rpx 0 20rpx;
  margin: 0 auto;
  /* #endif */
  align-items: center;
  background: transpanrent;
  
  image {
    width: 17rpx;
    height: 29rpx;
	
  }
}
.search_mp{
  padding: 0 25rpx;
}
.search_mp .search_mp_name{
  width: 105rpx;
  font-size: 34rpx;
  font-family: PingFang SC;
  font-weight: bold;
  color: #FFFFFF;
  margin-right: 20rpx;
}

.search .left {
  width: 580rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}
.search_mp .left{
  width: auto;
  flex: 1;
  height: 100%;
}

.search .left image {
  width: 50rpx;
  height: 50rpx;
  margin-top: 2rpx;
  margin-left: 10rpx;
}

.search .left text {
  color: #fff;
  font-size: 24rpx;
  margin-top: -2rpx;
}

.search .search_text {
  font-size: 26rpx;
  color: #fff;
}

.nav_item {
  display: inline-block;
  line-height: 74rpx;
  text-align: center;
  color: #fff;
  font-size: 28rpx;
  padding: 0 25rpx;
  font-weight: 600;
}

.nav_item.on text {
  display: inline-block;
  line-height: 74rpx;
  /* border-bottom: 4rpx solid #fff; */
  font-weight: 700;
}

.nav_item.on text:nth-child(2) {
  display: block;
   width: 58rpx;
  height: 2rpx;
  background: #fff;
  z-index: 1000;
  border: 4rpx solid #fff;
  border-radius: 40rpx;
  line-height: unset;
  margin: 0 auto;
}

.bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 750rpx;
  height: 468rpx;
  z-index: 0;
  right: 0;
  margin: 0 auto;
}
.bg_1 {
  position: fixed;
  top: 0;
  left: 0;
  width: 750rpx;
  height: 530rpx;
  z-index: 0;
  right: 0;
  margin: 0 auto;
  background:var(--color_video_main_vertical_bg);
}

.live_list {
  width: 750rpx;
  /* #ifdef H5 */
  height: calc(100vh - 185rpx);
  /* #endif */
  /* app-1-start */
  /* #ifdef APP-PLUS */
  height: calc(100vh - 255rpx);
  /* #endif */
  /* app-1-end */
  /* #ifdef MP */
  height: calc(100vh - 220rpx);
  /* #endif */
  
  position: fixed;
  top: 180rpx;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0 auto;
}

.rec_svideo {
  width: 750rpx;
  height: 345rpx;
  margin-bottom: 20rpx;
  margin-top: 20rpx;
}

.rec_svideo swiper-item {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.rec_svideo swiper-item .rec_img {
  width: 710rpx;
  height: 345rpx;
  border-radius: 15rpx;
}

.personal_homepage {
  width: 150rpx;
  height: 80rpx;
  background: var(--color_video_main_bg);
  border-radius: 40rpx 0 0 40rpx;
  display: flex;
  align-items: center;
  position: fixed;
  right: 0;
  bottom: 208rpx;
  z-index: 10;
  padding: 0 20rpx 0 8rpx;
  box-sizing: border-box;
}

.personal_avator {
  width: 64rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 8rpx;
  background: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
}

.personal_avator image {
  width: 64rpx;
  height: 60rpx;
  border-radius: 50%;
}

.my_video {
  font-size: 24rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #ffffff;
  line-height: 30rpx;
}

/* #ifdef H5 */
.nav {
  top: 168rpx;
}

.search {
  top: 88rpx;
}

.live_list {
  top: 268rpx;
}

/* #endif */
.nav {
  overflow-y: hidden;
}

.nav_mine {
  overflow-x: scroll;
  box-sizing: border-box;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  /* padding-bottom: 25px; */
}

.nav_mine::-webkit-scrollbar {
  display: none;
}

/* app-2-start */
/* #ifdef APP-PLUS */
.empty_label {
  position: absolute;
  top: 30%;
}
/* #endif */
/* app-2-end */

/* #ifdef H5 */
.empty_label {
  position: absolute;
  top: 20%;
}
/* #endif */

/* wx-1-start */
/* #ifdef MP-WEIXIN */
.empty_label {
  position: absolute;
  top: 30%;
}
/* #endif */
/* wx-1-end */
</style>
