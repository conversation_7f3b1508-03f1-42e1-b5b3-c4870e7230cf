var sysInfo = uni.getSystemInfoSync();

export default {
		/** 从 rpx 转为 px
		* @param float rpx 
		*/
		rpxToPx:function(rpx){ 
			var a = sysInfo.windowWidth / 750 * rpx;
			return Math.round(a);
			
		},
		/** 从 rpx 转为 px  包含精确小数
		* @param float rpx 
		*/
		rpxToPxFloat:function(rpx){
			var a = sysInfo.windowWidth / 750 * rpx;
			return a;
		},
		/** 从 px 转为 rpx
		 * @param float px
		 */
		pxToRpx: function(px){
			var a = 750 / sysInfo.windowWidth * px;
			return Math.round(a);
		},
		/** 从 px 转为 rpx 包含精确小数
		 * @param float px
		 */
		pxToRpxFloat: function(px){
			var a = 750 / sysInfo.windowWidth * px;
			return a;
		},
		
		//微信小程序专用，返回：页面定义导航栏所占用的高度（系统状态高度 + 标题栏的高度），单位：px
		//参数：sysInfo 为 uni.getSystemInfoSync() 的 实例
		getCustomBarHeight:function(){ //CustomBar 代表标题栏加状态栏的高度，HeaderBar 代表 标题栏的高度
			
			let rect = wx.getMenuButtonBoundingClientRect();  
			if (sysInfo.system.toLowerCase().indexOf('ios') > -1) {  
			    //IOS  
			    let CustomBar = rect.bottom + (rect.top - sysInfo.statusBarHeight) * 2;  
			    let HeaderBar = CustomBar - sysInfo.statusBarHeight; 
				return CustomBar;
			} else {  
			    //安卓  
			    let HeaderBar = rect.height + (rect.top - sysInfo.statusBarHeight) * 2;  
			    let CustomBar = HeaderBar + sysInfo.statusBarHeight; 
				return CustomBar;
			} 
		}
}