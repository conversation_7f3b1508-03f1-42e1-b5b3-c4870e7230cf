<template name="livePlayGoods">
	<view class="live_user_tab_content">
		<block v-for="(item, index) in goodsData" :key="index">
			<view class="live_user_tab_goods_item">
				<view class="live_user_tab_goods_img" @tap="goGoodsDetail(item)">
					<!-- app-1-start -->
					<!-- #ifdef APP -->
					<image class="live_user_tab_goods_img_o" :src="item.goodsImage" mode="aspectFit"></image>
					<!-- #endif -->
					<!-- app-1-end -->
					<!-- <coverImage :src="item.goodsImage" height="246" width="246" class="image"></coverImage> -->
					<!-- #ifndef APP -->
					<view class="goods_img_image" :style="'background-image:url('+item.goodsImage+')'"></view>
					<!-- #endif -->
					<text class="goods_img_text">{{index+1}}</text>
				</view>
				<view class="live_user_tab_right">
					<view class="live_user_tab_content_right_top" @tap="goGoodsDetail(item)">
						<text class="live_user_tab_content_right_name">{{item.goodsName}}</text>
						<text class="live_user_tab_content_right_jingle">{{item.goodsBrief}}</text>
					</view>
					<view class="live_user_tab_bottom">
						<view class="live_user_tab_bottom_price">
							<text class="live_user_tab_bottom_price_unit">{{L('¥')}}</text>
							<text class="live_user_tab_bottom_price_num">{{item.goodsPrice}}</text>
						</view>
						<view class="live_user_tab_bottom_price_click_num">
							<image class="live_user_tab_bottom_add_cart" :src="addCartIcon" mode="aspectFit" :data-gid="item.gid" @tap="addCart(item)"></image>
							<!-- <block v-if="memberInfo.isOwn==1">
								<image class="img" :src="eyeIcon" mode="aspectFit"></image>
								<text>{{item.clickNum}}</text>
							</block> -->
						</view>
					</view>
				</view>
			</view>
			<view v-if="goodsData.length-index > 1" class="line_marginl_20"></view>
		</block>
		</block>
	</view>
</template>

<script>
  import {getCurLanguage} from '@/utils/base.js'
	export default {
		name: "livePlayGoods",
		data() {
			return {
        L:getCurLanguage,
      }
		},
		props: {
			goodsData: {
				type: Array,
				value: []
			},
			addCartIcon: {
				type: String,
				value: ''
			},
			eyeIcon: {
				type: String,
				value: ''
			},
			live_id: {
				type: Number,
				value: 0
			},
			memberInfo: {
				type: Object,
				value: {}
			}
		},
		methods: {
			//跳转商品详情页
			goGoodsDetail(good) {
				let page = getCurrentPages();
				let len = page.length;

				if (len > 4) {
					// #ifndef APP
					this.$Router.replace({path:'/standard/product/detail',query:{productId:good.productId}})
					// #endif
					//app-2-start
					// #ifdef APP
					uni.redirectTo({
						url:'/standard/product/detail?productId='+good.productId
					})
					// #endif
					//app-2-end
				} else {
					// #ifndef APP
					this.$Router.push({path:'/standard/product/detail',query:{productId:good.productId}})
					// #endif
					//app-3-start
					// #ifdef APP
					uni.navigateTo({
						url:'/standard/product/detail?productId='+good.productId
					})
					// #endif
					//app-3-end
				}
			},

			//加入购物车
			addCart(goodinfo) {
				this.$emit('click', goodinfo);
			},
		}
	}
</script>


<style>
  .live_user_tab_content{
    box-sizing: border-box;
    margin-top: 10rpx;
  }
	.live_user_tab_goods_item {
		width: 710rpx;
		padding: 20rpx;
		border-radius: 14rpx;
		height: 246rpx;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
		margin: 5rpx 0;
	}

	.live_user_tab_goods_img {
		width: 246rpx;
		height: 246rpx;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		position: relative;
	}
  .live_user_tab_goods_img_o{
    width: 246rpx;
    height: 246rpx;
    border-radius: 15rpx;
  }

	.goods_img_image {
		background-position: center center;
		background-repeat: no-repeat;
		background-size: cover;
		width: 246rpx;
		height: 246rpx;
		border-radius: 15rpx;
	}

	.goods_img_text {
		position: absolute;
		top: 0;
		left: 0;
		color: #fff;
		font-size: 20rpx;
		height: 40rpx;
		line-height: 40rpx;
		padding: 0 15rpx;
		border-radius: 15rpx 0 15rpx 0;
		background: linear-gradient(45deg, rgba(255, 0, 0, 1) 0%, rgba(255, 122, 24, 1) 100%);
	}

	.live_user_tab_right {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: flex-start;
		height: 226rpx;
		padding: 10rpx 0 10rpx 20rpx;
	}

	.live_user_tab_content_right_top {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
	}

	.live_user_tab_content_right_name {
		color: #2d2d2d;
		font-size: 28rpx;
		line-height: 42rpx;
		height: 84rpx;
		width: 444rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		word-break: break-word;
	}

	.live_user_tab_content_right_jingle {
		color: #949494;
		font-size: 26rpx;
		line-height: 36rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		margin-top: 10rpx;
		width: 444rpx;
	}

	.live_user_tab_bottom {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: flex-end;
		width: 444rpx;
	}
  .live_user_tab_bottom_price{
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
  }

	.live_user_tab_bottom_price_unit {
		color: #fc1c1c;
		font-size: 24rpx;
    display: inline;
	}

	.live_user_tab_bottom_price_num {
		font-size: 36rpx;
		color: #fc1c1c;
		margin-left: 3rpx;
	}

	.live_user_tab_bottom_price_click_num {
		display: flex;
		flex-direction: row;
		align-items: center;
	}

	.live_user_tab_bottom_add_cart {
		width: 42rpx;
		height: 42rpx;
	}

	.live_user_tab_content .goods_item .right .bottom .click_num image {
		width: 42rpx;
		height: 42rpx;
	}

	.live_user_tab_content .goods_item .right .bottom .click_num text {
		color: #949494;
		font-size: 22rpx;
	}

	.line_marginl_20 {
		border-bottom: 1px solid #eee;
		width: 730rpx;
		margin-left: 20rpx;
	}
</style>
