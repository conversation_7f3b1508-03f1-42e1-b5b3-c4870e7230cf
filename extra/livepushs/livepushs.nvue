<template>
	<view class="body" :style="{height: bodyHeight + 'px'}">
    <!--注：原生插件的css样式，只支持使用style属性，不支持使用id属性和class属性-->
    <!-- 直播 -->
    <!-- <text class="asfsda">{{ads}}</text> -->
   <TXPushView ref="txView" v-if="pusher" tel="12305" :style="{width: width + 'px',height:height + 'px'}" @onTel="onTel" @click="myTextClick"></TXPushView>
    <view class="live_header" :style="{top: (statusBarHeight + rpxToPxFloat(25)) + 'px'}">
    	<image @tap="goBack" class="live_header_go_back" :src="imgUrl+'svideo/white_arrow_l.png'"></image>
      <image :src="liveDetail.memberAvatar" class="live_header_avator" mode=""></image>
    	<view class="live_mem_info">
    		<text class="live_header_text live_mem_info_name">{{liveDetail.memberNickname}}</text>
    		<view class="stat_num">
    			<text v-if="liveDetail.isSelf == 1" class="live_header_text stat_num_text click_num"
    				style="margin-right: 20rpx">{{live_like_num}}{{L('人气')}}</text>
    			<text class="live_header_text stat_num_text click_num">{{live_click_num}}{{L('观看')}}</text>
    		</view>
    	</view>
    </view>
	
		
		<view class="toolDiv">
			<view class="toolDiv-list"> 
				
				<view class="toolDiv-list-item" @click.stop="beatyOpen(0)">
          <image :src="imgUrl+'livepush/beauty.png'" mode="" class="toolDiv-list-item_img"></image>
					<text class="toolDiv-list-item-txt">美颜</text>
				</view>
				<view class="toolDiv-list-item" @click.stop="switchCamera()">
          <image :src="imgUrl+'livepush/overturn.png'" mode="" class="toolDiv-list-item_img"></image>
					<text class="toolDiv-list-item-txt">切换</text>
				</view>
        <view class="toolDiv-list-item" @click.stop="dingClick()" v-if="front=='back'">
          <image :src="imgUrl+'livepush/lamp.png'" mode="" class="toolDiv-list-item_img"></image>
        	<text class="toolDiv-list-item-txt">闪光灯</text>
        </view>
				<view class="toolDiv-list-item" @click.stop="bgmPopOpne()">
          <image :src="imgUrl+'livepush/music_white.png'" mode="" class="toolDiv-list-item_img"></image>
					<text class="toolDiv-list-item-txt">{{'音乐'}}</text>
				</view> 
			</view>
		</view>
		
    <!-- 底部框 -->
    <view class="live_footer">
    	<view class="live_footer_goods" @tap="showGoodsFun">
    		<image class="live_footer_goods_img" :src="imgUrl+'svideo/goods_icon.png'"></image>
    		<text class="live_footer_goods_texts">{{liveDetail.goodsNum * 1 > 99 ? '99+' : liveDetail.goodsNum }}</text>
    	</view>
    	<input type="text" name="talk_con" v-model="input_val" class="live_footer_talk_con"
    		:placeholder="live_state?L('和粉丝聊两句~'):L('直播开始后可与粉丝交流~')"
    	confirm-type="send"
    		@confirm="publishComment" maxlength="30" cursor-spacing="20"></input>
    	<view class="live_footer_share" @tap="showShare">
    		<image class="live_footer_share_img" :src="imgUrl+'svideo/share.png'"></image>
    		<text class="live_footer_text">{{L('分享')}}</text>
    	</view>
    	<view v-if="!live_state" class="live_footer_share add_heart" @click.stop="startLive">
    		<image class="live_footer_share_img" :src="imgUrl+'svideo/start_live.png'"></image>
    
    		<text class="live_footer_text">{{L('开始直播')}}</text>
    	</view>
    	<view v-if="live_state" class="live_footer_share add_heart" @tap="stopLiveTip">
    		<image class="live_footer_share_img" :src="imgUrl+'svideo/stop_live.png'"></image>
    		<text class="live_footer_text">{{L('结束直播')}}</text>
    	</view>
    </view>
    
    <!-- 弹幕 start-->
    <view class="barrage_wrap">
    	<view class="notice">
    		<text class="notice_text" v-if="noticeList&&noticeList.length>0">{{noticeList[0].msg}}</text>
    	</view>
      <MhMsgList ref="msgList" :msgList="msgList" :toBottom='toBottom' :liveDetail='liveDetail'></MhMsgList>
    </view>
    <!-- 弹幕 end-->
    <!-- 分享 start -->
    <!-- 分享 -->
    <view class="select-wrap" :style="{height:screenHeight+'px'}" catchtouchmove="touchmoveshare" v-if="shareWrap" @click="">
    	<view class="share-mode">
    		<view class="empty_158"></view>
    		<view class="share-img" @tap="prevImg">
    			<image class="share-img-image" :src="shareImg" mode="widthFix"></image>
    		</view>
    
    		<view class="share-mode-ul">
    			<view class="share-mode-item" @tap="downloadImg">
    				<image :src="imgUrl + 'svideo/hb_share.png'" mode="widthFix" class="share-mode-item-image"></image>
    				<text class="share-mode-item-text">{{L('下载海报')}}</text>
    			</view>
    		</view>
    
    		<view class="select-wrap-close" @tap="closeShare">
    			<image :src="imgUrl+'svideo/release_close.png'" class="select-wrap-close-image" mode="widthFix"></image>
    		</view>
    
    	</view>
    </view>
    
    
    <view class="select-wrap" :style="{height:screenHeight+'px'}" catchtouchmove="touchmoveshare" v-if="shareWrap2" @click="">
    	<view class="share-mode" :style="{height:screenHeight+'px'}">
    
    		<view class="share-img"></view>
    
    		<view class="share-mode-ul">
    			<button @click="sldShare(0,'WXSceneSession')" class="share-mode-item">
    				<image :src="imgUrl+'svideo/wx_share.png'" class="share-mode-item-image" mode="widthFix"></image>
    				<text class="share-mode-item-text">{{L('微信好友')}}</text>
    			</button>
    			<view class="share-mode-item" @tap="createhb">
    				<image :src="imgUrl+'svideo/createhb.png'" mode="widthFix" class="share-mode-item-image"></image>
    				<text class="share-mode-item-text">{{L('生成海报')}}</text>
    			</view>
    		</view>
    
    		<view class="select-wrap-close" @tap="closeShare">
    			<image class="select-wrap-close-image" :src="imgUrl+'svideo/release_close.png'" mode="widthFix"></image>
    		</view>
    	</view>
    </view>
    <!-- 分享 end -->
    <!-- 直播已结束 start -->
    <uni-popup ref="stopVideo" type="center">
    	<view class="stop_pop_box">
    		<view class="stop_pop_tit"><text class="stop_pop_tit_text">{{liveStateInfo.forbidReason?liveStateInfo.forbidReason:L('直播已结束')}}</text></view>
    		<view class="stop_pop_content">
          <text class="stop_pop_content_text">{{L('直播时长：')}}</text><text class="stop_pop_content_text">{{liveStateInfo.playbackTime?liveStateInfo.playbackTime:'0:0:0'}}</text></view>
    		<view class="stop_pop_content">
    			<text class="stop_pop_content_text">{{L('获赞：')}}{{liveStateInfo.popularityNum?liveStateInfo.popularityNum:0}}</text>
    			<text class="stop_pop_content_text">{{L('观看：')}}{{liveStateInfo.viewingNum?liveStateInfo.viewingNum:0}}</text>
    		</view>
    		<text class="stop_pop_btn" @tap="endBack">{{L('返回')}}</text>
    	</view>
    </uni-popup>
    <!-- 直播已结束 end -->
		
		<!-- 视频绑定的商品模块 start -->
		<view class="live_bind_goods_mask" v-if="showGoods">
			<view class="live_bind_goods">
				<view class="live_bind_goods_header">
					<text class="live_bind_goods_header_text">{{L('全部商品')}}({{liveDetail.goodsNum * 1 > 99 ? '99+' : liveDetail.goodsNum}})</text>
					<image class="live_bind_image" :src="imgUrl+'svideo/close.png'" @tap="closeGoods"></image>
				</view>
				<scroll-view class="scroll_goods" scroll-y="true" @scrolltolower="getMoreGoods">
					<view class="live_user_tab_content">
						<!-- 商品列表item -->
						<livePlayGoods :goodsData="goodsList" :memberInfo='liveDetail'
							:eyeIcon="imgUrl+'svideo/eye.png'"
							:live_id="live_id"/>
		
						<!-- 数据加载完毕 -->
						<dataLoaded :showFlag="!hasmore&&goodsList.length>0" />
		
						<!-- 数据加载中 -->
						<dataLoading :showFlag="hasmore&&loading" />
		
					</view>
				</scroll-view>
			</view>
		</view>
		<!-- 视频绑定的商品模块 end -->
    <!-- 音乐模块 start -->
    <uni-popup ref="showGoods_mp3" type="bottom">
      <view class="slider_beauty_live">
        <view class="slider_beauty_live_header">
          <view class="slider_beauty_volume" @click="showGoods_mp3_volume_type()">
            <image class="slider_beauty_volume_img" :src="imgUrl + 'livepush/volume.png'" mode=""></image>
            <text class="slider_beauty_volume_text">音量</text>
          </view>
          <view class="slider_beauty_volume_center">
            <view class="stop_slider_header_text_box stop_slider_header_text_box_one" @click="music_select(1)">
              <text class="stop_slider_header_text stop_slider_header_text_two" :class="{'stop_slider_header_text_one1':musicFlag==1}">正在播放</text>
              <view v-if="musicFlag==1" class="stop_slider_header_text_xian stop_slider_header_text_xian_one"></view>
            </view>
            <view class="stop_slider_header_text_box stop_slider_header_text_box_one" @click="music_select(2)">
              <text class="stop_slider_header_text stop_slider_header_text_two" :class="{'stop_slider_header_text_one1':musicFlag==2}">背景音乐</text>
              <view v-if="musicFlag==2" class="stop_slider_header_text_xian stop_slider_header_text_xian_one"></view>
            </view>
          </view>
        </view>
        <view class="slider_beauty_volume_type_box"  v-if="musicFlag==1">
          <view class="" v-if="musicInfo.length>0">
            
            <view class="music_list" v-for="(item,index) in musicInfo">
              <view class="music_list_cont">
                <image class="music_list_cont_img" :src="imgUrl + 'livepush/music.png'" mode="" v-if="item.type==1"></image>
                <image class="music_list_cont_play" :src="imgUrl + 'livepush/play.gif'" mode=""  v-if="item.type==2"></image>
              </view>
              <view class="music_list_cont_center">
                <text class="music_list_center1">{{item.musicName}}</text>
                <text class="music_list_center2">{{item.singerName}} · 时长{{item.musicDuration}}</text>
              </view>
              <view class="music_list_cont_right">
                <view class="music_list_cont_right_btn">
                  <text class="music_list_cont_right_btn_text" v-if="item.type==1" @click="selectBGMFiles(item,index,1)">使用</text>
                  <text class="music_list_cont_right_btn_text" v-if="item.type==2"  @click="tingBGMFiles(item,index,1)">停止</text> 
                </view>
              </view>
            </view>
          </view>
          <view class="" v-else>
            <text class="zanwugewu">暂无播放~</text>
          </view>
        </view>
          <view class="slider_beauty_volume_type" v-if="musicFlag==2">
            <view class="volume_type" @click="volumeType(1)">
              <text class="volume_type_text" :class="{'volume_type_text_one':musictype==1}">推荐</text>
            </view>
            <view class="volume_type" @click="volumeType(2)">
              <text class="volume_type_text" :class="{'volume_type_text_one':musictype==2}">全部</text>
            </view>
          </view>
          <view class="search_center_box"  v-if="musicFlag==2">
            <view class="search_center">
              <image class="search_icon" :src="imgUrl+'search.png'"></image>
              <input class='sea_input' type='text' v-model="input_val"
                placeholder="搜索歌曲或歌手"
                @input="inputChange" @confirm='search' maxlength="50"></input>
              <image class='clear_content' v-if="input_val" @click="clearInputVal"
                :src="imgUrl+'input_clear.png'" />
            </view>        
          </view>
          <!-- 音乐列表 -->
          <scroll-view class="scroll_music_list" scroll-y="true" @scrolltolower="getMoreMusic" v-if="musicFlag==2" :scroll-into-view='scrollMusic'>
            <view class="" v-if="music_list.length>0">
              <view class="" id='music_list_id'></view>
              <view class="music_list" v-for="(item,index) in music_list">
                <view class="music_list_cont">
                  <image class="music_list_cont_img" :src="imgUrl + 'livepush/music.png'" mode="" v-if="item.type==1"></image>
                  <image class="music_list_cont_play" :src="imgUrl + 'livepush/play.gif'" mode=""  v-if="item.type==2"></image>
                </view>
                <view class="music_list_cont_center">
                  <text class="music_list_center1">{{item.musicName}}</text>
                  <text class="music_list_center2">{{item.singerName}} · 时长{{item.musicDuration}}</text>
                </view>
                <view class="music_list_cont_right">
                  <view class="music_list_cont_right_btn">
                    <text class="music_list_cont_right_btn_text" v-if="item.type==1" @click="selectBGMFiles(item,index)">使用</text>
                    <text class="music_list_cont_right_btn_text" v-if="item.type==2"  @click="tingBGMFiles(item,index)">停止</text> 
                  </view>
                </view>
              </view>
            </view>
            <view class="" v-else>
              <text class="zanwugewu">暂无歌曲~</text>
            </view>
          </scroll-view>
      </view>
    </uni-popup>
    <uni-popup ref="showGoods_mp3_volume" type="bottom">
      <view class="showGoods_mp3_volume">
        <view class="slider_beauty_live_header slider_beauty_live_header_one">
          <view class="slider_beauty_volume" @click="showGoods_mp3_volume_type(1)">
            <image class="slider_beauty_volume_img1" :src="imgUrl + 'livepush/back.png'" mode=""></image>
            <text class="slider_beauty_volume_text"></text>
          </view>
          <view class="slider_beauty_volume_center">
            <view class="stop_slider_header_text_box stop_slider_header_text_box_one">
              <text class="stop_slider_header_text stop_slider_header_text_two stop_slider_header_text_three header_text_box_one">音量设置</text>
            </view>
          </view>
        </view>
          <view>
            <text class="slider_beauty_volume_slider_text">滑动设置背景音乐音量</text>
          </view>
          <view class="slider_beauty_volume_slider">
            <slider block-size='18' activeColor="#F03D39" backgroundColor='#B9B9B9'  block-color="#FC1C1C" :value="music_slider" min='0' max='90' @changing="musicChange" step="1" :show-value="true"/>
          </view>
      </view>
    </uni-popup> 
    <!-- 音乐模块 end -->
    <uni-popup ref="stop_slider" type="bottom">
      <view class="slider_beauty" v-if="beautySwitch&&Manager==1">
        <!-- 美顏 -->
        <slider block-size='18' activeColor="#fff" backgroundColor='#B9B9B9'  block-color="#FC1C1C" :value="beauty" min='0' max='90' @changing="sliderChange" step="1" v-if="beautyFlag==1"/>
        <!-- 美白 -->
        <slider :value="whiteness" block-size='18' activeColor="#fff" backgroundColor='#B9B9B9'  block-color="#FC1C1C" min='0' max='90' @changing="whitenessChange" step="1" v-if="beautyFlag==2"/>
        <!-- 红润 -->
        <slider :value="ruddy" block-size='18' activeColor="#fff" backgroundColor='#B9B9B9'  block-color="#FC1C1C" min='0' max='90' @changing="ruddyChange" step="1" v-if="beautyFlag==3"/>
      </view>
      <view class="slider_beauty" v-if="fliterSwitch&&Manager==2">
        <!-- 滤镜强度 -->
        <slider block-size='18' activeColor="#fff" backgroundColor='#B9B9B9'  block-color="#FC1C1C" :value="filter_slider" min='0' max='100' @changing="filterChange" step="1" v-if="fliterSwitch"/>
      </view>
      <view class="stop_slider_box">
        <view class="stop_slider_header">
          <view class="stop_slider_header_flex">
            <view class="stop_slider_header_text_box" @click="Manager_select(1)">
              <text class="stop_slider_header_text" :class="{'stop_slider_header_text_one':Manager==1}">美颜</text>
              <view v-if="Manager==1" class="stop_slider_header_text_xian"></view>
            </view>
            <view class="stop_slider_header_text_box" @click="Manager_select(2)">
              <text class="stop_slider_header_text" :class="{'stop_slider_header_text_one':Manager==2}">滤镜</text>
              <view v-if="Manager==2" class="stop_slider_header_text_xian"></view>
            </view>
          </view>
        </view>
      <!-- 美颜 start -->
        <view class="stop_slider_box_bottom" v-if="Manager==1">
          <view class="slider_box_bottom_one">
            <view class="slider_box_bottom_one_img" @click="switchChange(1)" :class="{'bottom_one_img':!beautySwitch}">
              <image :src="imgUrl+'livepush/no.png'" class="box_bottom_one_img"></image>
            </view>
            <text class="slider_box_bottom_one_text" :class="{'bottom_one_text':!beautySwitch}">无</text>
          </view>
          <view class="slider_box_bottom_one">
            <view class="slider_box_bottom_one_img" @click="beauty_select(1)" :class="{'bottom_one_img':beautyFlag==1&&beautySwitch}">
              <image :src="imgUrl+'livepush/dermabrasion.png'" mode="" class="box_bottom_one_img"></image>
            </view>
            <text class="slider_box_bottom_one_text" :class="{'bottom_one_text':beautyFlag==1&&beautySwitch}">磨皮</text>
          </view>
          <view class="slider_box_bottom_one">
            <view class="slider_box_bottom_one_img" @click="beauty_select(2)" :class="{'bottom_one_img':beautyFlag==2&&beautySwitch}">
               <image :src="imgUrl+'livepush/skin_whitening.png'" mode="" class="box_bottom_one_img"></image>
            </view>
            <text class="slider_box_bottom_one_text" :class="{'bottom_one_text':beautyFlag==2&&beautySwitch}">美白</text>
          </view>
          <view class="slider_box_bottom_one">
            <view class="slider_box_bottom_one_img" @click="beauty_select(3)" :class="{'bottom_one_img':beautyFlag==3&&beautySwitch}">
               <image :src="imgUrl+'livepush/ruddy.png'" mode="" class="box_bottom_one_img"></image>
            </view>
            <text class="slider_box_bottom_one_text" :class="{'bottom_one_text':beautyFlag==3&&beautySwitch}">红润</text>
          </view>
        </view>
        <!-- 美颜 end -->
        <!-- 滤镜 start -->
        <view class="stop_slider_box_bottom" v-if="Manager==2">
          <scroll-view class="slider-view-p" show-scrollbar="false" scroll-x style="display: flex;">
              <view class="slider_box_bottom_one" style="margin-right: 30rpx;width: 114rpx;">
                <view class="slider_box_bottom_one_img" @click="switchChange(2)" :class="{'bottom_one_img':!fliterSwitch}" style="width: 114rpx;height: 114rpx;">
                  <image :src="imgUrl+'livepush/no.png'" class="box_bottom_one_img_filter"></image>
                </view>
                <text class="slider_box_bottom_one_text" :class="{'bottom_one_text':!fliterSwitch}">无</text>
              </view>
            <view class="slider_bottom_one">
              <view class="slider_box_bottom_one" v-for="(item,index) in filterList" :key="index" style="margin-right: 30rpx;width: 114rpx;"> 
                <view class="slider_box_bottom_one_img" @click="fliter_select(item,index)" :class="{'bottom_one_img':fliterSwitch&&filterindex==index}" style="width: 114rpx;height: 114rpx;">
                   <image :src="imgUrl+item.image" class="box_bottom_one_img box_bottom_one_img_filter"></image>
                </view>
                <text class="slider_box_bottom_one_text" :class="{'bottom_one_text':fliterSwitch&&filterindex==index}">{{item.name}}</text>
              </view>  
            </view>            
          </scroll-view>
        </view>
        <view class="slider_box_bottom_foot">
          
        </view>
        <!-- 滤镜 end -->
      </view>
    </uni-popup>
    
	</view>
</template>

<script>
  const modal = uni.requireNativePlugin('modal');
  import permision from "./permission.js"
  import {
  	checkPageHasMore,
  	colorArray,
  	initNum
  } from "@/utils/live";
  import {
  	mapState, 
  	mapMutations 
  } from 'vuex';
  import io from '@hyoga/uni-socket.io';
	import rpxTool from "./tool/rpxTool.js";
	import fileTool from "./tool/fileTool.js";
	import request from "@/utils/request";
	var sysInfo = uni.getSystemInfoSync();
  import {getCurLanguage} from '@/utils/base.js'
  import livePlayGoods from "./livePlayGoods.vue";
  import dataLoading from "@/extra/component/dataLoading.vue";
  import dataLoaded from "@/extra/component/dataLoaded.vue";
  import MhMsgList from "@/extra/component/mh-msgList/mh-msgList.vue";
  import uniPopup from '@/components/uni-popup/uni-popup.vue';
	var app = getApp();
	var globalEvent = uni.requireNativePlugin('globalEvent');
  const bus = getApp().globalData.bus;
	
	export default {
		data() {
			return {
        // start
       L:getCurLanguage,
       pusher:true,
       pushurl: '',
       live_id: 61,
       //当前直播id
       loading: false,
       //数据加载状态
       liveDetail: '',
       //直播详情
       settingData: '',
       //平台设置信息
       imgUrl: getApp().globalData.imgUrl,
       //图片地址
       bgStyle: 'background-size:cover;background-position:center center;background-repeat: no-repeat;',
       //背景图片样式
       shareWrap: false,
       // 展示分享弹层
       shareWrap2: false,
       // 展示分享弹层
       shareImg: '',
       // 分享图片
       goodsList: [],
       //直播绑定的商品
       hasmore: true,
       //是否还有数据，用于页面展示
       showGoods: false,
       //是否显示商品弹层
       playFlag: true,
       //视频是否播放
       showPauseBtn: false,
       //是否显示暂停按钮
       showBtnIcn: '',
       //视频播放控制层按钮图片
       colorArray: colorArray,
       //颜色数组
       msgList: [],
       //弹幕内容
       noticeList: [],
       //消息通知列表
       input_val: '',
       //输入框内容
       toBottom: '',
       //弹幕滚动到指定的元素
       live_state: false,
       //直播状态 默认0：未开启,1开启直播，2:直接结束
       backGrade: 1,
       // 是否被禁止直播
       forbidReason: null,
       //返回页面级别
       live_like_num: 0,
       //人气数
       live_click_num: 0, //观看数
       liveStateInfo: "",
       pn: 1,
       pageSize: 10,
       musicCurrent:1,
       musicTotal:0,
       stat_end: 0, //终端，默认为1，pc端
       enableCamra: true, //摄像头是否开启
       enableMic: true, //麦克风是否开启
       beauty:20,//磨皮
       whiteness:20,//美白
       ruddy:20,//红润
       isStopLive: false, //是否要结束直播
       torchFlag:false,//手电筒
       Manager:1,//1美颜 2滤镜
       filter_slider:20,//滤镜强度
       music_slider:60,
       input_val:'',
       front:'front',
       isSocketInit:false,
       screenHeight:0,
       client:'1',
       musicFlag:2,//切换音乐
       musictype:1,//
       input_val:'',//歌曲或歌手
       musicId:'',//音乐id
       music_list:[],//音乐列表
       musicInfo:[],//音乐选中的歌曲列表
       triggered:false,//音乐列表下拉
       scrollMusic:'',
       filterindex:-1,//滤镜下标
       filterList:[
         {
           name:'白皙',back:'',id:0,val:'',
           image:'livepush/filter/baixi.png',
           ima_name:'tuibeauty_filter_baixi.png',
         },
         {
           name:'标准',back:'',id:1,val:'',
           image:'livepush/filter/biaozhun.png',
           ima_name:'tuibeauty_filter_biaozhun.png',
         },
         {
           name:'自然',back:'',id:2,val:'',
           image:'livepush/filter/ziran.png',
           ima_name:'tuibeauty_filter_ziran.png',
         },
         {
           name:'樱红',back:'',id:3,val:'',
           image:'livepush/filter/yinghong.png',
           ima_name:'tuibeauty_filter_yinghong.png',
         },
         {
           name:'云裳',back:'',id:4,val:'',
           image:'livepush/filter/yunshang.png',
           ima_name:'tuibeauty_filter_yunshang.png',
         },
         {
           name:'纯真',back:'',id:5,val:'',
           image:'livepush/filter/chunzhen.png',
           ima_name:'tuibeauty_filter_chunzhen.png',
         },
         {
           name:'白兰',back:'',id:6,val:'',
           image:'livepush/filter/bailan.png',
           ima_name:'tuibeauty_filter_bailan.png',
         },
         {
           name:'元气',back:'',id:7,val:'',
           image:'livepush/filter/yuanqi.png',
           ima_name:'tuibeauty_filter_yuanqi.png',
         },
         {
           name:'超脱',back:'',id:8,val:'',
           image:'livepush/filter/chaotuo.png',
           ima_name:'tuibeauty_filter_chaotuo.png',
         },
         {
           name:'香氛',back:'',id:9,val:'',
           image:'livepush/filter/xiangfen.png',
           ima_name:'tuibeauty_filter_xiangfen.png',
         },
         {
           name:'美白',back:'',id:10,val:'',
           image:'livepush/filter/white.png',
           ima_name:'tuibeauty_filter_white.png',
         },
         {
           name:'浪漫',back:'',id:11,val:'',
           image:'livepush/filter/langman.png',
           ima_name:'tuibeauty_filter_langman.png',
         },
         {
           name:'清新',back:'',id:12,val:'',
           image:'livepush/filter/qingxin.png',
           ima_name:'tuibeauty_filter_qingxin.png',
         },
         {
           name:'唯美',back:'',id:13,val:'',
           image:'livepush/filter/weimei.png',
           ima_name:'tuibeauty_filter_weimei.png',
         },
         {
           name:'粉嫩',back:'',id:14,val:'',
           image:'livepush/filter/fennen.png',
           ima_name:'tuibeauty_filter_fennen.png',
         },
         {
           name:'怀旧',back:'',id:15,val:'',
           image:'livepush/filter/huaijiu.png',
           ima_name:'tuibeauty_filter_huaijiu.png',
         },
         {
           name:'蓝调',back:'',id:16,val:'',
           image:'livepush/filter/landiao.png',
           ima_name:'tuibeauty_filter_landiao.png',
         },
         {
           name:'清凉',back:'',id:17,val:'',
           image:'livepush/filter/qingliang.png',
           ima_name:'tuibeauty_filter_qingliang.png',
         },
         {
           name:'日系',back:'',id:18,val:'',
           image:'livepush/filter/rix.png',
           ima_name:'tuibeauty_filter_rixi.png',
         },
       ],
       showGoods_mp3:false,
       dtask: null,
       ios:uni.getDeviceInfo().platform,
       ads:'',
        // end
      width: '750rpx',
      height: '1500rpx',
      reconnect:false,
      statusBarHeight: sysInfo.statusBarHeight,
      bodyHeight: sysInfo.screenHeight,
      // livePusherHeight: sysInfo.screenHeight - sysInfo.statusBarHeight,
      livePusherHeight: sysInfo.windowHeight,	
      beautyFlag:1,
      beautySwitch:false,//美颜开关
      fliterSwitch:false,//滤镜开关
        
			}
		},
    components: {
    	livePlayGoods,
    	dataLoading,
    	dataLoaded,
    	uniPopup,
      MhMsgList
    },
    computed: {
    	...mapState(['hasLogin'])
    },
		onLoad(option) {
			var page = this;
      uni.setKeepScreenOn({
      	keepScreenOn: true
      });
      uni.getSystemInfo({
        success:function(res){
          page.screenHeight = res.screenHeight
        }
      })
      page.live_id = option.live_id;
      page.backGrade = option.backGrade;
      page.forbidReason = option.forbidReason;
      page.shareImg = getApp().globalData.apiUrl + 'v3/video/front/video/live/share/shareImage?liveId=' +
      page.live_id + '&source=' + page.stat_end;
      plus.globalEvent.addEventListener('isPushing', function(e){
        console.log('isPushing',e)
      });
      plus.globalEvent.addEventListener('onPushStatusUpdate', function(e){
        console.log('onPushStatusUpdate',e)
      });
      this.getMusicList()
		},
    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function() {
      this.pusher = false
    	if (this.socket) {
    		this.closeSocket();
    	}
    	uni.$emit('updateLiveList');
    },
    onBackPress() {
      this.stopPush()
      setTimeout(()=>{
      this.$refs.txView.stopCamera()
        
      })
      if(this.musicId){
        this.$refs.txView.stopPlayMusic({
           id:this.musicId,
       }, (res) => { 
       })
           that.musicId = ''
          that.music_list.forEach((it=>{
            it.type = 1
          })) 
          that.musicInfo[0].type = 1
      }
    },
		onReady() {
			var page = this;
      //获取手机信息，并设置高度和宽度
      try {
        const res = uni.getSystemInfoSync();
      	this.width = res.windowWidth;
      	this.height = res.windowHeight;
      } catch (e) {
          // error
      }
      setTimeout(()=>{
      this.power()
        
      },100)
			
					
		},
    onShow() {
     
    },
    watch:{
      beautySwitch:{
      	handler(nv,ov){
      		if(nv){
              let setBeautyLevel;
              let setWhitenessLevel;
              let setRuddyLevel;
              setBeautyLevel = Number(this.beauty)*0.1
              setWhitenessLevel = Number(this.whiteness)*0.1
              setRuddyLevel = Number(this.ruddy)*0.1
            this.$refs.txView.setBeautyLevel({
                beautyLevel:setBeautyLevel
            });
            this.$refs.txView.setWhitenessLevel({
                whitenessLevel:setWhitenessLevel
            });
            this.$refs.txView.setRuddyLevel({
                ruddyLevel:setRuddyLevel
            });
          }
      	},
      	deep:true
      }
    },
		methods: {
      // 音乐列表下拉
      onPulling(){
        this.triggered = true
      },
      refreshMoreMusic(e){
        this.musicCurrent = 1
        this.getMusicList()
      },
      // 输入框 （搜索歌曲和歌手）
      inputChange(e) {
      	this.input_val = e.detail.value.trim()
        if(e.detail.value.trim()==''){
          this.musicCurrent = 1
          this.getMusicList()
        }
      },
      // 歌曲搜索
      search(){
        let _this = this
        _this.musicCurrent = 1
      		if(this.input_val !=''){
      			this.getMusicList()
      		}else{
      			this.getMusicList()
      		}    
      },
      // 清除输入框 （搜索歌曲和歌手）
      clearInputVal(){
        this.input_val = ''
        this.scrollMusic = 'music_list_id'
        this.musicCurrent = 1
        this.getMusicList()
      },
      // 音量设置弹框返回到歌曲列表弹框
      showGoods_mp3_volume_type(type){
        if(type==1){
          this.$refs.showGoods_mp3_volume.close()
          setTimeout(()=>{
            this.$refs.showGoods_mp3.open()
          },300)
          
        }else{
          this.$refs.showGoods_mp3.close()
          this.$refs.showGoods_mp3_volume.open()
          
        }
      },
      // 音乐列表下滑触发
      getMoreMusic(){
       if( this.musicTotal>this.music_list.length){
         this.musicCurrent+=1
         this.getMusicList()
       }
      },
      // 音乐列表
      getMusicList(){
        let _this = this
        let params = {};
        params.method = 'GET';
        params.url = 'v3/video/front/video/music/library/list';
        params.data = {};
        params.data.pageSize = _this.pageSize;
        params.data.current = _this.musicCurrent;
        if(_this.musictype==1){ 
          params.data.isRecommend = 1;
        }
        if(this.input_val){
          params.data.keyword = this.input_val;
        }
        request(params).then(result => {
        	if (result.state == 200) {
             _this.triggered = false
             _this.scrollMusic = ''
            _this.musicTotal = result.data.pagination.total
            result.data.list.forEach(item=>{
              item.type = 1
            })
            if(_this.musicCurrent==1){
              _this.music_list = result.data.list 
            }else{
              _this.music_list = _this.music_list.concat(result.data.list)
            }
        	}
        })
      },
      // 权限判断
      async power(){
        if(plus.os.name == 'iOS'){
          this.initData();
        }else{
          // 权限判断 摄像头 安卓
          let result = await permision.requestAndroidPermission('android.permission.CAMERA')
          let strStatus
          // result 1 已授权
          if (result == 1) {
             // 权限判断 麦克风 安卓
            let results = await permision.requestAndroidPermission('android.permission.RECORD_AUDIO')
            if(results==1){
              this.initData();
            }
          } else if (result == 0) {
            strStatus = "未获得授权"
          } else {
            strStatus = "被永久拒绝权限"
          }       
        }
      },
      onTel(e) {
        console.log('onTel',e) 
      },
      // 音乐弹框切换 val==1正在播放和 val==2背景音乐列表
      music_select(val){
        this.musicFlag = val
      },
      // 背景音乐列表推荐和全部切换
      volumeType(val){
        this.musictype = val
        this.scrollMusic = 'music_list_id'
        this.musicCurrent = 1
        this.getMusicList()
      },
      // 歌曲停止
      tingBGMFiles(item,index){
        let that = this
        if(this.musicId){
          this.$refs.txView.stopPlayMusic({
             id:this.musicId,
         }, (res) => {
             
         })
             that.musicId = ''
            that.music_list.forEach((it=>{
              it.type = 1
            })) 
            that.musicInfo[0].type = 1
        }
      },
      // 播放歌曲
      selectBGMFiles(item,index,type){
        let that = this
        that.music_list.forEach((it=>{
          it.type = 1
        })) 
      if(this.musicId){
        this.$refs.txView.stopPlayMusic({
           id:this.musicId,
       }, (res) => {
             
       })
             
      }
       this.$refs.txView.startPlayMusic({
           id:item.musicLibraryId,
           path:item.musicLink,
           publish:true,
           loopCount:1000,
       }, (res) => {
            that.musicInfo = []
            item.type = 2
            let aa
           that.musicInfo.push(item)
            that.musicId = item.musicLibraryId
            if(type==1){
              let ind = ''
              that.music_list.forEach((it,index)=>{
                if(it.musicLibraryId==item.musicLibraryId){
                  ind = index
                }
              })
              that.music_list[ind].type = 2
            }else{
              that.music_list[index].type = 2
            }
       });
      },
      // 设置某一首背景音乐的远端音量的大小
      musicChange(e){
        let that =this
        that.music_slider = e.detail.value
        this.$refs.txView.setAllMusicVolume({
            volume:e.detail.value,
        }, (res) => {
            
        });
      },
      // 打开美颜弹窗
      beatyOpen(){
        this.$refs.stop_slider.open()
      },
      // 滤镜强度
      filterChange(e){
       let that = this
       that.filter_slider = e.detail.value 
       that.$refs.txView.setFilterStrength({
           filterStrength:Number(e.detail.value)*0.01
       }, (res) => {
           that.filter_slider = e.detail.value
       });
      },
      // 美颜
      sliderChange(e){
        let that = this
        that.beauty = e.detail.value
        this.$refs.txView.setBeautyLevel({
            beautyLevel:Number(e.detail.value)*0.1
        }, (res) => {
            that.beauty = e.detail.value
        });
      },
      // 美白
      whitenessChange(e){
        let that = this
        that.whiteness = e.detail.value
        this.$refs.txView.setWhitenessLevel({
            whitenessLevel:Number(e.detail.value)*0.1
        }, res => {
            that.whiteness = e.detail.value
        });
      },
      // 红润
      ruddyChange(e){
        let that = this
        that.ruddy = e.detail.value
        this.$refs.txView.setRuddyLevel({
            ruddyLevel:Number(e.detail.value)*0.1
        }, (res) => {
            that.ruddy = e.detail.value
        });
      },
      // 美颜开关
      switchChange(type){
        if(type==1){
          this.beautySwitch = false
          let setBeautyLevel;
          let setWhitenessLevel;
          let setRuddyLevel;
          if(this.beautySwitch){
            setBeautyLevel = Number(this.beauty)*0.1
            setWhitenessLevel = Number(this.whiteness)*0.1
            setRuddyLevel = Number(this.ruddy)*0.1
          }else{
            setBeautyLevel = 0
            setWhitenessLevel = 0
            setRuddyLevel = 0
          }
          this.$refs.txView.setBeautyLevel({
              beautyLevel:setBeautyLevel
          });
          this.$refs.txView.setWhitenessLevel({
              whitenessLevel:setWhitenessLevel
          });
          this.$refs.txView.setRuddyLevel({
              ruddyLevel:setRuddyLevel
          });
        }else{
          this.fliterSwitch = false
          this.filterindex = -1
          this.$refs.txView.setFilter({
              image:''
          }, res => {
          });
        }
      },
      // 美颜选中
      beauty_select(val){
        this.beautySwitch = true
        this.beautyFlag = val
      },
      // 滤镜选择
      fliter_select(item,index){
        this.fliterSwitch = true
        this.filterindex = index
        this.$refs.txView.setFilter({
            image:plus.io.convertLocalFileSystemURL('../../static/app-plus/filter/'+item.ima_name)
        }, res => {
                
        });
      },
      // 切换美颜和滤镜
      Manager_select(val){
        this.Manager = val
        
      },
      //开启闪光灯 
      dingClick(){
        if(this.front == 'back'){
          if(this.torchFlag!=true){
            // 开启/关闭闪光灯，也就是手电筒模式
            this.$refs.txView.enableCameraTorch({
              enable:true
            }, res => {
                this.torchFlag = true
            });
          }else{
             // 开启/关闭闪光灯，也就是手电筒模式
            this.$refs.txView.enableCameraTorch({
              enable:false
            }, res => {
                this.torchFlag = false
            });
          }       
        }else{
          uni.showToast({
              title: '只有后置摄像头才能开启闪光灯',
              icon: 'none',
              duration: 1000
          })
        }
      },
      // 切换摄像头
      switchCamera(){
        let that = this
       if(this.front=='front'){
        that.$refs.txView.switchCamera({
            frontCamera:false
          }, res => {
              that.front = 'back'//返回0成功 1失败 检查推流地址    -5  授权信息错误   
        });
       }else{
         that.$refs.txView.switchCamera({
             frontCamera:true
           }, res => {
                that.front = 'front'
                that.torchFlag = false
                
         });
       }
      },
      // 打开背景音乐弹框
      bgmPopOpne(){
        let that = this
         this.$refs.showGoods_mp3.open()
      },
      // 插件里的方法 rpx转化为px
      rpxToPxFloat(rpx) {
      	return rpxTool.rpxToPxFloat(rpx); 
      },
      // 结束返回
      endBack() {
      	let {
      		backGrade
      	} = this;
      	this.enableCamra = false;
      	this.enableMic = false;
      	const pages = getCurrentPages(); //获取页面栈
      	const beforePage = pages[pages.length - (backGrade * 1 + 1)]; //前一个页面
      	if (beforePage&&beforePage.route.indexOf('myVideo') != -1) {
      		beforePage.$vm.changeTab('goods');
      	}
      	// this.$Router.back(backGrade * 1);
        uni.navigateBack({
          delta:backGrade * 1
        })
      },
      close_showGoods_mp3(){
        this.showGoods_mp3 = false
      },
      //初始化数据
      initData() {
      //初始化
       this.$refs.txView.initPusher({
           licenceURL: getApp().globalData.licenceURL,
           licenceKey: getApp().globalData.licenceKey,
       }, res => {
           console.log('initPusher',res);  //返回0成功 1失败 检查推流地址    -5  授权信息错误
           if(res==0){
           }
       });
        setTimeout(()=>{
          this.$refs.txView.startCamera()
        },100) 
      	this.getLiveInfo();
      	this.getLiveGoods();
      },
      //开始直播
      startLive() {
      	if (this.forbidReason) {
      		this.applicationShowTip(this.forbidReason);
      	} else {
      		uni.showLoading({
      			title: '直播开启中...'
      		})
      		this.operateLive('start');
      	}
      },
      // 获取推流地址
      operateLive(type) {
      	const _this = this;
      	if (type == 'start') {
      		//获取推流地址
      		let params = {};
      		params.method = 'POST';
      		params.url = 'v3/video/front/video/live/getLivePath';
      		params.data = {};
      		params.data.liveId = _this.live_id;
      		request(params).then(result => {
      			if (result.state == 200) {
      				_this.pushurl = result.data.livePath;
      				setTimeout(()=>{
                _this.$refs.txView.startPush({
                    url: _this.pushurl
                }, ret => {
                    if(ret.result==0){
                      _this.live_state = true;
                      uni.hideLoading()
                       _this.initSocket();
                    }else{
                      uni.hideLoading()
                    }
                })
              },200) //开始推流
      			}
      		})
      	} else {
      		// 处理返回列表页 后退跳转层级重复问题
      		let pages = getCurrentPages();
      		let flag = true;
      		let preIndex = '';
      		pages.forEach((item, index) => {
      			if (item.route == "extra/svideo/myVideo") {
      				if (!preIndex) {
      					preIndex = index;
      				}
      			} else if (item.route == "extra/livepushs/livepushs") {
      				flag = false;
      		    uni.navigateBack({
      		      delta:index - preIndex
      		    })
      			}
      		})
      		if (flag) {
      			uni.navigateBack({
      			  delta:1
      			})
      		}
      		//结束推流
      	}
      },
      // 获取直播详情
      getLiveInfo() {
      	let {
      		live_id
      	} = this;
      	let _this = this
      	let param = {};
      	param.url = 'v3/video/front/video/live/playingPage';
      	param.data = {
      		liveId: live_id
      	}
      	request(param).then(res => {
      		if (res.state == 200) {
      			let liveDetail = res.data;
      			_this.liveDetail = liveDetail;
      			if (liveDetail.liveState == 5) {
      				// 被删除
      				uni.showModal({
      					title: _this.L('提示'),
      					content: _this.L('该直播已被删除'),
      					showCancel: false,
      					confirmText: _this.L('返回'),
      					success: res => {
      						if (res.confirm) {
      							// uni.navigateBack({});
                    uni.navigateBack({
                      delta:1
                    })
      						}
      					}
      				});
      			} else if (liveDetail.playState == 2) {
      				//禁止播放
      				uni.showModal({
      					title: res.data.msg.msg,
      					content: res.data.msg.remark,
      					showCancel: false,
      					confirmText: _this.L('返回'),
      					success: res => {
      						if (res.confirm) {
      							// uni.navigateBack({});
      							uni.navigateBack({
      							  delta:1
      							})
      						}
      					}
      				});
      			} else if (liveDetail.liveState == 3) {
      				//录制中
      				uni.showModal({
      					title: '',
      					content: _this.L('正在录制中，录制完成才可以观看哦！'),
      					showCancel: false,
      					confirmText: _this.L('返回'),
      					success: res => {
      						if (res.confirm) {
      							// uni.navigateBack({});
      							uni.navigateBack({
      							  delta:1
      							})
      						}
      					}
      				});
      			}
      		} else {
      			//禁止播放
      			uni.showModal({
      				title: res.msg.msg,
      				content: res.msg.remark,
      				showCancel: false,
      				confirmText: this.L('返回'),
      				success: res => {
      					if (res.confirm) {
      						// uni.navigateBack({});
      						uni.navigateBack({
      						  delta:1
      						})
      					}
      				}
      			});
      		}
      	})
      },
      //获取直播商品
      getLiveGoods() {
      	this.loading = true;
      	let {
      		goodsList,
      		hasmore,
      		live_id
      	} = this;
      	let param = {};
      	param.method = 'GET';
      	param.url = 'v3/video/front/video/live/goodsList';
      	param.data = {
      		current: this.pn,
      		pageSize: this.pageSize,
      		liveId: live_id
      	};
      	request(param).then(res => {
      		let list = res.data.list;
      		if (this.pn == 1) {
      			this.goodsList = list;
      		} else {
      			this.goodsList = this.goodsList.concat(list);
      		}
      		if (checkPageHasMore(res.data.pagination)) {
      			this.pn++;
      		} else {
      			this.hasmore = false;
      		}
      		this.loading = false;
      	})
      },
      //关闭socket
      closeSocket() {
          
      	if (this.socket) {
      		this.socket.close();
      	}
      },
      initSocket() {
      	if (this.socket) {
      		this.closeSocket();
      	}
      	const {
      		live_id,
      		liveDetail
      	} = this;
      	const authorInfo = liveDetail;
      	let userInfo = {
      		live_id: live_id ? live_id : 1,
      		is_own: 1
      	};
      
      	if (authorInfo.authorId) {
      		userInfo.author_name = authorInfo.memberNickname ? authorInfo.memberNickname : authorInfo.memberName;
      		userInfo.author_id = authorInfo.authorId;
      	}
      
      	this.socket = io(getApp().globalData.socketUrl, {
      		reconnection: true,
      		jsonp: true,
      		transports: ['websocket', 'polling'],
      		timeout: 5000,
      		query: `author_name=${userInfo.author_name}&author_id=${userInfo.author_id}&live_id=${userInfo.live_id}&is_own=1`
      	});
      	this.socket.on("connect", () => {
      		this.isSocketInit = true
      		//给服务端发送消息
      		this.socket.emit("update_user", userInfo); //获取服务端的消息
      		if (this.liveDetail.isSelf != 1) {
      			this.socket.emit("update_user", userInfo); //获取服务端的消息
      		}
      
      		this.socket.on("get_msg", e => {
      			if (typeof e.msg === 'string') {
      				this.socketHandle('wec', e);
      			} else {
      				this.socketHandle('msg', e.msg);
      			}
      		}); //获取服务端新人信息
      
      		this.socket.on("get_new_user", e => {
      			this.socketHandle2('new', e);
      		});
      		this.socket.on("disconnect", function() {});
      	}); //获取关注的返回信息
      
      	this.socket.on("get_follow_msg", e => {
      		this.socketHandle2('follow', e);
      	}); //获取直播点击数
      
      	this.socket.on("get_click_num", e => {
      		this.clickNumHandle(e);
      	}); //获取直播人气数
      
      	this.socket.on("get_like_num", e => {
      		this.likeNumHandle(e);
      	}); // 直播结束
      
      	this.socket.on('stop_live', e => {
      		console.log('stop_live')
      		this.handleStopLive(e);
      	});
      },
      // 结束直播
      stopLiveTip() {
        let that = this
      	if (that.isStopLive) {
      		return;
      	}
      	that.isStopLive = true;
      	uni.showModal({
      		title: that.L('温馨提示!'),
      		content: that.L('确定结束该场直播吗？'),
      		success: res => {
      			if (res.confirm) {
              that.stopPush()
      				that.stopLive();
      			} else {
      				that.isStopLive = false;
      			}
      		}
      	});
      },
      
      //结束直播
      stopLive() {
      	this.operateLive('end');
      },
      //结束直播
      stopPush(){
        this.$refs.txView.stopPush()
      },
      
      //直播结束 
      handleStopLive(e) {
      	let {
      		backGrade,
      		liveDetail
      	} = this;
      	this.liveStateInfo = JSON.parse(e);
      	this.$refs.stopVideo.open();
      },
      // 人气
      likeNumHandle(e) {
      	let {
      		liveDetail,
      		live_like_num
      	} = this;
      	liveDetail.likeNum = e.like_num;
      	this.liveDetail = liveDetail;
      	this.live_like_num = initNum(e.like_num);
      },
      // 点击量处理
      clickNumHandle(e) {
      	let {
      		liveDetail,
      		live_click_num
      	} = this;
      	liveDetail.clickNum = e.click_num;
      	this.liveDetail = liveDetail;
      	this.live_click_num = initNum(e.click_num);
      },
      // 商品
      showGoodsFun() {
      	let {
      		showGoods,
      		liveDetail
      	} = this;
      	let that = this;
      	if (liveDetail.goodsNum * 1 > 0) {
      		that.showGoods = true;
      	}
      },
      //关闭商品弹层
      closeGoods() {
      	let {
      		showGoods
      	} = this;
      	let that = this;
      	that.showGoods = false;
      },
      socketHandle(type, msg) {
      	let {
      		msgList
      	} = this;
      
      	if (msg.type) {
      		return;
      	}
      
      	msg.type = type;
      	msg.color_random = colorArray[Math.floor(Math.random() * 8)];
      	msgList.push(msg);
      	this.msgList = msgList,
      	setTimeout(()=>{
          this.toBottom = msgList && msgList.length > 0 ? 'item' + (msgList.length - 1) : 0
        },100)
      },
      
      //获取服务端新人信息
      socketHandle2(type, msg) {
      	let {
      		noticeList
      	} = this;
      
      	if (msg.type) {
      		return;
      	}
      
      	if (noticeList.filter(el => el.type === 'follow').length > 0 && type === 'follow') {
      		return;
      	}
      
      	msg.type = type;
      	msg.timer = setTimeout(() => {
      		this.noticeRemoveItem(type);
      	}, 2000);
      
      	if (type === 'follow') {
      		noticeList.push(msg);
      	} else {
      		let index = noticeList.findIndex(el => el.type === 'new');
      
      		if (index > -1) {
      			noticeList[index] = msg;
      		} else {
      			noticeList.push(msg);
      		}
      	}
      	this.noticeList = noticeList;
      },
      
      noticeRemoveItem(type) {
      	let {
      		noticeList
      	} = this;
      	let index = noticeList.findIndex(el => el.type === type);
      
      	if (index > -1) {
      		clearTimeout(noticeList[index].timer);
      		noticeList.splice(index, 1);
      	}
      
      	this.noticeList = noticeList;
      
      },
      // 发送消息
      publishComment(e) {
      	const {
      		live_id,
      		liveDetail,
      		live_state
      	} = this;
      
      	if (!live_state) {
      		//直播开始后才可以聊天
      		uni.showToast({
      			title: this.L('直播开始后才可以聊天'),
      			icon: 'none'
      		});
      		return false;
      	}
      
      	if (this.hasLogin && liveDetail.authorId != null) {
      		let content = e.detail.value; //没有内容的话直接结束
      
      		if (!content.trim()) {
      			return false;
      		}
      
      		let msg = {
      			author_id: liveDetail.authorId,
      			author_name: liveDetail.memberNickname,
      			live_id: live_id,
      			msg: content
      		};
      		this.socket.emit("send_msg", msg); //清空输入框的内容
      		var that = this;
      		that.input_val = '';
      		setTimeout(() => {
      			that.input_val = ''
      		})
      	} else {
      		uni.showModal({
      			title: this.L('温馨提示!'),
      			content: this.L('需要登录才能操作'),
      			confirmText: this.L('确定'),
      			cancelText:this.L('取消'),
      			success: res => {
      				if (res.confirm) {
      					getApp().globalData.goLogin();
      				}
      			}
      		});
      	}
      },
      // 禁止提示
      applicationShowTip(tip) {
      	uni.showModal({
      		title: this.L('提示'),
      		content: tip,
      		showCancel: false
      	})
      },
      //分享点击事件
      showShare() {
      	let that = this;
      	if (this.forbidReason) {
      		this.applicationShowTip(this.forbidReason);
      	} else if (!that.live_state) {
          // that.shareWrap2 = true;
      		uni.showToast({
      			title: this.L('直播开始后才可以分享哦！'),
      			icon: 'none'
      		});
      	} else {
      		that.shareWrap2 = true;
      	}
      },
      //生成海报事件
      createhb() {
      	this.shareWrap = true;
      	this.shareWrap2 = false;
      },
      //关闭分享
      closeShare() {
      	let that = this;
      	that.shareWrap = false;
      	that.shareWrap2 = false;
      
      },
      //分享 type：分享类型 0 图文 2图片，scene 场景 WXSceneSession：分享朋友  WXSenceTimeline：分享朋友圈
      sldShare(type, scene) {
      
      	let {
      		liveDetail,
      		live_id,
      		shareImg
      	} = this;
      	let shareData = {};
      	if (type == 0) {
      		shareData.href = getApp().globalData.apiUrl + 'extra/live/livePlay?live_id=' + live_id;
      		shareData.title = this.L('我正在播') + liveDetail.memberNickname ? liveDetail.memberNickname : liveDetail
      			.memberName +
      			this.L('的精彩内容，快来围观~');
      		shareData.summary = liveDetail.liveName;
      		shareData.imageUrl = liveDetail.liveCover;
      	} else if (type == 2) {
      		shareData.imageUrl = shareImg;
      	}
      
      	this.weiXinAppShare(type, scene, shareData);
      	this.closeShare(); //关闭分享
      },
      //微信分享
      weiXinAppShare(type, scene, shareData) {
      	if(type == 0){
      		//分享图文
      		uni.share({
      		    provider: "weixin",
      		    scene: scene,
      		    type: type,//0为图文
      		    href: shareData.href,
      		    title: shareData.title,
      		    summary: shareData.summary,
      		    imageUrl: shareData.imageUrl,//图片,图片过大的话不展示，建议小于20kb
      		    success: function (res) {},
      		    fail: function (err) {}
      		});
      	}else if(type == 2){
      		//分享图片
      		uni.share({
      		    provider: "weixin",
      		    scene: scene,
      		    type: type,//2为图片
      		    imageUrl: shareData.imageUrl,//图片,图片过大的话不展示，建议小于20kb
      		    success: function (res) {},
      		    fail: function (err) {}
      		});
      	}
      },
      //下载海报
      downloadImg() {
      	let {
      		shareImg
      	} = this;
      
      	let _this = this;
      
      	uni.downloadFile({
      		url: shareImg,
      		success: res_info => {
      			if (res_info.statusCode == 200) {
					//app-1-start
      				//#ifdef APP-PLUS
      				_this.saveHb(res_info.tempFilePath);
      				//#endif
					//app-1-end
      			} else {
      				uni.showToast({
      					title: this.L('下载失败'),
      					icon: 'none'
      				});
      			}
      		}
      	});
      },
      /**
       * 保存图片
       */
      saveHb: function(img) {
      	let _this = this;
      
      	uni.saveImageToPhotosAlbum({
      		filePath: img,
      		success: function(data) {
      			_this.shareWrap = false;
      			_this.shareWrap2 = false;
      
      			uni.showToast({
      				title: _this.L('已保存到本地'),
      				icon: 'success',
      				duration: 2000
      			});
      		},
      		complete: function(res) {}
      	});
      },
      // 返回上级页面提示
      goBack() {
      	let {
      		live_state
      	} = this;
      	let _this = this
      	if (live_state) {
      		uni.showModal({
      			title: this.L('温馨提示!'),
      			content: this.L('返回上级页面将关闭直该场直播，确认返回吗？'),
      			confirmText: this.L('确定'),
      			cancelText:this.L('取消'),
      			success: res => {
      				if (res.confirm) {
      					this.enableCamra = false;
      					this.enableMic = false;
      					this.stopLive();
      				}
      			}
      		});
      	} else {
      		uni.showModal({
      			title: this.L('温馨提示!'),
      			content: this.L('直播还未开始，确认返回上级页面吗？'),
      			confirmText: this.L('确定'),
      			cancelText:this.L('取消'),
      			success: res => {
      				if (res.confirm) {
      					this.enableCamra = false;
      					this.enableMic = false;
      					// 处理返回列表页 后退跳转层级重复问题
      					let pages = getCurrentPages();
      					let flag = true; 
      					let preIndex = '';
      					pages.forEach((item, index) => {
      						if (item.route == "extra/svideo/myVideo") {
      							if (!preIndex) {
      								preIndex = index;
      							}
      						} else if (item.route == "extra/livepushs/livepushs") {
      							flag = false;
                    setTimeout(()=>{
                      this.$refs.txView.stopCamera();
                    })
                    setTimeout(()=>{
                      uni.navigateBack({
                        delta:index - preIndex
                      })      
                    },100)
      						}
      					})
      					if (flag) {
                  setTimeout(()=>{
                    this.$refs.txView.stopCamera();
                  })
                  setTimeout(()=>{
                    uni.navigateBack({
                      delta:1
                    })
                  },100)
      					}
      				}
      			}
      		});
      	}
      },
			
		
		}
	}
</script>

<style scoped>
  
.live_back,
.living {
	width: 750rpx;
	height: 100vh;
}

/* app-2-start */
/* #ifdef APP-NVUE */
.haiicon {
	font-family: haiicon;
	font-size: 30rpx;
	font-style: normal;
	text-decoration: none;
	text-align: center;
}
/* #endif */
/* app-2-end */
  
.body{
	width: 750;
	background-color: #000000;
	overflow: hidden;
  
}

.toolDiv{
	position: fixed;
	right: 20rpx;
	top: 90rpx;
	/* width: 750rpx; */
  margin-top: 60rpx;
	flex-direction: column;
	/* justify-content: flex-start; */
	align-items: center;
  z-index: 9;
}
.toolDiv-star{
	width: 490rpx;
	height: 94rpx;
	border-radius: 45rpx;
	background-color: #fe2c53;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	margin-top:40rpx;
}
.toolDiv-star-txt{
	font-size: 30rpx;
	color: #FFFFFF;
	text-align: center;
}

.toolDiv-list{
	/* width: 750rpx; */
	flex-direction: column;
	justify-content: flex-start;
	align-items: flex-end;
	flex-wrap: wrap;
	
}
.toolDiv-list-item{
  display: flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: center;
	width: 84rpx;
	margin-top:48rpx;
	margin-left: 10rpx;
	margin-right: 10rpx;
	
}
.toolDiv-list-item-txt{
	font-size: 24rpx;
	color: #F1F1F1;
	
}

.toolDiv-list-item-iconYan{
	font-size: 38rpx;
	color: #FFFFFF;
	margin-bottom: 2rpx;
}
.toolDiv-list-item-cionLujing{
	font-size: 50rpx;
	color: #f9f9f9;
	/* margin-top: -8rpx; */
}
.toolDiv-list-item-cionLujing2{
	/* margin-top: -4rpx; */
}
.toolDiv-list-item-iconHuan{
	font-size: 40rpx;
	color: #FFFFFF;
	margin-top: 3rpx;
}
.toolDiv-list-item-iconDing{
	font-size: 55rpx;
	color: #FFFFFF;
	/* margin-top: -10rpx; */
}


.live_footer {
		position: fixed;
		z-index: 99;
		left: 40rpx;
		bottom: 30rpx;
		width: 710rpx;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
		overflow-x: hidden;
		padding-top: 30rpx;
	}

	.live_footer_goods {
		width: 85rpx;
		height: 91rpx;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		position: relative;
	}

	.live_footer_goods_img {
		width: 85rpx;
		height: 91rpx;
	}

	.live_footer_goods_texts {
		position: absolute;
		bottom: 12rpx;
		line-height: 23rpx;
		color: #fff;
		font-size: 22rpx;
	}

	.live_footer_talk_con {
		height: 65rpx;
		width: 325rpx;
		margin-left: 30rpx;
		border-radius: 30rpx;
		background: rgba(0, 0, 0, 0.3);
		padding: 0 20rpx;
		color: #fff;
		font-weight: 600;
		font-size: 26rpx !important;
	}

	.live_footer_share {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: center;
		width: 77rpx;
		margin-left: 21rpx;
	}

	.live_footer_share_img {
		width: 65rpx;
		height: 65rpx;
		font-weight: 600;
	}
  .add_heart{
    width: 90rpx;
  }

	.live_footer_text {
		color: #fff;
		font-size: 22rpx;
		margin-top: 10rpx;
	}
/***** 弹幕 start *****/

	.barrage_wrap {
		position: fixed;
		bottom: 200rpx;
		left: 30rpx;
		width: 500rpx;
		background-color: transparent;
		z-index: 99;
		height: 500rpx;
		display: flex;
		flex-direction: column;
		justify-content: flex-end;
		align-items: flex-start;
	}

	.notice {
		height: 50rpx;
		max-width: 500rpx;
		display: inline-block;
		border-radius: 10rpx;
		line-height: 50rpx;
		margin-bottom: 10rpx;
	}

	.notice_text {
		color: #fff;
		font-size: 24rpx;
		height: 50rpx;
		max-width: 500rpx;
		display: inline-block;
		background-color: #fc1c1c;
		border-radius: 10rpx;
		line-height: 50rpx;
		margin-bottom: 10rpx;
		padding: 0 15rpx;
		font-weight: 600;
	}

	.barrage {
		min-height: 100rpx;
		max-height: 450rpx;
	}

	.gonggao {
		background-color: rgba(0, 0, 0, 0.3);
		white-space: normal;
		padding: 12rpx 16rpx;
		border-radius: 10rpx;
		word-break: break-all;
		font-weight: 600;
	}

	.gonggao_title {
		color: #fc1c1c;
		font-size: 26rpx;
		line-height: 36rpx;
    display: inline-block;
	}

	.gonggao_ba_txt {
		color: #fff;
		font-size: 26rpx;
		line-height: 36rpx;
	}

	.barrage_item {
		background-color: rgba(0, 0, 0, 0.3);
		max-width: 500rpx;
    min-width: 100rpx;
		border-radius: 23rpx;
		padding: 0rpx 15rpx 10rpx 15rpx;
    padding-top: 15rpx;
		margin-top: 10rpx;
		white-space: normal;
		word-break: break-all;
		font-weight: 600;
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    word-break: break-all;
	}

	.barrage_item_name {
		font-size: 25rpx;
		line-height: 36rpx;
		margin-right: 15rpx;
	}

	.barrage_item_con {
		color: #fff;
    max-width: 450rpx;
    line-height: 36rpx;
		font-size: 25rpx;
    word-break: break-all;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
	}

	/***** 弹幕 end *****/
  
  /* 视频绑定的商品模块 start */
  
  .live_bind_goods_mask {
  	width: 750rpx;
  	height: 100vh;
  	position: absolute;
  	left: 0;
  	bottom: 0;
  	z-index: 999;
  	background: rgba(0, 0, 0, 0.45);
  }
  
  .live_bind_goods {
  	width: 750rpx;
  	height: 850rpx;
  	position: fixed;
  	left: 0;
  	bottom: 0;
  	z-index: 4;
  	background: #fff;
  	border-radius: 15rpx 15rpx 0 0;
  	background: #f8f8f8;
  }
  
  .live_bind_goods_header {
  	width: 750rpx;
  	height: 100rpx;
  	padding: 0 20rpx;
  	background: #fff;
  	display: flex;
  	flex-direction: row;
  	justify-content: space-between;
  	align-items: center;
  	border-radius: 15rpx 15rpx 0 0;
  }
  
  .live_bind_goods_header_text {
  	color: #2d2d2d;
  	font-size: 32rpx;
  }
  
  .live_bind_image {
  	width: 47rpx;
  	height: 47rpx;
  }
  
  .scroll_goods {
  	height: 750rpx;
  }
  
  /* 视频绑定的商品模块 end */
  /* 分享 start */
  .select-wrap {
  	position: fixed;
  	top: 0;
  	left: 0;
  	width: 750rpx;
  	background-color: rgba(0, 0, 0, 0.45);
  	z-index: 999;
  }
  
  .share-mode {
  	position: absolute;
  	width: 750rpx;
  	height: 100%;
  	display: flex;
  	flex-direction: column;
  	align-items: center;
    bottom: 20rpx;
    z-index: 10000;
  }
  
  .share-img {
  	width: 750rpx;
    height: 700rpx;
  	border-radius: 30rpx;
  	overflow: hidden;
    margin-bottom: 100rpx;
  }
  
  .share-img-image {
  	width: 540rpx;
    /* height: 700rpx; */
  	border-radius: 20rpx;
  }
  
  .share-mode-ul {
  	display: flex;
    flex-direction: row;
  	align-items: center;
    justify-content: space-around;
  	width: 750rpx;
  }
  
  .share-mode-item {
  	display: flex;
  	flex-direction: column;
  	align-items: center;
  	text-align: center;
  	cursor: pointer;
  	border: none;
  	margin: 0;
  	padding: 0;
  	line-height: 1;
  	background-color: transparent;
  	height: auto !important;
  }
  
  .share-mode-item::after {
  	border: none;
  }
  
  .share-mode-item-image {
  	width: 106rpx;
  	height:106rpx;
  }
  
  .share-mode-item-text {
  	color: #fff;
  	font-size: 24rpx;
  	margin-top: 30rpx;
  }
  
  .select-wrap-close {
  	width: 750rpx;
  	height: 120rpx;
  	display: flex;
  	align-items: center;
  	justify-content: center;
    margin-bottom: 30rpx;
  }
  
  .select-wrap-close-image {
  	width: 30px;
  	height: 30px;
  }
  
  .share-img {
  	flex: 1;
  	display: flex;
  	align-items: center;
  	justify-content: center;
  }
  
  /* 分享 end */
  
  
  /* 头部头像 人气等 */
  .live_header {
  	position: fixed;
  	left: 20rpx;
  	z-index: 9999;
  	display: flex;
  	flex-direction: row;
  	align-items: center;
  	justify-content: flex-start;
  	height: 80rpx;
  }
  
  .live_header_text {
  	color: #fff;
  }
  
  .live_header_go_back {
  	width: 45rpx;
  	height: 47rpx;
  }
  
  .live_header_avator {
  	width: 78rpx;
  	height: 78rpx;
  	border-radius: 50%;
  	border: 2px solid #fff;
  	margin-left: 8rpx;
  }
  
  .live_mem_info {
  	margin-left: 20rpx;
  	display: flex;
  	flex-direction: column;
  	align-items: flex-start;
  	justify-content: space-between;
  }
  
  .live_mem_info_name {
  	max-width: 150rpx;
  	color: #fff;
  	font-size: 28rpx;
  	line-height: 32rpx;
  	overflow: hidden;
  	text-overflow: ellipsis;
  	white-space: nowrap;
  	font-weight: 600;
  	margin: 5rpx 0 6rpx;
  }
  
  .stat_num {
  	display: flex;
  	flex-direction: row;
  	justify-content: flex-start;
  }
  
  .stat_num_text {
  	color: #fff;
  	font-size: 22rpx;
  	line-height: 36rpx;
  	white-space: nowrap;
  	font-weight: 600;
  }
  
  .live_fllow {
  	display: flex;
  	flex-direction: row;
  	justify-content: center;
  	align-items: center;
  	width: 120rpx;
  	height: 50rpx;
  	background: #fc1c1c;
  	border-radius: 25rpx;
  	margin-left: 25rpx;
  }
  
  .live_header .live_fllow image {
  	width: 46rpx;
  	height: 46rpx;
  }
  
  .live_header .live_fllow text {
  	color: #fff;
  	font-size: 24rpx;
  	margin-right: 8rpx;
  }
  /* 头部头像 人气等 end */
 
 .stop_pop_box {
 	display: flex;
 	flex-direction: column;
 	align-items: center;
 	width: 600rpx;
 	border-radius: 15rpx;
 	background-color: #FFFFFF;
 }
 
 .stop_pop_tit {
 	font-size: 34rpx;
 	font-weight: 700;
 	margin-bottom: 40rpx;
 	padding-top: 40rpx;
 }
 .stop_pop_tit_text{
   font-size: 34rpx;
   font-weight: 700;
 }
 
 .stop_pop_content {
 	color: #666666;
 	margin-top: 16rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
 }
 .stop_pop_content_text{
  font-size: 34rpx;
   color: #666666;
 }
 
 .stop_pop_btn {
 	width: 600rpx;
 	color: #5D7088;
 	font-size: 34rpx;
 	font-weight: 700;
 	border-top: 2rpx solid #f1f1f1;
 	text-align: center;
 	margin-top: 60rpx;
 	padding-top: 30rpx;
 	padding-bottom: 30rpx;
 }
 /* 美颜 start */
 .stop_slider_box{
   width: 750rpx;
   padding: 20rpx 0;
   padding-top: 0rpx;
   font-size: 10rpx;
   background-color: #fff;
   border-top-left-radius: 40rpx;
   border-top-right-radius: 40rpx;
 }
 .stop_slider_box_bottom{
   display: flex;
   align-items: center;
   width: 750rpx;
   height: 271rpx;
   flex-direction: row;
   box-sizing: border-box;
   padding:0 30rpx;
 }
 .slider_box_bottom_one{
   display: flex;
   flex-direction: column;
   align-items: center;
   width: 99rpx;
   font-size: 24rpx;
   margin-right: 87rpx;
 }
 .slider_box_bottom_one_img{
   width: 99rpx;
   height: 99rpx;
   border-radius: 50%;
   border: 3rpx solid transparent;
   display: flex;
   align-items: center;
   overflow: hidden;
   justify-content: center;
   box-sizing: border-box;
 }
 .bottom_one_img{
   border: 3rpx solid rgba(252,28,28,0.6);
 }
 .box_bottom_one_img{
   width: 95rpx;
   height: 95rpx;
   box-sizing: border-box;
   border-radius: 50%;
   display: flex;
   align-items: center;
   overflow: hidden;
   justify-content: center;
 }
 .box_bottom_one_img_filter{
   width: 109rpx;
   height: 109rpx;
 }
 .slider_box_bottom_one_switch{
   background: transparent;
   display: flex;
   align-items: center;
   justify-content: center;
 }
 .slider_beauty{
   display: flex;
   flex-direction: row;
   margin: 25rpx 0;
   margin-left: 20rpx;
   align-items: center;
   
 }
 .slider_beauty_text{
   font-size: 28rpx;
   margin-right: 10rpx;
 }
 .slider_box_bottom_one_text{
   font-size: 28rpx !important;
   margin-top: 10rpx;
   font-family: PingFang SC;
   font-weight: 500;
   color: #666666;
 }
 .bottom_one_text{
   color: #FC1C1C;
 }
 .stop_slider_header{
   height: 84rpx;
   padding: 0 45rpx;
   display: flex;
   flex-direction: row;
   justify-content: space-between;
   align-items: center;
   border-bottom: 1rpx solid #999;
   margin-bottom: 15rpx;
   
 }
 .stop_slider_header_text_box{
   display: flex;
   position: relative;
   height: 84rpx;
   flex-direction: row;
   align-items: center;
 }
 .stop_slider_header_text{
   font-size: 30rpx;
   color: #999;
   margin-right: 50rpx;
 }
 .stop_slider_header_text_one{
   color:#000;
 }
 .stop_slider_header_text_xian{
   width: 32rpx;
   height: 4rpx;
   background: #000000;
   border-radius: 2rpx;
   position: absolute;
   display: flex;
   bottom: 0;
   left: 13rpx;
 }
 .stop_slider_header_flex{
   display: flex;
   flex-direction: row;
   flex: 1;
 }
 .stop_slider_header_img{
   width: 30rpx;
   height: 30rpx;
   
 }
 .slider_bottom_one{
   display: flex;
   flex-direction: row;
   align-items: center;
 }
 .slider-view-p{
   display: flex;
   flex-direction: row;
 }
 .slider_box_bottom_foot{
   padding-bottom: constant(safe-area-inset-bottom);
   padding-bottom: env(safe-area-inset-bottom);
 }
 .toolDiv-list-item_img{
   width: 80rpx;
   height: 80rpx;
 }
 /* 美颜 end */
 .asfsda{
   position: fixed;
   color: #fff;
   left: 0rpx;
   top: 200rpx;
 }
 
 /* 音乐 start */
 .slider_beauty_live{
   width: 750rpx;
   background: #FFFFFF;
   border-radius: 20rpx 20rpx 0rpx 0rpx;
 }
 .slider_beauty_live_header{
   height: 94rpx;
   width: 750rpx;
   display: flex;
   flex-direction: row;
   align-items: center;
   border-bottom: 1rpx solid #f1f1f1;
   padding: 0 20rpx;
 }
 .slider_beauty_volume{
   display: flex;
   align-items: center;
   flex-direction: row;
   height: 94rpx;
 }
 .slider_beauty_volume_img{
   width: 32rpx;
   height: 29rpx;
 }
 .slider_beauty_volume_text{
   font-size: 28rpx;
   font-family: PingFang SC;
   font-weight: 500;
   color: #333333;
   margin-left: 13rpx;
 }
 .slider_beauty_volume_center{
   flex: 1;
   height: 94rpx;
   display: flex;
   flex-direction: row;
   justify-content: center;
 }
 .stop_slider_header_text_box_one{
   height: 94rpx;
 }
 .header_text_box_one{
   margin-right: 0 !important;
 }
 .stop_slider_header_text_xian_one{
   left: 40rpx;
 }
 .stop_slider_header_text_two{
   font-size: 30rpx;
   font-family: PingFang SC;
   font-weight: 500;
   color: #999999;
 }
 .stop_slider_header_text_one1{
   font-weight: bold;
   color: #000000;
 }
 .slider_beauty_volume_type{
   height: 110rpx;
   width: 750rpx;
   display: flex;
   align-items: center;
   padding: 0 20rpx;
   flex-direction: row;
 }
 .volume_type{
   background: #F3F3F3;
   border-radius: 8rpx;
   display: flex;
   height: 60rpx;
   align-items: center;
   justify-content: center;
   flex-direction: row;
   margin-right: 19rpx;
   padding: 17rpx 30rpx;
 }
 .volume_type_text{
   font-size: 28rpx;
   font-family: PingFang SC;
   font-weight: 500;
   color: #999999;
 }
 .volume_type_text_one{
   color: #000000;
 }
 .search_center_box{
   width: 750rpx;
   height: 66rpx;
   display: flex;
   flex-direction: row;
   align-items: center;
   justify-content: center;
   margin-bottom: 50rpx;
 }
 .search_center{
   display: flex;
   flex-direction: row;
   align-items: center;
   border: none;
   width: 710rpx;
   height: 66rpx;
   padding-left: 20rpx;
   border-radius: 32.5rpx;
   background-color: #f5f5f5;
 }
 .search_icon{
   width: 30rpx;
   height: 30rpx;
   margin-top: 2rpx;
 }
 .clear_content{
   width: 45rpx !important;
   height: 45rpx !important;
   margin-right: 15rpx !important;
 }
 .sea_input{
   display: flex;
   align-items: center;
   border: none;
   flex: 1;
   height: 65rpx;
   padding-left: 20rpx;
   border-radius: 32.5rpx;
   background-color: #f5f5f5;
   font-size: 26rpx;
   font-family: PingFang SC;
   font-weight: 500;
   color: #B4B3B3;
 }
 .scroll_music_list{
   height: 600rpx;
 }
 .music_list{
   display: flex;
   align-items: center;
   flex-direction: row;
   width: 750rpx;
   padding: 0 20rpx;
   height: 80rpx;
   margin-bottom: 50rpx;
 }
 .music_list_cont{
   width: 80rpx;
   height: 80rpx;
   background: #F3F3F3;
   border-radius: 10rpx;
   display: flex;
   flex-direction: row;
   justify-content: center;
   align-items: center;
 }
 .music_list_cont_center{
   flex: 1;
   padding-left: 27rpx;
 }
 .music_list_center1{
   width: 400rpx;
   font-size: 30rpx;
   font-family: PingFang SC;
   font-weight: bold;
   color: #333333;
   text-overflow: ellipsis;
   lines:1;
 }
 .music_list_center2{
   width: 400rpx;
   font-size: 24rpx;
   font-family: PingFang SC;
   font-weight: 500;
   color: #999999;
   margin-top: 10rpx;
   overflow: hidden;
   white-space: nowrap;
   text-overflow: ellipsis;
   text-overflow: ellipsis;
   lines:1;
 }
 .music_list_cont_right{
   width: 134rpx;
   height: 56rpx;
 }
 .music_list_cont_right_btn{
   width: 134rpx;
   height: 56rpx;
   background: #F03D39;
   border-radius: 28rpx;
   display: flex;
   align-items: center;
   justify-content: center;
   flex-direction: row;
 }
 .music_list_cont_right_btn_text{
   font-size: 28rpx;
   font-family: PingFang SC;
   font-weight: 500;
   color: #FFFFFF;
 }
 .music_list_cont_img{
   width: 36rpx;
   height: 42rpx;
 }
 .music_list_cont_play{
   width: 80rpx;
   height: 80rpx;
 }
 .slider_beauty_volume_type_box{
   height: 300rpx;
   width: 750rpx;
   padding-top: 60rpx;
 }
 .zanwugewu{
   width: 750rpx;
   margin-top: 20rpx;
   font-size: 30rpx;
   font-family: PingFang SC;
   font-weight: 500;
   color: #999999;
   text-align: center;
 }
 .showGoods_mp3_volume{
   width: 750rpx;
   background: #FFFFFF;
   border-radius: 20rpx 20rpx 0rpx 0rpx;
 }
 .slider_beauty_volume_img1{
   width: 17rpx;
   height: 29rpx;
 }
 .slider_beauty_live_header_one{
   position: relative;
 }
 .slider_beauty_volume_one{
   justify-content: center;
   position: absolute;
   left: 25rpx;
   top: 0rpx;
  height: 94rpx;
 }
 .slider_beauty_volume_slider{
   height: 200rpx;
   width: 750rpx;
   display: flex;
   align-items: center;
   justify-content: center;
   flex-direction: row;
 }
 .stop_slider_header_text_three{
   font-size: 30rpx;
   font-family: PingFang SC;
   font-weight: 500;
   color: #333333;
 }
 .slider_beauty_volume_slider_text{
   width: 750rpx;
   margin-top: 83rpx;
   font-size: 24rpx;
   font-family: PingFang SC;
   font-weight: 500;
   color: #999999;
   text-align: center;
 }
</style>
