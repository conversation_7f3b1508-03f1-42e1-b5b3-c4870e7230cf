<template>
	<view class="main_live" :style="mix_diyStyle">
		<view class="main_live">
			<block v-if="openState">
				<!-- #ifndef MP -->
				<view class="bg_1">
				</view>
				<!-- #endif -->
				<!-- #ifdef MP -->
				<view class="bg_1">
				</view>
				<!-- <image class="bg" :src="imgUrl + 'svideo/list_bg1.png'" :style="{height:'calc('+statusBarHeight+' + 468rpx)'}"></image> -->
				<view class="" :style="{top:menuButtonTop,position:'fixed'}">
					<view class="nav-bar" :style="{height:menuButtonHeight}">
						<image :src="imgUrl+'tshou/back_icon.png'" mode="" @click="$back"></image>
						<view class="">直播列表</view>
					</view>
				</view>
				<!-- #endif -->


				<view class="search" @tap="goSearch" :style="{top:live_list_top}">
					<view class="left">
						<image :src="imgUrl + 'svideo/search_icon.png'" mode="aspectFit"></image>
						<text>{{ $L('请输入关键词') }}</text>
					</view>
					<text class="search_text">{{ $L('搜索') }}</text>
				</view>
				<!-- #ifdef MP -->
				<scroll-view scroll-x class="nav" v-if="labelList && labelList.length"
					:style="{top:statusBarFlag?'calc('+menuButtonHeights+'px + 80rpx)':'80rpx'}">
					<!-- #endif -->
					<!-- #ifndef MP -->
					<scroll-view scroll-x class="nav" v-if="labelList && labelList.length">
						<!-- #endif -->
						<view v-for="(item, index) in labelList" :key="index"
							:class="'nav_item ' + (curLabelId == item.labelId ? 'on' : '')" @tap="changeLabel"
							:data-id="item.labelId">
							<text>{{ item.labelName }}</text>
						</view>
					</scroll-view>
					<!-- #ifdef MP -->
					<scroll-view scroll-y class="live_list" v-if="labelList && labelList.length"
						@scrolltolower="getMoreData"
						:style="{top:statusBarFlag?'calc('+menuButtonHeights+'px + 180rpx)':'180rpx'}">
						<!-- #endif -->
						<!-- #ifndef MP -->
						<scroll-view scroll-y class="live_list" v-if="labelList && labelList.length"
							@scrolltolower="getMoreData">
							<!-- #endif -->
							<!-- 直播列表item -->
							<listLiveItem :liveList="liveList" :bgStyle="bgStyle"
								:listPlayBackIcon="imgUrl + 'svideo/list_playback.png'" :labelId="curLabelId"
								:listLivingIcon="imgUrl + 'svideo/list_living.gif'"
								:heartIcon="imgUrl + 'svideo/live_list_heart.gif'" ref="listLiveItem"/>

							<!-- 数据加载完毕 -->
							<dataLoaded :showFlag="!hasmore && liveList && liveList.length > 0" />

							<!-- 数据加载中 -->
							<dataLoading :showFlag="!firstLoading && hasmore && loading" />

							<!-- 页面loading -->
							<pageLoading :firstLoading="firstLoading"
								:loadingIcon="imgUrl + 'svideo/page_loading_icon.gif'" />

							<!-- 页面空数据 -->
							<emptyData :showFlag="!firstLoading && liveList && !liveList.length"
								:emptyIcon="imgUrl + 'svideo/live_list_empty_icon.png'" />

							<view class="empty_h"></view>
						</scroll-view>
			</block>
			<block v-else>
				<notOpen></notOpen>
			</block>
		</view>
	</view>
</template>

<script>
	import {
		checkPageHasMore,
		initNum
	} from '@/utils/live'
	import request from '@/utils/request'
	const bus = getApp().globalData.bus
	import pageLoading from '../component/pageLoading.vue'
	import emptyData from '../component/emptyData.vue'
	import dataLoading from '../component/dataLoading.vue'
	import dataLoaded from '../component/dataLoaded.vue'
	import listLiveItem from '../component/live/listLiveItem.vue'
	import notOpen from '@/components/not_open.vue'
	import uniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue'
	export default {
		components: {
			pageLoading,
			emptyData,
			dataLoading,
			dataLoaded,
			listLiveItem,
			notOpen,
			uniNavBar
		},
		data() {
			return {
				labelList: [],
				//标签列表
				liveList: [],
				//直播列表
				curLabelId: '',
				// #ifdef MP
				menuButtonHeight: uni.getMenuButtonBoundingClientRect().height + 'px',
				menuButtonTop: uni.getMenuButtonBoundingClientRect().top + 'px',
				menuButtonHeights: uni.getMenuButtonBoundingClientRect().height + uni.getMenuButtonBoundingClientRect().top,
				// #endif
				//当前选中的标签id
				imgUrl: getApp().globalData.imgUrl,
				//图片地址
				hasmore: true,
				//是否还有数据，用于页面展示
				loading: false,
				nav_left_icon: 'back', //底部tab进入的话为空，否则为back
				bgStyle: 'background-size:cover;background-position:center center;background-repeat: no-repeat;',
				//背景图片样式
				firstLoading: true, //是否初次加载，是的话展示页面居中的loading效果，
				pageSize: 10,
				current: 1,
				openState: true,
				statusBarHeight: '',
				statusBarFlag: false,
			}
		},

		computed: {
			live_list_top() {
				/* wx-1-start */
				// #ifdef MP-WEIXIN
				return this.statusBarFlag ? 'calc(' + this.menuButtonHeights + 'px + 15rpx)' : '20rpx'
				// #endif
				/* wx-1-end */
				// #ifndef MP-WEIXIN
				return ''
				// #endif
			}
		},

		props: {},
		//是否还有数据

		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			setTimeout(() => {
				uni.setNavigationBarTitle({
					title: this.$L('直播列表')
				})
			}, 0);

			this.statusBarHeight = (uni.getSystemInfoSync().statusBarHeight + 44) + 'px'
			this.statusBarFlag = true
			this.getLabelList()
		},
		onShow: function() {
			let _this = this //直播结束直接从列表中移除
		},
		methods: {
			// 获取直播分类
			getLabelList() {
				let {
					curLabelId
				} = this
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/live/list'
				param.method = 'GET'
				this.$request(param).then((res) => {
					if (res.state == 200) {
						this.openState = true
						let list = res.data.labelList //获取第一个分类下的数据
						if (list.length > 0) {
							curLabelId = list[0].labelId
							this.getLiveList(curLabelId)
						}
						this.labelList = list
						this.curLabelId = curLabelId
					} else if (res.state == 258) {
						this.openState = false
					}
				})
			},

			//获取直播列表
			getLiveList() {
				this.loading = true
				let {
					liveList,
					hasmore,
					firstLoading
				} = this
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/live/list'
				param.method = 'GET'
				param.data.labelId = this.curLabelId
				param.data.current = this.current
				param.data.pageSize = this.pageSize
				this.$request(param).then((res) => {
					if (res.state == 200) {
						let list = res.data.liveList
						if (this.current == 1) {
							liveList = list
						} else {
							liveList = liveList.concat(list)
						}

						if (checkPageHasMore(res.data.pagination)) {
							this.current++
						} else {
							this.hasmore = false
							hasmore = false
						}
					} //初次加载的话更改状

					if (firstLoading) {
						firstLoading = false
					}

					this.loading = false
					this.hasmore = hasmore
					this.liveList = liveList
					this.firstLoading = firstLoading
				})
			},

			//标签点击事件
			changeLabel(e) {
				let id = e.currentTarget.dataset.id
				let {
					curLabelId
				} = this
				if (curLabelId == id) return
				this.curLabelId = id
				this.firstLoading = true
				this.liveList = []
				this.current = 1
				this.hasmore = true
				this.getLiveList(id)
			},

			getMoreData: function() {
				if (this.hasmore) {
					this.getLiveList(this.curLabelId)
				}
			},
			//跳转搜索页面
			goSearch: function() {
				this.$Router.push('/extra/live/liveSearch')
			},
			backHome() {
				const page = getCurrentPages()
				if (page.length > 1) {
					this.$Router.back(1)
				} else {
					this.$Router.replace('/pages/index/index')
				}
			},
			
			updateView(updateData){
				if(this.$refs.listLiveItem){
					this.$refs.listLiveItem.updateView(updateData)
				}
			}
		}
	}
</script>
<style>
	page {
		background-color: #f5f5f5;
		width: 750rpx;
		margin: 0 auto;
	}

	.bg_1 {
		position: fixed;
		top: 0;
		left: 0;
		width: 750rpx;
		height: 530rpx;
		z-index: 0;
		right: 0;
		margin: 0 auto;
		background: var(--color_video_main_vertical_bg);
	}

	.nav {
		position: fixed;
		/* #ifdef H5 */
		top: 80rpx;
		/* #endif */
		/* #ifdef MP || APP-PLUS */
		top: 80rpx;
		/* #endif */
		left: 0;
		width: 750rpx;
		height: 80rpx;
		background-color: 'transpanrent';
		display: block;
		white-space: nowrap;
		overflow: hidden;
		z-index: 9999;
		right: 0;
		margin: 0 auto;
	}

	.empty_h {
		height: 170rpx;
		width: 100%;
		background-color: 'transpanrent';
	}

	.search {
		position: fixed;
		/* #ifdef H5 */
		top: 20rpx;
		/* #endif */
		/* #ifdef MP || APP-PLUS */
		top: 20rpx;
		/* #endif */
		left: 0;
		width: 710rpx;
		height: 60rpx;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		margin: 20rpx 20rpx 0 20rpx;
		background: transpanrent;
		right: 0;
		margin: 0 auto;
	}

	.search .left {
		width: 639rpx;
		height: 60rpx;
		border-radius: 30rpx;
		background: rgba(255, 255, 255, 0.2);
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
	}

	.search .left image {
		width: 50rpx;
		height: 50rpx;
		margin-top: 10rpx;
		margin-left: 10rpx;
	}

	.search .left text {
		color: #fff;
		font-size: 28rpx;
		margin-top: -2rpx;
	}

	.search .search_text {
		font-size: 28rpx;
		color: #fff;
	}

	.nav_item {
		display: inline-block;
		line-height: 80rpx;
		text-align: center;
		color: #fff;
		font-size: 30rpx;
		font-family: PingFang SC;
		font-weight: 500;
		padding: 0 25rpx;
	}

	.nav_item.on text {
		position: relative;
		font-size: 32rpx;
		font-family: PingFang SC;
		font-weight: bold;
	}

	.nav_item.on text:after {
		content: ' ';
		width: 58rpx;
		height: 6rpx;
		background: #fff;
		z-index: 1000;
		/* border: 4rpx solid #fff; */
		border-radius: 40rpx;
		/* line-height: unset; */
		margin: 0 auto;
		display: inline-block;
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
		bottom: -17rpx;
		/* display: inline-block;
  line-height: 76rpx;
  border-bottom: 4rpx solid #fff;
  font-weight: 700; */
	}

	.bg {
		position: fixed;
		top: 0;
		left: 0;
		width: 750rpx;
		height: 468rpx;
		z-index: 0;
		right: 0;
		margin: 0 auto;
	}

	.live_list {
		width: 750rpx;
		background-color: transparent;
		position: fixed;
		/* #ifdef H5 */
		height: calc(100vh - 100rpx);
		top: 180rpx;
		/* #endif */
		/* #ifdef MP || APP-PLUS */
		height: calc(100vh - 140rpx - var(--status-bar-height));
		/* #endif */
		left: 0;
		right: 0;
		bottom: 0;
		margin: 0 auto;
	}

	.nav-bar {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: flex-start;
		padding-left: 20rpx;
	}

	.nav-bar image {
		width: 19rpx;
		height: 33rpx;
		margin-top: 5rpx;
	}

	.nav-bar view {
		font-size: 34rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #FFFFFF;
		margin-left: 21rpx;
	}
</style>