<template>
	<view :style="mix_diyStyle">
	<view>
		<view class="sele_goods">
			<view class="header">
				<view class="header_nav">
					<view class="go_back_wrap" @tap="goBack">
						<image class="go_back" :src="imgUrl+'svideo/white_arrow_l.png'"></image>
					</view>
					<text class="title">{{$L('商品选择')}}</text>
				</view>
				<view class="search">
					<view class="left">
						<image :src="imgUrl+'svideo/search_icon.png'" mode="aspectFit"></image>
						<input confirm-type="search" class="sea_input" type="text" :value="goods_name" :placeholder="$L('请输入关键词')"
						 placeholder-style="font-size:24rpx;color:#fff;" @input="inConfirm"></input>
					</view>
					<text v-if="!goods_name" class="search_text" @tap="inConfirm">{{$L('搜索')}}</text>
					<text v-if="goods_name" class="search_text" @tap="cancleSearch">{{$L('取消')}}</text>
				</view>
			</view>
			<scroll-view class="main" scroll-y @scrolltolower="getMoreData">
				<view class="main_wrap" v-if="list.length">
					<view v-for="(item, index) in list" :key="index" class="sel_goods_item" @tap="operateGoods(item)">
						<view class="goods_image" :style="'background:url('+item.goodsImage+') no-repeat center;' + bgStyle"></view>
						<rich-text :nodes="item.goodsName" class="goods_name"></rich-text>
						<view class="goods_detail">
							<view class="goods_price">
								<text class="unit">{{$L('¥')}}</text>
								<text class="price_num">{{item.goodsPrice}}</text>
							</view>
							<text class="iconfont iconziyuan33 goods_sele_eck" v-if="item.isSel"></text>
							<text class="iconfont iconziyuan43 goods_sele" v-else></text>
							<!-- <image class="goods_sele" :src="imgUrl+(item.is_sel!=undefined&&item.is_sel?'svideo/goods_sel.png':'svideo/goods_unsel.png')"></image> -->
						</view>
					</view>
				</view>
				<!-- 数据加载完毕 -->
				<dataLoaded :showFlag="!hasmore&&list.length>0" />

				<!-- 数据加载中 -->
				<dataLoading :showFlag="!firstLoading&&hasmore&&loading" />

				<!-- 页面loading -->
				<pageLoading :firstLoading="firstLoading" :loadingIcon="imgUrl + 'svideo/page_loading_icon.gif'" />

				<!-- 页面空数据 -->
				<emptyData :showFlag="!firstLoading&&!list.length" :emptyIcon="imgUrl+'svideo/live_list_empty_icon.png'" />

				<view class="empty_h" v-if="selGoods.length"></view>
			</scroll-view>

			<view class="footer" v-if="selGoods.length">
				<view class="total_tip">
					<text class="total_tip_text">{{$L('已选')}}</text>
					<text class="total_tip_text total_num">{{selGoods.length}}</text>
					<text class="total_tip_text">{{$L('件商品')}}</text>
					<text v-if="bindGoodsNum > 0 && selGoods.length >= 10" class="total_tip_text">{{$L('(最多可选')}}{{bindGoodsNum}}{{$L('件商品)')}}</text>
				</view>
				<text class="confirm_btn" @tap="confirmSelGoods">{{$L('确认')}}</text>
			</view>
		</view>
	</view>
	</view>
</template>

<script>
	// import request from "@/utils/request";
	import {
		checkPageHasMore,
		initNum
	} from "@/utils/live";
	import pageLoading from "../component/pageLoading.vue";
	import emptyData from "../component/emptyData.vue";
	import dataLoading from "../component/dataLoading.vue";
	import dataLoaded from "../component/dataLoaded.vue";

	const bus = getApp().globalData.bus;

	export default {
		components: {
			pageLoading,
			emptyData,
			dataLoading,
			dataLoaded,
		},
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				//图片地址
				bgStyle: 'background-size:cover;background-position:center center;background-repeat: no-repeat;',
				//背景图片样式
				hasmore: true,
				//是否还有数据，用于页面展示
				loading: false,
				firstLoading: true,
				//是否初次加载，是的话展示页面居中的loading效果，
				list: [],
				goods_name: '',
				// 搜索关键词
				goodsList: [],
				// 已选择的商品
				goods_ids: [],
				// 已选择的商品id
				maxNum: '',
				pn: 1,
				pageSize: 10,
				selGoods:[],
				bindGoodsNum:10, //最多可选择商品数量
				storeId:'',
			};
		},

		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			this.bindGoodsNum = this.$Route.query.liveBindGoodsNum;
			this.storeId = this.$Route.query.storeId;
			if(uni.getStorageSync('selGoods')){
				this.selGoods = uni.getStorageSync('selGoods')
			}
			this.getList();
		},
		methods: {
			//获取商品列表
			getList() {
				this.loading = true;
				let {
					list,
					hasmore,
					goods_name,
					firstLoading,
					goods_ids,
					goodsList
				} = this;
				let param = {};
				param.url="v3/goods/front/goods/liveGoodsList";
				param.data = {
					pageSize: this.pageSize,
					current: this.pn,
					storeId:this.storeId,
					keyword:this.goods_name
				}
				this.$request(param).then(res => {
					if (res.state == 200) {
						let tmp_list = res.data.list;
						if (this.pn == 1) {
							this.list = tmp_list;
						} else {
							this.list = list.concat(tmp_list);
						}
						if(this.selGoods && this.selGoods.length > 0){
							this.list.map(listItem=>{
								this.selGoods.map(selGoodsItem=>{
									if(listItem.defaultProductId == selGoodsItem.defaultProductId){
										listItem.isSel = true;
									}
								})
							})
						}
						if (checkPageHasMore(res.data.pagination)) {
							this.pn++;
						} else {
							this.hasmore = false;
							this.hasmore = false;
						}
					}
					if (this.firstLoading) {
						this.firstLoading = false;
					}
				})
			},

			getMoreData: function() {
				if (this.hasmore) {
					this.getList();
				}
			},

			input(e) {
				this.setData({
					goods_name: e.detail.value
				});
			},

			//点击弹起的键盘按钮时触发
			inConfirm: function(e) {
				this.goods_name = e ? e.detail.value : this.goods_name;
				this.pn = 1;
				this.hasmore = true;
				this.hasmore = true;
				this.firstLoading = true;
				this.getList();
			},

			//取消搜索
			cancleSearch() {
				this.goods_name = '';
				this.inConfirm();
			},

			// 选择商品
			operateGoods(item) {
				let that = this;
				if(that.bindGoodsNum > 0 && that.selGoods.length >= that.bindGoodsNum && !item.isSel){
					uni.showToast({
						title:that.$L('最多能选择') + that.bindGoodsNum +that.$L('件商品'),
						icon:'none'
					})
					return;
				}else{
					that.list && that.list.map((listItem,listIndex)=>{
						if(listItem.defaultProductId == item.defaultProductId){
							listItem.isSel = !listItem.isSel;
							if(listItem.isSel){
								that.selGoods.push(listItem);
							}else{
								that.selGoods=that.selGoods.filter(subItem=>subItem.defaultProductId!=listItem.defaultProductId);
							}
						}
					})
					that.$forceUpdate();
				}
			},
			

			//确认选择商品
			confirmSelGoods() {
				let that = this;
				let tmp = that.selGoods
				let pages = getCurrentPages();
				let prevPage = pages[pages.length - 2]; //上一个页面 
				tmp.map(item=>{
					item.goodsName = item.goodsName.replace(/<\/?font>|<font color=\"red\">/g,'')
				})
				if(prevPage){
					prevPage.$vm.selGoods = tmp;
				}
				uni.setStorageSync('selGoods',tmp)
				this.$Router.back(1)
			},

			// 返回上级页面
			goBack() {
				this.$Router.back(1)
			}

		}
	};
</script>
<style>
	page {
		background: #f8f8f8;
	}
	view{
		box-sizing: content-box;
	}

	.sele_goods {
		width: 750rpx;
		height: 100vh;
	}

	.sele_goods .header {
		position: absolute;
		z-index: 2;
		width: 750rpx;
		height: calc(var(--status-bar-height) + 190rpx);
		background: var(--color_video_main_bg);
	}

	.header_nav {
		position: absolute;
		top: calc(var(--status-bar-height) + 20rpx);
		left: 20rpx;
		right: 0;
		z-index: 2;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		height: 80rpx;
	}

	.header_nav .go_back_wrap {
		position: absolute;
		left: 0;
		width: 80rpx;
		height: 47rpx;
	}

	.header_nav .go_back {
		width: 45rpx;
		height: 47rpx;
	}

	.header_nav .title {
		color: #fff;
		font-size: 34rpx;
		font-weight: bold;
	}

	.search {
		position: fixed;
		top: calc(var(--status-bar-height) + 110rpx);
		left: 0;
		width: 710rpx;
		height: 60rpx;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		margin: 0 20rpx;
		background: transpanrent;
	}

	.search .left {
		width: 639rpx;
		height: 60rpx;
		border-radius: 30rpx;
		background: rgba(255, 255, 255, 0.2);
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
	}

	.search .left image {
		width: 50rpx;
		height: 50rpx;
		margin-top: 2rpx;
		margin-left: 10rpx;
	}

	.search .left .sea_input {
		color: #fff;
		font-size: 24rpx;
		margin-top: -2rpx;
		width: 540rpx;
	}

	.search .search_text {
		font-size: 26rpx;
		color: #fff;
	}

	.main {
		position: fixed;
		z-index: 2;
		top: 240rpx;
		width: 750rpx;
		padding: 0 20rpx;
		height: calc(100vh - 260rpx);
	}

	.main .main_wrap {
		width: 710rpx;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		flex-wrap: wrap;
	}

	.main .sel_goods_item {
		width: 345rpx;
		height: 474rpx;
		background: #fff;
		border-radius: 14rpx;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
		margin-top: 20rpx;
		padding-bottom: 20rpx;
	}

	.main .sel_goods_item .goods_image {
		width: 345rpx;
		height: 345rpx;
		border-radius: 14rpx 14rpx 0 0;
	}

	.main .sel_goods_item .goods_name {
		color: #2d2d2d;
		font-size: 26rpx;
		line-height: 36rpx;
		padding: 0 20rpx;
		width: 305rpx;
		height: 72rpx;
		margin-top: 10rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		word-break: break-word;
	}

	.main .sel_goods_item .goods_detail {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		padding: 0 10rpx 0 20rpx;
		width: 315rpx;
	}

	.main .sel_goods_item .goods_detail .goods_price {
		color:var(--color_video_main);
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
		letter-spacing: 2rpx;
	}

	.main .sel_goods_item .goods_detail .goods_price .unit {
		font-size: 24rpx;
	}

	.main .sel_goods_item .goods_detail .goods_price .price_num {
		font-weight: bold;
		font-size: 34rpx;
	}

	.main .sel_goods_item .goods_detail .goods_sele {
		font-size: 32rpx;
	}
	.main .sel_goods_item .goods_detail .goods_sele_eck{
	  color: var(--color_video_main);
	}

	.sele_goods .footer {
		width: 750rpx;
		height: 205rpx;
		position: fixed;
		z-index: 3;
		left: 0;
		right: 0;
		bottom: 0;
		box-shadow: 0px 0px 9px 1px rgba(86, 86, 86, 0.2);
		border-radius: 15rpx 15px 0 0;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: center;
		background: #fff;
	}

	.sele_goods .footer .total_tip {
		width: 690rpx;
		height: 110rpx;
		padding: 0 30rpx;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
	}

	.sele_goods .footer .total_tip .total_tip_text {
		color: #2D2D2D;
		font-size: 28rpx;
	}

	.sele_goods .footer .total_tip .total_tip_text.total_num {
		color:var(--color_video_main);
	}

	.sele_goods .footer .confirm_btn {
		letter-spacing: 5rpx;
		width: 580rpx;
		height: 70rpx;
		background:var(--color_video_main_bg);
		box-shadow: 0px 3rpx 10rpx 0px var(--color_video_halo);
		border-radius: 35rpx;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		color: #fff;
	}

	.empty_h {
		height: 185rpx;
		width: 100%;
		background-color: 'transpanrent';
	}
</style>
