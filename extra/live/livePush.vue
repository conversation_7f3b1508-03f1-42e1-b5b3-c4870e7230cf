<template>
	<view :style="mix_diyStyle">
	<view>
		<!-- 回放 -->
		<view class="live_header">
			<image @tap="goBack" class="go_back" :src="imgUrl+'svideo/white_arrow_l.png'"></image>
			<view class="avator" :style="'background-image:url('+liveDetail.memberAvatar+');' + bgStyle"></view>
			<view class="mem_info">
				<text class="name">{{liveDetail.memberNickname}}</text>
				<view class="stat_num">
					<text v-if="liveDetail.isSelf == 1" class="click_num"
						style="margin-right: 20rpx">{{live_like_num}}{{$L('人气')}}</text>
					<text class="click_num">{{live_click_num}}{{$L('观看')}}</text>
				</view>
			</view>
		</view>
		<!-- 直播 -->
		<live-pusher :waiting-image="imgUrl + 'svideo/live_wait_img.jpg'" id="sldVideo" mode="SD" :beauty="beautyValue"
			:whiteness="whitenessValue" class="living" :url="pushurl" @statechange="statechange" object-fit="fillCrop"
			autopush="false" :enable-camera="enableCamra" :enable-mic="enableMic" :filter='filterValue' beauty-style="smooth">
		</live-pusher>

		<view class="live_footer">
			<view class="goods" @tap="showGoodsFun">
				<!-- <image :src="imgUrl+'svideo/goods_icon.png'"></image> -->
				<text class="iconfont iconshangpin goods_icon"></text>
				<text>{{liveDetail.goodsNum * 1 > 99 ? '99+' : liveDetail.goodsNum }}</text>
			</view>

			<input type="text" name="talk_con" v-model="input_val" class="talk_con"
				:placeholder="live_state?$L('和粉丝聊两句~'):$L('直播开始后可与粉丝交流~')"
				placeholder-style="font-size:24rpx;color:#fff;font-weight:600" confirm-type="send"
				@confirm="publishComment" maxlength="30" cursor-spacing="20"></input>
			<view class="share" @tap="showShare">
				<image :src="imgUrl+'svideo/share.png'"></image>
				<text>{{$L('分享')}}</text>
			</view>
			<view v-if="!live_state" class="share add_heart" @tap="startLive">
				<!-- <image :src="imgUrl+'svideo/start_live.png'"></image> -->
        <text class="iconfont icon15 start_live"></text>
				<text>{{$L('开始直播')}}</text>
			</view>
			<view v-if="live_state" class="share add_heart" @tap="stopLiveTip">
				<!-- <image :src="imgUrl+'svideo/stop_live.png'"></image> -->
        <text class="iconfont iconzhibojieshu start_live"></text>
				<text>{{$L('结束直播')}}</text>
			</view>
		</view>
    
    <!-- 右边操作 start-->
    <view class="toolDiv">
    	<view class="toolDiv-list"> 
    		
    		<view class="toolDiv-list-item" @click.stop="beatyOpen(0)">
          <image :src="imgUrl+'livepush/beauty.png'" mode=""></image>
    			<text class="toolDiv-list-item-txt">美颜</text>
    		</view>
    		<view class="toolDiv-list-item" @click.stop="switchCamera()">
          <image :src="imgUrl+'livepush/overturn.png'" mode=""></image>
    			<text class="toolDiv-list-item-txt">切换</text>
    		</view>
        <view class="toolDiv-list-item" @click.stop="dingClick()">
          <image :src="imgUrl+'livepush/lamp.png'" mode=""></image>
        	<text class="toolDiv-list-item-txt">闪光灯</text>
        </view>
    		<view class="toolDiv-list-item" @click.stop="bgmPopOpne()">
          <image :src="imgUrl+'livepush/music_white.png'" mode=""></image>
    			<text class="toolDiv-list-item-txt">{{'音乐'}}</text>
    		</view> 
    	</view>
    </view>
    <!-- 右边操作 end -->

		<!-- 分享 -->
		<view class="select-wrap" catchtouchmove="touchmoveshare" v-if="shareWrap">
			<view class="share-mode">
				<view class="empty_158"></view>
				<view class="share-img" @tap="prevImg">
					<image :src="shareImg" mode="widthFix"></image>
				</view>

				<view class="ul">
					<view class="item" @tap="downloadImg">
						<image :src="imgUrl + 'svideo/hb_share.png'" mode="widthFix"></image>
						<text>{{$L('下载海报')}}</text>
					</view>
				</view>

				<view class="close" @tap="closeShare">
					<image :src="imgUrl+'svideo/release_close.png'" mode="widthFix"></image>
				</view>

			</view>
		</view>

		<view class="select-wrap" catchtouchmove="touchmoveshare" v-if="shareWrap2">
			<view class="share-mode">

				<view class="share-img"></view>

				<view class="ul">
					<button open-type="share" class="item">
						<image :src="imgUrl+'svideo/wx_share.png'" mode="widthFix"></image>
						<text>{{$L('微信好友')}}</text>
					</button>
					<view class="item" @tap="createhb">
						<image :src="imgUrl+'svideo/createhb.png'" mode="widthFix"></image>
						<text>{{$L('生成海报')}}</text>
					</view>
				</view>

				<view class="close" @tap="closeShare">
					<image :src="imgUrl+'svideo/release_close.png'" mode="widthFix"></image>
				</view>
			</view>
		</view>

		<!-- 视频绑定的商品模块 start -->
		<view class="live_bind_goods_mask" v-if="showGoods">
			<view class="live_bind_goods">
				<view class="header">
					<text>{{$L('全部商品')}}({{liveDetail.goodsNum * 1 > 99 ? '99+' : liveDetail.goodsNum}})</text>
					<image :src="imgUrl+'svideo/close.png'" @tap="closeGoods"></image>
				</view>
				<scroll-view class="scroll_goods" scroll-y="true" @scrolltolower="getMoreGoods">
					<view class="live_user_tab_content">
						<!-- 商品列表item -->
						<livePlayGoods :goodsData="goodsList" addCartIcon="addCart1" :memberInfo='liveDetail'
							:eyeIcon="imgUrl+'svideo/eye.png'"
							:live_id="live_id" @click="addCart"/>

						<!-- 数据加载完毕 -->
						<dataLoaded :showFlag="!hasmore&&goodsList.length>0" />

						<!-- 数据加载中 -->
						<dataLoading :showFlag="hasmore&&loading" />

					</view>
				</scroll-view>
			</view>
		</view>
		<!-- 视频绑定的商品模块 end -->

		<!-- 弹幕 start-->
		<view class="barrage_wrap">
			<view class="notice">
				<text v-if="noticeList&&noticeList.length>0">{{noticeList[0].msg}}</text>
			</view>

			<scroll-view class="barrage" scroll-y :scroll-into-view="toBottom">
				<view class="gonggao" v-if="liveDetail.liveNotice">
					<text class="title">{{$L('公告')}} </text>
					<text class="ba_txt">{{liveDetail.liveNotice}}</text>
				</view>
				<view v-for="(item, index) in msgList" :key="index" :index="index" v-if="msgList.length>0">
					<view :id="'item' + index" class="barrage_item">
						<text class="name" :style="'color:' + item.color_random">{{item.authorName}}</text>
						<text class="barrage_item_con">{{item.msg}}</text>
					</view>
				</view>

			</scroll-view>
		</view>
		<!-- 弹幕 end-->

		<uni-popup ref="stopVideo" type="center">
			<view class="stop_pop_box">
				<view class="stop_pop_tit">{{liveStateInfo.forbidReason?liveStateInfo.forbidReason:$L('直播已结束')}}</view>
				<view class="stop_pop_content">
					{{$L('直播时长：')}}<span>{{liveStateInfo.playbackTime?liveStateInfo.playbackTime:'0:0:0'}}</span></view>
				<view class="stop_pop_content">
					<span>{{$L('获赞：')}}{{liveStateInfo.popularityNum?liveStateInfo.popularityNum:0}}</span>
					<span>{{$L('观看：')}}{{liveStateInfo.viewingNum?liveStateInfo.viewingNum:0}}</span>
				</view>
				<view class="stop_pop_btn" @tap="endBack">{{$L('返回')}}</view>
			</view>
		</uni-popup>
	</view>
  <!-- 美颜 start -->
    <uni-popup ref="stop_slider" type="bottom">
      <view class="slider_beauty" v-if="beautySwitch&&Manager==1">
        <!-- 美顏 -->
        <slider block-size='18' activeColor="#fff" backgroundColor='#B9B9B9'  block-color="var(--color_video_main)" :value="beauty" min='0' max='90' @changing="sliderChange" step="1" v-if="beautyFlag==1"/>
        <!-- 美白 -->
        <slider :value="whiteness" block-size='18' activeColor="#fff" backgroundColor='#B9B9B9'  block-color="var(--color_video_main)" min='0' max='90' @changing="whitenessChange" step="1" v-if="beautyFlag==2"/>
        <!-- 红润 -->
        <!-- <slider :value="ruddy" block-size='18' activeColor="#fff" backgroundColor='#B9B9B9'  block-color="var(--color_video_main)" min='0' max='90' @changing="ruddyChange" step="1" v-if="beautyFlag==3"/> -->
      </view>
      <!-- <view class="slider_beauty" v-if="fliterSwitch&&Manager==2"> -->
        <!-- 滤镜强度 -->
      <!--  <slider block-size='18' activeColor="#fff" backgroundColor='#B9B9B9'  block-color="var(--color_video_main)" :value="filter_slider" min='0' max='100' @changing="filterChange" step="1" v-if="fliterSwitch"/>
      </view> -->
      <view class="stop_slider_box">
        <view class="stop_slider_header">
          <view class="stop_slider_header_flex">
            <view class="stop_slider_header_text_box" @click="Manager_select(1)">
              <text class="stop_slider_header_text" :class="{'stop_slider_header_text_one':Manager==1}">美颜</text>
              <view v-if="Manager==1" class="stop_slider_header_text_xian"></view>
            </view>
            <view class="stop_slider_header_text_box" @click="Manager_select(2)">
              <text class="stop_slider_header_text" :class="{'stop_slider_header_text_one':Manager==2}">滤镜</text>
              <view v-if="Manager==2" class="stop_slider_header_text_xian"></view>
            </view>
          </view>
        </view>
      <!-- 美颜 start -->
        <view class="stop_slider_box_bottom" v-if="Manager==1">
          <view class="slider_box_bottom_one">
            <view class="slider_box_bottom_one_img" @click="switchChange(1)" :class="{'bottom_one_img':!beautySwitch}">
               <image :src="imgUrl+'livepush/no.png'" class="box_bottom_one_img"></image>
            </view>
            <text class="slider_box_bottom_one_text" :class="{'bottom_one_text':!beautySwitch}">无</text>
          </view>
          <view class="slider_box_bottom_one">
            <view class="slider_box_bottom_one_img" @click="beauty_select(1)" :class="{'bottom_one_img':beautyFlag==1&&beautySwitch}">
              <image :src="imgUrl+'livepush/dermabrasion.png'" mode="" class="box_bottom_one_img"></image>
            </view>
            <text class="slider_box_bottom_one_text" :class="{'bottom_one_text':beautyFlag==1&&beautySwitch}">磨皮</text>
          </view>
          <view class="slider_box_bottom_one">
            <view class="slider_box_bottom_one_img" @click="beauty_select(2)" :class="{'bottom_one_img':beautyFlag==2&&beautySwitch}">
               <image :src="imgUrl+'livepush/skin_whitening.png'" mode="" class="box_bottom_one_img"></image>
            </view>
            <text class="slider_box_bottom_one_text" :class="{'bottom_one_text':beautyFlag==2&&beautySwitch}">美白</text>
          </view>
          <!-- <view class="slider_box_bottom_one">
            <view class="slider_box_bottom_one_img" @click="beauty_select(3)" :class="{'bottom_one_img':beautyFlag==3&&beautySwitch}">
               <image :src="imgUrl+'livepush/ruddy.png'" mode="" class="box_bottom_one_img"></image>
            </view>
            <text class="slider_box_bottom_one_text" :class="{'bottom_one_text':beautyFlag==3&&beautySwitch}">红润</text>
          </view> -->
        </view>
        <!-- 美颜 end -->
        <!-- 滤镜 start -->
        <view class="stop_slider_box_bottom" v-if="Manager==2">
          <scroll-view class="slider-view-p" show-scrollbar="false" scroll-x style="display: flex;">
            <view class="slider_bottom_one">
              <view class="slider_box_bottom_one">
                <view class="slider_box_bottom_one_img" @click="switchChange(2)" :class="{'bottom_one_img':!fliterSwitch}">
                  <image :src="imgUrl+'livepush/no.png'" class="box_bottom_one_img"></image>
                </view>
                <text class="slider_box_bottom_one_text" :class="{'bottom_one_text':!fliterSwitch}">无</text>
              </view>
              <view class="slider_box_bottom_one" v-for="(item,index) in filterList" :key="index"> 
                <view class="slider_box_bottom_one_img" @click="fliter_select(item,index)" :class="{'bottom_one_img':fliterSwitch&&filterindex==index}">
                   <image :src="imgUrl+item.image" class="box_bottom_one_img"></image>
                </view>
                <text class="slider_box_bottom_one_text" :class="{'bottom_one_text':fliterSwitch&&filterindex==index}">{{item.name}}</text>
              </view>  
            </view>            
          </scroll-view>
        </view>
        <view class="slider_box_bottom_foot"></view>
        <!-- 滤镜 end -->
      </view>
    </uni-popup>
    <!-- 音乐模块 start -->
    <uni-popup ref="showGoods_mp3" type="bottom">
      <view class="slider_beauty_live">
        <view class="slider_beauty_live_header">
          <view class="slider_beauty_volume" @click="showGoods_mp3_volume_type()">
            <image class="slider_beauty_volume_img" :src="imgUrl + 'livepush/volume.png'" mode=""></image>
            <text class="slider_beauty_volume_text">音量</text>
          </view>
          <view class="slider_beauty_volume_center">
            <view class="stop_slider_header_text_box stop_slider_header_text_box_one" @click="music_select(1)">
              <text class="stop_slider_header_text stop_slider_header_text_two" :class="{'stop_slider_header_text_one1':musicFlag==1}">正在播放</text>
              <view v-if="musicFlag==1" class="stop_slider_header_text_xian stop_slider_header_text_xian_one"></view>
            </view>
            <view class="stop_slider_header_text_box stop_slider_header_text_box_one" @click="music_select(2)">
              <text class="stop_slider_header_text stop_slider_header_text_two" :class="{'stop_slider_header_text_one1':musicFlag==2}">背景音乐</text>
              <view v-if="musicFlag==2" class="stop_slider_header_text_xian stop_slider_header_text_xian_one"></view>
            </view>
          </view>
        </view>
        <view class="slider_beauty_volume_type_box"  v-if="musicFlag==1">
          <view class="" v-if="musicInfo.length>0">
            
            <view class="music_list" v-for="(item,index) in musicInfo">
              <view class="music_list_cont">
                <image class="music_list_cont_img" :src="imgUrl + 'livepush/music.png'" mode="" v-if="item.type==1"></image>
                <image class="music_list_cont_play" :src="imgUrl + 'livepush/play.gif'" mode=""  v-if="item.type==2"></image>
              </view>
              <view class="music_list_cont_center">
                <text class="music_list_center1">{{item.musicName}}</text>
                <text class="music_list_center2">{{item.singerName}} · 时长{{item.musicDuration}}</text>
              </view>
              <view class="music_list_cont_right">
                <view class="music_list_cont_right_btn">
                  <text class="music_list_cont_right_btn_text" v-if="item.type==1" @click="selectBGMFiles(item,index,1)">使用</text>
                  <text class="music_list_cont_right_btn_text" v-if="item.type==2"  @click="tingBGMFiles(item,index,1)">停止</text> 
                </view>
              </view>
            </view>
          </view>
          <view class="zanwugewu" v-else>
            <text>暂无播放~</text>
          </view>
        </view>
          <view class="slider_beauty_volume_type" v-if="musicFlag==2">
            <view class="volume_type" @click="volumeType(1)">
              <text class="volume_type_text" :class="{'volume_type_text_one':musictype==1}">推荐</text>
            </view>
            <view class="volume_type" @click="volumeType(2)">
              <text class="volume_type_text" :class="{'volume_type_text_one':musictype==2}">全部</text>
            </view>
          </view>
          <view class="search_center_box"  v-if="musicFlag==2">
            <view class="search_center">
              <image class="search_icon" :src="imgUrl+'search.png'"></image>
              <input class='sea_input' type='text' v-model="input_val"
                placeholder="搜索歌曲或歌手"
                @input="inputChange" @confirm='search' maxlength="50"></input>
              <image class='clear_content' v-if="input_val" @click="clearInputVal"
                :src="imgUrl+'input_clear.png'" />
            </view>        
          </view>
          <!-- 音乐列表 -->
          <scroll-view class="scroll_music_list" :refresher-triggered='triggered' refresher-enabled scroll-y="true" @scrolltolower="getMoreMusic"  v-if="musicFlag==2" @refresherpulling='onPulling' @refresherrefresh='refreshMoreMusic' :scroll-into-view='scrollMusic'>
            <view class="" v-if="music_list.length>0">
              <view class="" id='music_list_id'></view>
              <view class="music_list" v-for="(item,index) in music_list">
                <view class="music_list_cont">
                  <image class="music_list_cont_img" :src="imgUrl + 'livepush/music.png'" mode="" v-if="item.type==1"></image>
                  <image class="music_list_cont_play" :src="imgUrl + 'livepush/play.gif'" mode=""  v-if="item.type==2"></image>
                </view>
                <view class="music_list_cont_center">
                  <text class="music_list_center1">{{item.musicName}}</text>
                  <text class="music_list_center2">{{item.singerName}} · 时长{{item.musicDuration}}</text>
                </view>
                <view class="music_list_cont_right">
                  <view class="music_list_cont_right_btn">
                    <text class="music_list_cont_right_btn_text" v-if="item.type==1" @click="selectBGMFiles(item,index)">使用</text>
                    <text class="music_list_cont_right_btn_text" v-if="item.type==2"  @click="tingBGMFiles(item,index)">停止</text> 
                  </view>
                </view>
              </view>
            </view>
            <view class="zanwugewu" v-else>
              <text>暂无歌曲~</text>
            </view>
            
          </scroll-view>
          <view class="slider_box_bottom_foot"></view>
      </view>
    </uni-popup>
    <uni-popup ref="showGoods_mp3_volume" type="bottom">
      <view class="showGoods_mp3_volume">
        <view class="slider_beauty_live_header slider_beauty_live_header_one">
          <view class="slider_beauty_volume slider_beauty_volume_one" @click="showGoods_mp3_volume_type(1)">
            <image class="slider_beauty_volume_img1" :src="imgUrl + 'livepush/back.png'" mode=""></image>
          </view>
          <view class="slider_beauty_volume_center">
            <view class="stop_slider_header_text_box stop_slider_header_text_box_one header_text_box_one">
              <text class="stop_slider_header_text stop_slider_header_text_two stop_slider_header_text_three">音量设置</text>
            </view>
          </view>
        </view>
          <view class="slider_beauty_volume_slider_text">
            <text>滑动设置背景音乐音量</text>
          </view>
          <view class="slider_beauty_volume_slider">
            <slider block-size='18' activeColor="var(--color_video_main)" backgroundColor='#B9B9B9'  block-color="var(--color_video_main)" :value="music_slider" min='0' max='100' @changing="musicChange" step="1" :show-value="true"/>
          </view>
      </view>
    </uni-popup> 
    <!-- 音乐模块 end -->
	</view>
</template>

<script>
	import {
		checkPageHasMore,
		colorArray,
		initNum
	} from "../../utils/live";
	import request from "../../utils/request";
	import dataLoading from "../component/dataLoading.vue";
	import dataLoaded from "../component/dataLoaded.vue";
	import livePlayGoods from "../component/live/livePlayGoods.vue";
	import uniPopup from '@/components/uni-popup/uni-popup.vue';
	import io from '@hyoga/uni-socket.io';
	import {
		mapState,
		mapMutations
	} from 'vuex';
	const bus = getApp().globalData.bus;
	let livePusherContext = ''; //直播播放组件
	let live_connect_success = false; //直播连接成功
	let re_request_live_time = 0; //当前重复连接直播次数
	let re_request_live_num = 3; //最多可重复连接次数

	export default {
		data() {
			return {
				pushurl: '',
				// pushurl: "rtmp://uplive.livewebsite.cn/livewebsite/61?auth_key=1585015763-0-0-73cd3859d2bb30143760051a39de06f2",
				live_id: 61,
				//当前直播id
				loading: false,
				//数据加载状态
				liveDetail: '',
				//直播详情
				settingData: '',
				//平台设置信息
				imgUrl: getApp().globalData.imgUrl,
				//图片地址
				bgStyle: 'background-size:cover;background-position:center center;background-repeat: no-repeat;',
				//背景图片样式
				shareWrap: false,
				// 展示分享弹层
				shareWrap2: false,
				// 展示分享弹层
				shareImg: '',
				// 分享图片
				goodsList: [],
				//直播绑定的商品
				hasmore: true,
				//是否还有数据，用于页面展示
				showGoods: false,
				//是否显示商品弹层
				playFlag: true,
				//视频是否播放
				showPauseBtn: false,
				//是否显示暂停按钮
				showBtnIcn: '',
				//视频播放控制层按钮图片
				colorArray: colorArray,
				//颜色数组
				msgList: [],
				//弹幕内容
				noticeList: [],
				//消息通知列表
				input_val: '',
				//输入框内容
				toBottom: '',
				//弹幕滚动到指定的元素
				live_state: false,
				//直播状态 默认0：未开启,1开启直播，2:直接结束
				backGrade: 1,
				// 是否被禁止直播
				forbidReason: null,
				//返回页面级别
				live_like_num: 0,
				//人气数
				live_click_num: 0, //观看数
				liveStateInfo: "",
				pn: 1,
				pageSize: 10,
				stat_end: 1, //终端，默认为1，pc端
				enableCamra: true, //摄像头是否开启
				enableMic: true, //麦克风是否开启
				isStopLive: false, //是否要结束直播
				isSocketInit:false,
        torchFlag:false,//手电筒
        front:'front',
        // 美颜 start
        Manager:1,
        filterindex:-1,//滤镜下标
        beautyFlag:1,
        beautySwitch:false,//美颜开关
        fliterSwitch:false,//滤镜开关
        beauty:0,//美颜，取值范围 0-9 ，0 表示关闭
        beautyValue:0,
        whiteness:0,//美白，取值范围 0-9 ，0 表示关闭
        whitenessValue:0,
        filterValue:'',
        filterList:[
          {
            name:'标准',back:'',id:0,val:'',
            image:'livepush/filter/biaozhun.png',
            ima_name:'standard',
          },
          {
            name:'粉嫩',back:'',id:1,val:'',
            image:'livepush/filter/fennen.png',
            ima_name:'pink',
          },
          {
            name:'怀旧',back:'',id:2,val:'',
            image:'livepush/filter/huaijiu.png',
            ima_name:'nostalgia',
          },
          {
            name:'蓝调',back:'',id:3,val:'',
            image:'livepush/filter/landiao.png',
            ima_name:'blues',
          },
          {
            name:'浪漫',back:'',id:4,val:'',
            image:'livepush/filter/langman.png',
            ima_name:'romantic',
          },
          {
            name:'清凉',back:'',id:5,val:'',
            image:'livepush/filter/qingliang.png',
            ima_name:'cool',
          },
          {
            name:'清新',back:'',id:6,val:'',
            image:'livepush/filter/qingxin.png',
            ima_name:'fresher',
          },
          {
            name:'日系',back:'',id:7,val:'',
            image:'livepush/filter/rix.png',
            ima_name:'solor',
          },
          {
            name:'唯美',back:'',id:8,val:'',
            image:'livepush/filter/weimei.png',
            ima_name:'aestheticism',
          },
          {
            name:'美白',back:'',id:9,val:'',
            image:'livepush/filter/white.png',
            ima_name:'whitening',
          },
          {
            name:'樱红',back:'',id:10,val:'',
            image:'livepush/filter/yinghong.png',
            ima_name:'cerisered',
          },        
        ],
        musicFlag:2,//切换音乐
        musictype:1,//
        input_val:'',//歌曲或歌手
        musicId:'',//音乐id
        music_list:[],//音乐列表
        musicInfo:[],//音乐选中的歌曲列表
        musicCurrent:1,
        musicTotal:0,
        music_slider:60,//背景音音量
        triggered:false,
        scrollMusic:'',
			};
		},
		components: {
			livePlayGoods,
			dataLoading,
			dataLoaded,
			uniPopup
		},
		props: {},
		//继续播放按钮图片
		computed: {
			...mapState(['hasLogin'])
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			uni.setKeepScreenOn({
				keepScreenOn: true
			});
			this.live_id = this.$Route.query.live_id;
			this.backGrade = this.$Route.query.backGrade;
			this.forbidReason = this.$Route.query.forbidReason;
			this.initData();
			this.getPlatform();
			this.shareImg = getApp().globalData.apiUrl + 'v3/video/front/video/live/share/shareImage?liveId=' +
				this.live_id + '&source=' + this.stat_end;
      this.getMusicList()
		},
		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {
			if (this.socket) {
				this.closeSocket();
			}
			uni.$emit('updateLiveList');
		},
		onShow() {
			this.getPlatform();
		},
		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage: function(options) {
			let that = this;
			that.shareWrap = false;
			that.shareWrap2 = false;
			let {
				liveDetail,
				live_id
			} = this;
			return {
				title: liveDetail.liveName,
				path: '/extra/live/livePlay?live_id=' + live_id,
				imageUrl: liveDetail.liveCover
			};
		},
		onShareTimeline: function(options) {
			let that = this;
			that.shareWrap = false;
			that.shareWrap2 = false;
			let {
				liveDetail,
				live_id
			} = this;
			return {
				title: liveDetail.liveName,
				query: 'live_id=' + live_id,
				imageUrl: liveDetail.liveCover
			};
		},
		methods: {
      onPulling(){
        this.triggered = true
      },
      refreshMoreMusic(e){
        this.musicCurrent = 1
        this.getMusicList()
      },
      // 输入框 （搜索歌曲和歌手）
      inputChange(e) {
      	this.input_val = e.detail.value.trim()
        if(e.detail.value.trim()==''){
          this.musicCurrent = 1
          this.getMusicList()
        }
      },
      // 歌曲搜索
      search(){
        let _this = this
        _this.musicCurrent = 1
      		if(this.input_val !=''){
      			this.getMusicList()
      		}else{
      			this.getMusicList()
      		}    
      },
      // 清除输入框 （搜索歌曲和歌手）
      clearInputVal(){
        this.input_val = ''
        this.musicCurrent = 1
        this.getMusicList()
      },
      // 打开背景音乐弹框
      bgmPopOpne(){
        let that = this
         this.$refs.showGoods_mp3.open()
      },
      // 音量设置弹框返回到歌曲列表弹框
      showGoods_mp3_volume_type(type){
        if(type==1){
          this.$refs.showGoods_mp3_volume.close()
          setTimeout(()=>{
            this.$refs.showGoods_mp3.open()
          },300)
          
        }else{
          this.$refs.showGoods_mp3.close()
          this.$refs.showGoods_mp3_volume.open()
          
        }
      },
      // 音乐列表下滑触发
      getMoreMusic(){
       if( this.musicTotal>this.music_list.length){
         this.musicCurrent+=1
         this.getMusicList()
       }
      },
      // 音乐列表
      getMusicList(){
        let _this = this
        let params = {};
        params.method = 'GET';
        params.url = 'v3/video/front/video/music/library/list';
        params.data = {};
        params.data.pageSize = _this.pageSize;
        params.data.current = _this.musicCurrent;
        if(_this.musictype==1){ 
          params.data.isRecommend = 1;
        }
        if(this.input_val){
          params.data.keyword = this.input_val;
        }
        request(params).then(result => {
        	if (result.state == 200) {
            _this.triggered = false
            _this.scrollMusic = ''
            _this.musicTotal = result.data.pagination.total
            result.data.list.forEach(item=>{
              item.type = 1
            })
            if(_this.musicCurrent==1){
              _this.music_list = result.data.list 
            }else{
              _this.music_list = _this.music_list.concat(result.data.list)
            }
        	}
        })
      },
      // 音乐弹框切换 val==1正在播放和 val==2背景音乐列表
      music_select(val){
        this.musicFlag = val
      },
      // 背景音乐列表推荐和全部切换
      volumeType(val){
        this.musictype = val
        this.musicCurrent = 1
        this.scrollMusic = 'music_list_id'
        this.getMusicList()
      },
      // 歌曲停止
      tingBGMFiles(item,index){
        let that = this
        let livePusherContext = uni.createLivePusherContext();
        if(this.musicId){
          livePusherContext.stopBGM()
             that.musicId = ''
            that.music_list.forEach((it)=>{
              it.type = 1
            })
            that.musicInfo[0].type = 1
        }
      },
      // 播放歌曲
      selectBGMFiles(item,index,type){
        let that = this
        let livePusherContext = uni.createLivePusherContext();
        that.music_list.forEach((it=>{
          it.type = 1
        })) 
      if(this.musicId){
        livePusherContext.stopBGM()      
      }
       livePusherContext.playBGM({
           url:item.musicLink,
           success: res => {
               that.musicInfo = []
               item.type = 2
               that.musicInfo.push(item)
                that.musicId = item.musicLibraryId
                if(type==1){
                  let ind = ''
                  that.music_list.forEach((it,index)=>{
                    if(it.musicLibraryId==item.musicLibraryId){
                      ind = index
                    }
                  })
                  that.music_list[ind].type = 2
                }else{
                  that.music_list[index].type = 2
                }
           }
       });
      },
      // 设置某一首背景音乐的远端音量的大小
      musicChange(e){
        let that =this
        let livePusherContext = uni.createLivePusherContext();
        that.music_slider = e.detail.value
        livePusherContext.setBGMVolume({
            volume:Number(e.detail.value)*0.01,
            success:res=>{
              console.log(res)
            }
        });
      },
      // 打开美颜弹窗
      beatyOpen(){
        this.$refs.stop_slider.open()
      },
      // 滤镜强度
      filterChange(e){
       let that = this
       that.filter_slider = e.detail.value
       if(that.filterindex!=-1){
         let livePusherContext = uni.createLivePusherContext();
         livePusherContext.applyFilter({
           path:'./filter/'+that.filterList[that.filterindex].ima_name,
           alpha:Number(e.detail.value)*0.01
          ,success: (res) => {
            console.log(res,111)
          }
         });     
       }
      },
      // 美颜
      sliderChange(e){
        let that = this
        that.beauty = e.detail.value
        that.beautyValue = Number(e.detail.value)*0.1
      },
      // 美白
      whitenessChange(e){
        let that = this
        that.whiteness = e.detail.value
        that.whitenessValue = Number(e.detail.value)*0.1
      },
      // 红润
      ruddyChange(e){
        let that = this
        that.ruddy = e.detail.value
        this.$refs.txView.setRuddyLevel({
            ruddyLevel:Number(e.detail.value)*0.1
        }, (res) => {
            that.ruddy = e.detail.value
        });
      },
      // 美颜开关
      switchChange(type){
        if(type==1){
          this.beautySwitch = false
          this.beauty = 0
          this.whiteness = 0
          this.beautyValue = 0
          this.whitenessValue = 0
        }else{
          this.fliterSwitch = false
          this.filterindex = -1
          this.filterValue = ''
          // let livePusherContext = uni.createLivePusherContext();
          // LivePusherContext.clearFilters()
        }
      },
      // 美颜选中
      beauty_select(val){
        this.beautySwitch = true
        this.beautyFlag = val
      },
      // 滤镜选择
      fliter_select(item,index){
        let that = this
        this.fliterSwitch = true
        this.filterindex = index
        this.filterValue = item.ima_name  
      },
      // 切换美颜和滤镜
      Manager_select(val){
        this.Manager = val
        
      },
      //开启闪光灯 
      dingClick(){
        let livePusherContext = uni.createLivePusherContext();
        if(this.front == 'back'){
          livePusherContext.toggleTorch();
        }else{
          uni.showToast({
              title: '只有后置摄像头才能开启闪光灯',
              icon: 'none',
              duration: 1000
          })
        }
      },
      // 切换前后摄像头
      switchCamera(){
       let that = this
       let livePusherContext = uni.createLivePusherContext();
       if(this.front=='front'){
         livePusherContext.switchCamera({
             success: a => {
                 console.log('切换前后摄像头:' + JSON.stringify(a));
                  that.front = 'back'
             }
         });
       }else{
         livePusherContext.switchCamera({
             success: a => {
                 console.log('切换前后摄像头:' + JSON.stringify(a));
                  that.front = 'front'
                  that.torchFlag = false
             }
         });
       }
      },
      
      
			// 禁止提示
			applicationShowTip(tip) {
				uni.showModal({
					title: this.$L('提示'),
					content: tip,
					showCancel: false
				})
			},

			//获取当前终端的方法
			getPlatform() {
				//app-1-start
				//#ifdef APP-PLUS
				this.stat_end = 2;
				//#endif
				//app-1-end
				//#ifdef H5
				this.stat_end = 3;
				//#endif
				//wx-1-start
				//#ifdef MP-WEIXIN
				this.stat_end = 4;
				//#endif
				//wx-1-end
			},

			//加入购物车事件
			addCart(good) {
				if (!this.hasLogin) {
					let cart_list = {
						storeCartGroupList: [{
							promotionCartGroupList: [{
								cartList: [{
									buyNum: 1,
									goodsId: good.goodsId - 0,
									productId: good.defaultProductId ? good.defaultProductId :
										good.productId,
									productImage: good.goodsImage,
									goodsName: good.goodsName,
									isChecked: 1,
									productPrice: good.goodsPrice,
									productStock: good.goodsStock
								}],
							}],
							storeId: good.storeId,
							storeName: good.storeName,
							checkedAll: true
						}],
						checkedAll: true,
						invalidList: []
					}
					//未登录加入本地缓存
					let local_cart_list = uni.getStorageSync('cart_list') //购物车列表本地缓存
					if (local_cart_list) { //如果不是第一次存储
						let tmp_list1 = []
						let tmp_list2 = []
						cart_list.storeCartGroupList.forEach(item => {
							item.promotionCartGroupList.forEach(item1 => {
								item1.cartList.forEach(item2 => {
									local_cart_list.storeCartGroupList.forEach(v => {
										v.promotionCartGroupList.forEach(v1 => {
											v1.cartList.forEach(v2 => {
												if (v2.productId == item2
													.productId && v
													.storeId == item
													.storeId) {
													tmp_list1.push(v)
												}
											})
										})
									})
									tmp_list2 = local_cart_list.storeCartGroupList.filter(v => {
										return v.storeId == item.storeId
									})
								})
							})
						})
						if (tmp_list1.length > 0 && tmp_list2.length > 0) { //同一店铺同一商品
							local_cart_list.storeCartGroupList.map(item => {
								item.promotionCartGroupList.map(item1 => {
									item1.cartList.map(item2 => {
										if (item2.productId == this.productId && this.storeInf&&(item.storeId ==
											this.storeInf.storeId)) {
											item2.buyNum += this.currentSpecNum
										}
									})
								})
							})
						} else if (tmp_list1.length == 0 && tmp_list2.length > 0) { //同一店铺不同商品
							local_cart_list.storeCartGroupList.map(item => {
								if (this.storeInf&&(item.storeId == this.storeInf.storeId)) {
									item.promotionCartGroupList.map(item2 => {
										item2.cartList.push(cart_list.storeCartGroupList[0]
											.promotionCartGroupList[0].cartList[0])
									})
								}
							})
						} else { //不同店铺不同商品
							local_cart_list.storeCartGroupList.push(cart_list.storeCartGroupList[0])
						}
						// 未登录购物车展示数据
						uni.setStorage({
							key: 'cart_list',
							data: local_cart_list,
							success: function() {
								//更新购物车数量和购物车数据
							}
						});

					} else {
						uni.setStorage({
							key: 'cart_list',
							data: cart_list,
							success: function() {
								//更新购物车数量和购物车数据
							}
						});
					}
					uni.showToast({
						title: this.$L('加入购物车成功！'),
						icon: 'none',
						duration: 700
					})
				} else { //已登录
					this.$request({
						url: 'v3/business/front/cart/add',
						data: {
							productId: good.defaultProductId ? good.defaultProductId : good.productId,
							number: 1,
						},
						method: 'POST'
					}).then(res => {
						if (res.state == 200) {
							//更新购物车数量
							this.$api.msg(res.msg);
						} else {
							this.$api.msg(res.msg);
						}
					}).catch((e) => {})
				}
			},

			//初始化数据
			initData() {
				let livePusherContext = wx.createLivePusherContext();
        console.log(livePusherContext)
				livePusherContext.startPreview({
					fail: (err) => {
						console.log(err)
					}
				});
				this.getLiveInfo();
				this.getLiveGoods(); 
			},

			// 获取直播详情
			getLiveInfo() {
				let {
					live_id
				} = this;
				let _this = this
				let param = {};
				param.url = 'v3/video/front/video/live/playingPage';
				param.data = {
					liveId: live_id
				}
				_this.$request(param).then(res => {
					if (res.state == 200) {
						let liveDetail = res.data;
						_this.liveDetail = liveDetail;
						if (liveDetail.liveState == 5) {
							// 被删除
							uni.showModal({
								title: _this.$L('提示'),
								content: _this.$L('该直播已被删除'),
								showCancel: false,
								confirmText: _this.$L('返回'),
								success: res => {
									if (res.confirm) {
										// uni.navigateBack({});
										_this.$Router.back(1)
									}
								}
							});
						} else if (liveDetail.playState == 2) {
							//禁止播放
							uni.showModal({
								title: res.data.msg.msg,
								content: res.data.msg.remark,
								showCancel: false,
								confirmText: _this.$L('返回'),
								success: res => {
									if (res.confirm) {
										// uni.navigateBack({});
										_this.$Router.back(1)
									}
								}
							});
						} else if (liveDetail.liveState == 3) {
							//录制中
							uni.showModal({
								title: '',
								content: _this.$L('正在录制中，录制完成才可以观看哦！'),
								showCancel: false,
								confirmText: _this.$L('返回'),
								success: res => {
									if (res.confirm) {
										// uni.navigateBack({});
										_this.$Router.back(1)
									}
								}
							});
						}
					} else {
						//禁止播放
						uni.showModal({
							title: res.msg.msg,
							content: res.msg.remark,
							showCancel: false,
							confirmText: this.$L('返回'),
							success: res => {
								if (res.confirm) {
									// uni.navigateBack({});
									_this.$Router.back(1)
								}
							}
						});
					}
				})
			},

			//关闭分享
			closeShare() {
				let that = this;
				that.shareWrap = false;
				that.shareWrap2 = false;

			},

			touchmoveshare() {
				return false;
			},

			initSocket() {
				if (this.socket) {
					this.closeSocket();
				}
				const {
					live_id,
					liveDetail
				} = this;
				const authorInfo = liveDetail;
				let userInfo = {
					live_id: live_id ? live_id : 1,
					is_own: 1
				};

				if (authorInfo.authorId) {
					userInfo.author_name = authorInfo.memberNickname ? authorInfo.memberNickname : authorInfo.memberName;
					userInfo.author_id = authorInfo.authorId;
				}

				this.socket = io(getApp().globalData.socketUrl, {
					reconnection: true,
					jsonp: true,
					transports: ['websocket', 'polling'],
					timeout: 5000,
					query: `author_name=${userInfo.author_name}&author_id=${userInfo.author_id}&live_id=${userInfo.live_id}&is_own=1`
				});
				this.socket.on("connect", () => {
					this.isSocketInit = true
					//给服务端发送消息
					this.socket.emit("update_user", userInfo); //获取服务端的消息
					if (this.liveDetail.isSelf != 1) {
						this.socket.emit("update_user", userInfo); //获取服务端的消息
					}

					this.socket.on("get_msg", e => {
						if (typeof e.msg === 'string') {
							this.socketHandle('wec', e);
						} else {
							this.socketHandle('msg', e.msg);
						}
					}); //获取服务端新人信息

					this.socket.on("get_new_user", e => {
						this.socketHandle2('new', e);
					});
					this.socket.on("disconnect", function() {});
				}); //获取关注的返回信息

				this.socket.on("get_follow_msg", e => {
					this.socketHandle2('follow', e);
				}); //获取直播点击数

				this.socket.on("get_click_num", e => {
					this.clickNumHandle(e);
				}); //获取直播人气数

				this.socket.on("get_like_num", e => {
					this.likeNumHandle(e);
				}); // 直播结束

				this.socket.on('stop_live', e => {
					console.log('stop_live')
					this.handleStopLive(e);
				});
			},

			//关闭socket
			closeSocket() {
				if (this.socket) {
					this.socket.close();
				}
			},

			socketHandle(type, msg) {
				let {
					msgList
				} = this;

				if (msg.type) {
					return;
				}

				msg.type = type;
				msg.color_random = colorArray[Math.floor(Math.random() * 8)];
				msgList.push(msg);
				this.msgList = msgList,
					this.toBottom = msgList && msgList.length > 0 ? 'item' + (msgList.length - 1) : 0
			},

			//直播结束
			handleStopLive(e) {
				let {
					backGrade,
					liveDetail
				} = this;
				this.liveStateInfo = JSON.parse(e);
				this.$refs.stopVideo.open();
			},

			// 结束返回
			endBack() {
				let {
					backGrade
				} = this;
				this.enableCamra = false;
				this.enableMic = false;
				const pages = getCurrentPages(); //获取页面栈
				const beforePage = pages[pages.length - (backGrade * 1 + 1)]; //前一个页面
				if (beforePage&&beforePage.route.indexOf('myVideo') != -1) {
					beforePage.$vm.changeTab('goods');
				}
				this.$Router.back(backGrade * 1);
			},

			//获取服务端新人信息
			socketHandle2(type, msg) {
				let {
					noticeList
				} = this;

				if (msg.type) {
					return;
				}

				if (noticeList.filter(el => el.type === 'follow').length > 0 && type === 'follow') {
					return;
				}

				msg.type = type;
				msg.timer = setTimeout(() => {
					this.noticeRemoveItem(type);
				}, 2000);

				if (type === 'follow') {
					noticeList.push(msg);
				} else {
					let index = noticeList.findIndex(el => el.type === 'new');

					if (index > -1) {
						noticeList[index] = msg;
					} else {
						noticeList.push(msg);
					}
				}
				this.noticeList = noticeList;
			},

			noticeRemoveItem(type) {
				let {
					noticeList
				} = this;
				let index = noticeList.findIndex(el => el.type === type);

				if (index > -1) {
					clearTimeout(noticeList[index].timer);
					noticeList.splice(index, 1);
				}

				this.noticeList = noticeList;

			},

			// 点击量处理
			clickNumHandle(e) {
				let {
					liveDetail,
					live_click_num
				} = this;
				liveDetail.clickNum = e.click_num;
				this.liveDetail = liveDetail;
				this.live_click_num = initNum(e.click_num);
			},

			// 人气
			likeNumHandle(e) {
				let {
					liveDetail,
					live_like_num
				} = this;
				liveDetail.likeNum = e.like_num;
				this.liveDetail = liveDetail;
				this.live_like_num = initNum(e.like_num);
			},

			// 发送消息
			publishComment(e) {
				const {
					live_id,
					liveDetail,
					live_state
				} = this;

				if (!live_state) {
					//直播开始后才可以聊天
					uni.showToast({
						title: this.$L('直播开始后才可以聊天'),
						icon: 'none'
					});
					return false;
				}

				if (this.hasLogin && liveDetail.authorId != null) {
					let content = e.detail.value; //没有内容的话直接结束

					if (!content.trim()) {
						return false;
					}

					let msg = {
						author_id: liveDetail.authorId,
						author_name: liveDetail.memberNickname,
						live_id: live_id,
						msg: content
					};
					this.socket.emit("send_msg", msg); //清空输入框的内容
					var that = this;
					that.input_val = '';
					setTimeout(() => {
						that.input_val = ''
					})
				} else {
					uni.showModal({
						title: this.$L('温馨提示!'),
						content: this.$L('需要登录才能操作'),
						confirmText: this.$L('确定'),
						cancelText:this.$L('取消'),
						success: res => {
							if (res.confirm) {
								getApp().globalData.goLogin();
							}
						}
					});
				}
			},

			//增加人气事件
			add_heart() {
				let {
					live_id,
					liveDetail
				} = this;

				if (this.hasLogin) {
					this.socket.emit("update_like_num", {
						author_id: liveDetail.author.author_id,
						live_id: live_id
					});
				} else {
					uni.showModal({
						title: this.$L('温馨提示!'),
						content: this.$L('需要登录才能操作'),
						confirmText: this.$L('确定'),
						cancelText:this.$L('取消'),
						success: res => {
							if (res.confirm) {
								getApp().globalData.goLogin();
							}
						}
					});
				}
			},

			// 返回上级页面提示
			goBack() {
				let {
					live_state
				} = this;
				let _this = this
				if (live_state) {
					uni.showModal({
						title: this.$L('温馨提示!'),
						content: this.$L('返回上级页面将关闭直该场直播，确认返回吗？'),
						confirmText: this.$L('确定'),
						cancelText:this.$L('取消'),
						success: res => {
							if (res.confirm) {
								this.enableCamra = false;
								this.enableMic = false;
								this.stopLive();
							}
						}
					});
				} else {
					uni.showModal({
						title: this.$L('温馨提示!'),
						content: this.$L('直播还未开始，确认返回上级页面吗？'),
						confirmText: this.$L('确定'),
						cancelText:this.$L('取消'),
						success: res => {
							if (res.confirm) {
								this.enableCamra = false;
								this.enableMic = false;
								// 处理返回列表页 后退跳转层级重复问题
								let pages = getCurrentPages();
								let flag = true;
								let preIndex = '';
								pages.forEach((item, index) => {
									if (item.route == "extra/user/my") {
										if (!preIndex) {
											preIndex = index;
										}
									} else if (item.route == "extra/live/livePush") {
										flag = false;
										_this.$Router.back(index - preIndex)
									}
								})
								if (flag) {
									_this.$Router.replace('/extra/user/my')
								}
							}
						}
					});
				}
			},

			//分享点击事件
			showShare() {
				let that = this;
				if (this.forbidReason) {
					this.applicationShowTip(this.forbidReason);
				} else if (!that.live_state) {
					uni.showToast({
						title: this.$L('直播开始后才可以分享哦！'),
						icon: 'none'
					});
				} else {
					that.shareWrap2 = true;
				}
			},

			//生成海报事件
			createhb() {
				this.shareWrap = true;
				this.shareWrap2 = false;
			},

			//下载海报
			downloadImg() {
				let {
					shareImg
				} = this;

				let _this = this;

				uni.downloadFile({
					url: shareImg,
					success: res_info => {
						if (res_info.statusCode == 200) {
							uni.getSetting({
								success(res_down) {
									if (!res_down.authSetting['scope.writePhotosAlbum']) {
										uni.showModal({
											title: _this.$L('提示'),
											content: _this.$L('您好,需要开启相册权限'),
											showCancel: false,
											confirmText: _this.$L('确定'),

											success(res) {
												if (res.confirm) {
													uni.authorize({
														scope: 'scope.writePhotosAlbum',

														success() {
															// 用户已经同意,后续调用时不会弹窗询问
															_this.saveHb(res_info
																.tempFilePath);
														},

														fail() {
															//拒绝授权
															uni.showToast({
																title: _this
																	.$L(
																		'抱歉，没有授权无法下载海报'),
																icon: 'none'
															});
														}

													});
												}
											}

										});
									} else {
										_this.saveHb(res_info.tempFilePath);
									}
								}

							});
						} else {
							uni.showToast({
								title: _this.$L('下载失败'),
								icon: 'none'
							});
						}
					}
				});
			},

			/**
			 * 保存图片
			 */
			saveHb: function(img) {
				let _this = this;

				uni.saveImageToPhotosAlbum({
					filePath: img,
					success: function(data) {
						_this.shareWrap = false;
						_this.shareWrap2 = false;

						uni.showToast({
							title: _this.$L('已保存到本地'),
							icon: 'success',
							duration: 2000
						});
					},
					complete: function(res) {}
				});
			},

			//获取直播商品
			getLiveGoods() {
				this.loading = true;
				let {
					goodsList,
					hasmore,
					live_id
				} = this;
				let param = {};
				param.method = 'GET';
				param.url = 'v3/video/front/video/live/goodsList';
				param.data = {
					current: this.pn,
					pageSize: this.pageSize,
					liveId: live_id
				};
				this.$request(param).then(res => {
					let list = res.data.list;
					if (this.pn == 1) {
						this.goodsList = list;
					} else {
						this.goodsList = this.goodsList.concat(list);
					}
					if (checkPageHasMore(res.data.pagination)) {
						this.pn++;
					} else {
						this.hasmore = false;
					}
					this.loading = false;
				})
			},

			showGoodsFun() {
				let {
					showGoods,
					liveDetail
				} = this;
				let that = this;
				if (liveDetail.goodsNum * 1 > 0) {
					that.showGoods = true;
				}
			},

			//关闭商品弹层
			closeGoods() {
				let {
					showGoods
				} = this;
				let that = this;
				that.showGoods = false;
			},

			//绑定的商品分页事件
			getMoreGoods() {
				if (this.hasmore) {
					this.getLiveGoods();
				}
			},

			//开始直播
			startLive() {
				if (this.forbidReason) {
					this.applicationShowTip(this.forbidReason);
				} else {
					uni.showLoading({
						title: '直播开启中...'
					})
					this.operateLive('start');
				}
			},

			// 结束直播
			stopLiveTip() {
				if (this.isStopLive) {
					return;
				}
				this.isStopLive = true;
				uni.showModal({
					title: this.$L('温馨提示!'),
					content: this.$L('确定结束该场直播吗？'),
					success: res => {
						if (res.confirm) {
							this.stopLive();
						} else {
							this.isStopLive = false;
						}
					}
				});
			},

			//结束直播
			stopLive() {
				this.operateLive('end');
			},

			operateLive(type) {
				let _this = this;
				if (type == 'start') {
					//获取推流地址
					let params = {};
					params.method = 'POST';
					params.url = 'v3/video/front/video/live/getLivePath';
					params.data = {};
					params.data.liveId = _this.live_id;
					_this.$request(params).then(result => {
						if (result.state == 200) {
							_this.pushurl = result.data.livePath;
							livePusherContext = uni.createLivePusherContext();
							livePusherContext.start({
								success: function(data) {
									console.log('success connect livePusher')
									_this.live_state = true
									_this.initSocket();
								},
								fail: function(data) {
									/* 直播开启正常也会返回fail，暂且注释提示了 */
									console.log('fail connect livePusher')
									setTimeout(()=>{
										this.$api.msg('推流失败，请重试')
									},800)
								},
								complete: function(res) {
									uni.hideLoading()
								}
							}); //开始推流
							_this.live_state = true;
						}
					})
				} else {
					livePusherContext.stop({
						success: function(data) {},
						fail: function(data) {},
						complete: function(res) {
							_this.isStopLive = false;
							// 处理返回列表页 后退跳转层级重复问题
							let pages = getCurrentPages();
							let flag = true;
							let preIndex = '';
							pages.forEach((item, index) => {
								if (item.route == "extra/user/my") {
									if (!preIndex) {
										preIndex = index;
									}
								} else if (item.route == "extra/live/livePush") {
									flag = false;
									_this.$Router.back(index - preIndex);
								}
							})
							if (flag) {
								_this.$Router.back(1)
							}
						}
					}); //结束推流
				}
			},
			
			//获取直播状态
			statechange(e) {
				/* code 状态值
				 * 1001: 已经连接推流服务器
				 * 1002: 已经与服务器握手完毕,开始推流
				 * 1005: 推流动态调整分辨率
				 * 1006: 推流动态调整码率
				 * 1007: 首帧画面采集完成
				 * 1008: 编码器启动
				 * 1102: 网络断连, 已启动自动重连
				 * -1307: 网络断连，且经多次重连抢救无效，更多重试请自行重启推流
				 */
				// console.log('状态码', e.detail.code, e.detail.code==1001 ? '成功' : (e.detail.code==1102 ? '重连' : (e.detail.code==-1307 ? '失败' : '')));
				if (e.detail.code == 1001) {
					console.log('推流连接成功');
					uni.hideLoading();
					live_connect_success = true;
					re_request_live_time = 0;
					if (!this.socket && this.live_state){
						// console.log('重新连接 socket');
						this.initSocket();
					}
				} else if (e.detail.code == -1307) {
					live_connect_success = false;
					//可以尝试重新连接两次
					if (re_request_live_time < re_request_live_num) {
						re_request_live_time += 1;
						this.operateLive('start');
						console.log('第' + re_request_live_time + '次重连');
					} else {
						uni.hideLoading();
						this.live_state = false;
						re_request_live_time = 0;
						uni.showToast({
							title: this.$L('网络连接失败，请切换网络后重新开启直播'),
							icon: 'none'
						});
						livePusherContext = uni.createLivePusherContext();
						livePusherContext.stop();
						livePusherContext.stopPreview();
						setTimeout(()=>{
							// console.log('重新开启摄像头');
							livePusherContext.startPreview({
								fail: (err) => {
									console.log('fail', err)
								}
							})
						}, 0)
					}
				}
			},
			netstatus(e) {},
			error(e) {},
			start: function() {
				this.context.start({
					success: (a) => {}
				});
			},
			close: function() {
				this.context.close({
					success: (a) => {}
				});
			},
		},
		beforeRouteLeave(to, from, next) {
			let {
				backGrade
			} = this
			const pages = getCurrentPages(); //获取页面栈
			const beforePage = pages[pages.length - (backGrade * 1 + 1)]; //前一个页面
			if (beforePage.route.indexOf('myVideo') != -1) {
				beforePage.$vm.changeTab('live');
			}
			next();
		},
	};
</script>
<style>
	page {
		background: rgba(0, 0, 0, 0.3);
	}
  .start_live{
    font-size: 68rpx !important;
    margin-top: 0 !important;
  }
	view {
		box-sizing: content-box;
	}

	.live_back,
	.living {
		width: 750rpx;
		height: 100vh;
	}

	.live_header {
		position: absolute;
		top: 80rpx;
		left: 20rpx;
		z-index: 2;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: flex-start;
		height: 80rpx;
	}

	.live_header text {
		color: #fff;
	}

	.live_header .go_back {
		width: 45rpx;
		height: 47rpx;
	}

	.live_header .avator {
		width: 78rpx;
		height: 78rpx;
		border-radius: 50%;
		border: 2px solid #fff;
		margin-left: 8rpx;
	}

	.live_header .mem_info {
		margin-left: 20rpx;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-content: space-between;
	}

	.live_header .mem_info .name {
		max-width: 150rpx;
		color: #fff;
		font-size: 28rpx;
		line-height: 32rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		font-weight: 600;
		margin: 5rpx 0 6rpx;
	}

	.live_header .mem_info .stat_num {
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
	}

	.live_header .mem_info .stat_num text {
		color: #fff;
		font-size: 22rpx;
		line-height: 36rpx;
		white-space: nowrap;
		font-weight: 600;
	}

	.live_header .live_fllow {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		width: 120rpx;
		height: 50rpx;
		background:var(--color_video_main);
		border-radius: 25rpx;
		margin-left: 25rpx;
	}

	.live_header .live_fllow image {
		width: 46rpx;
		height: 46rpx;
	}

	.live_header .live_fllow text {
		color: #fff;
		font-size: 24rpx;
		margin-right: 8rpx;
	}

	.live_footer {
		position: fixed;
		z-index: 99;
		left: 40rpx;
		bottom: 30rpx;
		width: 710rpx;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
		overflow-x: hidden;
		padding-top: 30rpx;
	}

	.live_footer .goods {
		width: 85rpx;
		height: 91rpx;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		position: relative;
	}

	.live_footer .goods image {
		width: 100%;
		height: 100%;
	}
  
	  .live_footer .goods .goods_icon{
		font-size: 103rpx;
		color: var(--color_video_main);
		line-height: 70rpx;
	  }

	.live_footer .goods text {
		position: absolute;
		line-height: 23rpx;
		color: #fff;
		font-size: 22rpx;
		
		
	}
	
	.live_footer .goods text.goods_icon{
		bottom: 12rpx;
	}

	.live_footer .talk_con {
		height: 65rpx;
		width: 325rpx;
		margin-left: 30rpx;
		border-radius: 30rpx;
		background: rgba(0, 0, 0, 0.3);
		padding: 0 20rpx;
		color: #fff;
		font-weight: 600;
		font-size: 26rpx;
	}

	.live_footer .share {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: center;
		width: 77rpx;
		margin-left: 21rpx;
	}

	.live_footer .share image {
		width: 65rpx;
		height: 65rpx;
		font-weight: 600;
	}

	.live_footer .share text {
		color: #fff;
		font-size: 22rpx;
		margin-top: 10rpx;
	}

	.select-wrap {
		position: fixed;
		top: 0;
		left: 0;
		width: 750rpx;
		height: 100vh;
		background-color: rgba(0, 0, 0, 0.45);
		z-index: 9999;
	}

	.select-wrap .share-mode {
		position: absolute;
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.select-wrap .share-mode .share-img {
		width: 72vw;
		border-radius: 20rpx;
		overflow: hidden;
	}

	.select-wrap .share-mode .share-img image {
		width: 100%;
		height: 0;
		border-radius: 20rpx;
	}

	.select-wrap .share-mode .ul {
		display: flex;
		align-items: center;
		width: 100%;
		justify-content: space-evenly;
	}

	.share-mode .item {
		display: flex;
		flex-direction: column;
		align-items: center;
		text-align: center;
		cursor: pointer;
		border: none;
		margin: 0;
		padding: 0;
		line-height: 1;
		background-color: transparent;
		height: auto !important;
	}

	.share-mode .item::after {
		border: none;
	}

	.share-mode .item image {
		width: 106rpx;
		height: 0;
	}

	.share-mode .item text {
		color: #fff;
		font-size: 24rpx;
		margin-top: 30rpx;
	}

	.select-wrap .close {
		width: 750rpx;
		height: 120rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.select-wrap .close image {
		width: 30px;
		height: 30px;
	}

	.share-mode .share-img {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	/* 视频绑定的商品模块 start */

	.live_bind_goods_mask {
		width: 750rpx;
		height: 100vh;
		position: absolute;
		left: 0;
		bottom: 0;
		z-index: 100;
		background: rgba(0, 0, 0, 0.45);
	}

	.live_bind_goods {
		width: 750rpx;
		height: 850rpx;
		position: fixed;
		left: 0;
		bottom: 0;
		z-index: 4;
		background: #fff;
		border-radius: 15rpx 15rpx 0 0;
		background: #f8f8f8;
	}

	.live_bind_goods .header {
		width: 710rpx;
		height: 100rpx;
		padding: 0 20rpx;
		background: #fff;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		border-radius: 15rpx 15rpx 0 0;
	}

	.live_bind_goods .header text {
		color: #2d2d2d;
		font-size: 32rpx;
	}

	.live_bind_goods .header image {
		width: 47rpx;
		height: 47rpx;
	}

	.scroll_goods {
		height: 750rpx;
	}

	/* 视频绑定的商品模块 end */

	.empty_158 {
		width: 750rpx;
		height: 158rpx;
	}

	/***** 弹幕 start *****/

	.barrage_wrap {
		position: absolute;
		bottom: 200rpx;
		left: 30rpx;
		width: 500rpx;
		background-color: transparent;
		z-index: 99;
		height: 500rpx;
		display: flex;
		flex-direction: column;
		justify-content: flex-end;
		align-items: flex-start;
	}

	.notice {
		height: 50rpx;
		max-width: 500rpx;
		display: inline-block;
		border-radius: 10rpx;
		line-height: 50rpx;
		margin-bottom: 10rpx;
	}

	.notice text {
		color: #fff;
		font-size: 24rpx;
		height: 50rpx;
		max-width: 500rpx;
		display: inline-block;
		background-color: var(--color_video_main);
		border-radius: 10rpx;
		line-height: 50rpx;
		margin-bottom: 10rpx;
		padding: 0 15rpx;
		font-weight: 600;
	}

	.barrage {
		min-height: 100rpx;
		max-height: 450rpx;
	}

	.gonggao {
		background-color: rgba(0, 0, 0, 0.3);
		white-space: normal;
		padding: 12rpx 16rpx;
		border-radius: 10rpx;
		word-break: break-all;
		font-weight: 600;
	}

	.gonggao .title {
		color: var(--color_video_main);
		font-size: 26rpx;
		line-height: 36rpx;
	}

	.gonggao .ba_txt {
		color: #fff;
		font-size: 26rpx;
		line-height: 36rpx;
	}

	.barrage_item {
		background-color: rgba(0, 0, 0, 0.3);
		max-width: 500rpx;
		border-radius: 23rpx;
		padding: 0rpx 15rpx 10rpx 15rpx;
		margin-top: 10rpx;
		white-space: normal;
		word-break: break-all;
		display: inline-block;
		font-weight: 600;
	}

	.barrage_item .name {
		font-size: 25rpx;
		line-height: 36rpx;
		margin-right: 15rpx;
	}

	.barrage_item .barrage_item_con {
		color: #fff;
		font-size: 25rpx;
	}

	/***** 弹幕 end *****/

	.canvas {
		background: transparent;
		width: 180rpx;
		height: 400rpx;
		position: fixed;
		right: -40rpx;
		bottom: 130rpx;
	}

	.live_footer .add_heart {
		width: 90rpx;
	}

	.live_footer .add_heart text {
		color: var(--color_video_main);
	}

	.click_num {
		color: #FFFFFF;
	}

	.stop_pop_box {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 600rpx;
		border-radius: 15rpx;
		background-color: #FFFFFF;
	}

	.stop_pop_tit {
		font-size: 34rpx;
		font-weight: 700;
		margin-bottom: 40rpx;
		padding-top: 40rpx;
	}

	.stop_pop_content {
		color: #666666;
		margin-top: 16rpx;
	}

	.stop_pop_btn {
		width: 100%;
		color: #5D7088;
		font-size: 34rpx;
		font-weight: 700;
		border-top: 2rpx solid #f1f1f1;
		text-align: center;
		margin-top: 60rpx;
		padding-top: 30rpx;
		padding-bottom: 30rpx;
	}
  
  
  
  .toolDiv{
  	position: fixed;
  	right: 20rpx;
  	top: 90rpx;
  	/* width: 750rpx; */
    margin-top: 60rpx;
  	flex-direction: column;
  	/* justify-content: flex-start; */
  	align-items: center;
    z-index: 9;
  }
  .toolDiv-star{
  	width: 490rpx;
  	height: 94rpx;
  	border-radius: 45rpx;
  	background-color: var(--color_video_main);
  	flex-direction: row;
  	justify-content: center;
  	align-items: center;
  	margin-top:40rpx;
  }
  .toolDiv-star-txt{
  	font-size: 30rpx;
  	color: #FFFFFF;
  	text-align: center;
  }
  
  .toolDiv-list{
  	/* width: 750rpx; */
  	flex-direction: column;
  	justify-content: flex-start;
  	align-items: flex-end;
  	flex-wrap: wrap;
  	
  }
  .toolDiv-list-item{
    display: flex;
  	flex-direction: column;
  	justify-content: flex-start;
  	align-items: center;
  	width: 84rpx;
  	margin-top:48rpx;
  	margin-left: 10rpx;
  	margin-right: 10rpx;
  	
  }
  .toolDiv-list-item image{
    width: 80rpx;
    height: 80rpx;
  }
  .toolDiv-list-item-txt{
  	font-size: 24rpx;
  	color: #F1F1F1;
  	
  }
  
  .toolDiv-list-item-iconYan{
  	font-size: 38rpx;
  	color: #FFFFFF;
  	margin-bottom: 2rpx;
  }
  .toolDiv-list-item-cionLujing{
  	font-size: 50rpx;
  	color: #f9f9f9;
  	/* margin-top: -8rpx; */
  }
  .toolDiv-list-item-cionLujing2{
  	/* margin-top: -4rpx; */
  }
  .toolDiv-list-item-iconHuan{
  	font-size: 40rpx;
  	color: #FFFFFF;
  	margin-top: 3rpx;
  }
  .toolDiv-list-item-iconDing{
  	font-size: 55rpx;
  	color: #FFFFFF;
  	/* margin-top: -10rpx; */
  }
  .toolDiv-list-item-iconDing2{
  	margin-top: -8rpx;
  }
  .toolDiv-list-item-iconJingxing{
  	font-size: 47rpx;
  	color: #FFFFFF;
  	margin-top: -6rpx;
  }
  .toolDiv-list-item-iconJingxing2{
  	margin-top: -4rpx;
  }
  
  /* 美颜 start */
  .stop_slider_box{
    width: 750rpx;
    padding-top: 0rpx;
    font-size: 10rpx;
    background-color: #fff;
    border-top-left-radius: 40rpx;
    border-top-right-radius: 40rpx;
  }
  .stop_slider_box_bottom{
    display: flex;
    align-items: center;
    width: 750rpx;
    height: 271rpx;
    flex-direction: row;
    box-sizing: border-box;
    padding:0 30rpx;
  }
  .slider_box_bottom_one{
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 99rpx;
    font-size: 24rpx;
    margin-right: 87rpx;
    
  }
  .slider_box_bottom_one:last-child{
    margin-right: 0;
  }
  .slider_box_bottom_one_img{
    width: 99rpx;
    height: 99rpx;
    border-radius: 50%;
    border: 3rpx solid transparent;
    display: flex;
    align-items: center;
    overflow: hidden;
    justify-content: center;
  }
  .bottom_one_img{
    border: 3rpx solid var(--color_video_main);
  }
  .box_bottom_one_img{
    width: 99rpx;
    height: 99rpx;
    border-radius: 50%;
    
  }
  .slider_box_bottom_one_switch{
    background: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .slider_beauty{
    display: flex;
    flex-direction: row;
    margin: 25rpx 0;
    align-items: center;
    width: 750rpx;
    box-sizing: border-box;
    padding: 0 20rpx;
    
  }
  .slider_beauty slider{
    width: 100%;
  }
  .slider_beauty_text{
    font-size: 28rpx;
    margin-right: 10rpx;
  }
  .slider_box_bottom_one_text{
    font-size: 28rpx !important;
    margin-top: 10rpx;
    font-family: PingFang SC;
    font-weight: 500;
    color: #666666;
  }
  .bottom_one_text{
    color: var(--color_video_main);
  }
  .stop_slider_header{
    height: 84rpx;
    padding: 0 45rpx;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1rpx solid #f1f1f1;
    
  }
  .stop_slider_header_text_box{
    display: flex;
    position: relative;
    height: 84rpx;
    flex-direction: row;
    align-items: center;
    margin-right: 65rpx;
  }
  .stop_slider_header_text{
    font-size: 30rpx;
    color: #999;
  }
  .stop_slider_header_text_one{
    color:#000;
    font-weight: bold;
  }
  .stop_slider_header_text_xian{
    width: 32rpx;
    height: 4rpx;
    background: #000000;
    border-radius: 2rpx;
    position: absolute;
    display: flex;
    bottom: 0;
    left: 16rpx;
  }
  .stop_slider_header_flex{
    display: flex;
    flex-direction: row;
    flex: 1;
  }
  .stop_slider_header_img{
    width: 30rpx;
    height: 30rpx;
    
  }
  .slider_bottom_one{
    display: flex;
    flex-direction: row;
    align-items: center;
    padding-left: 6rpx;
  }
  .slider-view-p{
    display: flex;
    flex-direction: row;
  }
  .slider_box_bottom_foot{
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  }
  .slider_bottom_one .slider_box_bottom_one_img{
    width: 114rpx;
    height: 114rpx;
  }
  .slider_bottom_one .slider_box_bottom_one{
    margin-right: 30rpx;
    width: 114rpx;
  }
  .slider_bottom_one .box_bottom_one_img{
    width: 114rpx;
    height: 114rpx;
  }
  /* 美颜 end */
  .asfsda{
    position: fixed;
    color: #fff;
    left: 0rpx;
    top: 200rpx;
  }
  /* 音乐 start */
  .slider_beauty_live{
    width: 750rpx;
    background: #FFFFFF;
    border-radius: 20rpx 20rpx 0rpx 0rpx;
  }
  .slider_beauty_live_header{
    height: 94rpx;
    width: 750rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    border-bottom: 1rpx solid #f1f1f1;
    padding: 0 20rpx;
    box-sizing: border-box;
  }
  .slider_beauty_volume{
    display: flex;
    align-items: center;
    flex-direction: row;
    height: 94rpx;
  }
  .slider_beauty_live_header_one{
    position: relative;
  }
  .slider_beauty_volume_one{
    justify-content: center;
    position: absolute;
    left: 25rpx;
    top: 50%;
    height: auto;
    transform: translateY(-50%);
  }
  .slider_beauty_volume_img{
    width: 32rpx;
    height: 29rpx;
  }
  .slider_beauty_volume_text{
    font-size: 28rpx;
    font-family: PingFang SC;
    font-weight: 500;
    color: #333333;
    margin-left: 13rpx;
  }
  .slider_beauty_volume_center{
    flex: 1;
    height: 94rpx;
    display: flex;
    flex-direction: row;
    justify-content: center;
  }
  .stop_slider_header_text_box_one{
    height: 94rpx;
  }
  .header_text_box_one{
    margin-right: 0 !important;
  }
  .stop_slider_header_text_xian_one{
    left: 50%;
    transform: translateX(-50%);
  }
  .stop_slider_header_text_two{
    font-size: 30rpx;
    font-family: PingFang SC;
    font-weight: 500;
    color: #999999;
  }
  .stop_slider_header_text_one1{
    font-weight: bold;
    color: #000000;
  }
  .slider_beauty_volume_type{
    height: 110rpx;
    width: 750rpx;
    display: flex;
    align-items: center;
    padding: 0 20rpx;
    flex-direction: row;
  }
  .volume_type{
    background: #F3F3F3;
    border-radius: 8rpx;
    display: flex;
    height: 60rpx;
    align-items: center;
    justify-content: center;
    flex-direction: row;
    margin-right: 19rpx;
    padding: 0 30rpx;
  }
  .volume_type_text{
    font-size: 28rpx;
    font-family: PingFang SC;
    font-weight: 500;
    color: #999999;
  }
  .volume_type_text_one{
    color: #000000;
  }
  .search_center_box{
    width: 750rpx;
    height: 66rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin-bottom: 50rpx;
  }
  .search_center{
    display: flex;
    flex-direction: row;
    align-items: center;
    border: none;
    width: 710rpx;
    height: 66rpx;
    padding-left: 20rpx;
    border-radius: 32.5rpx;
    background-color: #f5f5f5;
  }
  .search_icon{
    width: 30rpx;
    height: 30rpx;
    margin-top: 2rpx;
  }
  .clear_content{
    width: 45rpx !important;
    height: 45rpx !important;
    margin-right: 15rpx !important;
  }
  .sea_input{
    display: flex;
    align-items: center;
    border: none;
    flex: 1;
    height: 65rpx;
    padding-left: 20rpx;
    border-radius: 32.5rpx;
    background-color: #f5f5f5;
    font-size: 26rpx;
    font-family: PingFang SC;
    font-weight: 500;
    color: #B4B3B3;
  }
  .scroll_music_list{
    min-height: 400rpx;
    max-height: 500rpx;
  }
  .music_list{
    display: flex;
    align-items: center;
    flex-direction: row;
    width: 750rpx;
    padding: 0 20rpx;
    height: 80rpx;
    margin-bottom: 50rpx;
    box-sizing: border-box;
  }
  .music_list_cont{
    width: 80rpx;
    height: 80rpx;
    background: #F3F3F3;
    border-radius: 10rpx;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }
  .music_list_cont_center{
    flex: 1;
    padding-left: 27rpx;
    display: flex;
    flex-direction: column;
  }
  .music_list_center1{
    width: 400rpx;
    font-size: 30rpx;
    font-family: PingFang SC;
    font-weight: bold;
    color: #333333;
    text-overflow: ellipsis;
    lines:1;
  }
  .music_list_center2{
    width: 400rpx;
    font-size: 24rpx;
    font-family: PingFang SC;
    font-weight: 500;
    color: #999999;
    margin-top: 10rpx;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-overflow: ellipsis;
    lines:1;
  }
  .music_list_cont_right{
    width: 134rpx;
    height: 56rpx;
  }
  .music_list_cont_right_btn{
    width: 134rpx;
    height: 56rpx;
    background: var(--color_video_main);
    border-radius: 28rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: row;
  }
  .music_list_cont_right_btn_text{
    font-size: 28rpx;
    font-family: PingFang SC;
    font-weight: 500;
    color: #FFFFFF;
  }
  .music_list_cont_img{
    width: 36rpx;
    height: 42rpx;
  }
  .music_list_cont_play{
    width: 80rpx;
    height: 80rpx;
  }
  .slider_beauty_volume_type_box{
    height: 300rpx;
    width: 750rpx;
    padding-top: 60rpx;
  }
  .zanwugewu{
    width: 750rpx;
    margin-top: 20rpx;
    font-size: 30rpx;
    font-family: PingFang SC;
    font-weight: 500;
    color: #999999;
    text-align: center;
  }
  .showGoods_mp3_volume{
    width: 750rpx;
    background: #FFFFFF;
    border-radius: 20rpx 20rpx 0rpx 0rpx;
  }
  .slider_beauty_volume_img1{
    width: 17rpx;
    height: 29rpx;
  }
  .slider_beauty_volume_slider{
    height: 200rpx;
    width: 750rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: row;
  }
  .slider_beauty_volume_slider slider{
     width: 750rpx;
  }
  .stop_slider_header_text_three{
    font-size: 30rpx;
    font-family: PingFang SC;
    font-weight: 500;
    color: #333333;
  }
  .slider_beauty_volume_slider_text{
    width: 750rpx;
    margin-top: 83rpx;
    font-size: 26rpx;
    font-family: PingFang SC;
    color: #999999;
    text-align: center;
  }
</style>
