<template>
	<view>
<list ref="listBox" :loadmoreoffset="wHeight*3" :show-scrollbar="false" :pagingEnabled="true" :scrollable="true" @scroll="touchMove" @loadmore="getMoreVideo">
	<cell v-for="(item,index) in liveList" :key="index" :data-index="index" @appear="appear" @disappear="disappear">
		<view v-if="isShow && item.liveDetail" class="live_play" :style="'height: '+ boxStyle.height +';'">
			<block v-if="!(show_live_stop||live_recording||live_ban||live_delete)">
				<view class="" v-if="item.liveDetail.liveState == 4">
					<video ref="sldVideo_child" :id="'sldVideo'+live_id" class="live_back" :src="item.liveDetail.replayUrl"
						:show-fullscreen-btn="false" object-fit="cover" :show-center-play-btn="true" :style="boxStyle"
						:enable-play-gesture="true" :initial_time="initial_time" :show-progress="true">
					</video>
				</view>
				
				<view v-if="item.liveDetail.liveState != 4">
					<video ref="sldVideo_child" :id="'sldVideo'+live_id" class="live_back" :src="item.liveDetail.pullUrl"
						:show-fullscreen-btn="false" object-fit="cover" :show-center-play-btn="false" :style="boxStyle"
						:enable-play-gesture="true" :initial_time="initial_time" :show-progress="false" controls="false">
					</video>
				</view>
			</block>

			<!-- 回放 -->
			<view class="live_header">
				<image @tap="goBack" class="live_header_go_back" :src="imgUrl+'svideo/white_arrow_l.png'"></image>
				<view class="live_header_avator" :data-authorId="item.liveDetail.authorId" @tap="goLiveUserCenter">
					<image mode="aspectFill" class="live_header_avator_img" :src="item.liveDetail.memberAvatar"/>
				</view>
				<view class="live_header_mem_info" :data-authorId="item.liveDetail.authorId" @tap="goLiveUserCenter">
					<text class="live_header_mem_info_name">{{item.liveDetail.memberNickname}}</text>
					<view class="live_header_mem_info_stat_num">
						<text v-if="item.liveDetail.isSelf == 1" class="live_header_mem_info_stat_num_click_num" style="margin-right: 20rpx">{{live_like_num}}人气</text>
						<text class="live_header_mem_info_stat_num_click_num">{{live_click_num}}{{Ls('观看')}}</text>
					</view>
				</view>
				<view v-if="!item.liveDetail.isSelf&&!item.liveDetail.isFollow" class="live_header_live_fllow" @tap="collect">
					<image class="live_header_live_fllow_image" :src="imgUrl+'svideo/fav_a.png'"></image>
					<text class="live_header_live_fllow_text">{{Ls('关注')}}</text>
				</view>
			</view>

			
		
			
			<!-- 商品，输入框，点赞按钮 -->
			<view class="live_footer" :style="item.liveDetail.liveState==4?'bottom:100rpx;':'bottom:30rpx'">
				
				<!-- 点赞动画 -->
				<view class="animate-wrap">
					<view class="a-img" v-for="(items,index) in viewList" :key="items.elId" :ref="items.elId" :style="{'right': site[0] + 'rpx','bottom': site[1] + 'rpx'}">
						<image :style="{'width': imgWidth + 'rpx','height': imgHeight + 'rpx'}" mode="widthFix" :src="items.src"></image>
					</view>
				</view>
				
				
				<view class="live_footer_con">
					<view class="live_footer_goods" @tap="showGoodsFun">
						<image class="live_footer_goods_image" :src="imgUrl+'svideo/nvbao.png'"></image>
						<text class="live_footer_goods_text">{{item.liveDetail.goodsNum * 1 > 99 ? '99+' : item.liveDetail.goodsNum }}</text>
					</view>
					
					<input type="text" name="talk_con" v-model="input_val" class="live_footer_talk_con" :placeholder="Ls('说点什么吧~')"
						:placeholder-class='live_footer_talk_con_placeholder' confirm-type="send" :disabled="liveDetail.liveState == 4"
						@confirm="publishComment" maxlength="30"></input>
					
					<view class="live_footer_share" @tap="showShare">
						<image class="live_footer_share_image" :src="imgUrl+'svideo/share.png'"></image>
						<text class="live_footer_share_text">{{Ls('分享')}}</text>
					</view>
					
					<view class="live_footer_add_heart" @tap="add_heart">
						<image class="live_footer_add_heart_image" :src="imgUrl+'svideo/add_heart.png'"></image>
						<text class="live_footer_share_text">{{live_like_num}}</text>
					</view>
				</view>
			</view>

			<view v-if="item.liveDetail.liveState == 4" class="talk_con_disabled"  @tap="notSendDamu(item)">
				<image class="talk_con_disabled_img" :src="imgUrl+'svideo/talk_disabled.png'" mode="aspectFit"></image>
			</view>

			<!-- 分享 -->
			<view class="select-wrap" catchtouchmove="touchmoveshare" v-if="shareWrap">
				<view class="select-wrap_share-mode">
					<view class="empty_158"></view>
					<view class="share-mode_share-img" @tap="prevImg">
						<image class="share-mode_share-img_poster" :src="item.shareImg" mode="widthFix"></image>
					</view>
					<view class="select-wrap_share-mode_ul">
						<view class="share-mode_item" @tap="downloadImg">
							<image class="share-mode_image" :src="imgUrl + 'svideo/hb_share.png'" mode="widthFix"></image>
							<text class="share-mode_text">{{Ls('保存海报')}}</text>
						</view>
						<view class="share-mode_item" @tap="sldShare(2,'WXSceneSession')">
							<image class="share-mode_image" :src="imgUrl + 'goods_detail/wx_share.png'" mode="widthFix"></image>
							<text class="share-mode_text">{{Ls('微信好友')}}</text>
						</view>
						<view class="share-mode_item" @tap="sldShare(2,'WXSenceTimeline')">
							<image class="share-mode_image" :src="imgUrl + 'svideo/pyq_share.png'" mode="widthFix"></image>
							<text class="share-mode_text">{{Ls('微信朋友圈')}}</text>
						</view>
					</view>
					<view class="select-wrap_close" @tap="closeShare">
						<image class="select-wrap_close_image" mode="scaleToFill" :src="imgUrl+'store/share_close2.png'"></image>
					</view>
				</view>
			</view>

			<view class="select-wrap" catchtouchmove="touchmoveshare" v-if="shareWrap2">
				<view class="select-wrap_share-mode">
					<view class="share-mode_share-img"></view>
					<view class="select-wrap_share-mode_ul">
						<button @tap.stop="sldShare(0,'WXSceneSession')" class="share-mode_item">
							<image class="share-mode_image" :src="imgUrl+'goods_detail/wx_share.png'" mode="widthFix"></image>
							<text class="share-mode_text">{{Ls('微信好友')}}</text>
						</button>
						<button @tap.stop="sldShare(0,'WXSenceTimeline')" class="share-mode_item">
							<image class="share-mode_image" :src="imgUrl+'svideo/pyq_share.png'" mode="widthFix"></image>
							<text class="share-mode_text">{{Ls('微信朋友圈')}}</text>
						</button>
						<view class="share-mode_item" @tap="createhb">
							<image class="share-mode_image" :src="imgUrl+'svideo/createhb.png'" mode="widthFix"></image>
							<text class="share-mode_text">{{Ls('保存海报')}}</text>
						</view>
					</view>
					<view class="select-wrap_close" @tap="closeShare">
						<image class="select-wrap_close_image" mode="aspectFit" :src="imgUrl+'store/share_close2.png'"></image>
					</view>
				</view>
			</view>

			<view class="wx_brower_share_mask" v-if="showWeiXinBrowerTip">
				<view class="wx_brower_share_top_wrap">
					<image :src="imgUrl+'wx_share_tip.png'" mode="widthFix" @tap="closeShare" class="wx_brower_share_img"></image>
				</view>
			</view>

			<!-- 视频绑定的商品模块 start -->
			<view class="live_bind_goods_mask" v-if="showGoods">
				<view class="live_bind_goods">
					<view class="live_bind_goods_header">
						<text class="live_bind_goods_header_text">{{Ls('全部商品')}}({{item.liveDetail.goodsNum * 1 > 99 ? '99+' : item.liveDetail.goodsNum}})</text>
						<image class="live_bind_goods_header_image" :src="imgUrl+'svideo/close.png'" @tap="closeGoods"></image>
					</view>
					<scroll-view class="scroll_goods" scroll-y="true" @scrolltolower="getMoreGoods">
						<view class="live_user_tab_content">
							<!-- 商品列表item -->
							 <block v-for="(items, indexs) in item.goodsList" :key="indexs">
								<view class="live_user_tab_content_item">
									<view class="live_user_tab_content_item_img" :data-gid="items.gid" @tap="goGoodsDetail(items)">
										<image class="live_user_tab_content_item_img_image" :src="items.goodsImage" mode="aspectFit"></image>
										<text class="live_user_tab_content_item_img_text">{{indexs+1}}</text>
									</view>
									<view class="live_user_tab_content_item_right">
										<view class="live_user_tab_content_item_right_top" :data-gid="items.gid" @tap.stop="goGoodsDetail(items)">
											<text class="live_user_tab_content_item_right_top_name">{{items.goodsName}}</text>
											<text class="live_user_tab_content_item_right_top_jingle">{{items.goodsBrief}}</text>
										</view>
										<view class="live_user_tab_content_item_right_bottom">
											<view class="live_user_tab_content_item_right_bottom_price">
												<text class="live_user_tab_content_item_right_bottom_price_num">{{Ls('¥')}}{{items.goodsPrice}}</text>
											</view>
											<view class="live_user_tab_content_item_right_bottom_click_num">
												<image class="live_user_tab_content_item_right_bottom_click_num_add_cart" :src="imgUrl+'svideo/add_cart1.png'" mode="aspectFit" :data-gid="items.gid"
												 @tap="addCart(items)"></image>
												<block>
													<image class="live_user_tab_content_item_right_bottom_click_num_image" :src="imgUrl+'live/eye.png'" mode="aspectFit"></image>
													<text class="live_user_tab_content_item_right_bottom_click_num_text">{{items.clickNum}}</text>
												</block>
											</view>
										</view>
									</view>
								</view>
								<view v-if="item.goodsList.length-indexs > 1" class="line_marginl_20"></view>
							</block>
							<!-- 数据加载完毕 -->
							<view class="no-has-more" v-if="!item.hasmore&&item.goodsList.length>0" style="background-color: transparent">
								<text class="no-has-more_text">{{Ls('数据加载完毕~')}}</text>
							</view>
							<!-- 数据加载中 -->
							<!-- <dataLoading :showFlag="hasmore&&loading" /> -->
						</view>
					</scroll-view>
				</view>
			</view>
			<!-- 视频绑定的商品模块 end -->

			<!-- 回放视频暂停/播放的弹层展示 -->
			<view class="video_control" v-if="playFlag&&showPauseBtn||!playFlag" @tap="videoPlayControl">
				<image class="video_control_image" :src="showBtnIcn"></image>
			</view>

			<!-- 弹幕 start-->
			<view class="barrage_wrap" :style="item.liveDetail.liveState==4?'bottom:200rpx;':'bottom:130rpx'">
				<view class="barrage_wrap_notice">
					<text class="barrage_wrap_notice_text" v-if="noticeList&&noticeList.length>0">{{noticeList[0].msg}}</text>
				</view>

				<scroll-view class="barrage" scroll-y :scroll-into-view="toBottom">
					<view class="gonggao" v-if="liveDetail.liveNotice">
						<text class="gonggao_title">{{Ls('公告')}} </text>
						<text class="gonggao_ba_txt">{{liveDetail.liveNotice}}</text>
					</view>
					<view v-for="(item, index) in msgList" :key="index" :index="index" v-if="msgList.length>0" :id="'item' + index" >
						<view class="barrage_item">
							<text class="barrage_item_name" :style="'color:' + item.color_random">{{item.authorName}}</text>
							<text class="barrage_item_con">{{item.msg}}</text>
						</view>
					</view>
				</scroll-view>
			</view>
			<!-- 弹幕 end-->

			<!-- 直播状态信息 start -->
			<view class="live_stop" v-if="show_live_stop||live_recording||live_ban||live_delete">
				<view class="live_stop_mask">
					<view class="live_stop_bg" :style="{backgroundImage:'url('+ item.liveDetail.liveCover +')'}"></view>
				</view>
				<!-- 直播结束 -->
				<view class="live_stop_container" v-if="show_live_stop">
					<view class="live_stop_title">
						{{live_stop_info.forbidReason?live_stop_info.forbidReason:Ls('直播已结束')}}
					</view>
					<view class="live_stop_time">
						{{Ls('直播时长：')}}{{live_stop_info.playbackTime}}
					</view>
					<view class="live_stop_like_watch">
						{{Ls('人气')}} {{live_stop_info.popularityNum * 1}}    {{Ls('观看')}} {{live_stop_info.viewingNum * 1}}
					</view>
					<view @tap="goBack" class="return_refresh">
						{{Ls('返回')}}
					</view>
				</view>
				<!-- 直播录制中 -->
				<view class="live_stop_container" v-if="live_recording">
					<view class="live_stop_title">
						{{Ls('直播已结束')}}
					</view>
					<view class="live_stop_recording">
						{{Ls('正在录制中，录制完成才可以观看')}}
					</view>
					<view class="live_stop_like_watch">
						{{Ls('获赞')}} {{item.liveDetail.live.like_num}}    {{Ls('观看')}} {{item.liveDetail.live.click_num}}
					</view>
					<view @tap="goBack" class="return_refresh">
						{{Ls('返回')}}
					</view>
				</view>
				<!-- 直播被禁止 -->
				<view class="live_stop_container" v-if="live_ban">
					<view class="live_stop_title">
						{{live_ban_info.msg}}
					</view>
					<view class="live_stop_recording">
						{{live_ban_info.remark}}
					</view>
					<view @tap="goBack" class="return_refresh">
						{{Ls('返回')}}
					</view>
				</view>
				<!-- 直播被删除 -->
				<view class="live_stop_container" v-if="live_delete">
					<view class="live_stop_title">
						{{Ls('该直播已被删除')}}
					</view>
					<view @tap="goBack" class="return_refresh">
						{{Ls('返回')}}
					</view>
				</view>
			</view>
			<!-- 直播状态信息 end -->
		</view>
	</cell>
</list>
	</view>
</template>

<script>
	import { checkPageHasMore, colorArray, initNum } from "@/utils/live";
	import request from "@/utils/request";
	import likeButton from '@/extra/component/like-button/like-button.vue'
	import io from '@hyoga/uni-socket.io';
	import { mapState, mapMutations } from 'vuex';
	import {getCurLanguage} from '@/utils/base.js'
	
	
	const likeIcon1 = getApp().globalData.imgUrl + 'svideo/1.png'
	const likeIcon2 = getApp().globalData.imgUrl + 'svideo/2.png'
	
	
	export default {
		components: {
			likeButton,
		},
		data() {
			return {
				isShow: false,
				key: '',
				pageSize: 10, //商品每页数量
				pn: 1, //商品的当前页
				loading: false,
				//数据加载状态
				liveDetail: '',
				//直播详情
				settingData: '',
				//平台设置信息
				imgUrl: getApp().globalData.imgUrl,
				//图片地址
				bgStyle: 'background-size:contain;background-position:center center;background-repeat: no-repeat;',
				//背景图片样式
				shareWrap: false,
				// 展示分享弹层
				shareWrap2: false,
				// 展示分享弹层
				shareImg: '',
				// 分享图片
				goodsList: [],
				//直播绑定的商品
				hasmore: true,
				//是否还有数据，用于页面展示
				showGoods: false,
				//是否显示商品弹层
				playFlag: true,
				//视频是否播放
				showPauseBtn: false,
				//是否显示暂停按钮
				showBtnIcn: '',
				//视频播放控制层按钮图片
				colorArray: colorArray,
				//颜色数组
				msgList: [],
				//弹幕内容
				noticeList: [],
				//消息通知列表
				input_val: '',
				//输入框内容
				toBottom: '',
				//弹幕滚动到指定的元素
				initial_time: 1,
				// 指定视频初始播放位置
				live_like_num: '--',
				//人气数
				live_click_num: '--', //观看数
				liveStateInfo: "",
				isWeiXinBrower: false, //是否微信浏览器
				showWeiXinBrowerTip: false, //微信浏览器分享的提示操作
				stat_end:0,  //终端，默认为1，pc端
				show_live_stop:false,//是否显示直播结束提示
				live_stop_info:{},//显示直播结束提示信息
				live_recording:false,//是否显示直播录制中提示
				live_ban:false,//直播是否被禁止
				live_ban_info:{
					msg:'',
					remark:''
				},
				live_delete: false,
				wHeight: 0,
				boxStyle: {
					width: '750rpx',
					height: '750rpx'
				},

				live_id: '', //当前直播id
				label_id: '', //当前直播标签

				liveList: [],
				liveIndex: 0, //当前视频下标
				appearOld: 0, //切换时当前视频下标
				appearNew: 0, //切换后视频下标
				livePn: 1,
				liveHasmore: true,
				scrollTop:2000,
				Ls:getCurLanguage, //语言包
				
				//点赞效果
				throttle:100,
				large:1,
				site:[45, 0],
				high:220,
				viewList:[],
				elId: 0, // 元素渲染id
				oldTime: 0, // 全局时间用于函数节流
				timer: null, // 定时器
				waitDeleteIndex: 0,
				showImgs:[likeIcon1,likeIcon2],
				imgWidth:52,
				imgHeight:52,
				duration:3000,
				range:50,
				alone:true,
			};
		},
		props: {},
		computed: {
			...mapState(['hasLogin', 'cartData', 'userInfo'])
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		beforeMount() {
			//从缓存获取
			this.live_id = uni.getStorageSync('slive_id');
			this.label_id = uni.getStorageSync('slabel_id');
		},
		mounted: function(options) {
			let that = this;
			this.wHeight = uni.getSystemInfoSync().screenHeight;//获取屏幕高度
			this.boxStyle.height = uni.getSystemInfoSync().windowHeight+'px';//获取屏幕高度
			console.log(uni.getSystemInfoSync())
			this.key = uni.getStorageSync('token');
			this.getPlatform();

			this.liveList.push({
				live_id: this.live_id,
				liveDetail: {},
				goodsList: {},
				hasmore: true,
				shareImg: getApp().globalData.apiUrl + 'v3/video/front/video/live/share/shareImage?liveId=' + this.live_id + '&source=' + this.stat_end
			})
			this.initData(this.live_id,0); //获取第一个视频的详情
			this.getVideoList(); //获取第一组视频列表数据
		},

		/**
		 * 生命周期函数--监听页面卸载
		 */
		destroyed() {
			if (this.socket) {
				this.closeSocket();
			}
			if (this.videoContext && this.videoContext.stop != undefined) {
				this.videoContext.stop();
			}
			uni.removeStorageSync('slive_id');
			uni.removeStorageSync('slabel_id');
		},

		methods: {
			//禁止subnvue点击消失
			subforbidden() {
				// 暂停/播放
				uni.$emit('tapVideo')
			},

			//获取当前终端的方法
			getPlatform(){
				if (uni.getDeviceInfo().platform == 'ios') {
					this.stat_end = 4;
				} else if (uni.getDeviceInfo().platform == 'android') {
					this.stat_end = 5;
				}
			},

			//滑动屏幕时关闭评论弹窗
			touchMove(){
				if(this.showGoods){
					this.closeGoods();
				}
			},
			//开始滑动屏幕
			appear(e) {
				this.appearNew = e.currentTarget.attr.dataIndex
			},
			//结束滑动屏幕
			disappear(e) {
				this.appearOld = e.currentTarget.attr.dataIndex
				if(this.appearOld != this.appearNew){
					this.liveIndex = this.appearNew
					this.$refs.sldVideo_child[this.appearOld].pause();
					this.$refs.sldVideo_child[this.appearNew].play();
				} else {
					// console.log('未切换')
				}
			},
			//获取更多短视频列表数据
			getMoreVideo() {
				if(this.liveHasmore){
					this.getVideoList();
				}
			},

			// 获取直播视频列表数据
			getVideoList() {
				let param = {}
				param.url = 'v3/video/front/video/live/liveList'
				param.method = 'GET'
				param.data = {}
				param.data.liveId = this.live_id
				param.data.labelId = this.label_id
				param.data.current = this.livePn
				param.data.pageSize = 3
				request(param).then(res => {
					if (res.state == 200) {
						if(res.data.list.length > 0){
							let arr = [];
							res.data.list.forEach(item=>{
								arr.push({
									live_id: item.liveId,
									liveDetail: {},
									goodsList: {},
									hasmore: true,
									shareImg: getApp().globalData.apiUrl + 'v3/video/front/video/live/share/shareImage?liveId=' + item.liveId + '&source=' + this.stat_end
								})
							})
							let len = this.liveList.length;
							this.liveList = this.liveList.concat(arr);
							res.data.list.forEach((item,index)=>{
								this.initData(item.liveId,len+index)
							})
						}
						this.liveHasmore = checkPageHasMore(res.data.pagination);//是否还有数据
						if(this.liveHasmore){
							this.livePn++;
						}
					}
				})
			},
			//获取直播详情和商品
			initData(id,index) {
				this.show_live_stop = false;
				this.getLiveInfo(id,index);
				this.getLiveGoods(id,index);
			},
			//获取直播详情
			getLiveInfo(live_id,index) {
				let liveDetail = this.liveList[index].liveDetail;
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/live/playingPage'
				param.method = 'GET'
				param.data.liveId = live_id
				request(param).then(res => {
					if (res.state == 200) {
						liveDetail = res.data;
			            if (liveDetail.liveState == 5) {
							//视频被删除
							this.live_delete = true;
						}
						if (liveDetail.playState == 2) {
							//禁止播放
							this.live_ban = true;
							this.live_ban_info = {
								msg: this.Ls('该直播已被禁止'),
								remark: liveDetail.forbidReason
							}
						} else if (liveDetail.liveState == 3) {
							//录制中
							this.live_recording = true;
						}
						liveDetail.replayUrl = liveDetail.replayUrl.split(',')[0];
						this.liveList[index].liveDetail = liveDetail;

						if(index == 0){
							setTimeout(()=>{
								this.$refs.sldVideo_child[0]&&this.$refs.sldVideo_child[0].play();
							}, 200)
						}

						this.isShow = true;
						if (this.socket) {
							//无操作
						} else {
							this.initSocket();
						}
					} else {
						//禁止播放
						this.live_ban = true;
						this.live_ban_info = {
							msg: this.Ls('该直播已被禁止'),
							remark: liveDetail.forbidReason ? liveDetail.forbidReason : ''
						}
					}
				})
			},
			//获取直播商品
			getLiveGoods(live_id,index) {
				this.loading = true;
				let goodsList = this.liveList[index].goodsList;
				let hasmore = this.liveList[index].hasmore;
				let param = {}
				param.data = {}
				param.data.liveId = live_id
				param.data.current = this.pn
				param.data.pageSize = this.pageSize
				param.url = 'v3/video/front/video/live/goodsList'
				param.method = 'GET'
				request(param).then(res => {
					if (res.state == 200) {
						let list = res.data.list;
						if (this.pn == 1) {
							goodsList = list;
						} else {
							goodsList = goodsList.concat(list);
						}
						if (checkPageHasMore(res.data.pagination)) {
							this.pn++;
						} else {
							hasmore = false;
						}
					}
					this.loading = false;
					this.liveList[index].goodsList = goodsList;
					this.liveList[index].hasmore = hasmore;
				})
			},

			returnShow() {
				let videoContext = uni.createVideoContext("sldVideo");
				if (videoContext) {
					videoContext.stop(); //停止播放视频
				}
				if (videoContext) {
					setTimeout(() => {
						//300毫秒延迟
						videoContext.seek(this.initial_time);
						videoContext.play(); //开始播放视频
					}, 300);
				}
			},
			
			notSendDamu(item){
				if(item.liveDetail.liveState == 4){
					uni.showToast({
						title:this.Ls('录播不能发送消息'),
						icon:'none'
					})
				}
			},

			//关闭分享
			closeShare() {
				this.shareWrap = false;
				this.shareWrap2 = false;
				this.showWeiXinBrowerTip = false; //微信浏览器提示层
			},
			//分享 type：分享类型 0 图文 2图片，scene 场景 WXSceneSession：分享朋友  WXSenceTimeline：分享朋友圈
			sldShare(type, scene) {
				let live_id = this.liveList[this.liveIndex].live_id;
				let liveDetail = this.liveList[this.liveIndex].liveDetail;
				let shareImg = this.liveList[this.liveIndex].shareImg;
				let shareData = {};
				if (type == 0) {
					shareData.href = getApp().globalData.apiUrl+'extra/live/livePlay?live_id=' + live_id;
					shareData.title = this.Ls('我正在看') + liveDetail.memberNickname ? liveDetail.memberNickname : liveDetail.memberName + this.Ls('的精彩内容，快来围观~');
					shareData.summary = liveDetail.liveName;
					shareData.imageUrl = liveDetail.liveCover;
				} else if (type == 2) {
					shareData.imageUrl = shareImg;
				}
				this.weiXinAppShare(type, scene, shareData);
				this.closeShare(); //关闭分享
			},
			//微信分享
			weiXinAppShare(type, scene, shareData) {
				if(type == 0){
					//分享图文
					uni.share({
					    provider: "weixin",
					    scene: scene,
					    type: type,//0为图文
					    href: shareData.href,
					    title: shareData.title,
					    summary: shareData.summary,
					    imageUrl: shareData.imageUrl,//图片,图片过大的话不展示，建议小于20kb
					    success: function (res) {},
					    fail: function (err) {}
					});
				}else if(type == 2){
					//分享图片
					uni.share({
					    provider: "weixin",
					    scene: scene,
					    type: type,//2为图片
					    imageUrl: shareData.imageUrl,//图片,图片过大的话不展示，建议小于20kb
					    success: function (res) {},
					    fail: function (err) {}
					});
				}
			},

			prevImg() {
				wx.previewImage({
					urls: [this.liveList[this.liveIndex].shareImg],
				})
			},
			touchmoveshare() {
				return false;
			},

			initSocket() {
				if (this.socket) {
					this.closeSocket();
				}

				const {
					live_id,
					liveDetail
				} = this;
				const authorInfo = liveDetail.author;
				let userInfo = {
					live_id: live_id ? live_id : 1
				};
				if (authorInfo && authorInfo.authorId) {
					userInfo.author_name = authorInfo.memberNickname ? authorInfo.memberNickname : authorInfo.memberName;
					userInfo.author_id = authorInfo.authorId;
				}else{
					userInfo.author_name = this.Ls('游客');
					userInfo.author_id = 0;
				}
				this.socket = io(getApp().globalData.socketUrl, {
					reconnection: true,
					jsonp: true,
					transports: ['websocket', 'polling'],
					timeout: 5000,
					query: `author_name=${userInfo.author_name}&author_id=${userInfo.author_id}&live_id=${userInfo.live_id}&is_own=${liveDetail.isSelf?1:0}`
				});

				this.socket.on("connect", () => {
					//给服务端发送消息
					this.socket.emit("update_user", userInfo);

					this.socket.on("get_msg", e => {
						if (typeof e.msg === 'string') {
							this.socketHandle('wec', e);
						} else {
							this.socketHandle('msg', e.msg);
						}
					}); //获取服务端新人信息

					this.socket.on("get_new_user", e => {
						this.socketHandle2('new', e);
					});
					this.socket.on("disconnect", function() {});
				}); //获取关注的返回信息

				this.socket.on("get_follow_msg", e => {
					this.socketHandle2('follow', e);
				}); //获取直播点击数

				this.socket.on("get_click_num", e => {
					this.clickNumHandle(e);
				}); //获取直播人气数

				this.socket.on("get_like_num", e => {
					this.likeNumHandle(e);
				}); // 直播结束

				this.socket.on('stop_live', e => {
					this.handleStopLive(e);
				});
			},

			//关闭socket
			closeSocket() {
				if (this.socket) {
					this.socket.close();
				}
			},

			socketHandle(type, msg) {
				let {
					msgList
				} = this;

				if (msg.type) {
					return;
				}

				msg.type = type;
				msg.color_random = colorArray[Math.floor(Math.random() * 8)];
				msgList.push(msg);
				this.msgList = msgList;


				let randomNum = (this.msgList.length - 1 + Math.random() * 1).toFixed(2)
				let randonNumText = randomNum.toString().replace('.', '')
				this.toBottom = `item${randonNumText}`
				this.$nextTick(() => {
					this.toBottom = this.msgList.length ? `item${this.msgList.length - 1}` : 'item0'
				})
				// this.scrollTop = Math.floor(Math.random() * 4000)
			},

			//直播结束
			handleStopLive(e) {
				this.liveStateInfo = JSON.parse(e);
				this.show_live_stop = true;
				this.live_stop_info = JSON.parse(e);
			},

			//获取服务端新人信息
			socketHandle2(type, msg) {
				let {
					noticeList
				} = this;

				if (msg.type) {
					return;
				}

				if (noticeList.filter(el => el.type === 'follow').length > 0 && type === 'follow') {
					return;
				}

				msg.type = type;
				msg.timer = setTimeout(() => {
					this.noticeRemoveItem(type);
				}, 2000);

				if (type === 'follow') {
					noticeList.push(msg);
				} else {
					let index = noticeList.findIndex(el => el.type === 'new');

					if (index > -1) {
						noticeList[index] = msg;
					} else {
						noticeList.push(msg);
					}
				}
				this.noticeList = noticeList;
			},

			noticeRemoveItem(type) {
				let {
					noticeList
				} = this;
				let index = noticeList.findIndex(el => el.type === type);
				if (index > -1) {
					clearTimeout(noticeList[index].timer);
					noticeList.splice(index, 1);
				}
				this.noticeList = noticeList;
			},

			// 点击量处理
			clickNumHandle(e) {
				let liveDetail = this.liveList[this.liveIndex].liveDetail;
				let {
					live_click_num
				} = this;
				liveDetail.clickNum = e.click_num;
				this.liveList[this.liveIndex].liveDetail = liveDetail;
				this.live_click_num = initNum(e.click_num);
			},

			// 人气
			likeNumHandle(e) {
				let liveDetail = this.liveList[this.liveIndex].liveDetail;
				let {
					live_like_num
				} = this;
				liveDetail.likeNum = e.like_num;
				this.liveList[this.liveIndex].liveDetail = liveDetail;
				this.live_like_num = initNum(e.like_num);
			},

			// 发送消息
			publishComment(e) {
				let live_id = this.liveList[this.liveIndex].live_id;
				let liveDetail = this.liveList[this.liveIndex].liveDetail;
				if (this.hasLogin && liveDetail.authorId != null) {
					let content = e.detail.value; //没有内容的话直接结束
					if (!content.trim()) {
						return false;
					}
					let msg = {
						author_id: liveDetail.author.authorId,
						author_name: liveDetail.author.memberNickname,
						live_id: live_id,
						msg: content
					};
					this.socket.emit("send_msg", msg);
					//清空输入框的内容
					this.input_val = ''
				} else {
					let routes = getCurrentPages(); // 获取当前打开过的页面路由数组
					let path = routes[routes.length - 1].route //获取当前页面路由
					let query = routes[routes.length - 1].options; //获取路由参数
					getApp().globalData.goLogin({path,query});
				}
			},

			//增加人气事件
			add_heart(e) {
				if(this.live_ban){
					uni.showToast({
						title: this.live_ban_info.msg,
						icon: 'none'
					})
					return;
				}
				let live_id = this.liveList[this.liveIndex].live_id;
				let liveDetail = this.liveList[this.liveIndex].liveDetail;
				if (this.hasLogin) {
					this.socket.emit("update_like_num", {
						author_id: liveDetail.authorId,
						live_id
					});
					this.handleClick(e)
				} else {
					let routes = getCurrentPages(); // 获取当前打开过的页面路由数组
					let path = routes[routes.length - 1].route //获取当前页面路由
					let query = routes[routes.length - 1].options; //获取路由参数
					getApp().globalData.goLogin({path,query});
				}
			},

			// 返回上级页面
			goBack() {
				uni.$emit('goBack');
			},

			//关注、取消关注事件
			collect(e) {
				let liveDetail = this.liveList[this.liveIndex].liveDetail
				if (this.hasLogin) {
					let param = {}
					param.data = {}
					param.method = 'POST'
					param.data.authorId = liveDetail.authorId
					if (liveDetail.isFollow) {
						param.url = 'v3/video/front/video/cancelFollow'
					} else {
						param.url = 'v3/video/front/video/followAuthor'
					}
					request(param).then(res => {
						if (res.state == 200) {
							this.liveList[this.liveIndex].liveDetail.isFollow = !(this.liveList[this.liveIndex].liveDetail.isFollow)
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							});
						}
					})
				} else {
					getApp().globalData.goLogin();
				}
			},

			//进入作者页面
			goLiveUserCenter(e) {
				let author_id = this.liveList[this.liveIndex].liveDetail.authorId;
				let page = getCurrentPages();
				let len = page.length;
				uni.$emit('goLiveUserCenter', {
					pageLength: len,
					url: '/extra/svideo/myVideo?author_id=' + author_id
				});
			},

			//分享点击事件
			showShare() {
				if(this.live_ban){
					uni.showToast({
						title: this.live_ban_info.msg,
						icon: 'none'
					})
					return;
				}
				this.shareWrap2 = true;
			},
			//生成海报事件
			createhb() {
				this.shareWrap = true;
				this.shareWrap2 = false;
			},
			//下载海报
			downloadImg() {
				let _this = this;
				uni.downloadFile({
					url: _this.liveList[_this.liveIndex].shareImg,
					success: res_info => {
						if (res_info.statusCode == 200) {
							_this.saveHb(res_info.tempFilePath);
						} else {
							uni.showToast({
								title: _this.Ls('下载失败'),
								icon: 'none'
							});
						}
					}
				});
			},
			//保存图片
			saveHb: function(img) {
				let _this = this;
				uni.saveImageToPhotosAlbum({
					filePath: img,
					success: function(data) {
						_this.shareWrap = false;
						_this.shareWrap2 = false;
						uni.showToast({
							title: _this.Ls('已保存到本地'),
							icon: 'success',
							duration: 2000
						});
					},
					complete: function(res) {}
				});
			},

			//跳转商品详情页
			goGoodsDetail(good) {
				let page = getCurrentPages();
				let len = page.length;
				uni.$emit('goGoodsDetail',{pageLength:len,url:'/standard/product/detail?productId=' + good.productId + '&goodsId='+ good.goodsId});
			},

			//加入购物车事件
			addCart(good) {
				if(this.live_ban){
					uni.showToast({
						title: this.live_ban_info.msg,
						icon: 'none'
					})
					return;
				}
				let that = this
				if (!this.hasLogin) {
					let cart_list = {
						storeCartGroupList: [{
							promotionCartGroupList: [{
								cartList: [{
									buyNum: 1,
									goodsId: good.goodsId - 0,
									productId: good.defaultProductId ? good.defaultProductId : good.productId,
									productImage: good.goodsImage,
									goodsName: good.goodsName,
									isChecked: 1,
									productPrice: good.goodsPrice,
									productStock: good.goodsStock
								}],
							}],
							storeId: good.storeId,
							storeName: good.storeName,
							checkedAll: true
						}],
						checkedAll: true,
						invalidList: []
					}
					//未登录加入本地缓存
					let local_cart_list = uni.getStorageSync('cart_list') //购物车列表本地缓存
					if (local_cart_list) { //如果不是第一次存储
						let tmp_list1 = []
						let tmp_list2 = []
						cart_list.storeCartGroupList.forEach(item => {
							item.promotionCartGroupList.forEach(item1 => {
								item1.cartList.forEach(item2 => {
									local_cart_list.storeCartGroupList.forEach(v => {
										v.promotionCartGroupList.forEach(v1 => {
											v1.cartList.forEach(v2 => {
												if (v2.productId == item2.productId && v.storeId == item.storeId) {
													tmp_list1.push(v)
												}
											})
										})
									})
									tmp_list2 = local_cart_list.storeCartGroupList.filter(v => {
										return v.storeId == item.storeId
									})
								})
							})
						})
						if (tmp_list1.length > 0 && tmp_list2.length > 0) { //同一店铺同一商品
							local_cart_list.storeCartGroupList.map(item => {
								item.promotionCartGroupList.map(item1 => {
									item1.cartList.map(item2 => {
										if (item2.productId == this.productId && this.storeInf&&(item.storeId == this.storeInf.storeId)) {
											item2.buyNum += this.currentSpecNum
										}
									})
								})
							})
						} else if (tmp_list1.length == 0 && tmp_list2.length > 0) { //同一店铺不同商品
							local_cart_list.storeCartGroupList.map(item => {
								if (this.storeInf&&(item.storeId == this.storeInf.storeId)) {
									item.promotionCartGroupList.map(item2 => {
										item2.cartList.push(cart_list.storeCartGroupList[0].promotionCartGroupList[0].cartList[0])
									})
								}
							})
						} else { //不同店铺不同商品
							local_cart_list.storeCartGroupList.push(cart_list.storeCartGroupList[0])
						}
						// 未登录购物车展示数据
						uni.setStorage({
							key: 'cart_list',
							data: local_cart_list,
							success: function() {
								//更新购物车数量和购物车数据
							}
						});

					} else {
						uni.setStorage({
							key: 'cart_list',
							data: cart_list,
							success: function() {
								//更新购物车数量和购物车数据
							}
						});
					}
					uni.showToast({
						title: that.Ls('加入购物车成功！'),
						icon: 'none',
						duration: 700
					})
				} else { //已登录
					request({
						url: 'v3/business/front/cart/add',
						data: {
							productId: good.defaultProductId ? good.defaultProductId : good.productId,
							number: 1,
						},
						method: 'POST'
					}).then(res => {
						if (res.state == 200) {
							//更新购物车数量
							// this.$api.msg(res.msg);
							uni.showToast({
								title: that.Ls('加入购物车成功！'),
								icon: 'none'
							})
						} else {
							// this.$api.msg(res.msg);
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}).catch((e) => {})
				}
			},

			//打开商品弹层
			showGoodsFun() {
				let liveDetail = this.liveList[this.liveIndex].liveDetail;
				let { showGoods } = this;
				if (liveDetail.goodsNum * 1 > 0) {
					this.showGoods = true;
				}
			},
			//关闭商品弹层
			closeGoods() {
				let { showGoods } = this;
				this.showGoods = false;
			},
			//绑定的商品分页事件
			getMoreGoods() {
				let index = this.liveIndex;
				let id = this.liveList[this.liveIndex].live_id;
				let hasmore = this.liveList[this.liveIndex].hasmore;
				if (hasmore) {
					this.getLiveGoods(id,index);
				}
			},

			//视频暂停/继续播放事件
			videoPlayControl() {
				let {
					playFlag,
					showPauseBtn
				} = this;
				let videoContext = uni.createVideoContext("sldVideo");

				if (playFlag) {
					if (!showPauseBtn) {
						this.showPauseBtn = true;
						this.showBtnIcn = this.pauseBtnIcon;

						 //3s后自动消失
						setTimeout(() => {
							this.showPauseBtn = false;
						}, 3000);
					} else {
						videoContext.pause(); //暂停播放

						this.showPauseBtn = false;
						this.playFlag = false;
						this.showBtnIcn = this.playBtnIcon;
					}
				} else {
					videoContext.play(); //开始播放

					this.playFlag = true;
					this.showPauseBtn = false;
				}
			},
			
			
			//点赞动画事件
			handleClick(e) {
				// 函数节流
				let timeStamp = e.timeStamp
				let interval = timeStamp - this.oldTime
				if (interval < this.throttle) return null;
				this.oldTime = timeStamp
				let animation = {}
				// 创建animate配置
				animation = uni.requireNativePlugin('animation')
				let randomImg = Math.floor(Math.random() * this.showImgs.length)
				let _item = {
					elId: this.elId, // 生成元素ref
					src: this.showImgs[randomImg], // 随机图标
					animation: animation, // 每个盒子动画
					x: Math.ceil(Math.random() * this.range), // 方向间距
					q: Math.floor(Math.random() * 2), // 随机方向
				}
				// 动画
				let _abs = ['-', '']
				let _dirX = Number(_abs[_item.q] + _item.x) // 随机的方向和间距
				let _dirY = this.high - Math.random() * 10
				// 生成DOM
				this.elId++
				this.viewList.push(_item)
				// 执行动画
				setTimeout(() => {
					this.$nextTick(() => {
						let _n = 1
						if (this.large) _n = typeof(this.large) === 'number' ? this.large : 2;
						let el = this.$refs[_item.elId][0];
						clearTimeout(this.timer)
						_item.animation.transition(el, {
							styles: {
								transform: `translate(${_dirX}rpx, -${_dirY}rpx) scale(${_n}, ${_n}])`,
								transformOrigin: 'center center',
								opacity: 0
							},
							duration: this.duration, // ms
							timingFunction: 'ease-out',
							delay: 100 // ms
						}, () => {
							console.log('animation finished.')
							// 完成后事件回调
							// 逐渐消失
							if (this.alone) {
 								this.waitDeleteIndex++
								this.deleteView()
								return null
							}
						})
					})
				}, 100)
				// 点击立即触发组件事件
			},
			deleteView() {
				this.viewList.splice(0, this.waitDeleteIndex)
				this.waitDeleteIndex = 0
			},
			
		}
	};
</script>
<style>
	page {
		width: 750rpx;
		overflow-x: hidden;
		overflow: hidden;
	}

	.live_fixed_bg {
		width: 750rpx;
		position: fixed;
		left: 0;
		right: 0;
		top: 0;
		bottom:0;
		z-index:1;
	}

	.no-has-more {
	  display: flex;
	  align-items: center;
	  justify-content: center;
	  position: relative;
	  height: 80rpx;
	  text-align: center;
	  line-height: 80rpx;
	  color: #949494;
	  font-size: 22rpx;
	}
	.no-has-more_text {
	  line-height: 80rpx;
	  color: #949494;
	  font-size: 22rpx;
	}

	.live_play{
		position: relative;
		background-color: transparent;
		width: 750rpx;
		overflow: hidden;
	}

	.live_back,
	.living {
		width: 750rpx;
		/* height: 600rpx; */
		height: 100vh;
		overflow: hidden;
	}
	/* app-1-start */
	/* #ifdef APP-PLUS */
	.live_back{
		width: 100%;
		height: 100vh;
		overflow: hidden;
	}
	/* #endif */
	/* app-1-end */
	.live_header {
		position: absolute;
		top: 80rpx;
		left: 20rpx;
		z-index: 2;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: flex-start;
		width: 750rpx;
		height: 80rpx;
		overflow-x: hidden;
	}

	.live_header_go_back {
		width: 45rpx;
		height: 47rpx;
	}

	.live_header_avator {
		width: 80rpx;
		height: 80rpx;
		border-radius: 40rpx;
		border-width: 2rpx;
		border-style: solid;
		border-color: #fff;
		margin-left: 8rpx;
		overflow: hidden;
	}

	.live_header_avator_img{
		width: 76rpx;
		height: 76rpx;
		border-radius: 38rpx;
		overflow: hidden;
	}

	.live_header_mem_info {
		margin-left: 20rpx;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-content: space-between;
	}

	.live_header_mem_info_name {
		display: -webkit-box;
		max-width: 250rpx;
		line-height: 32rpx;
		color: #fff;
		font-size: 28rpx;
		font-weight: 600;
		overflow: hidden;
		white-space: nowrap;
		lines: 1;
		text-overflow: ellipsis;
		margin-bottom: 15rpx;
	}

	.live_header_mem_info_stat_num {
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
	}

	.live_header_mem_info_stat_num_click_num {
		color: #fff;
		font-size: 22rpx;
		line-height: 36rpx;
		white-space: nowrap;
		font-weight: 600;
	}

	.live_header_live_fllow {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		width: 120rpx;
		height: 50rpx;
		background: #fc1c1c;
		border-radius: 25rpx;
		margin-left: 25rpx;
	}

	.already{
		background:#999 !important;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		width: 126rpx;
		height: 50rpx;
		border-radius: 25rpx;
		margin-left: 25rpx;
	}

	.live_header_live_fllow_image {
		width: 46rpx;
		height: 46rpx;
	}

	.live_header_live_fllow_text {
		color: #fff;
		font-size: 24rpx;
		margin-right: 8rpx;
	}

	.live_footer {
		position: absolute;
		left: 0;
		right: 0;
		bottom: calc(30rpx + constant(safe-area-inset-bottom));
		z-index: 10;
		
	}
	
	.live_footer_con{
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: flex-start;
		overflow-x: hidden;
		width: 750rpx;
		height: 130rpx;
		margin: 0 auto;
		padding-top: 30rpx;
		padding-left: 40rpx;
	}

	.live_footer_goods {
		width: 85rpx;
		height: 92rpx;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		position: relative;
	}

	.live_footer_goods_image {
		width: 85rpx;
		height: 92rpx;
	}

	.live_footer_goods_text {
		position: absolute;
		left: 36rpx;
		top: 54rpx;
		line-height: 23rpx;
		color: #fff;
		font-size: 22rpx;
	}

	.live_footer_talk_con {
		width: 365rpx;
		height: 65rpx;
		color: #fff;
		font-size: 26rpx;
		font-weight: 600;
		border-radius: 30rpx;
		background-color: rgba(0, 0, 0, 0.3);
		margin-left: 30rpx;
		padding: 0 20rpx;
	}

	.talk_con_disabled {
		display: flex;
		align-items: center;
		position: absolute;
		left: 160rpx;
		bottom: 118rpx;
		z-index: 9;
		width: 365rpx;
		height: 65rpx;
		border-radius: 30rpx;
	}

	.talk_con_disabled_img {
		width: 474rpx;
		height: 70rpx;
		opacity: 0.5;
	}

	.live_footer_talk_con_placeholder{
		color: #fff;
		font-size: 24rpx;
		font-weight: 600;
	}

	.live_footer_share {
		position: relative;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: center;
		position: relative;
		z-index: 99;
		width: 77rpx;
		margin-left: 21rpx;
	}

	.live_footer_share_image,.live_footer_add_heart_image {
		width: 65rpx;
		height: 65rpx;
		font-weight: 600;
	}

	.live_footer_share_text {
		color: #fff;
		font-size: 22rpx;
		margin-top: 10rpx;
	}

	/* .live_footer_add_heart_image:active {
		-webkit-animation: 1s seconddiv;
	}

	@keyframes seconddiv {
		0% {
			transform: scale(1.3, 1.3);
		}

		50% {
			transform: scale(1.2, 1.2);
		}

		100% {
			transform: scale(1, 1);
		}
	} */

	.select-wrap {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		margin: 0 auto;
		width: 750rpx;
		background-color: rgba(0, 0, 0, 0.45);
		z-index: 9999;
	}

	.select-wrap_share-mode {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		width: 750rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.share-mode_share-img {
		width: 540rpx;
		border-radius: 20rpx;
		overflow: hidden;
	}

	.share-mode_share-img_poster{
		width: 540rpx;
	}

	.select-wrap_share-mode_ul {
		display: flex;
		flex-direction: row;
		align-items: center;
		width: 750rpx;
		padding-left: 30px;
		padding-right: 30px;
		justify-content: space-around;
	}

	.share-mode {
		display: flex;
		flex-direction: column;
		align-items: center;
		text-align: center;
		cursor: pointer;
		border: none;
		margin: 0;
		padding: 0;
		line-height: 1;
		background-color: transparent;
	}

	.share-mode_item {
		display: flex;
		flex-direction: column;
		align-items: center;
		text-align: center;
		cursor: pointer;
		border: none;
		margin: 0;
		padding: 0;
		line-height: 1;
		background-color: transparent;
		border-width: 0;
	}

	.share-mode_item::after {
		border: none;
	}

	.share-mode_image {
		border-radius: 20rpx;
		width: 106rpx;
		height: 0;
	}

	.share-mode_text {
		color: #fff;
		font-size: 24rpx;
		margin-top: 30rpx;
	}

	.select-wrap_close {
		width: 750rpx;
		height: 120rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		margin-bottom: 80rpx;
	}

	.select-wrap_close_image {
		width: 32rpx;
		height: 32rpx;
	}

	.share-mode_share-img {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.video_control {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		top: 0;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: flex-start;
		z-index: 5;
	}

	.video_control_image {
		width: 102rpx;
		height: 102rpx;
		margin-top: calc(100vh/3);
	}

	.empty_158 {
		width: 750rpx;
		overflow-x: hidden;
		height: 158rpx;
	}

	/***** 弹幕 start *****/

	.barrage_wrap {
		position: absolute;
		bottom: 100rpx;
		left: 30rpx;
		z-index: 99;
		max-height: 500rpx;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-content: flex-end;
	}

	.barrage_wrap_notice {
		display: inline-block;
		max-width: 500rpx;
		height: 50rpx;
		line-height: 50rpx;
		border-radius: 10rpx;
		margin-bottom: 10rpx;
	}

	.barrage_wrap_notice_text {
		display: inline-block;
		max-width: 500rpx;
		height: 50rpx;
		color: #fff;
		font-size: 24rpx;
		font-weight: 600;
		background-color: #fc1c1c;
		border-radius: 10rpx;
		line-height: 50rpx;
		margin-bottom: 10rpx;
		padding: 0 15rpx;
	}

	.barrage {
		min-height: 100rpx;
		max-height: 450rpx;
	}

	.gonggao {
		background-color: rgba(0, 0, 0, 0.3);
		white-space: normal;
		padding: 12rpx 16rpx;
		border-radius: 10rpx;
		word-break: break-all;
		font-weight: 600;
		width: 500rpx;
	}

	.gonggao_title {
		color: #fc1c1c;
		font-size: 26rpx;
		line-height: 36rpx;
		margin-right: 6rpx;
	}

	.gonggao_ba_txt {
		color: #fff;
		font-size: 26rpx;
		line-height: 36rpx;
	}

	.barrage_item {
		background-color: rgba(0, 0, 0, 0.3);
		max-width: 500rpx;
		border-radius: 23rpx;
		padding: 0rpx 15rpx 10rpx 15rpx;
		margin-top: 10rpx;
		white-space: normal;
		word-break: break-all;
		display: inline-block;
		font-weight: 600;
		display: flex;
		flex-direction: row;
	}

	.barrage_item_name {
		font-size: 25rpx;
		line-height: 36rpx;
		margin-right: 15rpx;
	}

	.barrage_item_con {
		color: #fff;
		font-size: 25rpx;
	}

	/***** 弹幕 end *****/

	.canvas {
		background: transparent;
		width: 180rpx;
		height: 400rpx;
		position: fixed;
		right: -20rpx;
		bottom: 130rpx;
	}

	.live_footer_add_heart {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: center;
		width: 77rpx;
		margin-left: 21rpx;
	}

	/* 直播间商品样式-start */
	/* 视频绑定的商品模块 start */

	.live_bind_goods_mask {
		width: 750rpx;
		position: fixed;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		z-index: 100;
		background: rgba(0, 0, 0, 0.45);
	}

	.live_bind_goods {
		width: 750rpx;
		height: 850rpx;
		position: fixed;
		left: 0;
		bottom: 0;
		z-index: 4;
		border-radius: 15rpx 15rpx 0 0;
		background-color: #f8f8f8;
		right: 0;
		margin: 0 auto;
	}

	.live_bind_goods_header {
		width: 750rpx;
		height: 100rpx;
		padding: 0 20rpx;
		background-color: #fff;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		border-top-left-radius: 15rpx;
		border-top-right-radius: 15rpx;
		border-bottom-right-radius: 0;
		border-bottom-left-radius: 0;
	}

	.live_bind_goods_header_text {
		color: #2d2d2d;
		font-size: 32rpx;
	}

	.live_bind_goods_header_image {
		width: 47rpx;
		height: 47rpx;
	}

	.scroll_goods {
		height: 750rpx;
	}

	/* 视频绑定的商品模块 end */
	.live_user_tab_content_item {
		width: 750rpx;
		padding: 20rpx;
		border-top-left-radius: 14rpx;
		border-top-right-radius: 14rpx;
		border-bottom-right-radius: 14rpx;
		border-bottom-left-radius: 14rpx;
		height: 286rpx;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
	}

	.live_user_tab_content_item_img {
		width: 246rpx;
		height: 246rpx;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		position: relative;
	}

	.live_user_tab_content_item_img_image {
		height: 246rpx;
		width: 246rpx;
		border-top-left-radius: 15rpx;
		border-top-right-radius: 0;
		border-bottom-right-radius: 0;
		border-bottom-left-radius: 15rpx;
	}

	.live_user_tab_content_item_img_text {
		position: absolute;
		top: 0;
		left: 0;
		color: #fff;
		font-size: 20rpx;
		height: 40rpx;
		line-height: 40rpx;
		padding: 0 15rpx;
		border-top-left-radius: 15rpx;
		border-top-right-radius: 0;
		border-bottom-right-radius: 15rpx;
		border-bottom-left-radius: 0;
		background: linear-gradient(45deg, rgba(255, 0, 0, 1) 0%, rgba(255, 122, 24, 1) 100%);
	}

	.live_user_tab_content_item_right {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: flex-start;
		height: 226rpx;
		padding: 10rpx 0 10rpx 20rpx;
	}

	.live_user_tab_content_item_right_top {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
	}

	.live_user_tab_content_item_right_top_name {
		color: #2d2d2d;
		font-size: 28rpx;
		line-height: 42rpx;
		height: 84rpx;
		width: 444rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		word-break: break-word;
	}

	.live_user_tab_content_item_right_top_jingle {
		color: #949494;
		font-size: 26rpx;
		line-height: 36rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		margin-top: 10rpx;
		width: 444rpx;
	}

	.live_user_tab_content_item_right_bottom {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: flex-end;
		width: 444rpx;
	}

	.live_user_tab_content_item_right_bottom_price_unit {
		color: #fc1c1c;
		font-size: 24rpx;
	}

	.live_user_tab_content_item_right_bottom_price_num {
		font-size: 36rpx;
		color: #fc1c1c;
		margin-left: 3rpx;
	}

	.live_user_tab_content_item_right_bottom_click_num {
		display: flex;
		flex-direction: row;
		align-items: center;
	}

	.live_user_tab_content_item_right_bottom_click_num_add_cart {
		width: 42rpx;
		height: 42rpx;
	}

	.live_user_tab_content_item_right_bottom_click_num_image {
		width: 42rpx;
		height: 42rpx;
	}

	.live_user_tab_content_item_right_bottom_click_num_text {
		color: #949494;
		font-size: 22rpx;
	}

	.line_marginl_20 {
		border-bottom-width: 1px;
		border-bottom-color: #eee;
		border-bottom-style: solid;
		width: 730rpx;
		margin-left: 20rpx;
	}
	/* 直播间商品样式-end */
	/* 直播结束 start */
	.live_stop{
		/* width: 750rpx; */
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		top: 0;
		/* background: rgba(0, 0, 0, 0.5); */
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: flex-start;
		z-index: 2;
	}
	.live_stop_bg{
		/* width: 750rpx; */
		/* height: 100%; */
		background-size: cover;
		background-repeat: no-repeat;
		background-position: center center;
	}
	.live_stop_mask{
		/* width: 750rpx; */
		/* height: 100%; */
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		top: 0;
		z-index: 2;
		/* background: linear-gradient(0deg, #060606 0%, #4E4E4E 56.00000000000001%, #090909 99%); */
	}
	.live_stop_container{
		/* position: absolute;
		top: 374rpx;
		z-index: 2;
		width: 750rpx;
		height: 100%;
		text-align: center; */
		position: fixed;
		right: 0;
		bottom: 0;
		top: 0;
		left: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		opacity: 0;
	}
	.live_stop_title{
		color: #fffeee;
		font-size: 36rpx;
		font-weight: bold;
	}
	.live_stop_time{
		opacity: 0.6;
		color: #ffffff;
		font-size: 24rpx;
		font-weight: 500;
		margin-top: 27rpx;
	}
	.live_stop_recording{
		color: #ffffff;
		font-size: 24rpx;
		font-weight: 500;
		margin-top: 17rpx;
	}
	.live_stop_like_watch{
		color: #ffffff;
		font-size: 26rpx;
		font-weight: 500;
		margin-top: 17rpx;
	}
	.return_refresh{
		position: absolute;
		top: 470rpx;
		/* 以上定位单独为直播状态模块的返回按钮设置了定位位置，
		   使该按钮与 livePlay 页面中的返回按钮位置一致，
		   其他文字显示未做修改，整体设置了 opacity:0 隐藏
		*/
		z-index: 2;
		width: 160rpx;
		height: 90rpx;
		line-height: 90rpx;
		color: #ffffff;
		font-size: 28rpx;
		text-align: center;
		border: 1rpx solid #FFFFFF;
		border-radius: 25rpx;
		margin: 40rpx auto 0;
	}
	/* 直播结束 end */
	.animate-wrap{
		position: relative;
		height: 400rpx;
		width: 100%;
		align-self: flex-end;
		bottom: -20rpx;
	}
	
	.a-img {
		position: absolute;
		z-index: 99;
	}
	
</style>
