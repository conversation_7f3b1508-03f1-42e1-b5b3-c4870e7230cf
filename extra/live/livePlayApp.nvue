<template>
	<view>
		<swiper ref="listBox" vertical :style="'height: '+ boxStyle.height +';'" @animationfinish="disappear" :current="swiperCurrent" @change="videoChange">
			<swiper-item v-for="(item,index) in liveList" :key="index" :style="'height: '+ boxStyle.height +';'">
				<view v-if="item.liveDetail" class="live_play" :style="'height: '+ boxStyle.height +';'">
					<block v-if="item.liveState">
						<view v-if="item.liveDetail.liveState == 4">
							<video ref="sldVideo_child" :id="'sldVideo'+live_id" class="live_back"
								:src="item.liveDetail.replayUrl" :show-fullscreen-btn="false" object-fit="cover"
								:show-center-play-btn="true" :style="boxStyle" :enable-play-gesture="true"
								:show-progress="true" :controls="true" @pause="onPause(index)"
								@ended="onPlayBackEnd" @timeupdate="ontimeupdate"
							>
							</video>
						</view>

						<view v-if="item.liveDetail.liveState != 4">
							<video ref="sldVideo_child" :id="'sldVideo'+live_id" class="live_back"
								:src="item.liveDetail.pullUrl" :show-fullscreen-btn="false" object-fit="cover"
								:show-center-play-btn="false" :style="boxStyle" :enable-play-gesture="true"
								:show-progress="false" controls="false" :is-live="true">
							</video>
						</view>
					</block>
					
					
					<view class="live_pause" @click.stop="chansgedd" v-if="!item.videoPlay" >
						<image class="live_pause_img" :src="imgUrl + 'svideo/svideo_play.png'"
							mode="aspectFit" lazy-load></image>
					</view>

					<!-- 回放 -->
					<view class="live_header">
						<image @click="goBack" class="live_header_go_back" :src="imgUrl+'svideo/white_arrow_l.png'">
						</image>
						<view class="live_header_avator" :data-authorId="item.liveDetail.authorId"
							@click="goLiveUserCenter">
							<image mode="aspectFill" class="live_header_avator_img"
								:src="item.liveDetail.memberAvatar" />
						</view>
						<view class="live_header_mem_info" :data-authorId="item.liveDetail.authorId"
							@click="goLiveUserCenter">
							<text class="live_header_mem_info_name">{{item.liveDetail.memberNickname}}</text>
							<view class="live_header_mem_info_stat_num">
								<text v-if="item.liveDetail.isSelf == 1" class="live_header_mem_info_stat_num_click_num"
									style="margin-right: 20rpx">{{live_like_num}}人气</text>
								<text
									class="live_header_mem_info_stat_num_click_num">{{live_click_num}}{{Ls('观看')}}</text>
							</view>
						</view>
						<view v-if="!item.liveDetail.isSelf&&!item.liveDetail.isFollow" class="live_header_live_fllow"
							@click="collect">
							<image class="live_header_live_fllow_image" :src="imgUrl+'svideo/fav_a.png'"></image>
							<text class="live_header_live_fllow_text">{{Ls('关注')}}</text>
						</view>
					</view>


					<!-- 商品，输入框，点赞按钮 -->
					<view class="live_footer" :style="item.liveDetail.liveState==4?'bottom:100rpx;':'bottom:30rpx'">
						<!-- 点赞动画 -->
						<view class="animate-wrap">
							<view class="a-img" v-for="(items,index) in viewList" :key="items.elId" :ref="items.elId"
								:style="{'right': site[0] + 'rpx','bottom': site[1] + 'rpx'}">
								<image :style="{'width': imgWidth + 'rpx','height': imgHeight + 'rpx'}" mode="widthFix"
									:src="items.src"></image>
							</view>
						</view>


						<view class="live_footer_con">
							<view class="live_footer_goods" @click="showGoodsFun">
								<image class="live_footer_goods_image" :src="imgUrl+'svideo/nvbao.png'"></image>
								<text
									class="live_footer_goods_text">{{item.liveDetail.goodsNum * 1 > 99 ? '99+' : item.liveDetail.goodsNum }}</text>
							</view>

							<input type="text" name="talk_con" v-model="input_val" class="live_footer_talk_con"
								:placeholder="Ls('说点什么吧~')" :placeholder-class='live_footer_talk_con_placeholder'
								confirm-type="send" :disabled="liveDetail.liveState == 4" @confirm="publishComment"
								maxlength="30"></input>

							<view class="live_footer_share" @click="showShare">
								<image class="live_footer_share_image" :src="imgUrl+'svideo/share.png'"></image>
								<text class="live_footer_share_text">{{Ls('分享')}}</text>
							</view>

							<view class="live_footer_add_heart" @click="add_heart">
								<image class="live_footer_add_heart_image" :src="imgUrl+'svideo/add_heart.png'"></image>
								<text class="live_footer_share_text">{{live_like_num}}</text>
							</view>
						</view>
					</view>

					<view v-if="item.liveDetail.liveState == 4" class="talk_con_disabled" @click="notSendDamu(item)">
						<image class="talk_con_disabled_img" :src="imgUrl+'svideo/talk_disabled.png'" mode="aspectFit">
						</image>
					</view>

					<!-- 弹幕 start-->
					<view class="barrage_wrap" :style="item.liveDetail.liveState==4?'bottom:200rpx;':'bottom:130rpx'">
						<view class="barrage_wrap_notice">
							<text class="barrage_wrap_notice_text"
								v-if="noticeList&&noticeList.length>0">{{noticeList[0].msg}}</text>
						</view>

						<scroll-view class="barrage" scroll-y :scroll-into-view="toBottom">
							<view class="gonggao" v-if="item.liveDetail.liveNotice">
								<text class="gonggao_title">{{Ls('公告')}} </text>
								<text class="gonggao_ba_txt">{{item.liveDetail.liveNotice}}</text>
							</view>
							<view v-for="(item, index) in msgList" :key="index" :index="index" v-if="msgList.length>0"
								:id="'item' + index">
								<view class="barrage_item">
									<text class="barrage_item_name" :style="'color:' + item.color_random">{{item.authorName}}:{{item.msg}}</text>
								</view>
							</view>
						</scroll-view>
					</view>
					<!-- 弹幕 end-->

					<!-- 直播状态信息 start -->
					<view class="live_stop" v-if="!item.liveState" :style="'height:'+boxStyle.height+';'">
						<view class="live_stop_mask" :style="'height:'+boxStyle.height+';'">
							<image class="live_stop_bg" :src="item.liveDetail.liveCover"></image>
							<!-- 直播结束 -->
							<view class="live_stop_container" v-if="show_live_stop">
								<text class="live_stop_title">
									{{live_stop_info.forbidReason?live_stop_info.forbidReason:Ls('直播已结束')}}
								</text>
								<text class="live_stop_time">
									{{Ls('直播时长：')}}{{live_stop_info.playbackTime}}
								</text>
								<text class="live_stop_like_watch">
									{{Ls('人气')}} {{live_stop_info.popularityNum * 1}} {{Ls('观看')}}{{live_stop_info.viewingNum * 1}}
								</text>
								<text @click="goBack" class="return_refresh">
									{{Ls('返回')}}
								</text>
							</view>
							<!-- 直播录制中 -->
							<view class="live_stop_container" v-if="live_recording">
								<text class="live_stop_title">
									{{Ls('直播已结束')}}
								</text>
								<text class="live_stop_recording">
									{{Ls('正在录制中，录制完成才可以观看')}}
								</text>
								<text class="live_stop_like_watch">
									{{Ls('获赞')}} {{item.liveDetail.popularityNum}} {{Ls('观看')}}{{item.liveDetail.viewingNum}}
								</text>
								<text @click="goBack" class="return_refresh">
									{{Ls('返回')}}
								</text>
							</view>
							<!-- 直播被禁止 -->
							<view class="live_stop_container" v-if="live_ban">
								<text class="live_stop_title">
									{{live_ban_info.msg}}
								</text>
								<text class="live_stop_recording">
									{{live_ban_info.remark}}
								</text>
								<text @click="goBack" class="return_refresh">
									{{Ls('返回')}}
								</text>
							</view>
							<!-- 直播被删除 -->
							<view class="live_stop_container" v-if="live_delete">
								<view class="live_stop_title">
									{{Ls('该直播已被删除')}}
								</view>
								<view @click="goBack" class="return_refresh">
									{{Ls('返回')}}
								</view>
							</view>
							<!-- ss -->
						</view>
					</view>
					<!-- 直播状态信息 end -->
				</view>
			</swiper-item>
		</swiper>


		<!-- 视频绑定的商品模块 start -->
		<livePlayGoods ref="livePlayGoods"></livePlayGoods>
		<!-- 视频绑定的商品模块 end -->

		<!-- 分享start -->
		<shareWrap ref="shareWrap"></shareWrap>
		<!-- 分享end -->

	</view>
</template>

<script>
	import {
		checkPageHasMore,
		colorArray,
		initNum
	} from "@/utils/live";
	import request from "@/utils/request";
	import io from '@hyoga/uni-socket.io';
	import {
		mapState,
		mapMutations
	} from 'vuex';
	import {
		getCurLanguage
	} from '@/utils/base.js'


	const likeIcon1 = getApp().globalData.imgUrl + 'svideo/1.png'
	const likeIcon2 = getApp().globalData.imgUrl + 'svideo/2.png'

	import livePlayGoods from '../component/live/livePlayGoods.nvue'
	import shareWrap from '../component/live/shareWrap.nvue'

	export default {
		components: {
			livePlayGoods,
			shareWrap
		},
		data() {
			return {
				key: '',
				pageSize: 10, //商品每页数量
				pn: 1, //商品的当前页
				loading: false,
				//数据加载状态
				liveDetail: '',
				//直播详情
				settingData: '',
				//平台设置信息
				imgUrl: getApp().globalData.imgUrl,
				//图片地址
				bgStyle: 'background-size:contain;background-position:center center;background-repeat: no-repeat;',
				//背景图片样式
				shareWrap: false,
				// 展示分享弹层
				shareWrap2: false,
				// 展示分享弹层
				shareImg: '',
				// 分享图片
				goodsList: [],
				//直播绑定的商品
				hasmore: true,
				//是否还有数据，用于页面展示
				showGoods: false,
				//是否显示商品弹层
				playFlag: true,
				//视频是否播放
				showPauseBtn: false,
				//是否显示暂停按钮
				colorArray: colorArray,
				//颜色数组
				msgList: [],
				//弹幕内容
				noticeList: [],
				//消息通知列表
				input_val: '',
				//输入框内容
				toBottom: '',
				//弹幕滚动到指定的元素
				initial_time: 1,
				// 指定视频初始播放位置
				live_like_num: '--',
				//人气数
				live_click_num: '--', //观看数
				liveStateInfo: "",
				isWeiXinBrower: false, //是否微信浏览器
				showWeiXinBrowerTip: false, //微信浏览器分享的提示操作
				stat_end: 0, //终端，默认为1，pc端
				show_live_stop: false, //是否显示直播结束提示
				live_stop_info: {}, //显示直播结束提示信息
				live_recording: false, //是否显示直播录制中提示
				live_ban: false, //直播是否被禁止
				live_ban_info: {
					msg: '',
					remark: ''
				},
				live_delete: false,
				wHeight: 0,
				boxStyle: {
					width: '750rpx',
					height: '750rpx'
				},

				live_id: '', //当前直播id
				label_id: '', //当前直播标签

				liveList: [],
				liveIndex: 0, //当前视频下标
				appearOld: 0, //切换时当前视频下标
				appearNew: 0, //切换后视频下标
				livePn: 1,
				liveHasmore: true,
				scrollTop: 2000,
				Ls: getCurLanguage, //语言包

				//点赞效果
				throttle: 100,
				large: 1,
				site: [45, 0],
				high: 220,
				viewList: [],
				elId: 0, // 元素渲染id
				oldTime: 0, // 全局时间用于函数节流
				timer: null, // 定时器
				waitDeleteIndex: 0,
				showImgs: [likeIcon1, likeIcon2],
				imgWidth: 52,
				imgHeight: 52,
				duration: 3000,
				range: 50,
				alone: true,
				
				swiperCurrent:0,
				secondTrigger:false,
				
				clickStamp:0
			};
		},
		props: {},
		computed: {
			...mapState(['hasLogin', 'cartData', 'userInfo']),
			currentLive(){
				return this.liveList[this.liveIndex]
			}
		},
		
		onLoad(options) {
			let that = this;
			this.wHeight = uni.getSystemInfoSync().screenHeight; //获取屏幕高度
			this.boxStyle.height = uni.getSystemInfoSync().windowHeight + 'px'; //获取屏幕高度
			this.getPlatform();
			this.live_id = options.live_id
			this.label_id = options.label_id
			this.liveList = []
			this.liveList.push({
				live_id: this.live_id,
				liveDetail: {},
				goodsList: {},
				hasmore: true,
				shareImg: getApp().globalData.apiUrl + 'v3/video/front/video/live/share/shareImage?liveId=' +this.live_id + '&source=' + this.stat_end,
				liveState: true,
				videoPlay:true,
				replayUrlList:[],
				replaySegIndex:0
			})
			this.initData(this.live_id, 0); //获取第一个视频的详情
			this.getLiveList(); //获取第一组视频列表数据

		},

		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload() {
			if (this.socket) {
				this.closeSocket();
			}
			if (this.currentLive.liveState) {
				this.$refs.sldVideo_child[this.liveIndex].stop()
			}
		},
		
		onHide() {
			if (this.currentLive.liveState) {
				this.$refs.sldVideo_child[this.liveIndex].pause()
			}
		},
		
		
		onShow(){
			if(this.secondTrigger){
				if (this.currentLive.liveState) {
					this.$refs.sldVideo_child[this.liveIndex].play()
					this.currentLive.videoPlay = true;
				}
				
			}
		},

		methods: {

			onPause(index) {
				if(this.liveIndex==index){
					this.chansgedd()
				}
			},
			
			videoChange(e) {
				if (this.liveIndex != e.detail.current) {
					this.msgList = []
				}
			},

			//获取当前终端的方法
			getPlatform() {
				if (uni.getDeviceInfo().platform == 'ios') {
					this.stat_end = 4;
				} else if (uni.getDeviceInfo().platform == 'android') {
					this.stat_end = 5;
				}
			},

			//滑动屏幕时关闭评论弹窗
			touchMove() {
				this.$refs.livePlayGoods.closeGoods();
			},
			//结束滑动屏幕
			disappear(e) {
				let oldIndex = this.liveIndex
				if (this.liveIndex != e.detail.current) {
					this.liveIndex = e.detail.current
					if (this.currentLive.liveState) {
						this.$refs.sldVideo_child[oldIndex].pause();
						this.$refs.sldVideo_child[this.liveIndex].play();
					}
					this.initSocket()
					if(this.liveIndex==this.viewList.length-2){
						this.getMoreVideo()
					}

				}
			},
			//获取更多短视频列表数据
			getMoreVideo() {
				if (this.liveHasmore) {
					this.getLiveList();
				}
			},
			
			
			//视频播放/暂停
			chansgedd(e) {
				if (this.currentLive.videoPlay) {
					this.currentLive.videoPlay = false;
				} else {
					this.currentLive.videoPlay = true;
					this.$refs.sldVideo_child[this.liveIndex].play();
				}
			},

			// 获取直播视频列表数据
			getLiveList() {
				let param = {}
				param.url = 'v3/video/front/video/live/liveList'
				param.method = 'GET'
				param.data = {}
				param.data.liveId = this.live_id
				param.data.labelId = this.label_id
				param.data.current = this.livePn
				param.data.pageSize = 3
				request(param).then(res => {
					if (res.state == 200) {
						if (res.data.list.length > 0) {
							let arr = [];
							res.data.list.forEach(item => {
								arr.push({
									live_id: item.liveId,
									liveDetail: {},
									goodsList: {},
									hasmore: true,
									shareImg: getApp().globalData.apiUrl +'v3/video/front/video/live/share/shareImage?liveId=' + item.liveId + '&source=' + this.stat_end,
									liveState: true,
									videoPlay:true,
									replayUrlList:[],
									replaySegIndex:0
								})
							})
							let len = this.liveList.length;
							this.liveList = this.liveList.concat(arr);
							res.data.list.forEach((item, index) => {
								this.initData(item.liveId, len + index)
							})
						}
						this.liveHasmore = checkPageHasMore(res.data.pagination); //是否还有数据
						if (this.liveHasmore) {
							this.livePn++;
						}
					}
				})
			},
			//获取直播详情和商品
			initData(id, index) {
				this.show_live_stop = false;
				this.getLiveInfo(id, index);

			},
			//获取直播详情
			getLiveInfo(live_id, index) {
				let liveDetail = this.liveList[index].liveDetail;
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/live/playingPage'
				param.method = 'GET'
				param.data.liveId = live_id
				request(param).then(res => {
					if (res.state == 200) {
						liveDetail = res.data;
						this.liveList[index].liveDetail = liveDetail;
						if (liveDetail.liveState == 5) {
							//视频被删除
							this.live_delete = true;
							this.liveList[index].liveState = false
						} else if (liveDetail.playState == 2) {
							//禁止播放
							this.live_ban = true;
							this.liveList[index].liveState = false
							this.live_ban_info = {
								msg: this.Ls('该直播已被禁止'),
								remark: liveDetail.forbidReason
							}
						} else if (liveDetail.liveState == 3) {
							//录制中
							this.live_recording = true;
							this.liveList[index].liveState = false
						} else {
							if(liveDetail.replayUrl.split(',').length>1){
								let playList = liveDetail.replayUrl.split(',')
								this.liveList[index].replayUrlList = playList
								liveDetail.replayUrl = playList[this.liveList[index].replaySegIndex];
								this.liveList[index].liveDetail.replayUrl = liveDetail.replayUrl
							}
							if (index == 0) {
								setTimeout(() => {
									this.secondTrigger = true
									this.$refs.sldVideo_child[0] && this.$refs.sldVideo_child[0].play();
								}, 200)
								
								if (this.socket) {
								} else {
									this.initSocket();
								}
							}
						}
					} else {
						//禁止播放
						this.live_ban = true;
						this.liveList[index].liveState = false
						this.live_ban_info = {
							msg: this.Ls('该直播已被禁止'),
							remark: liveDetail.forbidReason ? liveDetail.forbidReason : ''
						}
					}
				})
			},

			notSendDamu(item) {
				if (item.liveDetail.liveState == 4) {
					uni.showToast({
						title: this.Ls('录播不能发送消息'),
						icon: 'none'
					})
				}
			},
			
			updateCollect(author_id){
				if(this.currentLive.liveDetail.authorId==author_id){
					if(this.currentLive.liveDetail.isFollow){
						this.currentLive.liveDetail.isFollow=false
					}else{
						this.currentLive.liveDetail.isFollow=true
					}
				}
			},


			touchmoveshare() {
				return false;
			},

			initSocket() {
				if (this.socket) {
					this.closeSocket();
				}

				const {
					live_id,
					liveDetail
				} = this.currentLive;

				const authorInfo = liveDetail.author;
				let userInfo = {
					live_id: live_id ? live_id : 1
				};
				if (authorInfo && authorInfo.authorId) {
					userInfo.author_name = authorInfo.memberNickname ? authorInfo.memberNickname : authorInfo.memberName;
					userInfo.author_id = authorInfo.authorId;
				} else {
					userInfo.author_name = this.Ls('游客');
					userInfo.author_id = 0;
				}
				this.socket = io(getApp().globalData.socketUrl, {
					reconnection: true,
					jsonp: true,
					transports: ['websocket', 'polling'],
					timeout: 5000,
					query: `author_name=${userInfo.author_name}&author_id=${userInfo.author_id}&live_id=${userInfo.live_id}&is_own=${liveDetail.isSelf?1:0}`
				});

				this.socket.on("connect", () => {
					//给服务端发送消息
					this.socket.emit("update_user", userInfo);

					this.socket.on("get_msg", e => {
						if (typeof e.msg === 'string') {
							this.socketHandle('wec', e);
						} else {
							this.socketHandle('msg', e.msg);
						}
					}); //获取服务端新人信息

					this.socket.on("get_new_user", e => {
						this.socketHandle2('new', e);
					});
					this.socket.on("disconnect", function() {});
				}); //获取关注的返回信息

				this.socket.on("get_follow_msg", e => {
					this.socketHandle2('follow', e);
				}); //获取直播点击数

				this.socket.on("get_click_num", e => {
					this.clickNumHandle(e);
				}); //获取直播人气数

				this.socket.on("get_like_num", e => {
					this.likeNumHandle(e);
				}); // 直播结束

				this.socket.on('stop_live', e => {
					this.handleStopLive(e);
				});
			},

			//关闭socket
			closeSocket() {
				if (this.socket) {
					this.socket.close();
				}
			},

			socketHandle(type, msg) {
				if (msg.type) {
					return;
				}
				msg.type = type;
				msg.color_random = colorArray[Math.floor(Math.random() * 8)];
				this.msgList.push(msg);
				let randomNum = (this.msgList.length - 1 + Math.random() * 1).toFixed(2)
				let randonNumText = randomNum.toString().replace('.', '')
				this.toBottom = `item${randonNumText}`
				this.$nextTick(() => {
					this.toBottom = this.msgList.length ? `item${this.msgList.length - 1}` : 'item0'
				})
			},

			//直播结束
			handleStopLive(e) {				
				this.liveList[this.liveIndex].liveState = false
				this.liveStateInfo = JSON.parse(e);
				this.show_live_stop = true;
				this.live_stop_info = JSON.parse(e);
			},

			//获取服务端新人信息
			socketHandle2(type, msg) {
				let that = this
				let {noticeList} = this;
				if (msg.type) {
					return;
				}
				if (noticeList.filter(el => el.type === 'follow').length > 0 && type === 'follow') {
					return;
				}
				msg.type = type;
				msg.timer = setTimeout(() => {
					that.noticeRemoveItem(type);
				}, 2000);
				if (type === 'follow') {
					noticeList.push(msg);
				} else {
					let index = noticeList.findIndex(el => el.type === 'new');
					if (index > -1) {
						noticeList[index] = msg;
					} else {
						noticeList.push(msg);
					}
				}
				this.noticeList = noticeList;
			},

			noticeRemoveItem(type) {
				let {noticeList} = this;
				let index = noticeList.findIndex(el => el.type === type);
				if (index > -1) {
					clearTimeout(noticeList[index].timer);
					noticeList.splice(index, 1);
				}
				this.noticeList = noticeList;
			},

			// 点击量处理
			clickNumHandle(e) {
				let liveDetail = this.currentLive.liveDetail;
				let {live_click_num} = this;
				liveDetail.clickNum = e.click_num;
				this.currentLive.liveDetail = liveDetail;
				this.live_click_num = initNum(e.click_num);
			},

			// 人气
			likeNumHandle(e) {
				let liveDetail = this.currentLive.liveDetail;
				let {live_like_num} = this;
				liveDetail.likeNum = e.like_num;
				this.currentLive.liveDetail = liveDetail;
				this.live_like_num = initNum(e.like_num);
			},

			// 发送消息
			publishComment(e) {
				let live_id = this.currentLive.live_id;
				let liveDetail = this.currentLive.liveDetail;
				if (this.hasLogin && liveDetail.authorId != null) {
					let content = e.detail.value; //没有内容的话直接结束
					if (!content.trim()) {
						return false;
					}
					let msg = {
						author_id: liveDetail.author.authorId,
						author_name: liveDetail.author.memberNickname,
						live_id: live_id,
						msg: content
					};
					this.socket.emit("send_msg", msg);
					//清空输入框的内容
					this.input_val = ''
				} else {
					this.goLogin()
				}
			},

			//增加人气事件
			add_heart(e) {
				if (this.live_ban) {
					uni.showToast({
						title: this.live_ban_info.msg,
						icon: 'none'
					})
					return;
				}
				let live_id = this.currentLive.live_id;
				let liveDetail = this.currentLive.liveDetail;
				if (this.hasLogin) {
					this.socket.emit("update_like_num", {
						author_id: liveDetail.authorId,
						live_id
					});
					this.handleClick(e)
				} else {
					this.goLogin()
				}
			},

			// 返回上级页面
			goBack() {
				uni.navigateBack()
			},

			//关注、取消关注事件
			collect(e) {
				let liveDetail = this.currentLive.liveDetail
				if (this.hasLogin) {
					let param = {}
					param.data = {}
					param.method = 'POST'
					param.data.authorId = liveDetail.authorId
					if (liveDetail.isFollow) {
						param.url = 'v3/video/front/video/cancelFollow'
					} else {
						param.url = 'v3/video/front/video/followAuthor'
					}
					request(param).then(res => {
						if (res.state == 200) {
							this.currentLive.liveDetail.isFollow = !(this.currentLive.liveDetail.isFollow)
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							});
						}
					})
				} else {
					this.goLogin()
				}
			},

			//进入作者页面
			goLiveUserCenter(e) {
				let author_id = this.currentLive.liveDetail.authorId;
				let page = getCurrentPages();
				let len = page.length;
				if (len > 4) {
					uni.redirectTo({
						url: '/extra/user/my?author_id=' + author_id
					});
				} else {
					uni.navigateTo({
						url: '/extra/user/my?author_id=' + author_id
					});
				}

			},

			//分享点击事件
			showShare() {
				if (this.live_ban) {
					uni.showToast({
						title: this.live_ban_info.msg,
						icon: 'none'
					})
					return;
				}
				let {liveDetail,shareImg} = this.currentLive;
				this.$refs.shareWrap.injectInfo({
					live_id: liveDetail.liveId,
					liveName: liveDetail.liveName,
					authorName: liveDetail.memberNickname || liveDetail.memberName,
					liveCover: liveDetail.liveCover,
					shareImg
				})
			},

			//打开商品弹层
			showGoodsFun() {
				let liveDetail = this.currentLive.liveDetail;
				let {
					showGoods
				} = this;
				if (Number(liveDetail.goodsNum) > 0) {
					this.$refs.livePlayGoods.injectGoods({
						live_id: liveDetail.liveId,
						goodsNum: liveDetail.goodsNum,
						live_ban: this.live_ban,
						live_ban_info: this.live_ban_info
					})
				}
			},


			//点赞动画事件
			handleClick(e) {
				// 函数节流
				let timeStamp = e.timeStamp
				let interval = timeStamp - this.oldTime
				if (interval < this.throttle) return null;
				this.oldTime = timeStamp
				let animation = {}
				// 创建animate配置
				animation = uni.requireNativePlugin('animation')
				let randomImg = Math.floor(Math.random() * this.showImgs.length)
				let _item = {
					elId: this.elId, // 生成元素ref
					src: this.showImgs[randomImg], // 随机图标
					animation: animation, // 每个盒子动画
					x: Math.ceil(Math.random() * this.range), // 方向间距
					q: Math.floor(Math.random() * 2), // 随机方向
				}
				// 动画
				let _abs = ['-', '']
				let _dirX = Number(_abs[_item.q] + _item.x) // 随机的方向和间距
				let _dirY = this.high - Math.random() * 10
				// 生成DOM
				this.elId++
				this.viewList.push(_item)
				// 执行动画
				setTimeout(() => {
					this.$nextTick(() => {
						let _n = 1
						if (this.large) _n = typeof(this.large) === 'number' ? this.large : 2;
						let el = this.$refs[_item.elId][0];
						clearTimeout(this.timer)
						_item.animation.transition(el, {
							styles: {
								transform: `translate(${_dirX}rpx, -${_dirY}rpx) scale(${_n}, ${_n}])`,
								transformOrigin: 'center center',
								opacity: 0
							},
							duration: this.duration, // ms
							timingFunction: 'ease-out',
							delay: 100 // ms
						}, () => {
							console.log('animation finished.')
							// 完成后事件回调
							// 逐渐消失
							if (this.alone) {
								this.waitDeleteIndex++
								this.deleteView()
								return null
							}
						})
					})
				}, 100)
				// 点击立即触发组件事件
			},
			deleteView() {
				this.viewList.splice(0, this.waitDeleteIndex)
				this.waitDeleteIndex = 0
			},



			goLogin() {
				let Route = {
					path: '/extra/live/livePlayApp',
					query: {
						live_id: this.live_id,
						label_id: this.label_id
					}
				}

				uni.showModal({
					title: '提示',
					content: '请登录',
					success: res => {
						if (res.confirm) {
							let url = Route.path;
							const query = Route.query;
							uni.setStorageSync('fromurl', {
								url,
								query
							});
							uni.navigateTo({
								url: '/pages/public/login'
							})
						}
					}
				});
			},
			
			
			//回放结束时处理
			onPlayBackEnd(){
				this.nextSegment()
			},
			
			//如果出现播放事件大于总时间，而且没有触发onPlayBackEnd方法的话，在这做个预防处理
			ontimeupdate(e){
				if(Number(e.detail.currentTime)>Number(e.detail.duration)){
					this.nextSegment()
				}
			},
			
			//如果该回放有分p的话，播放结束时切换至下一p播放，否则循环播放
			nextSegment(){
				let videoRef = this.$refs.sldVideo_child[this.liveIndex]
				let {replayUrlList,replaySegIndex} = this.currentLive
				if(replayUrlList.length){
					if(replaySegIndex==replayUrlList.length-1){
						this.currentLive.replaySegIndex = 0
					}else{
						this.currentLive.replaySegIndex++
						uni.showToast({
							title:'切换至下一分p',
							duration:2500,
							icon:"none"
						})
					}
					this.currentLive.liveDetail.replayUrl = this.currentLive.replayUrlList[this.currentLive.replaySegIndex]
					setTimeout(()=>{
						videoRef.play()
					},1000)
				}else{
					videoRef.stop()
					setTimeout(()=>{
						videoRef.play()
					},1000)
				}
			}
		}
	};
</script>
<style>
	page {
		width: 750rpx;
		overflow-x: hidden;
		overflow: hidden;
	}

	.live_fixed_bg {
		width: 750rpx;
		position: fixed;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		z-index: 1;
	}

	.no-has-more {
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		height: 80rpx;
		text-align: center;
		line-height: 80rpx;
		color: #949494;
		font-size: 22rpx;
	}

	.no-has-more_text {
		line-height: 80rpx;
		color: #949494;
		font-size: 22rpx;
	}

	.live_play {
		position: relative;
		background-color: transparent;
		width: 750rpx;
		overflow: hidden;
	}

	.live_back,
	.living {
		width: 750rpx;
		/* height: 600rpx; */
		height: 100vh;
		overflow: hidden;
	}

	/* app-1-start */
	/* #ifdef APP-PLUS */
	.live_back {
		width: 100%;
		height: 100vh;
		overflow: hidden;
	}

	/* #endif */
	/* app-1-end */

	.live_header {
		position: absolute;
		top: 80rpx;
		left: 20rpx;
		z-index: 2;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: flex-start;
		width: 750rpx;
		height: 80rpx;
		overflow-x: hidden;
	}

	.live_header_go_back {
		width: 45rpx;
		height: 47rpx;
	}

	.live_header_avator {
		width: 80rpx;
		height: 80rpx;
		border-radius: 40rpx;
		border-width: 2rpx;
		border-style: solid;
		border-color: #fff;
		margin-left: 8rpx;
		overflow: hidden;
	}

	.live_header_avator_img {
		width: 76rpx;
		height: 76rpx;
		border-radius: 38rpx;
		overflow: hidden;
	}

	.live_header_mem_info {
		margin-left: 20rpx;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-content: space-between;
	}

	.live_header_mem_info_name {
		display: -webkit-box;
		max-width: 250rpx;
		line-height: 32rpx;
		color: #fff;
		font-size: 28rpx;
		font-weight: 600;
		overflow: hidden;
		white-space: nowrap;
		lines: 1;
		text-overflow: ellipsis;
		margin-bottom: 15rpx;
	}

	.live_header_mem_info_stat_num {
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
	}

	.live_header_mem_info_stat_num_click_num {
		color: #fff;
		font-size: 22rpx;
		line-height: 36rpx;
		white-space: nowrap;
		font-weight: 600;
	}

	.live_header_live_fllow {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		width: 120rpx;
		height: 50rpx;
		background: #fc1c1c;
		border-radius: 25rpx;
		margin-left: 25rpx;
	}

	.already {
		background: #999 !important;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		width: 126rpx;
		height: 50rpx;
		border-radius: 25rpx;
		margin-left: 25rpx;
	}

	.live_header_live_fllow_image {
		width: 46rpx;
		height: 46rpx;
	}

	.live_header_live_fllow_text {
		color: #fff;
		font-size: 24rpx;
		margin-right: 8rpx;
	}

	.live_footer {
		position: absolute;
		left: 0;
		right: 0;
		bottom: calc(30rpx + constant(safe-area-inset-bottom));
		z-index: 10;

	}

	.live_footer_con {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: flex-start;
		overflow-x: hidden;
		width: 750rpx;
		height: 130rpx;
		margin: 0 auto;
		padding-top: 30rpx;
		padding-left: 40rpx;
	}

	.live_footer_goods {
		width: 85rpx;
		height: 92rpx;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		position: relative;
	}

	.live_footer_goods_image {
		width: 85rpx;
		height: 92rpx;
	}

	.live_footer_goods_text {
		position: absolute;
		left: 36rpx;
		top: 54rpx;
		line-height: 23rpx;
		color: #fff;
		font-size: 22rpx;
	}

	.live_footer_talk_con {
		width: 365rpx;
		height: 65rpx;
		color: #fff;
		font-size: 26rpx;
		font-weight: 600;
		border-radius: 30rpx;
		background-color: rgba(0, 0, 0, 0.3);
		margin-left: 30rpx;
		padding: 0 20rpx;
	}

	.talk_con_disabled {
		display: flex;
		align-items: center;
		position: absolute;
		left: 160rpx;
		bottom: 118rpx;
		z-index: 9;
		width: 365rpx;
		height: 65rpx;
		border-radius: 30rpx;
	}

	.talk_con_disabled_img {
		width: 474rpx;
		height: 70rpx;
		opacity: 0.5;
	}

	.live_footer_talk_con_placeholder {
		color: #fff;
		font-size: 24rpx;
		font-weight: 600;
	}

	.live_footer_share {
		position: relative;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: center;
		position: relative;
		z-index: 99;
		width: 77rpx;
		margin-left: 21rpx;
	}

	.live_footer_share_image,
	.live_footer_add_heart_image {
		width: 65rpx;
		height: 65rpx;
		font-weight: 600;
	}

	.live_footer_share_text {
		color: #fff;
		font-size: 22rpx;
		margin-top: 10rpx;
	}

	/* .live_footer_add_heart_image:active {
		-webkit-animation: 1s seconddiv;
	}

	@keyframes seconddiv {
		0% {
			transform: scale(1.3, 1.3);
		}

		50% {
			transform: scale(1.2, 1.2);
		}

		100% {
			transform: scale(1, 1);
		}
	} */


	.video_control {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		top: 0;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: flex-start;
		z-index: 5;
	}

	.video_control_image {
		width: 102rpx;
		height: 102rpx;
		margin-top: calc(100vh/3);
	}

	.empty_158 {
		width: 750rpx;
		overflow-x: hidden;
		height: 158rpx;
	}

	/***** 弹幕 start *****/

	.barrage_wrap {
		position: absolute;
		bottom: 100rpx;
		left: 30rpx;
		z-index: 99;
		max-height: 500rpx;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-content: flex-end;
	}

	.barrage_wrap_notice {
		display: inline-block;
		max-width: 500rpx;
		height: 50rpx;
		line-height: 50rpx;
		border-radius: 10rpx;
		margin-bottom: 10rpx;
	}

	.barrage_wrap_notice_text {
		display: inline-block;
		max-width: 500rpx;
		height: 50rpx;
		color: #fff;
		font-size: 24rpx;
		font-weight: 600;
		background-color: #fc1c1c;
		border-radius: 10rpx;
		line-height: 50rpx;
		margin-bottom: 10rpx;
		padding: 0 15rpx;
	}

	.barrage {
		min-height: 100rpx;
		max-height: 450rpx;
	}

	.gonggao {
		background-color: rgba(0, 0, 0, 0.3);
		white-space: normal;
		padding: 12rpx 16rpx;
		border-radius: 10rpx;
		word-break: break-all;
		font-weight: 600;
		width: 500rpx;
	}

	.gonggao_title {
		color: #fc1c1c;
		font-size: 26rpx;
		line-height: 36rpx;
		margin-right: 6rpx;
	}

	.gonggao_ba_txt {
		color: #fff;
		font-size: 26rpx;
		line-height: 36rpx;
	}

	.barrage_item {
		background-color: rgba(0, 0, 0, 0.3);
		max-width: 500rpx;
		border-radius: 23rpx;
		padding: 0rpx 15rpx 10rpx 15rpx;
		margin-top: 10rpx;
		white-space: normal;
		word-break: break-all;
		display: inline-block;
		font-weight: 600;
		display: flex;
		flex-direction: row;
		align-items: flex-start;
	}

	.barrage_item_name {
		font-size: 25rpx;
		line-height: 36rpx;
		margin-right: 15rpx;
		margin-top: 4rpx;
		max-width: 470rpx;
		word-wrap: break-word;
		word-break: break-all;
		padding:10rpx;
		background-color: rgba(0, 0, 0, 0.3);
		border-radius: 10rpx;
	}

	.barrage_item_con {
		color: #fff;
		font-size: 25rpx;
		margin-top: 8rpx;
	}

	/***** 弹幕 end *****/

	.canvas {
		background: transparent;
		width: 180rpx;
		height: 400rpx;
		position: fixed;
		right: -20rpx;
		bottom: 130rpx;
	}

	.live_footer_add_heart {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: center;
		width: 77rpx;
		margin-left: 21rpx;
	}

	/* 直播结束 start */
	.live_stop {
		width: 750rpx;
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		top: 0;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: flex-start;
		/* z-index: 2; */
	}
	
	.live_stop_bg {
		width: 750rpx;
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		top: 0;
		z-index: 1;
		background: rgba(0, 0, 0, 0.3);
	}
	
	.live_stop_mask {
		z-index: 2;
		background: rgba(0, 0, 0, 0.3);
	}
	
	.live_stop_container {
		position: fixed;
		right: 0;
		bottom: 0;
		top: 0;
		left: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		z-index: 3;
		background: rgba(0, 0, 0, 0.3);
	}
	
	.live_stop_title {
		color: #ffffff;
		font-size: 36rpx;
		font-weight: bold;
	}
	
	.live_stop_time {
		opacity: 0.6;
		color: #ffffff;
		font-size: 28rpx;
		font-weight: 500;
		margin-top: 37rpx;
	}
	
	.live_stop_recording {
		color: #ffffff;
		font-size: 24rpx;
		font-weight: 500;
		margin-top: 37rpx;
		white-space: nowrap;
	}
	
	.live_stop_like_watch {
		color: #ffffff;
		font-size: 26rpx;
		font-weight: 500;
		margin-top: 17rpx;
	}
	
	.return_refresh {
		width: 160rpx;
		height: 90rpx;
		line-height: 90rpx;
		color: #ffffff;
		font-size: 28rpx;
		text-align: center;
		border: 1rpx solid #FFFFFF;
		border-radius: 25rpx;
		margin: 40rpx auto 0;
	}
	/* 直播结束 end */
	.animate-wrap {
		position: relative;
		height: 400rpx;
		width: 100%;
		align-self: flex-end;
		bottom: -20rpx;
	}

	.a-img {
		position: absolute;
		z-index: 99;
	}
	
	.live_pause {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 1;
		overflow: hidden;
		display: flex;
		align-items: center;
		justify-content: center;
		background: rgba(0, 0, 0, 0.3);
	}
	
	.live_pause_img {
		/* position: relative;
		left: 65rpx; */
		width: 100rpx;
		height: 115rpx;
	}
</style>