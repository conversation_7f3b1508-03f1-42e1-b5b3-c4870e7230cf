<template>
	<view :style="mix_diyStyle">
		<view v-if="loadEnd">
			<block v-if="videoList.length == 1">
				<livePlayItem :initial_time="videoList[0].initial_time" :labelId="videoList[0].labelId"
					:live_id="videoList[0].live_id" :decoIndex="videoList[0].decoIndex" :index="videoList[0].index"
					:showHide="videoList[0].showHide" ref="livePlayItem" @shareVideo="shareVideo"></livePlayItem>
			</block>
			<block v-else>
				<swiper class="swiper" vertical :interval="interval" :duration="duration"
					@animationfinish="animationfinish">
					<swiper-item v-for="(item, index) in videoList" :key="index">
						<livePlayItem :initial_time="item.initial_time" :labelId="item.labelId" :live_id="item.live_id"
							:decoIndex="item.decoIndex" :index="item.index" :showHide="item.showHide"
							@shareVideo="shareVideo" ref="livePlayItem"></livePlayItem>
					</swiper-item>
				</swiper>
			</block>
		</view>
	</view>
</template>

<script>
	import livePlayItem from '../component/live/livePlayItem'

	export default {
		components: {
			livePlayItem
		},
		data() {
			return {
				initial_time: '',
				labelId: '',
				live_id: '',
				decoIndex: '',
				index: '',

				current: 0,
				videoList: [],
				interval: 2000,
				duration: 500,
				pageCurrent: 1,
				pageSize: 5,
				hasMore: true,
				loadEnd: false,
				shareVideoData: {}
			}
		},
		onLoad: function(options) {
			if (this.$Route.query.initial_time) {
				this.initial_time = Number(this.$Route.query.initial_time)
			}

			if (this.$Route.query.scene) {
				let url = decodeURIComponent(decodeURIComponent(this.$Route.query.scene))
				this.live_id = Number(url)
			} else {
				this.labelId = this.$Route.query.label_id ?Number(this.$Route.query.label_id) :0
				this.live_id = Number(this.$Route.query.live_id)
				this.decoIndex = Number(this.$Route.query.decoIndex)
				this.index = Number(this.$Route.query.index)
			}

			this.videoList.push({
				initial_time: this.initial_time,
				labelId: this.labelId,
				live_id: this.live_id,
				decoIndex: this.decoIndex || -1,
				index: this.index,
				showHide: 'show'
			})
			this.getVideoList()
		},
		onShow() {
			// #ifdef H5
			setTimeout(() => {
				this.playVideo()
			}, 500)
			// #endif
			// #ifdef MP
			setTimeout(() => {
				this.videoList[0].showHide = 'show'
			}, 1000)
			// #endif
		},
		onHide() {},

		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage(options) {
			let {
				shareVideoData
			} = this
			return {
				title: shareVideoData.liveName,
				path: '/extra/live/livePlay?live_id=' +
					shareVideoData.liveId +
					'&labelId=' +
					this.labelId,
				imageUrl: shareVideoData.liveCover
			}
		},

		onShareTimeline: function(options) {
			let {
				shareVideoData
			} = this
			return {
				title: shareVideoData.liveName,
				query: 'live_id=' +
					shareVideoData.liveId +
					'&labelId=' +
					shareVideoData.labelId,
				imageUrl: shareVideoData.liveCover
			}
		},

		methods: {
			// 获取短视频列表数据
			getVideoList() {
				let param = {}
				param.url = 'v3/video/front/video/live/liveList'
				param.method = 'GET'
				param.data = {}
				param.data.liveId = this.live_id
				param.data.labelId = this.labelId
				param.data.current = this.pageCurrent
				param.data.pageSize = this.pageCurrent == 1 ? 4 : this.pageSize
				this.$request(param).then((res) => {
					if (res.state == 200) {
						if (res.data && res.data.list.length > 0) {
							let arr = []
							res.data.list.forEach((item) => {
								arr.push({
									labelId: this.labelId,
									live_id: item.liveId,
									initial_time: '1',
									decoIndex: -1,
									index: -1,
									showHide: 'hide'
								})
							})
							this.videoList = this.videoList.concat(arr)
						}
						this.hasMore = this.$checkPaginationHasMore(res.data.pagination) //是否还有数据
						if (this.hasMore) {
							this.pageCurrent++
						}
					}
					this.loadEnd = true
				})
			},

			//设置短视频播放/暂停状态
			playVideo() {
				let _this = this
				// #ifdef H5
				let currentId = 'myVideo' + this.videoList[this.current].live_id // 获取当前视频id
				this.videoList[this.current].showHide = 'show'
				let current_video = videojs(currentId)
				current_video.play()
				// 获取视频列表
				this.videoList.forEach((item) => {
					// 获取json对象并遍历, 停止非当前视频
					let pauseId = 'myVideo' + item.live_id
					if (pauseId != currentId) {
						// 暂停其余视频
						if (item.showHide == 'show') {
							item.showHide = 'hide'
						}
						let other_video = videojs(pauseId)
						other_video.pause()
						other_video.currentTime(0)
					}
				})
				// #endif
				// #ifndef H5
				let currentId = 'sldVideo' + this.videoList[this.current].live_id // 获取当前视频id
				this.videoList.forEach((item, index) => {
					// 获取json对象并遍历, 停止非当前视频
					let pauseId = 'sldVideo' + item.live_id
					if (pauseId == currentId) {
						_this.videoList[index].showHide = 'show'
					} else {
						_this.videoList[index].showHide = 'hide'
					}
				})
				// #endif
			},

			animationfinish(e) {
				if (this.current != e.detail.current) {
					this.current = e.detail.current
					this.playVideo()
					if (this.pageCurrent == this.videoList.length && this.hasMore) {
						this.getVideoList()
					}
				}
			},

			shareVideo(liveDetail) {
				this.shareVideoData = liveDetail
			},

			updateCollect(author_id) {
				if (this.videoList.length == 1) {
					this.$refs.livePlayItem.updateCollect(author_id)
				} else {
					this.$refs.livePlayItem.forEach(ref => {
						ref.updateCollect(author_id)
					})
				}
			}
		}
	}
</script>
<style lang="scss">
	.swiper {
		position: relative;
		width: 100%;
		//去除tabbar高度
		height: 100vh;
		background: #000000;

		.swiper-item {
			.info {
				z-index: 1;
				position: absolute;
				bottom: 60upx;
				color: white;
				text-indent: 1em;
				font-size: 30upx;
			}

			.audio {
				position: absolute;
				bottom: 20upx;
				z-index: 1;
				text-indent: 1em;
				color: white;
				font-size: 30upx;
				display: flex;
				width: 100%;
				flex-direction: row;
				justify-content: space-between;
				align-items: flex-end;

				@-webkit-keyframes move {
					0% {
						left: 100vw;
					}

					100% {
						left: -50vw;
					}
				}

				@-moz-keyframes move {
					0% {
						left: 100vw;
					}

					100% {
						left: -50vw;
					}
				}

				@-ms-keyframes move {
					0% {
						left: 100vw;
					}

					100% {
						left: -50vw;
					}
				}

				@keyframes move {
					0% {
						left: 100vw;
					}

					100% {
						left: -50vw;
					}
				}

				.text-group {
					position: relative;
					width: 50vw;
					height: 40upx;

					overflow: hidden;

					.text {
						position: absolute;
						top: 0upx;
						white-space: nowrap;
						/*文本不会换行，文本会在在同一行上继续*/
						-webkit-animation: 10s move infinite;
						-moz-animation: 10s move infinite;
						-ms-animation: 10s move infinite;
						animation: 10s move infinite;
						width: 50vw;
						left: 100vw;
					}
				}

				.icon {
					width: 60upx;
					height: 60upx;
					border-radius: 60upx;
					animation: turn 3s linear infinite;
					margin-right: 5vw;
					border: 10upx solid white;
				}

				/* 
					  turn : 定义的动画名称
					  1s : 动画时间
					  linear : 动画以何种运行轨迹完成一个周期
					  infinite :规定动画应该无限次播放
					 */
				@keyframes turn {
					0% {
						-webkit-transform: rotate(0deg);
					}

					25% {
						-webkit-transform: rotate(90deg);
					}

					50% {
						-webkit-transform: rotate(180deg);
					}

					75% {
						-webkit-transform: rotate(270deg);
					}

					100% {
						-webkit-transform: rotate(360deg);
					}
				}
			}

			.video {
				width: 100%;
				z-index: 0;
				height: calc(100vh - 120rpx);
			}

			.buttons {
				display: flex;
				flex-direction: column;
				position: absolute;
				right: 5vw;
				bottom: 12vh;
				color: white;
				text-align: center;
				justify-content: center;
				z-index: 1;

				.header_group {
					margin-bottom: 50upx;
					height: 90upx;
					width: 90upx;
					position: relative;

					.header {
						border: 2px solid white;
						margin: 0 auto;
						border-radius: 90upx;
						height: 90upx;
						width: 90upx;
					}

					.add {
						position: absolute;
						bottom: -30upx;
						margin: 0 auto;
						right: 0upx;
						background-color: #f15b6c;
						left: 0upx;
						width: 50upx;
						height: 50upx;
						font-size: 50upx;
						line-height: 50upx;
						border-radius: 50upx;
					}
				}

				.button {
					text-align: center;
					font-size: 25upx;

					.icon {
						margin: 20upx;
						width: 60upx;
					}
				}
			}
		}
	}
</style>