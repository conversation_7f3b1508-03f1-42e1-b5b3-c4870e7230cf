<!-- 
	@zoucb 2023-05-15
	短视频app端播放页的评论组件
 -->  
<template>
	<view>
		<!-- 评论模块 start -->
		<view class="video_comment" v-if="showComment" @touchmove.stop.prevent="moveHandle">
			<view class="video_comment_title">
				<view class="video_comment_title_com_t_l">
					<text class="video_comment_title_com_t_l_com_t_l_title">{{Ls('全部评论')}}</text>
					<text class="video_comment_title_com_t_l_com_t_l_total">{{Ls('共有')}}{{commentNum}}{{Ls('条评论')}}</text>
				</view>
				<image class="video_comment_title_com_t_close" :src="imgUrl+'svideo/close.png'" @click="closeComment">
				</image>
			</view>

			<scroll-view class="video_comment_comment" scroll-y="true" @scrolltolower="getMoreCom">
				<block v-if="commentList.length>0">
					<view v-for="(items, index) in commentList" :key="index" class="video_comment_item_wrap">
						<view class="video_comment_item_wrap_item">
							<view class="video_comment_item_wrap_item_l">
								<view class="video_comment_item_wrap_item_l_com_avator">
									<image class="video_comment_item_wrap_item_l_com_avator_img" mode="aspectFill"
										:src="items.memberAvatar" />
								</view>
								<view class="video_comment_item_wrap_item_l_com_detail">
									<view @click="replyComment(items)">
										<text
											class="video_comment_item_wrap_item_l_com_detail_name">{{items.authorName?items.authorName:items.fromAuthorName}}</text>
										<text
											class="video_comment_item_wrap_item_l_com_detail_con">{{items.content}}</text>
									</view>
									<view class="video_comment_item_wrap_item_l_com_detail_other">
										<text
											class="video_comment_item_wrap_item_l_com_detail_other_time">{{items.createTime}}</text>
										<text v-if="items.isSelf"
											class="video_comment_item_wrap_item_l_com_detail_other_del_com"
											@click.stop="delCom(items,'comment')">{{Ls('删除')}}</text>
									</view>
								</view>
							</view>
							<view class="video_comment_item_wrap_item_r">
								<image class="video_comment_item_wrap_item_r_image" @click="likeComment(items,index)"
									:src="imgUrl+(items.isLike?'svideo/dz_complete.png':'svideo/dz.png')">
								</image>
								<text class="video_comment_item_wrap_item_r_text">{{items.likeNum}}</text>
							</view>
						</view>

						<view v-for="(itemReplay, index2) in items.replyList" class="video_comment_child" :index="index2">
							<view class="video_comment_child_item" v-if="index2<(replylimit==items.commentId?items.replyList.length:2)">
								<view class="video_comment_child_item_l">
									<view class="video_comment_child_item_l_avator">
										<image class="video_comment_child_item_l_avator_img" mode="aspectFill"
											:src="itemReplay.memberAvatar" />
									</view>
									<view class="video_comment_child_item_l_detail">
										<view @click="replyComment(itemReplay)">
											<text
												class="video_comment_child_item_l_detail_name">{{itemReplay.fromAuthorName}}</text>
											<view class="video_comment_child_item_l_detail_con_wrap">
												<text class="video_comment_child_item_l_detail_con">{{Ls('回复')}}</text>
												<text
													class="video_comment_child_item_l_detail_con_replay_name">{{itemReplay.toAuthorName}}:{{itemReplay.content}}</text>
											</view>
										</view>
										<view class="video_comment_item_wrap_item_l_com_detail_other">
											<text class="video_comment_item_wrap_item_l_com_detail_other_time">{{itemReplay.createTime}}</text>
											<text v-if="itemReplay.isSelf == 1"
												class="video_comment_item_wrap_item_l_com_detail_other_del_com"
												data-type="reply"
												@click.stop="delCom(itemReplay,'reply')">{{Ls('删除')}}</text>
										</view>
									</view>
								</view>
								<view class="video_comment_item_wrap_item_r">
									<image class="video_comment_item_wrap_item_r_image" @click="likeReply(itemReplay)"
										:src="imgUrl+(itemReplay.isLike?'svideo/dz_complete.png':'svideo/dz.png')">
									</image>
									<text class="video_comment_item_wrap_item_r_text">{{itemReplay.likeNum}}</text>
								</view>
							</view>
						</view>
						
						<!-- 查看更多回复 -->
						<view v-if="items.replyList.length>2" class="reply_pagnation" @click="getMoreReply(items)">
							<view class="reply_pagnation_left_line"></view>
							<text class="reply_pagnation_more_reply">{{replylimit==items.commentId?Ls('收起更多回复'):Ls('查看更多回复')}}</text>
							<view class="reply_pagnation_right_line"></view>
						</view>
					</view>
				</block>


				<!-- 数据加载完毕 -->
				<text v-if="!hasmore&&commentList.length>0" class="no-has-more" style="background-color: transparent">
					{{Ls('数据加载完毕~')}}
				</text>
				<!-- 数据加载中 -->

				<dataLoading :showFlag="hasmore&&loading" />

				<!-- 页面loading -->
				<view v-if="firstLoading" class="page_loading_child">
					<image class="page_loading_child_image" :src="imgUrl + 'live/page_loading_icon.gif'">
					</image>
				</view>
			</scroll-view>

			<view v-if="!firstLoading && !commentList.length" class="video_comment_empty">
				<view class="empty_data">
					<image class="empty_data_image" :src="imgUrl+'live/live_list_empty_icon.png'" mode="aspectFit">
					</image>
					<text class="empty_data_text">{{Ls('暂无数据')}}</text>
				</view>
			</view>

			<view class="replay">
				<view class="replay_input_wrap">
					<view v-if="replyInfo!=''" class="replay_reply_tip">
						<text class="replay_reply_text">{{Ls('回复')}}@</text>
						<text class="replay_reply_name">{{replyInfo.memberNickname?replyInfo.memberNickname:replyInfo.memberName}}</text>
					</view>
					<image v-if="replyInfo==''" class="replay_input_wrap_image" :src="imgUrl+'svideo/edit.png'"></image>
					<input type="text" cursor-spacing="10" :focus="showFocus" name="reply_con"
						class="replay_input_wrap_reply_inp" :placeholder="Ls('赶快发表你的评论吧~')"
						placeholder-style="replay_input_wrap_reply_inp_placeholder" confirm-type="send"
						@confirm="sendReplyComment" maxlength="100" v-model="input_val"></input>
				</view>
			</view>
		</view>
		<!-- 评论模块 end -->
		
	</view>
</template>

<script>
	import {checkPageHasMore} from "@/utils/live";
	import request from "@/utils/request";
	import dataLoading from "../dataLoading.vue";
	import dataLoaded from "../dataLoaded.vue";
	import {getCurLanguage} from '@/utils/base.js'
	import Config from '@/utils/config.js'
	const imgUrl = Config.imgUrl;
	let cur_time = 0; //记录发送评论的时间，由于微信小程序本身input的发送时间在swiper里面会触发两次，所以时间间隔规避一下这个问题
	import {mapState,mapMutations} from 'vuex';

	export default {
		components: {
			dataLoading,
			dataLoaded,
		},
		data() {
			return {
				loading: false, //数据加载状态
				imgUrl: Config.imgUrl, //图片地址
				commentList: [], // 评论列表
				replyInfo: '', //回复评论的数据

				showComment: false, //是否显示评论
				commentPn: 1, //当前页
				pageSize: 10, //每页数据
				hasmore: true, //是否还有评论数据
				input_val: '', //输入框评论内容
				showFocus: false, //评论回复输入框默认失去焦点
				firstLoading: true, //是否初次加载，是的话展示页面居中的loading效果，

				video_id: '',
				videoList: [], //视频列表
				isShow: false, //onshow事件开启监听
				Ls: getCurLanguage, //多语言变量在nvue里单独处理
				replylimit: -1
			};
		},
		computed: {
			...mapState(['hasLogin', 'cartData', 'userInfo'])
		},
		created() {
			console.log('created')
		},


		methods: {

			injectComment(info) {
				this.video_id = info.video_id
				this.label_id = info.label_id
				this.author_id = info.author_id
				this.commentNum = info.commentNum
				this.showComment = true
				this.getCommentList()
			},

			//评论模块隐藏键盘
			moveHandle() {
				this.showFocus = false;
				if (this.replyInfo) {
					this.replyInfo = '';
				}
				uni.hideKeyboard()
			},

			//关闭评论
			closeComment() {
				this.firstLoading = true;
				this.input_val = '';
				this.commentList = [];
				this.showComment = false;
			},

			// 查看更多回复
			getMoreReply(e) {
				let comment_id = e.commentId;
				if (this.replylimit == comment_id) {
					this.replylimit = -1
				} else {
					this.replylimit = comment_id
				}
			},


			//评论模块删除事件
			delCom(e, type) {
				let tmp_data = e
				uni.showModal({
					title: '',
					content: this.Ls(`确认删除该评论？`),
					confirmText: this.Ls('确定'),
					cancelText: this.Ls('取消'),
					success: res => {
						if (res.confirm) {
							if (type == 'comment') {
								this.delComment(tmp_data.commentId);
							} else {
								this.delApply(tmp_data.replyId, tmp_data.commentId);
							}
						}
					}
				});
			},
			// 删除回复
			delApply(replyId, commentId) {
				let commentList = this.commentList;
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/comment/delReply'
				param.method = 'POST'
				param.data.replyId = replyId
				request(param).then(res => {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					});
					if (res.state == 200) {
						let index = commentList.findIndex(el => el.commentId == commentId);
						let r_index = commentList[index].replyList.findIndex(el => el.replyId == replyId);
						commentList[index].replyList.splice(r_index, 1);
						this.commentList = commentList
					}
				})
			},
			// 删除评论
			delComment(commentId) {
				let commentList = this.commentList;
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/comment/del'
				param.method = 'POST'
				param.data.commentId = commentId
				request(param).then(res => {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					});
					if (res.state == 200) {
						this.commentNum = Number(this.commentNum) - 1;
						let index = commentList.findIndex(el => el.commentId == commentId);
						commentList.splice(index, 1);
						this.commentList = commentList
						this.$emit('updateComment',this.commentNum)
					}
				})
			},
			// 点赞评论
			likeComment(e, index) {
				let commentId = e.commentId;
				let commentList = this.commentList;
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/comment/clickPraise'
				param.method = 'POST'
				param.data.commentId = commentId
				if (this.hasLogin) {
					request(param).then(res => {
						if (res.state == 200) {
							let index = commentList.findIndex(el => el.commentId == commentId);
							let isLike = commentList[index].isLike;
							commentList[index].isLike = isLike == true ? false : true;
							commentList[index].likeNum = res.data;
							this.commentList = commentList
						} else {
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							});
						}
					})
				} else {
					this.goLogin()
				}
			},
			// 点赞回复
			likeReply(e) {
				let commentId = e.commentId;
				let replyId = e.replyId;
				let commentList = this.commentList;
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/comment/replyClickPraise'
				param.method = 'POST'
				param.data.replyId = replyId
				if (this.hasLogin) {
					request(param).then(res => {
						if (res.state == 200) {
							let index = commentList.findIndex(el => el.commentId == commentId);
							let r_index = commentList[index].replyList.findIndex(el => el.replyId == replyId);
							let isLike = commentList[index].replyList[r_index].isLike;
							commentList[index].replyList[r_index].isLike = isLike == true ? false : true;
							commentList[index].replyList[r_index].likeNum = res.data;
							this.commentList = commentList
						} else {
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							});
						}
					})
				} else {
					this.goLogin()
				}
			},
			//回复评论
			replyComment(e) {
				let tmp = e
				if (this.hasLogin) {
					this.replyInfo = {
							commentId: tmp.commentId,
							replyId: tmp.replyId,
							memberNickname: tmp.authorName || tmp.fromAuthorName
						},
						this.showFocus = true
				} else {
					uni.showToast({
						title: this.Ls('需要登录才能回复哦'),
						icon: 'none'
					});
				}
			},
			//评论功能
			sendReplyComment(e) {
				let _this = this
				if (!e.detail.value) {
					return
				}
				if (Date.parse(new Date()) - cur_time < 10) {
					return false;
				} else {
					cur_time = Date.parse(new Date());
				}
				if (!this.hasLogin) {
					this.goLogin()
					return;
				}

				let replyInfo = this.replyInfo;
				let video_id = this.video_id;
				let commentList = this.commentList;

				if (replyInfo == '') {
					//发布评论
					let param = {}
					param.data = {}
					param.url = 'v3/video/front/video/comment/publish'
					param.method = 'POST'
					param.data.content = e.detail.value
					param.data.videoId = this.video_id
					request(param).then(res => {
						if (res.state == 200) {
							this.commentNum = Number(this.commentNum) + 1;
							res.data.replyList = []
							commentList.unshift(res.data);
							//清空输入内容
							this.$emit('updateComment',this.commentNum)
							setTimeout(() => {
								this.input_val = ''
								this.showFocus = false
								this.commentList = JSON.parse(JSON.stringify(commentList))
							}, 0)

						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							});
						}
					})
				} else {
					//回复评论
					let param = {}
					param.data = {}
					param.url = 'v3/video/front/video/comment/reply'
					param.method = 'POST'
					param.data.content = e.detail.value
					param.data.commentId = replyInfo.commentId
					param.data.parentReplyId = replyInfo.replyId ? replyInfo.replyId : 0
					request(param).then(res => {
						if (res.state == 200) {
							let index = commentList.findIndex(el => el.commentId == replyInfo.commentId);
							commentList[index].replyList.push(res.data); //清空输入内容
							setTimeout(() => {
								this.input_val = ''
								this.replyInfo = ''
								this.showFocus = false
								this.commentList = commentList
								this.replyInfo = ''
							}, 0)
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							});
						}
					})
				}
			},

			//获取评论列表
			getCommentList() {
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/comment/list'
				param.method = 'GET'
				param.data.current = this.commentPn
				param.data.pageSize = this.pageSize
				param.data.videoId = this.video_id
				request(param).then(res => {
					if (res.state == 200) {
						let list = res.data.list;

						if (this.commentPn == 1) {
							this.commentList = list;
						} else {
							this.commentList = this.commentList.concat(list);
						}

						if (checkPageHasMore(res.data.pagination)) {
							this.commentPn++;
						} else {
							this.hasmore = false;
						} //初次加载的话更改状

						if (this.firstLoading) {
							this.firstLoading = false;
						}
					}
				})
			},

			//评论列表滑到底部加载数据
			getMoreCom() {
				if (this.hasmore) {
					this.getCommentList();
				}
			},
			
			goLogin(){
				let Route = {
					path:'/extra/svideo/videoPlayApp',
					query:{
						curLabelId:this.label_id,
						video_id:this.video_id,
						author_id:this.author_id,
					}
				}
				
				uni.showModal({
					title: '提示',
					content: '请登录',
					success: res => {
						if (res.confirm) {
							let url = Route.path;
							const query = Route.query;
							uni.setStorageSync('fromurl', {
								url,
								query
							});
							uni.navigateTo({
								url:'/pages/public/login'
							})
						}
					}
				});
			},
		}
	}
</script>


<style>
	/* 评论start */
	.video_comment {
		position: fixed;
		z-index: 100;
		left: 0;
		right: 0;
		bottom: 0;
		width: 750rpx;
		/* height: 90vw; */
		background-color: #fff;
		border-top-left-radius: 15rpx;
		border-top-right-radius: 15rpx;
		border-bottom-right-radius: 0;
		border-bottom-left-radius: 0;
	}

	.video_comment_title {
		display: flex;
		padding: 30rpx;
		flex-direction: row;
		justify-content: space-between;
		align-content: center;
		height: 100rpx;
		border-bottom-width: 1rpx;
		border-bottom-color: rgba(0, 0, 0, 0.1);
		border-bottom-style: solid;
	}

	.video_comment_title_com_t_l {
		display: flex;
		flex: 1;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
	}

	.video_comment_title_com_t_l_com_t_l_title {
		color: #2d2d2d;
		font-size: 32rpx;
		margin-right: 18rpx;
	}

	.video_comment_title_com_t_l_com_t_l_total {
		color: #949494;
		font-size: 22rpx;
	}

	.video_comment_title_com_t_close {
		width: 48rpx;
		height: 48rpx;
	}

	.video_comment_item_wrap {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
	}

	.video_comment_comment {
		width: 750rpx;
		height: 530rpx;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
		margin-top: 20rpx;
		/* position: relative; */
		padding-bottom: 100rpx;
	}

	.video_comment_item_wrap_item,
	.video_comment_child_item {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: flex-start;
		width: 690rpx;
		margin-left: 30rpx;
	}

	.video_comment_item_wrap_item_l,
	.video_comment_child_item_l {
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: flex-start;
		width: 600rpx;
	}

	.video_comment_item_wrap_item_l_com_avator {
		width: 43rpx;
		height: 43rpx;
		border-radius: 50%;
		margin-right: 18rpx;
		margin-top: 10rpx;
	}

	.video_comment_item_wrap_item_l_com_avator_img {
		width: 43rpx;
		height: 43rpx;
		border-radius: 50%;
	}

	.video_comment_child_item_l_avator {
		width: 30rpx;
		height: 30rpx;
		border-radius: 50%;
		margin-right: 18rpx;
		margin-top: 10rpx;
	}

	.video_comment_child_item_l_avator_img {
		width: 30rpx;
		height: 30rpx;
		border-radius: 50%;
	}

	.video_comment_item_wrap_item_l_com_detail {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
		flex: 1;
	}

	.video_comment_item_wrap_item_l_com_detail_name {
		color: #2d2d2d;
		font-size: 28rpx;
		margin-bottom: 12rpx;
	}

	.video_comment_item_wrap_item_l_com_detail_con {
		color: #2d2d2d;
		font-size: 26rpx;
		line-height: 39rpx;
		margin-bottom: 7rpx;
	}

	.video_comment_item_wrap_item_l_com_detail_other {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: flex-start;
		margin-bottom: 12rpx;
	}

	.video_comment_item_wrap_item_l_com_detail_other_time {
		color: #949494;
		font-size: 22rpx;
		margin-right: 20rpx;
	}

	.video_comment_item_wrap_item_l_com_detail_other_del_com {
		color: #4d8efb;
		font-size: 20rpx;
	}

	.video_comment_item_wrap_item_r {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: center;
	}

	.video_comment_item_wrap_item_r_image {
		width: 46rpx;
		height: 46rpx;
	}

	.video_comment_item_wrap_item_r_text {
		color: #949494;
		font-size: 20rpx;
		margin-top: -3rpx;
	}

	.video_comment_child {
		padding-left: 60rpx;
	}

	.video_comment_child_item_l_avator {
		width: 30rpx;
		height: 30rpx;
		margin-top: 0;
		margin-right: 10rpx;
	}

	.video_comment_child_item_l_detail_name {
		font-size: 26rpx;
		margin-bottom: 5rpx;
	}

	.video_comment_child_item_l_detail_con_wrap {
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: flex-start;
	}

	.video_comment_child_item_l_detail_con {
		font-size: 24rpx;
	}

	.video_comment_child_item_l_detail_con_replay_name {
		color: #949494;
		font-size: 24rpx;
	}

	.video_comment_child_item {
		width: 630rpx;
	}

	.video_comment_child_item_l {
		width: 550rpx;
	}

	.video_comment_empty {
		position: absolute;
		z-index: -1;
		left: 0;
		right: 0;
		bottom: 0;
		width: 750rpx;
		height: 750rpx;
	}

	.empty_data {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		background-color: transparent;
		width: 750rpx;
		margin-top: 350rpx;
	}

	.empty_data_image {
		width: 179rpx;
		height: 140rpx;
	}

	.empty_data_text {
		font-size: 28rpx;
		margin-top: 20rpx;
		color: #949494;
	}

	.reply_pagnation {
		width: 750rpx;
		display: flex;
		flex-direction: row;
		justify-content: space-around;
		align-items: center;
		height: 40rpx;
		padding-left: 110rpx;
		padding-right: 90rpx;
	}

	.reply_pagnation_left_line,
	.reply_pagnation_right_line {
		height: 1rpx;
		background-color: rgba(0, 0, 0, 0.1);
		width: 166rpx;
	}

	.reply_pagnation_more_reply {
		color: #2d2d2d;
		font-size: 22rpx;
	}

	.page_loading_child {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 750rpx;
		height: 750rpx;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		background-color: transparent;
	}

	.page_loading_child_image {
		width: 60rpx;
		height: 60rpx;
	}

	.no-has-more {
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		height: 80rpx;
		text-align: center;
		color: #949494;
		font-size: 22rpx;
		margin: 0 auto;
		width: 750rpx;
		margin-top: 30rpx;
	}

	.no-has-more::after,
	.no-has-more::before {
		position: absolute;
		content: '';
		width: 166rpx;
		height: 1rpx;
		background-color: rgba(0, 0, 0, 0.1);
		top: 50%;
		transform: translateY(-50%) scaleY(0.5);
	}

	.no-has-more::after {
		right: 114rpx;
	}

	.no-has-more::before {
		left: 114rpx;
	}

	.replay {
		position: absolute;
		z-index: 4;
		height: 100rpx;
		width: 750rpx;
		padding: 0 30rpx;
		background-color: #fff;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		left: 0;
		right: 0;
		bottom: 0;
		box-shadow: 0px 0px 20px 0px rgba(86, 86, 86, 0.08);
	}

	.replay_input_wrap {
		background-color: #ddd;
		border-radius: 6px;
		width: 690rpx;
		height: 57rpx;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
	}

	.replay_input_wrap_reply_inp {
		display: flex;
		flex: 1;
		font-size: 24rpx;
		/* app-1-start */
		/* #ifdef APP-PLUS */
		margin-left: -10rpx;
		/* #endif */
		/* app-1-end */
	}

	.replay_input_wrap_reply_inp_placeholder {
		font-size: 24rpx;
		color: #949494;
	}

	.replay_input_wrap_image {
		width: 47rpx;
		height: 47rpx;
		margin: 0 10rpx;
	}

	.replay_reply_tip {
		color: #2d2d2d;
		font-size: 24rpx;
		padding: 0 15rpx;
		display: flex !important;
		flex-direction: row;
		lines: 1;
	}

	.replay_reply_text {
		font-size: 24rpx;
		color: #2d2d2d;
		margin-right: 10rpx;
		line-height: 26rpx;
	}

	.replay_reply_name {
		color: #4d8efb;
		font-size: 24rpx;
		max-width: 130rpx;
		text-overflow: ellipsis;
		line-height: 26rpx;
		lines: 1;
	}

	/* 评论end */
</style>