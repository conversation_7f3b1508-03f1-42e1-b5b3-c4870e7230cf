<template>
	<view>
		<!-- #ifdef H5 -->
		<view class="video_wrap" ref="videoWrapHls"></view>
		<!-- #endif -->
		<view class="video_play" v-if="videoDetail">
			<view class="wrap">
				<view class="live_header">
					<image mode="scaleToFill" @tap="goBack" class='go_back' :src="imgUrl+'store/white_arrow_l.png'" />
					<view @tap="goLiveUserCenter" style="display: flex;" :data-authorId="authorDetail.authorId">
						<view class="avator" :data-authorId="authorDetail.authorId"
							:style="'background-image:url('+member_avatar+');'+bgStyle+';'"></view>
						<view class="mem_info" :data-authorId="authorDetail.authorId">
							<text
								class="name">{{authorDetail.memberNickname?authorDetail.memberNickname:authorDetail.memberName}}</text>
							<view class="stat_num">
								<text class="click_num" style="margin-right: 20rpx"
									v-if="authorDetail">{{authorDetail.fansNum}}{{$L('粉丝')}}</text>
							</view>
						</view>
					</view>

					<view v-if="!videoDetail.isSelf" class="live_fllow" @tap="collect"
						:style="'background:' + (authorDetail.isFollow == true?'#999':'#fc1c1c')">
						<image v-if="authorDetail.isFollow == false" :src="imgUrl+'svideo/fav_a.png'"></image>
						<text>{{authorDetail.isFollow == true?$L('已关注'):$L('关注')}}</text>
					</view>
				</view>

				<view v-if="!videoPlay" class="live_pause" @tap.stop="chansgedd">
					<image class="live_pause_img" :src="imgUrl + 'svideo/svideo_play.png'" mode="aspectFit" lazy-load></image>
				</view>

				<!-- #ifdef MP -->
				<video :id="'sldVideo_child'+video_id" class="live_back" :src="videoDetail.videoPath" loop
					:controls="true" :autoplay="false" :show-fullscreen-btn="false" show-progress="" object-fit="contain"
					:show-center-play-btn="false" enable-play-gesture @play="bindplay" @pause="bindpause">
				</video>
				<!-- #endif -->

				<view class="right_control">
					<block v-if="videoDetail.state==2">
						<view class="" @tap="like">
						  <svgGroup type="collected" width="68" height="68" px="rpx" :color="diyStyle_var['--color_video_main']"
							v-if="videoDetail.isLike == true"></svgGroup>
						  <svgGroup type="collected" width="68" height="68" px="rpx" color="#fff"
							v-else></svgGroup> 
						</view>
						<text>{{videoDetail.likeNum}}</text>
						<image :src="imgUrl+'svideo/play_comment.png'" @tap="openCommnet"></image>
						<text>{{(videoDetail.commentNum && videoDetail.commentNum > 0) ? videoDetail.commentNum : $L('评论')}}</text>
						<!-- #ifdef  MP -->
						<button open-type="share" class="share_btn">
							<image :src="imgUrl+'svideo/share.png'"></image>
							<view>{{$L('分享')}}</view>
						</button>
						<!-- #endif -->
					</block>
					<!-- #ifdef H5 -->
					<block v-if="isWeiXinBrower&&videoDetail.state==2">
						<button @tap="showShare" class="share_btn">
							<image :src="imgUrl+'svideo/share.png'"></image>
						</button>
						<text>分享</text>
					</block>
					<!-- #endif -->
					<block v-if="videoDetail.isSelf == 1">
						<image :src="imgUrl+'svideo/del_video.png'" @tap="del_video"></image>
						<text>{{$L('删除')}}</text>
					</block>
					<!-- 进入直播个人主页 -->
					<view class="personal_homepage" @tap="gPensonalCenter" data-curTab="video">
						<image class="personal_homepage_image" :src="my_avatar"></image>
					</view>
				</view>

				<view class="select-wrap" catchtouchmove="touchmoveshare" v-if="shareWrap">
					<view class="share-mode">
						<view class="share-img"></view>
						<view class="ul">
							<!-- #ifdef  H5 -->
							<button @tap.stop="sldShareBrower(1)" class="item" v-if="isWeiXinBrower">
								<image :src="imgUrl+'goods_detail/wx_share.png'" mode="widthFix"></image>
								<text>{{$L('微信好友')}}</text>
							</button>
							<button @tap.stop="sldShareBrower(2)" class="item" v-if="isWeiXinBrower">
								<image :src="imgUrl+'svideo/pyq_share.png'" mode="widthFix"></image>
								<text>{{$L('微信朋友圈')}}</text>
							</button>
							<!-- #endif -->

							<!-- #ifdef MP -->
							<button class="item" open-type="share" @click="shareWrap=false">
								<image :src="imgUrl+'goods_detail/wx_share.png'" mode="widthFix"></image>
								<text>{{$L('微信好友')}}</text>
							</button>
							<!-- #endif -->
						</view>

						<view class="close" @tap="closeShare">
							<image :src="imgUrl+'svideo/share_close2.png'" mode="aspectFit"></image>
						</view>
					</view>
				</view>
				<view class="wx_brower_share_mask" v-if="showWeiXinBrowerTip">
					<view class="wx_brower_share_top_wrap">
						<image :src="imgUrl+'wx_share_tip.png'" mode="widthFix" @tap="closeShare"
							class="wx_brower_share_img"></image>
					</view>
				</view>

				<view class="video_footer">
					<text class="title">{{videoDetail.videoName}}</text>
					<text class="desc">{{videoDetail.introduction}}</text>
					<!-- #ifndef MP -->
					<view v-if="goodsList.length == 0" style="width:100%;height:20rpx"></view>
					<!-- #endif -->
					<scroll-view scroll-x="true" class="video_goods" v-if="goodsList.length > 0">
						<view v-for="(item, index) in goodsList" :key="index" class="video_goods_item">
							<view class="goods_img" :data-goodsId="item.goodsId" @tap="goGoodsDetail(item)">
								<image :src="item.goodsImage" mode="aspectFit"></image>
							</view>
							<view class="goods_detail">
								<text class="goods_name" :data-goodsId="item.goodsId"
									@tap="goGoodsDetail(item)">{{item.goodsName}}</text>
								<view class="goods_info">
									<text class="goods_price">{{$L('¥')}}{{item.goodsPrice}}</text>
									<image class="add_cart" :data-goodsId="item.goodsId" @tap="addCart(item)"
										:src="imgUrl+'svideo/add_cart.png'"></image>
								</view>
							</view>
						</view>
					</scroll-view>
				</view>

				<uni-popup ref="commentModel" type="bottom" @touchmove.stop.prevent="moveHandle">
					<!-- 评论模块 start -->
					<view class="video_comment" @tap="closeCommet">
						<view class="title">
							<view class="com_t_l">
								<text class="com_t_l_title">{{$L('全部评论')}}</text>
								<text class="com_t_l_total">{{$L('共有')}}{{videoDetail.commentNum}}{{$L('条评论')}}</text>
							</view>
							<image class="com_t_close" :src="imgUrl+'svideo/close.png'" @tap="closeComment">
							</image>
						</view>

						<scroll-view class="comment" scroll-y="true" @scrolltolower="getMoreCom">
							<view v-for="(item, index) in commentList" :key="index" class="com_item_wrap" :index="index"
								v-if="commentList.length>0">
								<view class="com_item">
									<view class="com_item_l">
										<view class="com_avator"
											:style="'background-image:url('+item.memberAvatar+');' + bgStyle"
											@click="intoUser(item.authorId?item.authorId:item.fromAuthorId)"></view>
										<view class="com_detail" :data-commentId="item.commentId"
											:data-authorId="item.authorId?item.authorId:item.fromAuthorId"
											:data-memberNickname="item.authorName?item.authorName:item.fromAuthorName"
											@tap.stop="replyComment">
											<text
												class="com_name">{{item.authorName?item.authorName:item.fromAuthorName}}</text>
											<text class="com_con">{{item.content}}</text>
											<view class="com_other">
												<text class="com_time">{{item.createTime}}</text>
												<text v-if="item.isSelf" class="del_com" data-type="comment"
													@tap.stop="delCom"
													:data-commentId="item.commentId">{{$L('删除')}}</text>
											</view>
										</view>
									</view>
									<view class="com_item_r">
										<view class="" :data-commentid="item.commentId" @tap="likeComment">
											  <svgGroup type="dianzan" width="40" height="40" px="rpx" v-if="item.isLike"
												   :color="diyStyle_var['--color_video_main']"></svgGroup>
											  <svgGroup type="dianzan" width="40" height="40" px="rpx" v-else color="#dedede"></svgGroup>
										</view>
										<text>{{item.likeNum}}</text>
									</view>
								</view>

								<view v-for="(itemReplay, index2) in item.replyList" :key="index2" class="child"
									:index="index2" v-if="item.replyList.length>0">
									<view class="com_item" v-if="index2<item.limit">
										<view class="com_item_l">
											<view class="com_avator"
												:style="'background:url('+itemReplay.memberAvatar+');' + bgStyle">
											</view>
											<view class="com_detail" :data-commentId="item.commentId"
												:data-replyId="itemReplay.replyId"
												:data-memberNickname="itemReplay.fromAuthorName"
												@tap.stop="replyComment">
												<text class="com_name">{{itemReplay.fromAuthorName}}</text>
												<text class="com_con">{{$L('回复')}}<text
														class="replay_name">{{itemReplay.toAuthorName!=undefined?itemReplay.toAuthorName:""}}：</text>{{itemReplay.content}}
												</text>
												<view class="com_other">
													<text class="com_time">{{itemReplay.createTime}}</text>
													<text v-if="itemReplay.isSelf == 1" class="del_com"
														data-type="reply" @tap.stop="delCom"
														:data-commentId="item.commentId"
														:data-replyId="itemReplay.replyId">{{$L('删除')}}</text>
												</view>
											</view>
										</view>
										<view class="com_item_r">
											<view class="" :data-commentid="item.commentId" :data-replyid="itemReplay.replyId" @tap="likeReply">
											  <svgGroup type="dianzan" width="40" height="40" px="rpx" v-if="itemReplay.isLike"
												   :color="diyStyle_var['--color_video_main']"></svgGroup>
											  <svgGroup type="dianzan" width="40" height="40" px="rpx" v-else color="#dedede"></svgGroup>
											</view>
											<text>{{itemReplay.likeNum}}</text>
										</view>
									</view>
								</view>
								<!-- 查看更多回复 -->
								<view v-if="item.replyList.length>item.limit&&item.isFoldReply" class="reply_pagnation"
									@tap.stop="getMoreReply(item)" :data-commentId="item.commentId"
									:data-rpn="item.rpn">
									<view class="left_line"></view>
									<text class="more_reply">{{$L('查看更多回复')}}</text>
									<view class="right_line"></view>
								</view>
								<!-- 收起更多回复 -->
								<view v-if="item.replyList.length==item.limit&&!item.isFoldReply"
									class="reply_pagnation" @tap.stop="closeMoreReply(item)"
									:data-commentId="item.commentId">
									<view class="left_line"></view>
									<text class="more_reply">{{$L('收起更多回复')}}</text>
									<view class="right_line"></view>
								</view>
							</view>

							<!-- 数据加载完毕 -->
							<dataLoaded :showFlag="!hasmore&&commentList.length>0" />

							<!-- 数据加载中 -->
							<dataLoading :showFlag="hasmore&&loading" />

							<!-- 页面loading -->
							<view class="page_loading_child" v-if="firstLoading">
								<image :src="imgUrl + 'svideo/page_loading_icon.gif'"></image>
							</view>

						</scroll-view>

						<view class="comment_empty" v-if="!firstLoading&&!commentList.length">
							<!-- 页面空数据 -->
							<emptyData :emptyIcon="imgUrl+'svideo/live_list_empty_icon.png'"
								:showFlag="!firstLoading&&!commentList.length" />
						</view>

						<view class="replay">
							<view class="input_wrap" @click.stop="showFocus=true">
								<text v-if="replyInfo!=''" class="reply_tip">{{$L('回复')}}@<text
										class="reply_name">{{replyInfo.memberNickname?replyInfo.memberNickname:replyInfo.memberName}}</text></text>
								<image v-if="replyInfo==''" :src="imgUrl+'svideo/edit.png'"></image>
								<input type="text" cursor-spacing="30" :focus="showFocus" name="reply_con"
									class="reply_inp" :placeholder="$L('赶快发表你的评论吧~')"
									placeholder-style="font-size:24rpx;color:#949494" confirm-type="send"
									@confirm="sendReplyComment" maxlength="100" v-model="input_val"></input>
							</view>
						</view>
					</view>
					<!-- 评论模块 end -->
				</uni-popup>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		checkPageHasMore
	} from "@/utils/live";
	import request from "@/utils/request";
	import pageLoading from "../../component/pageLoading.vue";
	import emptyData from "../../component/emptyData.vue";
	import dataLoading from "../../component/dataLoading.vue";
	import dataLoaded from "../../component/dataLoaded.vue";
	import uniPopup from '@/components/uni-popup/uni-popup.vue'
	import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue'
	const imgUrl = getApp().globalData.imgUrl;
	let cur_time = 0; //记录发送评论的时间，由于微信小程序本身input的发送时间在swiper里面会触发两次，所以时间间隔规避一下这个问题
	import {
		mapState,
		mapMutations
	} from 'vuex';
	export default {
		components: {
			pageLoading,
			emptyData,
			dataLoading,
			dataLoaded,
			uniPopup,
			uniPopupDialog
		},
		data() {
			return {
				loading: false, //数据加载状态 
				videoDetail: '', //短视频详情
				authorDetail: '', //短视频作者详情
				imgUrl: getApp().globalData.imgUrl, //图片地址
				bgStyle: 'background-size:contain;background-position:center center;background-repeat: no-repeat;', //背景图片样式
				goodsList: [], //短视频绑定的商品
				commentList: [], // 评论列表
				hasmore: true, //是否还有数据，用于页面展示
				input_val: '', //输入框内容
				firstLoading: true, //是否初次加载，是的话展示页面居中的loading效果，
				showFocus: false, //评论回复输入框默认失去焦点
				replyInfo: '', //回复评论的数据
				showComment: false, //是否显示评论
				pn: 1, //当前页
				pageSize: 10, //每页数据
				autoplay: true, //是否自动播放 
				key: '', //登录的key值
				member_avatar: '', //默认作者头像
				shareWrap: false,
				isWeiXinBrower: false, //是否微信浏览器
				showWeiXinBrowerTip: false, //微信浏览器分享的提示操作
				stat_end: 1, //终端，默认为1，pc端,
				isFoldIndex: -1,
				pauOrPl: true,
				videoPlay: true, //视频是否播放--用户判断暂停按钮显示
				my_avatar:''
			};
		},
		computed: {
			...mapState(['hasLogin', 'cartData', 'userInfo'])
		},
		props: {
			video_id: {
				//短视频id
				type: Number,
				default: null
			},
			activeIndex: {
				//当前播放的短视频id
				type: Number,
				default: null
			},
			author_id: {
				//当前作者id
				type: Number,
				default: null
			},
			showHide: {
				type: String,
				default: 'hide'
			},
			preUpdateIndex: {
				type: String,
				default: ''
			},
		},

		watch: {
			video_id: {
				handler(nv, ov) {
					if (nv != ov) {
						// #ifndef MP-WEIXIN
						this.initData();
						// #endif
					}
				},
				deep: true
			},
			//wx-1-start
			// #ifdef MP-WEIXIN
			showHide: {
				handler() {
					if (this.showHide == 'show') {
						uni.createVideoContext('sldVideo_child'+this.video_id,this).play();
					} else {
						uni.createVideoContext('sldVideo_child'+this.video_id,this).pause();
					}
				},
				deep: true
			},
			// #endif
			//wx-1-end
		},

		beforeMount() {
		},

		mounted() {
			let {
				video_id
			} = this;

			// #ifdef H5
			this.isWeiXinBrower = this.$isWeiXinBrower();
			// #endif

			this.initData();
			this.getPlatform();
		},

		beforeDestroy() {
			// #ifdef H5
			if (this.player_video) {
				this.player_video.dispose()
			}
			// #endif
		},

		methods: {
			subnvueBackPre() {
				uni.getSubNVueById('videoPlayAppCon').hide();
			},
			//更新视频点击量
			updateVideoClick() {
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/updateClickNum'
				param.method = 'POST'
				param.data.videoId = this.video_id
				this.$request(param).then(res => {
					if (res.state == 200) {}
				})
			},
			//获取当前终端的方法
			getPlatform() {
				//判断终端
				this.stat_end = 1;
				// #ifdef H5
				this.stat_end = 2;
				// #endif
				//wx-2-start
				// #ifdef MP-WEIXIN
				this.stat_end = 3;
				// #endif
				//wx-2-end
			},
			//初始化数据
			initData() {
				this.getVideoInfo();
			},

			intoUser(authorId) {
				this.$Router.push({
					path: '/extra/user/my',
					query: {
						author_id: authorId
					}
				})
			},

			//获取短视频详情
			getVideoInfo() {
				let {
					video_id,
					videoDetail,
					member_avatar
				} = this;
				let _this = this
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/playingPage'
				param.method = 'GET'
				param.data.videoId = this.video_id
				this.$request(param).then(res => {
					if (res.state == 200) {
						this.goodsList = res.data.goodsList;
						this.videoDetail = res.data.videoInfo;
						this.$emit('showVideoInfo', this.videoDetail)
						this.member_avatar = res.data.authorInfo.memberAvatar
						this.my_avatar = res.data.memberAvatar
						if (this.videoDetail.state == 2) { //更新视频点击量
							this.updateVideoClick()
						}
						if (this.videoDetail.state == 3) {
							uni.showModal({
								title: '',
								content: _this.$L('审核失败，') + this.videoDetail.remark,
								showCancel: true,
								confirmText: _this.$L('重新编辑'),
								confirmColor: this.diyStyle_var['--color_main'],
								success: res => {
									if (res.confirm) {
										_this.$Router.replace({
											path: '/extra/svideo/svideoRelease',
											query: {
												video_id: this.video_id,
												edit: true
											}
										})
									} else {
										_this.$Router.back(1);
									}
								}
							});
						}
						this.authorDetail = res.data.authorInfo;
						// #ifdef H5
						this.appendVideo()
						// #endif 
					} else if (res.state == 267) {
						uni.$emit('updateState')
						uni.showModal({
							title: _this.$L('提示'),
							content: res.msg,
							showCancel: false,
							success: res => {
								if (res.confirm) {
									_this.$Router.back(1)
								}
							}
						});
					}
				})
			},

			appendVideo() {
				let _this =this
				var video = document.createElement('video');
				video.id = 'myVideo'+this.video_id;
				video.style = `max-width:100vw;max-height:100vh;`;
				video.setAttribute('class','video-js vjs-default-skin vjs-big-play-centered');
				video.setAttribute('webkit-playsinline', true);
				video.setAttribute('playsinline', true);
				video.setAttribute('x5-video-player-type', 'h5');
				video.setAttribute('x5-video-player-fullscreen', true);
				video.setAttribute('x-webkit-airplay', true);
				video.setAttribute('loop', true);
				this.$refs.videoWrapHls.$el.appendChild(video);
				this.player_video = videojs('myVideo'+this.video_id, {
					controls: true,
					controlBar: {
						children: [{
								name: 'playToggle'
							}, 
							{
								name: 'progressControl'
							}, // 播放进度条
						]
					},
					autoplay: false,
					preload: 'auto',
					poster: this.videoDetail.videoImage,
					sources: [{
						src: this.videoDetail.videoPath,
						type: "video/mp4"
					}]
				}, function onPlayerReady() {
					this.on("touchend",function(){
						let currentId = 'myVideo' + _this.video_id;
						let current_video = videojs(currentId)
						if (_this.videoPlay) {
							_this.videoPlay = false;
							current_video.pause();
						} else {
							_this.videoPlay = true;
							current_video.play();
						}
					})
					this.on("play", function(){
						_this.videoPlay = true;
					});
					this.on("pause", function(){
						_this.videoPlay = false;
					});
				})
			},

			//获取评论列表
			getCommentList() {
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/comment/list'
				param.method = 'GET'
				param.data.pageSize = this.pageSize
				param.data.current = this.pn
				param.data.videoId = this.video_id
				this.$request(param).then(res => {
					if (res.state == 200) {
						let list = res.data.list;
						list.forEach(el => el.rpn = 1);

						if (this.pn == 1) {
							this.commentList = list;
						} else {
							this.commentList = this.commentList.concat(list);
						}

						if (checkPageHasMore(res.data.pagination)) {
							this.pn++;
						} else {
							this.hasmore = false;
						} //初次加载的话更改状
						this.commentList.map((item) => {
							this.$set(item, 'limit', 3)
							this.$set(item, 'isFoldReply', true)
						})
						if (this.firstLoading) {
							this.firstLoading = false;
						}

						this.loading = false
					}
				})
			},

			//评论列表滑到底部加载数据
			getMoreCom() {
				if (this.hasmore) {
					this.getCommentList();
				}
			},

			// 返回上级页面
			goBack() {
				this.$Router.back(1)
			},

			//关注、取消关注事件
			collect(e) {
				let {
					authorDetail
				} = this
				if (this.hasLogin) {
					let param = {}
					param.data = {}
					param.method = 'POST'
					param.data.authorId = this.authorDetail.authorId
					if (this.authorDetail.isFollow) {
						param.url = 'v3/video/front/video/cancelFollow'
					} else {
						param.url = 'v3/video/front/video/followAuthor'
					}
					this.$request(param).then(res => {
						if (res.state == 200) {
							this.authorDetail.isFollow = this.authorDetail.isFollow == true ? false : true;
							// if (this.authorDetail.isFollow) {
							// 	this.authorDetail.fansNum += 1
							// } else {
							// 	this.authorDetail.fansNum -= 1
							// }
							this.getVideoInfo()
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							});
						}
					})
				} else {
					getApp().globalData.goLogin(this.$Route);
				}
			},

			//进入作者页面-监听事件处理
			onHandleGoLiveUserCenter(data) {
				if (data.pageLength > 4) {
					this.$Router.replace(data.url)
				} else {
					this.$Router.push(data.url)
				}
			},

			//进入作者页面
			goLiveUserCenter(e) {
				let author_id = e.currentTarget.dataset.authorid;
				let page = getCurrentPages();
				let len = page.length;
				if (len > 4) {
					this.$Router.replace({
						path: '/extra/user/my',
						query: {
							author_id
						}
					})
				} else {
					this.$Router.push({
						path: '/extra/user/my',
						query: {
							author_id
						}
					})
				}
			},

			//分享点击事件
			showShare() {
				this.shareWrap = true
			},

			//关闭分享
			closeShare() {
				this.shareWrap = false
				this.showWeiXinBrowerTip = false //微信浏览器提示层
			},

			//分享 type：分享类型 0 图文 2图片，scene 场景 WXSceneSession：分享朋友  WXSenceTimeline：分享朋友圈
			sldShare(type, scene) {
				let {
					videoDetail,
					video_id
				} = this;
				let shareData = {};
				if (type == 0) {
					shareData.href = getApp().globalData.apiUrl + 'extra/svideo/svideoPlay?video_id=' + video_id;
					shareData.title = videoDetail.videoName;
					shareData.summary = this.$L('我正在看') + this.authorDetail.memberNickname ? this.authorDetail.memberNickname : this
						.authorDetail
						.memberName + this.$L('的精彩内容，快来围观~');
					shareData.imageUrl = videoDetail.videoImage;
				}
				this.$weiXinAppShare(type, scene, shareData);
				this.closeShare(); //关闭分享
			},

			//浏览器分享
			sldShareBrower(type) {
				this.showWeiXinBrowerTip = true;
				this.shareWrap = false;
				let {
					videoDetail,
					video_id
				} = this;

				//微信浏览器分享
				this.$WXBrowserShareThen(type, {
					title: videoDetail.videoName,
					desc: `${this.$L('我正在看')}${this.authorDetail.memberNickname ? this.authorDetail.memberNickname : this.authorDetail.memberName}${this.$L('的精彩内容，快来围观~')}`,
					link: getApp().globalData.apiUrl + 'extra/svideo/svideoPlay?video_id=' + video_id,
					imgUrl: videoDetail.videoImage[0],
				});
			},

			//跳转商品详情页
			goGoodsDetailNew(data) {
				if (data.pageLength > 4) {
					this.$Router.replace(data.url)
				} else {
					this.$Router.push(data.url)
				}
			},

			//跳转商品详情页
			goGoodsDetail(good) {
				let page = getCurrentPages();
				let len = page.length;

				if (len > 4) {
					this.$Router.replace({
						path: '/standard/product/detail',
						query: {
							productId: good.defaultProductId,
							goodsId: good.goodsId,
							videoId: this.video_id
						}
					})
				} else {
					this.$Router.push({
						path: '/standard/product/detail',
						query: {
							productId: good.defaultProductId,
							goodsId: good.goodsId,
							videoId: this.video_id
						}
					})
				}
			},

			//加入购物车事件
			addCart(good) {
				if (this.videoDetail.state == 1) {
					uni.showToast({
						title: this.$L('该作品正在审核中'),
						icon: 'none'
					})
					return;
				} else if (this.videoDetail.state == 3) {
					uni.showToast({
						title: this.$L('该作品审核不通过'),
						icon: 'none'
					})
					return;
				}
				if (!this.hasLogin) {
					let cart_list = {
						storeCartGroupList: [{
							promotionCartGroupList: [{
								cartList: [{
									buyNum: 1,
									goodsId: good.goodsId - 0,
									productId: good.defaultProductId,
									productImage: good.goodsImage,
									goodsName: good.goodsName,
									isChecked: 1,
									productPrice: good.goodsPrice,
									productStock: good.goodsStock
								}],
							}],
							storeId: good.storeId,
							storeName: good.storeName,
							checkedAll: true
						}],
						checkedAll: true,
						invalidList: []
					}
					//未登录加入本地缓存
					let local_cart_list = uni.getStorageSync('cart_list') //购物车列表本地缓存
					if (local_cart_list) { //如果不是第一次存储
						let tmp_list1 = []
						let tmp_list2 = []
						cart_list.storeCartGroupList.forEach(item => {
							item.promotionCartGroupList.forEach(item1 => {
								item1.cartList.forEach(item2 => {
									local_cart_list.storeCartGroupList.forEach(v => {
										v.promotionCartGroupList.forEach(v1 => {
											v1.cartList.forEach(v2 => {
												if (v2.productId == item2
													.productId && v
													.storeId == item
													.storeId) {
													tmp_list1.push(v)
												}
											})
										})
									})
									tmp_list2 = local_cart_list.storeCartGroupList.filter(v => {
										return v.storeId == item.storeId
									})
								})
							})
						})
						
						if (tmp_list1.length > 0 && tmp_list2.length > 0) { //同一店铺同一商品
							let storeId = tmp_list2[0].storeId
							local_cart_list.storeCartGroupList.map(item => {
								item.promotionCartGroupList.map(item1 => {
									item1.cartList.map(item2 => {
										if (item2.productId == this.productId && item.storeId ==storeId) {
											item2.buyNum += this.currentSpecNum
										}
									})
								})
							})
						} else if (tmp_list1.length == 0 && tmp_list2.length > 0) { //同一店铺不同商品
							let storeId = tmp_list2[0].storeId
							local_cart_list.storeCartGroupList.map(item => {
								if (item.storeId == storeId) {
									item.promotionCartGroupList.map(item2 => {
										item2.cartList.push(cart_list.storeCartGroupList[0]
											.promotionCartGroupList[0].cartList[0])
									})
								}
							})
						} else { //不同店铺不同商品
							local_cart_list.storeCartGroupList.push(cart_list.storeCartGroupList[0])
						}
						
						// 未登录购物车展示数据
						uni.setStorage({
							key: 'cart_list',
							data: local_cart_list,
							success: function() {
								//更新购物车数量和购物车数据
							}
						});
			
					} else {
						uni.setStorage({
							key: 'cart_list',
							data: cart_list,
							success: function() {
								//更新购物车数量和购物车数据
							}
						});
					}
					uni.showToast({
						title: this.$L('加入购物车成功！'),
						icon: 'none',
						duration: 700
					})
				} else { //已登录
					request({
						url: 'v3/business/front/cart/add',
						data: {
							productId: good.defaultProductId,
							number: 1,
						},
						method: 'POST'
					}).then(res => {
						if (res.state == 200) {
							//更新购物车数量
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}).catch((e) => {})
				}
			},
			

			//删除视频提示
			del_video() {
				uni.showModal({
					title: this.$L('提示'),
					content: this.$L(`确定删除该视频？`),
					confirmText: this.$L('确定'),
					cancelText:this.$L('取消'),
					success: res => {
						if (res.confirm) {
							this.delVideo();
						}
					}
				});
			},

			//删除视频
			delVideo() {
				let {
					video_id
				} = this;
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/delVideo'
				param.method = 'POST'
				param.data.videoId = this.video_id
				request(param).then(res => {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					});
					if (res.state == 200) {
						//返回上一页
						uni.$emit('updateState')
						this.$Router.back(1)
					}
				})
			},

			//点赞事件
			like() {
				if (this.videoDetail.state != 2) {
					uni.showToast({
						title: this.$L('该视频未审核通过,不能点赞哦～'),
						icon: 'none'
					})
					return
				}
				let {
					video_id,
					videoDetail
				} = this;
				if (this.hasLogin) {
					let param = {}
					param.data = {}
					param.url = 'v3/video/front/video/clickPraise'
					param.method = 'POST'
					param.data.videoId = this.video_id
					this.$request(param).then(res => {
						if (res.state == 200) {
							if (res.state == 200) {
								videoDetail.isLike = videoDetail.isLike == true ? false : true;
								videoDetail.likeNum = res.data.likeNum
								this.videoDetail = videoDetail
								if (this.preUpdateIndex) {
									uni.$emit('updateLike', videoDetail.isLike)
								}
							} else {
								uni.showToast({
									title: res.msg,
									icon: 'none'
								});
							}
						}
					})

				} else {
					getApp().globalData.goLogin(this.$Route);
				}
			},

			//评论模块删除事件
			delCom(e) {
				e.stopPropagation()
				let tmp_data = e.currentTarget.dataset;
				uni.showModal({
					title: '',
					content: this.$L(`确认删除该评论？`),
					confirmText: this.$L('确定'),
					cancelText:this.$L('取消'),
					success: res => {
						if (res.confirm) {
							if (tmp_data.type == 'comment') {
								this.delComment(tmp_data.commentid);
							} else {
								this.delApply(tmp_data.replyid, tmp_data.commentid);
							}
						}
					}
				});
			},

			// 删除回复
			delApply(replyId, commentId) {
				let {
					video_id,
					videoDetail,
					commentList
				} = this;
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/comment/delReply'
				param.method = 'POST'
				param.data.replyId = replyId
				this.$request(param).then(res => {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					});
					if (res.state == 200) {
						let index = commentList.findIndex(el => el.commentId == commentId);
						let r_index = commentList[index].replyList.findIndex(el => el.replyId == replyId);
						commentList[index].replyList.splice(r_index, 1);
						this.commentList = commentList

					}
				})
			},

			// 删除评论
			delComment(commentId) {
				let {
					video_id,
					videoDetail,
					commentList
				} = this;
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/comment/del'
				param.method = 'POST'
				param.data.commentId = commentId
				this.$request(param).then(res => {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					});

					if (res.state == 200) {
						videoDetail.commentNum = --videoDetail.commentNum;
						uni.$emit('updateComment',videoDetail.commentNum)
						let index = commentList.findIndex(el => el.commentId == commentId);
						commentList.splice(index, 1);
						this.videoDetail = videoDetail
						this.commentList = commentList
					}
				})
			},

			// 点赞评论
			likeComment(e) {
				let commentId = e.currentTarget.dataset.commentid;
				let {
					commentList
				} = this;
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/comment/clickPraise'
				param.method = 'POST'
				param.data.commentId = commentId
				if (this.hasLogin) {
					this.$request(param).then(res => {
						if (res.state == 200) {
							let index = commentList.findIndex(el => el.commentId == commentId);
							let isLike = commentList[index].isLike;
							commentList[index].isLike = !isLike;
							commentList[index].likeNum = !isLike ? commentList[index].likeNum + 1 : commentList[index].likeNum - 1;
							this.commentList = commentList
						} else {
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							});
						}
					})
				} else {
					getApp().globalData.goLogin(this.$Route);
				}
			},

			// 点赞回复
			likeReply(e) {
				let commentId = e.currentTarget.dataset.commentid;
				let replyId = e.currentTarget.dataset.replyid;
				let {
					commentList
				} = this;
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/comment/replyClickPraise'
				param.method = 'POST'
				param.data.replyId = replyId
				if (this.hasLogin) {
					this.$request(param).then(res => {
						if (res.state == 200) {
							let index = commentList.findIndex(el => el.commentId == commentId);
							let r_index = commentList[index].replyList.findIndex(el => el.replyId == replyId);
							let isLike = commentList[index].replyList[r_index].isLike;
							commentList[index].replyList[r_index].isLike = !isLike;
							commentList[index].replyList[r_index].likeNum = !isLike ? commentList[index].replyList[r_index].likeNum + 1 : commentList[index].replyList[r_index].likeNum - 1;
							this.commentList = commentList
						} else {
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							});
						}
					})
				} else {
					getApp().globalData.goLogin(this.$Route);
				}
			},

			//回复评论
			replyComment(e) {
				let tmp = e.currentTarget.dataset;
				let {
					replyInfo
				} = this;
				if (this.hasLogin) {
					this.replyInfo = {
							commentId: tmp.commentid,
							replyId: tmp.replyid,
							memberNickname: tmp.membernickname
						},
						this.showFocus = true
				} else {
					uni.showToast({
						title: this.$L('需要登录才能回复哦'),
						icon: 'none'
					});
				}
			},

			//评论功能
			sendReplyComment(e) {
				if (!e.detail.value) {
					return
				}
				if (Date.parse(new Date()) - cur_time < 10) {
					return false;
				} else {
					cur_time = Date.parse(new Date());
				}
				if (!this.hasLogin) {
					getApp().globalData.goLogin(this.$Route);
					return;
				}

				let {
					replyInfo,
					video_id,
					commentList,
					videoDetail
				} = this;
				if (replyInfo == '') {
					//发布评论
					let param = {}
					param.data = {}
					param.url = 'v3/video/front/video/comment/publish'
					param.method = 'POST'
					param.data.content = e.detail.value
					param.data.videoId = video_id
					this.$request(param).then(res => {
						if (res.state == 200) {
							let that = this
							videoDetail.commentNum = Number(videoDetail.commentNum) + 1;
							res.data.replyList = []
							res.data.limit = 3
							commentList.unshift({
								...res.data,
								isSelf: true
							});
							// that.getCommentList()
							//清空输入内容
							setTimeout(() => {
								that.commentList = JSON.parse(JSON.stringify(commentList))
								that.input_val = ''
								that.showFocus = false
								that.videoDetail = videoDetail
							}, 0)

						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							});
						}
					})
				} else {
					//回复评论
					let that = this
					let param = {}
					param.data = {}
					param.url = 'v3/video/front/video/comment/reply'
					param.method = 'POST'
					param.data.content = e.detail.value
					param.data.commentId = replyInfo.commentId
					param.data.parentReplyId = replyInfo.replyId ? replyInfo.replyId : 0

					this.$request(param).then(res => {
						if (res.state == 200) {
							let index = commentList.findIndex(el => el.commentId == replyInfo.commentId);
							commentList[index].replyList.push({
								...res.data,
								isSelf: true
							}); //清空输入内容
							if (commentList[index].replyList.length > 3) {
								commentList[index].isFoldReply = false
								commentList[index].limit = commentList[index].replyList.length
							}
							this.commentList = commentList
							this.input_val = ''
							this.showFocus = false
							this.replyInfo = ''
							this.$forceUpdate()
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							});
						}
					})
				}
			},

			//关闭评论
			closeComment() {
				this.$refs.commentModel.close()
			},

			//打开评论
			openCommnet() {
				if (this.videoDetail.state != 2) {
					uni.showToast({
						title: this.$L('该视频未审核通过,不能评论哦～'),
						icon: 'none'
					})
					return
				}
				this.$refs.commentModel.open()
				this.firstLoading = true
				this.hasmore = true
				this.pn = 1
				this.getCommentList();
			},

			// 查看更多回复
			getMoreReply(reply) {
				reply.limit = reply.replyList.length
				reply.isFoldReply = false
			},

			// 收起更多回复
			closeMoreReply(reply) {
				reply.limit = 3
				reply.isFoldReply = true
			},

			chansgedd() {
				// #ifdef H5
				let currentId = 'myVideo' + this.video_id; // 获取当前视频id
				let current_video = videojs(currentId)
				if (this.videoPlay) {
					this.videoPlay = false;
					current_video.pause();
				} else {
					this.videoPlay = true;
					current_video.play();
				}
				// #endif
				// #ifdef MP
				if (this.videoPlay) {
					this.videoPlay = false;
					uni.createVideoContext('sldVideo_child' + this.video_id, this).pause();
				} else {
					this.videoPlay = true;
					uni.createVideoContext('sldVideo_child' + this.video_id, this).play();
				}
				// #endif
			},
			bindplay() {
				this.videoPlay = true;
			},
			bindpause() {
				this.videoPlay = false;
			},

			//视频暂停播放，flag为true播放，否则暂停
			videoCon(flag) {
				let {
					video_id
				} = this;
				let str = "sldVideo_child_" + video_id;
				let videoContext = uni.createVideoContext(str);
				videoContext.pause(); //暂停播放
				this.autoplay = flag
			},

			//进入直播个人中心
			gPensonalCenter(e) {
				var curTab = e.currentTarget.dataset.curtab;

				if (!this.hasLogin) {
					getApp().globalData.goLogin(this.$Route);
				} else {
					this.$Router.push('/extra/user/my')
				}
			},

			moveHandle() {},

			closeCommet() {
				this.showFocus = false;
				uni.hideKeyboard();
				if (this.replyInfo) {
					this.replyInfo = '';
				}
			}
		}
	};
</script>
<style scoped lang="scss">
	/* #ifdef H5 */
	.video_wrap ::v-deep .video-js {
		width: 100vw;
		height: 100vh;
	}
	.video_wrap ::v-deep .video-js .vjs-tech {
		width: auto;
		height: auto;
		left: 50%;
		top: 50%;
		transform: translate(-50%,-50%);
	}
	/* #endif */
	
	.live_back,
	.living {
		width: 752rpx;
		height: 100vh;
		margin-left: -2rpx;
	}

	.video_wrap {
		width: 100vw;
		height: 100vh;
		overflow-y: hidden;
	}

	.live_header {
		position: absolute;
		top: 80rpx;
		left: 20rpx;
		z-index: 2;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: flex-start;
		height: 80rpx;
	}

	.live_header text {
		color: #fff;
	}

	.live_header .go_back {
		width: 45rpx;
		height: 47rpx;
	}

	.live_header .avator {
		width: 78rpx;
		height: 78rpx;
		border-radius: 50%;
		border: 2px solid #fff;
		margin-left: 8rpx;
		background-repeat: no-repeat;
		background-position: center;
		background-size: 100%;
	}

	.live_header .mem_info {
		margin-left: 20rpx;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-content: space-between;
	}

	.live_header .mem_info .name {
		max-width: 150rpx;
		color: #fff;
		font-size: 28rpx;
		line-height: 32rpx;
		margin-bottom: 15rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		font-weight: 600;
	}

	.live_header .mem_info .stat_num {
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
	}

	.live_header .mem_info .stat_num text {
		color: #fff;
		font-size: 22rpx;
		line-height: 36rpx;
		white-space: nowrap;
		font-weight: 600;
	}

	.live_header .live_fllow {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		width: 120rpx;
		height: 50rpx;
		background: #fc1c1c;
		border-radius: 25rpx;
		margin-left: 25rpx;
	}

	.live_header .live_fllow image {
		width: 46rpx;
		height: 46rpx;
	}

	.live_header .live_fllow text {
		color: #fff;
		font-size: 24rpx;
		margin-right: 8rpx;
	}

	.live_footer {
		position: absolute;
		z-index: 2;
		left: 30rpx;
		bottom: 30rpx;
		width: 750rpx;
		height: 110rpx;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
	}

	.live_footer .goods {
		width: 110rpx;
		height: 90rpx;
		border-radius: 55rpx;
		background-color: rgba(0, 0, 0, 0.3);
		padding-top: 20rpx;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		position: relative;
	}

	.live_footer .goods image {
		width: 80rpx;
		height: 80rpx;
	}

	.live_footer .goods text {
		position: absolute;
		top: 16rpx;
		left: 65rpx;
		/* width: 28rpx; */
		height: 28rpx;
		line-height: 28rpx;
		background-color: #fff;
		color: #ff0200;
		font-size: 24rpx;
		border-radius: 14rpx;
		text-align: center;
		padding: 0 9rpx;
	}

	.live_footer .talk_con {
		height: 65rpx;
		width: 325rpx;
		margin-left: 30rpx;
		border-radius: 30rpx;
		background: rgba(0, 0, 0, 0.3);
		padding: 0 20rpx;
		color: #fff;
	}

	.live_footer .share {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: center;
		width: 77rpx;
		height: 110rpx;
		margin-left: 21rpx;
	}

	.live_footer .share image {
		width: 77rpx;
		height: 77rpx;
	}

	.live_footer .share text {
		color: #fff;
		font-size: 22rpx;
	}

	.select-wrap {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		margin: 0 auto;
		width: 750rpx;
		height: 100vh;
		background-color: rgba(0, 0, 0, 0.45);
		z-index: 9999;
	}

	.select-wrap .share-mode {
		position: absolute;
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.select-wrap .share-mode .share-img {
		width: 72vw;
		border-radius: 20rpx;
		overflow: hidden;
	}

	.select-wrap .share-mode .share-img image {
		width: 100%;
		height: 0;
		border-radius: 20rpx;
	}

	.select-wrap .share-mode .ul {
		display: flex;
		align-items: center;
		width: 100%;
		justify-content: space-evenly;

	}

	.share-mode .item {
		display: flex;
		flex-direction: column;
		align-items: center;
		text-align: center;
		cursor: pointer;
		border: none;
		margin: 0;
		padding: 0;
		line-height: 1;
		background-color: transparent;
		height: 180rpx;
		width: 122rpx;
	}

	.share-mode .item::after {
		border: none;
	}

	.share-mode .item image {
		width: 112rpx;
		height: 0;
	}

	.share-mode .item text {
		color: #fff;
		font-size: 24rpx;
		margin-top: 30rpx;
	}

	.select-wrap .close {
		width: 750rpx;
		height: 140rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.select-wrap .close image {
		width: 18rpx;
		height: 18rpx;
		padding: 20rpx;
		opacity: unset;
	}

	.select-wrap .close uni-image>img {
		opacity: unset !important;
	}

	.share-mode .share-img {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	/* 视频绑定的商品模块 start */

	.live_bind_goods_mask {
		width: 750rpx;
		height: 100vh;
		position: absolute;
		left: 0;
		bottom: 0;
		z-index: 100;
		background: rgba(0, 0, 0, 0.45);
	}

	.live_bind_goods {
		width: 750rpx;
		height: 850rpx;
		position: fixed;
		left: 0;
		bottom: 0;
		z-index: 4;
		background: #fff;
		border-radius: 15rpx 15rpx 0 0;
		background: #f8f8f8;
	}

	.live_bind_goods .header {
		width: 710rpx;
		height: 100rpx;
		padding: 0 20rpx;
		background: #fff;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		border-radius: 15rpx 15rpx 0 0;
	}

	.live_bind_goods .header text {
		color: #2d2d2d;
		font-size: 32rpx;
	}

	.live_bind_goods .header image {
		width: 47rpx;
		height: 47rpx;
	}

	.scroll_goods {
		height: 750rpx;
	}

	/* 视频绑定的商品模块 end */

	.video_control {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		top: 0;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: flex-start;
		z-index: 5;
	}

	.video_control image {
		width: 102rpx;
		height: 102rpx;
		margin-top: calc(100vh/3);
	}

	.empty_158 {
		width: 750rpx;
		height: 158rpx;
	}

	/***** 弹幕 start *****/

	.barrage_wrap {
		position: absolute;
		bottom: 200rpx;
		left: 30rpx;
		width: 500rpx;
		background-color: transparent;
		z-index: 99;
		height: 500rpx;
	}

	.notice {
		height: 50rpx;
		max-width: 500rpx;
		display: inline-block;
		border-radius: 10rpx;
		line-height: 50rpx;
		margin-bottom: 10rpx;
	}

	.notice text {
		color: #fff;
		font-size: 24rpx;
		height: 50rpx;
		max-width: 500rpx;
		display: inline-block;
		background-color: #fc1c1c;
		border-radius: 10rpx;
		line-height: 50rpx;
		margin-bottom: 10rpx;
		padding: 0 15rpx;
	}

	.barrage {
		min-height: 100rpx;
		max-height: 450rpx;
	}

	.gonggao {
		background-color: rgba(0, 0, 0, 0.3);
		white-space: normal;
		padding: 12rpx 16rpx;
		border-radius: 10rpx;
		word-break: break-all;
	}

	.gonggao .title {
		color: #fc1c1c;
		font-size: 26rpx;
		line-height: 36rpx;
	}

	.gonggao .ba_txt {
		color: #fff;
		font-size: 24rpx;
		line-height: 36rpx;
	}

	.barrage_item {
		background-color: rgba(0, 0, 0, 0.3);
		max-width: 500rpx;
		border-radius: 23rpx;
		padding: 0rpx 15rpx 10rpx 15rpx;
		margin-top: 10rpx;
		white-space: normal;
		word-break: break-all;
		display: inline-block;
	}

	.barrage_item .name {
		font-size: 24rpx;
		line-height: 36rpx;
		margin-right: 15rpx;
	}

	.barrage_item .barrage_item_con {
		color: #fff;
		font-size: 24rpx;
	}

	/***** 弹幕 end *****/

	.right_control {
		position: absolute;
		bottom: 310rpx;
		right: 30rpx;
		z-index: 99;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: center;
	}

	.right_control image {
		width: 68rpx;
		height: 68rpx;
		margin-bottom: 13rpx;
	}

	.right_control text {
		color: #fff;
		font-size: 24rpx;
		margin-bottom: 36rpx;
		white-space: nowrap;
	}

	.right_control .share_btn {
		background-color: transparent;
		border: none;
		color: transparent;
		line-height: 0;
		height: 120rpx;
		view{
			font-size: 24rpx;
			color: #fff;
		}
	}

	.right_control .share_btn::after {
		border: none !important;
	}

	/* 进入个人主页按钮入口 */
	.personal_homepage {
		width: 66rpx;
		height: 66rpx;
		border-radius: 50%;
		border: 2rpx solid #ffffff;
		bottom: 310rpx;
		right: 30rpx;
		z-index: 99;
		display: flex;
		justify-content: center;
		align-items: center;
		background: #ffffff;
	}

	.personal_homepage .personal_homepage_image {
		width: 66rpx;
		height: 66rpx;
		border-radius: 50%;
		margin-bottom: 0;
	}


	.video_footer {
		left: 30rpx;
		/* #ifdef MP */
		bottom: 78rpx;
		/* #endif */
		/* #ifndef MP */
		bottom: 37rpx;
		/* #endif */
		right: 0;
		z-index: 2;
		display: flex;
		flex-direction: column;
		justify-content: flex-end;
		align-items: flex-start;
		position: absolute;
	}

	.video_footer .title {
		color: #fff;
		font-size: 30rpx;
		font-weight: 600;
		margin-bottom: 10rpx;
		width: 600rpx;
	}

	.video_footer .desc {
		color: #fff;
		font-size: 26rpx;
		line-height: 40rpx;
		margin-bottom: 18rpx;
		width: 570rpx;
	}

	.wrap {
		width: 750rpx;
		height: 100vh;
		overflow: hidden;
	}
	
	.live_pause {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%,-50%);
		z-index: 99;
	}
	
	.live_pause .live_pause_img {
		width: 100rpx;
		height: 115rpx;
	}

	.video_footer .video_goods {
		height: 172rpx;
		white-space: nowrap;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
	}

	.video_footer .video_goods .video_goods_item {
		width: 410rpx;
		height: 100%;
		display: inline-block;
		border-radius: 15rpx;
		flex-direction: row;
		justify-content: flex-start;
		align-items: flex-start;
		margin-right: 30rpx;
		clear: both;
		overflow: hidden;
	}

	.video_footer .video_goods .video_goods_item .goods_img {
		width: 172rpx;
		height: 172rpx;
		background: #fff;
		border-radius: 15rpx 0 0 15rpx;
		float: left;
	}

	.video_footer .video_goods .video_goods_item .goods_img image {
		width: 172rpx;
		height: 172rpx;
	}


	.video_footer .video_goods .video_goods_item .goods_detail {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: flex-start;
		flex: 1;
		background: #222;
		height: 100%;
		border-radius: 0 15rpx 15rpx 0;
		padding: 15rpx 20rpx;
		box-sizing: border-box;
	}

	.video_footer .video_goods .video_goods_item .goods_detail .goods_name {
		width: 200rpx;
		color: #fff;
		font-size: 24rpx;
		line-height: 36rpx;
		height: 72rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		word-break: break-word;
		white-space: normal;
	}

	.video_footer .video_goods .video_goods_item .goods_detail .goods_info {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		width: 200rpx;
	}

	.video_footer .video_goods .video_goods_item .goods_detail .goods_info .goods_price {
		color: #fff;
		font-size: 28rpx;
		letter-spacing: 2rpx;
	}

	.video_footer .video_goods .video_goods_item .goods_detail .goods_info .add_cart {
		width: 48rpx;
		height: 48rpx;
	}

	.video_comment {
		/* position: fixed; */
		position: absolute;
		z-index: 100;
		left: 0;
		right: 0;
		bottom: 0;
		width: 750rpx;
		/* height: 90vw; */
		background: #fff;
		border-radius: 15rpx 15rpx 0 0;
	}


	.video_comment .comment_empty {
		position: absolute;
		z-index: -1;
		left: 0;
		right: 0;
		bottom: 0;
		top: 70rpx;
	}

	.video_comment .title {
		display: flex;
		padding: 30rpx;
		flex-direction: row;
		justify-content: space-between;
		align-content: center;
		height: 102rpx;
		border-bottom: 1px solid #f5f5f5;
	}

	.video_comment .title .com_t_l {
		display: flex;
		flex: 1;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
	}

	.video_comment .title .com_t_l .com_t_l_title {
		color: #2d2d2d;
		font-size: 32rpx;
		margin-right: 18rpx;
	}

	.video_comment .title .com_t_l .com_t_l_total {
		color: #949494;
		font-size: 22rpx;
	}

	.video_comment .title .com_t_close {
		width: 48rpx;
		height: 48rpx;
	}

	.replay {
		position: absolute;
		z-index: 4;
		height: 100rpx;
		width: 750rpx;
		padding: 0 30rpx;
		background: #fff;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		left: 0;
		right: 0;
		bottom: 0;
		box-shadow: 0px 0px 20px 0px rgba(86, 86, 86, 0.08);
	}

	.replay .input_wrap {
		background-color: #f5f5f5;
		border-radius: 12px;
		width: 690rpx;
		height: 65rpx;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
	}

	.replay .input_wrap .reply_inp {
		display: flex;
		flex: 1;
	}

	.replay .input_wrap image {
		width: 47rpx;
		height: 47rpx;
		margin: 0 10rpx;
	}

	.replay .reply_tip {
		color: #2d2d2d;
		font-size: 24rpx;
		padding: 0 15rpx;
	}

	.replay .reply_name {
		color: #4d8efb;
	}

	.video_comment .comment .com_item_wrap {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
	}

	.video_comment .comment {
		width: 100%;
		height: 720rpx;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
		margin-top: 20rpx;
		/* position: relative; */
		padding-bottom: 100rpx;
	}

	.video_comment .comment .com_item {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: flex-start;
		width: calc(750rpx - 60rpx);
		margin-left: 30rpx;
	}

	.video_comment .comment .com_item .com_item_l {
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: flex-start;
		width: 600rpx;
	}

	.video_comment .comment .com_item .com_item_l .com_avator {
		width: 43rpx;
		height: 43rpx;
		border-radius: 50%;
		margin-right: 18rpx;
		margin-top: 10rpx;
	}

	.video_comment .comment .com_item .com_item_l .com_detail {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
		flex: 1;
	}

	.video_comment .comment .com_item .com_item_l .com_detail .com_name {
		color: #2d2d2d;
		font-size: 28rpx;
		margin-bottom: 12rpx;
	}

	.video_comment .comment .com_item .com_item_l .com_detail .com_con {
		color: #2d2d2d;
		font-size: 26rpx;
		line-height: 39rpx;
		margin-bottom: 7rpx;
		word-break: break-all;
	}

	.video_comment .comment .com_item .com_item_l .com_detail .com_other {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: flex-start;
		margin-bottom: 12rpx;
	}

	.video_comment .comment .com_item .com_item_l .com_detail .com_other .com_time {
		color: #949494;
		font-size: 22rpx;
		margin-right: 20rpx;
	}

	.video_comment .comment .com_item .com_item_l .com_detail .com_other .del_com {
		color:var(--color_video_main);
		font-size: 20rpx;
	}

	.video_comment .comment .com_item .com_item_r {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: center;
	}

	.video_comment .comment .com_item .com_item_r image {
		width: 46rpx;
		height: 46rpx;
	}

	.video_comment .comment .com_item .com_item_r text {
		color: #949494;
		font-size: 20rpx;
		margin-top: -3rpx;
	}

	.comment .child {
		padding-left: 60rpx;
	}

	.video_comment .comment .child .com_item .com_item_l .com_avator {
		width: 30rpx;
		height: 30rpx;
		margin-top: 0;
		margin-right: 10rpx;
	}

	.video_comment .comment .child .com_item .com_item_l .com_detail .com_name {
		font-size: 26rpx;
		margin-bottom: 5rpx;
	}

	.video_comment .comment .child .com_item .com_item_l .com_detail .com_con {
		font-size: 24rpx;
	}

	.video_comment .comment .child .com_item .com_item_l .com_detail .com_con .replay_name {
		color: #949494;
		font-size: 24rpx;
	}

	.video_comment .comment .child .com_item {
		width: calc(750rpx - 120rpx);
	}

	.video_comment .comment .child .com_item .com_item_l {
		width: 550rpx;
	}

	.reply_pagnation {
		width: 550rpx;
		display: flex;
		flex-direction: row;
		justify-content: space-around;
		align-items: center;
		height: 40rpx;
		/* padding-left: 110rpx;
		padding-right: 90rpx; */
		margin: 0 auto;
	}

	.reply_pagnation .left_line,
	.reply_pagnation .right_line {
		height: 1rpx;
		background: rgba(0, 0, 0, 0.1);
		width: 166rpx;
	}

	.reply_pagnation .more_reply {
		color: #2d2d2d;
		font-size: 22rpx;
	}

	.page_loading_child {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 750rpx;
		height: 750rpx;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		background-color: transparent;
	}

	.page_loading_child image {
		width: 60rpx;
		height: 60rpx;
	}
</style>
