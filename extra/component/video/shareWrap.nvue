<!-- 
	@zoucb 2023-05-15
	短视频app端播放页的分享组件
 -->
<template>
	<view>
		<view v-if="shareWrap" class="select-wrap" catchtouchmove="touchmoveshare">
			<view class="select-wrap_share-mode">
				<view class="select-wrap_share-mode_share-img"></view>
				<view class="select-wrap_share-mode_ul">
					<button @click.stop="sldShare(0,'WXSceneSession')" class="share-mode_item">
						<image class="share-mode_item_image" :src="imgUrl+'goods_detail/wx_share.png'"
							mode="widthFix"></image>
						<text class="share-mode_item_text">{{Ls('微信好友')}}</text>
					</button>
					<button @click.stop="sldShare(0,'WXSenceTimeline')" class="share-mode_item">
						<image class="share-mode_item_image" :src="imgUrl+'svideo/pyq_share.png'"
							mode="widthFix"></image>
						<text class="share-mode_item_text">{{Ls('微信朋友圈')}}</text>
					</button>
				</view>
		
				<view class="select-wrap_close" @click="closeShare">
					<image class="select-wrap_close_image" :src="imgUrl+'svideo/share_close.png'"
						mode="widthFix"></image>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {weiXinAppShare} from '@/utils/base.js';
	import {getCurLanguage} from '@/utils/base.js';
	import Config from '@/utils/config.js'
	const imgUrl = Config.imgUrl;
	export default {
		data(){
			return{
				imgUrl: Config.imgUrl, //图片地址
				Ls:getCurLanguage,
				shareWrap:false
			}
		},
		
		
		methods:{
			
			injectShare(info){
				this.video_id = info.video_id
				this.videoName = info.videoName
				this.authorName = info.authorName
				this.videoImage = info.videoImage
				this.shareWrap = true
			},
			
			
			//分享 type：分享类型 0 图文 2图片，scene 场景 WXSceneSession：分享朋友  WXSenceTimeline：分享朋友圈
			sldShare(type, scene) {
				let shareData = {};
				if (type == 0) {
					shareData.href = Config.apiUrl + 'extra/svideo/svideoPlay?video_id=' + this.video_id;
					shareData.title = this.videoName;
					shareData.summary = this.Ls('我正在看') + this.authorName + this.Ls('的精彩内容，快来围观~');
					shareData.imageUrl = this.videoImage[0];
				}
				try{
					this.weiXinAppShare(type, scene, shareData);
				}catch(e){
					console.log(e.message)
				}
				this.closeShare(); //关闭分享
			},
			//微信分享
			weiXinAppShare(type, scene, shareData) {
				if (type == 0) {
					//分享图文
					uni.share({
						provider: "weixin",
						scene: scene,
						type: type, //0为图文
						href: shareData.href,
						title: shareData.title,
						summary: shareData.summary,
						imageUrl: shareData.imageUrl, //图片,图片过大的话不展示，建议小于20kb
						success: function(res) {},
						fail: function(err) {}
					});
				} else if (type == 2) {
					//分享图片
					uni.share({
						provider: "weixin",
						scene: scene,
						type: type, //2为图片
						imageUrl: shareData.imageUrl, //图片,图片过大的话不展示，建议小于20kb
						success: function(res) {},
						fail: function(err) {}
					});
				}
			},
			//关闭分享
			closeShare() {
				this.shareWrap = false;
			},
		}
		
		
	}
</script>

<style>
	/* 分享start */
	.select-wrap {
		position: fixed;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 9999;
		/* margin: 0 auto; */
		width: 750rpx;
		background-color: rgba(0, 0, 0, 0.45);
	}
	
	.select-wrap_share-mode {
		position: absolute;
		bottom: 100rpx;
		left: 0;
		right: 0;
		width: 750rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.select-wrap_share-mode_share-img {
		width: 72vw;
		border-radius: 20rpx;
		overflow: hidden;
	}
	
	.select-wrap_share-mode_share-img_image {
		width: 100%;
		height: 0;
		border-radius: 20rpx;
	}
	
	.select-wrap_share-mode_ul {
		display: flex;
		flex-direction: row;
		align-items: center;
		width: 750rpx;
		padding-left: 30px;
		padding-right: 30px;
		justify-content: space-around;
	}
	
	.share-mode_item {
		display: flex;
		flex-direction: column;
		align-items: center;
		text-align: center;
		border-color: none;
		margin: 0;
		padding: 0;
		line-height: 1;
		background-color: transparent;
		border-width: 0;
	}
	
	.share-mode_item_image {
		width: 106rpx;
		height: 0;
	}
	
	.share-mode_item_text {
		color: #fff;
		font-size: 24rpx;
		margin-top: 30rpx;
	}
	
	.select-wrap_close {
		width: 750rpx;
		height: 120rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
	}
	
	.select-wrap_close_image {
		width: 18rpx;
		height: 18rpx;
		padding: 20rpx;
	}
	
	.select-wrap_share-mode_share-img {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	/* 分享end */
</style>