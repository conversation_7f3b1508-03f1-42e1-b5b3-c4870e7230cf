<template>
	<view class="msg-view">
		<scroll-view class="msg-view-p" :scroll-into-view="toBottom" show-scrollbar="false" scroll-y >
			<view id="chatArea">
          <view class="system-msg" :style="{'flex-direction':true}"  v-if="liveDetail.liveNotice">
          	<view class="system-msg-detail">
          		<text class="system-msg-detail-username" v-for="i in msgInfo[0].userName">{{i}}</text>
          		<text class="system-msg-detail-content " v-for="(i,ind) in liveDetail.liveNotice" :style="{marginLeft:ind==0?'10rpx':'0'}">{{i}}</text>
          	</view>
          </view>
          <view class="chat-area-line" v-for="(item,index) in msgList">
           <view class="user-msg" :id="'item' + index" :style="{'flex-direction':((item.authorName.length + item.msg.length)<20?'row':'')}"  v-if="liveDetail.liveNotice&&msgListFlag">
            	<view class="user-msg-detail">
            		<text class="user-msg-detail-username"  :style="'color:' + item.color_random">{{item.authorName}}:{{item.msg}}</text>
                <!-- 
            		<text class="user-msg-detail-content " :style="{marginLeft:ind==0?'10rpx':'0'}"  v-for="(i,ind) in item.msg">{{item.msgWidth==true?'':''}}{{i}}</text> -->
            	</view>
            </view>
          </view>
				
			</view>
		</scroll-view>
	</view>
</template>
<script>
	export default {
		name:'MhMsgList',
		props: {
			msgList: {
				type: [Object,Array],
				default: [],
			},
      liveDetail:{
        type: [Object,Array],
        default:{},
      },
			msgPanelScrollTop: {
				type: [Number, String],
				default: 0
			},
      toBottom: {
				type: [String],
				default: 'item0'
			}
		},
    data(){
      return{
        msg_list:[],
        px:0,
        msgListFlag:true,
        msgInfo:[
          {
          	userName: "公告",
          	content: "直播倡导绿色直播，严禁发布涉黄涉毒涉赌，严禁发布涉政、违法及低俗违规内容。健康直播，文明互动",
          	cmd: "say",
          	msg_type: "system"
          }
        ]
      }
    },
    mounted() {
      this.rpxToPxFloat()
    },
		watch:{
			msgList:{
				handler(nv,ov){
					this.tess()
				},
				deep:true
			}
		},
		methods: {

      /** 从 rpx 转为 px  包含精确小数
      * @param float rpx 
      */
      rpxToPxFloat(){
        const sysInfo = uni.getSystemInfoSync();
      	const a = sysInfo.windowWidth / 750 * 500;
        this.px = a
      },
      tess(){
        let list = this.msgList
        const num = '#item'+String(this.msgList.length-1)
        let that = this
        setTimeout(()=>{
            uni.getSystemInfo({
              success(res) { 
                let obj = uni.createSelectorQuery().select(num)
                obj.boundingClientRect(function (data) { 
				this.msgListFlag = false
                  if(data.width>=that.px){
                    list[list.length-1].msgWidth = true
                  }
                    that.msg_list = list
                    that.msgListFlag = true
                }).exec()
              }
            })
        },100);
         this.msg_list[this.msg_list.length-1]
      },
			test(e){
				uni.showToast({
					title:e,
					icon:'none'
				})
			},
			setMsgPanelScroll() {
				var that = this;
				setTimeout(function() {
					const query = uni.createSelectorQuery().in(that);
					query.select('#chatArea').boundingClientRect(data => {
						that.msgPanelScrollTop = data.height - 200;
					}).exec();
				}, 50)

				
			},
		}

	}
</script>
<style lang="scss" scoped>
	.msg-view {
		// position: absolute;
		// bottom: 150rpx;
		// left: 15px;
	}

	.msg-view-p {
		display: flex;
		width: 500rpx;
		max-height: 450rpx;
    min-height: 100rpx;
	}
	 
	.chat-area-line{
		width: 500upx;
		flex-direction: row;
	}
	.system-msg{
		width: 500upx;
		margin-bottom: 5upx;
	}
	.system-msg-detail {
		// max-width: 550upx;
		
		border-radius: 23rpx;
		padding: 0rpx 15rpx 10rpx 15rpx;
		padding-top: 15rpx;
		margin-top: 10rpx;
		background-color: rgba(0, 0, 0, 0.3);
		flex-direction: row;  
		flex-wrap: wrap;
		margin-right: 14upx;
    font-weight: 600;
	
	}
		.system-msg-detail-username {
			color: #fc1c1c;
			font-size: 26rpx;
			line-height: 36rpx;
			display: inline-block;
			
		}
	
		.system-msg-detail-content {
			color: #fff;
			font-size: 26rpx;
			line-height: 36rpx;
		}
	.user-line{
		// max-width: 530upx;
		padding: 10upx;
		color: #FFFFFF;
		flex-direction: row;
		border-radius: 30upx;
		background-color: rgba(0, 0, 0, 0.3);
		margin-right: 14upx;
		font-size: 25rpx;
		font-weight: 400;
		line-height: 40upx;
	}
	.user-msg{
		// width: 500rpx;
    display: flex;
    box-sizing: border-box;
		flex-wrap: wrap;
		margin-bottom: 5upx;
	}
	.user-msg-detail {
   border-radius: 23rpx;
   padding: 0rpx 15rpx 10rpx 15rpx;
   padding-top: 15rpx;
   margin-top: 10rpx;
   // background-color: rgba(0, 0, 0, 0.3
   flex-direction: row;
   flex-wrap: wrap;
   margin-right: 14upx;
   font-weight: 600;
		
	}
		.user-msg-detail-username {
			 font-size: 25rpx;
			  line-height: 36rpx;
			  margin-right: 15rpx;
			  margin-top: 4rpx;
			  max-width: 470rpx;
			  word-wrap: break-word;
			  word-break: break-all;
			  padding:10rpx;
			  background-color: rgba(0, 0, 0, 0.3);
			  border-radius: 10rpx;

		}
	
		.user-msg-detail-content {
			font-size: 25rpx;
			font-weight: 400;
			line-height: 36rpx;
			color: #fff;
		}
  
  .gonggao {
  	background-color: rgba(0, 0, 0, 0.3);
  	white-space: normal;
  	padding: 12rpx 16rpx;
  	border-radius: 10rpx;
  	word-break: break-all;
  	font-weight: 600;
  }
  
  .gonggao_title {
  	color: #fc1c1c;
  	font-size: 26rpx;
  	line-height: 36rpx;
    display: inline-block;
  }
  
  .gonggao_ba_txt {
  	color: #fff;
  	font-size: 26rpx;
  	line-height: 36rpx;
  }
</style>
