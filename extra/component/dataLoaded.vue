<!-- 数据加载完毕 -->
<template name="dataLoaded">
    <view class="no-has-more" v-if="showFlag" style="background-color: transparent">
        {{ $L('已经到底啦~') }}
    </view>
</template>

<script>
export default {
    name: 'dataLoaded',
    data() {
        return {};
    },
    props: {
        showFlag: {
            type: Boolean,
            value: false
        }
    },
    methods: {}
};
</script>
<style>
.no-has-more {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    height: 120rpx;
    text-align: center;
    line-height: 120rpx;
    color: #949494;
    font-size: 22rpx;
}
</style>
