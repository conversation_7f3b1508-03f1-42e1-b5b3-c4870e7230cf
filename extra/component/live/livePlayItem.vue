<template>
	<view>
		<!-- #ifdef H5 -->
		<view class="video-wrap" ref="videoWrapHls"></view>
		<!-- #endif -->
		<view v-if="isShow && liveDetail" class="live_play">
			<!-- 回放 -->
			<view class="live_header">
				<image @tap="goBack" class="go_back" :src="imgUrl+'svideo/white_arrow_l.png'"></image>
				<view class="avator" @tap="goLiveUserCenter(liveDetail.authorId)"
					:style="'background:url('+liveDetail.memberAvatar+');' + bgStyle"></view>
				<view class="mem_info" @tap="goLiveUserCenter(liveDetail.authorId)">
					<text class="name">{{liveDetail.memberNickname}}</text>
					<view class="stat_num">
						<!-- <text v-if="liveDetail.isSelf == 1" class="click_num" style="margin-right: 20rpx">{{live_like_num}}人气</text> -->
						<text class="click_num">{{live_click_num}}{{$L('观看')}}</text>
					</view>
				</view>
				<view v-if="!liveDetail.isSelf&&liveDetail.isFollow==false" class="live_fllow" @tap="collect">
					<image :src="imgUrl+'svideo/fav_a.png'"></image>
					<text>{{$L('关注')}}</text>
				</view>
			</view>
			
			<view v-if="!videoPlay" class="live_pause" @tap.stop="chansgedd">
				<image class="live_pause_img" :src="imgUrl + 'svideo/svideo_play.png'" mode="aspectFit" lazy-load></image>
			</view>

			<!-- #ifdef MP -->
			<block v-if="liveDetail.liveState == 4 && !(show_live_stop||live_recording||live_ban||live_delete)">
				<view class="" @click="videoPlayControl">
					<video :id="'sldVideo'+live_id" class="live_back" :src="liveDetail.replayUrl"
						:show-fullscreen-btn="false" object-fit="cover" :show-center-play-btn="true"
						:enable-play-gesture="true" :initial_time="initial_time" :show-progress="true"
						@play="bindplay" @pause="bindpause">
					</video>
				</view>
			</block>
			<!-- #endif -->

			<!-- 直播 -->
			<block v-if="liveDetail.liveState == 2 && !(show_live_stop||live_recording||live_ban||live_delete)">
				<!-- wx-1-start -->
				<!-- #ifdef MP-WEIXIN -->
				<live-player :id="'sldVideo'+live_id" class="living" :src="liveDetail.pullUrl" object-fit="fillCrop" @statechange="statechange" autoplay="true"></live-player>
				<!-- #endif -->
				<!-- wx-1-end -->
			</block>

			<view class="live_footer">
				<view class="goods" @tap="showGoodsFun">
					<svgGroup type="to_shopping" width="85" height="91" px="rpx" :color="diyStyle_var['--color_video_main']">
					</svgGroup>
					<text>{{liveDetail.goodsNum * 1 > 99 ? '99+' : liveDetail.goodsNum }}</text>
				</view>
				<view class="input_con">
					<input type="text" name="talk_con" v-model="input_val" class="talk_con" :placeholder="$L('说点什么吧~')"
						placeholder-style="font-size:26rpx;color:#fff;font-weight:600" confirm-type="send" :disabled="(liveDetail.liveState == 4||live_ban||live_delete)" 
						@confirm="publishComment" maxlength="30" @input="handleInput" cursor-spacing="20" :adjust-position="true"></input>
					<view v-if="(liveDetail.liveState == 4||live_ban||live_delete)" class="talk_con_disabled" @click="notSendDamu">
						<!-- #ifdef H5 -->
						<img class="talk_con_disabled_img" :src="imgUrl+'svideo/talk_disabled.png'"></img>
						<!-- #endif -->
						<!-- #ifdef MP -->
						<image class="talk_con_disabled_img" :src="imgUrl+'svideo/talk_disabled.png'" mode="aspectFill"></image>
						<!-- #endif -->
					</view>
				</view>
				<view class="share" @tap="showShare">
					<image :src="imgUrl+'svideo/share.png'"></image>
					<text>{{$L('分享')}}</text>
				</view>
				<likeButton :throttle="100" :large="1" :showImgs="[likeIcon1,likeIcon2]" :high="220"
					:site="[50,150]" :live_ban="live_ban">
					<view class="share add_heart" @tap="add_heart">
						<image :src="imgUrl+'svideo/add_heart.png'"></image>
						<text>{{live_like_num}}</text>
					</view>
				</likeButton>
			</view>

			<!-- 分享 -->
			<view class="select-wrap" catchtouchmove="touchmoveshare" v-if="shareWrap">
				<view class="share-mode">
					<view class="empty_158"></view>
					<view class="share-img">
						<image :src="shareImg" mode="widthFix"></image>
					</view>
					<!-- wx-2-start -->
					<!-- #ifdef MP-WEIXIN -->
					<view class="ul">
						<button class="item" @tap="downloadImg">
							<image :src="imgUrl + 'svideo/hb_share.png'" mode="widthFix"></image>
							<text>{{$L('下载海报')}}</text>
						</button>
					</view>
					<!-- #endif -->
					<!-- wx-2-end -->
					
					<!-- #ifdef H5 -->
					<view class="ul">
						<view class="item">
							<view style="height:58px;">
								<image :src="imgUrl + 'svideo/hb_share.png'" mode="widthFix"></image>
							</view>
							<text class="share_h5_tip_text">{{$L('请长按图片进行保存')}}</text>
							<view class="share_h5_close_img" @tap="closeShare">
								<image class="share_h5" :src="imgUrl + 'goods_detail/share_close.png'"
									mode="scaleToFill"></image>
							</view>
							<view class="share_h5_img_bottom"></view>
						</view>
					</view>
					<!-- #endif -->
					<!-- #ifndef H5 -->
					<view class="close" @tap="closeShare">
						<image mode="aspectFit" :src="imgUrl+'store/share_close2.png'"></image>
					</view>
					<!-- #endif -->
				</view>
			</view>

			<view class="select-wrap" catchtouchmove="touchmoveshare" v-if="shareWrap2">
				<view class="share-mode">
					<view class="share-img"></view>
					<view class="ul">
						<!-- wx-3-start -->
						<!-- #ifdef MP-WEIXIN -->
						<button open-type="share" class="item">
							<image :src="imgUrl+'goods_detail/wx_share.png'" mode="widthFix"></image>
							<text>{{$L('微信好友')}}</text>
						</button>
						<!-- #endif -->
						<!-- wx-3-end -->
						<!-- #ifdef H5 -->
						<button @tap.stop="sldShareBrower(1)" class="item" v-if="isWeiXinBrower">
							<image :src="imgUrl+'goods_detail/wx_share.png'" mode="widthFix"></image>
							<text>{{$L('微信好友')}}</text>
						</button>
						<button @tap.stop="sldShareBrower(2)" class="item" v-if="isWeiXinBrower">
							<image :src="imgUrl+'svideo/pyq_share.png'" mode="widthFix"></image>
							<text>{{$L('微信朋友圈')}}</text>
						</button>
						<!-- #endif -->
						<view class="item" @tap="createhb">
							<image :src="imgUrl+'svideo/createhb.png'" mode="widthFix"></image>
							<text>{{$L('生成海报')}}</text>
						</view>
					</view>
					<view class="close" @tap="closeShare">
						<image :src="imgUrl+'store/share_close2.png'" mode="aspectFit"></image>
					</view>
				</view>
			</view>

			<view class="wx_brower_share_mask" v-if="showWeiXinBrowerTip">
				<view class="wx_brower_share_top_wrap">
					<image :src="imgUrl+'wx_share_tip.png'" mode="widthFix" @tap="closeShare"
						class="wx_brower_share_img"></image>
				</view>
			</view>

			<!-- 视频绑定的商品模块 start -->
			<view class="live_bind_goods_mask" v-if="showGoods">
				<view class="live_bind_goods">
					<view class="header">
						<text>{{$L('全部商品')}}({{liveDetail.goodsNum * 1 > 99 ? '99+' :
							liveDetail.goodsNum}})</text>
						<image :src="imgUrl+'svideo/close.png'" @tap="closeGoods"></image>
					</view>
					<scroll-view class="scroll_goods" scroll-y="true" @scrolltolower="getMoreGoods">
						<view class="live_user_tab_content">
							<!-- 商品列表item -->
							<livePlayGoods :goodsData="goodsList" :memberInfo='liveDetail'
								addCartIcon="addCart1" :eyeIcon="imgUrl+'svideo/eye.png'"
								:live_id="live_id" @click="addCart" />
							<!-- 数据加载完毕 -->
							<dataLoaded :showFlag="!hasmore&&goodsList.length>0" />
							<!-- 数据加载中 -->
							<!-- <dataLoading :showFlag="hasmore&&loading" /> -->
						</view>
					</scroll-view>
				</view>
			</view>
			<!-- 视频绑定的商品模块 end -->

			<!-- 回放视频暂停/播放的弹层展示 -->
			<view class="video_control" v-if="playFlag&&showPauseBtn||!playFlag" @tap="videoPlayControl">
				<image :src="showBtnIcn"></image>
			</view>

			<!-- 弹幕 start-->
			<view class="barrage_wrap">
				<!-- 进入直播间提示 -->
				<view class="notice" v-if="noticeList&&noticeList.length>0">
					<!-- <view class="notice"> -->
					<text>{{noticeList[0].msg}}</text>
				</view>

				<scroll-view class="barrage" scroll-y :scroll-into-view="toBottom">
					<view class="gonggao" v-if="liveDetail.liveNotice">
						<text class="title">{{$L('公告')}}</text>
						<text class="ba_txt">{{liveDetail.liveNotice}}</text>
					</view>
					<view v-for="(item, indexs) in msgList" :key="indexs" :index="indexs" v-if="msgList.length>0" :id="'item' + indexs">
						<view class="barrage_item">
							<text class="name" :style="'color:' + item.color_random">{{item.authorName}}</text>
							<text class="barrage_item_con">{{item.msg}}</text>
						</view>
					</view>
				</scroll-view>
			</view>
			<!-- 弹幕 end-->

			<!-- 直播状态信息 start -->
			<view class="live_stop" v-if="show_live_stop||live_recording||live_ban||live_delete">
				<view class="live_stop_mask">
					<view class="live_stop_bg" :style="{backgroundImage:'url('+ liveDetail.liveCover +')'}"></view>
				</view>
				<!-- 直播结束 -->
				<view class="live_stop_container" v-if="show_live_stop">
					<view class="live_stop_title">
						{{live_stop_info.forbidReason?live_stop_info.forbidReason:$L('直播已结束')}}
					</view>
					<view class="live_stop_time">
						{{$L('直播时长')}}：{{live_stop_info.playbackTime}}
					</view>
					<view class="live_stop_like_watch">
						{{$L('人气')}} {{live_stop_info.popularityNum * 1}} {{$L('观看')}} {{live_stop_info.viewingNum
						* 1}}
					</view>
					<view @click="goBack" class="return_refresh">
						{{$L('返回')}}
					</view>
				</view>
				<!-- 直播录制中 -->
				<view class="live_stop_container" v-if="live_recording">
					<view class="live_stop_title">
						{{$L('直播已结束')}}
					</view>
					<view class="live_stop_recording">
						{{$L('正在录制中，录制完成才可以观看')}}
					</view>
					<!-- <view class="live_stop_like_watch">
						获赞 {{liveDetail.live.like_num}}    观看 {{liveDetail.live.click_num}}
					</view> -->
					<view @click="goBack" class="return_refresh">
						{{$L('返回')}}
					</view>
				</view>
				<!-- 直播被禁止 -->
				<view class="live_stop_container" v-if="live_ban">
					<view class="live_stop_title">
						{{live_ban_info.msg}}
					</view>
					<view class="live_stop_recording">
						{{live_ban_info.remark}}
					</view>
					<view @click="goBack" class="return_refresh">
						{{$L('返回')}}
					</view>
				</view>
				<!-- 直播被删除 -->
				<view class="live_stop_container" v-if="live_delete">
					<view class="live_stop_title">
						{{$L('该直播已被删除')}}
					</view>
					<view @click="goBack" class="return_refresh">
						{{$L('返回')}}
					</view>
				</view>
			</view>
			<!-- 直播状态信息 end -->
		</view>
	</view>
</template>

<script>
	import { checkPageHasMore, colorArray, initNum } from "@/utils/live";
	import request from "@/utils/request";
	import dataLoaded from "../dataLoaded.vue";
	import livePlayGoods from "../live/livePlayGoods.vue";
	import likeButton from '../like-button/like-button.vue'
	import io from '@hyoga/uni-socket.io';
	import { mapState, mapMutations } from 'vuex';

	export default {
		components: {
			livePlayGoods,
			likeButton,
			dataLoaded,
		},
		data() {
			return {
				isShow: false,
				key: '',
				pageSize: 10, //商品每页数量
				pn: 1, //商品的当前页
				//当前直播id
				loading: false,
				//数据加载状态
				liveDetail: {},
				//直播详情
				settingData: '',
				//平台设置信息
				imgUrl: getApp().globalData.imgUrl,
				//图片地址
				bgStyle: 'background-size:cover;background-position:center center;background-repeat: no-repeat;',
				//背景图片样式
				shareWrap: false,
				// 展示分享弹层
				shareWrap2: false,
				// 展示分享弹层
				shareImg: '',
				// 分享图片
				goodsList: [],
				//直播绑定的商品
				hasmore: true,
				//是否还有数据，用于页面展示
				showGoods: false,
				//是否显示商品弹层
				playFlag: true,
				//视频是否播放
				showPauseBtn: false,
				//是否显示暂停按钮
				showBtnIcn: '',
				//视频播放控制层按钮图片
				colorArray: colorArray,
				//颜色数组
				msgList: [],
				//弹幕内容
				noticeList: [],
				//消息通知列表
				input_val: '',
				//输入框内容
				toBottom: '',
				//弹幕滚动到指定的元素
				img_path: [
					[{
						x: 30,
						y: 400
					}, {
						x: 70,
						y: 300
					}, {
						x: -50,
						y: 150
					}, {
						x: 30,
						y: 0
					}],
					[{
						x: 30,
						y: 400
					}, {
						x: 30,
						y: 300
					}, {
						x: 80,
						y: 150
					}, {
						x: 30,
						y: 0
					}],
					[{
						x: 30,
						y: 400
					}, {
						x: 0,
						y: 90
					}, {
						x: 80,
						y: 100
					}, {
						x: 30,
						y: 0
					}]
				],
				// 指定视频初始播放位置
				live_like_num: '--',
				//人气数
				live_click_num: '--', //观看数,
				liveStateInfo: "",
				isWeiXinBrower: false, //是否微信浏览器
				showWeiXinBrowerTip: false, //微信浏览器分享的提示操作
				stat_end: 0, //终端，默认为1，pc端
				show_live_stop: false, //是否显示直播结束提示
				live_stop_info: {}, //显示直播结束提示信息
				live_recording: false, //是否显示直播录制中提示
				live_ban: false, //直播是否被禁止
				live_ban_info: {
					msg: '',
					remark: ''
				},
				live_delete: false,
				likeIcon1: getApp().globalData.imgUrl + 'svideo/1.png',
				likeIcon2: getApp().globalData.imgUrl + 'svideo/2.png',
				pauOrPl: true,
				videoPlay: true, //视频是否播放--用户判断暂停按钮显示
				preventSend:false,
				viewHeight:uni.getSystemInfoSync().windowHeight
			};
		},
		props: {
			initial_time: {
				type: String,
				default: 1
			},
			labelId: Number,
			live_id: Number,
			decoIndex: Number,
			index: Number,
			showHide: {
				type: String,
				default: 'hide'
			},
		},
		computed: {
			...mapState(['hasLogin', 'cartData', 'userInfo'])
		},
		watch: {
			//wx-4-start
			// #ifdef MP-WEIXIN
			showHide: {
				handler() {
					if (this.showHide == 'show') {
						console.log('showHide')
						this.initSocket();
						uni.createVideoContext('sldVideo'+this.live_id,this).play();
					} else {
						this.closeSocket();
						uni.createVideoContext('sldVideo'+this.live_id,this).pause();
					}
				},
				deep: true
			},
			// #endif
			//wx-4-end
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		created: function (options) {
			//wx-5-start
			// #ifdef MP-WEIXIN
			let launch_options = wx.getLaunchOptionsSync()
			// #endif
			//wx-5-end
			this.initData();
			this.getPlatform();
			
			this.shareImg = getApp().globalData.apiUrl + 'v3/video/front/video/live/share/shareImage?liveId='
				+ this.live_id + '&source=' + this.stat_end;
				
			// #ifdef H5
			this.isWeiXinBrower = this.$isWeiXinBrower();
			// #endif
		},
		beforeMount() {
			
		},
		mounted() {
			this.getPlatform();
	
			//wx-6-start
			// #ifdef MP-WEIXIN
			let ctx = uni.createLivePlayerContext('sldVideo'+this.live_id, this)
			ctx.play()
			// #endif
			//wx-6-end
			// #ifdef H5
			if (this.player_video) {
				this.player_video.play()
			}
			// #endif
			
			this.updateData = {}
			if (this.decoIndex >= 0 && this.index >= 0) {
				this.updateData.decoIndex=this.decoIndex
				this.updateData.index=this.index
			} else if (this.index >= 0) {
				this.updateData.index=this.index
			}
			this.prePage = this.$api.prePage()
		},

		/**
		 * 生命周期函数--监听页面卸载
		 */
		destroyed: function () {
			if (this.socket) {
				this.closeSocket();
			}
			
			if (this.videoContext && this.videoContext.stop != undefined) {
				this.videoContext.stop();
			}

			// #ifdef H5
			if (this.player_video) {
				this.player_video.dispose()
			}
			// #endif
			
			
		},

		beforeDestroy() {
			this.shareWrap = false
			this.shareWrap2 = false
		},
		
		methods: {
			//处理遮罩层物理返回键
			onBackPress() { },
			//获取当前终端的方法
			getPlatform() {
				//判断终端
				//#ifdef MP-TOUTIAO
				this.stat_end = 1;
				//#endif
				//#ifdef MP-ALIPAY
				this.stat_end = 2;
				//#endif
				//#ifdef MP-BAIDU
				this.stat_end = 3;
				//#endif
				//wx-7-start
				//#ifdef MP-WEIXIN
				this.stat_end = 4;
				//#endif
				//wx-7-end
			},
			statechange(e) {},
			returnShow() {
				let videoContext = uni.createVideoContext("sldVideo"+this.live_id);
				if (videoContext) {
					videoContext.stop(); //停止播放视频
				}
				if (videoContext) {
					setTimeout(() => {
						//300毫秒延迟
						videoContext.seek(this.initial_time);
						videoContext.play(); //开始播放视频
					}, 300);
				}
			},

			//初始化数据
			initData() {
				this.show_live_stop = false
				this.getLiveInfo();
				this.getLiveGoods();
			},

			//获取直播详情
			getLiveInfo() {
				uni.showLoading({
					title:"加载中..."
				})
				let {
					live_id,
					liveDetail
				} = this;
				let _this = this;
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/live/playingPage'
				param.method = 'GET'
				param.data.liveId = live_id
				this.$request(param).then(res => {
					uni.hideLoading()
					if (res.state == 200) {
						liveDetail = res.data;
						this.$emit('shareVideo',liveDetail)
						if (liveDetail.liveState == 5) {
							//视频被删除
							this.live_delete=true
						} else if (liveDetail.playState == 2) {
							//禁止播放
								this.live_ban=true
								this.live_ban_info={
									msg: _this.$L('该直播已被禁止'),
									remark: liveDetail.forbidReason
								}
						} else if (liveDetail.liveState == 3) {
							//录制中
							this.live_recording=true
						} else {
							// #ifdef H5
							var video = document.createElement('video');
							liveDetail.replayUrl = liveDetail.replayUrl.split(',')[0];
							video.id = 'myVideo' + live_id;
							video.style = `width:100vw;height:100vh;min-height:${this.viewHeight}px`;
							video.setAttribute('class','video-js vjs-default-skin vjs-big-play-centered');
							video.setAttribute('webkit-playsinline', true);
							video.setAttribute('playsinline', true);
							video.setAttribute('x5-video-player-type', 'h5');
							video.setAttribute('x5-video-player-fullscreen', true);
							video.setAttribute('x-webkit-airplay', true);
							this.$refs.videoWrapHls.$el.appendChild(video);
							this.player_video = videojs('myVideo' + live_id, {
								controls: true,
								controlBar: {
									children: [{
										name: 'playToggle'
									}, // 播放按钮
									{
										name: 'progressControl'
									}, // 播放进度条
									]
								},
								autoplay: 'autoplay',
								preload: 'auto',
								poster: liveDetail.liveCover,
								sources: [{
									src: liveDetail.liveState == 2 ? liveDetail.m3u8Url :
										liveDetail.replayUrl,
									type: liveDetail.liveState == 2 ? "application/x-mpegURL" :
										"video/mp4"
								}]
							}, function onPlayerReady() {
								this.on("touchend",function(){
									let currentId = 'myVideo' + live_id;
									let current_video = videojs(currentId)
									if (_this.videoPlay) {
										_this.videoPlay = false;
										current_video.pause();
									} else {
										_this.videoPlay = true;
										current_video.play();
									}
								})
								this.on("play", function(){
									_this.videoPlay = true;
								});
								this.on("pause", function(){
									_this.videoPlay = false;
								});
							})
							// #endif
						}
						liveDetail.replayUrl = liveDetail.replayUrl.split(',')[0];
						this.liveDetail=liveDetail
						this.isShow=true
						this.live_click_num=res.data.viewingNum
						if (this.socket) {
							this.socket.emit("update_user", {
								author_name: liveDetail.author.authorName,
								author_id: liveDetail.author.authorId ? liveDetail.author.authorId : 1,
								live_id: live_id ? live_id : 1
							});
						} else {
							if(this.showHide=='show'){
								this.initSocket()
							}
						}
					} else {
						//禁止播放
						this.live_ban=true
						this.live_ban_info={
							msg: _this.$L('该直播已被禁止'),
							remark: liveDetail.forbidReason ? liveDetail.forbidReason : ''
						}
					}
				})
			},

			//关闭分享
			closeShare() {
				this.shareWrap=false
				this.shareWrap2=false
				this.showWeiXinBrowerTip=false //微信浏览器提示层
			},

			//分享 type：分享类型 0 图文 2图片，scene 场景 WXSceneSession：分享朋友  WXSenceTimeline：分享朋友圈
			sldShare(type, scene) {

				let {
					liveDetail,
					live_id,
					shareImg
				} = this;
				let shareData = {};
				if (type == 0) {
					shareData.href = getApp().globalData.apiUrl + 'extra/live/livePlay?live_id=' + live_id;
					shareData.title = this.$L('我正在看') + liveDetail.memberNickname ? liveDetail.memberNickname : liveDetail
						.memberName +
						this.$L('的精彩内容，快来围观~');
					shareData.summary = liveDetail.liveName;
					shareData.imageUrl = liveDetail.liveCover;
				} else if (type == 2) {
					shareData.imageUrl = shareImg;
				}

				this.$weiXinAppShare(type, scene, shareData);
				this.closeShare(); //关闭分享
			},

			//浏览器分享
			sldShareBrower(type) {
				let {
					liveDetail,
					live_id,
					labelId,
					shareImg
				} = this;
				//展示分享提示
				this.showWeiXinBrowerTip = true;
				this.shareWrap2 = false;
				//微信浏览器分享
				this.$weiXinBrowerShare(type, {
					title: this.$L('我正在看') + liveDetail.memberNickname + this.$L('的精彩内容，快来围观~'),
					desc: liveDetail.liveName,
					link: `${getApp().globalData.apiUrl}extra/live/livePlay?live_id=${live_id}&labelId=${labelId}`,
					imgUrl: liveDetail.liveCover,
				});

			},

			prevImg() {
				wx.previewImage({
					urls: [this.shareImg],
				})
			},

			touchmoveshare() {
				return false;
			},

			initSocket() {

				if (this.socket) {
					this.closeSocket();
				}
				const {
					live_id,
					liveDetail
				} = this;
				const authorInfo = liveDetail.author;
				let userInfo = {
					live_id: live_id ? live_id : 1
				};
				if (authorInfo && authorInfo.authorId) {
					userInfo.author_name = authorInfo.memberNickname ? authorInfo.memberNickname : authorInfo.memberName;
					userInfo.author_id = authorInfo.authorId ? authorInfo.authorId : 0;
				} else {
					userInfo.author_name = this.$L('游客');
					userInfo.author_id = 0;
				}
				this.socket = io(getApp().globalData.socketUrl, {
					reconnection: true,
					jsonp: true,
					transports: ['websocket', 'polling'],
					timeout: 5000,
					query: `author_name=${userInfo.author_name}&author_id=${userInfo.author_id}&live_id=${userInfo.live_id}&is_own=${liveDetail.isSelf ? 1 : 0}`
				});

				this.socket.on("connect", () => {
					//给服务端发送消息
					console.log("connect")
					this.socket.emit("update_user", userInfo);

					this.socket.on("get_msg", e => {
						console.log("get_msg",e)
						if (typeof e.msg === 'string') {
							this.socketHandle('wec', e);
						} else {
							this.socketHandle('msg', e.msg);
						}
					}); //获取服务端新人信息

					this.socket.on("get_new_user", e => {
						this.socketHandle2('new', e);
					});
					this.socket.on("disconnect", function () { });
				}); //获取关注的返回信息

				this.socket.on("get_follow_msg", e => {
					this.socketHandle2('follow', e);
				}); //获取直播点击数

				this.socket.on("get_click_num", e => {
					this.clickNumHandle(e);
				}); //获取直播人气数

				this.socket.on("get_like_num", e => {
					this.likeNumHandle(e);
				}); // 直播结束

				this.socket.on('stop_live', e => {
					this.handleStopLive(e);
				});
			},

			//关闭socket
			closeSocket() {
				if (this.socket) {
					console.log('close')
					this.socket.close();
				}
			},

			socketHandle(type, msg) {
				let {
					msgList
				} = this;

				if (msg.type) {
					return;
				}

				msg.type = type;
				msg.color_random = colorArray[Math.floor(Math.random() * 8)];
				msgList.push(msg);
				
				this.msgList = msgList
				let randomNum = (this.msgList.length - 1 + Math.random() * 1).toFixed(2)
				let randonNumText = randomNum.toString().replace('.', '')
				this.toBottom = `item${randonNumText}`
				this.$nextTick(() => {
					this.toBottom = this.msgList.length ? `item${this.msgList.length - 1}` : 'item0'
				})
			},

			//直播结束
			handleStopLive(e) {
				this.liveStateInfo=JSON.parse(e)
				this.show_live_stop=true
				this.live_stop_info=JSON.parse(e)
			},
			
			notSendDamu(){
				this.$api.msg(this.$L('录播不能发送消息'))
			},

			//获取服务端新人信息
			socketHandle2(type, msg) {
				let {
					noticeList
				} = this;

				if (msg.type) {
					return;
				}

				if (noticeList.filter(el => el.type === 'follow').length > 0 && type === 'follow') {
					return;
				}

				msg.type = type;
				msg.timer = setTimeout(() => {
					this.noticeRemoveItem(type);
				}, 2000);

				if (type === 'follow') {
					noticeList.push(msg);
				} else {
					let index = noticeList.findIndex(el => el.type === 'new');

					if (index > -1) {
						noticeList[index] = msg;
					} else {
						noticeList.push(msg);
					}
				}
				this.noticeList=noticeList
			},

			noticeRemoveItem(type) {
				let {
					noticeList
				} = this;
				let index = noticeList.findIndex(el => el.type === type);

				if (index > -1) {
					clearTimeout(noticeList[index].timer);
					noticeList.splice(index, 1);
				}

				this.noticeList=noticeList
			},

			// 点击量处理
			clickNumHandle(e) {
				let {
					liveDetail,
					live_click_num
				} = this;
				this.liveDetail.clickNum = e.click_num;
				this.live_click_num=initNum(e.click_num)
				
				//更新视频观看数量
				if(this.prePage){
					this.updateData.live_click_num = this.live_click_num
					if(typeof this.prePage.updateView == 'function'){
						this.prePage.updateView(this.updateData)
					}
					uni.$emit('updateListWatchNum',{index:this.index,num:this.live_click_num})
				}
			},

			// 人气
			likeNumHandle(e) {
				this.liveDetail.likeNum = e.like_num;
				this.live_like_num=initNum(e.like_num)
			},
			
			handleInput(){
				this.preventSend = false
			},

			// 发送消息
			publishComment(e) {
				const {
					live_id,
					liveDetail
				} = this;
				
				

				if (this.hasLogin && liveDetail.authorId != null) {
					let content = e.detail.value; //没有内容的话直接结束

					if (!content.trim()) {
						return false;
					}
					
					if(this.preventSend){
						return
					}
					this.preventSend = true

					let msg = {
						author_id: liveDetail.author.authorId,
						author_name: liveDetail.author.memberNickname,
						live_id: live_id,
						msg: content
					};
					console.log("send_msg", msg)
					this.socket.emit("send_msg", msg);
					//清空输入框的内容
					this.input_val=''

				} else {
					getApp().globalData.goLogin(this.$Route);
				}
			},

			//增加人气事件
			add_heart() {
				if (this.live_ban) {
					uni.showToast({
						title: this.live_ban_info.msg,
						icon: 'none'
					})
					return;
				}else if(this.live_delete){
					return;
				}
				
				
				
				let {
					live_id,
					liveDetail
				} = this;

				if (this.hasLogin) {
					this.socket.emit("update_like_num", {
						author_id: liveDetail.authorId,
						live_id: live_id
					});
					if (this.index >= 0) {
						let updateData = {
							index: this.index
						}
						uni.$emit('updateLikeNum', updateData)
					}
				} else {
					getApp().globalData.goLogin(this.$Route);
				}
			},

			// 返回上级页面
			goBack() {
				this.$Router.back(1)
			},

			//跳转商品详情页
			goGoodsDetail(data) {
				if (data.pageLength > 4) {
					this.$Router.replace(data.url)
				} else {
					this.$Router.push(data.url)
				}
			},

			//关注、取消关注事件
			collect(e) {
				let {
					liveDetail
				} = this
				if (this.hasLogin) {
					let param = {}
					param.data = {}
					param.method = 'POST'
					param.data.authorId = this.liveDetail.authorId
					if (this.liveDetail.isFollow) {
						param.url = 'v3/video/front/video/cancelFollow'
					} else {
						param.url = 'v3/video/front/video/followAuthor'
					}
					this.$request(param).then(res => {
						if (res.state == 200) {
							this.liveDetail.isFollow = this.liveDetail.isFollow == true ? false : true;
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							});
						}
					})
				} else {
					getApp().globalData.goLogin(this.$Route);
				}
			},

			//进入作者页面-监听事件处理
			onHandleGoLiveUserCenter(data) {
				if (data.pageLength > 4) {
					this.$Router.replace(data.url)
				} else {
					this.$Router.push(data.url)
				}
			},

			//进入作者页面
			goLiveUserCenter(author_id) {
				let page = getCurrentPages();
				let len = page.length;
				if (len > 4) {
					this.$Router.replace({
						path: '/extra/user/my',
						query: {
							author_id
						}
					})
				} else {
					this.$Router.push({
						path: '/extra/user/my',
						query: {
							author_id
						}
					})
				}
			},

			//分享点击事件
			showShare() {
				if (this.live_ban) {
					uni.showToast({
						title: this.live_ban_info.msg,
						icon: 'none'
					})
					return;
				}
				this.shareWrap2=true
			},

			//生成海报事件
			createhb() {
				this.shareWrap=true
				this.shareWrap2=false
			},

			//下载海报
			downloadImg() {
				let {
					shareImg
				} = this;
				let _this = this;
				uni.downloadFile({
					url: shareImg,
					success: res_info => {
						if (res_info.statusCode == 200) {
							//wx-8-start
							//#ifdef MP-WEIXIN
							uni.getSetting({
								success(res_down) {
									if (!res_down.authSetting['scope.writePhotosAlbum']) {
										uni.showModal({
											title: _this.$L('提示'),
											content: _this.$L('您好,需要开启相册权限'),
											showCancel: false,
											success(res) {
												if (res.confirm) {
													uni.authorize({
														scope: 'scope.writePhotosAlbum',
														success() {
															// 用户已经同意,后续调用时不会弹窗询问
															_this.saveHb(res_info
																.tempFilePath);
														},
														fail() {
															//拒绝授权
															uni.showToast({
																title: _this
																	.$L(
																		'抱歉，没有授权无法下载海报'
																	),
																icon: 'none'
															});
														}

													});
												}
											}

										});
									} else {
										_this.saveHb(res_info.tempFilePath);
									}
								}
							});
							//#endif
							//wx-8-end
						} else {
							uni.showToast({
								title: _this.$L('下载失败'),
								icon: 'none'
							});
						}
					}
				});
			},

			/**
			 * 保存图片
			 */
			saveHb: function (img) {
				let _this = this;
				uni.saveImageToPhotosAlbum({
					filePath: img,
					success: function (data) {
						_this.shareWrap=false
						_this.shareWrap2=false

						uni.showToast({
							title: _this.$L('已保存到本地'),
							icon: 'success',
							duration: 2000
						});
					}
				});
			},

			//获取直播商品
			getLiveGoods() {
				this.loading=true
				let {
					goodsList,
					hasmore,
					live_id
				} = this;
				let param = {}
				param.data = {}
				param.data.pageSize = this.pageSize
				param.data.current = this.pn
				param.data.liveId = live_id
				param.url = 'v3/video/front/video/live/goodsList'
				param.method = 'GET'
				this.$request(param).then(res => {
					if (res.state == 200) {
						let list = res.data.list;

						if (this.pn == 1) {
							this.goodsList = list;
						} else {
							this.goodsList = this.goodsList.concat(list);
						}

						if (checkPageHasMore(res.data.pagination)) {
							this.pn++;
						} else {
							this.hasmore = false;
						}
					}
					this.loading = false
				})
			},

			//加入购物车事件
			addCart(good) {
				if (this.live_ban) {
					uni.showToast({
						title: this.live_ban_info.msg,
						icon: 'none'
					})
					return;
				}
				if (!this.hasLogin) {
					let cart_list = {
						storeCartGroupList: [{
							promotionCartGroupList: [{
								cartList: [{
									buyNum: 1,
									goodsId: good.goodsId - 0,
									productId: good.defaultProductId ? good.defaultProductId :
										good.productId,
									productImage: good.goodsImage,
									goodsName: good.goodsName,
									isChecked: 1,
									productPrice: good.goodsPrice,
									productStock: good.goodsStock
								}],
							}],
							storeId: good.storeId,
							storeName: good.storeName,
							checkedAll: true
						}],
						checkedAll: true,
						invalidList: []
					}
					//未登录加入本地缓存
					let local_cart_list = uni.getStorageSync('cart_list') //购物车列表本地缓存
					if (local_cart_list) { //如果不是第一次存储
						let tmp_list1 = []
						let tmp_list2 = []
						cart_list.storeCartGroupList.forEach(item => {
							item.promotionCartGroupList.forEach(item1 => {
								item1.cartList.forEach(item2 => {
									local_cart_list.storeCartGroupList.forEach(v => {
										v.promotionCartGroupList.forEach(v1 => {
											v1.cartList.forEach(v2 => {
												if (v2.productId == item2
													.productId && v
														.storeId == item
														.storeId) {
													tmp_list1.push(v)
												}
											})
										})
									})
									tmp_list2 = local_cart_list.storeCartGroupList.filter(v => {
										return v.storeId == item.storeId
									})
								})
							})
						})
						if (tmp_list1.length > 0 && tmp_list2.length > 0) { //同一店铺同一商品
							local_cart_list.storeCartGroupList.map(item => {
								item.promotionCartGroupList.map(item1 => {
									item1.cartList.map(item2 => {
										if (item2.productId == this.productId && item.storeId ==
											this.storeInf.storeId) {
											item2.buyNum += this.currentSpecNum
										}
									})
								})
							})
						} else if (tmp_list1.length == 0 && tmp_list2.length > 0) { //同一店铺不同商品
							local_cart_list.storeCartGroupList.map(item => {
								if (item.storeId == this.storeInf.storeId) {
									item.promotionCartGroupList.map(item2 => {
										item2.cartList.push(cart_list.storeCartGroupList[0]
											.promotionCartGroupList[0].cartList[0])
									})
								}
							})
						} else { //不同店铺不同商品
							local_cart_list.storeCartGroupList.push(cart_list.storeCartGroupList[0])
						}
						// 未登录购物车展示数据
						uni.setStorage({
							key: 'cart_list',
							data: local_cart_list,
							success: function () {
								//更新购物车数量和购物车数据
							}
						});

					} else {
						uni.setStorage({
							key: 'cart_list',
							data: cart_list,
							success: function () {
								//更新购物车数量和购物车数据
							}
						});
					}
					uni.showToast({
						title: this.$L('加入购物车成功！'),
						icon: 'none',
						duration: 700
					})
				} else { //已登录
					this.$request({
						url: 'v3/business/front/cart/add',
						data: {
							productId: good.defaultProductId ? good.defaultProductId : good.productId,
							number: 1,
						},
						method: 'POST'
					}).then(res => {
						if (res.state == 200) {
							//更新购物车数量
							this.$api.msg(res.msg);
						} else {
							this.$api.msg(res.msg);
						}
					}).catch((e) => { })
				}
			},

			showGoodsFun() {
				let {
					showGoods,
					liveDetail
				} = this;

				if (liveDetail.goodsNum * 1 > 0) {
					this.showGoods=true
				}
			},

			//关闭商品弹层
			closeGoods() {
				let {
					showGoods
				} = this;
				this.showGoods=false
			},

			//绑定的商品分页事件
			getMoreGoods() {
				if (this.hasmore) {
					this.getLiveGoods();
				}
			},

			//视频暂停/继续播放事件
			videoPlayControl() {
				let {
					playFlag,
					showPauseBtn
				} = this;
				let videoContext = uni.createVideoContext("sldVideo"+this.live_id);

				if (playFlag) {
					if (!showPauseBtn) {
						this.showPauseBtn=true
						this.showBtnIcn=this.pauseBtnIcon
						//3s后自动消失

						setTimeout(() => {
							this.showPauseBtn=false
						}, 3000);
					} else {
						videoContext.pause(); //暂停播放
						this.showPauseBtn=false
						this.playFlag=false
						this.showBtnIcn=this.playBtnIcon
					}
				} else {
					videoContext.play(); //开始播放
					this.playFlag=true,
					this.showPauseBtn=false
				}
			},

		
			chansgedd() {
				// #ifdef H5
				let currentId = 'myVideo' + this.live_id; // 获取当前直播id
				let current_video = videojs(currentId)
				if (this.videoPlay) {
					this.videoPlay = false;
					current_video.pause();
				} else {
					this.videoPlay = true;
					current_video.play();
				}
				// #endif
				// #ifdef MP
				if (this.videoPlay) {
					this.videoPlay = false;
					uni.createVideoContext('sldVideo' + this.live_id, this).pause();
				} else {
					this.videoPlay = true;
					uni.createVideoContext('sldVideo' + this.live_id, this).play();
				}
				// #endif
				
			},
			bindplay() {
				this.videoPlay = true;
			},
			bindpause() {
				this.videoPlay = false;
			},
			
			updateCollect(author_id){
				if(this.liveDetail.authorId==author_id){
					if(this.liveDetail.isFollow){
						this.liveDetail.isFollow=false
					}else{
						this.liveDetail.isFollow=true
					}
				}
			}
		}
	};
</script>
<style>
	page {
		width: 750rpx;
		margin: 0 auto;
		position: relative;
		overflow-x: hidden;
		overflow: hidden;
		height: 100vh;
	}

	.live_play {
		width: 750rpx;
		height: 100vh;
		overflow: hidden;
	}

	.live_back,
	.living {
		width: 750rpx;
		height: 100vh;
		margin-left: 0rpx;

		overflow: hidden;
	}

	

	.live_header {
		position: absolute;
		top: 80rpx;
		left: 20rpx;
		z-index: 2;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: flex-start;
		height: 80rpx;
		width: 750rpx;
		overflow-x: hidden;
		margin-left: calc((100vw - 750rpx) / 2);
	}

	.live_header text {
		color: #fff;
	}

	.live_header .go_back {
		width: 45rpx;
		height: 47rpx;
	}

	.live_header .avator {
		width: 78rpx;
		height: 78rpx;
		border-radius: 50%;
		border: 2px solid #fff;
		margin-left: 8rpx;
	}

	.live_header .mem_info {
		margin-left: 20rpx;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-content: space-between;
	}

	.live_header .mem_info .name {
		max-width: 250rpx;
		color: #fff;
		font-size: 28rpx;
		line-height: 32rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		font-weight: 600;
		margin: 5rpx 0 6rpx;
	}

	.live_header .mem_info .stat_num {
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
	}

	.live_header .mem_info .stat_num text {
		color: #fff;
		font-size: 22rpx;
		line-height: 36rpx;
		white-space: nowrap;
		font-weight: 600;
	}

	.live_header .live_fllow {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		width: 120rpx;
		height: 50rpx;
		background: #fc1c1c;
		/* #999 */
		border-radius: 25rpx;
		margin-left: 25rpx;
	}

	.live_header .live_fllowEd {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		width: 135rpx;
		height: 50rpx;
		background: #999;
		/* #999 */
		border-radius: 25rpx;
		margin-left: 25rpx;
	}

	.live_header .live_fllowEd image {
		width: 46rpx;
		height: 46rpx;
	}

	.live_header .live_fllowEd text {
		color: #fff;
		font-size: 24rpx;
		margin-right: 8rpx;
	}

	.live_header .live_fllow image {
		width: 46rpx;
		height: 46rpx;
	}

	.live_header .live_fllow text {
		color: #fff;
		font-size: 24rpx;
		margin-right: 8rpx;
	}
	
	.live_pause {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%,-50%);
		z-index: 99;
	}
	
	.live_pause .live_pause_img {
		width: 100rpx;
		height: 115rpx;
	}

	.live_footer {
		position: fixed;
		left: 0;
		right: 0;
		margin: 0 auto;
		z-index: 2;
		/* left: 40rpx; */
		bottom: 70rpx;
		padding-left: 40rpx;
		width: 750rpx;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
		overflow-x: hidden;
		padding-top: 30rpx;
	}

	/* wx-9-start */
	/* #ifdef MP-WEIXIN */
	.live_footer {
		bottom: 60rpx;
	}
	/* #endif */
	/* wx-9-end */

	.live_footer .goods {
		width: 85rpx;
		height: 91rpx;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		position: relative;
	}

	.live_footer .goods image {
		width: 100%;
		height: 100%;
	}

	.live_footer .goods text {
		position: absolute;
		bottom: 24rpx;
		left: 47%;
		transform: translateX(-50%);
		line-height: 23rpx;
		color: #fff;
		font-size: 22rpx;
	}

	.live_footer .talk_con {
		width: 365rpx;
		height: 65rpx;
		border-radius: 30rpx;
		background: rgba(0, 0, 0, 0.3);
		padding: 0 20rpx;
		color: #fff;
		font-weight: 600;
		font-size: 26rpx;
	}
	
	.live_footer .talk_con_disabled {
		display: flex;
		align-items: center;
		position: absolute;
		left: 0rpx;
		top:0;
		z-index: 99;
		width: 377rpx;
		height: 68rpx;
		/* #ifdef MP */
		width: 377rpx;
		height: 65rpx;
		/* #endif */
		border-radius: 30rpx;
	}
	.live_footer .talk_con_disabled_img {
		width: 377rpx;
		height: 68rpx;
		/* #ifdef MP */
		width: 377rpx;
		height: 65rpx;
		/* #endif */
		object-fit:cover;
		border-radius:50rpx;
		background-size: cover;
		background-repeat: no-repeat;
		background-position: center center;
	}
	
	.input_con{
		position:relative;
		margin-left: 16rpx;
	}

	.live_footer .share {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: center;
		width: 77rpx;
		margin-left: 21rpx;
	}

	.live_footer .share image {
		width: 65rpx;
		height: 65rpx;
		font-weight: 600;
	}

	.live_footer .share text {
		color: #fff;
		font-size: 26rpx;
		margin-top: 10rpx;
	}

	.live_footer .add_heart image:active {
		-webkit-animation: 1s seconddiv;
	}

	@keyframes seconddiv {
		0% {
			transform: scale(1.3, 1.3);
		}

		50% {
			transform: scale(1.2, 1.2);
		}

		100% {
			transform: scale(1, 1);
		}
	}

	.select-wrap {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		margin: 0 auto;
		width: 750rpx;
		height: 100vh;
		background-color: rgba(0, 0, 0, 0.45);
		z-index: 9999;
	}

	.select-wrap .share-mode {
		position: absolute;
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.select-wrap .share-mode .share-img {
		width: 540rpx;
		border-radius: 20rpx;
		overflow: hidden;
	}

	.select-wrap .share-mode .share-img image {
		width: 100%;
		height: 0;
		border-radius: 20rpx;
	}

	.select-wrap .share-mode .ul {
		display: flex;
		align-items: center;
		width: 100%;
		justify-content: space-evenly;
	}

	.share-mode .item {
		display: flex;
		flex-direction: column;
		align-items: center;
		text-align: center;
		cursor: pointer;
		border: none;
		margin: 0;
		padding: 0;
		line-height: 1;
		background-color: transparent;
		height: auto;
	}

	.share-mode .item::after {
		border: none;
	}

	.share-mode .item image {
		width: 106rpx;
		height: 0;
	}

	.share-mode .item text {
		color: #fff;
		font-size: 24rpx;
		margin-top: 30rpx;
	}

	.select-wrap .close {
		width: 750rpx;
		height: 120rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.select-wrap .close image {
		width: 30rpx;
		height: 30rpx;
		/* padding: 30rpx; */
	}

	.share-mode .share-img {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	/* 视频绑定的商品模块 start */

	.live_bind_goods_mask {
		width: 750rpx;
		height: 100vh;
		position: absolute;
		left: 0;
		bottom: 0;
		z-index: 100;
		background: rgba(0, 0, 0, 0.45);
	}

	.live_bind_goods {
		width: 750rpx;
		height: 850rpx;
		position: fixed;
		left: 0;
		bottom: 0;
		z-index: 4;
		border-radius: 15rpx 15rpx 0 0;
		background: #f8f8f8;
		right: 0;
		margin: 0 auto;
	}

	.live_bind_goods .header {
		width: 750rpx;
		height: 100rpx;
		padding: 0 20rpx;
		background: #fff;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		border-radius: 15rpx 15rpx 0 0;
	}

	.live_bind_goods .header text {
		color: #2d2d2d;
		font-size: 32rpx;
	}

	.live_bind_goods .header image {
		width: 47rpx;
		height: 47rpx;
	}

	.scroll_goods {
		height: 750rpx;
	}

	/* 视频绑定的商品模块 end */

	.video_control {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		top: 0;
		background: transparent;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: flex-start;
		z-index: 5;
	}

	.video_control image {
		width: 102rpx;
		height: 102rpx;
		margin-top: calc(100vh/3);
	}

	.empty_158 {
		width: 750rpx;
		overflow-x: hidden;
		height: 158rpx;
	}

	/***** 弹幕 start *****/

	.barrage_wrap {
		position: absolute;
		/* #ifndef MP-WEIXIN */
		bottom: 189rpx;
		/* #endif */
		/* wx-10-start */
		/* #ifdef MP-WEIXIN */
		bottom: 160rpx;
		/* #endif */
		/* wx-10-end */
		padding-left: 19rpx;
		left: calc((100vw - 750rpx)/2);
		width: 500rpx;
		border-radius: 10rpx;
		/* background: rgba(0, 0, 0, 0.3); */
		z-index: 99;
		max-height: 500rpx;
		display: flex;
		flex-direction: column;
		justify-content: flex-end;
		align-items: flex-start;
	}

	.notice {
		height: 50rpx;
		max-width: 500rpx;
		display: inline-block;
		border-radius: 10rpx;
		line-height: 50rpx;
		margin-bottom: 10rpx;
		/* background-color: #000000; */
	}

	.notice text {
		color: #fff;
		font-size: 24rpx;
		height: 50rpx;
		max-width: 500rpx;
		display: inline-block;
		background-color: #fc1c1c;
		border-radius: 10rpx;
		line-height: 50rpx;
		margin-bottom: 10rpx;
		padding: 0 15rpx;
		font-weight: 600;
	}

	.barrage {
		min-height: 100rpx;
		max-height: 500rpx;
	}

	.gonggao {
		background-color: rgba(0, 0, 0, 0.3);
		white-space: normal;
		padding: 12rpx 18rpx;
		border-radius: 10rpx;
		word-break: break-all;
		font-weight: 600;
	}

	.gonggao .title {
		display: inline-block;
		color: #fc1c1c;
		font-size: 26rpx;
		line-height: 36rpx;
	}

	.gonggao .ba_txt {
		color: #fff;
		font-size: 26rpx;
		line-height: 36rpx;
		word-break: break-all;
		font-family: PingFang SC;
		font-weight: 600;


	}

	.barrage_item {
		background-color: rgba(0, 0, 0, 0.3);
		max-width: 500rpx;
		border-radius: 23rpx;
		padding: 0rpx 15rpx 10rpx 15rpx;
		margin-top: 10rpx;
		white-space: normal;
		word-break: break-all;
		display: inline-block;
		font-weight: 600;
	}

	.barrage_item .name {
		font-size: 25rpx;
		line-height: 36rpx;
		margin-right: 15rpx;
	}

	.barrage_item .barrage_item_con {
		color: #fff;
		font-size: 25rpx;
	}

	/***** 弹幕 end *****/

	.canvas {
		background: transparent;
		width: 180rpx;
		height: 400rpx;
		position: absolute;
		right: -20rpx;
		bottom: 130rpx;
	}

	.live_footer .add_heart {
		transform: scale(1);
		-webkit-transform: scale(1);
		-webkit-transition: ease all;
		-moz-transition: ease all;
		transition: ease all;
		-webkit-transition-duration: 700ms;
		-moz-transition-duration: 700ms;
		transition-duration: 700ms;
	}

	/* 直播结束 start */
	.live_stop {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		top: 0;
		width: 750rpx;
		height: 100%;
		/* background: rgba(0, 0, 0, 0.5); */
		display: flex;
		flex-direction: row;
		align-items: flex-start;
		justify-content: center;
		z-index: 1;
	}

	.live_stop_bg {
		width: 750rpx;
		height: 100%;
		background-size: cover;
		background-repeat: no-repeat;
		background-position: center center;
		opacity: 0.1;
	}

	.live_stop_mask {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		top: 0;
		width: 750rpx;
		height: 100%;
		background: linear-gradient(0deg, #060606 0%, #4E4E4E 56.00000000000001%, #090909 99%);
	}

	.live_stop_container {
		position: absolute;
		top: 374rpx;
		left: 0;
		width: 750rpx;
		height: 100%;
		text-align: center;
	}

	.live_stop_title {
		color: #fffeee;
		font-size: 36rpx;
		font-weight: bold;
	}

	.live_stop_time {
		color: #ffffff;
		font-size: 24rpx;
		font-weight: 500;
		margin-top: 27rpx;
		opacity: 0.6;
	}

	.live_stop_recording {
		width: 600rpx;
		color: #ffffff;
		font-size: 24rpx;
		font-weight: 500;
		margin: 17rpx auto 0;
		word-break: break-all;
	}

	.live_stop_like_watch {
		color: #ffffff;
		font-size: 26rpx;
		font-weight: 500;
		margin-top: 17rpx;
	}

	.return_refresh {
		width: 160rpx;
		height: 50rpx;
		line-height: 50rpx;
		color: #ffffff;
		font-size: 28rpx;
		text-align: center;
		border: 1px solid #FFFFFF;
		border-radius: 25rpx;
		margin: 40rpx auto 0;
	}

	/* 直播结束 end */

	/* h5端保存海报 start */
	.share_h5_tip_text {
		position: relative;
		bottom: 10rpx;
	}

	.share_h5_close_img {
		margin-top: 30rpx;
	}

	.share_h5_img_bottom {
		height: 34rpx !important;
	}

	/* h5端保存海报 end */
</style>