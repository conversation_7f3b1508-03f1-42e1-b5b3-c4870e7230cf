<template>
	<view>
		<!-- 视频绑定的商品模块 start -->
		<view class="live_bind_goods_mask" v-if="showGoods">
			<view class="live_bind_goods">
				<view class="live_bind_goods_header">
					<text
						class="live_bind_goods_header_text">{{Ls('全部商品')}}({{goodsNum * 1 > 99 ? '99+' : goodsNum}})</text>
					<image class="live_bind_goods_header_image" :src="imgUrl+'svideo/close.png'"
						@tap="closeGoods"></image>
				</view>
				<scroll-view class="scroll_goods" scroll-y="true" @scrolltolower="getMoreGoods">
					<view class="live_user_tab_content">
						<!-- 商品列表item -->
						<block v-for="(items, indexs) in goodsList" :key="indexs">
							<view class="live_user_tab_content_item">
								<view class="live_user_tab_content_item_img" :data-gid="items.gid"
									@tap="goGoodsDetail(items)">
									<image class="live_user_tab_content_item_img_image"
										:src="items.goodsImage" mode="aspectFit"></image>
									<text class="live_user_tab_content_item_img_text">{{indexs+1}}</text>
								</view>
								<view class="live_user_tab_content_item_right">
									<view class="live_user_tab_content_item_right_top" :data-gid="items.gid"
										@tap.stop="goGoodsDetail(items)">
										<text
											class="live_user_tab_content_item_right_top_name">{{items.goodsName}}</text>
										<text
											class="live_user_tab_content_item_right_top_jingle">{{items.goodsBrief}}</text>
									</view>
									<view class="live_user_tab_content_item_right_bottom">
										<view class="live_user_tab_content_item_right_bottom_price">
											<text
												class="live_user_tab_content_item_right_bottom_price_num">{{Ls('¥')}}{{items.goodsPrice}}</text>
										</view>
										<view class="live_user_tab_content_item_right_bottom_click_num">
											<image
												class="live_user_tab_content_item_right_bottom_click_num_add_cart"
												:src="imgUrl+'svideo/add_cart1.png'" mode="aspectFit"
												@tap="addCart(items)"></image>
											<block>
												<image
													class="live_user_tab_content_item_right_bottom_click_num_image"
													:src="imgUrl+'live/eye.png'" mode="aspectFit"></image>
												<text
													class="live_user_tab_content_item_right_bottom_click_num_text">{{items.clickNum}}</text>
											</block>
										</view>
									</view>
								</view>
							</view>
							<view v-if="goodsList.length-indexs > 1" class="line_marginl_20"></view>
						</block>
						<!-- 数据加载完毕 -->
						<view class="no-has-more" v-if="!hasmore&&goodsList.length>0"
							style="background-color: transparent">
							<text class="no-has-more_text">{{Ls('数据加载完毕~')}}</text>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
		<!-- 视频绑定的商品模块 end -->
	</view>
</template>

<script>
	import {getCurLanguage} from '@/utils/base.js'
	import Config from '@/utils/config.js'
	import {addCartCommon,checkPageHasMore} from '@/utils/live.js'
	import request from "@/utils/request";
	import {mapState} from 'vuex'
	export default {
		data(){
			return{
				imgUrl: Config.imgUrl,
				Ls:getCurLanguage,
				showGoods:false,
				goodsList:[],
				goodsNum:0,
				hasMore:true,
				current:1
			}
		},
		
		
		computed:{
			...mapState(['hasLogin'])
		},
		
		methods:{
			
			injectGoods(info){
				this.live_id = info.live_id
				this.goodsNum = info.goodsNum
				this.live_ban = info.live_ban
				this.live_ban_info = info.live_ban_info
				this.getLiveGoods();
				this.showGoods = true
			},
			
			//绑定的商品分页事件
			getMoreGoods() {
				if (this.hasmore) {
					this.getLiveGoods();
				}
			},
			
			//关闭商品弹层
			closeGoods() {
				this.goodsList = []
				this.showGoods = false;
			},
			
			//跳转商品详情页
			goGoodsDetail(good) {
				let page = getCurrentPages();
				let len = page.length;
				let url = '/standard/product/detail?productId=' + good.productId + '&goodsId=' + good.goodsId
				if (len > 4) {
					uni.redirectTo({url})
				} else {
					uni.navigateTo({url})
				}
			},
			
			//加入购物车事件
			addCart(good) {
				if (this.live_ban) {
					uni.showToast({
						title: this.live_ban_info.msg,
						icon: 'none'
					})
					return;
				}
				let that = this
				addCartCommon(good,this)
			},
			
			
			//获取直播商品
			getLiveGoods() {
				this.loading = true;
				let param = {}
				param.data = {}
				param.data.liveId = this.live_id
				param.data.current = this.current
				param.data.pageSize = 5
				param.url = 'v3/video/front/video/live/goodsList'
				param.method = 'GET'
				request(param).then(res => {
					if (res.state == 200) {
						let list = res.data.list;
						if (this.current == 1) {
							this.goodsList = list;
						} else {
							this.goodsList = this.goodsList.concat(list);
						}
						if (checkPageHasMore(res.data.pagination)) {
							this.current++;
						} else {
							this.hasmore = false;
						}
					}
					this.loading = false;
				})
			},
		}
	}
</script>

<style>
	
	.no-has-more {
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		height: 80rpx;
		text-align: center;
		line-height: 80rpx;
		color: #949494;
		font-size: 22rpx;
	}
	
	.no-has-more_text {
		line-height: 80rpx;
		color: #949494;
		font-size: 22rpx;
	}
	
	/* 直播间商品样式-start */
	/* 视频绑定的商品模块 start */
	.live_bind_goods_mask {
		width: 750rpx;
		position: fixed;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		z-index: 100;
		background: rgba(0, 0, 0, 0.45);
	}
	
	.live_bind_goods {
		width: 750rpx;
		height: 850rpx;
		position: fixed;
		left: 0;
		bottom: 0;
		z-index: 4;
		border-radius: 15rpx 15rpx 0 0;
		background-color: #f8f8f8;
		right: 0;
		margin: 0 auto;
	}
	
	.live_bind_goods_header {
		width: 750rpx;
		height: 100rpx;
		padding: 0 20rpx;
		background-color: #fff;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		border-top-left-radius: 15rpx;
		border-top-right-radius: 15rpx;
		border-bottom-right-radius: 0;
		border-bottom-left-radius: 0;
	}
	
	.live_bind_goods_header_text {
		color: #2d2d2d;
		font-size: 32rpx;
	}
	
	.live_bind_goods_header_image {
		width: 47rpx;
		height: 47rpx;
	}
	
	.scroll_goods {
		height: 750rpx;
	}
	
	/* 视频绑定的商品模块 end */
	.live_user_tab_content_item {
		width: 750rpx;
		padding: 20rpx;
		border-top-left-radius: 14rpx;
		border-top-right-radius: 14rpx;
		border-bottom-right-radius: 14rpx;
		border-bottom-left-radius: 14rpx;
		height: 286rpx;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
	}
	
	.live_user_tab_content_item_img {
		width: 246rpx;
		height: 246rpx;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		position: relative;
	}
	
	.live_user_tab_content_item_img_image {
		height: 246rpx;
		width: 246rpx;
		border-top-left-radius: 15rpx;
		border-top-right-radius: 0;
		border-bottom-right-radius: 0;
		border-bottom-left-radius: 15rpx;
	}
	
	.live_user_tab_content_item_img_text {
		position: absolute;
		top: 0;
		left: 0;
		color: #fff;
		font-size: 20rpx;
		height: 40rpx;
		line-height: 40rpx;
		padding: 0 15rpx;
		border-top-left-radius: 15rpx;
		border-top-right-radius: 0;
		border-bottom-right-radius: 15rpx;
		border-bottom-left-radius: 0;
		background: linear-gradient(45deg, rgba(255, 0, 0, 1) 0%, rgba(255, 122, 24, 1) 100%);
	}
	
	.live_user_tab_content_item_right {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: flex-start;
		height: 226rpx;
		padding: 10rpx 0 10rpx 20rpx;
	}
	
	.live_user_tab_content_item_right_top {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
	}
	
	.live_user_tab_content_item_right_top_name {
		color: #2d2d2d;
		font-size: 28rpx;
		line-height: 42rpx;
		height: 84rpx;
		width: 444rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		word-break: break-word;
	}
	
	.live_user_tab_content_item_right_top_jingle {
		color: #949494;
		font-size: 26rpx;
		line-height: 36rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		margin-top: 10rpx;
		width: 444rpx;
	}
	
	.live_user_tab_content_item_right_bottom {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: flex-end;
		width: 444rpx;
	}
	
	.live_user_tab_content_item_right_bottom_price_unit {
		color: #fc1c1c;
		font-size: 24rpx;
	}
	
	.live_user_tab_content_item_right_bottom_price_num {
		font-size: 36rpx;
		color: #fc1c1c;
		margin-left: 3rpx;
	}
	
	.live_user_tab_content_item_right_bottom_click_num {
		display: flex;
		flex-direction: row;
		align-items: center;
	}
	
	.live_user_tab_content_item_right_bottom_click_num_add_cart {
		width: 42rpx;
		height: 42rpx;
	}
	
	.live_user_tab_content_item_right_bottom_click_num_image {
		width: 42rpx;
		height: 42rpx;
	}
	
	.live_user_tab_content_item_right_bottom_click_num_text {
		color: #949494;
		font-size: 22rpx;
	}
	
	.line_marginl_20 {
		border-bottom-width: 1px;
		border-bottom-color: #eee;
		border-bottom-style: solid;
		width: 730rpx;
		margin-left: 20rpx;
	}
	
	/* 直播间商品样式-end */
</style>