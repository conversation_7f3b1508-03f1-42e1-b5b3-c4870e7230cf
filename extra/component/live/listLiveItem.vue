<!-- 直播列表item -->
<template name="listLiveItem">
	<view>
		<view v-for="(item, index) in liveList" :key="index"
			:class="'live_list_item ' + (index == 0 ? 'live_list_item_first' : '')" :data-liveId="item.liveId"
			@tap="goLivePlay(item.liveId, index)">
			<view class="left" :style="'background:url(' + item.liveCover + ');' + bgStyle">
				<view class="left_top">
					<view class="left_top_icon">
						<block v-if="item.liveState == 4">
							<image class="back_img" :src="listPlayBackIcon" mode="aspectFit"></image>
						</block>
						<block v-else>
							<image class="back_img_living" :src="listLivingIcon" mode="aspectFit"></image>
						</block>
					</view>
					<text class="watch_num_text left_top_num">{{ item.viewingNum }}{{ $L('观看') }}</text>
				</view>
				<view class="right_bottom">
					<image class="heart_icon" :src="heartIcon" mode="aspectFit"></image>
					<text class="watch_num_text bottom_right">{{item.popularityNum}}</text>
				</view>
			</view>

			<view class="right">
				<text class="live_name">{{ item.liveName }}</text>
				<view class="member_info">
					<!-- wx-1-start -->
					<!-- #ifdef MP-WEIXIN -->
					<image class="avator" :src="item.memberAvatar" mode="aspectFill"></image>
					<!-- #endif -->
					<!-- wx-1-end -->
					<!-- #ifdef H5 -->
					<view class="avator" :style="'background-image:url(' + item.memberAvatar + ');' + bgStyle"></view>
					<!-- #endif -->
					<text class="nickname">{{item.memberNickname ? item.memberNickname : item.memberName}}</text>
				</view>
				<block v-if="item.goodsList && item.goodsList.length">
					<view class="goods_info">
						<view class="first_goods" :style="'background:url(' +item.goodsList[0].goodsImage +');' +bgStyle">
							<text class="goods_price">{{ $L('¥') }}{{ item.goodsList[0].goodsPrice }}</text>
						</view>
						<block v-if="item.goodsList && item.goodsList.length > 1">
							<view class="second_goods" :style="'background:url(' +item.goodsList[1].goodsImage +');' +bgStyle">
								<view class="second_goods_wrap">
									<text class="total">{{item.goodsNum > 99 ? '99+' : item.goodsNum}}</text>
									<text class="total">{{ $L('宝贝') }}</text>
								</view>
							</view>
						</block>
					</view>
				</block>
				<block v-if="!(item.goodsList && item.goodsList.length)">
					<view class="no_recom_goods">{{ $L('[无推荐商品]') }}</view>
				</block>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'listLiveItem',
		data() {
			return {}
		},
		props: {
			liveList: {
				type: Array,
				value: []
			},
			bgStyle: {
				type: String,
				value: ''
			},
			listPlayBackIcon: {
				type: String,
				value: ''
			},
			listLivingIcon: {
				type: String,
				value: ''
			},
			heartIcon: {
				type: String,
				value: ''
			},
			labelId: {
				type: Number,
				value: null
			}
		},
		mounted() {
			uni.$on('updateListWatchNum', (res) => {
				this.liveList[res.index].viewingNum =Number(this.liveList[res.index].viewingNum) + 1
			})
		},
		destroyed() {
			uni.$off('updateListWatchNum')
		},
		methods: {
			//进入播放页面
			goLivePlay(liveId, index) {
				this.$livePlayNav({
					live_id: liveId,
					index,
					label_id: this.labelId || 0
				})
			},
			
			updateView(updateData){
				this.liveList[updateData.index].viewingNum = updateData.live_click_num
			}
		}
	}
</script>

<style>
	view {
		box-sizing: content-box;
	}

	.live_list_item {
		width: 670rpx;
		height: 360rpx;
		background-color: #fff;
		margin-top: 20rpx;
		border-radius: 15rpx;
		overflow: hidden;
		margin-left: 20rpx;
		padding: 20rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		position: relative;
		left: 50%;
		transform: translateX(-375rpx);
	}

	.live_list_item_first {
		margin-top: 0 !important;
	}

	.live_list_item .left {
		width: 360rpx;
		height: 360rpx;
		border-radius: 15rpx;
		background-repeat: no-repeat;
		/* background-size:100%;  */
		position: relative;
	}

	.live_list_item .left .left_top {
		position: absolute;
		top: 0;
		left: 0;
		padding-right: 10rpx;
		height: 36rpx;
		background-color: rgba(0, 0, 0, 0.3);
		display: flex;
		flex-direction: row;
		align-items: center;
		border-bottom-right-radius: 15rpx;
		border-top-left-radius: 15rpx;
	}

	.live_list_item .left .left_top .left_top_icon {
		width: 46rpx;
		height: 36rpx;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		border-radius: 15rpx 0;
		background: var(--color_video_main_bg);
	}

	.left .left_top .back_img {
		width: 40rpx;
		height: 40rpx;
	}

	.left .left_top .back_img_living {
		width: 32rpx;
		height: 32rpx;
		margin-left: 5rpx;
		margin-right: 5rpx;
		margin-bottom: 3rpx;
	}

	.left .watch_num_text {
		color: #fff;
		font-size: 20rpx;
		line-height: 28rpx;
		display: inline-block;
		font-weight: 600;
	}

	.live_list_item .left .right_bottom {
		position: absolute;
		bottom: 10rpx;
		right: 5rpx;
		display: flex;
		flex-direction: column;
		align-items: flex-end;
	}

	.live_list_item .left .right_bottom image {
		height: 330rpx;
	}

	.left .watch_num_text.left_top_num {
		margin-left: 10rpx;
	}

	.left .watch_num_text.bottom_right {
		margin-right: 14rpx;
		font-weight: 600;
	}

	.left .right_bottom .heart_icon {
		width: 100rpx;
		height: 300rpx;
	}

	.left .right_bottom text {
		margin-right: 10rpx;
	}

	.live_list_item .right {
		display: flex;
		flex: 1;
		flex-direction: column;
		align-items: flex-start;
		justify-content: flex-start;
		padding: 30rpx 0rpx 30rpx 15rpx;
		height: 300rpx;
	}

	.right .live_name {
		height: 72rpx;
		color: '#2D2D2D';
		font-size: 26rpx;
		line-height: 36rpx;
		font-weight: 600;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		word-break: break-word;
	}

	.right .member_info,
	.right .goods_info {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: flex-start;
		margin-top: 20rpx;
	}

	.right .member_info .avator {
		width: 46rpx;
		height: 46rpx;
		border-radius: 50%;
	}

	.right .member_info .nickname {
		color: '#949494';
		font-size: 23rpx;
		margin-left: 10rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.right .goods_info .first_goods,
	.right .goods_info .second_goods {
		width: 140rpx;
		height: 140rpx;
		border-radius: 15rpx;
		overflow: hidden;
		margin-right: 10rpx;
		background-repeat: no-repeat;
		background-size: contain;
	}

	.right .goods_info .second_goods {
		margin-right: 0;
	}

	.right .goods_info .first_goods .goods_price {
		display: flex;
		width: 140rpx;
		height: 120rpx;
		flex-direction: row;
		justify-content: center;
		align-items: flex-end;
		color: #fff;
		margin-top: 20rpx;
		background: -webkit-linear-gradient(to bottom,
				rgba(221, 221, 221, 0),
				rgba(221, 221, 221, 0),
				#000);
		font-size: 22rpx;
		line-height: 44rpx;
		border-radius: 0 0 15rpx 15rpx;
		font-weight: 600;
	}

	.right .goods_info .second_goods .second_goods_wrap {
		display: flex;
		width: 140rpx;
		height: 140rpx;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		background-color: rgba(0, 0, 0, 0.3);
		font-size: 24rpx;
		color: #fff;
		border-radius: 15rpx;
		line-height: 32rpx;
		font-weight: 600;
	}

	.no_recom_goods {
		width: 300rpx;
		height: 200rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 24rpx;
		color: #949494;
	}
</style>