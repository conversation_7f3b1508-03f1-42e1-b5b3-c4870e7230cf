<template name="livePlayGoods">
	<view class="live_user_tab_content">
		<block v-for="(item, index) in goodsData" :key="index">
			<view class="goods_item">
				<view class="goods_img" @tap="goGoodsDetail(item)">
					<!-- <image :src="item.goodsImage" mode="aspectFit"></image> -->
					<!-- <coverImage :src="item.goodsImage" height="246" width="246" class="image"></coverImage> -->
					<view class="image" :style="'background-image:url('+item.goodsImage+')'"></view>
					<text>{{index+1}}</text>
				</view>
				<view class="right">
					<view class="top" @tap="goGoodsDetail(item)">
						<text class="name">{{item.goodsName}}</text>
						<text class="jingle">{{item.goodsBrief}}</text>
					</view>
					<view class="bottom">
						<view class="price">
							<text class="unit">{{$L('¥')}}</text>
							<text class="num">{{item.goodsPrice}}</text>
						</view>
						<view class="click_num" @click="addCart(item)">
						  <svgGroup :type="addCartIcon" :color="diyStyle_var['--color_video_main']" width="40" height="40" px="rpx">
						  </svgGroup>
						</view>
					</view>
				</view>
			</view>
			<view v-if="goodsData.length-index > 1" class="line_marginl_20"></view>
		</block>
		</block>
	</view>
</template>

<script>
	export default {
		name: "livePlayGoods",
		data() {
			return {}
		},
		props: {
			goodsData: {
				type: Array,
				value: []
			},
			addCartIcon: {
				type: String,
				value: ''
			},
			eyeIcon: {
				type: String,
				value: ''
			},
			live_id: {
				type: Number,
				value: 0
			},
			memberInfo: {
				type: Object,
				value: {}
			}
		},
		methods: {
			//跳转商品详情页
			goGoodsDetail(good) {
				let page = getCurrentPages();
				let len = page.length;

				if (len > 4) {
					this.$Router.replace({path:'/standard/product/detail',query:{productId:good.productId}})
				} else {
					this.$Router.push({path:'/standard/product/detail',query:{productId:good.productId}})
				}
			},

			//加入购物车
			addCart(goodinfo) {
				this.$emit('click', goodinfo);
			},
		}
	}
</script>


<style>
	.live_user_tab_content .goods_item {
		width: 710rpx;
		padding: 20rpx;
		border-radius: 14rpx;
		height: 246rpx;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
		margin: 5rpx 0;
	}

	.live_user_tab_content .goods_item .goods_img {
		width: 246rpx;
		height: 246rpx;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		position: relative;
	}

	.live_user_tab_content .goods_item .goods_img .image {
		background-position: center center;
		background-repeat: no-repeat;
		background-size: cover;
		width: 246rpx;
		height: 246rpx;
		border-radius: 15rpx;
	}

	.live_user_tab_content .goods_item .goods_img text {
		position: absolute;
		top: 0;
		left: 0;
		color: #fff;
		font-size: 20rpx;
		height: 40rpx;
		line-height: 40rpx;
		padding: 0 15rpx;
		border-radius: 15rpx 0 15rpx 0;
		background:var(--color_video_main_bg);
	}

	.live_user_tab_content .goods_item .right {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: flex-start;
		height: 226rpx;
		padding: 10rpx 0 10rpx 20rpx;
	}

	.live_user_tab_content .goods_item .right .top {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
	}

	.live_user_tab_content .goods_item .right .top .name {
		color: #2d2d2d;
		font-size: 28rpx;
		line-height: 42rpx;
		height: 84rpx;
		width: 444rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		word-break: break-word;
	}

	.live_user_tab_content .goods_item .right .top .jingle {
		color: #949494;
		font-size: 26rpx;
		line-height: 36rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		margin-top: 10rpx;
		width: 444rpx;
	}

	.live_user_tab_content .goods_item .right .bottom {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: flex-end;
		width: 444rpx;
	}

	.live_user_tab_content .goods_item .right .bottom .price .unit {
		color:var(--color_video_main);
		font-size: 24rpx;
	}

	.live_user_tab_content .goods_item .right .bottom .price .num {
		font-size: 36rpx;
		color:var(--color_video_main);
		margin-left: 3rpx;
	}

	.live_user_tab_content .goods_item .right .bottom .click_num {
		display: flex;
		flex-direction: row;
		align-items: center;
	}

	.live_user_tab_content .goods_item .right .bottom .click_num .add_cart {
		width: 42rpx;
		height: 42rpx;
	}

	.live_user_tab_content .goods_item .right .bottom .click_num image {
		width: 42rpx;
		height: 42rpx;
	}

	.live_user_tab_content .goods_item .right .bottom .click_num text {
		color: #949494;
		font-size: 22rpx;
	}

	.line_marginl_20 {
		border-bottom: 1px solid #eee;
		width: 730rpx;
		margin-left: 20rpx;
	}
</style>
