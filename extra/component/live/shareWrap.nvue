<template>
	<view class="">
		<!-- 分享 -->
		<view class="select-wrap" catchtouchmove="touchmoveshare" v-if="shareWrap">
			<view class="select-wrap_share-mode">
				<view class="empty_158"></view>
				<view class="share-mode_share-img" @click="prevImg">
					<image class="share-mode_share-img_poster" :src="shareImg" mode="widthFix"></image>
				</view>
				<view class="select-wrap_share-mode_ul">
					<view class="share-mode_item" @click="downloadImg">
						<image class="share-mode_image" :src="imgUrl + 'svideo/hb_share.png'"
							mode="widthFix"></image>
						<text class="share-mode_text">{{Ls('保存海报')}}</text>
					</view>
					<view class="share-mode_item" @click="sldShare(2,'WXSceneSession')">
						<image class="share-mode_image" :src="imgUrl + 'goods_detail/wx_share.png'"
							mode="widthFix"></image>
						<text class="share-mode_text">{{Ls('微信好友')}}</text>
					</view>
					<view class="share-mode_item" @click="sldShare(2,'WXSenceTimeline')">
						<image class="share-mode_image" :src="imgUrl + 'svideo/pyq_share.png'"
							mode="widthFix"></image>
						<text class="share-mode_text">{{Ls('微信朋友圈')}}</text>
					</view>
				</view>
				<view class="select-wrap_close" @click="closeShare">
					<image class="select-wrap_close_image" mode="scaleToFill"
						:src="imgUrl+'store/share_close2.png'"></image>
				</view>
			</view>
		</view>
		
		<view class="select-wrap" catchtouchmove="touchmoveshare" v-if="shareWrap2">
			<view class="select-wrap_share-mode">
				<view class="share-mode_share-img"></view>
				<view class="select-wrap_share-mode_ul">
					<button @click.stop="sldShare(0,'WXSceneSession')" class="share-mode_item">
						<image class="share-mode_image" :src="imgUrl+'goods_detail/wx_share.png'"
							mode="widthFix"></image>
						<text class="share-mode_text">{{Ls('微信好友')}}</text>
					</button>
					<button @click.stop="sldShare(0,'WXSenceTimeline')" class="share-mode_item">
						<image class="share-mode_image" :src="imgUrl+'svideo/pyq_share.png'"
							mode="widthFix"></image>
						<text class="share-mode_text">{{Ls('微信朋友圈')}}</text>
					</button>
					<!-- <view class="share-mode_item" @click="createhb">
						<image class="share-mode_image" :src="imgUrl+'svideo/createhb.png'" mode="widthFix">
						</image>
						<text class="share-mode_text">{{Ls('生成海报')}}</text>
					</view> -->
				</view>
				<view class="select-wrap_close" @click="closeShare">
					<image class="select-wrap_close_image" mode="aspectFit"
						:src="imgUrl+'store/share_close2.png'"></image>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {getCurLanguage} from '@/utils/base.js'
	import Config from '@/utils/config.js'
	import {checkPageHasMore} from "@/utils/live";
	export default {
		data(){
			return{
				Ls:getCurLanguage,
				shareWrap2:false,
				shareWrap:false,
				imgUrl:Config.imgUrl
			}
		},
		
		
		methods:{
			//注入信息并打开
			injectInfo(info){
				this.live_id = info.live_id
				this.liveName = info.liveName
				this.authorName = info.authorName
				this.liveCover = info.liveCover
				this.shareImg = info.shareImg
				this.shareWrap2 = true
			},
			
		
			//关闭分享
			closeShare() {
				this.shareWrap = false;
				this.shareWrap2 = false;
			},
			//分享 type：分享类型 0 图文 2图片，scene 场景 WXSceneSession：分享朋友  WXSenceTimeline：分享朋友圈
			sldShare(type, scene) {
				let shareData = {};
				if (type == 0) {
					shareData.href = Config.apiUrl + 'extra/live/livePlay?live_id=' + this.live_id;
					shareData.title = this.Ls('我正在看') + this.authorName + this.Ls('的精彩内容，快来围观~');
					shareData.summary = this.liveName;
					shareData.imageUrl = this.liveCover;
				} else if (type == 2) {
					shareData.imageUrl = this.shareImg;
				}
				this.weiXinAppShare(type, scene, shareData);
				this.closeShare(); //关闭分享
			},
			//微信分享
			weiXinAppShare(type, scene, shareData) {
				if (type == 0) {
					//分享图文
					uni.share({
						provider: "weixin",
						scene: scene,
						type: type, //0为图文
						href: shareData.href,
						title: shareData.title,
						summary: shareData.summary,
						imageUrl: shareData.imageUrl, //图片,图片过大的话不展示，建议小于20kb
						success: function(res) {},
						fail: function(err) {
							console.log(err)
						}
					});
				} else if (type == 2) {
					//分享图片
					uni.share({
						provider: "weixin",
						scene: scene,
						type: type, //2为图片
						imageUrl: shareData.imageUrl, //图片,图片过大的话不展示，建议小于20kb
						success: function(res) {},
						fail: function(err) {
							console.log(err)
						}
					});
				}
			},
			
			prevImg() {
				uni.previewImage({
					urls: [this.shareImg],
				})
			},
			
			
			//生成海报事件
			createhb() {
				this.shareWrap = true;
				this.shareWrap2 = false;
			},
			//下载海报
			downloadImg() {
				let _this = this;
				uni.downloadFile({
					url: _this.shareImg,
					success: res_info => {
						if (res_info.statusCode == 200) {
							_this.saveHb(res_info.tempFilePath);
						} else {
							uni.showToast({
								title: _this.Ls('下载失败'),
								icon: 'none'
							});
						}
					}
				});
			},
			//保存图片
			saveHb: function(img) {
				let _this = this;
				uni.saveImageToPhotosAlbum({
					filePath: img,
					success: function(data) {
						_this.shareWrap = false;
						_this.shareWrap2 = false;
						uni.showToast({
							title: _this.Ls('已保存到本地'),
							icon: 'success',
							duration: 2000
						});
					},
					complete: function(res) {}
				});
			},
		}
	}
</script>

<style>
	/* 分享start */
	.select-wrap {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		margin: 0 auto;
		width: 750rpx;
		background-color: rgba(0, 0, 0, 0.45);
		z-index: 9999;
	}
	
	.select-wrap_share-mode {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		width: 750rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.share-mode_share-img {
		width: 540rpx;
		border-radius: 20rpx;
		overflow: hidden;
	}
	
	.share-mode_share-img_poster {
		width: 540rpx;
	}
	
	.select-wrap_share-mode_ul {
		display: flex;
		flex-direction: row;
		align-items: center;
		width: 750rpx;
		padding-left: 30px;
		padding-right: 30px;
		justify-content: space-around;
	}
	
	.share-mode {
		display: flex;
		flex-direction: column;
		align-items: center;
		text-align: center;
		border-color: none;
		margin: 0;
		padding: 0;
		line-height: 1;
		background-color: transparent;
	}
	
	.share-mode_item {
		display: flex;
		flex-direction: column;
		align-items: center;
		text-align: center;
		cursor: pointer;
		border-color: none;
		margin: 0;
		padding: 0;
		line-height: 1;
		background-color: transparent;
		border-width: 0;
	}
	
	.share-mode_item::after {
		border: none;
	}
	
	.share-mode_image {
		border-radius: 20rpx;
		width: 106rpx;
		height: 0;
	}
	
	.share-mode_text {
		color: #fff;
		font-size: 24rpx;
		margin-top: 30rpx;
	}
	
	.select-wrap_close {
		width: 750rpx;
		height: 120rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		margin-bottom: 80rpx;
	}
	
	.select-wrap_close_image {
		width: 32rpx;
		height: 32rpx;
	}
	
	.share-mode_share-img {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	/* 分享end */
</style>