<template>
	<view class="live_user_tab_content" @click="toDetail">
		<!-- :style="goodsData.length-index==1?'border-radius:0 0 14rpx 14rpx':'border-radius:0'" -->
		<view class="goods_item">
			<view class="goods_img">
				<image :src="item.goodsImage" mode="aspectFit"></image>
			</view>
			<view class="right">
				<view class="top">
					<text class="name">{{ item.goodsName }}</text>
					<text class="jingle">{{ item.goodsBrief }}</text>
				</view>
				<view class="bottom">
					<view class="price">
						<text class="unit">¥</text>
						<text class="num">{{item.productPrice ? item.productPrice : item.goodsPrice}}</text>
					</view>
					<view @click.stop="addCart(item)">
						<svgGroup type="addCart1" :color="diyStyle_var['--color_video_main']" width="48" height="48"
							px="rpx">
						</svgGroup>
					</view>
					
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapMutations
	} from 'vuex'
	export default {
		name: 'videoReleaseGoods',
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl
			}
		},
		props: {
			item: {
				type: Object,
				value: {}
			},
			addCartIcon: {
				type: String,
				value: ''
			},
			eyeIcon: {
				type: String,
				value: ''
			},
			canAdd: {
				type: Boolean,
				value: true
			}
		},
		computed: {
			...mapState(['hasLogin', 'cartData', 'userInfo'])
		},
		methods: {
			//删除商品
			delGoods(productId) {
				this.$emit('delGoods', {
					productId: productId
				})
			},
			//加入购物车事件
			addCart(good) {
				if (!this.canAdd) {
					this.$api.msg(this.$L('该视频未审核通过,不能加购哦～'))
					return
				}
				const that = this
				if (!this.hasLogin) {
					let cart_list = {
						storeCartGroupList: [{
							promotionCartGroupList: [{
								cartList: [{
									buyNum: 1,
									goodsId: good.goodsId - 0,
									productId: good.defaultProductId,
									productImage: good.goodsImage,
									goodsName: good.goodsName,
									isChecked: 1,
									productPrice: good.goodsPrice,
									productStock: good.goodsStock
								}]
							}],
							storeId: good.storeId,
							storeName: good.storeName,
							checkedAll: true
						}],
						checkedAll: true,
						invalidList: []
					}
					//未登录加入本地缓存
					let local_cart_list = uni.getStorageSync('cart_list') //购物车列表本地缓存
					if (local_cart_list) {
						//如果不是第一次存储
						let tmp_list1 = []
						let tmp_list2 = []
						cart_list.storeCartGroupList.forEach((item) => {
							item.promotionCartGroupList.forEach((item1) => {
								item1.cartList.forEach((item2) => {
									local_cart_list.storeCartGroupList.forEach((v) => {
										v.promotionCartGroupList.forEach((v1) => {
											v1.cartList.forEach((v2) => {
												if (
													v2.productId == item2
													.productId &&
													v.storeId == item
													.storeId
												) {
													tmp_list1.push(v)
												}
											})
										})
									})
									tmp_list2 = local_cart_list.storeCartGroupList.filter((v) => {
										return v.storeId == item.storeId
									})
								})
							})
						})
						if (tmp_list1.length > 0 && tmp_list2.length > 0) {
							//同一店铺同一商品
							local_cart_list.storeCartGroupList.map((item) => {
								item.promotionCartGroupList.map((item1) => {
									item1.cartList.map((item2) => {
										if (
											item2.productId == this.productId &&
											item.storeId == this.storeInf &&
											this.storeInf.storeId
										) {
											item2.buyNum += this.currentSpecNum
										}
									})
								})
							})
						} else if (tmp_list1.length == 0 && tmp_list2.length > 0) {
							//同一店铺不同商品
							local_cart_list.storeCartGroupList.map((item) => {
								if (item.storeId == this.storeInf && this.storeInf.storeId) {
									item.promotionCartGroupList.map((item2) => {
										item2.cartList.push(
											cart_list.storeCartGroupList[0].promotionCartGroupList[0]
											.cartList[0]
										)
									})
								}
							})
						} else {
							//不同店铺不同商品
							local_cart_list.storeCartGroupList.push(
								cart_list.storeCartGroupList[0]
							)
						}
						// 未登录购物车展示数据
						uni.setStorage({
							key: 'cart_list',
							data: local_cart_list,
							success: function() {
								//更新购物车数量和购物车数据
							}
						})
					} else {
						uni.setStorage({
							key: 'cart_list',
							data: cart_list,
							success: function() {
								//更新购物车数量和购物车数据
							}
						})
					}
					uni.showToast({
						title: this.$L('加入购物车成功！'),
						icon: 'none',
						duration: 700
					})
				} else {
					//已登录
					this.$request({
							url: 'v3/business/front/cart/add',
							data: {
								productId: good.defaultProductId,
								number: 1
							},
							method: 'POST'
						})
						.then((res) => {
							if (res.state == 200) {
								//更新购物车数量
								this.$api.msg(res.msg)
							} else {
								this.$api.msg(res.msg)
							}
						})
						.catch((e) => {})
				}
			},
			toDetail() {
				this.$Router.push({
					path: '/standard/product/detail',
					query: {
						productId: this.item.defaultProductId,
						goodsId: this.item.goodsId
					}
				})
			}
		}
	}
</script>

<style>
	.live_user_tab_content .goods_item {
		padding: 20rpx;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
		border-bottom: 1rpx solid #f7f7f7;
		background: #fff;
	}

	.live_user_tab_content .goods_item .goods_img {
		width: 246rpx;
		height: 246rpx;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		position: relative;
	}

	.live_user_tab_content .goods_item .goods_img image {
		max-height: 100%;
		max-width: 100%;
		border-radius: 15rpx;
	}

	.live_user_tab_content .goods_item .goods_img text {
		position: absolute;
		top: 0;
		left: 0;
		color: #fff;
		font-size: 20rpx;
		height: 40rpx;
		line-height: 40rpx;
		padding: 0 15rpx;
		border-radius: 15rpx 0 15rpx 0;
		background: linear-gradient(45deg,
				rgba(255, 0, 0, 1) 0%,
				rgba(255, 122, 24, 1) 100%);
	}

	.live_user_tab_content .goods_item .right {
		width: 100%;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: flex-start;
		height: 226rpx;
		padding: 10rpx 0 10rpx 20rpx;
	}

	.live_user_tab_content .goods_item .right .top {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
	}

	.live_user_tab_content .goods_item .right .top .name {
		color: #2d2d2d;
		font-size: 28rpx;
		line-height: 42rpx;
		height: 84rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		word-break: break-word;
	}

	.live_user_tab_content .goods_item .right .top .jingle {
		color: #949494;
		font-size: 26rpx;
		line-height: 36rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		margin-top: 10rpx;
		width: 404rpx;
	}

	.live_user_tab_content .goods_item .right .bottom {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: flex-end;
		width: 100%;
	}

	.live_user_tab_content .goods_item .right .bottom .price .unit {
		color: var(--color_video_main);
		font-size: 24rpx;
	}

	.live_user_tab_content .goods_item .right .bottom .price .num {
		font-size: 36rpx;
		color: var(--color_video_main);
		margin-left: 3rpx;
	}

	.live_user_tab_content .goods_item .right .bottom .click_num {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
	}

	.live_user_tab_content .goods_item .right .bottom .click_num .add_cart {
		width: 42rpx;
		height: 42rpx;
	}

	.live_user_tab_content .goods_item .right .bottom .click_num image {
		width: 42rpx;
		height: 42rpx;
	}

	.live_user_tab_content .goods_item .right .bottom .click_num text {
		color: #949494;
		font-size: 22rpx;
	}

	.line_marginl_20 {
		border-bottom: 1px solid #eee;
		width: 690rpx;
		margin-left: 40rpx;
	}

	.live_user_tab_content .goods_item .right .bottom .del_goods {
		width: 90rpx;
		height: 40rpx;
		border: 1px solid rgba(0, 0, 0, 0.7);
		border-radius: 20rpx;
		color: #2d2d2d;
		font-size: 24rpx;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
	}
</style>