<template>
    <view>
        <!-- 直播/短视频 个人中心 商品模块 -->
        <scroll-view class="live_user_tab_content_scroll" :scroll-y="scrollEnable" @scrolltolower="getMore" v-if="videoList.length > 0" :style="{ height: scrollHeight }">
            <view class="video_list">
                <view v-for="(item, index) in videoList" :key="index" class="item">
                    <videoItem :detail="item" page="my" @delete="handleDelete" />
                </view>
            </view>
            <!-- 数据加载完毕 -->
            <dataLoaded :showFlag="!hasmore && videoList.length > 0" />
            <!-- 数据加载中 -->
            <dataLoading :showFlag="hasmore && loading && !firstLoading" />
            <!-- 页面loading -->
            <pageLoading :firstLoading="firstLoading" :loadingIcon="imgUrl + 'svideo/page_loading_icon.gif'" :topVal="467" />
            <block v-if="authorInfo.isSelf">
                <!-- wx-1-start -->
                <!-- #ifdef MP-WEIXIN -->
                <view style="height: 90rpx; width: 750rpx"></view>
                <!-- #endif -->
                <!-- wx-1-end -->
                <!-- #ifdef H5 -->
                <view style="height: 130rpx; width: 750rpx"></view>
                <!-- #endif -->
                <!-- app-1-start -->
                <!-- #ifdef APP-PLUS -->
                <view style="height: 60rpx; width: 750rpx"></view>
                <!-- #endif -->
                <!-- app-1-end -->
            </block>
        </scroll-view>
        <!-- 页面空数据 -->
        <emptyData :showFlag="!firstLoading && videoList.length == 0" :emptyIcon="imgUrl + 'empty_default.png'" />
    </view>
</template>

<script>
import { checkPageHasMore } from '@/utils/live';
import videoItem from '@/components/videoItem.vue';
import pageLoading from '../../component/pageLoading.vue';
import emptyData from '../../component/emptyData.vue';
import dataLoading from '../../component/dataLoading.vue';
import dataLoaded from '../../component/dataLoaded.vue';

const imgUrl = getApp().globalData.imgUrl;
//每页数据
let hasmore = true; //是否还有数据
export default {
    components: {
        pageLoading,
        videoItem,
        emptyData,
        dataLoading,
        dataLoaded
    },
    data() {
        return {
            pn: 1,
            //当前页
            pageSize: 10,
            //每页数据
            loading: false,
            //数据加载状态
            videoList: [],
            //动态列表数据
            addCartIcon: imgUrl + 'live/add_cart.png',
            eyeIcon: imgUrl + 'live/eye.png',
            hasmore: true,
            //是否还有数据，用于页面展示
            firstLoading: true,
            //是否初次加载，是的话展示页面居中的loading效果，
            imgUrl: getApp().globalData.imgUrl, //图片地址
            // #ifdef MP
            menuButtonHeight: uni.getMenuButtonBoundingClientRect().height + 'px',
            menuButtonTop: uni.getMenuButtonBoundingClientRect().top + 'px'
            // #endif
        };
    },
    props: {
        scrollHeight: {
            // 滚动区域高度
            type: [String, Number],
            default: 'calc(100vh - 98px)'
        },
        scrollEnable: {
            // 是否允许滚动
            type: Boolean,
            default: true
        },
        memberInfo: {
            // 用户信息
            type: Object
        },
        authorInfo: {
            // 作者信息
            type: Object
        },
        author_id: {
            // 作者id
            type: String,
            default: ''
        },
        settingData: {
            // 平台设置信息
            type: Object
        }
    },

    created() {
        // 更新浏览次数
        let operateIndex = null;
        uni.$on('updateView', (index) => {
            operateIndex = index;
            if (this.videoList[index].state != 1 && this.videoList[index].state != 3) {
                this.videoList[index].clickNum = Number(this.videoList[index].clickNum) + 1;
            }
        });
        // 更新评论条数
        uni.$on('updateComment', (num) => {
            if (this.videoList[this.operateIndex].state != 1 && this.videoList[this.operateIndex].state != 3) {
                this.videoList[this.operateIndex].commentNum = num;
            }
        });

        uni.$on('updateLike', (isLike) => {
            let { likeNum } = this.videoList[operateIndex];
            if (this.videoList[operateIndex].state != 1 && this.videoList[operateIndex].state != 3) {
                if (isLike) {
                    likeNum++;
                } else {
                    likeNum--;
                }
                this.videoList[operateIndex].likeNum = likeNum;
            }
        });

        // 更新编辑后的图文审核状态
        uni.$on('updateState', (val) => {
            // if(val>=0){
            // 	this.videoList[val].state = 1;
            // }else{
            this.pn = 1;
            this.hasmore = true;
            this.getVideoList();
            // }
        });
    },

    mounted() {
        this.getVideoList();
    },

    destroyed() {
        uni.$off('updateView');
        uni.$off('updateComment');
        uni.$off('updateState');
    },

    computed: {
        topHeight() {
            //wx-2-start
            // #ifdef MP-WEIXIN
            return `calc(100vh - ${this.menuButtonTop} - ${this.menuButtonHeight} - 100rpx - 49px)`;
            // #endif
            //wx-2-end
            // #ifndef MP-WEIXIN
            return '';
            // #endif
        }
    },

    methods: {
        //获取动态数据
        getVideoList() {
            this.loading = true;
            let { author_id, firstLoading, videoList, pageSize, pn, hasmore } = this;
            let param = {};
            param.data = {};
            if (author_id != '') {
                param.data.authorId = author_id;
            }
            param.data.pageSize = pageSize;
            param.data.current = pn;
            param.url = 'v3/video/front/video/author/videoList';
            param.method = 'GET';
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    let result = res.data.videoList;
                    if (this.pn == 1) {
                        this.videoList = result;
                    } else {
                        this.videoList = videoList.concat(result);
                    }

                    if (checkPageHasMore(res.data.pagination)) {
                        this.pn++;
                    } else {
                        this.hasmore = false;
                    }
                }
                if (this.firstLoading) {
                    this.firstLoading = false;
                }
                this.loading = false;
            });
        },
        // 处理删除视频事件
        handleDelete({ state, msg, videoId }) {
            if (state == 200) {
                this.$api.msg(msg);
                // 删除成功后，重新获取视频列表
                let index = this.videoList.findIndex((i) => i.videoId == videoId);
                this.videoList.splice(index, 1);
                this.$emit('getAuthorInfo');
                this.pn = 1;
                this.hasmore = true;
                this.getVideoList();
            } else {
                this.$api.msg(msg);
            }
        },

        //加载更多数据
        getMore() {
            if (this.hasmore && !this.loading) {
                this.getVideoList();
            }
        },

        //进入播放页面
        goVideoPlay(item, index) {
            let author_id = this.authorInfo.authorId || this.author_id || '';
            this.operateIndex = index;
            if (item.videoType == 1) {
                this.$videoPlayNav({ video_id: item.videoId, author_id, index });
            } else {
                this.$Router.push({ path: '/extra/graphic/graphicDetail', query: { video_id: item.videoId, index: index, roleType: this.authorInfo.roleType } });
            }
        },
        //重新编辑功能
        editVideo(videoId, videoType, index) {
            if (videoType == 1) {
                this.$Router.push({
                    path: '/extra/svideo/svideoRelease',
                    query: {
                        video_id: videoId,
                        roleType: this.authorInfo.roleType,
                        storeId: this.authorInfo.storeId ? this.authorInfo.storeId : '',
                        edit: true
                    }
                });
            } else {
                this.$Router.push({
                    path: '/extra/graphic/graphicRelease',
                    query: {
                        video_id: videoId,
                        roleType: this.authorInfo.roleType,
                        storeId: this.authorInfo.storeId ? this.authorInfo.storeId : '',
                        index: index
                    }
                });
            }
        }
    }
};
</script>
<style lang="scss" scoped>
view {
    box-sizing: border-box;
}
.live_user_tab_content_scroll {
    height: calc(100vh - 580rpx);
    /* #ifdef MP-TOUTIAO */
    height: calc(100vh - 500rpx);
    /* #endif */
    width: 100%;
}

.video_list {
    width: 100%;
    box-sizing: border-box;
    padding: 30rpx 3%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    row-gap: 20rpx;
}

.video_list .item {
    width: 100%;
}

.video_list .item .top {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
}

.video_list .item .top .base_info {
    width: 550rpx;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
}

.video_list .item .top .fullWidth {
    width: 100%;
}

.video_list .item .top .status {
    width: 100rpx;
    height: 77rpx;
}

.video_list .item .top .base_info .time {
    color: #949494;
    font-size: 22rpx;
    line-height: 32rpx;
}

.video_list .item .top .base_info .title {
    color: #2d2d2d;
    font-size: 30rpx;
    line-height: 40rpx;
    padding: 10rpx 0;
}

.video_list .item .top .base_info .desc {
    width: 550rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #949494;
    font-size: 26rpx;
}

.video_list .item .top .base_info .reject {
    width: 550rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    color: #949494;
    font-size: 26rpx;
}

.video_list .item .video {
    width: 100%;
    height: 690rpx;
    border-radius: 15rpx;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    margin-top: 20rpx;
    position: relative;
    .graphic_label {
        position: absolute;
        top: 0;
        left: 0;
        border-radius: 15rpx 0 15rpx 0;
        background: var(--color_video_main_bg);
        color: #fff;
        padding: 4rpx 20rpx;
        font-size: 22rpx;
        display: flex;
        align-items: center;
        image {
            margin-right: 10rpx;
            width: 30rpx;
            height: 30rpx;
        }
    }
}

.video_list .item .video .play_btn {
    width: 88rpx;
    height: 88rpx;
}

.video_list .item .stat_num {
    width: 100%;
    height: 76rpx;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-left: 0;
    margin-top: 0;
}

.video_list .item .stat_num .left,
.video_list .item .stat_num .right {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
}

.video_list .item .stat_num .left image,
.video_list .item .stat_num .right image {
    width: 43rpx;
    height: 43rpx;
}

.video_list .item .stat_num .left text,
.video_list .item .stat_num .right text {
    color: #666;
    font-size: 22rpx;
}

.video_list .item .detail_num {
    width: 100%;
    height: 64rpx;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.video_list .item .detail_num text {
    color: #666;
    font-size: 24rpx;
}

.video_list .item .detail_num .color_9 {
    color: #999;
}

.video_list .item .detail_num .color_3 {
    color: #333;
}

.ml_30 {
    margin-left: 30rpx;
}

.ml_10 {
    margin-left: 10rpx;
}

.mr_30 {
    margin-right: 30rpx;
}
</style>
