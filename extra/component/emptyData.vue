<!-- 页面空数据 -->
<template name="emptyData">
    <view class="empty_data" v-if="showFlag">
        <image :src="emptyIcon" mode="aspectFit"></image>
        <text>{{ $L('暂无数据~') }}</text>
    </view>
</template>

<script>
export default {
    name: 'emptyData',
    data() {
        return {};
    },
    props: {
        showFlag: {
            type: Boolean,
            value: false
        },
        emptyIcon: {
            type: String,
            value: ''
        }
    },
    methods: {}
};
</script>

<style>
.empty_data {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: transparent;
    width: 100%;
    margin-top: 200rpx;
}

.empty_data image {
    width: 260rpx;
    height: 260rpx;
}

.empty_data text {
    font-size: 26rpx;
    margin-top: 20rpx;
    color: #949494;
}
</style>
