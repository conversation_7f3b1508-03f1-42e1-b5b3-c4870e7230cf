<template>
	<view :style="mix_diyStyle">
		<view class="main">
			<view class="fixed_top_status_bar"></view>
			<view class="main_top">
				<topBanner :name="goodsName">
					<block v-if="query != 'aTo'">
						<view class="nav">
							<view class="nav_item" @click="changeNav(idx)" v-for="(item, idx) in navList" :key="idx">
								<view :class="{ nav_item_t: true, on: current == idx }">
									<text>{{ item.name }}</text>
									<image :src="imgUrl + '' + item.img" mode="aspectFit"
										v-if="item.img && current == idx"
										:class="{ rotate: current == idx && isRotate }"></image>
								</view>
								<view :class="{ nav_item_b: current == idx }" v-if="current == idx"></view>
							</view>
							<view class="nav_item" @click="openSeries">
								<view class="nav_item_t">
									<text>{{ isCheck ? $L('完成') : $L('批量操作') }}</text>
								</view>
							</view>
						</view>
					</block>
					<block v-else>
						<view class="nav_label">
							<view class="nav_item_l" @click="changeLab(idx, item)" v-for="(item, idx) in labelList"
								:key="idx">
								<view :class="{ nav_item_t: true, on: labelIdx == idx }">
									<text>{{ item.labelName }}</text>
								</view>
								<view :class="{ nav_item_b: labelIdx == idx }"></view>
							</view>
						</view>
					</block>
				</topBanner>

				<view class="nav_type2" v-if="query == 'aTo'">
					<view class="nav2">
						<view class="nav_item" @click="changeNav(idx1)" v-for="(item1, idx1) in navList" :key="idx1">
							<view :class="{ nav_item_t2: true, on: current == idx1 }">
								<text>{{ item1.name }}</text>
								<image :src="imgUrl + '' + item1.imgb" mode="aspectFit"
									v-if="item1.imgb && current == idx1"
									:class="{ rotate: current == idx1 && item1.imgb && isRotate }"></image>
							</view>
							<view class="nav_item_b"></view>
						</view>
						<view class="nav_item" @click="openSeries">
							<view class="nav_item_t2">
								<text>{{ isCheck ? $L('完成') : $L('批量操作') }}</text>
							</view>
							<view class="nav_item_b"></view>
						</view>
					</view>
				</view>
				<!-- wdawd -->
			</view>

			<view :class="{ main_bottom: true, main_bottom_aTo: query == 'aTo' }">
				<view scroll-y="true" v-if="goodsList.length">
					<block v-for="(item, index) in goodsList" :key="index">
						<goodsItem :goods="item">
							<view slot="checkbox">
								<view :class="{checkbox:true,hasJoin:item.isJoinStore}" v-if="isCheck"
									@click.stop="check(item,'single')">
									<text :class="{iconfont:true, iconziyuan43:true}" v-if="!item.checkState"></text>
									<text :class="{item_check:true, iconfont:true, iconziyuan33:true}" v-else></text>
								</view>
							</view>

							<view slot="button">
								<block v-if="query == 'aTo'">
									<view class="slot_button" @click.stop="
                    addToOwn(item.productId, item.isJoinStore, 'single')
                  ">
										{{ item.isJoinStore ? $L('已加入小店') : $L('加入小店') }}
									</view>
								</block>
								<block v-else>
									<view v-if="item.isJoinStore" @click.stop="
                    addToOwn(item.productId, item.isJoinStore, 'single')
                  " class="slot_button slot_button_auto">{{ $L('已加入小店') }}</view>
									<view v-else class="slot_button slot_button_auto" @click.stop="openShare(item)">
										<image :src="imgUrl + 'tshou/share_icon.png'" mode="aspectFit"></image>
										<text>{{ $L('赚') }}{{ ' ' }}{{ $L('¥') }}</text>
										<text>{{ filters.toFix(item.commission) }}</text>
									</view>
								</block>
							</view>

							<view slot="commission">
								<view class="slot_commission" v-if="query == 'aTo'">
									<text>{{ $L('佣金') }}：¥{{ filters.toFix(item.commission) }}</text>
								</view>
							</view>
						</goodsItem>
					</block>
					<loadingState :state="loadState"></loadingState>
				</view>

				<tsEmpty img="tshou/empty_data.png" width="252" height="240" v-else></tsEmpty>
			</view>

			<view class="aTo_bottom" v-if="isCheck">
				<view class="aTo_bottom_check">
					<view class="checkbox_all" @click="check({},'all')">
						<text :class="{iconfont:true, iconziyuan43:true}" v-if="!checkAll"></text>
						<text :class="{item_check:true, iconfont:true, iconziyuan33:true}" v-else></text>
					</view>
					<text>{{$L("全选")}}</text>
				</view>
				<view class="aTo_bottom_add">
					<text>{{ $L('共') }}{{ checkCount }}{{ $L('件商品') }}</text>
					<view class="aTo_button" @click="addToOwn({}, false, 'all')">{{$L('加入我的小店')}}</view>
				</view>
			</view>

			<tsShare ref="tsShare" @getLink="getLink"></tsShare>
		</view>
	</view>
</template>
<script>
	import filters from '@/utils/filter.js'
	import loadingState from '@/components/loading-state.vue'
	import goodsItem from '../component/goodsItem.vue'
	import topBanner from '../component/topBanner.vue'
	import tsEmpty from '../component/tsEmpty.vue'
	import tsShare from '../component/tsShare.vue'
	import {
		mapState
	} from 'vuex'
	export default {
		components: {
			goodsItem,
			topBanner,
			tsEmpty,
			tsShare,
			loadingState
		},
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				current: 0,
				labelIdx: 0,
				navList: [{
						name: this.$L('综合')
					},
					{
						name: this.$L('销量')
					},
					{
						name: this.$L('价格'),
						img: 'tshou/price_up.png',
						imgb: 'tshou/price_up_b.png'
					},
					{
						name: this.$L('佣金'),
						img: 'tshou/price_up.png',
						imgb: 'tshou/price_up_b.png'
					}
				],
				isCheck: false,
				checkState: false,
				checkAll: false,
				labelList: [],
				labelId: 0,
				pn: 1,
				hasmore: true,
				goodsList: [],
				loadState: '',
				isRotate: false,
				tmpCheckState: [],
				sortG: 0,
				goodsName: '',
				query: '',
				goodsData: {},
				source: 0,
				sharePoster: '',
				filters
			}
		},
		//触底加载事件
		onReachBottom() {
			if (this.hasmore) {
				this.loadState = 'loading'
				this.getGoods()
			}
		},
		onLoad(option) {
			//#ifdef H5
			if (!this.hasLogin) {
				let urls = this.$Route.path
				const query = this.$Route.query
				uni.setStorageSync('fromurl', {
					url: urls,
					query
				})
				this.$Router.push('/pages/public/login')
			}
			//#endif
			//app-1-start
			//#ifdef APP-PLUS
			this.source = 2
			//#endif
			//app-1-end
			//#ifdef H5
			this.source = this.$isWeiXinBrower() ? 3 : 2
			//#endif
			//wx-1-start
			//#ifdef MP-WEIXIN
			this.source = 4
			//#endif
			//wx-1-end
			this.query = this.$Route.query.type ? this.$Route.query.type : 'proto'
			this.goodsName = this.$Route.query.name ? this.$Route.query.name : ''
			this.getLabel()
		},
		computed: {
			checkCount() {
				return this.tmpCheckState.length
			},
			...mapState(['hasLogin'])
		},
		onHide() {
			this.$refs.tsShare.closeShareModel()
		},

		onShareAppMessage() {
			return {
				title: this.goodsData.goodsName,
				desc: this.goodsData.goodsBrief,
				path: `/standard/product/detail?${this.goodsData.path}`,
				imageUrl: this.goodsData.goodsImage ?
					this.goodsData.goodsImage :
					this.goodsData.productImage,
				success: (res) => {}
			}
		},

		methods: {
			// 顶部导航切换事件
			changeNav(idx) {
				if (this.isCheck) {
					return false
				}

				this.current = idx
				this.sortG = idx
				if (this.navList[idx].imgb || this.navList[idx].img) {
					this.isRotate = !this.isRotate
					if (idx == 2) {
						this.sortG = this.isRotate ? 3 : 2
					} else if (idx == 3) {
						this.sortG = this.isRotate ? 5 : 4
					}
				}
				this.pn = 1
				this.getGoods()
				this.$forceUpdate()
			},
			//批量操作
			openSeries() {
				this.isCheck = !this.isCheck
				if (!this.isCheck) {
					this.current = 0
					this.goodsList.map((i) => {
						i.checkState = false
					})
					this.tmpCheckState = []
					this.checkAll = false
				} else {
					this.current = -1
				}
				this.$forceUpdate()
			},

			//标签切换事件
			changeLab(idx, item) {
				if (this.isCheck) {
					return false
				}
				this.labelIdx = idx;
				(this.pn = 1), (this.labelId = item.labelId)
				this.getGoods()
			},

			// 获取标签事件
			getLabel() {
				this.$request({
						url: 'v3/spreader/front/spreaderGoods/list',
						data: {
							pageSize: 20
						}
					})
					.then((res) => {
						if (res.state == 200) {
							uni.hideLoading()
							this.labelList = res.data.labelList
							if (this.labelList.length) {
								this.labelId = this.labelList[0].labelId
							}
						}
					})
					.then(() => {
						this.getGoods()
					})
			},

			//选中事件
			check(item, type) {
				if (item.isJoinStore) {
					return
				}

				//单个选中
				if (type == 'single') {
					item.checkState = !item.checkState
					//index为校验选中项数组和商品列表数组是否有相同项的下标
					let index = this.tmpCheckState.findIndex((i) => {
						i == item.productId
					})
					//选中则进入选中项数组tmpcheckState
					if (item.checkState) {
						if (this.tmpCheckState.length == 0) {
							this.tmpCheckState.push(item.productId)
						} else if (index < 0) {
							this.tmpCheckState.push(item.productId)
						}
					} else {
						//否则踢掉
						this.tmpCheckState.splice(index, 1)
						this.checkAll = false
					}
					//如果全部都选中则把全选的check置为true
					if (this.tmpCheckState.length == this.goodsList.length) {
						this.checkAll = true
					}
				} else if (type == 'all') {
					//全部选中
					this.checkAll = !this.checkAll
					//选中全选，则全部商品置为选中状态
					if (this.checkAll) {
						this.goodsList.map((i) => {
							if (!i.isJoinStore) {
								if (!i.checkState) {
									this.tmpCheckState.push(i.productId) //商品id全部push进选中项数组
								}
								i.checkState = true
							}
						})
					} else {
						//否则全置为否，选中项数组清空
						this.goodsList.map((i) => {
							i.checkState = false
						})
						this.tmpCheckState = []
					}
				}
				this.$forceUpdate()
			},

			//获取商品列表事件
			getGoods() {
				// this.goodsList = []
				let param = {
					url: 'v3/spreader/front/spreaderGoods/searchList',
					data: {
						current: this.pn,
						sort: this.sortG
					}
				}
				if (this.labelId && this.query == 'aTo') {
					param.data.labelId = this.labelId
				}
				if (this.goodsName) {
					param.data.goodsName = this.goodsName
				}
				this.$request(param).then((res) => {
					if (res.state == 200) {
						uni.hideLoading()
						if (this.pn == 1) {
							this.goodsList = res.data.list
						} else {
							this.goodsList = this.goodsList.concat(res.data.list)
							this.checkAll =
								this.tmpCheckState.length == this.goodsList.length ? true : false
						}

						if (this.$checkPaginationHasMore(res.data.pagination)) {
							this.pn++
							this.hasmore = true
							this.loadState = 'allow_loading_more'
						} else {
							this.hasmore = false
							this.loadState = 'no_more_data'
						}

						//校验选中项数组和商品列表数组是否有相同项,有则置为选中状态
						this.goodsList.map((item) => {
							if (this.tmpCheckState.indexOf(item.productId) > -1) {
								item.checkState = true
							} else {
								item.checkState = false
							}
						})

						this.$forceUpdate()
					} else {
						this.$api.msg(res.msg)
					}
				})
			},

			//加入小店事件
			addToOwn(productId, isJoinStore, type) {
				if (type != 'all' && isJoinStore) {
					return
				}

				if (type == 'all' && this.tmpCheckState.length == 0) {
					this.$api.msg(this.$L('请选择商品'))
					return
				}

				this.$request({
					url: 'v3/spreader/front/spreaderStore/edit',
					method: 'POST',
					data: {
						productIds: type == 'all' ? this.tmpCheckState.join(',') : productId,
						isCollect: true
					}
				}).then((res) => {
					this.$api.msg(res.msg)
					if (res.state == 200) {
						this.isCheck = false
						this.goodsList.map((item) => {
							item.checkState = false
							if (type == 'all') {
								this.tmpCheckState.forEach((tmp) => {
									if (tmp == item.productId) {
										item.isJoinStore = true
									}
								})
							} else {
								if (item.productId == productId) {
									item.isJoinStore = true
								}
							}
						})
						this.checkAll = false
						this.tmpCheckState = []
						this.$forceUpdate()
					}
				})
			},

			openShare(item) {
				this.$forceUpdate()
				this.goodsData = item
				this.$refs.tsShare.injectInfo(item, 'goods')
			},

			getLink(link) {
				this.goodsData.path = link.split('?')[1]
			}
		}
	}
</script>

<style lang="scss" scoped>
	page {
		background: #f5f5f5;
		position: relative;
	}

	.fixed_top_status_bar {
		position: fixed;
		//app-2-start
		/* #ifdef APP-PLUS */
		height: var(--status-bar-height);
		/* #endif */
		//app-2-end
		/* #ifndef APP-PLUS */
		height: 0;
		/* #endif */
		/* #ifdef H5 */
		 height: 170rpx;
		/* #endif */
    
		top: 0;
		left: 0;
		right: 0;
		z-index: 999;
		background: #fff;
	}

	.main_top {
		width: 100%;
		position: fixed;
		/* #ifndef MP */
		top: var(--status-bar-height);
		/* #endif */
		/* #ifdef MP */
		top: 0;
		/* #endif */
		z-index: 999;
		background: var(--color_extral_main_bg);

	}

	.main_bottom {
		position: relative;
		/* #ifdef MP */
		top: 170rpx;
		/* #endif */
		/* #ifdef H5 */
		top: 170rpx;
		/* #endif */
		//app-3-start
		/* #ifdef APP-PLUS */
		top: calc(var(--status-bar-height) + 170rpx);
		/* #endif */
		//app-3-end
		&.main_bottom_aTo {
			/* #ifdef MP||H5 */
			top: 250rpx;
			/* #endif */
			//app-4-start
			/* #ifdef APP-PLUS */
			top: calc(var(--status-bar-height) + 240rpx);
			/* #endif */
			//app-4-end
		}
	}

	.slot_button {
		background: var(--color_extral_main_bg);
		border-radius: 15px;
		padding: 10rpx 28rpx;
		font-size: 28rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #ffffff;
		line-height: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		&.slot_button_auto {
			min-width: 210rpx;
		}

		image {
			width: 32rpx;
			height: 32rpx;
			margin-right: 6rpx;
		}

		text {
			font-size: 32rpx;

			&:first-of-type {
				font-size: 24rpx;
				margin-right: 6rpx;
			}
		}
	}

	.slot_commission {
		font-size: 22rpx;
		color: #999999;
	}

	.checkbox {
		width: 46rpx;

		image {
			width: 32rpx;
			height: 32rpx;
		}

		&.hasJoin {
			opacity: 0.2;
		}

		.item_check {
			color: var(--color_extral_main);
		}
	}

	.rotate {
		transform: rotate(180deg);
	}

	.nav_type2 {
		padding: 0 20rpx 6rpx;
		// height: 56rpx;
		background-color: #fff;

		.nav2 {
			display: flex;
			flex: 1;
			font-size: 30rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			padding: 18rpx 0;
		}

		.on {
			color: var(--color_extral_main) !important;
		}

		.nav_item {
			display: flex;
			justify-content: center;
			flex: 1;

			.on {
				display: flex;
				justify-content: center;
			}
		}

		.nav_item_t2 {
			display: flex;
			align-items: center;
			justify-content: center;

			image {
				margin-left: 4rpx;
				width: 24rpx;
				height: 24rpx;
			}

			color: #666666;
		}
	}

	.nav {
		display: flex;
		justify-content: space-between;
		font-size: 30rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		margin-top: 22rpx;

		.nav_item {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: flex-start;
			width: 120rpx;

			.on {
				font-weight: bold;

				display: flex;
				justify-content: center;

				&:last-child {
					width: 130rpx;
					text-align: center;
				}
			}

			.nav_item_t {
				display: flex;
				align-items: center;
				color: #fff;

				image {
					margin-left: 4rpx;
					width: 24rpx;
					height: 24rpx;
				}
			}

			.nav_item_b {
				margin-top: 6rpx;

				height: 6rpx;
				background-color: #fff;
				border-radius: 6rpx;
				width: 70%;
			}
		}
	}

	.nav_label {
		display: flex;
		justify-content: flex-start;
		font-size: 30rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		margin-top: 26rpx;
		overflow-x: scroll;

		.nav_item_l {
			color: #fff;
			white-space: nowrap;

			margin-right: 38rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: flex-start;
			box-sizing: border-box;

			.on {
				font-weight: bold;
			}

			.nav_item_b {
				/* #ifndef APP-PLUS */
				margin-top: 8rpx;
				/* #endif */

				height: 6rpx;
				background-color: #fff;
				border-radius: 6rpx;
				width: 70%;
			}

			text {
				white-space: nowrap;
			}
		}
	}

	.aTo_bottom {
		position: fixed;
		bottom: 0;
		width: 100%;
		height: 100rpx;
		background: #ffffff;
		border: 1px solid #f0f0f0;
		display: flex;
		padding: 0 20rpx;
		justify-content: space-between;
		align-items: center;

		.aTo_bottom_check {
			display: flex;
			align-items: center;
		}

		.checkbox_all {
			height: 32rpx;
			width: 46rpx;

			image {
				width: 32rpx;
				height: 32rpx;
			}
			
			.item_check{
				color:var(--color_extral_main);
			}
		}

		.aTo_bottom_add {
			display: flex;
			align-items: center;

			text {
				margin-right: 10rpx;
				font-size: 24rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: 500;
				color: #999999;
			}

			.aTo_button {
				width: 220rpx;
				height: 60rpx;
				background:var(--color_extral_main_bg);
				border-radius: 30rpx;
				text-align: center;
				line-height: 60rpx;
				font-size: 28rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #ffffff;
			}
		}
	}
</style>