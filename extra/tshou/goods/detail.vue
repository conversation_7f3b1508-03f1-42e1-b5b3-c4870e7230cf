<template>
	<view :style="mix_diyStyle">
  <view class="container" v-if="isLoading">
    <!-- 透明遮罩层 -->
    <view
      v-if="transparent_mask"
      class="transparent_mask"
      :style="copyname_now ? 'opacity:0' : ''"
      @tap="hideMask"
    >
    </view>

    <detailPic :defaultProduct="defaultProduct"></detailPic>

    <!-- 无活动商品 start-->
    <view class="introduce_section">
      <view class="price_part flex_row_start_center">
        <view class="left">
          <view
            class="sell_price"
            v-if="defaultProduct.productPrice || choiceSpecDes.productPrice"
          >
            <text class="unit">{{ $L('¥') }} </text>
            <text class="price_int">{{
              isChoice == 'default'
                ? $getPartNumber(defaultProduct.productPrice, 'int')
                : $getPartNumber(choiceSpecDes.productPrice, 'int')
            }}</text>
            <text class="price_decimal">{{
              isChoice == 'default'
                ? $getPartNumber(defaultProduct.productPrice, 'decimal')
                : $getPartNumber(choiceSpecDes.productPrice, 'decimal')
            }}</text>
          </view>
        </view>
        <view class="right">
          <text>{{ $L('奖励') }}</text>
          <text>{{ $L('¥') }}{{ filters.toFix(commission) }}</text>
        </view>
      </view>
      <view class="flex_row_start_center">
        <view
          class="goods_name"
          @longpress="openCopyname"
          @touchend="copyOpenEnd"
        >
          <view
            v-if="copyname_now"
            class="copy_pop"
            @click.stop.prevent="copyName"
            >{{ $L('复制') }}</view
          >
          <text>{{ goodsData.goodsName }}</text>
        </view>
      </view>
      <view class="goods_ad" v-if="goodsData.goodsBrief">{{
        goodsData.goodsBrief
      }}</view>
    </view>
    <!-- 无活动商品 end -->

    <!-- 选择规格 start -->
    <view class="spec_con" @click="showSpecModel('')">
      <view class="spec_left">
        <view class="spec_left_title">{{ $L('已选') }}</view>
        <view
          class="spec_left_content"
          v-if="defaultProduct.getSpecValues || choiceSpecDes.getSpecValues"
        >
          {{
            isChoice == 'default'
              ? defaultProduct.getSpecValues
              : choiceSpecDes.getSpecValues
          }}</view
        >
        <view class="spec_left_content" v-else>{{ $L('默认') }}</view>
      </view>
      <image
        :src="imgUrl + 'goods_detail/right_down.png'"
        mode="aspectFit"
        class="spec_right"
      ></image>
    </view>
    <!-- 选择规格 end -->

    <!-- 发货地址,及运费 start -->
    <view class="deliver_goods" v-if="deliverInfo" @click="showAddress">
      <view
        class="deliver_goods_con"
        v-if="
          (!curAddress || curAddress.length < 10) &&
          filters.toFix(deliverInfo.expressFee) < 10000 &&
          goodsData.sales < 10000
        "
      >
        <view class="deliver_goods_left">
          <view class="deliver_goods_title">{{ $L('送至') }}</view>
          <view class="deliver_goods_address">
            <svgGroup type="location" class="location_box" width="17" height="19" :color="diyStyle_var['--color_extral_main']">
            </svgGroup>
            <text :class="{ addressDefault: !curAddress }">{{
              curAddress ? curAddress : $L('请选择地址')
            }}</text>
          </view>
        </view>
        <view
          class="deliver_goods_center"
          v-if="deliverInfo.expressFee != undefined"
        >
          {{ $L('快递') }}:{{ filters.toFix(deliverInfo.expressFee) }}元
        </view>
        <view class="deliver_goods_right"
          >{{ $L('已销') }}{{ goodsData.sales ? goodsData.sales : 0 }}</view
        >
      </view>
      <view class="deliver_goods_con" v-else>
        <view class="deliver_goods_left">
          <view class="deliver_goods_title">{{ $L('送至') }}</view>
        </view>
        <view class="deliver_goods_right_main">
          <view class="deliver_goods_address">
            <svgGroup type="location" class="location_box" width="17" height="19" :color="diyStyle_var['--color_extral_main']">
            </svgGroup>
            <text :class="{ addressDefault: !curAddress }">{{
              curAddress ? curAddress : '请选择地址'
            }}</text>
          </view>
          <view class="deliver_goods_right_bottom">
            <view
              class="deliver_goods_center"
              v-if="deliverInfo.expressFee != undefined"
            >
              {{ $L('快递') }}:{{ filters.toFix(deliverInfo.expressFee) }}元
            </view>
            <view class="deliver_goods_right"
              >{{ $L('已销') }}{{ goodsData.sales ? goodsData.sales : 0 }}
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 发货地址,及运费 end -->

    <!-- 规格参数 start -->
    <view
      class="spec_param"
      v-if="goodsParameterList && goodsParameterList.length > 0"
    >
      <view class="spec_param_title">
        <text>{{ $L('规格参数') }}</text>
        <image
          :src="imgUrl + 'goods_detail/spec_param_bg.png'"
          mode="aspectFit"
        ></image>
      </view>
      <view class="spec_param_list" :class="{ open_param: openGoodsParam }">
        <view
          class="spec_param_pre"
          v-for="(item, index) in goodsParameterList" :key="index"
        >
          <text>{{ item.parameterName }}</text>
          <text>{{ item.parameterValue }}</text>
        </view>
      </view>
      <view
        class="spec_param_fold"
        @click="handleGoodsParam"
        v-if="goodsParameterList.length > 5"
      >
        <text>{{ openGoodsParam ? $L('收起') : $L('展开') }}</text>
        <image
          :src="
            openGoodsParam
              ? imgUrl + 'goods_detail/up.png'
              : imgUrl + 'goods_detail/down.png'
          "
          mode="aspectFit"
        ></image>
      </view>
    </view>
    <!-- 规格参数 end -->

    <view class="detail-desc" id="nav4">
      <view class="detail-desc_title">
        <text>{{ $L('商品详情') }}</text>
        <image
          :src="imgUrl + 'goods_detail/spec_param_bg.png'"
          mode="aspectFit"
        ></image>
      </view>
      <jyf-parser
        :html="goodsData.goodsDetails"
        :isAll="true"
        ref="description"
      ></jyf-parser>
      <view style="width: 750rpx; height: 100rpx" />
    </view>

    <!-- 底部操作菜单 -->
    <view class="page_bottom">
      <view class="p_b_btn" @click="addToOwn" v-if="goodsData.state == 3">
        <image
          :src="imgUrl + 'goods_detail/store.png'"
          mode="aspectFit"
        ></image>
        <text class="show_text">{{ $L('加入我的小店') }}</text>
      </view>
      <!-- 正常商品 start -->
      <block>
        <!--商品已下架 start -->
        <view class="action_btn_group" v-if="goodsData.state != 3">
          <button class="action_btn not_stock flex_row_center_center">
            {{ $L('商品已下架') }}
          </button>
        </view>
        <!--商品已下架 end -->
        <!--库存不足start -->
        <view
          class="action_btn_group"
          v-else-if="
            (isChoice == 'default' &&
              defaultProduct &&
              defaultProduct.productStock == 0) ||
            (isChoice == 'choice' &&
              choiceSpecDes &&
              choiceSpecDes.productStock == 0)
          "
        >
          <button
            class="action_btn not_stock flex_row_center_center"
            @click="showSpecModel"
          >
            {{ $L('库存不足') }}
          </button>
        </view>
        <!--库存不足 end -->
        <!-- 普通商品 start -->
        <view class="action_btn_group" v-else>
          <view
            class="action_btn add_cart_btn flex_row_center_center"
            @click="showSpecModel('add')"
          >
            {{ $L('立即购买') }}</view
          >
          <view
            class="action_btn buy_now_btn flex_row_center_center"
            @click="goShare"
          >
            {{ $L('分享赚') }}{{ $L('¥') }}{{ filters.toFix(commission) }}</view
          >
        </view>
        <!-- 普通商品 end -->
      </block>
      <!-- 正常商品 end -->
    </view>

    <!-- 分享 -->
    <uni-popup ref="popup" type="dialog" @touchmove.stop.prevent="() => {}">
      <uni-popup-dialog
        type="input"
        :title="dialogTitle"
        :content="dialogCon"
        :duration="2000"
        before-close="true"
        @close="dialogCancle"
        @confirm="dialogConfirm"
      ></uni-popup-dialog>
    </uni-popup>

    <tsShare
      ref="tsShare"
      @getLink="getLink"
    >
    </tsShare>

    <!-- 规格弹框 start -->
    <uni-popup
      class="spec_model"
      ref="specModel"
      type="bottom"
      @touchmove.stop.prevent="() => {}"
    >
      <view class="spec_model_con">
        <view class="spec_model_content">
          <view class="spec_model_top">
            <view class="spec_model_goods">
              <view
                class="spec_goods_image"
                v-if="
                  defaultProduct &&
                  defaultProduct.goodsPics &&
                  defaultProduct.goodsPics[0] &&
                  isChoice == 'default'
                "
              >
                <!-- <image :src="defaultProduct.goodsPics[0]" mode="aspectFit"></image> -->
                <view
                  class="image"
                  :style="{
                    backgroundImage: 'url(' + defaultProduct.goodsPics[0] + ')'
                  }"
                >
                </view>
              </view>
              <view
                class="spec_goods_image"
                v-else-if="
                  choiceSpecDes &&
                  choiceSpecDes.goodsPics &&
                  choiceSpecDes.goodsPics[0] &&
                  isChoice == 'choice'
                "
              >
                <!-- <image :src="choiceSpecDes.goodsPics[0]" mode="aspectFit"></image> -->
                <view
                  class="image"
                  :style="{
                    backgroundImage: 'url(' + defaultProduct.goodsPics[0] + ')'
                  }"
                >
                </view>
              </view>
              <view class="spec_goods_right">
                <view class="spec_goods_price_con">
                  <view class="spec_prices">
                    <!-- 正常商品start -->
                    <view
                      class="spec_goods_price"
                      v-if="
                        isChoice == 'default' && defaultProduct.productPrice
                      "
                    >
                      <text>{{ $L('￥') }}</text>
                      <text>{{
                        $getPartNumber(defaultProduct.productPrice, 'int')
                      }}</text>
                      <text>{{
                        $getPartNumber(defaultProduct.productPrice, 'decimal')
                      }}</text>
                    </view>
                    <view
                      class="spec_goods_price"
                      v-if="choiceSpecDes.productPrice"
                    >
                      <text>{{ $L('￥') }}</text>
                      <text>{{
                        $getPartNumber(choiceSpecDes.productPrice, 'int')
                      }}</text>
                      <text>{{
                        $getPartNumber(choiceSpecDes.productPrice, 'decimal')
                      }}</text>
                    </view>
                    <!-- 正常商品end -->
                  </view>
                  <view class="spec_prices">
                    <text>{{ $L('奖励') }}</text>
                    <view>
                      <text>{{ $L('￥') }}</text>
                      <text>{{ filters.toFix(commission) }}</text>
                    </view>
                  </view>
                </view>
                <!-- 已下架商品 start -->
                <view class="spec_goods_des" v-if="goodsData.state != 3">
                  {{ $L('商品已下架') }}
                </view>
                <!-- 已下架商品 end -->
                <!-- 普通商品 start -->
                <view class="spec_goods_des" v-else>
                  {{ $L('已选规格') }}：
                  <text
                    v-if="
                      defaultProduct.getSpecValues ||
                      choiceSpecDes.getSpecValues
                    "
                    >{{
                      isChoice == 'default'
                        ? defaultProduct.getSpecValues
                        : choiceSpecDes.getSpecValues
                    }}</text
                  >
                  <text v-else>{{ $L('默认') }}</text>
                </view>
                <!-- 普通商品 end -->
              </view>
            </view>
            <image
              :src="imgUrl + 'goods_detail/close.png'"
              mode="aspectFit"
              class="close_spec"
              @click="closeSpecModel"
            ></image>
          </view>
          <scroll-view scroll-y="true" class="spec_content">
            <view class="spec_list" v-if="specs && specs.length > 0">
              <view
                class="spec_list_pre"
                v-for="(item, index) in specs"
                :key="index"
              >
                <view class="spec_list_pre_name">{{ item.specName }}</view>
                <block
                  v-if="
                    item && item.specValueList && item.specValueList.length > 0
                  "
                  v-for="(item1, index1) in item.specValueList"
                  :key="index1"
                >
                  <!-- checkState : 1-选中，2-可选，3-禁用 -->
                  <view
                    class="spec_list_pre_desc"
                    :class="{
                      spec_list_pre_desc_disabled: item1.checkState == '3'
                    }"
                    v-if="item1.checkState == '3'"
                  >
                    <view class="spec_list_pre_con">
                      <image
                        :src="item1.image"
                        mode="aspectFit"
                        v-if="item1.image"
                      >
                      </image>
                      <text>{{ item1.specValue }}</text>
                    </view>
                  </view>
                  <view
                    class="spec_list_pre_desc"
                    :class="{
                      spec_list_pre_desc_active: item1.checkState == '1'
                    }"
                    @click="
                      selectSpecVal('choice', item.specId, item1.specValueId)
                    "
                    v-else
                  >
                    <view class="spec_list_pre_con">
                      <image
                        :src="item1.image"
                        mode="aspectFit"
                        v-if="item1.image"
                      >
                      </image>
                      <text>{{ item1.specValue }}</text>
                    </view>
                  </view>
                </block>
              </view>
            </view>
            <view class="spec_num">
              <view class="spec_num_left">
                {{ $L('购买数量') }}
              </view>
              <view class="spec_num_right">
                <text
                  @click="editNum('reduce')"
                  :class="{ no_edit: currentSpecNum == 1 }"
                  >-</text
                >
                <input
                  type="number"
                  v-model="currentSpecNum"
                  @input="editNum('edit', $event)"
				  maxlength="5"
                />
                <text @click="editNum('add')" :class="{ no_edit: noEdit }"
                  >+</text
                >
              </view>
            </view>
          </scroll-view>
        </view>
        <!-- 规格弹框的底部按钮 start -->
        <block>
          <!-- 商品下架 start -->
          <text>{{ showSpecModelType }}</text>
          <view class="spec_btn" v-if="goodsData.state != 3">
            <button type="primary" class="spec_not_stock spec_btn_only">
              {{ $L('商品已下架') }}
            </button>
          </view>
          <!-- 商品下架 end -->
          <!--库存不足 start -->
          <view
            class="spec_btn"
            v-else-if="
              (isChoice == 'default' &&
                defaultProduct &&
                defaultProduct.productStock == 0) ||
              (isChoice == 'choice' &&
                choiceSpecDes &&
                choiceSpecDes.productStock == 0)
            "
          >
            <button type="primary" class="spec_not_stock spec_btn_only">
              {{ $L('库存不足') }}
            </button>
          </view>
          <!--库存不足 end -->
          <!-- 普通商品 start-->
          <block v-else>
            <view class="spec_btn" v-if="showSpecModelType == ''">
              <text class="spec_add_cart_btn" @click="buy">{{
                $L('立即购买')
              }}</text>
              <text class="spec_buy_btn" @click="goShare"
                >{{ '分享赚¥' }}{{ filters.toFix(commission) }}</text
              >
            </view>
            <view class="spec_btn" v-if="showSpecModelType == 'add'">
              <text class="spec_add_cart_btn spec_btn_only" @click="buy">{{
                $L('立即购买')
              }}</text>
            </view>
            <view class="spec_btn" v-if="showSpecModelType == 'buy'">
              <text class="spec_buy_btn spec_btn_only" @click="goShare"
                >{{ $L('分享赚') }}{{ $L('¥')
                }}{{ filters.toFix(commission) }}</text
              >
            </view>
          </block>
          <!-- 普通商品 end-->
        </block>
        <!-- 规格弹框的底部按钮 end -->
      </view>
    </uni-popup>
    <!-- 规格弹框 end -->

    <!-- 地址选择弹窗 start -->
    <selectAddress
      ref="selectAddress"
      :sel_data="selAddressData"
      @selectAddress="successSelectAddress"
      :isBack="isBack"
      @backToAdd="backToAdd"
    >
    </selectAddress>
    <!-- 地址选择弹窗 end -->

    <!-- 更换地址选择弹窗 start -->
    <uni-popup
      ref="addressModel"
      type="bottom"
      @touchmove.stop.prevent="moveHandle"
    >
      <view class="address_list_con">
        <view class="address_top">
          <view class="back_view">
            <image src="" mode="aspectFit"></image>
          </view>
          <view class="address_top_text">{{ $L('配送至') }}</view>
          <image
            :src="imgUrl + 'goods_detail/close.png'"
            mode="aspectFit"
            @click="addressClose"
          ></image>
        </view>
        <scroll-view
          scroll-y="true"
          class="address_list"
          @touchmove.stop.prevent="moveHandle"
        >
          <view
            v-for="(item, index) in addressList"
            :key="index"
            @click="checkAddress(item)"
            :class="{ list: true }"
          >
            <view class="wrapper flex_row_start_center">
              <svgGroup type="location" width="17" height="19" v-if="sourceId==item.addressId" :color="diyStyle_var['--color_extral_main']">
              </svgGroup>
              <svgGroup type="location" width="17" height="19" v-else>
              </svgGroup>
              <image src="" mode="aspectFit" v-else></image>
              <view class="flex_column_start_start">
                <view class="address-box">
                  <text
                    :class="{
                      address: true,
                      address_on: sourceId == item.addressId
                    }"
                    >{{ item.addressAll }} {{ item.detailAddress }}</text
                  >
                </view>
              </view>
            </view>
            <view class="wrapper_right">
              <svgGroup type="checked" :color="diyStyle_var['--color_extral_main']" width="20" height="16"
              	v-if="sourceId==item.addressId"></svgGroup>
            </view>
          </view>
        </scroll-view>
        <view class="other_address">
          <view class="other_btn" @click="chooseArea">{{
            $L('选择其他地址')
          }}</view>
        </view>
      </view>
    </uni-popup>

    <purchasePop
      ref="purchasePop"
      :exList="exceptionProList"
      :exState="exState"
      :exStateTxt="exStateTxt"
      @goNext="goNext"
      :isDetail="true"
      type='tshou'
    ></purchasePop>
    <!-- 更换地址选择弹窗 end -->
  </view>
	</view>
</template>
<script>
import purchasePop from '@/components/purchasePop.vue'
import filters from '@/utils/filter.js'
import selectAddress from '@/components/yixuan-selectAddress/yixuan-selectAddress'
import detailPic from '../component/detailPic.vue'
import uniPopup from '@/components/uni-popup/uni-popup.vue'
import uniPopupMessage from '@/components/uni-popup/uni-popup-message.vue'
import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue'
import jyfParser from '@/components/jyf-parser/jyf-parser'
import tsShare from '../component/tsShare.vue'
import { WXBrowserShareThen, quillEscapeToHtml } from '@/utils/common.js'
import { mapState, mapMutations } from 'vuex'
export default {
  components: {
    selectAddress,
    uniPopup,
    uniPopupMessage,
    uniPopupDialog,
    jyfParser,
    detailPic,
    tsShare,
    purchasePop
  },
  data() {
    return {
      imgUrl: getApp().globalData.imgUrl,
      dialogTitle: this.$L('温馨提示!'),
      dialogCon: this.$L('您需要先登录哦～'),
      goodsData: {},
      specSelected: [],
      goodsId: '', //商品id
      defaultProduct: {}, //默认货品信息
      specs: [], //商品规格列表
      choiceSpecDes: {}, //选择规格之后,要修改的数据信息
      isChoice: 'default', //是默认选中的,还是点击选择规格之后的  default:默认  choice:选择
      productId: '', //货品id
      sharePoster: null,
      source: 0, //来源，终端类型，1、pc 2、app；3、公众号或微信内部浏览器；4、小程序
      mode: 'nav', //轮播图的显示样式类型
      current: 0, //轮播图默认显示第一页
      showSpecModelType: '', //规格弹框的底部按钮的显示类型	默认：加入购物车及立即购买都有	add：加入购物车	buy：立即购买		nosocket库存不足	offshelf商品下架
      noOpcity: false, //顶部导航的背景是否有透明度，轮播图以上有，以下没有
      isLoading: false,
      transparent_mask: false, //透明遮罩蒙层
      goodsParameterList: [], //规格参数列表
      openGoodsParam: false, //规格参数超出5行，点击展开，收起
      pageCurrent: 1, //优惠券列表，页
      noEdit: false, //不可编辑
      showState: 0,
      currentSpecNum: 1,
      isChoice: 'default',
      share_model: false, //分享弹框
      shareList: [],
      poster: false, //生成海报,
      showWeiXinBrowerTip: false, //微信浏览器分享的提示操作,
      isWeiXinBrower: false, //是否微信浏览器,
      commission: 0, //推手佣金,
      openTypeShareLInk: '',
      filters,
      deliverInfo: {}, //发货地及运费信息
      selAddressData: [],
      addressAll: this.$L('请选择所在地区'),
      addressList: [], //地址列表
      sourceId: '',
      curAddress: '',
      isBack: true,
      copyname_now: false, //复制弹窗是否展示
      copyname_go: false,
      //下单价格提示
      exceptionProList: [],
      exState: 200,
      exStateTxt: ''
    }
  },
  computed: {
    ...mapState(['hasLogin', 'userInfo', 'userCenterData'])
  },
  async onLoad(options) {
    setTimeout(()=>{
      uni.setNavigationBarTitle({
        title: this.$L('商品详情')
      })
    },0);
    
    if (!this.hasLogin) {
      let urls = this.$Route.path
      const query = this.$Route.query
      uni.setStorageSync('fromurl', {
        url: urls,
        query
      })
      this.$Router.push('/pages/public/login')
    }
    uni.showLoading({
      title: this.$L('加载中!')
    })
    this.productId = this.$Route.query.productId
    this.goodsId = this.$Route.query.goodsId
    this.getGoodsDetail(this.productId)
	//wx-1-start
    //#ifdef MP-WEIXIN
    this.source = 4
    //#endif
	//wx-1-end
    // #ifdef MP-BAIDU
    this.source = 3
    // #endif
    // #ifdef MP-ALIPAY
    this.source = 2
    // #endif
    // #ifdef MP-TOUTIAO
    this.source = 1
    // #endif
    // #ifdef H5
    this.isWeiXinBrower = this.$isWeiXinBrower()
    this.source = this.isWeiXinBrower ? 3 : 2
    // #endif
  },
  onShow() {
    if (this.showState == 1) {
      this.getGoodsDetail(this.productId)
    }
  },
  onHide() {
    this.$refs.tsShare.closeShareModel()
  },
  onShareAppMessage(res) {
    return {
      title: this.goodsData.goodsName,
      desc: this.goodsData.goodsBrief,
      path: this.openTypeShareLInk,
      imageUrl: this.goodsData.shareImage
    }
  },

  methods: {
    ...mapMutations(['saveChatBaseInfo']),
    //轮播图切换
    change: function (e) {
      this.current = e.detail.current
    },

    //回到页面顶部
    goTop() {
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 300
      })
      this.currentNav = 0
    },

    getLink(link) {
      this.openTypeShareLInk = '/standard/product/detail?' + link.split('?')[1]
    },

    //增加推手分享记录
    addTsRe() {
      let spreaderMemberId = this.userCenterData.memberId
      let { productId } = this

      this.$request({
        url: 'v3/spreader/front/spreaderShare/addRecord',
        method: 'POST',
        data: {
          spreaderMemberId,
          productId
        }
      }).then((res) => {
        if (res.state == 200) {
        }
      })
    },

    //获取商品详情信息
    getGoodsDetail(productId) {
      this.$request({
        url: 'v3/goods/front/goods/details',
        data: {
          productId: this.productId, //货品id
          source: this.source
        }
      }).then((res) => {
        if (res.state == 200) {
          if (res.data.goodsDetails) {
            res.data.goodsDetails = quillEscapeToHtml(res.data.goodsDetails)
          }
          this.goodsData = res.data //详情信息
          this.goodsId = res.data.goodsId
          this.defaultProduct = res.data.defaultProduct //默认货品信息
          this.specs = res.data.specs //规格列表
          this.storeInf = res.data.storeInf //店铺信息
          this.deliverInfo = res.data.deliverInfo //发货地及运费信息
          this.goodsParameterList = []
          if (this.goodsData.brandName) {
            this.goodsParameterList.push({
              parameterName: this.$L('品牌'),
              parameterValue: this.goodsData.brandName
            })
          }
          this.goodsParameterList = this.goodsParameterList.concat(
            res.data.goodsParameterList
          ) //规格参数列表
          this.isLoading = true
          uni.hideLoading()
          this.promotionId = this.defaultProduct.promotionId //活动id
          // #ifdef H5
          let shareData = {
            title: this.goodsData.goodsName,
            desc: this.goodsData.goodsBrief,
            link: this.goodsData.shareLink,
            imgUrl: this.goodsData.shareImage
          }
          WXBrowserShareThen(1, {
            ...shareData
          })
          WXBrowserShareThen(2, {
            ...shareData
          })
          // #endif

          this.getAddressList() //获取地址列表

          this.getCommission()
        } else {
          //错误提示
          this.$api.msg(res.msg)
          this.$Router.back(1)
        }
      })
    },

    getActPrice(url, callBack) {
      let param = {
        url,
        method: 'GET',
        data: {
          productId: this.productId,
          promotionId: this.promotionId
        }
      }
      this.$request(param).then((res) => {
        if (res.state == 200) {
          // let result = res.data;
          callBack(res.data)
        } else {
          this.$api.msg(res.msg)
        }
      })
    },

    /**选择规格值
     * @param {Object} type 类型   值：choice,规格选择    default:默认
     * @param {Object} specId 父级规格值
     * @param {Object} specValueId   点击的当前的规格值
     */
    selectSpecVal(type, specId, specValueId) {
      let that = this
      this.isChoice = type == 'choice' ? 'choice' : 'default'
      let curParSpec = [] //当前点击的规格的父级id的当前项
      curParSpec = that.specs.filter((item) => item.specId == specId)
      let curSPec = [] //当前点击的规格的规格id的当前项
      curSPec = curParSpec[0].specValueList.filter(
        (item1) => item1.specValueId == specValueId
      )
      curSPec[0].checkState = 1
      //被选择的规格值的id
      let choiceSpecIds = []
      that.specs.forEach((item) => {
        if (item.specId != specId) {
          item.specValueList.forEach((item1) => {
            if (item1.checkState == '1') {
              // checkState: 1-选中，2-可选，3-禁用
              choiceSpecIds.push(item1.specValueId)
            }
          })
        } else {
          choiceSpecIds.push(specValueId)
        }
      })

      let param = {}
      param.url = 'v3/goods/front/goods/productInfo'
      param.method = 'GET'
      param.data = {}
      param.data.goodsId = that.goodsId
      param.data.specValueIds = choiceSpecIds.join(',')
      that
        .$request(param)
        .then((res) => {
          if (res.state == 200) {
            let result = res.data
            that.choiceSpecDes = result.defaultProduct
            that.productId = result.defaultProduct.productId
            that.deliverInfo = result.deliverInfo //地址及运费
            that.specs = result.specs //规格列表
            that.goodsData.shareLink = result.shareLink
            that.current = 0
            that.promotionId = that.choiceSpecDes.promotionId //活动id
            that.currentSpecNum = 1
          } else {
            this.$api.msg(res.msg)
          }
        })
        .catch((e) => {
          //异常处理
        })
    },
    //弹层取消事件
    dialogCancle() {
      this.$refs.popup.close()
    },
    //弹层确认事件
    dialogConfirm() {
      this.$Router.push(`/pages/public/login`)
      this.$refs.popup.close()
    },
    //确认下单
    buy() {
      if (!this.hasLogin) {
        getApp().globalData.goLogin()
        return
      } else {
        this.editNum('edit')
        this.$refs.specModel.close()

        let param = {
          url: 'v3/business/front/orderOperate/check',
          method: 'POST',
          header: {
            'Content-Type': 'application/json'
          },
          data: {
            isCart: false,
            productId: this.productId,
            number: this.currentSpecNum,
            source: 1,
            productPrice: this.isChoice == 'default' ? this.defaultProduct.productPrice : this.choiceSpecDes.productPrice,
          }
        }
        uni.showLoading()
        this.$request(param).then((res) => {
			uni.hideLoading()
          if (res.state == 200) {
            //从这里跳转页面后置为1，从一个页面返回回来，将在onShow里调用getGoodsDetail
            this.showState = 1
            this.$Router.push({
              path: '/order/confirmOrder',
              query: {
                orderType: 1,
                goodsId: this.goodsId,
                productId: this.productId,
                numbers: this.currentSpecNum,
                ifcart: 2
              }
            })
          } else if (res.state == 267) {
            this.exceptionProList = res.data.productList
            this.exState = res.data.state
            this.exStateTxt = res.data.stateValue
            if (this.exState == 7) {
              this.$refs.purchasePop.open(0)
            } else if (this.exState == 5) {
              this.$refs.purchasePop.open(1)
            } else {
              this.$api.msg(res.data.stateValue)
            }
          } else {
            this.$api.msg(res.msg)
          }
        })
      }
    },
	
	
	goNext() {
		this.showState = 1
		this.$Router.push({
		  path: '/order/confirmOrder',
		  query: {
		    orderType: 1,
		    goodsId: this.goodsId,
		    productId: this.productId,
		    numbers: this.currentSpecNum,
		    ifcart: 2
		  }
		})
	},
	
	
	
	
	
    //弹层取消事件
    dialogCancle() {
      this.$refs.popup.close()
    },
    //弹层确认事件
    dialogConfirm() {
      this.$Router.push(`/pages/public/login`)
      this.$refs.popup.close()
    },
    //规则展开
    descriptionOpen(couponId) {
      this.couponList.map((item) => {
        if (item.couponId == couponId) {
          if (item.description != '') {
            item.isOpen = !item.isOpen
            this.$forceUpdate()
          }
        }
      })
    },
    //打开规格弹框
    showSpecModel(type) {
      if (type == 'add') {
        this.showSpecModelType = 'add'
      } else if (type == 'buy') {
        this.showSpecModelType = 'buy'
      } else if (type == 'offshelf') {
        this.showSpecModelType = 'offshelf'
      } else if (type == 'nosocket') {
        this.showSpecModelType = 'nosocket'
      } else {
        this.showSpecModelType = ''
      }
      this.$forceUpdate()
      this.$refs.specModel.open()
    },

    //编辑数量
    editNum(type, e) {
      let that = this
      if (type == 'add') {
        if (
          (that.isChoice == 'default' &&
            that.currentSpecNum >= that.defaultProduct.productStock) ||
          (that.isChoice == 'choice' &&
            that.currentSpecNum >= that.choiceSpecDes.productStock)
        ) {
          if (
            that.defaultProduct.productStock == 0 ||
            that.choiceSpecDes.productStock == 0
          ) {
            that.currentSpecNum = 1
          } else {
            that.currentSpecNum =
              that.isChoice == 'default'
                ? that.defaultProduct.productStock
                : that.choiceSpecDes.productStock
          }
          that.noEdit = true
        } else {
          if (that.currentSpecNum > 999) {
            that.currentSpecNum = 999
            that.noEdit = true
          } else {
            that.currentSpecNum++
            that.noEdit = false
          }
        }
      } else if (type == 'edit') {
        if (that.currentSpecNum == '' || that.currentSpecNum < 0) {
          setTimeout(() => {
            that.currentSpecNum = 1
          }, 0)
          return
        }
        if (
          (that.isChoice == 'default' &&
            that.currentSpecNum > that.defaultProduct.productStock) ||
          (that.isChoice == 'choice' &&
            that.currentSpecNum > that.choiceSpecDes.productStock)
        ) {
          that.currentSpecNum =
            that.isChoice == 'default'
              ? that.defaultProduct.productStock
              : that.choiceSpecDes.productStock
          that.noEdit = true
          return
        } else {
          that.currentSpecNum =
            e && e.detail.value ? e.detail.value : that.currentSpecNum
          if (that.currentSpecNum == 0 || that.currentSpecNum < 0) {
            setTimeout(() => {
              that.currentSpecNum = 1
            }, 0)
            return
          } else {
            that.currentSpecNum = that.currentSpecNum
              .toString()
              .replace(/\D/g, '')
            if (that.currentSpecNum > 999) {
              setTimeout(() => {
                that.currentSpecNum = 999
              }, 0)
              that.noEdit = true
              return
            } else {
              setTimeout(() => {
                that.currentSpecNum = that.currentSpecNum
              }, 0)
              that.noEdit = false
            }
          }
        }
      } else if (type == 'reduce') {
        if (that.currentSpecNum > 1) {
          that.currentSpecNum--
          that.noEdit = false
        } else {
          that.currentSpecNum = 1
        }
      }
    },
    //关闭规格弹框
    closeSpecModel() {
      this.$refs.specModel.close()
    },
    //隐藏透明遮罩层
    hideMask() {
      if (this.copyname_now) {
        if (!this.copyname_go) {
          return
        }
        this.copyname_now = false
      } else {
        this.tips_show = false
      }
      this.transparent_mask = false
    },


    goShare() {	  
	  this.$refs.tsShare.injectInfo(this.goodsData,'goods')
      this.addTsRe()
    },

    //展开规格参数
    handleGoodsParam() {
      this.openGoodsParam = !this.openGoodsParam
    },
    //加入小店事件
    addToOwn() {
      this.$request({
        url: 'v3/spreader/front/spreaderStore/edit',
        method: 'POST',
        data: {
          productIds: this.productId,
          isCollect: true
        }
      }).then((res) => {
        this.$api.msg(res.msg)
      })
    },

    getCommission() {
      this.$request({
        url: 'v3/spreader/front/spreaderGoods/detail',
        data: {
          productId: this.productId
        }
      }).then((res) => {
        if (res.state == 200) {
		  this.defaultProduct.productStock = res.data.stock
		  this.goodsData.sales = res.data.saleNum
          this.commission = res.data.commission
          this.defaultProduct.productPrice = res.data.productPrice
        } else {
          this.$api.msg(res.msg)
        }
      })
    },

    //获取地址列表
    getAddressList() {
      if (!this.hasLogin) {
        return false
      }
      this.$request({
        url: 'v3/member/front/memberAddress/list',
        method: 'GET'
      }).then((res) => {
        if (res.state == 200) {
          if (res.data.list.length > 0) {
            this.addressList = res.data.list
            if (this.addressList.findIndex((i) => i.isDefault == 1) > 0) {
              let index = this.addressList.findIndex((i) => i.isDefault == 1)
              this.curAddress =
                this.addressList[index].addressAll +
                '' +
                this.addressList[index].detailAddress
              this.sourceId = this.addressList[index].addressId
            } else {
              this.curAddress =
                this.addressList[0].addressAll +
                '' +
                this.addressList[0].detailAddress
              this.sourceId = this.addressList[0].addressId
            }
            if (uni.getStorageSync('addressId')) {
              let addressID = uni.getStorageSync('addressId')
              if (res.data.list.filter((i) => i.addressId == addressID)[0]) {
                let tmp = res.data.list.filter(
                  (i) => i.addressId == addressID
                )[0]
                this.curAddress = tmp.addressAll + '' + tmp.detailAddress
                this.sourceId = tmp.addressId
              }
            }
          }
        }
      })
    },
    //选择地址
    showAddress() {
      if (!this.hasLogin || this.addressList.length == 0) {
        this.$refs.selectAddress.showNoMask()
        this.isBack = false
      } else {
        this.$refs.addressModel.open()
        this.isBack = true
      }
    },
    backToAdd() {
      this.$refs.selectAddress.hidden()
      this.$refs.addressModel.open()
    },
    //选择成功回调
    successSelectAddress(address) {
      this.curAddress = ''
      address.map((item) => {
        this.curAddress += item.name
      })
      let regionCode = address[address.length - 2].code
      this.getUserEx(regionCode)
    },
    checkAddress(item) {
      this.sourceId = item.addressId
      this.$refs.addressModel.close()
      this.curAddress = item.addressAll + '' + item.detailAddress
      uni.setStorageSync('addressId', this.sourceId)
      let cityCode = item.cityCode
      this.getUserEx(cityCode)
    },
    addressClose() {
      this.$refs.addressModel.close()
    },
    chooseArea() {
      this.$refs.addressModel.close()
      this.$refs.selectAddress.showNoMask()
    },
    //用于切换地址，获取运费
    getUserEx(cityCode) {
      this.$request({
        url: 'v3/goods/front/goods/calculateExpress',
        data: {
          cityCode,
          productId: this.productId
        }
      }).then((res) => {
        if (res.state == 200) {
          this.deliverInfo.expressFee = res.data
        } else {
          this.$api.msg(res.msg)
        }
      })
    },

    //复制产品名称
    openCopyname() {
      this.copyname_go = false
      this.copyname_now = true
      this.transparent_mask = true
    },
    copyOpenEnd() {
      setTimeout(() => {
        this.copyname_go = true
      }, 300)
    },
    copyName() {
      let data = this.goodsData.goodsName
      uni.setClipboardData({
        data,
        success: () => {
          this.transparent_mask = false
          this.copyname_now = false
          this.$api.msg('已复制到剪切板')
        }
      })
    }
  }
}
</script>

<style lang="scss">
page {
  background: $bg-color-split;
  /* padding-bottom: 100rpx; */
  width: 750rpx;
  margin: 0 auto;
}

.container {
  position: relative;
}

.icon-you {
  font-size: $font-base;
  color: #888;
}

/* 透明遮罩层 */
.transparent_mask {
  width: 100%;
  height: 100%;
  position: fixed;
  background: rgba(0, 0, 0, 1);
  opacity: 0.4;
  top: 0;
  left: 0;
  z-index: 10;
}

.fixed_top_status_bar {
  position: fixed;
  //app-1-start
  /* #ifdef APP-PLUS */
  height: var(--status-bar-height);
  /* #endif */
  //app-1-end
  /* #ifndef APP-PLUS */
  height: 0;
  /* #endif */
  top: 0;
  left: 0;
  right: 0;
  z-index: 99;
  background: rgba(250, 250, 250, 0.9);
}

.fixed_top_status_bar_no_opcity {
  background: #ffffff;
}

/* 标题简介 */
.introduce_section {
  background: #fff;
  padding: 20rpx;
  margin-bottom: 20rpx;

  .price_part {
    .left {
      display: flex;
      flex-direction: column;

      .sell_price {
        color:var(--color_extral_main);

        .unit {
          font-size: 26rpx;
          font-weight: bold;
        }

        .price_int {
          font-size: 50rpx;
          line-height: 50rpx;
          margin-left: 4rpx;
          font-weight: bold;
        }

        .price_decimal {
          font-size: 26rpx;
          font-weight: bold;
        }
      }

      .original_price {
        color: #949494;
        font-size: 22rpx;
        text-decoration: line-through;
      }
    }

    .right {
      margin-left: 36rpx;

      text {
        font-size: 24rpx;

        &:first-child {
          color: #333333;
        }

        &:last-child {
          color:var(--color_extral_main);
        }
      }
    }
  }

  .goods_name {
    position: relative;
    margin-top: 20rpx;
    min-width: 120rpx;

    text {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      line-height: 45rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      word-break: break-word;
    }

    .copy_pop {
      position: absolute;
      top: -76rpx;
      right: -8rpx;
      z-index: 51;
      width: 140rpx;
      height: 68rpx;
      line-height: 68rpx;
      color: #fff;
      font-size: 28rpx;
      text-align: center;
      border-radius: 10rpx;
      background: #000;

      &:after {
        content: '';
        position: absolute;
        right: 56rpx;
        bottom: -14rpx;
        z-index: 51;
        width: 0;
        height: 0;
        border-top: 14rpx solid #000;
        border-left: 14rpx solid transparent;
        border-right: 14rpx solid transparent;
      }
    }
  }

  .goods_ad {
    color: #666666;
    font-size: 28rpx;
    line-height: 60rpx;
    height: 60rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .coupon-tip {
    align-items: center;
    padding: 4rpx 10rpx;
    background: $uni-color-primary;
    font-size: $font-sm;
    color: #fff;
    border-radius: 6rpx;
    line-height: 1;
    transform: translateY(-4rpx);
  }
}

/* 选择规格 start */
.spec_con {
  padding: 20rpx 8rpx 20rpx 20rpx;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  /* align-items: center;
		height: 90rpx; */
  background: #ffffff;

  .spec_left {
    display: flex;
    /* align-items: center; */

    .spec_left_title {
      font-size: 26rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #666666;
      line-height: 45rpx;
      margin-right: 35rpx;
    }

    .spec_left_content {
      width: 550rpx;
      /* white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden; */
      word-break: break-all;
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #343434;
      line-height: 45rpx;
      margin-right: 10rpx;
    }
  }

  .spec_right {
    width: 36rpx;
    height: 36rpx;
  }
}

/* 选择规格 end */

/* 发货地址及运费 start */
.deliver_goods {
  background: #ffffff;
  border-top: 1rpx solid #f2f2f2;

  .deliver_goods_con {
    margin: 0 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .deliver_goods_left {
      display: flex;
      align-items: center;

      .deliver_goods_title {
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #666666;
        margin-right: 36rpx;
      }
    }

    .deliver_goods_address {
      height: 100rpx;
      display: flex;
      align-items: center;

      image {
        width: 34rpx;
        height: 38rpx;
        margin-right: 10rpx;
      }

      text {
        display: inline-block;
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #333333;
        line-height: 45rpx;
        width: 252rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .deliver_goods_right_main {
      width: 610rpx;
      padding-top: 8rpx;
      padding-bottom: 10rpx;

      .deliver_goods_address {
        height: 60rpx;
        margin-bottom: 4rpx;

        text {
          width: 560rpx;
        }
      }

      .deliver_goods_right_bottom {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .deliver_goods_center {
          margin-left: 44rpx;
        }
      }
    }

    .deliver_goods_center {
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #2d2d2d;
      line-height: 45rpx;
    }

    .deliver_goods_right {
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #777777;
      line-height: 45rpx;
    }
  }
}

/* 发货地址及运费 end */

.c-list {
  font-size: $font-sm;
  color: $font-color-base;
  background: #fff;

  .c-row {
    display: flex;
    align-items: center;
    padding: 20rpx 20rpx;
    position: relative;
  }

  .tit {
    color: #666;
    font-size: 26rpx;
    margin-right: 35rpx;
  }

  .con {
    flex: 1;
    color: #333;
    font-size: 28rpx;

    .selected-text {
      margin-right: 10rpx;
    }
  }

  .bz-list {
    height: 40rpx;
    font-size: $font-sm;
    color: $font-color-dark;

    text {
      display: inline-block;
      margin-right: 30rpx;
    }
  }

  .con-list {
    flex: 1;
    display: flex;
    flex-direction: column;
    color: $font-color-dark;
    line-height: 40rpx;
  }

  .red {
    color: $uni-color-primary;
  }
}

/* 买家秀 start */
.buy_show {
  background: #ffffff;

  .buy_show_top {
    height: 80rpx;
    border-top: 1rpx solid #f2f2f2;
    margin: 0 20rpx;
    padding: 31rpx 0 20rpx 0;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .buy_show_title {
      font-size: 30rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #2d2d2d;
    }

    .buy_show_more {
      text {
        font-size: 26rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #fc281f;
        line-height: 45rpx;
      }

      image {
        width: 12rpx;
        height: 20rpx;
        margin-left: 18rpx;
      }
    }
  }

  .buy_show_con {
    display: flex;
    align-items: center;
    padding-bottom: 20rpx;

    .buy_show_pre {
      width: 177rpx;
      height: 177rpx;
    }

    .buy_show_pre:nth-child(1) {
      border-radius: 15rpx 0 0 15rpx;
    }

    .buy_show_pre:nth-last-child(1) {
      border-radius: 0 15rpx 15rpx 0;
    }

    .only {
      border-radius: 15rpx;
    }
  }
}

/* 买家秀 end */

/* 规格参数 start */
.spec_param {
  background: #ffffff;
  margin-top: 20rpx;
  padding-bottom: 30rpx;

  .spec_param_title {
    display: flex;
    flex-direction: column;
    padding: 30rpx 0 0 38rpx;

    text {
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: bold;
      color: #2d2d2d;
      line-height: 36rpx;
    }

    image {
      width: 675rpx;
      height: 22rpx;
    }
  }

  .spec_param_list {
    padding: 0 20rpx;
    max-height: 350rpx;
    margin-top: 20rpx;
    box-sizing: border-box;

    .spec_param_pre {
      width: 710rpx;
      height: 70rpx;
      display: flex;
      align-items: center;
      font-size: 24rpx;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #777777;
      line-height: 36rpx;
      border: 1rpx solid #f2f2f2;
      border-bottom: 0;

      text:nth-child(1) {
        width: 167rpx;
        height: 70rpx;
        border-right: 1rpx solid #f2f2f2;
        line-height: 70rpx;
        text-align: right;
        padding-right: 20rpx;
      }

      text:nth-child(2) {
        width: 541rpx;
        height: 70rpx;
        line-height: 70rpx;
        padding-left: 20rpx;
        box-sizing: border-box;
      }
    }

    .spec_param_pre:nth-last-child(1) {
      border-bottom: 1rpx solid #f2f2f2;
    }
  }

  .open_param {
    height: auto;
  }

  .spec_param_fold {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 30rpx;

    text {
      font-size: 24rpx;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #777777;
    }

    image {
      width: 20rpx;
      height: 12rpx;
      margin-left: 10rpx;
    }
  }
}

/* 规格参数 end */

/*  详情 */
.detail-desc {
  background: #fff;
  margin-top: 20rpx;
  overflow-x: hidden;
  padding: 30rpx 20rpx;
  box-sizing: border-box;

  /* wx-2-start */
  /* #ifdef MP-WEIXIN */
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  /* #endif */
  /* wx-2-end */

  .detail-desc_title {
    margin-bottom: 30rpx;
    display: flex;
    flex-direction: column;

    text {
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: bold;
      color: #2d2d2d;
      line-height: 36rpx;
      text-align: center;
    }

    image {
      width: 675rpx;
      height: 22rpx;
    }
  }
}

.poster_share_img {
  width: 390rpx;
  height: 90rpx;
  /* left: 179rpx; */
  position: absolute;
  bottom: 177rpx;
  margin: 72rpx 0 22rpx;
}

.poster_share_close {
  width: 49rpx;
  height: 49rpx;
  position: absolute;
  /* left: 351rpx; */
  bottom: 105rpx;
}

/*  弹出层 */
.popup {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 99;

  &.show {
    display: block;

    .mask {
      animation: showPopup 0.2s linear both;
    }

    .layer {
      animation: showLayer 0.2s linear both;
    }
  }

  &.hide {
    .mask {
      animation: hidePopup 0.2s linear both;
    }

    .layer {
      animation: hideLayer 0.2s linear both;
    }
  }

  &.none {
    display: none;
  }

  .mask {
    position: fixed;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    background-color: rgba(0, 0, 0, 0.4);
  }

  .layer {
    position: fixed;
    z-index: 99;
    bottom: 0;
    width: 100%;
    min-height: 40vh;
    border-radius: 10rpx 10rpx 0 0;
    background-color: #fff;

    .btn {
      height: 66rpx;
      line-height: 66rpx;
      border-radius: 100rpx;
      background: $uni-color-primary;
      font-size: $font-base;
      color: #fff;
      margin: 30rpx auto 20rpx;
    }
  }

  @keyframes showPopup {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  @keyframes hidePopup {
    0% {
      opacity: 1;
    }

    100% {
      opacity: 0;
    }
  }

  @keyframes showLayer {
    0% {
      transform: translateY(120%);
    }

    100% {
      transform: translateY(0%);
    }
  }

  @keyframes hideLayer {
    0% {
      transform: translateY(0);
    }

    100% {
      transform: translateY(120%);
    }
  }
}

.spec_model_con {
  width: 750rpx;
  height: 900rpx;
  background: #ffffff;
  border-radius: 10rpx 10rpx 0;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  z-index: 150;

  .spec_model_content {
    padding-bottom: 115rpx;

    .spec_model_top {
      display: flex;
      justify-content: space-between;
      padding: 30rpx 22rpx 0 30rpx;
      box-sizing: border-box;

      .spec_model_goods {
        display: flex;
        height: 151rpx;
        /* align-items: center; */

        .spec_goods_image {
          width: 151rpx;
          height: 151rpx;
          background: #eeeeee;
          border-radius: 15rpx;

          .image {
            width: 151rpx;
            height: 151rpx;
            border-radius: 15rpx;
            background-position: center center;
            background-size: cover;
            background-repeat: no-repeat;
          }
        }

        .spec_goods_right {
          margin-left: 30rpx;
          flex-shrink: 0;

          .spec_goods_price_con {
            display: flex;
            align-items: center;

            .spec_prices {
              display: flex;

              .spec_goods_price {
                display: inline-block;

                text {
                  font-size: 24rpx;
                  font-family: PingFang SC;
                  font-weight: 500;
                  color:var(--color_extral_main);
                }

                text:nth-child(2) {
                  font-size: 50rpx;
                }
              }

              &:last-child {
                margin-left: 20rpx;
                font-size: 28rpx;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #333333;

                view {
                  display: flex;
                  margin-left: 10rpx;
                  color:var(--color_extral_main);
                }
              }
            }

            .sec_kill_tips {
              width: 130rpx;
              height: 40rpx;
              background: linear-gradient(
                90deg,
                #ffaa06 0%,
                #ff8323 0%,
                #fc5300 0%,
                #ff1353 100%
              );
              border-radius: 20rpx;
              font-size: 24rpx;
              font-family: PingFang SC;
              font-weight: 500;
              color: #ffffff;
              text-align: center;
              line-height: 40rpx;
              margin-left: 20px;
            }

            .pre_sale_tips {
              width: 76rpx;
              height: 38rpx;
              background: linear-gradient(90deg, #891ff7, #da01e8);
              border-radius: 18rpx;
              font-size: 22rpx;
              font-family: PingFang SC;
              font-weight: 500;
              color: #fff;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-left: 20px;
            }

            .ladder_regiment_tips {
              width: 100rpx;
              height: 40rpx;
              background: linear-gradient(90deg, #ff7a18 0%, #fea10e 100%);
              border-radius: 20rpx;
              font-size: 24rpx;
              font-family: PingFang SC;
              font-weight: 500;
              color: #ffffff;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-left: 20rpx;
            }

            .pin_tips {
              width: 80rpx;
              height: 40rpx;
              background: linear-gradient(90deg, #fc1c1c 0%, #ff6c00 100%);
              border-radius: 20rpx;
              font-size: 24rpx;
              font-family: PingFang SC;
              font-weight: 500;
              color: #ffffff;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-left: 20rpx;
            }
          }

          .spec_goods_des {
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #343434;
            margin-top: 19rpx;
            width: 520rpx;
            /* white-space: nowrap;
							text-overflow: ellipsis;
							overflow: hidden; */
            word-break: break-all;
          }
        }
      }

      .close_spec {
        position: absolute;
        top: 14rpx;
        right: 14rpx;
        z-index: 9;
        width: 46rpx;
        height: 46rpx;
      }
    }

    .spec_content {
      height: 620rpx;

      .spec_list {
        margin: 0 30rpx;
        padding-top: 34rpx;

        .spec_list_pre {
          border-bottom: 1rpx solid #f5f5f5;
          margin-bottom: 20rpx;

          .spec_list_pre_name {
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #666666;
            margin-bottom: 20rpx;
          }

          .spec_list_pre_desc {
            display: inline-table;
            padding: 13rpx 25rpx;
            box-sizing: border-box;
            box-sizing: border-box;
            background: #f5f5f5;
            border-radius: 8rpx;
            margin-bottom: 20rpx;
            margin-right: 20rpx;
            border: 1rpx solid #f5f5f5;

            .spec_list_pre_con {
              display: flex;
              align-items: center;

              text {
                font-size: 26rpx;
                font-family: PingFang SC;
                font-weight: 500;
                color: #343434;
                text-align: left;
              }

              image {
                width: 36rpx;
                height: 36rpx;
                margin-right: 20rpx;
              }
            }
          }

          .spec_list_pre_desc_active {
            background: #ffffff;
            border: 1rpx solid var(--color_extral_main);

            .spec_list_pre_con {
              text {
                color: var(--color_extral_main);
              }
            }
          }

          .spec_list_pre_desc_disabled {
            background: #f5f5f5;
            opacity: 0.2;

            .spec_list_pre_con {
              text {
                color: #2d2d2d;
              }
            }
          }
        }
      }

      .spec_num {
        height: 50rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 20rpx 0 30rpx;
        box-sizing: border-box;
        margin-top: 16rpx;

        .spec_num_left {
          font-size: 28rpx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #666666;

          text {
            color: #949494;
          }
        }

        .spec_num_right {
          width: 182rpx;
          height: 50rpx;
          border: 1rpx solid #ededed;
          border-radius: 6rpx;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 24rpx;
          font-family: PingFang SC;
          font-weight: bold;
          color: #a6a6a6;
          line-height: 30rpx;

          text {
            width: 51rpx;
            height: 50rpx;
            text-align: center;
            line-height: 50rpx;
            border-left: 1rpx solid #ededed;

            &.no_edit {
              background: #f8f8f8;
              opacity: 0.5;
              color: #949494;
            }
          }

          text:nth-child(1) {
            color: #949494;
            border-right: 1rpx solid #ededed;
            border-left: none;
          }

          input {
            width: 78rpx;
            height: 50rpx;
            line-height: 50rpx;
            text-align: center;
            font-size: 24rpx;
          }
        }
      }
    }
  }

  .spec_btn {
    width: 750rpx;
    height: 98rpx;
    background: #ffffff;
    box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(86, 86, 86, 0.08);
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
    bottom: env(safe-area-inset-bottom);

    .spec_add_cart_btn {
      width: 345rpx;
      height: 70rpx;
      background:var(--color_extral_vice);
      border-radius: 35rpx 0 0 35rpx;
      font-size: 30rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
      text-align: center;
      line-height: 70rpx;
    }

    .spec_buy_btn {
      width: 345rpx;
      height: 70rpx;
      background: var(--color_extral_main);
      border-radius: 0 35rpx 35rpx 0;
      font-size: 30rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
      text-align: center;
      line-height: 70rpx;
    }

    .spec_not_stock {
      background: #adadad;
      border-radius: 35rpx;
      font-size: 30rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
      text-align: center;
      line-height: 70rpx;
    }

    .spec_seckill_btn {
      background: linear-gradient(45deg, #fc2d2d 0%, #fd572b 100%);
      border-radius: 35rpx;
      font-size: 30rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
      text-align: center;
      line-height: 70rpx;
    }

    .spec_btn_only {
      width: 690rpx;
      height: 70rpx;
      border-radius: 35rpx;
      text-align: center;
      line-height: 70rpx;
    }

    .specifications_btn2 {
      width: 690rpx;
      height: 70rpx;
      background: linear-gradient(
        45deg,
        #ff5c00 0%,
        #fce000 0%,
        #fe8300 0%,
        #fb9721 100%
      );
      border-radius: 35rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 32rpx;
    }

    .specifications_bottom_btn3 {
      width: 690rpx;
      height: 70rpx;
      background: linear-gradient(
        45deg,
        #ff5d00 0%,
        #fce000 0%,
        #fe8400 0%,
        #fb9721 100%
      );
      border-radius: 35rpx;
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 30rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .specifications_bottom_btn4 {
      width: 690rpx;
      height: 70rpx;
      background: linear-gradient(45deg, #fb2d2d 0%, #fc572a 100%);
      border-radius: 35rpx;
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 30rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .specification_add {
      width: 347rpx;
      height: 70rpx;
      background: linear-gradient(45deg, #ff7918 0%, #fea00d 100%);
      border-radius: 34rpx 0 0 34rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 28rpx;
    }

    .specification_add text:nth-of-type(1),
    .specification_buy text:nth-of-type(1) {
      margin-right: 20rpx;
    }

    .specification_buy {
      width: 343rpx;
      height: 70rpx;
      background: linear-gradient(45deg, #fb2d2d 0%, #fc572a 100%);
      border-radius: 0 34rpx 34rpx 0;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 28rpx;
    }
  }
}

/* //sdasdasd */
.right_down {
  width: 36rpx;
  height: 36rpx;
}

.uni-transition {
  width: 750rpx;
  margin: 0 auto;
}

/* 底部操作菜单 */
.page_bottom {
  position: fixed;
  left: 0rpx;
  right: 0rpx;
  margin: 0 auto;
  bottom: 0rpx;
  z-index: 95;
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 750rpx;
  background: #fff;
  box-shadow: 0px 0px 20px 0px rgba(86, 86, 86, 0.2);
  padding-bottom: calc(12rpx + constant(safe-area-inset-bottom));
  /* 兼容 iOS < 11.2 */
  padding-bottom: calc(12rpx + env(safe-area-inset-bottom));
  /* 兼容 iOS >= 11.2 */
  box-sizing: border-box;
  padding-top: 12rpx;
  /* #ifdef H5 */
  padding-bottom: 12rpx;

  /* #endif */
  .p_b_btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: $font-sm;
    color: $font-color-base;
    /* width: 96rpx; */
    height: 80rpx;
    position: relative;
    margin-top: 6rpx;
    margin-bottom: 6rpx;

    image {
      width: 40rpx;
      height: 40rpx;
      margin-bottom: 2rpx;
    }

    .show_text {
      color: #2d2d2d;
      font-size: 20rpx;
    }
  }

  .ladder_btn {
    width: 420rpx;
    height: 70rpx;
    background: linear-gradient(
      45deg,
      rgba(255, 122, 24, 1) 0%,
      rgba(254, 161, 14, 1) 100%
    );
    border-radius: 35rpx;
    font-size: 28rpx;
    font-family: PingFang SC;
    font-weight: 500;
    color: rgba(255, 255, 255, 1);
    line-height: 70rpx;
    text-align: center;
  }

  .action_btn_group {
    display: flex;
    height: 70rpx;
    overflow: hidden;
    margin-left: 20rpx;
    color: #fff;

    .action_btn {
      height: 100%;
      font-size: 32rpx;
      color: #fff;

      &::after {
        border: none;
      }
    }

    .add_cart_btn {
      width: 223rpx;
      background:var(--color_extral_vice);
      border-radius: 35rpx 0 0 35rpx;
    }

    .buy_now_btn {
      /* width: 210rpx; */
      padding: 0 20rpx;
      background:var(--color_extral_main);
      border-radius: 0 35rpx 35rpx 0;
    }

    .not_stock {
      width: 420rpx;
      background: #adadad;
      border-radius: 35rpx;
    }

    .instant_pay_deposit {
      width: 420rpx;
      height: 70rpx;
      background: linear-gradient(45deg, #ff7a18 0%, #fea10e 100%);
      border-radius: 35rpx;
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 26rpx;
    }
  }
}


button {
  padding: 0;
  margin: 0;
}

/* 变更选择地址弹窗 */
.address_list_con {
  border-radius: 5px 5px 0;

  .address_top {
    padding: 20rpx 30rpx;
    border-radius: 5px 5px 0 0;
    font-size: 32rpx;
    font-family: PingFang SC;
    font-weight: bold;
    color: #333333;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    border-bottom: 0.5px solid #f2f2f2;

    image {
      width: 50rpx;
      height: 50rpx;
    }
  }

  .address_list {
    width: 750rpx;
    height: 680rpx;
    margin: 0 auto;
    z-index: 150;
    background-color: #fff;

    .wrapper {
      flex: 1;
      background: #fff;

      .iconfont {
        color: $main-color;
        font-size: 32rpx;
        margin-right: 30rpx;
      }

      image {
        width: 36rpx;
        height: 38rpx;
        margin-right: 22rpx;
      }

      .address-box {
        display: flex;
        align-items: center;
        margin-left: 22rpx;
        .address {
          font-size: 28rpx;
          color: #333;
          line-height: 38rpx;
          margin-top: 5rpx;
          word-break: break-all;
          max-width: 570rpx;
        }

        .tag {
          width: 63rpx;
          height: 30rpx;
          margin-left: 20rpx;
          margin-right: 0rpx;
        }

        .address_on {
          color:var(--color_extral_main) !important;
        }
      }
    }

    .wrapper_right {
      .checkedIcon {
        width: 32rpx;
        height: 24rpx;
        margin-left: 30rpx;
      }
    }

    .list {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      padding: 24rpx 30rpx;
      background: #fff;
      position: relative;
    }
  }

  .other_address {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 130rpx;
    background: #fff;

    .other_btn {
      width: 668rpx;
      height: 80rpx;
      background:var(--color_extral_main_bg);
      border-radius: 44rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 34rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #fefefe;
    }
  }
}
.location_box{
  margin-right: 10rpx;
}
</style>
