<template>
	<view :style="mix_diyStyle">
  <view>
    <view
      class="main_ts"
      :style="'background-image:url(' + imgUrl + 'tshou/tsg_bg.png)'"
      v-if="alts"
    >
      <view class="main_text">
        <view>{{ $L('购买推手礼包') }}</view>
        <view>{{ $L('方可成为推手，享受推手权益') }}</view>
      </view>
      <view class="main_con">
        <view class="inner_con">
          <view class="inner_img">
            <image
              :src="imgUrl + 'tshou/tsg_banner.png'"
              mode="aspectFit"
            ></image>
          </view>
          <view
            class="inner_b_img"
            :style="'background-image:url(' + loBag[0] + ')'"
          ></view>
          <view class="inner_text">
            <view>{{ loBag[1] }}</view>
            <view>{{ loBag[2] }}</view>
            <view>¥{{ loBag[3] }}</view>
          </view>
          <view class="inner_but_con">
            <view class="inner_but" @click="goBuyTs">{{ $L('前往购买') }}</view>
          </view>
        </view>
      </view>
    </view>
    <uni-popup ref="popup" type="dialog">
      <uni-popup-dialog
        type="input"
        :title="this.$L('提示')"
        :content="failMsg"
        :duration="2000"
        @close="acDialog"
        @confirm="acDialog"
      ></uni-popup-dialog>
    </uni-popup>
  </view>
	</view>
</template>

<script>
import uniPopup from '@/components/uni-popup/uni-popup.vue'
import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue'
import { mapState } from 'vuex'
export default {
  components: {
    uniPopup,
    uniPopupDialog
  },
  data() {
    return {
      imgUrl: getApp().globalData.imgUrl,
      loBag: [],
      alts: false,
      failMsg: ''
    }
  },
  onLoad() {
    setTimeout(()=>{
      uni.setNavigationBarTitle({
        title: this.$L('推手礼包')
      })
    },0);
    
    if (!this.hasLogin) {
      let urls = this.$Route.path
      const query = this.$Route.query
      uni.setStorageSync('fromurl', {
        url: urls,
        query
      })
      this.$Router.push('/pages/public/login')
    }
    this.getBag()
  },
  computed: {
    ...mapState(['hasLogin'])
  },
  methods: {
    getBag() {
      this.$request({
        url: 'v3/system/front/setting/getSettings',
        data: {
          names:
            'spreader_gift_bag_image,spreader_gift_bag_name,spreader_gift_bag_description,spreader_gift_bag_amount'
        }
      }).then((res) => {
        if (res.state == 200) {
          this.alts = true
          this.loBag = res.data
        } else if (res.state == 255) {
          this.$api.msg(res.msg)
        }
      })
    },
    acDialog() {
      this.$Router.back(1)
    },
    goBuyTs() {
      this.$Router.push('/extra/tshou/tsGift/giftPay')
    }
  }
}
</script>

<style lang="scss" scoped>
page {
  background: #f5f5f5;
}

.main_ts {
  background-repeat: no-repeat;
  background-size: contain;
  background-position: 0 0;
  width: 750rpx;
  margin: 0 auto;
  padding: 24rpx 20rpx 20rpx;

  .main_text {
    text-align: center;

    view {
      margin-top: 20rpx;
      font-size: 44rpx;
      font-family: MFTongXin_Noncommercial-Regular, MFTongXin_Noncommercial;
      font-weight: 400;
      color: #ffffff;
    }
  }

  .main_con {
    margin-top: 320rpx;
    width: 100%;
    background: #ffffff;
    border-radius: 8rpx;
    // height: 854rpx;
    padding: 30rpx 42rpx 0;
    position: relative;

    .inner_con {
      background: linear-gradient(180deg, #ffede9 0%, #ffffff 100%);
      border-radius: 8rpx;
      padding: 0 24rpx;

      .inner_img {
        width: 170rpx;
        height: 44rpx;
        margin: 0 auto;

        image {
          width: 170rpx;
          height: 44rpx;
        }
      }

      .inner_b_img {
        height: 380rpx;
        margin-top: 40rpx;
        background: #fff;
        background-position: center center;
        background-repeat: no-repeat;
        background-size: contain;
      }

      .inner_text {
        view {
          margin-top: 20rpx;
          font-size: 28rpx;
          max-width: 474rpx;
          word-break: break-all;

          &:first-child {
            color: #303030;
          }

          &:nth-child(2) {
            color: #b1b1b1;
          }

          &:last-child {
            font-size: 32rpx;
            color:var(--color_extral_main);
          }
        }
      }

      .inner_but_con {
        width: 100%;
        // position: absolute;
        margin-top: 20rpx;
        padding: 20rpx 0;
        bottom: 0;

        .inner_but {
          margin: 0 auto;
          width: 534rpx;
          height: 80rpx;
          background:var(--color_extral_main_bg);
          border-radius: 40rpx;
          text-align: center;
          line-height: 80rpx;
          color: #fff;
        }
      }
    }
  }
}
</style>
