<template>
	<view :style="mix_diyStyle">
  <view class="main_comdetail">
    <view class="question">
      <text>{{ detail.problemContent }}</text>
    </view>
    <view class="answer">
      <!-- <text>{{detail.problemReply}}</text> -->
      <!-- #ifdef MP -->
      <rich-text :nodes="detail.problemReply"></rich-text>
      <!-- #endif -->
      <!-- #ifndef MP -->
      <jyfParser :isAll="true" :html="detail.problemReply"></jyfParser>
      <!-- #endif -->
    </view>
  </view>
	</view>
</template>

<script>
import { mapState } from 'vuex'
import { quillEscapeToHtml } from '@/utils/common.js'
import jyfParser from '@/components/jyf-parser/jyf-parser.vue'
export default {
  components: {
    jyfParser
  },
  data() {
    return {
      problemId: 0,
      detail: {}
    }
  },
  computed: {
    ...mapState(['hasLogin'])
  },
  onLoad(op) {
    setTimeout(()=>{
      uni.setNavigationBarTitle({
        title: this.$L('问题详情')
      })
    },0);
    
    this.problemId = this.$Route.query.problemId
    if (!this.hasLogin) {
      let urls = this.$Route.path
      const query = this.$Route.query
      uni.setStorageSync('fromurl', {
        url: urls,
        query
      })
      this.$Router.push('/pages/public/login')
    } else {
      this.getComDetail()
    }
  },
  onshow() {
    this.getComDetail()
  },
  methods: {
    getComDetail() {
      let param = {
        url: 'v3/spreader/front/problem/detail',
        data: {
          problemId: this.problemId
        }
      }
      this.$request(param).then((res) => {
        if (res.state == 200) {
          res.data.problemReply = quillEscapeToHtml(res.data.problemReply)
          this.detail = res.data
        } else {
          this.$api.msg(res.msg)
        }
      })
    }
  }
}
</script>

<style lang="scss">
.question {
  padding: 30rpx 26rpx;
  word-break: break-all;
  background-color: #f7f7f7;
  font-weight: bold;
  color: #000000;
}

.answer {
  padding: 30rpx 26rpx;
  word-break: break-all;
  color: #000000;
  font-size: 28rpx;
}

.answer ::v-deep table td {
  width: auto;
  flex: 1;
}
</style>
