<template>
	<view :style="mix_diyStyle">
			<view class="main_commiss_bg_box">
				<view class="main_commiss_bg" :style="'background-image:url('+imgUrl+'tshou/census_bg.png)'" v-if="authoState">
				  
				</view>
			</view>
			<view class="main_commiss" v-if="authoState">
				<view class="commiss_top">
					<text>{{ $L('累计总收入') }}</text>
					<text>¥{{ commiSum }}</text>
					<text>{{ $L('收入自动转入余额中，可用于消费') }}</text>
				</view>
				<view class="commiss_mid">
					<view class="day_nav">
						<view :class="{ d_nav_item: true, sel: navIdx == itm.id }" v-for="(itm, idx) in arr" :key="idx"
							@click="changeNav(itm.id)">
							{{ itm.t }}
						</view>
					</view>
					<view class="nav_content">
						<view class="nav_d_picker" v-if="navIdx == 5">
							<picker mode="date" @change="getDate($event, 'start')"
								:end="endTime ? endTime : filters.getDateTime(initEndTime)">
								<view class="dp_item">
									<image :src="imgUrl + 'tshou/calen_icon.png'" mode="aspectFit"></image>
									<text>{{ startTime ? startTime : $L('开始日期') }}</text>
								</view>
							</picker>
							<text>~</text>
							<picker mode="date" @change="getDate($event, 'end')" :start="startTime"
								:end="filters.getDateTime(initEndTime)">
								<view class="dp_item">
									<image :src="imgUrl + 'tshou/calen_icon.png'" mode="aspectFit"></image>
									<text>{{ endTime ? endTime : $L('截止日期') }}</text>
								</view>
							</picker>
						</view>
						<view class="nav_con">
							<view class="nav_content_item" v-for="(n, i) in arr2" :key="i">
								<text>{{ n.title }}</text>
								<text>{{ n.value }}</text>
							</view>
						</view>
					</view>
				</view>
				<view class="commiss_bottom">
					<scroll-view scroll-y="true" @scrolltolower="load" v-if="orderList.length">
						<view v-for="(item, index) in orderList" :key="index">
							<orderItem :order="item"></orderItem>
						</view>
						<loadingState :state="loadState"></loadingState>
					</scroll-view>
					<tsEmpty :text="$L('暂无订单')" img="store/no_content.png" width="200" height="200" v-else></tsEmpty>
				</view>
			</view>
			<!-- // -->
			<view v-else>
				<noAuthorized></noAuthorized>
			</view>
			<DiyTabBar idx="2"></DiyTabBar>
	</view>
</template>

<script>
	import orderItem from '../component/orderItem.vue'
	import tsEmpty from '../component/tsEmpty.vue'
	import noAuthorized from '../component/noAuthorized.vue'
	import DiyTabBar from '../component/DiyTabBar.vue'
	import filters from '@/utils/filter.js'
	import loadingState from '@/components/loading-state.vue'
	export default {
		components: {
			orderItem,
			tsEmpty,
			noAuthorized,
			DiyTabBar,
			loadingState
		},
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				navIdx: 1,
				orderList: [],
				loading: true,
				loadState: '',
				hasmore: true,
				arr: [{
						t: this.$L('今日'),
						id: 1
					},
					{
						t: this.$L('昨天'),
						id: 2
					},
					{
						t: this.$L('本月'),
						id: 3
					},
					{
						t: this.$L('上月'),
						id: 4
					},
					{
						t: this.$L('自定义'),
						id: 5
					}
				],
				commiSum: 0,
				arr2: [{
						title: this.$L('订单收入'),
						value: '¥0'
					},
					{
						title: this.$L('订单数'),
						value: 0
					},
					{
						title: this.$L('邀请新用户'),
						value: 0
					}
				],
				pn: 1,
				startTime: '',
				initEndTime: new Date().getTime(),
				endTime: '',
				authoState: true,
				filters
			}
		},
		onLoad() {
			this.getSum()
			this.getOther()
		},
		methods: {
			changeNav(id) {
				if (this.navIdx == id) {
					return
				}
				this.navIdx = id
				this.orderList = []
				if (id != 5) {
					this.endTime = ''
					this.startTime = ''
					this.getOther()
				}
				this.arr2 = [{
						title: this.$L('订单收入'),
						value: '¥0'
					},
					{
						title: this.$L('订单数'),
						value: 0
					},
					{
						title: this.$L('邀请新用户'),
						value: 0
					}
				]
			},
			load() {
				if (this.hasmore) {
					this.loadState = 'loading'
					this.getOrder()
				}
			},

			getSum() {
				this.$request({
					url: 'v3/spreader/front/spreaderOrderInfo/sumCommission'
				}).then((res) => {
					if (res.state == 200) {
						this.commiSum = res.data
					}
				})
			},

			getOther() {
				let param1 = {
					url: 'v3/spreader/front/spreaderOrderInfo/list',
					data: {
						timeState: this.navIdx,
						current: this.pn
					}
				}
				if (this.endTime && this.startTime) {
					param1.data.startTime = this.startTime.replace(/\//g, '-') + ' 00:00:00'
					param1.data.endTime = this.endTime.replace(/\//g, '-') + ' 23:59:59'
					delete param1.data.timeState
				}
				this.$request(param1).then((res) => {
					if (res.state == 200) {
						this.authoState = true
						this.orderList = res.data.spreaderOrderInfoVOList
						this.arr2 = [{
								title: this.$L('订单收入'),
								value: '¥' + res.data.income
							},
							{
								title: this.$L('订单数'),
								value: res.data.spreaderOrderNum
							},
							{
								title: this.$L('邀请新用户'),
								value: res.data.inviteNewSpreaderNum
							}
						]
					} else {
						this.$api.msg(res.msg)
					}
				})
			},

			// getOrder() {
			// 	let param = {
			// 		url: 'v3/spreader/front/spreaderOrderInfo/list',
			// 		data: {
			// 			current: this.pn,
			// 			timeState: this.navIdx
			// 		}
			// 	}
			// 	if(this.endTime&&this.startTime){
			// 		param.data.endTime = encodeURIComponent(this.endTime)
			// 		param.data.startTime = encodeURIComponent(this.startTime)
			// 		delete param.data.timeState
			// 	}
			// 	this.$request(param).then(res => {
			// 		if (res.state == 200) {
			// 			this.authoState = true
			// 			this.orderList = res.data.spreaderOrderInfoVOList
			// 			this.loadState = 'no_more_data'
			// 			if (this.pn == 1) {
			// 				this.orderList = res.data.spreaderOrderInfoVOList
			// 			} else {
			// 				this.orderList = this.orderList.concat(res.data.spreaderOrderInfoVOList)
			// 			}

			// 			if (this.$checkPaginationHasMore(res.data.pagination)) {
			// 				this.hasmore = true
			// 				this.loadState = 'allow_loading_more'
			// 			} else {
			// 				this.hasmore = false
			// 				this.loadState = 'no_more_data'
			// 			}
			// 		} else if(res.state==269){
			// 			this.authoState = false
			// 			this.$api.msg(res.msg)
			// 		}
			// 	})
			// },

			getDate(e, type) {
				if (type == 'start') {
					this.startTime = e.detail.value
				} else {
					this.endTime = e.detail.value
				}
				if (this.startTime && this.endTime) {
					this.getOther()
				}
			}
		}
	}
</script>

<style lang="scss">
	.main_commiss_bg_box{
	    position: fixed;
	    z-index: -1;
	    background: var(--color_extral_main_bgZero);
	  .main_commiss_bg{
	    background-position: 0 0;
	    background-repeat: no-repeat;
	    background-size: contain;
	    height: 363rpx;
	    width: 750rpx;
	  }
	}
	.main_commiss {
		padding: 0 20rpx;
		padding-top: 44rpx;
		/* #ifdef H5 */
		padding-top: 44rpx;
		/* #endif */
		/* #ifdef APP-PLUS||MP */
		padding-top: calc(var(--status-bar-height) + 44rpx);
		/* #endif */
		padding-bottom: 100rpx;
		padding-left: 20rpx;
		padding-right: 20rpx;
		padding-bottom: calc(constant(safe-area-inset-bottom) + 100rpx);
		padding-bottom: calc(env(safe-area-inset-bottom) + 100rpx);

		.commiss_top {
			text-align: center;

			text {
				margin-bottom: 34rpx;
				display: block;

				&:first-child {
					color: #ffffff;
					font-size: 28rpx;
				}

				&:nth-child(2) {
					color: #ffffff;
					font-size: 24px;
					font-family: DIN-Black, DIN;
					font-weight: 900;
				}

				&:last-child {
					font-size: 24rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #ffffff;
					opacity: 0.7;
				}
			}
		}

		.commiss_mid {
			background: #fff;
			border-radius: 16rpx;

			.day_nav {
				display: flex;

				.d_nav_item {
					padding: 12rpx 0;
					flex: 1;
					text-align: center;
					font-size: 30rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #999;

					&.sel {
						background: #f6f6f6;
						color:var(--color_extral_vice);
						border-radius: 16rpx 16rpx 0 0;
					}
				}
			}

			.nav_content {
				padding: 26rpx;
				// display: flex;
				// justify-content: space-between;
				background: #f6f6f6;
				border-radius: 0 0 16rpx 16rpx;
				margin-bottom: 40rpx;

				.nav_d_picker {
					display: flex;
					justify-content: center;
          margin-bottom: 20rpx;
					&>text {
						color: #959595;
					}

					.dp_item {
						margin: 0 10rpx;
						display: flex;
						align-items: center;
						padding: 10rpx 30rpx;
						background-color: #fff;
						border-radius: 20rpx;
						color: #959595;
						font-size: 24rpx;
						max-width: 276rpx;

						image {
							margin-right: 14rpx;
							width: 32rpx;
							height: 32rpx;
						}
					}
				}

				.nav_con {
					display: flex;
					justify-content: space-between;

					.nav_content_item {
						background:var(--color_extral_main_bgZero);
						border-radius: 10rpx;
						height: 100%;
						flex: 1;
						max-width: 210rpx;
						padding: 10rpx 0;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						align-items: center;
            opacity: 0.75;
						text {
							color: #ffffff;

							&:first-child {
								font-size: 24rpx;
							}

							&:last-child {
								font-size: 28rpx;
								font-weight: 600;
							}
						}
					}
				}
			}
		}

		.commiss_bottom {
			margin-top: 40rpx;
		}
	}
</style>