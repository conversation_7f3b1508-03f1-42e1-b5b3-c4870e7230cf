<template>
	<view :style="mix_diyStyle">
  <view class="sld_point_search">
    <view class="fixed_top_status_bar"></view>
    <!-- 小程序头部兼容 -->
    <view :class="{ mp_search_box: true, fixedTop: isTop }">
      <view class="search_con">
        <image
          :src="imgUrl + 'search.png'"
          mode="aspectFit"
          class="search_img"
        ></image>
        <input
          confirm-type="search"
          @confirm="search"
          class="ser_input"
          type="text"
          v-model="keyword"
          :placeholder="$L('请输入关键字')"
          placeholder-class="search_input"
        />
        <image
          class="clear_content"
          v-show="keyword"
          @click="clearInputVal"
          :src="imgUrl + 'input_clear.png'"
        />
      </view>
      <text class="search_btn" @click="backLastPage">{{ $L('取消') }}</text>
    </view>
    <view class="no_data_con" v-if="recommendedList.length == 0">
      <!-- 暂无历史记录 -->

      <text class="no_history" v-if="history_val.length == 0 && !keyword">{{
        $L('暂无历史搜索记录~')
      }}</text>
      <!-- 搜索历史 start -->
      <view
        class="search-item"
        v-if="!keyword && history_val && history_val.length"
        style="padding-bottom: 0"
      >
        <view class="search-title">
          <text>{{ $L('搜索历史') }}</text>
          <view class="del" @click="clearHistory">
            <image :src="imgUrl + 'del_search.png'" />
          </view>
        </view>

        <view class="search-con">
          <view
            class="item"
            v-for="(item, index) in history_val"
            :key="index"
            @click="btnSearch(item)"
            >{{ item }}</view
          >
        </view>
      </view>
      <!-- 搜索历史 end -->
      <!-- 分类空页面 -->
      <view class="empty_sort_page" v-if="keyword && is_show_empty">
        <image
          :src="imgUrl + 'empty_goods.png'"
          mode="aspectFit"
          class="empty_img"
        ></image>
        <view class="empty_text">{{ $L('暂无数据') }}</view>
      </view>
    </view>
    <!-- 积分商城搜索列表 -->
    <!-- <view class="recommend_list">
			<recommendGoods class='recommend_pre' v-for="(recommendItem,recommendIndex) in recommendedList" :goods_info="recommendItem"></recommendGoods>
		</view>
		<recommendItemList v-if='!this.hasMore' ref='recommendList'></recommendItemList> -->
  </view>
	</view>
</template>

<script>
import { mapState } from 'vuex'
// import recommendGoods from '../components/recommend_item_v.vue'
// import loadingState from "../components/loading-state.vue";
// import recommendItemList from '../components/recommend_list.vue'
export default {
  data() {
    return {
      imgUrl: getApp().globalData.imgUrl,
      keyword: '',
      recommendedList: [],
      loadingState: '',
      current: 1,
      pageSize: 10,
      hasMore: false,
      is_show_empty: false,
      labelId: '',
      history_val: [],
      isTop: false
    }
  },
  components: {
    // recommendGoods,
    // loadingState,
    // recommendItemList
  },
  computed: {
    ...mapState(['hasLogin', 'userInfo', 'userCenterData'])
  },
  async mounted() {
    this.getHistoryList()
  },
  onReachBottom() {},

  onPageScroll(e) {
    if (e.scrollTop > 100) {
      this.isTop = true
    } else {
      this.isTop = false
    }
  },
  onLoad(options) {
    if (!this.hasLogin) {
      let urls = this.$Route.path
      const query = this.$Route.query
      uni.setStorageSync('fromurl', {
        url: urls,
        query
      })
      this.$Router.push('/pages/public/login')
    }

    if (options.labelId) {
      this.labelId = this.$Route.query.labelId
    }
    if (options.keyword) {
      this.keyword = this.$Route.query.keyword
    }
  },
  methods: {
    clearInputVal() {
      this.keyword = ''
    },
    btnSearch(item) {
      this.keyword = item
      this.search()
    },
    backLastPage() {
      this.$Router.back(1)
    },
    search() {
      let { keyword } = this
      if (!keyword) {
        return
      }

      uni.hideKeyboard()

      keyword = keyword.trim()
      if (keyword.length > 0) {
        this.setHistoryData()
      }
      uni.showLoading()
      this.$Router.replace({
        path: '/extra/tshou/goods/list',
        query: { name: this.keyword }
      })
    },
    //设置缓存
    setHistoryData() {
      let { history_val, keyword } = this
      let tmp_data = [...history_val]
      tmp_data.unshift(keyword)
      // 最多取12条，不重复且不为空的数据
      tmp_data = tmp_data.reduce((a, b) => {
        a.length < 12 && b && a.indexOf(b) == -1 ? a.push(b) : null
        return a
      }, [])
      let history_val_str = tmp_data.join('~')
      this.history_val = tmp_data
      uni.setStorageSync('ts_his_keyword', history_val_str)
    },
    //获取历史记录
    getHistoryList() {
      let history_data = uni.getStorageSync('ts_his_keyword')
      if (history_data) {
        let his_array = history_data.split('~')
        let last_arr = []
        for (var i = 0; i < his_array.length; i++) {
          !this.$checkSpace(his_array[i]) && last_arr.push(his_array[i])
        }
        this.history_val = last_arr
      }
    },
    //清除搜索历史
    clearHistory() {
      uni.removeStorageSync('ts_his_keyword')
      this.history_val = []
    }
  }
}
</script>

<style lang="scss">
.fixed_top_status_bar {
  position: fixed;
  //app-1-start
  /* #ifdef APP-PLUS */
  height: var(--status-bar-height);
  /* #endif */
  //app-1-end
  /* #ifndef APP-PLUS */
  height: 0;
  /* #endif */
  top: 0;
  left: 0;
  right: 0;
  z-index: 99;
  background: #fff;
}
page {
  background: #f5f5f5;
  width: 750rpx;
  margin: 0 auto;
}

.mp_search_box {
  display: flex;
  align-items: center;
  background-color: white;
  padding: 11rpx 0;
  transition: all 0.4s ease;
  //app-2-start
  /* #ifdef APP-PLUS */
  padding-top: calc(var(--status-bar-height) + 11rpx);
  /* #endif */
  //app-2-end
  .search_con {
    width: 631rpx;
    height: 65rpx;
    background: #f5f5f5;
    border-radius: 33rpx;
    padding: 18rpx 22rpx;
    display: flex;
    align-items: center;
    margin-left: 20rpx;
    .ser_input {
      flex: 1;
      padding-left:12rpx;
      /* #ifdef MP-TOUTIAO */
      font-size: 28rpx;
      /* #endif */
      background: transparent;
      font-size: 28rpx;
      margin-top: 2rpx;
    }
    .search_img {
      width: 32rpx;
      height: 32rpx;
    }
    .clear_content {
      width: 44rpx !important;
      height: 44rpx !important;
    }
    .search_input {
      font-size: 28rpx;
      color: #999999;
    }
  }
  .search_btn {
    margin-left: 20rpx;
    font-size: 30rpx;
    color: #333333;
  }
}

.fixedTop {
  position: fixed;
  top: 0;
  z-index: 1000;
  width: 100%;
}
.no_data_con {
  .no_history {
    color: #2d2d2d;
    font-size: 12px;
    display: inline-block;
    width: 100%;
    height: 100%;
    text-align: center;
    line-height: 500rpx;
  }
  height: 514rpx;
  background-color: white;
}
.recommend_list {
  margin-top: 20rpx;
  display: flex;
  flex-wrap: wrap;
  padding: 0 20rpx;
  .recommend_pre {
    margin-right: 20rpx !important;
    &:nth-child(2n) {
      margin-right: 0 !important;
    }
  }
}
// 空页面
.empty_sort_page {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx;

  .empty_img {
    width: 380rpx;
    height: 280rpx;
    margin-bottom: 32rpx;
  }

  .empty_text {
    font-size: 26rpx;
    color: #999;
  }
}
.search-item {
  padding: 30rpx 28rpx;

  .search-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 48rpx;
    color: #2d2d2d;
    font-size: 28rpx;
    font-weight: bold;

    image {
      width: 48rpx;
      height: 48rpx;
    }
  }

  .search-con {
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .item {
      height: 50rpx;
      padding: 0 18rpx;
      color: #2d2d2d;
      line-height: 50rpx;
      font-size: 24rpx;
      background-color: #f5f5f5;
      border-radius: 25rpx;
      margin-right: 20rpx;
      margin-top: 20rpx;
      max-width: 274rpx;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;
    }
  }
}
</style>
