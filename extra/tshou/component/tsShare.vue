<template>
	<view class="share_main">
		<!-- 分享弹框 start -->
		<view class="share_model1 flex_column_end_center" v-if="share_model" @touchmove.stop.prevent="moveHandle">
			<view class="share_model_list">
				<!-- #ifdef H5 -->
				<view class="share_model_pre" @tap.stop="sldShareBrower(1)" v-if="isWeiXinBrower">
					<image :src="imgUrl + 'goods_detail/wx_share.png'" mode="aspectFit"></image>
					<text>{{ $L('微信好友') }}</text>
				</view>
				<view class="share_model_pre" @tap.stop="sldShareBrower(2)" v-if="isWeiXinBrower">
					<image :src="imgUrl + 'goods_detail/wechat_moments.png'" mode="aspectFit"></image>
					<text>{{ $L('微信朋友圈') }}</text>
				</view>
				<!-- #endif -->
				<!-- wx-1-start -->
				<!-- #ifdef MP-WEIXIN -->
				<button open-type="share" class="share_model_pre" @click="closeShareModel">
					<image :src="imgUrl + 'goods_detail/wx_share.png'" mode="aspectFit"></image>
					<text>{{ $L('微信好友') }}</text>
				</button>
				<!-- #endif -->
				<!-- wx-1-end -->
				<!-- app-1-start -->
				<!-- #ifdef APP-PLUS -->
				<view class="share_model_pre" @tap.stop="sldShare(0, 'WXSceneSession')">
					<image :src="imgUrl + 'goods_detail/wx_share.png'" mode="aspectFit"></image>
					<text>{{ $L('微信好友') }}</text>
				</view>
				<view class="share_model_pre" @tap.stop="sldShare(0, 'WXSenceTimeline')">
					<image :src="imgUrl + 'goods_detail/wechat_moments.png'" mode="aspectFit"></image>
					<text>{{ $L('微信朋友圈') }}</text>
				</view>
				<!-- #endif -->
				<!-- app-1-end -->
				<view class="share_model_pre" v-if="shareType != 'shop'">
					<image :src="imgUrl + 'goods_detail/poster.png'" mode="aspectFit" @click="getPoster"></image>
					<text>{{ $L('生成海报') }}</text>
				</view>
			</view>
			<view class="share_model_close" @click="closeShareModel">
				<image :src="imgUrl + 'goods_detail/share_close.png'" mode="aspectFit"></image>
			</view>
		</view>
		<!-- 分享弹框 end -->

		<!-- 生成海报  start-->
		<view class="poster" v-if="poster" @touchmove.stop.prevent="moveHandle">
			<!-- 分享海报弹框 start -->
			<view class="share_model" :class="{ poster_share_model: poster }">
				<sldPoster ref="sldPoster" :info="posterInfo" @cachePoster="cachePoster"></sldPoster>
				<view class="share_model_list">
					<!-- #ifdef MP -->
					<view class="share_model_pre" @tap.stop="downloadPoster">
						<image :src="imgUrl + 'goods_detail/poster.png'" mode="aspectFit"></image>
						<text>{{ $L('下载海报') }}</text>
					</view>
					<!-- #endif -->
					<!-- app-2-start -->
					<!-- #ifdef APP-PLUS -->
					<view class="share_model_pre" @tap.stop="saveImgAPP">
						<image :src="imgUrl + 'goods_detail/poster.png'" mode="aspectFit"></image>
						<text>{{ $L('保存海报') }}</text>
					</view>
					<view class="share_model_pre" @tap.stop="sldShare(2, 'WXSceneSession')">
						<image :src="imgUrl + 'goods_detail/wx_share.png'" mode="aspectFit"></image>
						<text>{{ $L('微信好友') }}</text>
					</view>
					<view class="share_model_pre" @tap.stop="sldShare(2, 'WXSenceTimeline')">
						<image :src="imgUrl + 'goods_detail/wechat_moments.png'" mode="aspectFit"></image>
						<text>{{ $L('微信朋友圈') }}</text>
					</view>
					<!-- #endif -->
					<!-- app-2-end -->
				</view>
				<!-- #ifndef H5 -->
				<view class="share_model_close" @click="closeShareModel">
					<image :src="imgUrl + 'goods_detail/share_close.png'" mode="aspectFit"></image>
				</view>
				<!-- #endif -->
				<!-- #ifdef H5 -->
				<image :src="imgUrl + 'goods_detail/poster_share.png'" mode="aspectFit" class="poster_share_img"></image>
				<image :src="imgUrl + 'goods_detail/poster_share_close.png'" mode="aspectFit" class="poster_share_close"
					@click.stop="closePoster"></image>
				<!-- #endif -->
			</view>
			<!-- 分享海报弹框 end -->
		</view>
		<!-- 生成海报  end-->
		<!-- 微信浏览器分享提示  start-->
		<view class="wx_brower_share_mask" v-if="showWeiXinBrowerTip">
			<view class="wx_brower_share_top_wrap">
				<image :src="imgUrl + 'wx_share_tip.png'" mode="widthFix" @tap="closeShareModel"
					class="wx_brower_share_img"></image>
			</view>
		</view>
		<!-- 微信浏览器分享提示  end-->
	</view>
</template>

<script>
	import {
		weBtoa
	} from '@/utils/base.js'
	import {
		WXBrowserShareThen
	} from '@/utils/common.js'
	import {mapState } from 'vuex'
	import sldPoster from '@/components/sld_poster/poster.vue'
	export default {
		components: {
			sldPoster
		},
		data() {
			return {
				dialogTitle: this.$L('温馨提示!'),
				dialogCon: this.$L('您需要先登录哦～'),
				imgUrl: getApp().globalData.imgUrl,
				share_model: false, //分享弹框
				shareList: [],
				poster: false, //生成海报,
				showWeiXinBrowerTip: false, //微信浏览器分享的提示操作,
				isWeiXinBrower: false, //是否微信浏览器,
				productId: 0,
				data: {},
				source: 0,
				sharePoster: '',
				sharePosterLink: '',
				goodsData:{},
				shareType:''
			}
		},
		mounted() {
			//#ifdef MP-TOUTIAO
			this.source = 1
			//#endif
			//#ifdef MP-ALIPAY
			this.source = 2
			//#endif
			// #ifdef MP-BAIDU
			this.source = 3
			// #endif
			//wx-2-start
			//#ifdef MP-WEIXIN
			this.source = 4
			//#endif
			//wx-2-end
			this.isWeiXinBrower = this.$isWeiXinBrower()
		},
		
		computed:{
			...mapState(['userCenterData']),
			posterInfo(){
				let {defaultProduct,goodsName,goodsImage,productImage,shareImage,goodsBrief,productPrice,goodsId,productId} = this.goodsData
				let info = {
					name:goodsName,
					image:goodsImage||productImage||shareImage,
					brief:goodsBrief,
					price:'¥' + productPrice,
				}
				
				info.marketPrice = defaultProduct?'¥' + this.$getPartNumber(defaultProduct.marketPrice,'all'):''
				if(productPrice) info.price = '¥' + this.$getPartNumber(productPrice,'all')
				if(defaultProduct){
					info.price = '¥' + this.$getPartNumber(defaultProduct.productPrice,'all')
					info.image = defaultProduct.goodsPics[0]
					productId = defaultProduct.productId
				}
				info.url = getApp().globalData.apiUrl + `standard/product/detail?productId=${productId}&goodsId=${goodsId}&u=${weBtoa(this.userCenterData.memberId)}`
				return info
			}
		},
		
		methods: {
			//浏览器分享
			sldShareBrower(type) {
				this.showWeiXinBrowerTip = true
				this.share_model = false
				if (this.shareType != 'goodsDetail') {
					let goodsOringalLink = this.goodsData.shareLink ?
						this.goodsData.shareLink :
						getApp().globalData.apiUrl +
						'extra/tshou/goods/detail?productId=' +
						this.goodsData.productId

					let shareData = {
						title: this.goodsData.goodsName,
						desc: this.goodsData.goodsBrief,
						link: goodsOringalLink,
						imgUrl: this.goodsData.goodsImage ?
							this.goodsData.goodsImage :
							this.goodsData.productImage ?
							this.goodsData.productImage :
							this.goodsData.shareImage
					}
					WXBrowserShareThen(type, {
						...shareData
					})
				}
			},

			shareBrowserOn() {},

			//获取海报
			getPoster() {
				this.share_model = false
				this.showWeiXinBrowerTip = false
				this.poster = true
			},

			//关闭分享弹框
			closeShareModel() {
				this.share_model = false
				this.showWeiXinBrowerTip = false
				this.poster = false
			},
			
			getSunCode(data){
				this.$request({
					url: 'v3/goods/common/sunCode',
					data: {
						productId: data.defaultProduct ? data.defaultProduct.productId : data.productId,
						goodsId:data.goodsId,
						page:'standard/product/detail',
						u: weBtoa(this.userCenterData.memberId),
					}
				}).then(res => {
					this.posterInfo.qrcode = `data:image/png;base64,${res.data}`
				})
			},

			//分享 type：分享类型 0 图文 2图片，scene 场景 WXSceneSession：分享朋友  WXSenceTimeline：分享朋友圈
			sldShare: function(type, scene) {
				let shareData = {}
				let {
					goodsData,
					sharePoster
				} = this
				if (type == 0) {
					shareData.href = goodsData.shareLink
					shareData.title = goodsData.goodsName
					shareData.summary = goodsData.goodsBrief
					shareData.imageUrl = goodsData.shareImage ?
						goodsData.shareImage :
						goodsData.goodsImage
				} else if (type == 2) {
					shareData.imageUrl = this.posterInfo.cachePoster
				}
				this.$weiXinAppShare(type, scene, shareData)
				this.closeShareModel() //关闭分享
			},

			getLink(data) {
				this.$request({
					url: 'v3/spreader/front/spreaderGoods/share',
					data: {
						productId: data.defaultProduct ?
							data.defaultProduct.productId :
							data.productId
					}
				}).then((res) => {
					if (res.state == 200) {
						this.goodsData.shareLink = res.data.shareLink
						this.$emit('getLink', res.data.shareLink,this.goodsData)
					}
				})
			},
			
			downloadPoster() {
				this.$refs.sldPoster.savePoster()
			},
			
			//APP端保存图片的方法
			saveImgAPP() {
				this.$refs.sldPoster.savePoster()
			},

			//关闭海报
			closePoster() {
				this.poster = false
			},
			
			cachePoster(url){
				this.posterInfo.cachePoster = url
			},
			
			injectInfo(data,type){
				let _productId = data.defaultProduct?data.defaultProduct.productId:data.productId
				let curProductId = this.goodsData.defaultProduct?this.goodsData.defaultProduct.productId:this.goodsData.productId
				if(_productId!=curProductId&&(type == 'goods' || type == 'goodsDetail')){
					this.posterInfo.cachePoster = ''
					this.getLink(data)
					//wx-3-start
					// #ifdef MP-WEIXIN
					this.getSunCode(data)
					// #endif
					//wx-3-end
				}
				this.goodsData = data
				this.shareType = type
				
				this.share_model = true
				
			}
		}
	}
</script>

<style lang="scss">
	.share_main {
		z-index: 99999999;
	}

	.share_model1 {
		width: 752rpx;
		height: 100%;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		margin: 0 auto;
		background: rgba(0, 0, 0, 0.6);
		z-index: 99999999;
		padding-bottom: 60rpx;
	}

	.share_model_list {
		display: flex;
		justify-content: space-around;
		padding: 0 50rpx;
		box-sizing: border-box;
		margin-top: 20rpx;
		z-index: 110;
		width: 750rpx;

		.share_model_pre {
			display: flex;
			flex-direction: column;
			align-items: center;
			background: transparent;
			border-radius: 0;
			height: auto;
			line-height: auto;
			margin: 20rpx 0;

			&::after {
				border-width: 0;
			}

			image {
				width: 105rpx;
				height: 105rpx;
			}

			text {
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #ffffff;
				line-height: 36rpx;
				margin-top: 30rpx;
			}
		}
	}

	.share_model_close {
		width: 46rpx;
		height: 46rpx;
		z-index: 110;
		margin: 0 auto;

		image {
			width: 46rpx;
			height: 46rpx;
		}
	}

	button {
		padding: 0;
		margin: 0;
	}

	.poster {
		width: 752rpx;
		height: 100%;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		margin: 0 auto;
		background: rgba(0, 0, 0, 0.6);
		z-index: 998;
		padding-top: 50rpx;

		.poster_image {
			width: 541rpx;
			height: 837rpx;
			background: #ffffff;
			border-radius: 20rpx;
			position: absolute;
			left: 105rpx;
			/* #ifdef H5 */
			bottom: 360rpx;
			/* #endif */
			/* #ifndef H5 */
			bottom: 380rpx;
			/* #endif */
			/* wx-4-start */
			/* #ifdef MP-WEIXIN */
			bottom: 330rpx;
			/* #endif */
			/* wx-4-end */
		}

		.poster_share_img {
			width: 390rpx;
			height: 90rpx;
			margin: 72rpx 0 22rpx;
		}

		.poster_share_close {
			width: 49rpx;
			height: 49rpx;
		}

		.poster_share_model {
			width: 750rpx;
			height: 100%;
			position: fixed;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;

			.poster_image {
				width: 541rpx;
				height: 837rpx;
				background: #ffffff;
				border-radius: 20rpx;
			}

			.poster_share_img {
				width: 390rpx;
				height: 90rpx;
				margin: 72rpx 0 22rpx;
			}

			.poster_share_close {
				width: 49rpx;
				height: 49rpx;
			}
		}

	}
</style>
