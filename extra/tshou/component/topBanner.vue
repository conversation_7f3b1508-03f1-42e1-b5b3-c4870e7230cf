<template>
  <view
    class="top_banner"
  >
    <view class="middle_con">
      <!-- #ifndef MP -->
      <image
        :src="imgUrl + 'tshou/back_icon.png'"
        mode="aspectFit"
        @click="goBack"
      ></image>
      <!-- #endif -->
      <view class="search" @click="goSearch">
        <image :src="imgUrl + 'tshou/ts_search.png'" mode="aspectFit"></image>
        <text>{{ name ? name : $L('输入关键字搜索') }}</text>
      </view>
    </view>
    <view class="slot_botton">
      <slot></slot>
    </view>
  </view>
</template>

<script>
export default {
  props: ['name'],
  data() {
    return {
      imgUrl: getApp().globalData.imgUrl,
      keyword: ''
    }
  },
  methods: {
    goBack() {
      uni.navigateBack({})
    },
    goSearch() {
      this.$Router.push({
        path: '/extra/tshou/search/search',
        query: { keyword: this.name }
      })
    }
  }
}
</script>

<style lang="scss">
.top_banner {
  width: 100%;

  height: 170rpx;
  background-size: 100% 100%;
  background-position: center center;
  background-repeat: no-repeat;
  padding: 30rpx 20rpx 0;
  .middle_con {
    display: flex;
    justify-content: space-between;
    align-items: center;
    & > image {
      width: 26rpx;
      height: 46rpx;
    }
    .search {
      width: 650rpx;
      /* #ifdef MP */
      width: 100%;
      /* #endif */
      height: 60rpx;

      background: #f5f5f5;
      border-radius: 30rpx;
      display: flex;
      align-items: center;
      padding-left: 20rpx;
      padding-right: 20rpx;
      image {
        width: 32rpx;
        height: 32rpx;
      }
      text {
        display: block;
        width: 600rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-left: 10rpx;
        font-size: 30rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
      }
    }
  }
}
</style>
