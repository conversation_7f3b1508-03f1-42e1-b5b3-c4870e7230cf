<template>
  <view class="tabBar">
    <block v-for="(item, index) in tabBar.list" :key="index">
      <view
        class="tab-item"
        :data-url="idx == index ? '' : item.pagePath"
        @tap="reLaunch"
      >
       <image :src="idx!=index?item.iconPath:''" v-if="idx!=index"></image>
       <svgGroup v-if="idx==index" :type="item.selectedIconPath" width="40" height="40" px='rpx' :color="diyStyle_var['--color_extral_main']">
       </svgGroup>
        <text
          :style="
            'color: ' + (idx == index ? tabBar.selectedColor : tabBar.color)
          "
          >{{ item.text }}</text
        >
      </view>
    </block>
  </view>
</template>

<script>
export default {
  data() {
    return {
      tabBar: {
        // tabBar 配置
        color: '#333333',
        // 文字颜色
        selectedColor: 'var(--color_extral_main)',
        // 选中的颜色
        selectItem: 0,
        list: [
          {
            pagePath: '/extra/tshou/index/index',
            text: this.$L('推手首页'),
            iconPath: getApp().globalData.imgUrl + "tshou/ts_hone.png",
            selectedIconPath: 'ts_hone'
          },
          {
            pagePath: '/extra/tshou/mShop/mShop',
            text: this.$L('推手小店'),
            iconPath: getApp().globalData.imgUrl + "tshou/ts_myStore.png",
            selectedIconPath: 'ts_myStore'
          },
          {
            pagePath: '/extra/tshou/commission/commission',
            text: this.$L('佣金统计'),
            iconPath: getApp().globalData.imgUrl + "tshou/ts_commission.png",
            selectedIconPath: 'ts_commission'
          },
          {
            pagePath: '/extra/tshou/user/user',
            text: this.$L('个人中心'),
            iconPath: getApp().globalData.imgUrl + "tshou/ts_centre.png",
            selectedIconPath: 'ts_centre'
          }
        ]
      }
    }
  },
  props: ['idx'],
  mounted() {
    // #ifdef H5
    this.ifOpen()
    // #endif
  },
  methods: {
    reLaunch(e) {
      let url = e.currentTarget.dataset.url
      if (url) {
        this.$Router.replaceAll(url)
      }
    },
    // #ifdef H5
    ifOpen() {
      let param = {}
      param.data = {}
      param.data.names = 'spreader_is_enable'
      param.url = 'v3/system/front/setting/getSettings'
      param.method = 'GET'
      this.$request(param).then((res) => {
        if (res.state == 200) {
          if (res.data[0] != '1') {
            this.$Router.replace('/extra/tshou/index/index')
          }
        }
      })
    }
    // #endif
  }
}
</script>
<style>
/* component/DiyTabBar/DiyTabBar.wxss */
.tabBar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 750rpx;
  height: calc(90rpx + env(safe-area-inset-bottom));
  background-color: #fff;
  border-top: 1px solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-around;
  right: 0;
  margin: 0 auto;
  z-index: 997;
  padding-bottom: env(safe-area-inset-bottom);
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10rpx 0;
}

.tab-item image {
  width: 40rpx;
  height: 40rpx;
}

.tab-item text {
  margin-top: 8rpx;
  font-size: 20rpx;
}
</style>
