<template>
	<view class="carousel" id="nav1">
		<uni-swiper-dot :info="defaultProduct.goodsPics" :current="current" field="content" mode="nav">
			<swiper class="swiper-box" :current="current" @change="change">
				<!-- 默认规格 图  start-->
				<block v-if="defaultProduct && defaultProduct.goodsPics && defaultProduct.goodsPics.length > 0 && isChoice == 'default'">
					<swiper-item class="swiper-item" v-for="(item,index) in defaultProduct.goodsPics" :key="index">
						<view class="image-wrapper" @click="previewImg(index)">
							<image :src="item" class="loaded" mode="aspectFit"></image>
						</view>
					</swiper-item>
				</block>
				<!-- 默认规格 图  end-->
				<!-- 选择规格的图 start-->
				<block v-else-if="choiceSpecDes && choiceSpecDes.goodsPics && choiceSpecDes.goodsPics.length > 0 && isChoice == 'choice'">
					<swiper-item class="swiper-item" v-for="(item,index) in choiceSpecDes.goodsPics" :key="index">
						<view class="image-wrapper" @click="previewImg(index)">
							<image :src="item" class="loaded" mode="aspectFit"></image>
						</view>
					</swiper-item>
				</block>
				<!-- 选择规格的图 end-->
			</swiper>
		</uni-swiper-dot>
	</view>
</template>

<script>
	import uniSwiperDot from "@/components/uni-swiper-dot/uni-swiper-dot.vue";
	export default{
		props:['defaultProduct'],
		components:{
			uniSwiperDot
		},
		data(){
			return{
				current:0,
				isChoice: 'default',
				
			}
			
		},
		methods:{
			change(e) {
				this.current = e.detail.current;
				if (this.goodsVideo && e.detail.current == 0) {
					this.showControls = false
				} else {
					this.showControls = true
				}
			},
			//预览产品图片
			previewImg(current) {
				let _this = this;
				uni.previewImage({
					urls: this.defaultProduct.goodsPics,
					current,
					longPressActions: {
						success: function(data) {  
							uni.saveImageToPhotosAlbum({  
								filePath: urls[data.index],
								success: function() {
									_this.$api.msg('保存成功')
								},
								fail: function(err) {
									_this.$api.msg(err.errMsg)
								}
							});
						},
						fail: function(err) {
							_this.$api.msg(err)
						}
					}
				})
			},
		}
	}
</script>

<style lang="scss">
	.carousel {
		height: 750rpx;
		position: relative;
		.swiper-box {
			width: 750rpx;
			height: 750rpx;
			image {
				max-width: 100%;
				max-height: 100%;
			}
		}
	
		swiper {
			height: 100%;
		}
	
		.image-wrapper {
			width: 100%;
			height: 100%;
		}
	
		.swiper-item {
			display: flex;
			justify-content: center;
			align-content: center;
			height: 750rpx;
			overflow: hidden;
	
			image {
				max-width: 100%;
				max-height: 100%;
			}
		}
		/* 图片载入替代方案 */
		.image-wrapper {
			font-size: 0;
			background: #f3f3f3;
			border-radius: 4px;
		
			image {
				width: 100%;
				height: 100%;
				transition: .6s;
				opacity: 0;
		
				&.loaded {
					opacity: 1;
				}
			}
		}
	
	}
</style>
