<template>
	<view :style="mix_diyStyle">
  <view class="main_mshop">
    <view class="mshop_top">
      <view class="m_info">
        <view class="m_info_left">
          <view class="m_avatar">
            <image :src="mShop.memberAvatar" mode="aspectFit"></image>
          </view>
          <view class="m_info_text">
            <view class="m_text_top">
              <view>{{ mShop.memberName }}</view>
            </view>
            <view class="m_text_bottom"
              >{{ $L('店内商品') }}：{{ mShop.goodsNumber }}</view
            >
          </view>
        </view>
      </view>
      <view class="m_nav">
        <view :class="{ m_nav_item: true, sel: navIdx == 1 }" @click="cNav(1)">
          <text>{{ $L('店内商品') }}</text>
          <view :class="{ bot_line: navIdx == 1 }"></view>
        </view>
      </view>
      <view class="m_blank"></view>
    </view>
    <view class="m_shop_list">
      <scroll-view
        scroll-y="true"
        v-if="shopGoods.length"
        @scrolltolower="loadData"
      >
        <block v-for="(item, index) in shopGoods" :key="index">
          <view class="m_shop_item">
            <goodsItem
              :goods="item"
              :spreaderMemberId="spreaderMemberId"
              :isOwner="mShop.isSelf"
            >
            </goodsItem>
          </view>
        </block>
        <loadingState :state="loadState"></loadingState>
      </scroll-view>
      <tsEmpty
        img="tshou/tsm_empty.png"
        width="202"
        height="350"
        v-else
      ></tsEmpty>
    </view>
  </view>
	</view>
</template>

<script>
import filters from '@/utils/filter.js'
import goodsItem from '../component/goodsItem.vue'
import tsEmpty from '../component/tsEmpty.vue'
import loadingState from '@/components/loading-state.vue'
export default {
  components: {
    goodsItem,
    tsEmpty,
    loadingState
  },
  data() {
    return {
      imgUrl: getApp().globalData.imgUrl,
      navIdx: 1,
      is_show_btn: false,
      startX: 0,
      startY: 0,
      id: 0,
      mShop: {},
      shopGoods: [],
      loading: true,
      loadState: '',
      hasmore: true,
      pn: 1,
      share_model: false,
      isWeiXinBrower: false,
      showWeiXinBrowerTip: false,
      source: 0,
      goodsData: {},
      sharePoster: '',
      shareType: '',
      spreaderMemberId: 0,
      filters
    }
  },
  onLoad(options) {
    if (this.$Route.query.scene) {
      let url = decodeURIComponent(decodeURIComponent(this.$Route.query.scene))
      this.spreaderMemberId = url
    } else {
      this.spreaderMemberId = this.$Route.query.u
    }

    this.isWeiXinBrower = this.$isWeiXinBrower()
	//app-1-start
    //#ifdef APP-PLUS
    this.source = 2
    //#endif
	//app-1-end
    //#ifdef H5
    this.source = this.isWeiXinBrower ? 3 : 2
    //#endif
	//wx-1-start
    //#ifdef MP-WEIXIN
    this.source = 4
    //#endif
	//wx-1-end
    this.getMshop()
  },

  onReachBottom() {
    if (this.hasmore) {
      this.getShopGood()
    }
  },

  methods: {
    loadData() {
      if (this.hasmore) {
        this.getShopGood()
      }
    },

    getMshop() {
      this.$request({
        url: 'v3/spreader/front/spreaderStore/store',
        data: {
          spreaderMemberId: this.spreaderMemberId
        }
      })
        .then((res) => {
          if (res.state == 200) {
            this.mShop = res.data
          }
        })
        .then(() => {
          this.getShopGood()
        })
    },

    getShopGood() {
      let param = {
        url: 'v3/spreader/front/spreaderStore/list',
        data: {
          current: this.pn,
          queryType: this.navIdx,
          spreaderMemberId: this.spreaderMemberId
        }
      }
      this.$request(param).then((res) => {
        if (res.state == 200) {
          if (this.pn == 1) {
            this.shopGoods = res.data.list
          } else {
            this.shopGoods = this.shopGoods.concat(res.data.list)
          }

          if (this.$checkPaginationHasMore(res.data.pagination)) {
            this.pn++
            this.hasmore = true
            this.loadState = 'allow_loading_more'
          } else {
            this.hasmore = false
            this.loadState = 'no_more_data'
          }
        } else {
          this.$api.msg(res.msg)
        }
      })
    }
  }
}
</script>

<style lang="scss">
page {
  background: #f5f5f5;
}

.mshop_top {
  position: fixed;
  top: 0;
  width: 100%;
  background: linear-gradient(360deg, #f98d30 00%, #f65826 90%);
  padding-top: 52rpx;
  //app-2-start
  /* #ifdef APP-PLUS */
  padding-top: calc(var(--status-bar-height) + 52rpx);
  /* #endif */
  //app-2-end
  z-index: 9;

  .m_info {
    display: flex;
    padding: 0 35rpx;
    justify-content: space-between;
    align-items: center;

    .m_info_left {
      display: flex;
      align-items: center;

      .m_avatar {
        width: 130rpx;
        height: 130rpx;
        background: #c0bbcf;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        image {
          display: block;
          width: 120rpx;
          height: 120rpx;
          border-radius: 50%;
          background: #fff;
        }
      }

      .m_info_text {
        margin-left: 30rpx;
        color: #fff;

        .m_text_top {
          display: flex;
          font-size: 1em;
          font-family: PingFangSC-Regular, PingFang SC;
          align-items: center;

          image {
            margin-left: 10rpx;
            width: 38rpx;
            height: 38rpx;
          }
        }

        .m_text_bottom {
          margin-top: 10rpx;
          font-size: 26rpx;
        }
      }
    }

    .m_info_right {
      width: 144rpx;
      height: 42rpx;
      background: #ffffff;
      border-radius: 20rpx;
      text-align: center;
      line-height: 42rpx;
      font-size: 24rpx;
      color: $ts-font-color;
    }
  }

  .m_nav {
    margin-top: 32rpx;
    display: flex;
    justify-content: center;

    .m_nav_item {
      font-size: 32rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #ffffff;
      display: flex;
      flex-direction: column;
      align-items: center;

      &.sel {
        font-weight: 600;
      }

      .bot_line {
        margin-top: 20rpx;
        width: 90%;
        height: 8rpx;
        border-radius: 10rpx;
        background-color: #fff;
      }
    }
  }

  .m_blank {
    margin-top: 16rpx;
    height: 60rpx;
    background-color: #fff;
    border-radius: 40rpx 40rpx 0 0;
  }
}

.m_shop_list {
  position: relative;
  top: 340rpx;
  //app-3-start
  /* #ifdef APP-PLUS*/
  top: calc(var(--status-bar-height) + 340rpx);
  /* #endif */
  //app-3-end
  padding-bottom: 90rpx;

  .m_shop_item {
    position: relative;
    transition: all 0.3s;

    .goods_item {
      border-bottom: 2rpx solid #f7f7f7;
      border-top: none;
    }
  }
}

.slot_button {
  background: linear-gradient(270deg, #f04047 0%, #fe7951 100%);
  border-radius: 15px;
  padding: 10rpx 28rpx;
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #ffffff;
  line-height: 40rpx;
  display: flex;
  align-items: center;

  image {
    width: 32rpx;
    height: 32rpx;
    margin-right: 6rpx;
  }

  text {
    font-size: 32rpx;

    &:first-of-type {
      font-size: 14rpx;
      margin-right: 6rpx;
    }
  }
}

.checkbox {
  width: 42rpx;

  image {
    width: 32rpx;
    height: 32rpx;
  }
}

.slot_commission {
  font-size: 22rpx;
  color: #999999;
}

.operate_wrap {
  position: absolute;
  width: 160rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  right: -160rpx;
  top: 20rpx;
  height: 100%;

  image {
    width: 88rpx;
    height: 88rpx;
    transition: all 0.3s;
  }
}

.wx_brower_share_mask {
  width: 750rpx;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.45);
  position: absolute;
  z-index: 99999;
  top: 0;
}

.wx_brower_share_top_wrap {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  margin-top: 150rpx;
}

.wx_brower_share_top_wrap .wx_brower_share_img {
  width: 450rpx;
  height: 150rpx;
  margin-right: 80rpx;
}

.share_h5 {
  width: 100% !important;
  height: 100% !important;
}

.share_model {
  width: 750rpx;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  background: rgba(0, 0, 0, 0.6);
  z-index: 100;
}

.share_model_list {
  display: flex;
  justify-content: space-around;
  padding: 0 50rpx;
  box-sizing: border-box;
  position: fixed;
  bottom: 150rpx;
  z-index: 110;
  width: 750rpx;

  .share_model_pre {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: transparent;
    border-radius: 0;
    height: auto;
    line-height: auto;

    &::after {
      border-width: 0;
    }

    image {
      width: 105rpx;
      height: 105rpx;
    }

    text {
      font-size: 24rpx;
      font-family: PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 36rpx;
      margin-top: 30rpx;
    }
  }
}

.share_model_close {
  width: 46rpx;
  height: 46rpx;
  bottom: 60rpx;
  position: fixed;
  z-index: 110;
  left: 0;
  right: 0;
  margin: 0 auto;

  image {
    width: 46rpx;
    height: 46rpx;
  }
}

button {
  padding: 0;
  margin: 0;
}
</style>
