<template>
	<view :style="mix_diyStyle">
		<view>
			<view class="main_mshop" v-if="authoState">
				<view class="mshop_top_fixed"></view>
				<view class="mshop_top">
					<view class="m_info">
						<view class="m_info_left">
							<view class="m_avatar">
								<image :src="mShop.memberAvatar" mode="aspectFit" v-if="mShop.memberAvatar"></image>
								<image :src="userCenterData.memberAvatar" mode="aspectFit" v-else></image>
							</view>
							<view class="m_info_text">
								<view class="m_text_top">
									<view>
										{{
                    userCenterData.memberNickName
                      ? userCenterData.memberNickName + $L('的小店')
                      : mShop.memberName
                  }}
									</view>
									<!-- #ifdef H5 -->
									<block v-if="isWeiXinBrower">
										<image :src="imgUrl + 'tshou/share_icon2.png'" mode="aspectFit"
											@click="shareShop"></image>
									</block>
									<!-- #endif -->
									<!-- #ifdef APP-PLUS||MP-WEIXIN -->
									<image :src="imgUrl + 'tshou/share_icon2.png'" mode="aspectFit" @click="shareShop">
									</image>
									<!-- #endif -->
								</view>
								<view class="m_text_bottom">{{ $L('店内商品') }}：{{ mShop.goodsNumber }}</view>
							</view>
						</view>
						<view class="m_info_right" @click="navTo('/extra/tshou/goods/list', { type: 'aTo' })">
							+{{ $L('添加商品') }}
						</view>
					</view>
					<view class="m_nav">
						<view :class="{ m_nav_item: true, sel: navIdx == 1 }" @click="cNav(1)">
							<text>{{ $L('店内商品') }}</text>
							<view :class="{ bot_line: navIdx == 1 }"></view>
						</view>
						<view :class="{ m_nav_item: true, sel: navIdx == 2 }" @click="cNav(2)">
							<text>{{ $L('推广商品排行') }}</text>
							<view :class="{ bot_line: navIdx == 2 }"></view>
						</view>
					</view>
					<view class="m_blank"></view>
				</view>
				<view class="m_shop_list">
					<scroll-view scroll-y="true" v-if="shopGoods.length">
						<block v-for="(item, index) in shopGoods" :key="index">
							<view @touchstart="handleTouchStart($event, index)"
								@touchmove="handleTouchMove($event, index)" :data-id="index">
								<view class="m_shop_item" :style="{
                  left: is_show_btn && followId == index ? '-160rpx' : '0'
                }">
									<goodsItem :goods="item" :isOwner="true" @upStateShow="upStateShow">
										<view slot="button">
											<view class="slot_button" @click.stop="goShare(item)">
												<image :src="imgUrl + 'tshou/share_icon.png'" mode="aspectFit">
												</image>
												<text>{{ $L('赚') }}{{ ' ' }}{{ $L('¥') }}</text>
												<text>{{ filters.toFix(item.commission) }}</text>
											</view>
											<view class="slot_commission" slot="commission" v-if="item.commissionSum">
												<text>{{ $L('已得佣金') }}：¥{{ item.commissionSum }}</text>
											</view>
										</view>
									</goodsItem>
									<view class="operate_wrap">
										<view class="" @click.stop="delGoods(item.productId, false)">
											<!-- <image
                      :src="imgUrl + 'tshou/tsm_del.png'"
                      mode="aspectFit"
                    ></image> -->
											<view class="operate_wrap_del">
												<text class="iconfont iconshanchu-copy-copy-copy"></text>
											</view>
										</view>
									</view>
								</view>
							</view>
						</block>
						<loadingState :state="loadState"></loadingState>
					</scroll-view>
					<tsEmpty img="tshou/tsm_empty.png" width="202" height="350" v-else></tsEmpty>
				</view>

				<tsShare ref="tsShare" @getLink="getLink"></tsShare>
			</view>
			<view v-else>
				<noAuthorized></noAuthorized>
			</view>
			<DiyTabBar idx="1"></DiyTabBar>
		</view>
	</view>
</template>
<script>
	import filters from '@/utils/filter.js'
	import goodsItem from '../component/goodsItem.vue'
	import tsEmpty from '../component/tsEmpty.vue'
	import tsShare from '../component/tsShare.vue'
	import noAuthorized from '../component/noAuthorized.vue'
	import DiyTabBar from '../component/DiyTabBar.vue'
	import loadingState from '@/components/loading-state.vue'
	import {
		mapState
	} from 'vuex'
	export default {
		components: {
			goodsItem,
			tsEmpty,
			tsShare,
			noAuthorized,
			DiyTabBar,
			loadingState
		},
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				navIdx: 1,
				is_show_btn: false,
				startX: 0,
				startY: 0,
				id: 0,
				mShop: {},
				shopGoods: [],
				loading: true,
				loadState: '',
				hasmore: true,
				pn: 1,
				share_model: false,
				isWeiXinBrower: false,
				showWeiXinBrowerTip: false,
				source: 0,
				goodsData: {},
				sharePoster: '',
				shareType: '',
				authoState: true,
				openTypeShareLInk: '',
				followId: 0,
				filters,
				showState: false
			}
		},
		onLoad() {
			setTimeout(() => {
				uni.setNavigationBarTitle({
					title: this.$L('我的小店')
				})
			}, 0);

			if (!this.hasLogin) {
				let urls = this.$Route.path
				const query = this.$Route.query
				uni.setStorageSync('fromurl', {
					url: urls,
					query
				})
				this.$Router.push('/pages/public/login')
			}

			this.isWeiXinBrower = this.$isWeiXinBrower()
			this.getMshop()
			//app-1-start
			//#ifdef APP-PLUS
			this.source = 2
			//#endif
			//app-1-end
			//#ifdef H5
			this.source = this.isWeiXinBrower ? 3 : 2
			//#endif
			//wx-1-start
			//#ifdef MP-WEIXIN
			this.source = 4
			//#endif
			//wx-1-end
		},
		computed: {
			...mapState(['hasLogin', 'userCenterData'])
		},
		onShow() {
			if (this.showState) {
				this.pn = 1
				this.getMshop()
			}
		},
		onHide() {
			this.$refs.tsShare.closeShareModel()
		},
		onReachBottom() {
			if (this.hasmore) {
				this.getShopGood()
			}
		},
		onShareAppMessage(res) {
			return {
				title: this.goodsData.goodsName,
				desc: this.goodsData.goodsBrief,
				path: this.openTypeShareLInk ?
					this.openTypeShareLInk :
					'/extra/tshou/mShop/shareShop?' + this.mShop.shareLink.split('?')[1],
				imageUrl: this.goodsData.goodsImage ?
					this.goodsData.goodsImage :
					this.goodsData.productImage,
				success: (res) => {}
			}
		},

		methods: {
			upStateShow() {
				this.showState = true
			},

			getMshop() {
				this.$request({
						url: 'v3/spreader/front/spreaderStore/store'
					})
					.then((res) => {
						if (res.state == 200) {
							this.mShop = res.data
						} else if (res.state == 269) {
							this.authoState = false
							this.$api.msg(res.msg)
						}
					})
					.then(() => {
						this.getShopGood()
					})
			},
			goShare(item) {
				this.goodsData = item
				this.shareType = 'goods'
				this.$refs.tsShare.injectInfo(item, this.shareType)
			},
			shareShop() {
				this.goodsData = {}
				this.goodsData = {
					goodsName: this.userCenterData.memberNickName ?
						this.userCenterData.memberNickName + this.$L('的小店') :
						this.mShop.memberName,
					goodsBrief: this.$L('你想要的都在这里,快来我的店里看看吧!'),
					shareLink: this.mShop.shareLink,
					goodsImage: this.mShop.memberAvatar
				}
				this.shareType = 'shop'
				this.$refs.tsShare.injectInfo(this.goodsData, this.shareType)
			},

			getLink(link) {
				this.openTypeShareLInk = '/standard/product/detail?' + link.split('?')[1]
			},

			cNav(idx) {
				this.navIdx = idx
				this.pn = 1
				this.getMshop()
			},
			getShopGood() {
				let param = {
					url: 'v3/spreader/front/spreaderStore/list',
					data: {
						current: this.pn,
						queryType: this.navIdx
					}
				}
				this.$request(param).then((res) => {
					if (res.state == 200) {
						if (this.pn == 1) {
							this.shopGoods = res.data.list
						} else {
							this.shopGoods = this.shopGoods.concat(res.data.list)
						}

						if (this.$checkPaginationHasMore(res.data.pagination)) {
							this.pn++
							this.hasmore = true
							this.loadState = 'allow_loading_more'
						} else {
							this.hasmore = false
							this.loadState = 'no_more_data'
						}
					} else {
						this.$api.msg(res.msg)
					}
				})
			},

			delGoods(productIds, isCollect) {
				this.$request({
					url: 'v3/spreader/front/spreaderStore/edit',
					method: 'POST',
					data: {
						productIds,
						isCollect
					}
				}).then((res) => {
					if (res.state == 200) {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 1000
						})
						this.mShop.goodsNumber--
						this.shopGoods.splice(this.followId, 1)
					}
					this.followId = -1
				})
			},

			//关闭分享弹框
			closeShareModel() {
				this.share_model = false
				this.showWeiXinBrowerTip = false
			},

			handleTouchStart(e, idx) {
				if (this.navIdx == 2) {
					return
				}

				this.startX = e.touches[0].clientX
				this.startY = e.touches[0].clientY
			},
			handleTouchMove(e, idx) {
				if (this.navIdx == 2) {
					return
				}

				// 获得当前坐标
				this.followId = idx
				this.currentX = e.touches[0].clientX
				this.currentY = e.touches[0].clientY
				let x = this.startX - this.currentX //横向移动距离
				let y = Math.abs(this.startY - this.currentY) //纵向移动距离，若向左移动有点倾斜也可以接受
				if (y > 1) {
					e.preventDefault()
				}

				if (x > 5 && this.startX > 30) {
					//向左滑显示
					e.preventDefault()
					this.is_show_btn = true
				} else if (x < 5) {
					//向右滑隐藏
					e.preventDefault()
					this.is_show_btn = false
				}
			},
			navTo(url, payload) {
				this.showState = true
				this.$Router.push({
					path: url,
					query: {
						...payload
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	page {
		background: #f5f5f5;
	}

	.mshop_top_fixed {
		position: fixed;
		top: 0;
		height: 340rpx;
		//app-2-start
		/* #ifdef APP-PLUS*/
		height: calc(var(--status-bar-height) + 340rpx);
		/* #endif */
		//app-2-end
		//wx-2-start
		/* #ifdef MP-WEIXIN*/
		height: calc(var(--status-bar-height) + 300rpx);
		/* #endif */
		//wx-2-end
		width: 100%;
		background: #fff;
		padding-top: 52rpx;
		//app-3-start
		/* #ifdef APP-PLUS */
		padding-top: calc(var(--status-bar-height) + 52rpx);
		/* #endif */
		//app-3-end
		z-index: 8;
	}

	.mshop_top {
		position: fixed;
		top: 0;
		width: 100%;
		background: var(--color_extral_main_bgZero);
		padding-top: 52rpx;
		//app-4-start
		/* #ifdef APP-PLUS */
		padding-top: calc(var(--status-bar-height) + 52rpx);
		/* #endif */
		//app-4-end
		z-index: 9;

		.m_info {
			display: flex;
			padding: 0 35rpx;
			justify-content: space-between;
			align-items: center;

			.m_info_left {
				display: flex;
				align-items: center;

				.m_avatar {
					width: 130rpx;
					height: 130rpx;
					background: #c0bbcf;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;

					image {
						display: block;
						width: 120rpx;
						height: 120rpx;
						border-radius: 50%;
						background: #fff;
					}
				}

				.m_info_text {
					margin-left: 30rpx;
					color: #fff;

					.m_text_top {
						display: flex;
						font-size: 1em;
						font-family: PingFangSC-Regular, PingFang SC;
						align-items: center;

						view {
							max-width: 300rpx;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
						}

						image {
							margin-left: 10rpx;
							width: 38rpx;
							height: 38rpx;
						}
					}

					.m_text_bottom {
						margin-top: 10rpx;
						font-size: 26rpx;
					}
				}
			}

			.m_info_right {
				width: 144rpx;
				height: 42rpx;
				background: #ffffff;
				border-radius: 20rpx;
				text-align: center;
				line-height: 42rpx;
				font-size: 24rpx;
				color: var(--color_extral_vice);
			}
		}

		.m_nav {
			margin-top: 32rpx;
			display: flex;
			//app-5-start
			/* #ifdef APP-PLUS */
			justify-content: space-between;

			&:before,
			&:after {
				content: '';
				display: block;
			}
			/* #endif */
			//app-5-end
			/* #ifndef APP-PLUS */
			justify-content: space-evenly;
			/* #endif */
			.m_nav_item {
				font-size: 32rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #ffffff;
				display: flex;
				flex-direction: column;
				align-items: center;

				&.sel {
					font-weight: 600;
				}

				.bot_line {
					margin-top: 20rpx;
					width: 90%;
					height: 8rpx;
					border-radius: 10rpx;
					background-color: #fff;
				}
			}
		}

		.m_blank {
			margin-top: 16rpx;
			height: 60rpx;
			background-color: #fff;
			border-radius: 40rpx 40rpx 0 0;
		}
	}

	.m_shop_list {
		position: relative;
		top: 340rpx;
		//app-6-start
		/* #ifdef APP-PLUS*/
		top: calc(var(--status-bar-height) + 340rpx);
		/* #endif */
		//app-6-end
		//wx-3-start
		/* #ifdef MP-WEIXIN*/
		top: calc(var(--status-bar-height) + 300rpx);
		/* #endif */
		//wx-3-end
		padding-bottom: 90rpx;
		height: 100%;

		.m_shop_item {
			position: relative;
			transition: all 0.3s;

			.goods_item {
				border-bottom: 2rpx solid #f7f7f7;
				border-top: none;
			}
		}
	}

	.slot_button {
		background: var(--color_extral_main_bg);
		border-radius: 15px;
		padding: 10rpx 28rpx;
		font-size: 28rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #ffffff;
		line-height: 40rpx;
		display: flex;
		align-items: center;

		image {
			width: 32rpx;
			height: 32rpx;
			margin-right: 6rpx;
		}

		text {
			font-size: 32rpx;

			&:first-of-type {
				font-size: 24rpx;
				margin-right: 6rpx;
			}
		}
	}

	.checkbox {
		width: 42rpx;

		image {
			width: 32rpx;
			height: 32rpx;
		}
	}

	.slot_commission {
		font-size: 22rpx;
		color: #999999;
	}

	.operate_wrap {
		position: absolute;
		width: 160rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		right: -160rpx;
		top: 20rpx;
		height: 100%;

		.operate_wrap_del {
			width: 88rpx;
			height: 88rpx;
			transition: all 0.3s;
			border-radius: 50%;
			background: var(--color_extral_main);
			display: flex;
			align-items: center;
			justify-content: center;

			text {
				font-size: 40rpx;
				color: #fff;
			}
		}

		// image {
		//   width: 88rpx;
		//   height: 88rpx;
		//   transition: all 0.3s;
		// }
	}

	.wx_brower_share_mask {
		width: 750rpx;
		height: 100vh;
		background-color: rgba(0, 0, 0, 0.45);
		position: absolute;
		z-index: 99999;
		top: 0;
	}

	.wx_brower_share_top_wrap {
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: flex-end;
		align-items: flex-start;
		margin-top: 150rpx;
	}

	.wx_brower_share_top_wrap .wx_brower_share_img {
		width: 450rpx;
		height: 150rpx;
		margin-right: 80rpx;
	}

	.share_h5 {
		width: 100% !important;
		height: 100% !important;
	}

	.share_model {
		width: 750rpx;
		height: 100%;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		margin: 0 auto;
		background: rgba(0, 0, 0, 0.6);
		z-index: 100;
	}

	.share_model_list {
		display: flex;
		justify-content: space-around;
		padding: 0 50rpx;
		box-sizing: border-box;
		position: fixed;
		bottom: 150rpx;
		z-index: 110;
		width: 750rpx;

		.share_model_pre {
			display: flex;
			flex-direction: column;
			align-items: center;
			background: transparent;
			border-radius: 0;
			height: auto;
			line-height: auto;

			&::after {
				border-width: 0;
			}

			image {
				width: 105rpx;
				height: 105rpx;
			}

			text {
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #ffffff;
				line-height: 36rpx;
				margin-top: 30rpx;
			}
		}
	}

	.share_model_close {
		width: 46rpx;
		height: 46rpx;
		bottom: 60rpx;
		position: fixed;
		z-index: 110;
		left: 0;
		right: 0;
		margin: 0 auto;

		image {
			width: 46rpx;
			height: 46rpx;
		}
	}

	button {
		padding: 0;
		margin: 0;
	}
</style>