<template>
	<view :style="mix_diyStyle">
  <view class="main_order">
    <view class="main_o_top">
      <!-- #ifndef H5 -->
      <topHeader :title="$L('推手订单')"></topHeader>
      <!-- #endif -->
      <view class="nav">
        <view class="nav_item" @click="changeNav(0)">
          <view :class="{ nav_item_t: true, on: current == 0 }">
            <text>{{ $L('全部') }}</text>
          </view>
          <view :class="{ nav_item_b: current == 0 }"></view>
        </view>
        <view class="nav_item" @click="changeNav(1)">
          <view :class="{ nav_item_t: true, on: current == 1 }">
            <text>{{ $L('冻结') }}</text>
          </view>
          <view :class="{ nav_item_b: current == 1 }"></view>
        </view>
        <view class="nav_item" @click="changeNav(2)">
          <view :class="{ nav_item_t: true, on: current == 2 }">
            <text>{{ $L('失效') }}</text>
          </view>
          <view :class="{ nav_item_b: current == 2 }"></view>
        </view>
        <view class="nav_item" @click="changeNav(3)">
          <view :class="{ nav_item_t: true, on: current == 3 }">
            <text>{{ $L('结算') }}</text>
          </view>
          <view :class="{ nav_item_b: current == 3 }"></view>
        </view>
      </view>
    </view>
    <view class="main_o_bottom">
      <scroll-view
        scroll-y="true"
        class="order_i"
        @scrolltolower="load"
        v-if="orderList.length"
      >
        <view>
          <view v-for="(item, index) in orderList" :key="index">
            <orderItem :order="item"></orderItem>
          </view>
        </view>
        <loadingState :state="loadState"></loadingState>
      </scroll-view>
      <tsEmpty
        :text="$L('暂无订单')"
        img="store/no_content.png"
        width="200"
        height="200"
        v-else
      ></tsEmpty>
    </view>
  </view>
	</view>
</template>

<script>
import loadingState from '@/components/loading-state.vue'
import topHeader from '../component/topheader.vue'
import orderItem from '../component/orderItem.vue'
import tsEmpty from '../component/tsEmpty.vue'
import { mapState } from 'vuex'
export default {
  components: {
    topHeader,
    orderItem,
    tsEmpty,
    loadingState
  },
  data() {
    return {
      imgUrl: getApp().globalData.imgUrl,
      current: 0,
      pn: 1,
      comiState: 0,
      orderList: [],
      loading: true,
      loadState: '',
      hasmore: true
    }
  },
  onLoad() {
    if (!this.hasLogin) {
      let urls = this.$Route.path
      const query = this.$Route.query
      uni.setStorageSync('fromurl', {
        url: urls,
        query
      })
      this.$Router.push('/pages/public/login')
    }
    this.getOrder()
  },
  computed: {
    ...mapState(['hasLogin'])
  },
  methods: {
    changeNav(idx) {
      this.current = idx
      this.comiState = idx
      this.pn = 1
      this.getOrder()
    },
    load() {
      if (this.hasmore) {
        this.loadState = 'loading'
        this.getOrder()
      }
    },
    getOrder() {
      let param = {
        url: 'v3/spreader/front/spreaderOrderInfo/list',
        data: {
          current: this.pn
        }
      }
      if (this.comiState) {
        param.data.commissionState = this.comiState
      }
      this.$request(param).then((res) => {
        if (res.state == 200) {
          if (this.pn == 1) {
            this.orderList = res.data.spreaderOrderInfoVOList
          } else {
            this.orderList = this.orderList.concat(
              res.data.spreaderOrderInfoVOList
            )
          }

          // if (this.$checkPaginationHasMore(res.data.pagination)) {
          // 	this.pn++
          // 	this.hasmore = true
          // 	this.loadState = 'allow_loading_more'
          // } else {
          // 	this.hasmore = false

          // }
          this.loadState = 'no_more_data'
        } else {
          this.$api.msg(res.msg)
        }
      })
    }
  }
}
</script>

<style lang="scss">
page {
  background: #f7f7f7;
}
.main_order {
  z-index: 0;
  background: #f7f7f7;
  background-repeat: no-repeat;
  position: relative;
  .main_o_top {
    position: fixed;
    top: 0;
    background:var(--color_extral_main_bgZero);
    // padding-top: 44rpx;
    /* #ifdef APP-PLUS||MP */
    padding-top: calc(var(--status-bar-height) + 36rpx);
    /* #ifdef MP-ALIPAY */
    padding-top: 36rpx;
    /* #endif */
    /* #endif */
    z-index: 9999;
  }
  .main_o_bottom {
    position: relative;
    top: 170rpx;
    /* #ifdef H5 */
    top: 80rpx;
    /* #endif */
    z-index: 0;
	//app-1-start
    /* #ifdef APP-PLUS */
    top: calc(var(--status-bar-height) + 170rpx);
    /* #endif */
	//app-1-end
  }
  .nav {
    display: flex;
    justify-content: space-around;
    font-size: 30rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    margin-top: 20rpx;
    margin-bottom: 10rpx;
    /* #ifdef H5 */
    width: 750rpx;
    /* #endif */
    .nav_item {
      display: flex;
      flex-direction: column;
      align-items: center;
      .on {
        font-weight: bold;
      }
      .nav_item_t {
        display: flex;
        align-items: center;
        color: #fff;
        image {
          margin-left: 4rpx;
          width: 24rpx;
          height: 24rpx;
        }
      }
      .nav_item_b {
        margin-top: 12rpx;
        height: 6rpx;
        background-color: #fff;
        border-radius: 6rpx;
        width: 70%;
      }
    }
  }
  .order_i {
    position: relative;
    padding: 30rpx 20rpx;
  }
}
</style>
