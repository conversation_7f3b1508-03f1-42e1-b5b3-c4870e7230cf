<template>
    <view class="container" :style="{ backgroundImage: `url(${imgUrl + 'default_bg.jpg'})` }">
        <KTabbar :title="$L('话题')" :showLeft="true" :bgColor="'transparent'" />
        <view v-if="loading" class="loading-wrap">
            <text>加载中...</text>
        </view>
        <view v-else-if="label_list.length === 0" class="empty-wrap">
            <text>暂无话题</text>
        </view>
        <view v-else class="topic-list">
            <view v-for="(item, index) in label_list" :key="index" class="topic-item" @click="selectTopic(item)" :style="{ backgroundImage: `url(${item.image})` }">
                <view class="filter_bg">
                    <view class="card-row topic-name">
                        <text>{{ '#' + item.themeName + '#' }}</text>
                        <text class="topic-lable" :style="{ backgroundColor: item.labelColor }">{{ item.labelName }}</text>
                    </view>
                    <view class="card-row video-num">
                        <text class="num">{{ '参与次数 ' + item.videoNum }}</text>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>
<script>
import KTabbar from '@/components/ktabbar.vue';
export default {
    components: {
        KTabbar
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            label_list: [], // 标签列表
            action: '', // 选择话题的动作
            loading: false,
            selectedTopic: null
        };
    },
    onLoad() {
        this.getLableList();
        const { action } = this.$Route.query;
        this.action = action ? action : 'view'; // 默认查看模式
    },
    onPullDownRefresh() {
        this.getLableList(true);
    },
    methods: {
        // 选择话题
        selectTopic(topic) {
            this.selectedTopic = topic;
            // 如果是选择话题的动作
            if (this.action === 'select') {
                // 把选择的话题通过获取页面栈并设置
                const pages = getCurrentPages();
                const prevPage = pages[pages.length - 2];
                if (prevPage && prevPage.$vm.setTopic) {
                    prevPage.$vm.setTopic(topic);
                    this.$Router.back();
                }
            }
        },
        // 获取标签列表
        getLableList() {
            this.loading = true;
            this.error = '';
            let param = {};
            param.url = 'v3/video/front/video/themeList';
            param.method = 'GET';
            this.$request(param)
                .then((res) => {
                    this.loading = false;
                    if (res.state == 200) {
                        this.label_list = res.data.list || [];
                    }
                })
                .catch((err) => {
                    this.loading = false;
                });
        }
    }
};
</script>
<style lang="scss" scoped>
.container {
    min-height: 100vh;
    background: $bg1;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}

.topic-list {
    width: 100%;
    padding: 20rpx 4%;
    display: flex;
    flex-wrap: wrap;
    gap: 24rpx;
    justify-content: flex-start;
}

.topic-item {
    width: 100%;
    background: #fff;
    border-radius: 20rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative;
    .filter_bg {
        width: 100%;
        padding: 24rpx 40rpx;
        // 毛玻璃效果
        backdrop-filter: blur(10rpx);
    }
    .card-row {
        width: 100%;
        row-gap: 20rpx;
    }
    .video-num {
        text-align: right;
        .num {
            color: #fff;
            font-size: $fs-s;
        }
    }

    .topic-name {
        min-height: 120rpx;
        font-size: 32rpx;
        color: #fff;
        font-weight: 500;
        .topic-lable {
            margin-left: 20rpx;
            display: inline-block;
            top: 0rpx;
            right: 0rpx;
            padding: 4rpx 12rpx;
            border-radius: 12rpx;
            font-size: $fs-m;
            background-color: #e74c3c;
            color: #fff;
            box-sizing: border-box;
        }
    }
}

.loading-wrap,
.empty-wrap,
.error-wrap {
    width: 100%;
    text-align: center;
    color: #999;
    font-size: $fs-m;
    margin-top: 120rpx;
}

.error-wrap {
    color: #e74c3c;
}
</style>
