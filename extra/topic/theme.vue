<template>
    <view class="container" :style="{ backgroundImage: `url(${imgUrl + 'default_bg.jpg'})` }">
        <!-- 自定义顶部导航栏 -->
        <view class="fixed_top_status_bar">
            <view class="custom-nav-taber" :style="{ paddingTop: topBar.offsettop + 'px' }">
                <view class="nav-left bg" @click="$Router.back()">
                    <uv-icon class="back_icon" name="arrow-left" color="#fff" size="20"></uv-icon>
                </view>
            </view>
        </view>
        <view class="theme_header" :style="{ backgroundImage: `url(${themeDetail.image})` }">
            <view class="topic_name">
                <text>{{ '#' + themeDetail.themeName + '#' }}</text>
            </view>
            <view class="video_num">
                <text class="num">{{ themeDetail.videoNum + '次参与 ' }}</text>
            </view>
        </view>
        <!-- 作品列表-->
        <view class="video-container">
            <view class="video-item" v-for="item in video_list" :key="item.videoId">
                <VideoItem :detail="item" page="theme" @changeFollow="changeFollow" />
            </view>
        </view>
        <LoadingState :state="loadingState" />
    </view>
</template>
<script>
import LoadingState from '@/components/loading-state.vue';
import VideoItem from '@/components/videoItem.vue';

export default {
    components: {
        LoadingState,
        VideoItem
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            topBar: {}, // 顶部导航栏信息
            video_list: [],
            themeId: null, // 主题ID
            themeDetail: {}, // 主题详情
            query: {
                current: 1,
                pageSize: 10
            },
            loadingState: 'first_loading' // first_loading、 no_more_data 、loading 、refreshing
        };
    },
    onLoad() {
        const { themeId } = this.$Route.query;
        if (themeId) {
            this.themeId = themeId;
            this.getThemeDetail();
            this.getVideoList();
        }
        // #ifdef MP-WEIXIN
        // 胶囊按钮位置信息
        const systemInfo = uni.getWindowInfo();
        // 导航栏高度 = 状态栏高度 + 44
        const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
        this.navBarHeight = systemInfo.statusBarHeight + 44 + 'px';
        this.menuRight = systemInfo.screenWidth - menuButtonInfo.right + 'px';
        let globalData = {};
        globalData.navBarHeight = systemInfo.statusBarHeight + 44;
        globalData.menuRight = systemInfo.screenWidth - menuButtonInfo.right;
        globalData.menuHeight = menuButtonInfo.height;
        globalData.menuWidth = menuButtonInfo.width;
        globalData.offsettop = menuButtonInfo.top;
        this.topBar = globalData;
        // #endif
    },
    onPullDownRefresh() {
        this.getLableList(true);
    },
    methods: {
        getThemeDetail() {
            let param = {};
            param.url = 'v3/video/front/video/themeDetail';
            param.method = 'GET';
            param.data = {
                themeId: this.themeId
            };
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    this.themeDetail = res.data;
                }
            });
        },
        getVideoList() {
            let param = {};
            param.url = 'v3/video/front/video/list';
            param.method = 'GET';
            param.data = {
                ...this.query,
                themeId: this.themeId
            };
            this.loadingState = 'loading';
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    const { pagination, videoList } = res.data;
                    if (pagination.current >= 2) {
                        this.video_list = [...this.video_list, ...videoList];
                    } else {
                        this.video_list = videoList;
                    }
                    if (pagination.total <= pagination.current * pagination.pageSize) {
                        this.loadingState = 'no_more_data';
                    }
                }
            });
        }
    }
};
</script>
<style lang="scss" scoped>
.fixed_top_status_bar {
    .custom-nav-taber {
        position: fixed;
        z-index: 10;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        top: 0;
        // #ifdef MP
        left: 0;
        width: 100%;
        // #endif
        // #ifdef H5
        padding-top: 100rpx;
        box-sizing: content-box;
        left: 50%;
        width: 750rpx;
        transform: translateX(-50%);
        // #endif
        background-repeat: no-repeat;
        background-position: top center;
        background-size: 100% auto;
    }
    .nav-left {
        width: 50rpx;
        height: 50rpx;
        margin-left: 20rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        .back_icon{
            margin-left: -5rpx;
        }
        // #ifdef MP-WEIXIN
        // #endif

        // #ifdef H5
        // #endif
        &.bg {
            background-color: #9e9e9e;
            border-radius: 25rpx;
        }
    }
}
.container {
    min-height: 100vh;
    background: $bg1;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    .theme_header {
        width: 100%;
        height: 500rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-end;
        background-position: center;
        background-repeat: no-repeat;
        background-size: cover;
        .topic_name {
            width: 100%;
            text-align: left;
            padding: 0 4%;
            box-sizing: border-box;
            font-size: 38rpx;
            font-weight: bold;
            letter-spacing: 2px;
            color: #fff;
            line-height: 2;
        }
        .video_num {
            width: 100%;
            padding: 0 4%;
            box-sizing: border-box;
            line-height: 2;
            text-align: left;
            font-size: 28rpx;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 20rpx;
        }
    }
    .video-container {
        width: 100%;
        padding: 20rpx 0;
        .video-item {
            width: 94%;
            margin: 0 auto;
            margin-bottom: 20rpx;
        }
    }
}
</style>
