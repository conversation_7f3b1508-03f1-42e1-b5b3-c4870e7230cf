<template>
    <view class="page" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }">
        <ktabbar :title="pageTitle" :bgColor="tabbarBg" />
        <view class="container">
            <!-- item -->
            <videoFollow :list="list" :bgStyle="bgStyle" :showFans="false" @collect="collect" />

            <!-- 页面空数据 -->
            <emptyData :showFlag="loadingState != 'first_loading' && !list.length" :emptyIcon="imgUrl + 'empty_default.png'" />
            <!-- 页面loading -->
            <loading-state :state="loadingState" v-show="loadingState == 'first_loading' || list.length > 0" />
        </view>
    </view>
</template>

<script>
import { checkPageHasMore } from '@/utils/live';
import ktabbar from '@/components/ktabbar.vue';
import emptyData from '../component/emptyData.vue';
import loadingState from '@/components/loading-state.vue';
import videoFollow from '../component/user/videoFollow.vue';
export default {
    components: {
        ktabbar,
        loadingState,
        videoFollow,
        emptyData
    },
    data() {
        return {
            tabbarBg: 'transparent',
            loadingState: 'first_loading',
            pageTitle: '',
            type: 1, // 1 关注 2 粉丝
            list: [],
            loading: false,
            imgUrl: getApp().globalData.imgUrl,
            bgStyle: 'background-size:contain;background-position:center center;background-repeat: no-repeat;', //背景图片样式
            current: 1,
            pageSize: 10
        };
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function () {
        const { type } = this.$Route.query;
        this.type = type ? parseInt(type) : 1; // 默认关注
        if (type == 1) {
            this.pageTitle = '我的关注';
        } else {
            this.pageTitle = '我的粉丝';
        }
        this.getList();
    },
    //
    onPageScroll({ scrollTop }) {
        if (scrollTop > 50) {
            this.tabbarBg = '#fff';
        } else {
            this.tabbarBg = 'transparent';
        }
    },
    // 触底加载
    onReachBottom() {
        if (this.loadingState == 'allow_loading_more') {
            this.getList();
        }
    },

    methods: {
        //获取数据
        getList() {
            let {} = this;
            this.loading = true;
            let param = {};
            param.data = {
                pageSize: this.pageSize,
                current: this.current,
                followType: this.type
            };
            param.url = 'v3/video/front/video/author/followList';
            param.method = 'GET';
            this.loadingState = 'loading';
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    const { list, pagination } = res.data;
                    if (pagination.current == 1) {
                        this.list = list;
                    } else {
                        this.list = this.list.concat(list);
                    }
                    if (checkPageHasMore(pagination)) {
                        this.current++;
                        this.loadingState = 'allow_loading_more';
                    } else {
                        this.loadingState = 'no_more_data';
                    }
                }
                this.loading = false;
            });
        },
        //关注、取消关注事件
        collect(e) {
            let param = {};
            if (e.isFollow) {
                //取消关注
                param.url = 'v3/video/front/video/cancelFollow';
            } else {
                //关注
                param.url = 'v3/video/front/video/followAuthor';
            }
            param.method = 'POST';
            param.data = {};
            param.data.authorId = e.author_id;
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    uni.showToast({
                        title: res.msg,
                        icon: 'none'
                    });
                    this.current = 1;
                    this.getList();
                }
            });
        }
    }
};
</script>
<style lang="scss">
.page {
    width: 750rpx;
    min-height: 100vh;
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    /* 背景图片固定，不滚动 */
    background-attachment: fixed;
    .container {
        width: 94%;
        margin: 0 auto;
        padding-top: 50rpx;
    }
}
.empty_data {
    width: 750rpx;
}
</style>
