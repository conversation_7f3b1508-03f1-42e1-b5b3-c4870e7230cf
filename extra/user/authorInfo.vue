<template>
    <view :style="mix_diyStyle">
        <view>
            <view class="avator" style="margin-top: 20rpx" @tap="chooseImage('memberAvatar')">
                <text class="left">{{ $L('设置头像') }}</text>
                <view class="right">
                    <view class="img">
                        <!-- <image :src="author_info.memberAvatar" mode="aspectFit"></image> -->
                        <view
                            class="image"
                            :style="{
                                backgroundImage: 'url(' + author_info.memberAvatar + ')'
                            }"
                        ></view>
                    </view>
                    <image class="arrow_r" :src="imgUrl + 'svideo/mem_arrow_r.png'"></image>
                </view>
            </view>

            <view class="avator" @tap="chooseImage('backgroundImage')">
                <text class="left">{{ $L('设置主页背景') }}</text>
                <view class="right">
                    <view class="mem_bg" :style="'background:url(' + author_info.backgroundImage + ');'"></view>
                    <image class="arrow_r" :src="imgUrl + 'svideo/mem_arrow_r.png'"></image>
                </view>
            </view>

            <view class="avator common_style">
                <text class="left">{{ $L('会员名') }}</text>
                <view class="right">
                    <text class="member_name">{{ author_info.memberName }}</text>
                </view>
            </view>
            <view class="avator common_style" @click="editInfo('nick_name', $L('更改昵称'), author_info.memberNickname ? author_info.memberNickname : author_info.memberName)">
                <text class="left">{{ $L('昵称') }}</text>
                <view class="right">
                    <text class="member_name">{{ author_info.memberNickname ? author_info.memberNickname : author_info.memberName }}</text>
                    <image class="arrow_r" :src="imgUrl + 'svideo/mem_arrow_r.png'"></image>
                </view>
            </view>
            <view class="avator common_style" @click="editInfo('introduction', $L('个人简介'), author_info.introduction ? author_info.introduction : '')">
                <text class="left">{{ $L('个人简介') }}</text>
                <view class="right">
                    <text class="member_name">{{ author_info.introduction ? author_info.introduction : '' }}</text>
                    <image class="arrow_r" :src="imgUrl + 'svideo/mem_arrow_r.png'"></image>
                </view>
            </view>
        </view>

        <!-- #ifdef MP -->
        <privacyPop ref="priPop"></privacyPop>
        <!-- #endif -->
    </view>
</template>

<script>
import request from '@/utils/request';
import { mapState } from 'vuex';
// #ifdef MP
import privacyPop from '@/components/privacy-pop.vue';
// #endif
export default {
    // #ifdef MP
    components: {
        privacyPop
    },
    // #endif
    data() {
        return {
            //背景图片样式
            imgUrl: getApp().globalData.imgUrl,
            author_info: {}, //作者信息
            author_id: ''
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        setTimeout(() => {
            uni.setNavigationBarTitle({
                title: this.$L('编辑资料')
            });
        }, 0);

        this.getAuthorInfo();
    },
    onShow() {
        let _this = this;
        // #ifdef MP
        // 判断用户隐私授权
        if (!getApp().globalData.allow_privacy) {
            if (wx.getPrivacySetting == undefined) {
                //微信低版本不适配该授权方法
                getApp().globalData.allow_privacy = true;
            } else {
                wx.getPrivacySetting({
                    success(res) {
                        if (res.needAuthorization) {
                            _this.$refs.priPop.PrivacyProtocol = {
                                needAuthorization: res.needAuthorization,
                                privacyContractName: res.privacyContractName
                            };
                            _this.$refs.priPop.open();
                        } else {
                            getApp().globalData.allow_privacy = true;
                        }
                    }
                });
            }
        }
        // #endif
        this.getAuthorInfo();
    },
    computed: {
        ...mapState(['userInfo'])
    },
    methods: {
        //获取用户信息
        getAuthorInfo() {
            let key = uni.getStorageSync('token');
            let param = {};
            param.data = {};
            param.data.authorId = this.author_id;
            param.url = 'v3/video/front/video/author/personPage';
            param.method = 'GET';
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    this.author_info = res.data;
                }
            });
        },

        // 选择图片
        chooseImage(type) {
            // #ifdef MP
            if (!getApp().globalData.allow_privacy) {
                this.$refs.priPop.open();
                return;
            }
            // #endif
            let that = this;
            let key = uni.getStorageSync('token');
            uni.chooseImage({
                count: 1,
                sizeType: ['original', 'compressed'],
                //可选择原图或压缩后的图片
                sourceType: ['album', 'camera'],
                //可选择性开放访问相册、相机
                success: function (res) {
                    setTimeout(() => {
                        uni.showLoading({
                            title: this.$L('上传中'),
                            mask: true
                        });
                    });
                    if (res.size > 20971520) {
                        uni.showToast({
                            title: this.$L('超出文件最大限制20m，请重新上传！'),
                            icon: 'none',
                            duration: 1500
                        });
                    } else {
                        uni.uploadFile({
                            url: getApp().globalData.apiUrl + 'v3/oss/front/upload',
                            filePath: res.tempFilePaths[0],
                            name: 'file',
                            formData: {
                                source: 'evaluate'
                            },
                            header: {
                                Authorization: 'Bearer ' + that.userInfo.access_token
                            },
                            success: (uploadFileRes) => {
                                // #ifndef MP-ALIPAY||MP-BAIDU
                                let result = JSON.parse(uploadFileRes.data);
                                // #endif
                                // #ifdef MP-ALIPAY||MP-BAIDU
                                let result = uploadFileRes.data;
                                // #endif

                                if (type == 'memberAvatar') {
                                    that.author_info.memberAvatar = result.data.url;
                                    let memberAvatarPath = result.data.path;
                                    that.editImg('memberAvatar', memberAvatarPath);
                                } else {
                                    that.author_info.backgroundImage = result.data.url;
                                    let backgroundImagePath = result.data.path;
                                    that.editImg('backgroundImage', backgroundImagePath);
                                }
                            },
                            complete: () => {
                                uni.hideLoading();
                            }
                        });
                    }
                }
            });
        },

        //修改头像和设置主页背景
        editImg(type, path) {
            let param = {};
            param.data = {};
            if (type == 'memberAvatar') {
                param.data.memberAvatar = path;
            } else {
                param.data.backgroundImage = path;
            }
            param.url = 'v3/video/front/video/author/updateInfo';
            param.method = 'POST';
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    this.getAuthorInfo();
                } else {
                    uni.showToast({
                        title: res.msg,
                        icon: 'none'
                    });
                }
            });
        },

        //编辑信息
        editInfo(index, title, val) {
            this.$Router.push({
                path: '/extra/user/editAuthorInfo',
                query: { title, type: index, val }
            });
        }
    }
};
</script>
<style>
page {
    background-color: #f8f8f8;
    width: 750rpx;
    margin: 0 auto;
}

.avator {
    background: #fff;
    width: 750rpx;
    padding: 20rpx;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
}

.avator .left {
    color: rgba(45, 45, 45, 1);
    font-size: 28rpx;
}

.common_style {
    height: 100rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
    margin-bottom: 0;
}

.avator .right {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    height: 100%;
    overflow: hidden;
}

.avator .right .img {
    width: 80rpx;
    height: 80rpx;
    border-radius: 40rpx;
    overflow: hidden;
    background-size: contain !important;
    background-position: center center !important;
    background-repeat: no-repeat !important;
}

.avator .right .img .image {
    width: 100%;
    height: 100%;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
}

.avator .right .arrow_r {
    width: 40rpx;
    height: 42rpx;
    margin-left: 6rpx;
}

.avator .right .mem_bg {
    width: 126rpx;
    height: 82rpx;
    border-radius: 15rpx;
    background-size: cover !important;
    background-position: center center !important;
    background-repeat: no-repeat !important;
}

.avator .right .member_name {
    color: #949494;
    font-size: 24rpx;
    max-width: 500rpx;
    line-height: 82rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-break: break-all;
}
</style>
