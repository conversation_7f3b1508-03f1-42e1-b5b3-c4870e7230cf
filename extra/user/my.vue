<!-- 我的视频 -->
<template>
    <view class="page" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }">
        <ktabbar id="ktRef" :bgColor="tabbarBg" />
        <view class=""></view>
        <view class="container">
            <view class="author_info">
                <view class="info">
                    <view class="left_info">
                        <view class="author_avator" :style="'background-image:url(' + authorInfo.memberAvatar + ');'"></view>
                        <view class="author_name">
                            <text class="name">{{ authorInfo.memberNickname ? authorInfo.memberNickname : authorInfo.memberName }}</text>
                            <view class="author_desc" @tap="editMemInfo">
                                {{ authorInfo.introduction ? authorInfo.introduction : !authorInfo.isSelf ? $L('这个人很懒，什么都没有写') : $L('请设置个人简介~') }}
                            </view>
                        </view>
                    </view>
                    <view class="right_info">
                        <view class="follow-block" v-if="JSON.stringify(authorInfo) != '{}' && !authorInfo.isSelf">
                            <view class="follow_btn" :class="{ follow: authorInfo.isFollow }" type="info" @click.stop="collect('info')">
                                {{ authorInfo.isFollow ? '已关注' : '关注' }}
                            </view>
                        </view>
                    </view>
                </view>

                <view class="stat_num">
                    <view class="stat_num_item">
                        <text class="num">{{ authorInfo && authorInfo.likeNum ? authorInfo.likeNum : '0' }}</text>
                        <text class="desc">{{ $L('获赞') }}</text>
                    </view>
                    <view class="stat_num_item" @click="goAttention(1, authorInfo.memberNickname ? authorInfo.memberNickname : authorInfo.memberName)">
                        <text class="num">{{ authorInfo && authorInfo.followNum ? authorInfo.followNum : '0' }}</text>
                        <text class="desc">{{ $L('关注') }}</text>
                    </view>
                    <view class="stat_num_item" @click="goAttention(2, authorInfo.memberNickname ? authorInfo.memberNickname : authorInfo.memberName)">
                        <text class="num">{{ authorInfo && authorInfo.fansNum ? authorInfo.fansNum : '0' }}</text>
                        <text class="desc">{{ $L('粉丝') }}</text>
                    </view>
                </view>
            </view>
            <!-- tab切换 -->
            <view class="tab" id="tabRef" :style="{ backgroundColor: tabbarBg, top: ktabbarHeight - 1 + 'px' }">
                <view class="tab_item" :class="curTab == 'video' ? 'sel' : ''" @tap="changeTab('video')">
                    {{ $L('动态') }}
                    <text class="num">({{ authorInfo && authorInfo.videoNum ? (authorInfo.videoNum * 1 > 99 ? '99+' : authorInfo.videoNum) : '0' }})</text>
                </view>
                <view class="tab_item" v-if="authorInfo.isSelf" :class="curTab == 'favorite' ? 'sel' : ''" @tap="changeTab('favorite')">
                    {{ $L('喜欢') }}
                    <text class="num">({{ authorInfo && authorInfo.likeVideoNum ? (authorInfo.likeVideoNum * 1 > 99 ? '99+' : authorInfo.likeVideoNum) : '0' }})</text>
                </view>
            </view>
            <!-- #ifdef H5 -->
            <view :class="{ tab_fix: true, show: showFixTab }" id="tabFixRef" :style="{ backgroundColor: tabbarBg, top: ktabbarHeight - 1 + 'px' }">
                <view class="tab_item" :class="curTab == 'video' ? 'sel' : ''" @tap="changeTab('video')">
                    {{ $L('动态') }}
                    <text class="num">({{ authorInfo && authorInfo.videoNum ? (authorInfo.videoNum * 1 > 99 ? '99+' : authorInfo.videoNum) : '0' }})</text>
                </view>
                <view class="tab_item" v-if="authorInfo.isSelf" :class="curTab == 'favorite' ? 'sel' : ''" @tap="changeTab('favorite')">
                    {{ $L('喜欢') }}
                    <text class="num">({{ authorInfo && authorInfo.likeVideoNum ? (authorInfo.likeVideoNum * 1 > 99 ? '99+' : authorInfo.likeVideoNum) : '0' }})</text>
                </view>
            </view>
            <!-- #endif -->
            <view class="live_user_tab_content_main">
                <!-- 动态模块 -->
                <userVideo
                    v-show="curTab == 'video'"
                    :scrollEnable="scrollEnable"
                    :scrollHeight="scrollHeight"
                    :memberInfo="memberInfo"
                    :author_id="author_id"
                    ref="video"
                    :settingData="settingData"
                    :authorInfo="authorInfo"
                    @liveEvent="liveEvent"
                    @getAuthorInfo="getAuthorInfo"
                ></userVideo>
                <!-- 喜欢模块 -->
                <userFavorite
                    v-show="curTab == 'favorite'"
                    :scrollEnable="scrollEnable"
                    :scrollHeight="scrollHeight"
                    :memberInfo="memberInfo"
                    :settingData="settingData"
                    :authorInfo="authorInfo"
                    ref="favorite"
                ></userFavorite>
            </view>
        </view>
        <!-- 底部固定发布按钮 -->
        <view class="fix_bottom_btn" @click="openRelease">
            <text class="btn">{{ $L('发布') }}</text>
        </view>
        <!-- 发布按钮弹层 start -->
        <releasePop ref="releasePop" offsetBottom="0"></releasePop>
        <!-- 发布按钮弹层 end -->
    </view>
</template>
<script>
import ktabbar from '@/components/ktabbar.vue';
import userFavorite from '../component/user/userFavorite.vue';
import userVideo from '../component/user/userVideo.vue';
import { mapState, mapMutations } from 'vuex';
import releasePop from '@/components/releaseLive/releasePop.vue';

export default {
    components: {
        releasePop,
        userFavorite,
        userVideo,
        ktabbar
    },
    data() {
        return {
            author_id: '',
            ktabbarHeight: 0, //
            firstLoad: true,
            tabbarBg: 'transparent',
            scrollEnable: false,
            ktabbarHeight: 0, // 存储 ktabbar 组件高度
            // #ifdef H5
            showFixTab: false, // 是否显示固定tab H5 专用
            myObserver: null,
            // #endif
            // #ifdef MP
            menuButtonHeight: uni.getMenuButtonBoundingClientRect().height + 'px',
            menuButtonTop: uni.getMenuButtonBoundingClientRect().top + 'px',
            // #endif
            //作者id
            settingData: {},
            //平台设置信息
            authorInfo: {},
            //作者信息
            memberInfo: {},
            //用户相关信息
            imgUrl: getApp().globalData.imgUrl,
            //图片地址
            curTab: 'video',
            //当前tab
            showReleaseType: false, //是否展示发布类型
            role_type: '',
            publish_live_can: '',
            //店铺信息
            store_info: '',
            setting: {}, //平台设置信息
            bgStyle: 'background-size:cover;background-position:center center;background-repeat: no-repeat;',
            routeCurTab: '',
            showState: false,
            windowHeight: uni.getSystemInfoSync().windowHeight
        };
    },
    computed: {
        ...mapState(['hasLogin']),
        topHeight() {
            //wx-2-start
            // #ifdef MP-WEIXIN
            return `calc(${this.menuButtonTop} + ${this.menuButtonHeight} + 380rpx)`;
            // #endif
            //wx-2-end
            // #ifndef MP-WEIXIN
            return '';
            // #endif
        },
        scrollHeight() {
            // #ifdef H5
            return `calc(100vh - ${this.ktabbarHeight}px)`;
            // #endif
            // #ifndef H5
            return this.windowHeight - this.ktabbarHeight - uni.rpx2px(100) + 'px';
            // #endif
        }
    },
    mounted() {
        this.initData();
        // #ifdef MP
        let query = uni.createSelectorQuery().in(this);
        // 延时获取 ktabbar 组件高度
        setTimeout(() => {
            query
                .select('#ktRef')
                .boundingClientRect((res) => {
                    if (res) {
                        // 您可以将高度保存到 data 中
                        this.ktabbarHeight = res.height;
                        let tabRef = uni.createIntersectionObserver();
                        tabRef.relativeToViewport({ top: -(res.height + uni.rpx2px(100)) }).observe('#tabRef', (res) => {
                            // console.log('tabRef', res);
                            if (res.intersectionRatio == 0 && !this.firstLoad) {
                                this.scrollEnable = true;
                                this.tabbarBg = '#FFFFFF';
                            } else {
                                this.scrollEnable = false;
                                this.tabbarBg = 'transparent';
                            }
                            this.firstLoad = false;
                        });
                    }
                })
                .exec();
        }, 100);
        // #endif

        // #ifdef H5
        // 使用浏览器api观察元素相交状态
        this.ktabbarHeight = document.querySelector('.custom-nav-taber').offsetHeight;
        this.myObserver = new IntersectionObserver(
            (entries) => {
                // console.log('observer', entries);
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        this.scrollEnable = false;
                        this.showFixTab = false;
                        this.tabbarBg = 'transparent';
                    } else {
                        this.scrollEnable = true;
                        this.tabbarBg = '#FFFFFF';
                        this.showFixTab = true; // 显示固定tab
                    }
                });
            },
            { threshold: [0], rootMargin: `-${this.ktabbarHeight + 1}px` }
        );
        this.myObserver.observe(document.querySelector('#tabRef'));
        // #endif
    },
    beforeDestroy() {
        // #ifdef H5
        // 断开观察
        console.log('beforeDestroy 断开观察');
        this.myObserver.disconnect();
        // #endif
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        setTimeout(() => {
            uni.setNavigationBarTitle({
                title: this.$L('我的视频')
            });
        }, 0);

        if (this.$Route.query.author_id) {
            this.author_id = this.$Route.query.author_id.toString();
        }
        if (this.$Route.query.curTab) {
            this.routeCurTab = this.$Route.query.curTab;
        }
        this.getSetting();

        uni.$on('updateState', () => {
            this.$refs[this.curTab].pn = 1;
            this.$refs.video.getVideoList();
        });
    },
    onShow: function () {
        if (this.curTab == 'favorite') {
            //从视频播放页返回后刷新列表
            this.$refs.favorite.getFavoriteList();
        }

        if (this.showState) {
            this.showState = false;
            if (this.curTab == 'video' && this.$refs.video) {
                this.$refs[this.curTab].pn = 1;
                this.$refs.video.getVideoList();
            } else if (this.curTab == 'goods' && this.$refs.goods) {
                this.$refs[this.curTab].pn = 1;
                this.$refs.goods.getGoods();
            } else if (this.curTab == 'favorite' && this.$refs.favorite) {
                this.$refs[this.curTab].pn = 1;
                this.$refs.favorite.getFavoriteList();
            } else if (this.curTab == 'live' && this.$refs.live) {
                this.$refs[this.curTab].pn = 1;
                this.$refs.live.getLiveList();
            }
        }

        this.initData();
    },
    onHide() {
        this.showReleaseType = false;
    },

    onUnload() {
        uni.$off('updateState');
        if (this.setting.live_switch == 1) {
            uni.$off('updateLiveList');
        }
    },
    methods: {
        // 打开发布类型选择
        openRelease() {
            this.$refs.releasePop.open();
        },
        //初始化数据
        initData() {
            this.getAuthorInfo();
        },
        //获取作者信息
        getAuthorInfo() {
            let { author_id } = this;
            let param = {};
            param.data = {};
            if (author_id != '') {
                param.data.authorId = author_id;
            }
            param.url = 'v3/video/front/video/author/personPage';
            param.method = 'GET';
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    this.authorInfo = res.data;
                    uni.$emit('fromLiveAttention');
                    if (!this.authorInfo.isSelf) {
                        uni.setNavigationBarTitle({
                            title: this.authorInfo.memberNickname
                        });
                    }
                }
            });
        },

        // 获取设置信息
        getSetting() {
            let param = {};
            param.url = 'v3/video/front/video/setting/getSettingList';
            param.method = 'GET';
            param.data = {};
            param.data.str = 'video_switch,live_switch';

            this.$request(param).then((res) => {
                if (res.state == 200) {
                    let result = res.data;
                    result.map((settingItem) => {
                        if (settingItem.name == 'video_switch') {
                            //绑定商品数
                            this.setting.video_switch = settingItem.value;
                        }
                        if (settingItem.name == 'live_switch') {
                            //绑定商品数
                            this.setting.live_switch = settingItem.value;

                            if (this.setting.live_switch == 1) {
                                uni.$on('updateLiveList', () => {
                                    setTimeout(() => {
                                        this.$refs[this.curTab].pn = 1;
                                        this.$refs.live.getLiveList();
                                    }, 1000);
                                });
                            }
                        }
                        this.setting = JSON.parse(JSON.stringify(this.setting));
                    });
                    this.curTab = 'video';
                }
            });
        },
        // 返回上级页面
        goBack() {
            this.$Route.back(1);
        },

        //关注、取消关注事件
        collect(type = '') {
            if (this.hasLogin) {
                let param = {};
                param.data = {};
                param.method = 'POST';
                param.data.authorId = this.author_id;
                if (this.authorInfo.isFollow) {
                    //取消关注
                    param.url = 'v3/video/front/video/cancelFollow';
                } else {
                    //关注作者
                    param.url = 'v3/video/front/video/followAuthor';
                }
                this.$request(param).then((res) => {
                    if (res.state == 200) {
                        uni.showToast({
                            title: res.msg,
                            icon: 'none',
                            duration: 500
                        });
                        if (type != 'info') {
                            this.$api.prePage() && this.$api.prePage().updateCollect(this.author_id);
                        }
                        this.getAuthorInfo();
                    }
                });
            } else {
                getApp().globalData.goLogin(this.$Route);
            }
        },

        //查看粉丝
        goAttention(index, title) {
            if (this.authorInfo.isSelf) {
                this.$Router.push({
                    path: '/extra/user/attention',
                    query: {
                        type: index
                    }
                });
            }
        },

        //编辑资料
        editMemInfo() {
            if (this.authorInfo.isSelf) {
                this.$Router.push('/extra/user/authorInfo');
            }
        },

        //tab切换
        changeTab(targetTab) {
            this.curTab = targetTab;
            this.$Route.query.curTab = targetTab;
        },

        //进入评论列表
        goComments() {
            this.$Router.push('/extra/svideo/svideoComments');
        },

        //点击底部加号事件
        release(e) {
            this.showReleaseTypeFun(e);
        },

        // 发布直播
        releaseLive(type) {
            let { author_id, authorInfo, memberInfo } = this;
            this.showState = true;
            if (type == 'live') {
                if (this.setting.live_switch == 1) {
                    //直播
                    if (this.authorInfo.liveState == 0) {
                        this.applicationShowTip(this.authorInfo.forbidReason);
                        return;
                    }
                }

                this.$Router.push({
                    path: '/extra/live/liveReleaseLive',
                    query: {
                        roleType: this.authorInfo.roleType,
                        storeId: this.authorInfo.storeId
                    }
                });
            } else if (type == 'video') {
                if (this.authorInfo.permissionState == 0) {
                    this.applicationTip(this.$L('是否立即申请发布权限'));
                    return;
                } else if (this.authorInfo.permissionState == 1) {
                    if (!(this.authorInfo.remark && this.authorInfo.forbidReason)) {
                        this.applicationShowTip(this.$L('审核中，请耐心等待'));
                        return;
                    }
                } else if (this.authorInfo.permissionState == 3) {
                    this.applicationTip(this.authorInfo.remark);
                    return;
                } else if (this.authorInfo.permissionState == 4) {
                    if (!(this.authorInfo.remark && this.authorInfo.forbidReason)) {
                        this.applicationShowTip(this.authorInfo.remark);
                        return;
                    }
                }

                let query = {
                    roleType: this.authorInfo.roleType
                };
                if (this.authorInfo.storeId) {
                    query.storeId = this.authorInfo.storeId;
                }

                this.$Router.push({
                    path: '/extra/svideo/svideoRelease',
                    query
                });
            } else if (type == 'graphic') {
                if (this.authorInfo.permissionState == 0) {
                    this.applicationTip(this.$L('是否立即申请发布权限'));
                    return;
                } else if (this.authorInfo.permissionState == 1) {
                    if (!(this.authorInfo.remark && this.authorInfo.forbidReason)) {
                        this.applicationShowTip(this.$L('审核中，请耐心等待'));
                        return;
                    }
                } else if (this.authorInfo.permissionState == 3) {
                    this.applicationTip(this.authorInfo.remark);
                    return;
                } else if (this.authorInfo.permissionState == 4) {
                    if (!(this.authorInfo.remark && this.authorInfo.forbidReason)) {
                        this.applicationShowTip(this.authorInfo.remark);
                        return;
                    }
                }

                this.$Router.push({
                    path: '/extra/graphic/graphicRelease'
                });
            }
        },

        applicationShowTip(tip) {
            let tips = tip;
            if (this.authorInfo.remark && this.authorInfo.forbidReason) {
                tips = this.$L('禁止发布：') + this.authorInfo.remark + ',' + this.authorInfo.forbidReason;
            }
            if (this.authorInfo.permissionState == 1) {
                uni.showModal({
                    title: this.$L('提示'),
                    content: tips,
                    showCancel: false,
                    confirmText: this.$L('确定')
                });
            } else {
                uni.showModal({
                    title: this.$L('您已被平台限制发布'),
                    content: tips,
                    showCancel: false,
                    confirmText: this.$L('确定')
                });
            }
        },
        applicationTip(tips) {
            this.applicationFb(tips);
        },

        applicationFb(tip) {
            uni.showModal({
                title: this.$L('提示'),
                content: tip,
                confirmText: this.authorInfo.permissionState == 3 ? this.$L('再次申请') : this.$L('确定'),
                cancelText: this.$L('取消'),
                success: (res) => {
                    if (res.confirm) {
                        let param = {};
                        param.data = {};
                        param.method = 'POST';
                        param.url = 'v3/video/front/video/author/apply';
                        this.$request(param).then((res) => {
                            if (res.state == 200) {
                                uni.showToast({
                                    title: res.msg,
                                    icon: 'none'
                                });
                                this.getAuthorInfo();
                            }
                        });
                    }
                }
            });
        },

        liveEvent(e) {
            this.getAuthorInfo();
        },

        // 获取 ktabbar 组件高度
        getKtabbarHeight() {
            this.$nextTick(() => {
                let query = uni.createSelectorQuery().in(this);
                query
                    .select('#ktRef')
                    .boundingClientRect((res) => {
                        if (res) {
                            console.log('ktabbar 组件信息:', res);
                            this.ktabbarHeight = res.height;
                            // res 包含以下信息：
                            // res.width - 宽度
                            // res.height - 高度
                            // res.top - 距离页面顶部的距离
                            // res.left - 距离页面左侧的距离
                            // res.bottom - 距离页面底部的距离
                            // res.right - 距离页面右侧的距离
                        }
                    })
                    .exec();
            });
        },

        showReleaseTypeFun(e) {
            this.showReleaseType = e.currentTarget.dataset.flag;
        }
    }
};
</script>
<style lang="scss">
.page {
    width: 750rpx;
    min-height: 100vh;
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    // 背景图片固定，不滚动
    background-attachment: fixed;
    .container {
    }
}
.fix_bottom_btn {
    position: fixed;
    bottom: 30rpx;
    left: 0;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;

    .btn {
        color: #fff;
        height: 80rpx;
        line-height: 80rpx;
        border-radius: 40rpx;
        text-align: center;
        width: 94%;
        font-size: $fs-base;
        background-color: rgba(73, 73, 73, 1);
    }
}
.author_info {
    width: 100%;
    padding: 0 4%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    box-sizing: border-box;
    overflow: hidden;
    position: relative;

    .info {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 150rpx;
        .left_info {
            display: flex;
            flex-direction: row;
            align-items: center;
            width: calc(100% - 200rpx);
        }
        .right_info {
            display: flex;
        }
        .follow-block {
            display: flex;
            justify-content: center;
            align-items: center;

            .follow_btn {
                background-color: transparent;
                color: #000;
                border: 1px solid;
                border-color: rgba(89, 87, 87, 1);
                border-radius: 25rpx;
                padding: 0 30rpx;
                font-size: 24rpx;
                text-align: center;
                cursor: pointer;
                height: 50rpx;
                line-height: 50rpx;
                &.follow {
                    color: rgba(0, 0, 0, 0.3);
                    border-color: rgba(0, 0, 0, 0.3);
                }
            }
        }
        .author_avator {
            width: 120rpx;
            height: 120rpx;
            border-radius: 60rpx;
            border: 2rpx solid rgba(255, 255, 255, 0.8);
            background-size: cover;
            background-position: center center;
            background-repeat: no-repeat;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
        }

        .author_name {
            max-width: 310rpx;
            margin-left: 20rpx;
            .name {
                width: 100%;
                color: #000;
                font-size: 36rpx;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            .author_desc {
                color: rgba(89, 87, 87, 1);
                width: 100%;
                margin-top: 15rpx;
                font-size: 26rpx;
                line-height: 32rpx;
                text-overflow: ellipsis;
                overflow: hidden;
                word-break: break-all;
            }
        }
        .fav_info {
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            border-radius: 8rpx;
            background: #fc1c1c;
            padding: 8rpx 10rpx;
            margin-left: 20rpx;

            image {
                width: 30rpx;
                height: 30rpx;
            }

            text {
                color: #f9e9e9;
                font-size: 24rpx;
                margin-left: 5rpx;
                margin-right: 5rpx;
            }
        }

        .fav_info_bg {
            background: var(--color_video_main) !important;
        }
        .go_vendor {
            background: linear-gradient(to left, #fc1c1c, #ffa300);
            width: 73rpx;
            height: 40rpx;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            border-radius: 20rpx;
            margin-left: 20rpx;

            text {
                color: #fff;
                font-size: 22rpx;
            }
        }
    }
    .stat_num {
        width: 100%;
        margin-top: 50rpx;
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        border-radius: 40rpx;
        padding: 30rpx 50rpx;
        background-color: rgba(255, 255, 255, 0.78);
        .stat_num_item {
            width: 33.33%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            .num {
                font-size: 34rpx;
                font-weight: bold;
                margin-bottom: 10rpx;
            }
            .desc {
                color: rgba(0, 0, 0, 0.5);
                font-size: $fs-base;
            }
        }
    }
}
.tab_fix {
    position: fixed !important;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease !important;
    &.show {
        opacity: 1;
        transform: translateY(0);
    }
    z-index: 15;
}
.tab,
.tab_fix {
    // #ifdef MP
    position: sticky;
    left: 0;
    // #endif
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
    align-items: center;
    width: 100%;
    height: 100rpx;
    z-index: 15;
    transition: background-color 0.3s ease;
    background-color: transparent;
    .tab_item {
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        text-align: center;
        color: #000;
        font-size: 28rpx;
        line-height: 40rpx;
        padding-bottom: 10rpx;
        &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            border-radius: 1px;
            background-color: transparent;
        }
        &.sel {
            &::after {
                background-color: $color1;
            }
        }
        .num {
            margin-left: 5rpx;
            font-size: 24rpx;
            color: rgba(0, 0, 0, 0.5);
        }
    }
}
.live_user_tab_content_main {
    width: 750rpx;
    top: 467rpx;
    margin: 0 auto;
}

/* #ifdef MP*/
.live_user_tab_content {
    width: 750rpx;
    position: fixed;
    top: 390rpx;
    left: 0;
    bottom: 0;
}

/* #endif */
/* #ifdef H5  */
.live_user_tab_content {
    width: 750rpx;
    position: fixed;
    top: 448rpx;
    /* left: 0; */
    bottom: 0;
}

/* #endif */

.author_info .info .msg_wrap {
    width: 34rpx;
    height: 34rpx;
    position: relative;
    margin-left: 20rpx;
}

.author_info .info .msg_wrap image {
    width: 34rpx;
    height: 34rpx;
}

.author_info .info .msg_wrap text {
    position: absolute;
    z-index: 2;
    top: -26rpx;
    left: 6rpx;
    color: #fff;
    background: var(--color_video_main);
    min-width: 58rpx;
    padding: 6rpx 4rpx;
    border-radius: 50%;
    text-align: center;
    transform: scale(0.5);
}

.release {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2;
    width: 750rpx;
    height: 100rpx;
    background: transparent;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    margin: 0 auto;
}

// .release image {
// 	position: absolute;
// 	bottom: 40rpx;
// 	z-index: 3;
// 	width: 120rpx;
// 	height: 120rpx;
// }

.release .release_bot {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 100rpx;
    background: #fff;
}
.release_img {
    position: absolute;
    bottom: 40rpx;
    z-index: 3;
    width: 120rpx;
    height: 120rpx;
    background-color: #fff;
    border-radius: 50%;
}

.release_mask {
    width: 750rpx;
    position: fixed;
    z-index: 99;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    margin: 0 auto;
}

.release_mask .content {
    position: absolute;
    bottom: 35rpx;
    left: 0;
    right: 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
}

.release_mask .content .colse {
    width: 47rpx;
    height: 47rpx;
    margin-top: 38rpx;
}

.release_mask .content .detail {
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: 100%;
}

.release_mask .content .detail .item {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
}

.release_mask .content .detail .item image {
    width: 110rpx;
    height: 110rpx;
}

.release_mask .content .detail .item text {
    color: #fff;
    font-size: 34rpx;
    margin-top: 28rpx;
}

.nav-bar {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding-left: 20rpx;
}

.nav-bar image {
    width: 21rpx;
    height: 35rpx;
}

.nav-bar view {
    font-size: 36rpx;
    font-family: PingFang SC;
    font-weight: 500;
    color: #ffffff;
    margin-left: 21rpx;
}
</style>
