<template>
	<view :style="mix_diyStyle">
	<view>
		<view v-if="type=='nick_name'" class="edit_nick_name">
			<view class="input_wrap">
				<input type="text" :value="val" :maxlength="limit_num"
					:placeholder="`${$L('请输入昵称,最多输入')}${limit_num}${$L('个字符')}`"
					placeholder-style="font-size:24rpx;color:#949494" @input="inputCon"
					confirm-type="done"></input>
				<image v-if="val" class="clear_con" @tap="clearCon" :src="imgUrl+'svideo/input_clear.png'"></image>
			</view>
			<view v-if="val" class="count">
				<text class="cur_count">{{val.length}}</text><text>/{{limit_num}}</text>
			</view>
		</view>

		<view v-if="type=='introduction'" class="edit_introduction">
			<view class="introduction">
				<textarea @input="inputCon" :placeholder="$L('请输入简介，最多50个字符~')" :maxlength="limit_num" :value="val"
					placeholder-style="color:#949494"></textarea>
				<view class="count">
					<block v-if="val">
						<text class="cur_count">{{val.length}}</text>
						<text>/</text>
					</block>
					<text>{{limit_num}}</text>
				</view>
			</view>
		</view>
		<view class="save_btn" @click="saveCon">
			{{$L('保存')}}
		</view>


	</view>
	</view>
</template>

<script>
	import request from "@/utils/request";

	export default {
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				//图片地址
				val: '',
				//内容
				limit_num: 0, //限制长度
				type: '',
				title: ''

			};
		},

		components: {},
		props: {},

		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function (options) {
      setTimeout(()=>{
        uni.setNavigationBarTitle({
          title: this.$L('编辑资料')
        });    
      },0);
			setTimeout(()=>{
        uni.setNavigationBarTitle({
          title: this.$Route.query.title
        });
			},1);
			
			this.val = this.$Route.query.val;
			this.type = this.$Route.query.type;
			this.title = this.$Route.query.title;
			this.limit_num = this.$Route.query.type == 'nick_name' ? 11 : 50;
		},
		onUnload: function () { },
		methods: {
			//清空输入值
			clearCon() {
				this.val = '';
			},

			//input输入事件
			inputCon(e) {
				let {
					limit_num
				} = this;
				let con = e.detail.value;

				if (con.length <= limit_num) {
					this.val = con
				}
			},
			//保存数据
			saveCon() {
				let {
					val,
					type
				} = this;
				if (val.trim().length == 0) {
					uni.showToast({
						title: `${this.$L('请输入')}${type == "introduction" ? this.$L('简介') : this.$L('昵称')}`,
						icon: 'none'
					});
				} else {
					let data = {

					};

					if (type == 'introduction') {
						data.introduction = val;
					} else {
						data.memberNickname = val;
					}
					let param = {}
					param.data = data;
					param.url = 'v3/video/front/video/author/updateInfo'
					param.method = 'POST'
					this.$request(param).then(res => {
						if (res.state == 200) {
							this.$api.msg(res.msg)
							setTimeout(() => {
								this.$Router.back(1)
							}, 1000)
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							});
						}
					})
				}
			}

		}
	};
</script>
<style>
	/* addons/pages/svideoEditAuthorInfo/svideoEditAuthorInfo.wxss */

	page {
		background-color: #F8F8F8;
		width: 750rpx;
		margin: 0 auto;
		height: 100%;
	}

	.edit_nick_name,
	.edit_introduction {
		width: 100%;
		height: 100%;
		background: #fff;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
	}

	.edit_nick_name .input_wrap {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		height: 83rpx;
		padding: 0 20rpx;
		width: 710rpx;
		border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
	}

	.edit_nick_name .input_wrap input {
		width: 450rpx;
		color: #2D2D2D;
		font-size: 26rpx;
	}

	.edit_nick_name .input_wrap .clear_con {
		width: 43rpx;
		height: 43rpx;
	}

	.edit_nick_name .count {
		width: calc(750rpx-39rpx);
		display: flex;
		justify-content: flex-end;
		margin-right: 39rpx;
		margin-top: 19rpx;
		margin-bottom: 15rpx;
	}

	.edit_nick_name .count text,
	.edit_introduction .introduction .count text {
		font-size: 22rpx;
		color: #949494;
	}

	.edit_nick_name .count .cur_count,
	.edit_introduction .introduction .count .cur_count {
		color:var(--color_video_main);
	}

	.edit_introduction .introduction {
		margin-top: 20rpx;
		width: 710rpx;
		margin-left: 20rpx;
		height: 300rpx;
		position: relative;
	}

	.edit_introduction .introduction textarea {
		width: 670rpx;
		height: 258rpx;
		background: #DDDDDD;
		border: 1rpx solid rgba(0, 0, 0, 0.1);
		border-radius: 15rpx;
		padding: 20rpx;
		padding-right: 40rpx;
		color: #2D2D2D;
		font-size: 26rpx;
		margin: 0 auto;
		word-break: break-all;
	}

	.edit_introduction .introduction .count {
		position: absolute;
		right: 15rpx;
		bottom: 10rpx;
		z-index: 2;
	}

	.save_btn {
		width: 468rpx;
		margin: 0 auto;
		font-size: 36rpx;
		color: #fff;
		height: 80rpx;
		background:var(--color_video_main);
		border-radius: 40rpx;
		letter-spacing: 0.5px;
		display: flex;
		align-items: center;
		justify-content: center;
		letter-spacing: 1rpx;
		margin-top: 80rpx;
	}
</style>