<template>
    <view class="page" :style="'background-image:url(' + imgUrl + 'default_bg.jpg)'">
        <KTabbar :bgImg="imgUrl + 'default_bg.jpg'" />
        <!-- 标题 -->
        <view class="main_gra_detail">
            <block v-if="loadingShow">
                <!-- 作者信息 -->
                <view class="gra_user">
                    <view class="user_left" :data-authorId="authorDetail.authorId" @tap="goLiveUserCenter">
                        <view class="mem_info" :data-authorId="authorDetail.authorId">
                            <view class="avator" :data-authorId="authorDetail.authorId" :style="'background-image:url(' + authorDetail.memberAvatar + ');'"></view>
                            <text class="name">{{ authorDetail.memberNickname ? authorDetail.memberNickname : authorDetail.memberName }}</text>
                        </view>
                        <view class="level">
                            <image
                                v-if="authorDetail.memberType != 1"
                                class="vip-image"
                                :src="imgUrl + 'user/vip-' + (authorDetail.memberLevel ? authorDetail.memberLevel : '1') + '.png'"
                                mode=""
                            />
                            <image v-else class="lima-image" :src="imgUrl + 'lima_text.png'" mode="widthFix" />
                        </view>
                    </view>
                    <view class="follow-block" v-if="!videoDetail.isSelf">
                        <view class="follow_btn" :class="{ follow: authorDetail.isFollow }" type="info" @click.stop="collect">{{ authorDetail.isFollow ? '已关注' : '关注' }}</view>
                    </view>
                    <!-- <block v-if="authorDetail.isSelf">
                        <view class="uni-navbar__content_view" @tap="delGraphic('open')">
                            <uni-icons color="rgb(0, 0, 0)" type="trash" size="24" />
                        </view>
                    </block> -->
                </view>
                <!-- 分割线 -->
                <div class="divider"></div>
                <!-- 视频内容 -->
                <view class="gra_text">
                    <view class="gra_title">
                        <view class="title">{{ videoDetail.videoName }}</view>
                        <view class="gra_info">
                            <text class="info_v">{{ formatTime(videoDetail.createTime, 1) }}</text>
                            <text class="c_line"></text>
                            <block v-if="formatTime(videoDetail.createTime, 2)">
                                <text class="info_v">{{ formatTime(videoDetail.createTime, 2) }}</text>
                                <text class="c_line"></text>
                            </block>
                            <text class="info_v">{{ videoDetail.clickNum }}次浏览</text>
                        </view>
                    </view>
                    <!-- 主题 -->
                    <!-- v-if="videoDetail.themeId != undefined && videoDetail.themeId" -->
                    <view class="tag_part" v-if="videoDetail.themeId != undefined && videoDetail.themeId">
                        <view class="tag" @click="goThemeDetail">
                            <text class="icon">#</text>
                            <text class="tag_text">{{ videoDetail.themeName }}</text>
                        </view>
                    </view>
                    <!-- <view class="gra_desc">
                        <view class="gra_intro">{{ videoDetail.introduction }}</view>
                    </view> -->
                    <view class="gra_content">
                        <text>{{ videoDetail.videoContent || '' }}</text>
                    </view>
                </view>
                <!-- 图片列表 -->
                <view class="img_list">
                    <view
                        class="gra_pic_item"
                        v-for="(item, idxA) in videoDetail.videoImage"
                        @click="previewImage(idxA)"
                        :key="idxA"
                        :style="'background-image:url(' + item + ');'"
                    ></view>
                </view>
                <!-- 底部评论 -->
                <view class="gra_bottom">
                    <view class="left">
                        <view class="input_collect">
                            <view class="input" @click="openComment">
                                <image class="image" :src="imgUrl + 'edit2.png'"></image>
                                <text class="text">{{ $L('分享你的探索感受...') }}</text>
                            </view>
                        </view>
                    </view>
                    <view class="right">
                        <view class="collect">
                            <image class="image" mode="aspectFit" :src="imgUrl + 'svideo/comment.png'"></image>
                            <text class="num">{{ formatNum(videoDetail.commentNum) }}</text>
                        </view>
                        <view class="collect">
                            <image class="image" mode="aspectFit" :src="imgUrl + 'svideo/fav.png'" @click="like"></image>
                            <text class="num">{{ formatNum(videoDetail.likeNum) }}</text>
                        </view>
                        <view class="collect share" @click="showShare">
                            <image class="share_img" :src="imgUrl + 'Share_btn.png'"></image>
                        </view>
                        <!-- #ifndef H5 -->
                        <view class="collect share" @click="showShare" v-if="isWeiXinBrower">
                            <image class="share_img" :src="imgUrl + 'graphic/share.png'"></image>
                        </view>
                        <!-- #endif -->
                    </view>
                </view>
            </block>
            <uni-popup ref="commentModel" type="bottom" @change="changePop">
                <comment :videoDetail="videoDetail" ref="comment" @close="closeComment" :videoId="video_id" @updateCommentNum="updateCommentNum"></comment>
            </uni-popup>
            <view class="select-wrap" catchtouchmove="touchmoveshare" v-if="shareWrap">
                <view class="share-mode">
                    <view class="share-img"></view>
                    <view class="ul">
                        <!-- app-1-start -->
                        <!-- #ifdef APP-PLUS -->
                        <button @tap.stop="sldShare(0, 'WXSceneSession')" class="item">
                            <image :src="imgUrl + 'goods_detail/wx_share.png'" mode="widthFix"></image>
                            <text>{{ $L('微信好友') }}</text>
                        </button>
                        <button @tap.stop="sldShare(0, 'WXSenceTimeline')" class="item">
                            <image :src="imgUrl + 'svideo/pyq_share.png'" mode="widthFix"></image>
                            <text>{{ $L('微信朋友圈') }}</text>
                        </button>
                        <!-- #endif -->
                        <!-- app-1-end -->

                        <!-- #ifdef  H5 -->
                        <button @tap.stop="sldShareBrower(1)" class="item" v-if="isWeiXinBrower">
                            <image :src="imgUrl + 'goods_detail/wx_share.png'" mode="widthFix"></image>
                            <text>{{ $L('微信好友') }}</text>
                        </button>
                        <button @tap.stop="sldShareBrower(2)" class="item" v-if="isWeiXinBrower">
                            <image :src="imgUrl + 'svideo/pyq_share.png'" mode="widthFix"></image>
                            <text>{{ $L('微信朋友圈') }}</text>
                        </button>
                        <!-- #endif -->

                        <!-- #ifdef MP -->
                        <button open-type="share" @click="shareWrap = false" class="item">
                            <view class="img_con flex_column_between_center">
                                <image :src="imgUrl + 'rank/wx_share.png'" mode="aspectFit"></image>
                                <text>{{ $L('微信好友') }}</text>
                            </view>
                        </button>
                        <!-- #endif -->
                    </view>

                    <view class="close" @tap="closeShare">
                        <image :src="imgUrl + 'svideo/share_close2.png'" mode="aspectFit"></image>
                    </view>
                </view>
            </view>
            <view class="wx_brower_share_mask" v-if="showWeiXinBrowerTip">
                <view class="wx_brower_share_top_wrap">
                    <image :src="imgUrl + 'wx_share_tip.png'" mode="widthFix" @tap="closeShare" class="wx_brower_share_img"></image>
                </view>
            </view>
            <uni-popup ref="popup" type="dialog">
                <uni-popup-dialog type="input" :title="$L('提示')" :content="$L('确定要删除吗?')" :duration="2000" @confirm="delGraphic('confirm')"></uni-popup-dialog>
            </uni-popup>
            <loginPop ref="loginPop"></loginPop>
        </view>
    </view>
</template>

<script>
import { mapState } from 'vuex';
import KTabbar from '@/components/ktabbar.vue';
import loadingState from '@/components/loading-state.vue';
import uniSwiperDot from '@/components/uni-swiper-dot/uni-swiper-dot.vue';
import uniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue';
import graGoodsItem from '../component/graphic/graGoodItem.vue';
import comment from '../component/graphic/comment.vue';
import uniIcons from '@/components/uni-icons/uni-icons.vue';
import loginPop from '@/components/loginPop/loginPop.vue';
import { formatVideoBigNum } from '@/utils/common.js';
export default {
    components: {
        uniNavBar,
        KTabbar,
        uniSwiperDot,
        uniPopup,
        graGoodsItem,
        comment,
        loadingState,
        uniPopupDialog,
        uniIcons
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            member_avatar: '',
            current: 0,
            goodsList: [],
            videoDetail: {},
            authorDetail: {},
            roleType: '',
            video_id: '',
            goodsMore: true,
            loadingState: '',
            shareWrap: false,
            showWeiXinBrowerTip: false,
            isWeiXinBrower: false,
            iconSelf: '',
            isShow: false,
            index: null, //当前操作的图文下标
            loadingShow: false,
            preUpdateIndex: -1 //是否需要更新上一个页面的数据 -- 取操作的列表下标
        };
    },
    onLoad() {
        this.video_id = this.$Route.query.video_id;
        this.roleType = this.$Route.query.roleType;
        this.index = this.$Route.query.index;
        this.getVideoInfo();
        // #ifdef H5
        this.isWeiXinBrower = this.$isWeiXinBrower();
        // #endif
    },
    onShow() {
        if (this.isShow) {
            this.getVideoInfo();
        }
    },
    computed: {
        ...mapState(['hasLogin'])
    },
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function (options) {
        let { videoDetail, video_id } = this;
        return {
            title: videoDetail.videoName,
            path: '/extra/graphic/graphicDetail?video_id=' + video_id,
            imageUrl: videoDetail.videoImage
        };
    },
    onShareTimeline: function (options) {
        let { videoDetail, video_id } = this;
        return {
            title: videoDetail.videoName,
            query: 'video_id=' + video_id,
            imageUrl: videoDetail.videoImage
        };
    },
    methods: {
        //进入主题详情
        goThemeDetail() {
            if (this.videoDetail.themeId) {
                this.$Router.push({ path: '/extra/topic/theme', query: { themeId: this.videoDetail.themeId } });
            }
        },
        // 处理视频数据的显示方式
        formatNum(num) {
            return formatVideoBigNum(num);
        },
        //格式化时间
        formatTime(time, type) {
            // type 1: 年月日    type 2: xx分钟前、xx小时前、xx天前 最大为3天，超过三天返回空
            if (!time) return '';
            try {
                const now = this.dayjs();
                const targetTime = this.dayjs(time);

                if (type === 1) {
                    // 返回年月日格式：2024-01-15
                    return targetTime.format('YYYY-MM-DD');
                } else if (type === 2) {
                    // 计算时间差
                    const diffMinutes = now.diff(targetTime, 'minute');
                    const diffHours = now.diff(targetTime, 'hour');
                    const diffDays = now.diff(targetTime, 'day');

                    if (diffDays > 3) {
                        // 超过三天返回空
                        return '';
                    } else if (diffDays >= 1) {
                        // 显示天数
                        return `${diffDays}天前`;
                    } else if (diffHours >= 1) {
                        // 显示小时数
                        return `${diffHours}小时前`;
                    } else if (diffMinutes >= 1) {
                        // 显示分钟数
                        return `${diffMinutes}分钟前`;
                    } else {
                        // 刚刚
                        return '刚刚';
                    }
                }

                return '';
            } catch (error) {
                console.error('时间格式化错误:', error);
                return '';
            }
        },
        //预览图片
        previewImage(idx) {
            let { videoDetail } = this;
            let urls = videoDetail.videoImage.map((item) => {
                return item;
            });
            uni.previewImage({
                current: idx,
                urls: urls
            });
        },
        //获取短视频详情
        getVideoInfo() {
            let { video_id } = this;
            let _this = this;
            let param = {};
            param.data = {};
            param.url = 'v3/video/front/video/playingPage';
            param.method = 'GET';
            param.data.videoId = this.video_id;
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    this.loadingState = 'no_more_data';
                    this.goodsList = res.data.goodsList;

                    // 处理图文轮播图宽高适配
                    let w = 375; //默认宽沾满 750rpx
                    let h = (res.data.videoInfo.height / res.data.videoInfo.width) * 375;
                    let maxHeight = h > 500 ? 500 : h; //高最大为 1000rpx
                    res.data.videoInfo.width = w * 2;
                    res.data.videoInfo.height = h * 2;
                    res.data.videoInfo.maxHeight = maxHeight * 2;

                    this.videoDetail = res.data.videoInfo;
                    this.member_avatar = res.data.memberAvatar;
                    if (this.videoDetail.state == 1) {
                        this.loadingShow = true;
                    } else if (this.videoDetail.state == 2) {
                        //更新视频点击量
                        this.loadingShow = true;
                        this.updateVideoClick();
                    } else if (this.videoDetail.state == 3) {
                        uni.showModal({
                            title: '',
                            content: this.$L('审核失败，') + this.videoDetail.remark,
                            showCancel: true,
                            confirmText: this.$L('重新编辑'),
                            confirmColor: '#FC1C1C',
                            success: (res) => {
                                if (res.confirm) {
                                    this.isShow = true;
                                    _this.$Router.replace({
                                        path: '/extra/graphic/graphicRelease',
                                        query: {
                                            video_id: this.video_id,
                                            roleType: this.roleType,
                                            index: this.index
                                        }
                                    });
                                } else {
                                    _this.$Router.back(1);
                                }
                            }
                        });
                    } else if (this.videoDetail.state == 4) {
                        if (!this.videoDetail.isSelf) {
                            this.$api.msg(this.$L('该图文已禁止显示'));
                            this.$Router.back(1);
                        } else {
                            uni.showModal({
                                title: '',
                                content: this.$L('禁止显示，') + this.videoDetail.remark,
                                showCancel: true,
                                confirmText: this.$L('重新编辑'),
                                cancelText: this.$L('取消'),
                                confirmColor: '#FC1C1C',
                                success: (res) => {
                                    if (res.confirm) {
                                        this.isShow = true;
                                        _this.$Router.replace({
                                            path: '/extra/graphic/graphicRelease',
                                            query: {
                                                video_id: this.video_id
                                            }
                                        });
                                    } else {
                                        _this.$Router.back(1);
                                    }
                                }
                            });
                        }
                    }
                    this.authorDetail = res.data.authorInfo;
                } else if (res.state == 267) {
                    uni.showModal({
                        title: this.$L('提示'),
                        content: res.msg,
                        confirmText: this.$L('确定'),
                        cancelText: this.$L('取消'),
                        success: (res) => {
                            if (res.confirm) {
                                // uni.navigateBack()
                                _this.$Router.back(1);
                            } else {
                                // uni.navigateBack()
                                _this.$Router.back(1);
                            }
                        }
                    });
                }
            });
        },
        //更新视频点击量
        updateVideoClick() {
            let param = {};
            param.data = {};
            param.url = 'v3/video/front/video/updateClickNum';
            param.method = 'POST';
            param.data.videoId = this.video_id;
            this.$request(param).then((res) => {
                if (res.state == 200) {
                }
            });
        },
        goodsModelClose() {
            this.$refs.goodsModel.close();
        },
        //点赞事件
        like() {
            if (this.videoDetail.state == 3) {
                this.$api.msg(this.$L('该图文未审核通过,不能点赞哦～'));
                return;
            } else if (this.videoDetail.state == 4) {
                this.$api.msg(this.$L('该图文已下架,不能点赞哦～'));
                return;
            } else if (this.videoDetail.state == 1) {
                this.$api.msg(this.$L('该图文正在审核中,不能点赞哦～'));
                return;
            }
            let { video_id, videoDetail } = this;
            if (this.hasLogin) {
                let param = {};
                param.data = {};
                param.url = 'v3/video/front/video/clickPraise';
                param.method = 'POST';
                param.data.videoId = this.video_id;
                this.$request(param).then((res) => {
                    if (res.state == 200) {
                        if (res.state == 200) {
                            videoDetail.isLike = videoDetail.isLike == true ? false : true;
                            videoDetail.likeNum = res.data.likeNum;
                            this.videoDetail = videoDetail;
                            if (this.preUpdateIndex > -1) {
                                uni.$emit('updateLike', videoDetail.isLike);
                            }
                        } else {
                            uni.showToast({
                                title: res.msg,
                                icon: 'none'
                            });
                        }
                    }
                });
            } else {
                this.$refs.loginPop.openLogin('no_replace');
            }
        },
        openComment() {
            if (this.videoDetail.state == 3) {
                this.$api.msg(this.$L('该图文未审核通过,不能评论哦～'));
                return;
            } else if (this.videoDetail.state == 4) {
                this.$api.msg(this.$L('该图文已下架,不能评论哦～'));
                return;
            } else if (this.videoDetail.state == 1) {
                this.$api.msg(this.$L('该图文正在审核中,不能评论哦～'));
                return;
            }
            this.$refs.commentModel.open();
        },
        changePop(e) {
            if (e.show) {
                this.$refs.comment.getCommentList();
            }
        },
        //关闭评论
        closeComment() {
            this.$refs.commentModel.close();
        },
        // 更新评论数量
        updateCommentNum(val) {
            this.videoDetail.commentNum = val;
        },
        //关闭分享
        closeShare() {
            this.shareWrap = false;
            this.showWeiXinBrowerTip = false; //微信浏览器提示层
        },

        //分享 type：分享类型 0 图文 2图片，scene 场景 WXSceneSession：分享朋友  WXSenceTimeline：分享朋友圈
        sldShare(type, scene) {
            let { videoDetail, video_id } = this;
            let shareData = {};
            if (type == 0) {
                shareData.href = getApp().globalData.apiUrl + 'extra/graphic/graphicDetail?video_id=' + video_id;
                shareData.title = videoDetail.videoName;
                shareData.summary =
                    this.$L('我正在看') + this.authorDetail.memberNickname ? this.authorDetail.memberNickname : this.authorDetail.memberName + this.$L('的精彩内容，快来围观~');
                shareData.imageUrl = videoDetail.videoImage;
            }
            this.$weiXinAppShare(type, scene, shareData);
            this.closeShare(); //关闭分享
        },

        //浏览器分享
        sldShareBrower(type) {
            let { videoDetail, video_id } = this;
            //展示分享提示
            this.showWeiXinBrowerTip = true;
            this.shareWrap = false;

            this.$WXBrowserShareThen(type, {
                title: videoDetail.videoName,
                desc: this.$L('我正在看') + this.authorDetail.memberNickname ? this.authorDetail.memberNickname : this.authorDetail.memberName + this.$L('的精彩内容，快来围观~'),
                link: getApp().globalData.apiUrl + 'extra/graphic/graphicDetail?video_id=' + video_id,
                imgUrl: videoDetail.videoImage[0]
            });
        },

        //进入作者页面
        goLiveUserCenter(e) {
            let author_id = e.currentTarget.dataset.authorid;
            let page = getCurrentPages();
            let len = page.length;
            if (len > 4) {
                this.isShow = true;
                this.$Router.replace({
                    path: '/extra/user/my',
                    query: {
                        author_id
                    }
                });
            } else {
                this.isShow = true;
                this.$Router.push({
                    path: '/extra/user/my',
                    query: {
                        author_id
                    }
                });
            }
        },

        showShare() {
            if (this.videoDetail.state != 2) {
                this.$api.msg(this.$L('该作品未审核通过,无法分享～'));
                return;
            }

            // #ifdef H5
            if (this.isWeiXinBrower) {
                this.shareWrap = true;
            }
            // #endif
            // #ifdef APP-PLUS || MP-WEIXIN
            this.shareWrap = true;
            // #endif
        },
        delGraphic(type) {
            switch (type) {
                case 'open': {
                    this.$refs.popup.open();
                    break;
                }
                case 'confirm': {
                    this.$refs.popup.close();
                    let param = {};
                    param.data = {};
                    param.data.videoId = this.video_id;
                    param.url = 'v3/video/front/video/delVideo';
                    param.method = 'POST';
                    this.$request(param).then((res) => {
                        if (res.state == 200) {
                            if (this.this.preUpdateIndex > -1) {
                                uni.$emit('updateState');
                            }
                            this.$api.msg(res.msg);
                            this.$Router.back(1);
                        } else {
                            this.$api.msg(res.msg);
                        }
                    });
                }
            }
        },
        //关注、取消关注事件
        collect() {
            let { authorDetail } = this;
            if (this.hasLogin) {
                let param = {};
                param.data = {};
                param.method = 'POST';
                param.data.authorId = authorDetail.authorId;
                if (authorDetail.isFollow) {
                    param.url = 'v3/video/front/video/cancelFollow';
                } else {
                    param.url = 'v3/video/front/video/followAuthor';
                }
                this.$request(param).then((res) => {
                    if (res.state == 200) {
                        authorDetail.isFollow = authorDetail.isFollow == true ? false : true;
                        this.$api.msg(res.msg);
                        this.getVideoInfo();
                    } else {
                        uni.showToast({
                            title: res.msg,
                            icon: 'none'
                        });
                    }
                });
            } else {
                this.$refs.loginPop.openLogin('no_replace');
            }
        }
    }
};
</script>

<style lang="scss">
.uni-nav-bar-text {
    font-size: 32rpx !important;
}

.page {
    width: 100%;
    min-height: 100vh;
    margin: 0;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    padding-bottom: env(safe-area-inset-bottom);
    background-attachment: fixed;
}
.divider {
    height: 0.5px;
    background-color: rgba(0, 0, 0, 0.1);
    width: 92%;
    margin: 0 auto;
}

.gra_user {
    position: relative;
    display: flex;
    padding: 20rpx 4%;
    align-items: center;
    justify-content: space-between;
    .user_left {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        column-gap: 20rpx;
    }
    .avator {
        width: 78rpx;
        height: 78rpx;
        border-radius: 50%;
        margin-left: 8rpx;
        background-size: cover;
        background-position: center center;
        background-repeat: no-repeat;
        background-color: #f7f7f7;
    }
    .mem_info {
        display: flex;
        align-items: center;
        justify-content: center;
        column-gap: 20rpx;
        .name {
            max-width: 350rpx;
            font-size: 32rpx;
            font-family: PingFang;
            font-weight: bold;
            line-height: 32rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }

    .level {
        padding: 0 20rpx;
        height: 40rpx;
        border-radius: 20rpx;
        border: 1px solid #000;
        display: flex;
        align-items: center;
        justify-content: center;
        .vip-image {
            width: 30rpx;
            height: 30rpx;
        }
        .lima-image {
            width: 60rpx;
        }
    }
    .uni-navbar__content_view {
        position: absolute;
        right: 30rpx;
        top: 50%;
        transform: translateY(-50%);
        /* #ifndef APP-NVUE */
        display: flex;
        /* #endif */
        align-items: center;
        flex-direction: row;
        // background-color: #FFFFFF;
    }
    .follow-block {
        display: flex;
        justify-content: center;
        align-items: center;

        .follow_btn {
            background-color: transparent;
            color: #000;
            border: 1px solid;
            border-color: rgba(89, 87, 87, 1);
            border-radius: 25rpx;
            padding: 0 30rpx;
            font-size: 24rpx;
            text-align: center;
            cursor: pointer;
            height: 50rpx;
            line-height: 50rpx;
            &.follow {
                color: rgba(0, 0, 0, 0.3);
                border-color: rgba(0, 0, 0, 0.3);
            }
        }
    }
}
.gra_text {
    margin-top: 20rpx;
    padding-top: 10rpx;
    padding-left: 30rpx;
    padding-right: 30rpx;
    .gra_title {
        padding-bottom: 40rpx;
        .title {
            font-size: 38rpx;
            font-family: PingFang;
            font-weight: bold;
            color: #000;
            margin-bottom: 16rpx;
        }

        .gra_info {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            .info_v {
                font-size: $fs-s;
                font-family: PingFang;
                font-weight: 500;
                color: rgba(0, 0, 0, 0.5);
            }
            .c_line {
                width: 1px;
                height: 20rpx;
                background-color: rgba(0, 0, 0, 0.5);
                margin: 0 10rpx;
            }
        }
    }
    .tag_part {
        width: 100%;
        margin: 0 auto;
        padding: 0rpx 0rpx 20rpx;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .tag {
            display: inline-block;
            padding: 0rpx 20rpx 0 20rpx;
            height: 50rpx;
            border-radius: 25rpx;
            color: #fff;
            font-size: $fs-base;
            background: rgba(178, 161, 240, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            .icon {
                height: 30rpx;
                width: 30rpx;
                line-height: 30rpx;
                text-align: center;
                margin-right: 5px;
                border-radius: 50%;
                color: #9794ff;
                background-color: #fff;
            }
        }
    }
    .gra_desc {
        padding-bottom: 20rpx;
        border-bottom: 1px solid #f7f7f7;

        .gra_title {
            font-size: 38rpx;
            font-family: PingFang;
            font-weight: bold;
            color: #333333;
            margin-bottom: 16rpx;
        }

        .gra_intro {
            font-size: 30rpx;
            font-family: PingFang;
            font-weight: 500;
            color: #666666;
        }
    }

    .gra_content {
        color: #333333;
        line-height: 42rpx;
        font-size: 28rpx;
        font-family: PingFang;
        font-weight: 400;
        margin-top: 20rpx;
        word-break: break-all;
    }
}
.img_list {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 92%;
    margin: 0 auto;
    row-gap: 20rpx;
    padding-bottom: calc(env(safe-area-inset-bottom) + 140rpx);
    padding-top: 40rpx;
    .gra_pic_item {
        border-radius: 20rpx;
        background-position: center center;
        background-repeat: no-repeat;
        background-size: cover;
        width: 100%;
        aspect-ratio: 1 / 1;
    }
}

.shareButton {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: transparent;
    border-radius: 0;
    height: auto;
    line-height: unset;
    padding: 0;
    margin: 0;

    &::after {
        border-width: 0;
    }
}

.gra_bottom {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: calc(120rpx + env(safe-area-inset-bottom));
    padding: 20rpx 20rpx;
    box-shadow: 0px 0px 29rpx 1px rgba(86, 86, 86, 0.08);
    padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    .left {
        width: 350rpx;
        .input_collect {
            width: 100%;
            .input {
                width: 100%;
                display: flex;
                align-items: center;
                justify-content: flex-start;
                background-color: rgba(242, 242, 242, 1);
                border-radius: 50px;
                padding: 0 25rpx;
                height: 80rpx;
                cursor: pointer;
                .image {
                    width: 32rpx;
                    height: 32rpx;
                    margin-right: 10rpx;
                }

                .text {
                    font-size: $fs-base;
                    color: rgba(153, 153, 153, 1);
                }
            }
        }
    }
    .right {
        display: flex;
        align-items: center;
        justify-content: space-evenly;
        flex: 1;
        .collect {
            width: 78rpx;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;

            image {
                width: 42rpx;
                height: 42rpx;
            }
            .num {
                font-size: 24rpx;
                color: rgba(0, 0, 0, 0.5);
                margin-top: 6rpx;
            }
            .messageNum {
                font-size: 22rpx;
                background-color: var(--color_video_main);
                color: #fff;
                border-radius: 16rpx;
                position: absolute;
                right: -4rpx;
                top: -4rpx;
                padding: 2rpx 6rpx;
            }
            .share_img {
                width: 62rpx;
                height: 62rpx;
            }
        }

        .islike {
            background: var(--color_video_main) !important;
        }
    }
}

.goods_list_con {
    border-radius: 5px 5px 0;
}

.select-wrap {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    margin: 0 auto;
    width: 750rpx;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.45);
    z-index: 9999;

    .share-mode {
        position: absolute;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;

        .share-img {
            width: 72vw;
            border-radius: 20rpx;
            overflow: hidden;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;

            image {
                width: 100%;
                height: 0;
                border-radius: 20rpx;
            }
        }

        .ul {
            display: flex;
            align-items: center;
            width: 100%;
            justify-content: space-evenly;
        }

        .item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            cursor: pointer;
            border: none;
            margin: 0;
            padding: 0;
            line-height: 1;
            background-color: transparent;
            height: 180rpx;
            width: 122rpx;

            &::after {
                border: none;
            }

            image {
                width: 112rpx;
                height: 112rpx;
            }

            text {
                color: #fff;
                font-size: 24rpx;
                margin-top: 30rpx;
            }
        }
    }

    .close {
        width: 750rpx;
        height: 140rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        image {
            /* wx-1-start */
            /* #ifdef MP-WEIXIN */
            width: 26rpx;
            height: 26rpx;
            /* #endif */
            /* wx-1-end */
            /* #ifndef MP-WEIXIN */
            width: 18rpx;
            height: 18rpx;
            padding: 20rpx;
            /* #endif */
            opacity: unset;
        }

        uni-image > img {
            opacity: unset !important;
        }
    }
}
</style>
