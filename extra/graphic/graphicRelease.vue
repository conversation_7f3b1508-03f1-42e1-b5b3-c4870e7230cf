<template>
    <view :style="mix_diyStyle" class="page">
        <view class="main_graRe" :style="'background-image:url(' + imgUrl + 'default_bg.jpg)'">
            <KTabbar :title="$L('发布图文')" :showLeft="true" :bgColor="'transparent'" :bgImg="imgUrl + 'default_bg.jpg'" />
            <!-- 标题 -->
            <view class="title_part">
                <input class="input" autofocus :placeholder="$L('请输入文章标题不超过20字')" maxlength="20" v-model="title" />
            </view>
            <!-- 正文内容 -->
            <view class="content_part">
                <textarea class="content_con" :placeholder="$L('请输入正文内容~')" placeholder-class="placeholder" maxlength="500" v-model="content"></textarea>
                <!-- 字数统计 -->
                <view class="text_count">{{ textCount }}/500</view>
            </view>
            <!-- 图片 -->
            <view class="pic_part">
                <view class="pic_part_sel">
                    <view
                        class="cover_image"
                        v-for="(item, index) in imageList"
                        :key="index"
                        :style="{ backgroundImage: 'url(' + item.url + ')' }"
                        @click.stop="previewImage(index)"
                    >
                        <view class="close_img" @click.stop="delUploadImg(index)">
                            <uv-icon class="icon" name="close-circle" size="18" color="#FFF"></uv-icon>
                        </view>
                    </view>
                    <view class="cover" @tap="uploadCover" v-if="imageList.length < 9">
                        <uv-icon name="plus" color="rgba(0,0,0,0.5)" size="28"></uv-icon>
                        <text class="cover_tip">添加图片</text>
                    </view>
                </view>
            </view>
            <!-- <view class="desc_part">
                <view class="desc_title">
                    <svgGroup type="to_brief" class="desc_title_img" width="37" height="37" px="rpx" :color="diyStyle_var['--color_video_main']"></svgGroup>
                    <text class="title">{{ $L('图文简介') }}</text>
                </view>
                <textarea
                    class="desc_con"
                    :placeholder="$L('请输入简介，最多30字(必填)')"
                    placeholder-style="font-size:26rpx;color:#949494;z-index:1,zoom:1"
                    maxlength="30"
                    v-model="desc"
                ></textarea>
            </view> -->
            <!-- 话题 -->
            <view class="avator">
                <view class="left">
                    <text class="con">{{ $L('参与话题') }}</text>
                </view>
                <view class="right" @click="selectTopic">
                    <view class="picker">{{ $L('选择话题') }}</view>
                    <image class="arrow_r" :src="imgUrl + 'svideo/mem_arrow_r.png'"></image>
                </view>
            </view>
            <!-- 选中的话题 -->
            <view class="tag_part" v-if="selectLabel.themeId != undefined && selectLabel.themeId">
                <view class="tag">
                    <text class="icon">#</text>
                    <text class="tag_text">{{ selectLabel.themeName }}</text>
                    <image class="close" :src="imgUrl + 'input_clear.png'" @click="selectLabel = {}"></image>
                </view>
            </view>
            <view class="live_btn">
                <text @tap="$frequentyleClick(startGraphic)" class="live_btn_text btn_enable">{{ $L('发布') }}</text>
            </view>
            <!-- 选择话题 -->
            <uni-popup ref="tagSelectPop" type="dialog">
                <view class="live_user_tab_content">
                    <view class="live_user_tab_item" v-for="(item, index) in label_list" :key="index" @tap="selectLabel = item">
                        <text class="live_user_tab_text">{{ item.labelName }}</text>
                    </view>
                </view>
            </uni-popup>
            <!-- 退出发布提示 -->
            <uni-popup ref="popBack" type="dialog">
                <uni-popup-dialog
                    type="input"
                    :title="$L('确定退出发布')"
                    :content="$L('退出后编辑过的信息将不会保存')"
                    :duration="2000"
                    @confirm="popCon('confirm')"
                ></uni-popup-dialog>
            </uni-popup>
        </view>
    </view>
</template>

<script>
// #ifdef H5
import EXIF from '@/utils/exif.js';
// #endif
import { mapState } from 'vuex';
import KTabbar from '@/components/ktabbar.vue';
import videoReleaseGoods from '../component/video/videoReleaseGoods.vue';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue';
import { imgUrl } from '../../utils/config';
export default {
    components: {
        KTabbar,
        videoReleaseGoods,
        uniPopup,
        uniPopupDialog
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            video_id: '',
            title: '', // 标题
            content: '', // 正文内容

            imageList: [],
            imageSize: [], //图片对应的宽高
            desc: '',
            setting: {},
            label_list: [],
            roleType: 1, // 默认会员 1   2：商家
            selGoods: [], //已选中的商品列表
            selectLabel: {},
            onOff: true,
            graTitle: '发布',
            index: null, //当前操作的图文下标
            firstOpen: true, //是否第一次加载页面
            storeId: null,
            token: ''
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        if (this.$Route.query.video_id) {
            this.graTitle = this.$L('编辑');
            this.video_id = this.$Route.query.video_id;
            this.getVideoDetail();
        }
        this.roleType = this.$Route.query.roleType ? this.$Route.query.roleType : 1;
        this.storeId = this.$Route.query.storeId;
        this.index = this.$Route.query.index;
        uni.removeStorageSync('selGoods');
        if (this.firstOpen) {
            this.initData();
        }
        // #ifdef H5
        setTimeout(() => {
            uni.setNavigationBarTitle({
                title: this.graTitle
            });
        }, 0);

        // #endif
    },
    onShow: function () {
        if (!this.firstOpen) {
            this.initData();
        } else {
            this.firstOpen = false;
        }
    },

    onBackPress() {
        this.$refs.popBack.open();
    },
    computed: {
        ...mapState(['userInfo']),
        textCount() {
            return this.content.length || 0;
        }
    },
    methods: {
        initData() {
            // this.getSetting();
        },
        // 删除图片
        delUploadImg(index) {
            this.imageList.splice(index, 1);
        },
        //图片预览
        previewImage(index) {
            const urls = this.imageList.map((e) => e.url) || [];
            uni.previewImage({
                urls: urls,
                current: index
            });
        },
        // 选择话题
        selectTopic() {
            this.$Router.push({ path: '/extra/topic/list', query: { action: 'select' } });
        },
        setTopic(value) {
            console.log('选择的话题', value);
            this.selectLabel = value;
        },
        //获取短视频详情
        getVideoDetail() {
            let { video_id } = this;

            let param = {};
            param.url = 'v3/video/front/video/detail';
            param.method = 'GET';
            param.data = {};
            param.data.videoId = video_id;
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    let result = res.data;
                    this.title = result.videoName;
                    this.desc = result.introduction;
                    this.selectLabel.labelId = result.labelId;
                    this.selectLabel.labelName = result.labelName;
                    result.videoImageUrl.forEach((item, index) => {
                        this.imageList.push({
                            url: item,
                            path: result.videoImage.split(',')[index]
                        });
                    });
                    if (res.data.imageInfo) {
                        this.imageSize = JSON.parse(res.data.imageInfo);
                    }
                    this.content = result.videoContent;
                    this.selGoods = result.goodsList;
                    uni.setStorageSync('selGoods', this.selGoods);
                }
            });
        },

        // 获取设置信息
        getSetting() {
            let param = {};
            param.url = 'v3/video/front/video/setting/getSettingList';
            param.method = 'GET';
            param.data = {};
            param.data.str = 'bind_goods_num,member_bind_goods';
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    let result = res.data;
                }
            });
        },
        // 选封面
        uploadCover() {
            let { cover } = this;
            let that = this;
            uni.chooseImage({
                count: 9,
                sizeType: ['original', 'compressed'],
                //可选择原图或压缩后的图片
                success: (res) => {
                    uni.showLoading();
                    for (let index = 0; index < res.tempFilePaths.length; index++) {
                        if (index + this.imageList.length == 9) {
                            uni.hideLoading();
                            uni.showToast({
                                title: that.$L('最多上传9张图片！'),
                                icon: 'none'
                            });
                            break;
                        }

                        uni.uploadFile({
                            url: getApp().globalData.apiUrl + 'v3/oss/front/upload',
                            filePath: res.tempFilePaths[index],
                            name: 'file',
                            formData: {
                                source: 'goods'
                            },
                            header: {
                                Authorization: 'Bearer ' + that.userInfo.access_token
                            },
                            success: (uploadFileRes) => {
                                let result = JSON.parse(uploadFileRes.data);
                                if (result.state == 200) {
                                    this.imageList.push({ url: result.data.url, path: result.data.path });
                                    this.imageSize.push({ width: result.data.width, height: result.data.height });
                                    // #ifdef H5
                                    /** setSize方法 处理部分机型像素过高时图片旋转导致的宽高值不正确问题 */
                                    this.setSize(this.imageList.length - 1, res.tempFilePaths[index]);
                                    // #endif
                                }
                            },
                            complete: () => {
                                uni.hideLoading();
                            }
                        });
                    }
                }
            });
        },

        // #ifdef H5
        async setSize(index, url) {
            let Orientation = 0; // 0: 正常无旋转 1: 横屏拍摄 6:竖屏拍摄照片旋转
            //获取图片META信息
            await this.getImageTag(url, 'Orientation', function (e) {
                if (e != undefined) Orientation = e;
            });
            if (Orientation > 1) {
                //竖屏拍照时照片旋转的话则图片的宽高值互换
                let w = this.imageSize[index].width;
                let h = this.imageSize[index].height;
                this.imageSize[index].width = h;
                this.imageSize[index].height = w;
            }
        },
        getImageTag(file, tag, suc) {
            if (!file) return 0;
            return new Promise((resolve, reject) => {
                /* eslint-disable func-names */
                // 箭头函数会修改this，所以这里不能用箭头函数
                let imgObj = new Image();
                imgObj.src = file;
                uni.getImageInfo({
                    src: file,
                    success(res) {
                        EXIF.getData(imgObj, function () {
                            EXIF.getAllTags(this);
                            let or = EXIF.getTag(this, 'Orientation');
                            resolve(suc(or));
                        });
                    }
                });
            });
        },
        // #endif
        //发布
        startGraphic() {
            let { title, desc, content, imageList, imageSize, selectLabel, video_id } = this;

            if (title.trim() == '') {
                uni.showToast({
                    title: this.$L('请输入标题'),
                    icon: 'none'
                });
                return;
            }

            if (imageList.length == 0) {
                uni.showToast({
                    title: this.$L('请选择上传图片'),
                    icon: 'none'
                });
                return;
            }

            // if (desc == '') {
            //     uni.showToast({
            //         title: this.$L('请输入图文简介'),
            //         icon: 'none'
            //     });
            //     return;
            // }

            // if (content == '') {
            //     uni.showToast({
            //         title: this.$L('请输入正文内容'),
            //         icon: 'none'
            //     });
            //     return;
            // }
            let param = {};
            param.data = {};
            param.method = 'POST';
            let selGoodsIds = [];
            this.selGoods.map((selGoodsItem) => {
                if (video_id != '') {
                    selGoodsIds.push(selGoodsItem.goodsId);
                } else {
                    if (selGoodsItem.isSel == true) {
                        selGoodsIds.push(selGoodsItem.goodsId);
                    }
                }
            });
            let selImagePath = imageList.map((i) => i.path);
            param.data = {
                introduction: desc,
                themeId: selectLabel.themeId,
                videoName: title,
                content,
                videoImage: selImagePath.join(','),
                goodsIds: selGoodsIds.join(','),
                type: 2,
                width: imageSize[0].width,
                height: imageSize[0].height,
                imageInfo: JSON.stringify(imageSize)
            };
            if (video_id != '') {
                param.url = 'v3/video/front/video/editVideo';
                param.data.videoId = video_id;
            } else {
                param.url = 'v3/video/front/video/releaseVideo';
            }
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    // if(this.video_id){
                    // 	// 编辑成功则修改图文列表当前项的审核状态
                    // 	uni.$emit('updateState',this.index)
                    // }

                    uni.showToast({
                        title: this.video_id ? this.$L('编辑成功！') : this.$L('发布成功！'),
                        icon: 'none',
                        duration: 1000
                    });

                    let pages = getCurrentPages();
                    let flag = true;
                    let preIndex = '';
                    pages.forEach((item, index) => {
                        // 处理返回列表页 后退跳转层级重复问题
                        if (item.route == 'extra/user/my') {
                            if (!preIndex) {
                                preIndex = index;
                            }
                        } else if (item.route == 'extra/graphic/graphicRelease') {
                            flag = false;
                            setTimeout(() => {
                                uni.$emit('updateState');
                                this.$Router.back(index - preIndex);
                            }, 1000);
                        }
                    });
                    if (flag) {
                        let prevPage = pages[pages.length - 2]; //上一个页面
                        if (prevPage) {
                            prevPage.$vm.initData();
                        }
                        setTimeout(() => {
                            uni.$emit('updateState');
                            this.$Router.back(1);
                        }, 1000);
                    }
                } else {
                    this.$api.msg(res.msg);
                }
            });
        }
    }
};
</script>

<style lang="scss">
page {
    background: #f5f5f5;
    .placeholedr {
        font-size: 26rpx;
        color: rgba(0, 0, 0, 0.5);
        z-index: 1;
        zoom: 1;
    }
}

.main_graRe {
    width: 100%;
    min-height: 100vh;
    margin: 0;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    padding-bottom: env(safe-area-inset-bottom);
    background-attachment: fixed;

    .title_part {
        width: 92%;
        margin: 0 auto;
        padding: 40rpx 0rpx 20rpx 0rpx;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        box-sizing: content-box;
        border-bottom: 0.5px solid;
        border-color: rgba(0, 0, 0, 0.1);
        .input {
            width: 100%;
            color: #2d2d2d;
            font-size: $fs-base;
            border: none;
            outline: none;
        }
        textarea {
            width: 100%;
            padding-left: 11rpx;
            color: #2d2d2d;
            font-size: 30rpx;
        }
    }

    .content_part {
        width: 92%;
        margin: 0 auto;
        padding: 40rpx 0rpx 10rpx 0rpx;
        box-sizing: border-box;
        .content_con {
            height: 500rpx;
            font-size: $fs-base;
            line-height: 1.4;
            width: 100%;
        }
        .text_count {
            margin-top: 10rpx;
            width: 100%;
            text-align: right;
            font-size: $fs-m;
            color: #949494;
        }
    }
    .pic_part {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        width: 92%;
        margin: 0 auto;
        box-sizing: border-box;
        padding: 20rpx 0;

        .pic_part_title {
            display: flex;
            align-items: center;

            .title {
                color: #2d2d2d;
                font-size: 30rpx;
            }
        }

        .pic_part_sel {
            margin-top: 26rpx;
            display: flex;
            overflow: auto;
            width: 100%;
            flex-wrap: wrap;
            row-gap: 20rpx;
            column-gap: calc(8% / 3);
            .cover {
                width: 23%;
                aspect-ratio: 1 / 1;
                background: rgb(255, 255, 255);
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                position: relative;
                .cover_icon {
                    width: 40rpx;
                    height: 40rpx;
                }

                .cover_tip {
                    font-size: $fs-m;
                    color: #949494;
                    margin-top: 10rpx;
                }
            }

            .cover_image {
                position: relative;
                width: 23%;
                aspect-ratio: 1 / 1;
                background: rgb(0, 0, 0, 0.3);
                background-size: cover;
                background-position: center;
                background-repeat: no-repeat;
                .close_img {
                    width: 50rpx;
                    height: 50rpx;
                    position: absolute;
                    right: 0;
                    top: 0rpx;
                    z-index: 5;
                    border-radius: 0rpx 0 0rpx 50rpx;
                    background-color: #9794ff;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    .uv-icon {
                        margin-top: -8rpx;
                        margin-right: -8rpx;
                    }
                }
            }
        }
    }
    .avator {
        width: 92%;
        margin: 0 auto;
        padding: 40rpx 0rpx 20rpx 0rpx;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        border-top: 0.5px solid rgba(0, 0, 0, 0.1);
        margin-top: 25rpx;
        .left {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;

            .con {
                color: #2d2d2d;
                font-size: $fs-base;
            }
        }

        .right {
            min-width: 300rpx;
            display: flex;
            flex-direction: row;
            justify-content: flex-end;
            align-items: center;
            height: 100%;
            overflow: hidden;
            .picker {
                color: #949494;
                font-size: 24rpx;
                text-align: right;
            }

            .arrow_r {
                width: 40rpx;
                height: 42rpx;
                margin-left: 6rpx;
            }
        }
    }
    .tag_part {
        width: 92%;
        margin: 0 auto;
        padding: 0rpx 0rpx 20rpx;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .tag {
            display: inline-block;
            padding: 0rpx 0 0 20rpx;
            height: 50rpx;
            border-radius: 25rpx;
            color: #fff;
            font-size: $fs-base;
            background: var(--color_video_main);
            background: linear-gradient(141.55deg, #9794ff 0%, #ffc7c8 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            .icon {
                height: 30rpx;
                width: 30rpx;
                line-height: 30rpx;
                text-align: center;
                margin-right: 5px;
                border-radius: 50%;
                color: #9794ff;
                background-color: #fff;
            }
            .close {
                width: 46rpx;
                height: 46rpx;
                margin-left: 10rpx;
            }
        }
    }

    .live_btn {
        width: 92%;
        margin: 0 auto;
        margin-top: 80rpx;
        .btn_enable {
            background: #494949 !important;
            box-shadow: 0px 3px 10px 0px #aaaaaa;
        }

        .live_btn_text {
            width: 100%;
            font-size: $fs-base;
            color: #fff;
            line-height: 90rpx;
            height: 90rpx;
            background: #aaa;
            border-radius: 45rpx;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
        }
    }

    .live_user_tab_content {
        max-height: 900rpx;
        overflow: auto;
    }
    .desc_part {
        background: #fff;
        border-radius: 15rpx;
        margin: 26rpx 20rpx;
        box-sizing: border-box;
        padding: 20rpx;

        .desc_title {
            display: flex;
            align-items: center;

            .title {
                color: #2d2d2d;
                font-size: 32rpx;
            }
        }

        .desc_con {
            color: #949494;
            font-size: 28rpx;
            line-height: 32rpx;
            margin-top: 10rpx;
            width: 100%;
            padding-left: 12rpx;
            height: 80rpx;
        }
    }
}
</style>
