import Vue from 'vue'
import store from './store'
import App from './App'
import router from './router'
import { RouterMount } from 'uni-simple-router'
import request from './utils/request'
import dayjs from 'dayjs'
import seller_request from './utils/seller_request.js'
import loginPop from '@/components/loginPop/loginPop.vue'
import {
	checkPaginationHasMore, checkSpace, replaceConByPosition, getPartNumber, checkMobile, checkPwd, loginGoPage, callNativeLogin,callNativeLogout,isAppInset,
	formatW, checkEmail, diyNavTo, setCookie, setStoreIsCookie, weiXinBrowerShare, getQueryVariable, weiXinBrowerPay, sldCommonTip, getLoginClient, formatPercent, setPointIsCookie,
	frequentyleClick, back, isShowTime, formatChatTime, checkTel, checkIdentity, WXBrowserShareThen, wxLoginClient, prePage, msg, videoPlayNav, livePlayNav, getUnLoginCartParam, checkImgCode, isTabPage, preventMutiClick, saveBusinessToken
} from './utils/common.js'
import { getCurLanguage, weiXinAppShare, isWeiXinBrower, base64Encrypt } from './utils/base.js'
import { sldStatEvent, getLocation } from './utils/stat.js'
import svgGroup from '@/diyStyle/svgGroup.vue'
import diyStyle from '@/diyStyle/index.js'
// #ifdef H5
import vconsole from 'vconsole'
if(process.env.NODE_ENV=='development'){
	const console = new vconsole()
}
// #endif

Vue.component('loginPop', loginPop)
Vue.component('svgGroup', svgGroup)
Vue.use(diyStyle)

Vue.config.productionTip = false
Vue.prototype.dayjs = dayjs
Vue.prototype.$store = store;
Vue.prototype.$request = request;
Vue.prototype.$seller_request = seller_request
Vue.prototype.$checkPaginationHasMore = checkPaginationHasMore;
Vue.prototype.$checkSpace = checkSpace;
Vue.prototype.$replaceConByPosition = replaceConByPosition;
Vue.prototype.$getPartNumber = getPartNumber;
Vue.prototype.$checkMobile = checkMobile;
Vue.prototype.$checkPwd = checkPwd;
Vue.prototype.$loginGoPage = loginGoPage;
Vue.prototype.$formatW = formatW;
Vue.prototype.$checkEmail = checkEmail;
Vue.prototype.$diyNavTo = diyNavTo;
Vue.prototype.$setCookie = setCookie;
Vue.prototype.$formatPercent = formatPercent;
Vue.prototype.$setStoreIsCookie = setStoreIsCookie;
Vue.prototype.$setPointIsCookie = setPointIsCookie;
Vue.prototype.$frequentyleClick = frequentyleClick;
Vue.prototype.$L = getCurLanguage;
Vue.prototype.$api = { msg, prePage };
Vue.prototype.$isWeiXinBrower = isWeiXinBrower;
Vue.prototype.$weiXinBrowerShare = weiXinBrowerShare;
Vue.prototype.$weiXinAppShare = weiXinAppShare;
Vue.prototype.$getQueryVariable = getQueryVariable;
Vue.prototype.$weiXinBrowerPay = weiXinBrowerPay;
Vue.prototype.$sldCommonTip = sldCommonTip;
Vue.prototype.$getLoginClient = getLoginClient;
Vue.prototype.$wxLoginClient = wxLoginClient
Vue.prototype.$back = back;
Vue.prototype.$isShowTime = isShowTime;
Vue.prototype.$formatChatTime = formatChatTime;
Vue.prototype.$sldStatEvent = sldStatEvent;
Vue.prototype.$getLocation = getLocation;
Vue.prototype.$checkTel = checkTel
Vue.prototype.$checkIdentity = checkIdentity
Vue.prototype.$WXBrowserShareThen = WXBrowserShareThen
Vue.prototype.$videoPlayNav = videoPlayNav
Vue.prototype.$livePlayNav = livePlayNav
Vue.prototype.$base64Encrypt = base64Encrypt
Vue.prototype.$getUnLoginCartParam = getUnLoginCartParam
Vue.prototype.$checkImgCode = checkImgCode
Vue.prototype.$isTabPage = isTabPage
Vue.prototype.$preventMutiClick = preventMutiClick
Vue.prototype.$saveBusinessToken = saveBusinessToken
Vue.prototype.$appLogin = callNativeLogin
Vue.prototype.$appLogout = callNativeLogout
Vue.prototype.$isAppInset = isAppInset
App.mpType = 'app'


const app = new Vue({
	...App
})

//v1.3.5起 H5端 你应该去除原有的app.$mount();使用路由自带的渲染方式
// #ifdef H5
RouterMount(app, '#app');
// #endif
// #ifndef H5
app.$mount(); //为了兼容小程序及app端必须这样写才有效果
// #endif