
import request from './request'
import {getCurLanguage} from './base.js'
import store from '../store/index.js'

/** 
 * 判断分页是否还有数据
 */

export const checkPageHasMore = ({
	current,
	pageSize,
	total
}) => {
	return current * pageSize < total * 1;
}

/** 
 * 大数据统一处理
 */

export const initNum = (num) => {
	if (num * 1 > 9999) {
		return (num / 10000).toFixed(2) * 1 + 'w';
	}

	return num;
}

/** 
 * 颜色数组
 */
export const colorArray = ['#fb8615', '#f2f520', '#3eea02', '#41c3fd', '#0439eb', '#6d02de', '#e207a9', '#eb003b'];


//加入购物车事件(nvue页面引用common.js会报错，因此在live.js放一个)
export function addCartCommon(good,instance) {
	if (!instance.hasLogin) {
		let cart_list = {
			storeCartGroupList: [{
				promotionCartGroupList: [{
					cartList: [{
						buyNum: 1,
						goodsId: good.goodsId - 0,
						productId: good.defaultProductId||good.productId,
						productImage: good.goodsImage,
						goodsName: good.goodsName,
						isChecked: 1,
						productPrice: good.goodsPrice||good.productPrice,
						productStock: good.goodsStock||good.productStock
					}],
				}],
				storeId: good.storeId,
				storeName: good.storeName,
				checkedAll: true
			}],
			checkedAll: true,
			invalidList: []
		}
		//未登录加入本地缓存
		let local_cart_list = uni.getStorageSync('cart_list') //购物车列表本地缓存
		if (local_cart_list) { //如果不是第一次存储
			let tmp_list1 = []
			let tmp_list2 = []
			cart_list.storeCartGroupList.forEach(item => {
				item.promotionCartGroupList.forEach(item1 => {
					item1.cartList.forEach(item2 => {
						local_cart_list.storeCartGroupList.forEach(v => {
							v.promotionCartGroupList.forEach(v1 => {
								v1.cartList.forEach(v2 => {
									if (v2.productId == item2.productId && v.storeId == item.storeId) {
										tmp_list1.push(v)
									}
								})
							})
						})
						tmp_list2 = local_cart_list.storeCartGroupList.filter(v => {
							return v.storeId == item.storeId
						})
					})
				})
			})
			if (tmp_list1.length > 0 && tmp_list2.length > 0) { //同一店铺同一商品
				local_cart_list.storeCartGroupList.map(item => {
					item.promotionCartGroupList.map(item1 => {
						item1.cartList.map(item2 => {
							if (item2.productId == (good.defaultProductId||good.productId) && item.storeId ==good.storeId) {
								item2.buyNum += 1
							}
						})
					})
				})
			} else if (tmp_list1.length == 0 && tmp_list2.length > 0) { //同一店铺不同商品
				local_cart_list.storeCartGroupList.map(item => {
					if (item.storeId == good.storeId) {
						item.promotionCartGroupList.map(item2 => {
							item2.cartList.push(cart_list.storeCartGroupList[0].promotionCartGroupList[0].cartList[0])
						})
					}
				})
			} else { //不同店铺不同商品
				local_cart_list.storeCartGroupList.push(cart_list.storeCartGroupList[0])
			}
			// 未登录购物车展示数据
			uni.setStorage({
				key: 'cart_list',
				data: local_cart_list,
				success: function() {
					//更新购物车数量和购物车数据
				}
			});

		} else {
			uni.setStorage({
				key: 'cart_list',
				data: cart_list,
				success: function() {
					//更新购物车数量和购物车数据
				}
			});
		}
		uni.showToast({
			title: getCurLanguage('加入购物车成功！'),
			icon: 'none',
			duration: 700
		})
	} else { //已登录
		request({
			url: 'v3/business/front/cart/add',
			data: {
				productId: good.defaultProductId||good.productId,
				number: 1,
			},
			method: 'POST'
		}).then(res => {
			if (res.state == 200) {
				//更新购物车数量
				uni.showToast({
					title: res.msg,
					icon: 'none'
				})
			} else {
				uni.showToast({
					title: res.msg,
					icon: 'none'
				})
			}
		})
	}
}