import {
	lang_en
} from "../static/language/en.js";
import {
	lang_ko
} from "../static/language/ko.js";
import {
	curLang
} from './config.js'
//百度地图跳转
export function openNavigator(POIID) {
	let url = null
	if (typeof POIID == 'string') {
		url = encodeURIComponent(
			`https://nav-qdjc.nt-iot.cn/bb/v4/c-h5v4/main/v4.0.2/index.html?sign=72c2035ef12a437494cf0c7e17215e12&service=2&privatization=1&page=map&type=2&poiId=${POIID}`
		)

	} else {
		url = encodeURIComponent(
			`https://nav-qdjc.nt-iot.cn/bb/v4/c-h5v4/main/v4.0.2/index.html#/search?sign=72c2035ef12a437494cf0c7e17215e12&service=2&privatization=1&name=${POIID.keyName}`
		)
	}
	uni.navigateTo({
		url: "/pages/map/index?url=" + url
	})
}
// 判断当前时间是否在商家营业时间段内
export function checkTimeRange(days, startTime, endTime) {
	let today = new Date();
	var year = today.getFullYear();
	var month = today.getMonth() + 1; // 月份是从0开始计数的，所以需要加1
	var day = today.getDate();
	let toweek = today.getDay() != 0 ? today.getDay() : 7; //默认周日是0，转换为7，和系统对应
	let weeks = days ? days.split(',') : null;
	// 星期1-7判断
	if (weeks && weeks.length && weeks.includes(String(toweek))) {
		// 时间点比较
		let nowTimeStamp = Date.now();
		if (!startTime || !endTime) {
			return false
		};
		// console.log({startTime, endTime})
		let startTimeStamp = new Date(year + '/' + month + '/' + day + ' ' + startTime).getTime();
		let endTimeStamp = new Date(year + '/' + month + '/' + day + ' ' + endTime).getTime();
		return nowTimeStamp > startTimeStamp ? (endTimeStamp > nowTimeStamp ? true : false) : false;
	} else {
		return false;
	}
}
//字符串转base64，文件流则调用Uint8Array方法处理后调用此方法
export function weBtoa(string) {
	var b64 = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",
		b64re = /^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/;
	string = String(string);
	var bitmap, a, b, c,
		result = "",
		i = 0,
		rest = string.length % 3; // To determine the final padding

	for (; i < string.length;) {
		if ((a = string.charCodeAt(i++)) > 255 ||
			(b = string.charCodeAt(i++)) > 255 ||
			(c = string.charCodeAt(i++)) > 255)
			throw new TypeError(
				"Failed to execute 'btoa' on 'Window': The string to be encoded contains characters outside of the Latin1 range."
			);

		bitmap = (a << 16) | (b << 8) | c;
		result += b64.charAt(bitmap >> 18 & 63) + b64.charAt(bitmap >> 12 & 63) +
			b64.charAt(bitmap >> 6 & 63) + b64.charAt(bitmap & 63);
	}

	// If there's need of padding, replace the last 'A's with equal signs
	return rest ? result.slice(0, rest - 3) + "===".substring(rest) : result;
}

/*
 * 获取当前语言或者指定语言下的数据 —— Object类型
 * 返回对象  语言数据对象
 * @kris-2024-12-09
 * */
export function getCurLanguage(key, lang) {
	const language = {
		'en': lang_en,
		'ko': lang_ko
	}
	let curData = '';
	if (lang) {
		// 指定语言---优先级最高
		if (lang == 'zh') {
			return key;
		}
		curData = language[lang][key]
	} else {
		// 全局默认语言
		if (curLang == 'zh') {
			return key;
		}
		curData = language[curLang][key]
	}
	return curData != undefined && curData ? curData : key; //语言包中缺少该数据 --- 此处不要翻译
}

/** 
 * APP微信分享功能
 * type 分享类型  0 图文 2 图片
 * scene 场景  WXSceneSession 分享朋友   WXSenceTimeline 分享朋友圈
 * shareData  分享数据数组 里面的参数分别如下：
 * 	  1、图文数据
 * 		href: '', // 分享链接
 * 		title: '', // 分享标题
 * 		summary: '', // 分享描述
 * 		imageUrl: '', // 分享图片
 * 	  2、图片数据
 * 		imageUrl: '', // 分享图片
 * 
 * @zjf-2020-11-12
 */
export function weiXinAppShare(type, scene, shareData) {
	if (type == 0) {
		//分享图文
		uni.share({
			provider: "weixin",
			scene: scene,
			type: type, //0为图文
			href: shareData.href,
			title: shareData.title,
			summary: shareData.summary,
			imageUrl: shareData.imageUrl, //图片,图片过大的话不展示，建议小于20kb
			success: function(res) {},
			fail: function(err) {},
			complete: (com) => {}
		});
	} else if (type == 2) {
		//分享图片
		uni.share({
			provider: "weixin",
			scene: scene,
			type: type, //2为图片
			imageUrl: shareData.imageUrl, //图片,图片过大的话不展示，建议小于20kb
			success: function(res) {},
			fail: function(err) {},
			complete: (com) => {}
		});
	}
}

/** 
 * base64加密
 * @params data String 要加密的字符串
 * @zjf-2021-06-28
 */
export function base64Encode(data) {
	let b64 = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
	let o1,
		o2,
		o3,
		h1,
		h2,
		h3,
		h4,
		bits,
		i = 0,
		ac = 0,
		enc = '',
		tmp_arr = [];
	if (!data) {
		return data;
	}
	data = utf8Encode(data);
	do {
		o1 = data.charCodeAt(i++);
		o2 = data.charCodeAt(i++);
		o3 = data.charCodeAt(i++);

		bits = o1 << 16 | o2 << 8 | o3;

		h1 = bits >> 18 & 0x3f;
		h2 = bits >> 12 & 0x3f;
		h3 = bits >> 6 & 0x3f;
		h4 = bits & 0x3f;
		tmp_arr[ac++] = b64.charAt(h1) + b64.charAt(h2) + b64.charAt(h3) + b64.charAt(h4);
	} while (i < data.length);

	enc = tmp_arr.join('');

	switch (data.length % 3) {
		case 1:
			enc = enc.slice(0, -2) + '==';
			break;
		case 2:
			enc = enc.slice(0, -1) + '=';
			break;
	}

	return enc;
}

export function utf8Encode(string) {
	string = (string + '').replace(/\r\n/g, '\n').replace(/\r/g, '\n');

	let utftext = '',
		start,
		end;
	let stringl = 0,
		n;

	start = end = 0;
	stringl = string.length;

	for (n = 0; n < stringl; n++) {
		let c1 = string.charCodeAt(n);
		let enc = null;

		if (c1 < 128) {
			end++;
		} else if ((c1 > 127) && (c1 < 2048)) {
			enc = String.fromCharCode((c1 >> 6) | 192, (c1 & 63) | 128);
		} else {
			enc = String.fromCharCode((c1 >> 12) | 224, ((c1 >> 6) & 63) | 128, (c1 & 63) | 128);
		}
		if (enc !== null) {
			if (end > start) {
				utftext += string.substring(start, end);
			}
			utftext += enc;
			start = end = n + 1;
		}
	}

	if (end > start) {
		utftext += string.substring(start, string.length);
	}

	return utftext;
}

/** 
 * 判断是否是微信浏览器
 * 
 * @zjf-2020-11-06
 */
export function isWeiXinBrower() {
	//#ifdef H5
	let ua = window.navigator.userAgent.toLowerCase();
	if (ua.match(/MicroMessenger/i) == 'micromessenger') {
		return true; // 微信中打开
	} else {
		return false; // 普通浏览器中打开
	}
	//#endif
	//#ifndef H5
	return false;
	//#endif
}




/** 
 * base64加密
 * @params data String 要加密的字符串
 * @zoucb-2023-05-25
 */
export function base64Encrypt(data) {
	let b64_obj = {
		a: 'CABDE',
		b: 'FoHIJ',
		c: 'KLpNO',
		d: 'PQRyr',
		e: 'UVWXi',
		f: 'Zght6',
		g: 'efabYzS',
		h: 'jkmu012l',
		m: 'GMqTs345d',
		n: 'cnvwx789()=',
	};
	let position = [
		'a:0',
		'b:1',
		'c:2',
		'd:3',
		'd:4',
		'e:4',
		'f:1',
		'f:2',
		'f:3',
		'n:8',
		'a:1',
		'n:9',
		'g:6',
		'h:7',
		'b:1',
		'm:8',
		'b:1',
		'n:1',
	]
	for (let i in position) {
		let [k, v] = position[i].split(':')
		data = data + `${b64_obj[k][v]}`
	}
	let b64 = Object.values(b64_obj).join('')

	let o1,
		o2,
		o3,
		h1,
		h2,
		h3,
		h4,
		bits,
		i = 0,
		ac = 0,
		enc = '',
		tmp_arr = [];
	if (!data) {
		return data;
	}
	data = utf8Encode(data);
	do {
		o1 = data.charCodeAt(i++);
		o2 = data.charCodeAt(i++);
		o3 = data.charCodeAt(i++);

		bits = o1 << 16 | o2 << 8 | o3;

		h1 = bits >> 18 & 0x3f;
		h2 = bits >> 12 & 0x3f;
		h3 = bits >> 6 & 0x3f;
		h4 = bits & 0x3f;
		tmp_arr[ac++] = b64.charAt(h1) + b64.charAt(h2) + b64.charAt(h3) + b64.charAt(h4);
	} while (i < data.length);

	enc = tmp_arr.join('');

	switch (data.length % 3) {
		case 1:
			enc = enc.slice(0, -2) + '==';
			break;
		case 2:
			enc = enc.slice(0, -1) + '=';
			break;
	}

	return enc;
}