module.exports = {
	apiUrl: (() => {
		let _base = 'https://api.lima.lightcloudapps.com/'
		// #ifdef MP-WEIXIN
		const Appversion = __wxConfig.envVersion
		if (Appversion == 'release') {
			_base = 'https:///'
		}
		// #endif
		return _base
	})(),
	chatUrl: (() => {
		let _base = 'wss://api.lima.lightcloudapps.com/'
		// #ifdef MP-WEIXIN
		const Appversion = __wxConfig.envVersion
		if (Appversion == 'release') {
			_base = 'wss://'
		}
		// #endif
		return _base
	})(),
	imgUrl: (() => {
		let _base = "https://dev-lima-bbc.oss-cn-shanghai.aliyuncs.com/mp-qq/"
		// #ifdef MP-WEIXIN
		const Appversion = __wxConfig.envVersion
		if (Appversion == 'release') {
			_base = 'https://'
		}
		// #endif 
		return _base
	})(), //静态资源地址——线上开发小程序素材
	storeUrl: (() => {
		let _base = 'https://'
		// #ifdef MP-WEIXIN
		const Appversion = __wxConfig.envVersion
		if (Appversion == 'release') {
			_base = 'https://'
		}
		// #endif
		return _base
	})(),
	h5AppId: '', //微商城appid 
	uploadMaxSize: 20, //上传最大限制，以M为单位
	curLang: 'zh', //当前语言,zh:中文，若为其他语言，需要对应/static/language下面的文件名
	h5GdKey: '5ec2dcfb622f1103dc5225464282c42d', //高德web-js key
	h5GdSecurityCode: '', //高德web-js 安全密钥
	// WxXcxGdKey: 'd51e13ff9542b34a1dd627253ae81b81', //高德小程序key
	statShowDebug: false, //是否开启统计的调试
	allow_privacy: false, //用户允许隐私授权，默认未允许
};
/** copyright *** lima *** version-v5.2.0 *** date-2025-06-01 ***主版本v5.2**/