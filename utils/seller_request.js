import Config from './config.js'
import store from '../store'
import {
	msg,
	saveBusinessToken
} from './common.js'
import Router from './request.js'
//seller接口请求
export default async function seller_request(opt) {
	// 每次请求之前都要获取新的商户token
	await saveBusinessToken()

	opt = opt || {};
	opt.url = opt.url || '';
	opt.data = opt.data || null;
	opt.method = opt.method || 'GET';

	let otherParam = {};
	if (!opt.responseType) {
		opt.header = opt.header || {
			"Content-Type": opt.isJSon ? "application/json" : "application/x-www-form-urlencoded",
			"Language": Config.curLang
		};
		otherParam.dataType = 'json'
	} else {
		opt.header = {
			"Language": Config.curLang
		}
		otherParam.responseType = opt.responseType;
		if (opt.dataType) {
			otherParam.dataType = opt.dataType
		}
	}

	let userInfo = uni.getStorageSync('userInfo') || '';
	if (opt.url.indexOf('sellerLogin/oauth/token') > -1) {
		opt.header.Authorization = 'Basic ZnJvbnQ6ZnJvbnQ=';
	} else {
		let token = '';
		if (userInfo && userInfo.vendorLoginVO.access_token) {
			token = userInfo.vendorLoginVO.access_token
		}
		opt.header.Authorization = 'Bearer ' + token;
		opt.header['User-Header'] = token
	}

	return new Promise((resolve, reject) => {
		uni.request({
			url: Config.storeUrl + opt.url,
			data: opt.data,
			method: opt.method,
			header: opt.header,
			...otherParam,
			success: res => {
				if (res.data.state == 266) {
					store.commit('logout', '')
				} else {
					resolve(res.data);
				}

			},
			fail: err => {
				reject(err);
			},
			complete: (res) => {}
		})
	})

}

// /** 
//  * 刷新token
//  * @zjf-2021-07-22
//  */
function refreshToken(refresh_token) {
	return new Promise(func => {
		uni.request({
			url: Config.apiUrl + 'v3/frontLogin/oauth/token',
			data: {
				grant_type: 'refresh_token',
				refresh_token: refresh_token,
			},
			method: 'POST',
			header: {
				"Content-Type": "application/x-www-form-urlencoded",
				"Language": Config.curLang,
				Authorization: 'Basic ZnJvbnQ6ZnJvbnQ=',
			},
			success: res => {
				func(res);
			}
		})
	})
}