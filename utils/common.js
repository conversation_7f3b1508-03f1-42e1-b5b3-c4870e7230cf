// #ifdef H5
var jweixin = require('../static/h5/jweixin.js'); //引入微信浏览器分享的jssdk
// #endif 
import Router from '../router.js'
import store from '../store/index.js'
import request from '@/utils/request'
import {
	getCurLanguage
} from './base.js'
let L = getCurLanguage

/*
 *
 * 检查页面是否是APP内嵌
 */
export function isAppInset() {
	// 检查是否是App内嵌
	return uni.getStorageSync("openType") === "app-h5";
}

/*
 * APP内嵌H5页面需要登录时调用
 */
export function callNativeLogin() {
	if (window.jsBridgeHelper != undefined) {
		window.jsBridgeHelper.sendMessage('receiveMessage', {
			action: 'login'
		}).then(result => {
			console.log('登录结果:', result);
			// 登录成功后页面会自动刷新并带上access_token
		}).catch(error => {
			console.error('登录失败:', error);
		});
	}
}

/*
 * APP内嵌H5页面需要登出时调用
 */
export function callNativeLogout() {
	if (window.jsBridgeHelper != undefined) {
		window.jsBridgeHelper.sendMessage('receiveMessage', {
			action: 'logout'
		}).then((result) => {
			console.log('登出结果:', result);
			if (result && result.success) {
				console.log('登出成功，页面即将刷新');
				store.commit('logout')
			} else {
				console.log('登出失败:', result ? result.message : '未知错误');
			}
		}).catch(error => {
			console.error('登出异常:', error);
		});
	}
}
/*
 *自定义Tabbar的页面切换
 * index  当前页面的index
 */
export function customTabbarShow(index) {
	// 获取当前页面实例
	const curPages = getCurrentPages()[0];
	if (typeof curPages.getTabBar === 'function' && curPages.getTabBar()) {
		curPages.getTabBar().setData({
			selected: index, // 表示当前菜单的索引，该值在不同的页面表示不同
			bgLeft: -750 + 750 / 5 * index - 2,
			animation: true
		});
	}
}
export function customTabbarHide(index) {
	// 获取当前页面实例
	const curPages = getCurrentPages()[0];
	if (typeof curPages.getTabBar === 'function' && curPages.getTabBar()) {
		curPages.getTabBar().resetAnimation()
	}
}

/*
* 自定义点赞、播放数显示格式
*  @param {number} num 需要格式化的数字 10w一下不处理，10w至1亿 显示为xxw，1亿以上显示为xx亿
*  @return {string} 格式化后的字符串
*/
export function formatVideoBigNum(_num) {
	const num = parseInt(_num, 10);
	if (num < 100000) {
		return num.toString();
	} else if (num < 100000000) {
		return (num / 10000).toFixed(1) + 'w';
	} else {
		return (num / 100000000).toFixed(1) + '亿';
	}
}


/*
 * 判断分页是否还有数据
 */
export function checkPaginationHasMore({
	current,
	pageSize,
	total
}) {
	return current * pageSize < total * 1;
}

// 检查字符串是否全是空格
export function checkSpace(str) {
	return str.trim() == "" ? true : false;
}

/*
 * 短信图形验证码的验证：4位英文字母和数字
 * 验证通过，返回boolean值true，否则返回具体的错误信息
 * @zjf-2021-01-07
 * */
export function checkImgCode(val) {
	let reg = /^[a-zA-Z0-9]{4}$/;
	if (!val) {
		return L("请输入图形验证码");
	} else if (!reg.test(val)) {
		return L("请输入正确的图形验证码");
	} else {
		return true;
	}
}

/* 
 * 替换指定位置的字符
 * str 
 * startIndex 要替换的字符串的开始位置
 * stopIndex  要替换的字符串的结束位置
 * replacetext  指定位置要替换成的内容
 */
export function replaceConByPosition(str, startIndex, stopIndex, replacetext) {
	let target_str = str.substring(0, startIndex - 1) + replacetext + str.substring(stopIndex + 1);
	return target_str;
}

/*
 * 返回一个数字的整数和小数
 * number 需要处理的数据
 * type: 要获取的数据 int 整数  decimal 小数
 */
export function getPartNumber(number, type) {
	let target = '';
	if (number == undefined) {
		return 0;
	}

	number = number.toString();

	let int = number.split('.')[0];

	let decimal = number.split('.')[1] != undefined ? ('.' + number.split('.')[1].slice(0, 2)) : '.00';
	if (decimal.length < 3) {
		decimal += '0';
	}

	if (type == 'int') {
		target = int
	} else if (type == 'decimal') {
		target = decimal
	} else if (type == 'all') {
		target = `${int}${decimal}`
	}
	return target;
}

// 手机号的验证
export function checkMobile(mobile, name) {
	let regMobile = /(1[3-9]\d{9}$)/;
	if (!mobile) {
		this.$api.msg(name ? `${L('请输入')}${name}!` : L('请输入手机号!'));
		return false;
	} else if (!regMobile.test(mobile)) {
		this.$api.msg(name ? `${L('请输入正确的')}${name}!` : L('请输入正确的手机号!'));
		return false;
	} else {
		return true;
	}
}

//座机和移动手机号码的验证
export function checkTel(mobile, name) {
	let regMobile = /(1[3-9]\d{9}$)/;
	let regTel = /(\d{4}-)\d{6,8}/
	if (!mobile) {
		this.$api.msg(name ? `${L('请输入')}${name}!` : L('请输入手机号!'));
		return false;
	} else if (!regMobile.test(mobile) && !regTel.test(mobile)) {
		this.$api.msg(name ? `${L('请输入正确的')}${name}!` : L('请输入正确的手机号!'));
		return false;
	} else {
		return true;
	}
}

// 6～20位，由英文、数字或符号组成的验证
export function checkPwd(pwd) {
	if (pwd.length < 6) {
		this.$api.msg(L('密码最少6位哦～'));
		return false;
	} else if (pwd.length > 20) {
		this.$api.msg(L('密码最多20位哦～'));
		return false;
	} else if (/[\u4E00-\u9FA5]/g.test(pwd)) {
		this.$api.msg(L('密码不可以有中文哦～'));
		return false;
	} else if (!(/^\S*$/.test(pwd))) {
		this.$api.msg(L('密码中不可以有空格哦～'));
		return false;
	} else {
		return true;
	}
}

//设置cookie，判断首页是否弹出开屏
export function setCookie() {
	uni.setStorage({
		key: 'cookie',
		data: 'cookie'
	});
}
//设置cookie，判断店铺首页是否弹出开屏
export function setStoreIsCookie(vid) {
	uni.setStorage({
		key: 'storeIsCookie' + vid,
		data: 'storeIsCookie' + vid
	});
}
//设置cookie，判断积分商城首页是否弹出开屏
export function setPointIsCookie() {
	uni.setStorage({
		key: 'pointIsCookie',
		data: 'pointIsCookie'
	});
}

// 登录成功的页面跳转
export function loginGoPage() {
	const pages = getCurrentPages();
	let fromurl = uni.getStorageSync('fromurl');
	if (fromurl && fromurl.url) {
		uni.removeStorage({
			key: 'fromurl',
			success: function(res) {}
		});
		if (fromurl.url.indexOf("pages/user/user") > -1 ||
			fromurl.url.indexOf("pages/index/index") > -1 ||
			fromurl.url.indexOf("pages/service/home") > -1 ||
			fromurl.url.indexOf("pages/vehicle/home") > -1) {
			Router.pushTab(fromurl.url)
		} else {
			Router.replace({
				path: fromurl.url,
				query: fromurl.query
			})
		}
	} else if (pages.length > 1) {
		Router.back(1)
	} else {
		Router.pushTab('/pages/user/user')
	}
}

export function isTabPage(url) {
	let tabPage = [
		'/pages/user/user',
		'/pages/index/index',
		'/pages/cart/cart',
		'/pages/index/information',
		'/pages/category/category',
	]
	return tabPage.some(u => u.indexOf(url) > -1)
}

// 数字格式化为以w为单位，保留2为小数
export function formatW(num) {
	return num > 10000 ? ((num / 10000) * 1).toFixed(2) + 'w' : num;
}

// 邮箱的验证
export function checkEmail(email, name) {
	let reg = /^([a-zA-Z0-9]+[-_.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[-_.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,6}$/;
	if (!email) {
		this.$api.msg(name ? `${L('请输入')}${name}!` : L('请输入邮箱!'));
		return false;
	} else if (!reg.test(email)) {
		this.$api.msg(name ? `${L('请输入正确的')}${name}!` : L('请输入正确的邮箱!'));
		return false;
	} else {
		return true;
	}
}

//验证身份证
export function checkIdentity(value, name) {
	let reg18 = /^[1-9][0-9]{5}(18|19|20)[0-9]{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)[0-9]{3}([0-9]|(X|x))/
	let reg15 = /^[1-9][0-9]{5}[0-9]{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)[0-9]{2}[0-9]/
	if (!value) {
		this.$api.msg(name ? `${L('请输入')}${name}!` : L('请输入身份证号'))
		return false
	} else if (reg18.test(value) || reg15.test(value)) {
		return true;
	} else {
		this.$api.msg(name ? `${L('请输入正确的')}${name}!` : L('请输入正确的身份证号'))
		return false;
	}
}

// 装修地址跳转
export function diyNavTo(val, HorS) {
	if (val.link_type == 'url') {
		//链接地址,只有h5可以跳转外部链接，其他端都不可以
		//ifdef H5
		window.location.href = val.link_value;
		//endif
		//app-1-start
		// #ifdef APP-PLUS
		plus.runtime.openURL(val.link_value) //这里默认使用外部浏览器打开而不是内部web-view组件打开
		// #endif
		//app-1-end
		// #ifdef MP
		Router.push({
			path: '/pages/index/skip_to',
			query: {
				url: val.link_value
			}
		})
		// #endif
	} else if (val.link_type == 'keyword') {
		//关键词
		let query = {
			keyword: encodeURIComponent(val.link_value),
			source: 'search'
		}
		if (HorS != 'home') {
			query.storeId = HorS
		}
		Router.push({
			path: '/standard/product/list',
			query
		})
	} else if (val.link_type == 'goods') {
		//商品
		Router.push({
			path: '/standard/product/detail',
			query: {
				productId: val.info.defaultProductId,
				goodsId: val.info.goodsId
			}
		})
	} else if (val.link_type == 'category') {
		//商品分类
		let query = {
			categoryId: val.info.categoryId
		}
		if (val.info.grade == 3) {
			query.pid = val.info.pid
		}
		Router.push({
			path: '/standard/product/list',
			query
		})
	} else if (val.link_type == 'topic') {
		//专题
		Router.push({
			path: '/pages/index/topic',
			query: {
				id: val.info.decoId ? val.info.decoId : val.info.id
			}
		})
	} else if (val.link_type == 'voucher_center') {
		if (!this.hasLogin) {
			setFromUrl({
				url: '/standard/coupon/couponCenter'
			})
			this.$emit('needLogin')
		} else {
			this.$Router.push('/standard/coupon/couponCenter')
		}
	} else if (val.link_type == 'brand_home') {
		this.$Router.push('/pages/public/brand')
	} else if (val.link_type == 'seckill') {
		this.$Router.push({
			path: '/standard/seckill/seckill',
			query: {
				seckillId: val.info.seckillId
			}
		})
	} else if (val.link_type == 'rank') {
		this.$Router.push({
			path: '/standard/rank/aggr'
		})
	} else if (val.link_type == 'spell_group') {
		this.$Router.push('/standard/pinGroup/index/index')
	} else if (val.link_type == 'ladder_group') { //阶梯团
		this.$Router.push('/standard/ladder/index/index')
	} else if (val.link_type == 'presale') { //预售入口页
		this.$Router.push('/standard/presale/index/list')
	} else if (val.link_type == 'point') { //积分商城首页
		this.$Router.push('/standard/point/index/index')
	} else if (val.link_type == 'svideo_center') { //短视频列表
		this.$Router.push('/extra/svideo/svideoList')
	} else if (val.link_type == 'live_center') { //直播列表
		this.$Router.push('/extra/live/liveList')
	} else if (val.link_type == 'spreader_center') { //推手中心
		if (!this.hasLogin) {
			// uni.showToast({
			// 	title: '请登录～',
			// 	icon: 'none',
			// 	duration: 700
			// })
			setFromUrl({
				url: '/extra/tshou/index/index'
			})
			this.$emit('needLogin')
		} else {
			this.$Router.push('/extra/tshou/index/index')
		}

	} else if (val.link_type == 'live') { //直播播放页面
		livePlayApp({
			live_id: val.link_value
		})
	} else if (val.link_type == 'svideo') { //短视频播放页面
		videoPlayNav({
			video_id: val.info.videoId,
			curLabelId: val.info.labelId,
			author_id: val.info.authorId
		})
	} else if (val.link_type == 'draw') {
		if (!this.hasLogin) {
			setFromUrl({
				url: '/standard/lottery/detail',
				query: {
					drawId: val.info.drawId
				}
			})
			this.$emit('needLogin')
		} else {
			this.$Router.push({
				path: '/standard/lottery/detail',
				query: {
					drawId: val.info.drawId
				}
			})
		}
	} else if (val.link_type == 'rank') {
		this.$Router.push('/standard/rank/aggr')
	} else if (val.link_type == 'sign_center') {
		if (!this.hasLogin) {
			setFromUrl({
				url: '/standard/signIn/signIn'
			})
			this.$emit('needLogin')
		} else {
			this.$Router.push('/standard/signIn/signIn')
		}
	} else if (val.link_type == 'store_list') { //店铺街
		this.$Router.push('/standard/store/list')
	} else if (val.link_type == 'store') {
		this.$Router.push(`/standard/store/shopHomePage?vid=${info.storeId}`)
	}
}

//登录时存储当前页面的路径
function setFromUrl(fromurl) {
	uni.setStorageSync('fromurl', fromurl)
}

// 装修类型判断
export function decoType(info) {
	let deco_obj = {}
	deco_obj.tupianzuhe = []
	// deco_obj.gonggao = []
	info.map(item => {
		if (item.type == 'more_tab') { //tab切换
			deco_obj.more_tab = {
				border_radius: item.border_radius,
				data: item.data,
			}
		} else if (item.type == 'lunbo') { //轮播图
			deco_obj.lunbo = {
				is_show: item.is_show,
				data: item.data
			}
		} else if (item.type == 'gonggao') { //公告
			if (item.show_style == 'one') {
				deco_obj.gonggao1 = {
					show_style: item.show_style,
					text: item.text,
					is_show: item.is_show,
				}
			} else {
				deco_obj.gonggao2 = {
					show_style: item.show_style,
					text: item.text,
					is_show: item.is_show,
				}
			}
		} else if (item.type == 'top_cat_nav') { //顶部轮播图
			deco_obj.top_cat_nav = {
				swiper_bg_style: item.swiper_bg_style,
				data: item.data
			}
		} else if (item.type == 'nav') { //导航
			deco_obj.nav = {
				is_show: item.is_show,
				data: item.data,
				icon_set: item.icon_set
			}
		} else if (item.type == 'kefu') { //客服
			deco_obj.kefu = {
				is_show: item.is_show,
				tel: item.tel,
				text: item.text
			}
		} else if (item.type == 'fuwenben') { //富文本
			deco_obj.fuwenben = {
				is_show: item.is_show,
				text: item.text
			}
		} else if (item.type == 'tupianzuhe') { //图片组合
			deco_obj.tupianzuhe.push({
				is_show: item.is_show,
				sele_style: item.sele_style,
				width: item.width,
				height: item.height,
				data: item.data,
			})
		} else if (item.type == 'dapei') { //搭配
			deco_obj.dapei = {
				is_show: item.is_show,
				dapei_desc: item.dapei_desc,
				dapei_img: item.dapei_img,
				data: item.data,
				width: item.width,
				height: item.height
			}
		} else if (item.type == 'fzx') { //辅助线
			deco_obj.fzx = {
				color: item.color,
				is_show: item.is_show,
				lrmargin: item.lrmargin,
				tbmargin: item.tbmargin,
				type: item.val
			}
		} else if (item.type == 'tuijianshangpin') { //推荐商品
			deco_obj.recommond_goods = {
				border_style: item.border_style,
				data: item.data,
				goods_margin: item.goods_margin,
				is_show: item.is_show,
				page_margin: item.page_margin,
				show_style: item.small,
				text_align: item.text_align,
				text_style: item.normal,
				isshow_sales: item.isshow_sales
			}
		} else if (item.type == 'fzkb') { //辅助空白
			deco_obj.fzkb = {
				color: item.color,
				is_show: item.is_show,
				text: item.text,
			}
		}
	})
	return deco_obj
}


//#ifdef H5
/** 
 * 微信浏览器里面的分享功能
 * type 分享类型  1 为微信好友分享 2为微信朋友圈分享
 * shareData  分享数据数组 里面的参数分别如下：
 * 		title: '', // 分享标题
 * 		desc: '', // 分享描述
 * 		link: '', // 分享链接
 * 		imgUrl: '', // 分享图片
 * 		type: '', // 分享类型,music、video或link，不填默认为link
 * 		dataUrl: '', // 如果type是music或video，则要提供数据链接，默认为空
 * isTs 是否推手模块，默认false
 * @zjf-2020-11-06
 */
export function weiXinBrowerShare(type, shareData) {
	let tar_url = getApp().globalData.apiUrl + 'v3/member/front/login/wxjsConf?source=1';

	uni.request({
		url: tar_url,
		method: 'GET',
		data: {
			url: encodeURIComponent(location.href)
		},
		success(res) {
			let data = res.data;
			jweixin.config({
				debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
				appId: data.data.appId, // 必填，公众号的唯一标识
				timestamp: data.data.timestamp, // 必填，生成签名的时间戳
				nonceStr: data.data.nonceStr, // 必填，生成签名的随机串
				signature: data.data.signature, // 必填，签名
				jsApiList: ["updateAppMessageShareData", "updateTimelineShareData"] // 必填，需要使用的JS接口列表
			});
			jweixin.ready(function() {
				if (type == 1) {
					//获取“分享给朋友”按钮点击状态及自定义分享内容接口
					jweixin.updateAppMessageShareData({
						title: shareData.title != undefined ? shareData.title : '', // 分享标题
						desc: shareData.desc != undefined ? shareData.desc : '', // 分享描述
						link: shareData.link != undefined ? shareData.link :
						'', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
						imgUrl: shareData.imgUrl, // 分享图标
						type: '', // 分享类型,music、video或link，不填默认为link
						dataUrl: '', // 如果type是music或video，则要提供数据链接，默认为空
						success: function() {

						}
					})
				} else if (type == 2) {
					//获取“分享到朋友圈”按钮点击状态及自定义分享内容接口
					jweixin.updateTimelineShareData({
						title: shareData.title != undefined ? shareData.title : '', // 分享标题
						link: shareData.link != undefined ? shareData.link :
						'', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
						imgUrl: shareData.imgUrl, // 分享图标
						success: function() {
							// 设置成功
						}
					})

				}
			})
		}
	})
}

/** 
 * 用于页面启动时配置微信分享参数
 * 
 * @zcb-2021-11-17
 */
export function WXBrowserShareStart() {
	let tar_url = getApp().globalData.apiUrl + 'v3/member/front/login/wxjsConf?source=1';
	let hrefUrl = encodeURIComponent(location.href)
	uni.request({
		url: tar_url,
		method: 'GET',
		data: {
			url: hrefUrl
		},
		success(res) {
			let data = res.data;

			if (data.data) {
				// #ifdef H5
				jweixin.config({
					debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
					appId: data.data.appId, // 必填，公众号的唯一标识
					timestamp: data.data.timestamp, // 必填，生成签名的时间戳
					nonceStr: data.data.nonceStr, // 必填，生成签名的随机串
					signature: data.data.signature, // 必填，签名
					jsApiList: ["updateAppMessageShareData",
						"updateTimelineShareData"
					] // 必填，需要使用的JS接口列表
				});
				//#endif
			}
		}
	})
}


/** 
 * 用于需要有微信分享的页面的方法
 * 
 * @zcb-2021-11-17
 */
export function WXBrowserShareThen(type, shareData) {
	jweixin.ready(function() {
		if (type == 1) {
			//获取“分享给朋友”按钮点击状态及自定义分享内容接口
			jweixin.updateAppMessageShareData({
				title: shareData.title != undefined ? shareData.title : '', // 分享标题
				desc: shareData.desc != undefined ? shareData.desc : '', // 分享描述
				link: shareData.link != undefined ? shareData.link :
				'', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
				imgUrl: shareData.imgUrl ? shareData.imgUrl : '123.png', // 分享图标
				type: '', // 分享类型,music、video或link，不填默认为link
				dataUrl: '', // 如果type是music或video，则要提供数据链接，默认为空
				success: function() {

				}
			})
		} else if (type == 2) {
			//获取“分享到朋友圈”按钮点击状态及自定义分享内容接口
			jweixin.updateTimelineShareData({
				title: shareData.title != undefined ? shareData.title : '', // 分享标题
				link: shareData.link != undefined ? shareData.link :
				'', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
				imgUrl: shareData.imgUrl ? shareData.imgUrl : '123.png', // 分享图标
				success: function() {
					// 设置成功
				}
			})

		}
	})
}




/** 
 * 获取浏览器地址参数
 * variable 为参数名，存在的话返回具体值，否则返回false
 * 
 * @zjf-2020-11-17
 */
export function getQueryVariable(variable) {
	let query = window.location.search.substring(1);
	let vars = query.split("&");
	for (let i = 0; i < vars.length; i++) {
		let pair = vars[i].split("=");
		if (pair[0] == variable) {
			return pair[1];
		}
	}
	return false;
}

/** 
 * 微信浏览器里面的支付
 * payData  支付数据数组 里面的参数分别如下：
 * 		timestamp: '',  // 支付签名时间戳，注意微信jssdk中的所有使用timestamp字段均为小写。但最新版的支付后台生成签名使用的timeStamp字段名需大写其中的S字符
 * 		nonceStr: '', // 支付签名随机串，不长于 32 位
 * 		package: '', // 统一支付接口返回的prepay_id参数值，提交格式如：prepay_id=***）
 * 		signType: '', // 签名方式，默认为'SHA1'，使用新版支付需传入'MD5'
 * 		paySign: '', // 支付签名
 * 		appId: '', 
 * 		success: function (res) { // 支付成功后的回调函数 }
 *		fail: function (res) { // 失败时执行的回调函数 }
 * 		complete: function (res) { // 接口调用完成时执行的回调函数，无论成功或失败都会执行 }
 * 		cancel: function (res) { // 用户点击取消时的回调函数，仅部分有用户取消操作的api才会用到 }
 * 		trigger: function (res) { // 监听Menu中的按钮点击时触发的方法，该方法仅支持Menu中的相关接口 }
 * 
 * @zjf-2020-11-06
 */
export function weiXinBrowerPay(payData) {
	let tar_url = getApp().globalData.apiUrl + 'v3/member/front/login/wxjsConf?source=1';
	uni.request({
		url: tar_url,
		method: 'GET',
		data: {
			url: encodeURIComponent(location.href)
		},
		success(res) {
			let data = res.data;
			// #ifdef H5
			jweixin.config({
				debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
				appId: data.data.appId, // 必填，公众号的唯一标识
				timestamp: data.data.timestamp, // 必填，生成签名的时间戳
				nonceStr: data.data.nonceStr, // 必填，生成签名的随机串
				signature: data.data.signature, // 必填，签名
				jsApiList: ['chooseWXPay', 'scanQRCode'] // 必填，需要使用的JS接口列表
			});
			jweixin.ready(function() {
				jweixin.chooseWXPay(payData);
			})
			//#endif
		}
	})
}
//#endif





/** 
 * 通用提示
 * con  String  提示的内容,无特殊要求的话可不传
 * 
 * @zjf-2020-11-18
 */
export function sldCommonTip(con = L('该功能在升级中～')) {
	uni.showToast({
		title: con,
		icon: 'none',
	});
}
export function formatPercent(val) {
	return val.substring(0, val.length - 1)
}


/** 
 * 获取用户登录模块的终端类型
 * 
 * @zjf-2020-11-23
 */
export function getLoginClient() {
	let client = 1;
	//app-2-start
	//#ifdef APP-PLUS
	client = uni.getDeviceInfo().platform == 'IOS' ? 4 : 3;
	//#endif
	//app-2-end
	//wx-1-start
	//#ifdef MP-WEIXIN
	client = 6;
	//#endif
	//wx-1-end
	//#ifdef H5
	client = 2;
	//#endif
	// #ifdef MP-ALIPAY
	client = 8;
	// #endif
	// #ifdef MP-BAIDU
	client = 7;
	// #endif
	// #ifdef MP-TOUTIAO
	client = 9;
	// #endif
	return client;
}

/**
 * 微信登录要传的client值
 * */
export function wxLoginClient() {
	let client = 1;
	//app-3-start
	//#ifdef APP-PLUS
	client = 3;
	//#endif
	//app-3-end
	//wx-2-start
	//#ifdef MP-WEIXIN
	client = 2;
	//#endif
	//wx-2-end
	//#ifdef H5
	client = 1;
	//#endif
	return client;
}

/**
 * 防止用户多此点击触发事件
 * @ljp - 2021-2-7
 * */
export function frequentyleClick(fn) {
	let that = this;
	if (that.onOff) {
		that.onOff = false;
		fn();
		setTimeout(() => {
			that.onOff = true;
		}, 1500)
	} else {
		//如果一直走else，可能是你没有页面的data下面挂载onOff = true; 不然一直会走else
	}
}
/**
 * h5端页面返回处理，刷新后返回到首页
 * @ww - 2021-2-21
 * */
export function back(fn) {
	// #ifdef H5
	const pages = getCurrentPages()

	if (pages.length > 1) {
		Router.back(1)
		return;
	} else {
		//重新定向跳转页面
		Router.replaceAll('/pages/index/index')
	}
	return;
	// #endif
	// #ifndef H5
	const pages = getCurrentPages()
	if (pages.length > 1) {
		Router.back(1)
		return;
	} else {
		Router.replaceAll('/pages/index/index')
	}
	// #endif
}

/*
 * 判断是否显示聊天页面的时间,2条消息之间间隔超过3分钟显示
 * 返回Boolean类型
 * preMsgTime 上一条消息的发送时间，curMsgTime该条消息的发送时间
 * @zjf-2021-03-05
 * */
export function isShowTime(preMsgTime, curMsgTime) {
	let res = false;
	//app-4-start
	// #ifdef APP-PLUS
	if (uni.getDeviceInfo().platform === 'ios' && preMsgTime != undefined && curMsgTime !=
		undefined) { //ios系统不识别该时间格式，进行专门格式化
		let arr = [preMsgTime, curMsgTime].map(item => item.toString().split(/[- :]/))
		let newDate = arr.map(item => item = new Date(item[0], item[1] - 1, item[2], item[3], item[4], item[5]))
		if (Date.parse(newDate[1]) * 1 - Date.parse(newDate[0]) * 1 > 3 * 60 * 1000) {
			res = true;
		}
	} else if (uni.getDeviceInfo().platform === 'android' && preMsgTime != undefined && curMsgTime != undefined) {
		if (Date.parse(new Date(curMsgTime.toString().replace(/-/g, '/'))) * 1 - Date.parse(new Date(preMsgTime
				.toString().replace(/-/g, '/'))) * 1 > 3 * 60 * 1000) {
			res = true;
		}
	}
	// #endif
	//app-4-end
	// #ifndef APP-PLUS
	let cur, pre
	if (typeof curMsgTime == "number") {
		cur = curMsgTime
	} else {
		cur = new Date(curMsgTime.toString().replace(/-/g, '/')) * 1
	}
	pre = new Date(preMsgTime.toString().replace(/-/g, '/')) * 1
	if (cur - pre > 3 * 60 * 1000) {
		res = true;
	}
	// #endif

	return res;
}

/*
 * 格式化聊天时间
 * 返回格式化后的数据，字符串类型
 * time 时间戳 13位
 * @zjf-2021-03-05
 * */
export function formatChatTime(time) {
	//app-5-start
	// #ifdef APP-PLUS
	if (uni.getDeviceInfo().platform === 'ios') { //ios系统不识别该时间格式，进行专门格式化
		let arr = time.toString().split(/[- :]/)
		let newDate = new Date(
			arr[0],
			arr[1] - 1,
			arr[2],
			arr[3],
			arr[4],
			arr[5]
		)
		return format(newDate, `yyyy${L('年')}MM${L('月')}dd${L('日')} hh:mm`)
	} else {
		return format(new Date(time), `yyyy${L('年')}MM${L('月')}dd${L('日')} hh:mm`);
	}
	// #endif
	//app-5-end

	// #ifndef APP-PLUS
	if (time.toString().indexOf('-') == -1 && typeof(time) == 'number') {
		return formatNorm(time);
	} else {
		return format(new Date(time.toString().replace(/-/g, '/')), 'yyyy年MM月dd日 hh:mm');
	}
	// #endif
}

export function formAdd0(m) {
	return m < 10 ? '0' + m : m;
}

/** 
 * 时间戳转标准时间
 * timeStep:Number
 */
export function formatNorm(timeStep) {
	let time = new Date(parseInt(timeStep));
	let y = time.getFullYear();
	let m = time.getMonth() + 1;
	let d = time.getDate();
	let h = time.getHours();
	let mm = time.getMinutes();
	let s = time.getSeconds();
	let newTime = y + '-' + formAdd0(m) + '-' + formAdd0(d) + ' ' + formAdd0(h) + ':' + formAdd0(mm) + ':' + formAdd0(
		s);
	return format(new Date(newTime.toString().replace(/-/g, '/')), 'yyyy年MM月dd日 hh:mm');
}

/** 
 * 格式化时间,转时间为字符串
 * date:Date
 * fmt:String
 */
export function format(date, fmt) {
	let o = {
		"y+": date.getFullYear(), //年
		"M+": date.getMonth() + 1, //月份
		"d+": date.getDate(), //日
		"h+": date.getHours(), //小时
		"m+": date.getMinutes(), //分
		"s+": date.getSeconds(), //秒
		"q+": Math.floor((date.getMonth() + 3) / 3), //季度
		"S": date.getMilliseconds() //毫秒
	};
	if (/(y+)/.test(fmt)) {
		fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
	}
	for (let k in o) {
		if (new RegExp("(" + k + ")").test(fmt)) {
			fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
		}
	}
	return fmt;
}

/** 
 * 基于uniapp.uploadFile的多文件上传，支持 跨端
 * fileArray:Array
 * params:Object
 * success:Function
 * @lijm-2021-06-15
 */
export function multifileUpload({
	fileArray,
	params,
	success,
	fail
}) {
	if (!fileArray || !params) {
		return '';
	}
	let promiseArray = [];
	fileArray.forEach((item, index) => {
		params.filePath = item;
		// params.formData.file = item;
		//将每个请求封装成promise
		promiseArray[index] = uni.uploadFile(params)
	})
	if (success && typeof success === 'function') {
		//当所有请求完成后，调用success回调，返回请求后的reponse
		Promise.all(promiseArray).then((res) => {
			let data = []
			res.forEach((item, index) => {
				// #ifndef MP-BAIDU||MP-ALIPAY
				if (JSON.parse(item[1].data).state === 200) {
					data.push(JSON.parse(item[1].data))
				}
				// #endif
				// #ifdef MP-BAIDU||MP-ALIPAY
				if (item[1].data.state === 200) {
					data.push(item[1].data)
				}
				// #endif

			})
			if (data.length) {
				success(data);
			}
			//如果有失败回调，则返回失败信息
			if (fail && typeof fail === 'function') {
				let errData = '';
				let tempStatus = res.some((item) => {
					// #ifdef MP-BAIDU||MP-ALIPAY
					if (item[1].data.state != 200) {
						errData = item[1].data
						return true;
					}
					// #endif

					// #ifndef MP-BAIDU||MP-ALIPAY
					if (JSON.parse(item[1].data).state != 200) {
						errData = JSON.parse(item[1].data);
						return true;
					}
					// #endif

					return false;
				})
				fail(errData || {
					status: 200,
					msg: 'no errors'
				})
			}
		})
	}

}

//app-6-start
/** 
 * APP热更新
 * params:Object
 */
// #ifdef APP-PLUS
export function checkUpdate() {
	let cur_edition = plus.runtime;
	let cur_platform = uni.getDeviceInfo().platform;
	let paramArr = ['app_android_hot_edition',
		'app_android_hot_link',
		'app_android_hot_tip',
		'app_android_package_edition',
		'app_android_package_link',
		'app_ios_hot_edition',
		'app_ios_hot_link',
		'app_ios_hot_tip',
		'app_ios_package_edition',
		'app_ios_package_link'
	]

	request({
		url: 'v3/system/front/setting/getSettings',
		data: {
			names: paramArr.join(',')
		}
	}).then(res => {
		if (res.state == 200) {
			let updateObj = {}
			paramArr.forEach((item, index) => {
				updateObj[item] = res.data[index]
			})

			if (updateObj[`app_${cur_platform}_package_edition`] && updateObj[
					`app_${cur_platform}_package_link`]) {
				if (cur_edition.version * 1 < updateObj[`app_${cur_platform}_package_edition`] * 1) {
					//需要整包升级
					console.log('package', updateObj[`app_${cur_platform}_package_edition`])
					uni.showModal({ //提醒用户更新   
						title: "更新提示",
						content: updateObj[`app_${cur_platform}_hot_tip`],
						success: (result) => {
							if (result.confirm) {
								plus.runtime.openURL(updateObj[`app_${cur_platform}_package_link`]);
							}
						}
					})
					return;
				}
			}



			if (updateObj[`app_${cur_platform}_package_edition`] && updateObj[
					`app_${cur_platform}_hot_edition`] && updateObj[`app_${cur_platform}_hot_link`]) {
				if (cur_edition.version * 1 == updateObj[`app_${cur_platform}_package_edition`] * 1 &&
					cur_edition.versionCode * 1 < updateObj[`app_${cur_platform}_hot_edition`] * 1) {
					let curVersinCode = uni.getStorageSync('curVersionCode');
					if (curVersinCode && curVersinCode * 1 >= updateObj[`app_${cur_platform}_hot_edition`] *
						1) {
						return false;
					}
					console.log('hot')
					//热更新
					uni.showModal({ //提醒用户更新
						title: "更新提示",
						content: updateObj[`app_${cur_platform}_hot_tip`],
						success: (result) => {
							if (result.confirm) {
								uni.downloadFile({
									url: updateObj[`app_${cur_platform}_hot_link`],
									success: (downloadResult) => {
										if (downloadResult.statusCode === 200) {
											plus.runtime.install(downloadResult
												.tempFilePath, {
													force: true
												},
												function() {
													uni.setStorageSync(
														'curVersionCode',
														updateObj[
															`app_${cur_platform}_hot_edition`
														]);
													console.log(
														'install success...');
													plus.runtime.restart();
												},
												function(e) {
													console.error('install fail...',
														e);
												});
										}
									}
								});
							}
						}
					})
					return;
				}
			}

		}

	})

}
// #endif
//app-6-end

/*
 * 富文本内容反转义（接口返回的富文本内容经过了转义，导致内容无法展示，所以需要反转义）
 * @param {String} str 富文本内容
 * @zjf-2022-01-07
 * */
export function quillEscapeToHtml(str) {
	if (str != undefined) {
		const arrEntities = {
			'lt': '<',
			'gt': '>',
			'nbsp': ' ',
			'amp': '&',
			'quot': '"'
		}
		return str.replace(/&(lt|gt|nbsp|amp|quot);/ig, function(all, t) {
			return arrEntities[t]
		}).replace(/\<img/g, "<img class='rich_text_image'")
	} else {
		return '';
	}
}


/*
 * 源数组和新数组进行某个key值的对比，新数组里出现源数组相比新的值则加入源数组
 * @param {Array} source 源数组
 * @param {Array} target 新数组
 * @param {str} key 属性值
 * @zcb-2021-06-06
 * */
export function arrCom(source, target, key) {

	let res = []

	if (source.length == 0 && target.length > 0) {
		return target
	} else if (target.length == 0 && source.length > 0) {
		return []
	} else {
		target.forEach(item => {
			let idx = source.findIndex(d => d[key] == item[key])
			if (idx < 0) res.push(item)
		})

		return res
	}
}

/*
 * h5获取路由的参数进行取值
 * @param {String} str 地址字符串
 * @param {String} key 要取的key
 * @zcb-2022-03-02
 * */
export function getSceneParam(str, key) {
	let res = '';
	let param_str = str.split('?');

	if (param_str[1]) {
		let param_array = param_str[1].split('&');

		for (let i in param_array) {
			let tmp_data = param_array[i].split('=');

			if (tmp_data[0] == key) {
				res = tmp_data[1];
				break;
			}
		}
	}

	return res;
}


/*
 * 统一提示方便全局修改
 * @param {title} str 提示语
 * @param {duration} number 持续时间
 * @param {mask} Boolean 是否遮罩
 * @param {icon} String 图标类型
 * */
export const msg = (title, duration = 1500, mask = false, icon = 'none') => {
	if (Boolean(title) === false) {
		return;
	}

	// #ifndef MP-TOUTIAO
	uni.showToast({
		title,
		duration,
		mask,
		icon
	});
	// #endif
	// #ifdef MP-TOUTIAO
	tt.showToast({
		title,
		icon
	});
	// #endif

}

//获取上一页信息
export const prePage = () => {
	let pages = getCurrentPages();
	let prePage = pages[pages.length - 2];
	return prePage.$vm;
}

//集中处理APP端推送事件
export function appPushHandle() {
	let platform = uni.getDeviceInfo().platform; //获取手机平台
	// 推送消息点击事件 start
	plus.push.addEventListener("click", function(msg) {
		// console.log('推送消息点击', plus.runtime, plus.ios);
		let info = {};
		if (!msg || !msg.payload) {
			return;
		} else if (platform == "ios") {
			var GeTuiSdk = plus.ios.importClass("GeTuiSdk");
			GeTuiSdk.setBadge(0);
			plus.runtime.setBadgeNumber(0);
			if (msg.aps && msg.aps.category) { //ios挂后台时推送信息在 'aps' 的 'category' 中
				info = (typeof msg.aps.category == 'string') ? JSON.parse(msg.aps.category) : msg.aps.category;
			} else if (msg.payload && !msg.payload.msgLinkInfo) {
				info = (typeof msg.payload == 'string') ? JSON.parse(msg.payload) : msg.payload;
			} else if (msg.payload && msg.payload.msgLinkInfo) {
				info = (typeof msg.payload.msgLinkInfo == 'string') ? JSON.parse(msg.payload.msgLinkInfo) : msg
					.payload.msgLinkInfo;
			}
		} else {
			plus.runtime.setBadgeNumber(0);
			if (msg.payload && !msg.payload.msgLinkInfo) {
				info = (typeof msg.payload == 'string') ? JSON.parse(msg.payload) : msg.payload;
			} else if (msg.payload && msg.payload.msgLinkInfo) {
				info = (typeof msg.payload.msgLinkInfo == 'string') ? JSON.parse(msg.payload.msgLinkInfo) : msg
					.payload.msgLinkInfo;
			}
		}
		if (!info.type) {
			return;
		}
		if (info.type == 'order_new' || info.type == 'order_news') { // 商品出库提醒 || 付款成功提醒  进入订单详情
			if (info.orderType && info.orderType == 'integral_order') { //积分订单
				uni.navigateTo({
					url: '/standard/point/order/detail?orderSn=' + info.orderSn
				})
			} else { //普通订单
				uni.navigateTo({
					url: '/order/detail?orderSn=' + info.orderSn
				})
			}
		} else if (info.type == 'balance_change') { //余额变动提醒  进入我的钱包
			uni.navigateTo({
				url: '/pages/balance/list'
			})
		} else if (info.type == 'coupon_news') { //优惠券过期提醒  进入我的优惠券
			uni.navigateTo({
				url: '/standard/coupon/myCoupon'
			})
		} else if (info.type == 'return_news' || info.type ==
			'refund_news') { //售后提醒 return_news(退货退款)  refund_news（仅退款）  进入我的售后列表（afsSn）
			uni.navigateTo({
				url: '/standard/refund/returnAndRefundList?state=' + (info.type == 'refund_news' ? 0 :
					1)
			})
		} else if (info.type == 'integral_change') { //积分变动提醒  进入我的积分
			uni.navigateTo({
				url: '/pages/user/myIntegral'
			})
		} else if (info.type == 'appointment_news' || info.type ==
			'seckill_prepare_start') { //预约消息跳转详情 || 秒杀开始提醒
			uni.navigateTo({
				url: '/standard/product/detail?productId=' + info.productId + '&goodsId=' + info.goodsId
			})
		} else if (info.type == 'cash_apply_notice') { //提现申请
			uni.navigateTo({
				url: '/pages/balance/outputList'
			})
		} else if (info.type == 'order_deliver_success') { //商品出库提醒
			uni.navigateTo({
				url: '/order/list?state=3'
			})
		} else if (info.type == 'order_payment_success') { //付款成功提醒
			uni.navigateTo({
				url: '/order/list?state=2'
			})
		} else if (info.type == 'red_notice') { //优惠卷到期提醒
			uni.navigateTo({
				url: '/standard/coupon/myCoupon'
			})
		} else if (info.type == 'refund_return_notice') { //退款退货提醒
			uni.navigateTo({
				url: '/standard/refund/returnAndRefundList?state=1'
			})
		}
	});
	// 推送消息点击事件 end
	//透传的情况下才会走这个，这个获取到额外的参数
	plus.push.addEventListener("receive", function(msg) {
		var info = {};
		if (platform == "ios") { //ios在打开应用的情况下生成推送消息
			if (msg.payload) {
				info = msg.payload;
				if (msg.type == 'receive') {
					let options = {
						cover: false,
						sound: 'system',
						title: info.title
					};
					plus.push.createMessage(info.content, info.msgLinkInfo, options);
				}
			}
		} else { //安卓在应用界面或后台状态下点金推送事件进入该监听
			if (msg.payload && msg.payload.msgLinkInfo) {
				info = (typeof msg.payload.msgLinkInfo == 'string') ? JSON.parse(msg.payload.msgLinkInfo) : msg
					.payload.msgLinkInfo;
			}
			if (info.type == 'order_new' || info.type == 'order_news') { // 商品出库提醒 || 付款成功提醒  进入订单详情
				if (info.orderType && info.orderType == 'integral_order') { //积分订单
					uni.navigateTo({
						url: '/standard/point/order/detail?orderSn=' + info.orderSn
					})
				} else { //普通订单
					uni.navigateTo({
						url: '/order/detail?orderSn=' + info.orderSn
					})
				}
			} else if (info.type == 'balance_change') { //余额变动提醒  进入我的钱包
				uni.navigateTo({
					url: '/pages/balance/list'
				})
			} else if (info.type == 'coupon_news') { //优惠券过期提醒  进入我的优惠券
				uni.navigateTo({
					url: '/standard/coupon/myCoupon'
				})
			} else if (info.type == 'return_news' || info.type ==
				'refund_news') { //售后提醒 return_news(退货退款)  refund_news（仅退款）  进入我的售后列表（afsSn）
				uni.navigateTo({
					url: '/standard/refund/returnAndRefundList?state=' + (info.type == 'refund_news' ?
						0 :
						1)
				})
			} else if (info.type == 'integral_change') { //积分变动提醒  进入我的积分
				uni.navigateTo({
					url: '/pages/user/myIntegral'
				})
			} else if (info.type == 'appointment_news' || info.type ==
				'seckill_prepare_start') { //预约消息跳转详情 || 秒杀开始提醒
				uni.navigateTo({
					url: '/standard/product/detail?productId=' + info.productId + '&goodsId=' + info
						.goodsId
				})
			} else if (info.type == 'cash_apply_notice') { //提现申请
				uni.navigateTo({
					url: '/pages/balance/outputList'
				})
			} else if (info.type == 'order_deliver_success') { //商品出库提醒
				uni.navigateTo({
					url: '/order/list?state=3'
				})
			} else if (info.type == 'order_payment_success') { //付款成功提醒
				uni.navigateTo({
					url: '/order/list?state=2'
				})
			} else if (info.type == 'red_notice') { //优惠卷到期提醒
				uni.navigateTo({
					url: '/standard/coupon/myCoupon'
				})
			} else if (info.type == 'refund_return_notice') { //退款退货提醒
				uni.navigateTo({
					url: '/standard/refund/returnAndRefundList?state=1'
				})
			}
		}
	});
}

/**
 * @zoucb 2023-05-12
 * 集中判断 跳转短视频播放页
 * app和替他端分开处理
 **/
export function videoPlayNav(arg) {
	//app-7-start
	// #ifdef APP-PLUS
	Router.push({
		path: '/extra/svideo/videoPlayApp',
		query: {
			...arg
		}
	})
	// #endif
	//app-7-end

	// #ifndef APP-PLUS
	Router.push({
		path: '/extra/svideo/svideoPlay',
		query: {
			...arg
		}
	})
	// #endif
}

/**
 * @zoucb 2023-05-16
 * 集中判断 跳转直播播放页
 * app和替他端分开处理
 **/
export function livePlayNav(arg, type = 'push') {
	//app-8-start
	// #ifdef APP-PLUS
	Router[type]({
		path: '/extra/live/livePlayApp',
		query: {
			...arg
		}
	})
	// #endif
	//app-8-end

	// #ifndef APP-PLUS
	Router[type]({
		path: '/extra/live/livePlay',
		query: {
			...arg
		}
	})
	// #endif
}

//当未登录去登录或者注册时，缓存里的购物车数据带进接口里
export const getUnLoginCartParam = () => {
	// let local_cart_list = uni.getStorageSync('cart_list')
	let cartInfo = []
	// if (local_cart_list && Object.keys(local_cart_list).length) {
	// 	local_cart_list.storeCartGroupList.map((item) => {
	// 		item.promotionCartGroupList.map((item1) => {
	// 			item1.cartList.map((item2) => {
	// 				cartInfo.push({
	// 					productId: item2.productId,
	// 					buyNum: item2.buyNum
	// 				})
	// 			})
	// 		})
	// 	})
	// }
	let local_cart_list = uni.getStorageSync('cart_list_Pickup'); // 自提
	let local_cart_list2 = uni.getStorageSync('cart_list_Email'); // 邮寄
	if (local_cart_list && Object.keys(local_cart_list).length) { // 自提
		local_cart_list.storeCartGroupList.map((item) => {
			item.promotionCartGroupList.map((item1) => {
				item1.cartList.map((item2) => {
					cartInfo.push({
						productId: item2.productId,
						buyNum: item2.buyNum,
						isPickup: 1,
					})
				})
			})
		})
	}
	if (local_cart_list2 && Object.keys(local_cart_list2).length) { // 邮寄
		local_cart_list2.storeCartGroupList.map((item) => {
			item.promotionCartGroupList.map((item1) => {
				item1.cartList.map((item2) => {
					cartInfo.push({
						productId: item2.productId,
						buyNum: item2.buyNum,
						isPickup: 0,
					})
				})
			})
		})
	}
	return cartInfo.length > 0 ? JSON.stringify(cartInfo) : null
}


//获取图形验证码
export function fetchImgCode() {
	return new Promise((resolve, reject) => {
		request({
			url: 'v3/captcha/common/getCaptcha'
		}).then((res) => {
			if (res.state == 200) {
				resolve({
					codeImg: 'data:image/png;base64,' + res.data.captcha,
					imgCodeKey: res.data.key
				})
			} else {
				resolve({})
				api.msg(res.data.msg);
			}
		})
	})
}


export async function getSaveAmount(cityCode, productId, num) {
	const result = await store.dispatch('getMemberConfig', ['super_is_enable'])
	let data = {
		productId,
		num
	}
	cityCode && (data.cityCode = cityCode)
	return new Promise((resolve, reject) => {
		if (result.super_is_enable != 1) {
			resolve({})
		}
		request({
			url: 'v3/goods/front/goods/saveAmount',
			data
		}).then(res => {
			if (res.state == 200) {
				resolve(res.data)
			} else {
				resolve({})
				msg(res.msg)
			}
		})
	})
}


export const preventMutiClick = (targetFn, instance) => {
	const continueExec = () => instance.isPreventClick = false
	if (!instance.isPreventClick) {
		instance.isPreventClick = true
		targetFn(continueExec)
	}
}

export const saveBusinessToken = () => {
	return new Promise((resolve, reject) => {
		request({
			url: 'v3/member/front/member/autoRegisterSeller'
		}).then((res) => {
			if (res.state == 200 || res.state == 267) {
				const vendorLoginVO = {};
				vendorLoginVO.access_token = res.data.access_token;
				vendorLoginVO.refresh_token = res.data.refresh_token;
				store.commit('setuserInfo', {
					vendorLoginVO
				})
				resolve(true)
			} else if (res.state == 255) {
				msg('登录已过期，请重新登录')
				setTimeout(() => {
					Router.push('/pages/public/login')
				}, 800)
			} else {
				Router.replace('/pages/index/index')
				resolve(false)
			}
		})
	})
}