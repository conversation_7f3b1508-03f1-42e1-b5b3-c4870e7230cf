<!-- 账号安全页面 -->
<template>
	<view class="container" :style="mix_diyStyle">
		<!-- <view class="list_cell b_b m_t" @click="goBindModile" hover-class="cell_hover" :hover-stay-time="50" v-if="!isBindMobile">
			<text class="cell_tit">{{ $L('绑定手机号码') }}</text>
			<view>
				<text class="cell_right_con active">{{ $L('尚未绑定手机号') }}</text>
				<text class="cell_more iconfont iconziyuan11"></text>
			</view>
		</view>
		<view class="list_cell b_b m_t" @click="navTo('/pages/account/changeMobile')" hover-class="cell_hover" :hover-stay-time="50">
			<text class="cell_tit">{{ $L('修改手机号') }}</text>
			<view>
				<text class="cell_right_con">{{ userCenterData.memberMobile ? this.$replaceConByPosition(this.userCenterData.memberMobile, 4, 6, '****') : '' }}</text>
				<text class="cell_more iconfont iconziyuan11"></text>
			</view>
		</view>
		<view
			class="list_cell b_b"
			@click="navTo(userCenterData.hasMemberEmail ? `/pages/account/changeEmail` : `/pages/account/bindEmail`)"
			hover-class="cell_hover"
			:hover-stay-time="50"
		>
			<text class="cell_tit">{{ userCenterData.hasMemberEmail ? '修改电子邮箱' : '绑定电子邮箱' }}</text>
			<view>
				<text class="cell_right_con">{{ userCenterData.memberEmail ? userCenterData.memberEmail : '' }}</text>
				<text class="cell_more iconfont iconziyuan11"></text>
			</view>
		</view>
		<view
			class="list_cell"
			@click="navTo(userCenterData.hasLoginPassword ? `/pages/account/changePwd?source=change_login` : `/pages/account/managePwd?source=set_login`)"
			hover-class="cell_hover"
			:hover-stay-time="50"
		>
			<text class="cell_tit">{{ userCenterData.hasLoginPassword ? '修改登录密码' : '设置登录密码' }}</text>
			<view>
				<text class="cell_more iconfont iconziyuan11"></text>
			</view>
		</view>

		<template v-if="userCenterData.hasPayPassword">
			<view class="list_cell b_b m_t" @click="navTo({ path: `/pages/account/changePwd`, query: { source: 'change_pay' } })" hover-class="cell_hover" :hover-stay-time="50">
				<text class="cell_tit">{{ $L('修改支付密码') }}</text>
				<view>
					<text class="cell_more iconfont iconziyuan11"></text>
				</view>
			</view>
			<view class="list_cell" @click="navTo({ path: `/pages/account/managePwd`, query: { source: 'reset_pay' } })" hover-class="cell_hover" :hover-stay-time="50">
				<text class="cell_tit">{{ $L('重置支付密码') }}</text>
				<view>
					<text class="cell_more iconfont iconziyuan11"></text>
				</view>
			</view>
		</template>

		<view v-if="!userCenterData.hasPayPassword" class="list_cell m_t" @click="navTo(`/pages/account/managePwd?source=set_pay`)" hover-class="cell_hover" :hover-stay-time="50">
			<text class="cell_tit">{{ $L('设置支付密码') }}</text>
			<view>
				<text class="cell_more iconfont iconziyuan11"></text>
			</view>
		</view> -->

		<view class="list_cell m_t" @click="$refs.wo.open()" hover-class="cell_hover" :hover-stay-time="50">
			<text class="cell_tit">{{ $L('注销账号') }}</text>
			<view>
				<text class="cell_more iconfont iconziyuan11"></text>
			</view>
		</view>

		<uni-popup ref="wo" type="dialog">
			<uni-popup-dialog
				type="input"
				title="温馨提示"
				content="请确认您是否真的要注销账户，注销后，您的会员信息、优惠券等内容会被清除，且无法恢复，请知晓。"
				:duration="2000"
				@confirm="confirmWo"
			></uni-popup-dialog>
		</uni-popup>

		<uni-popup ref="woCom" type="center" :maskClick="false">
			<view class="wo_com">
				<view class="wo_tip_title">注销成功</view>
				<view class="wo_tip">很遗憾，我们无法继续为您提供服务，感谢您一直以来的陪伴</view>
				<view class="confirm flex_row_center_center" @click="toLogin">
					<view class="">确定</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue';
import { mapState, mapMutations } from 'vuex';
export default {
	data() {
		return {
			memberAvatarLocal: '',
			memberAvatar: getApp().globalData.imgUrl + 'default/member-avatar.png', //会员头像
			memberName: '',
			memberNickName: '', //昵称
			gender: 0,
			sexArray: ['保密', '男', '女'],
			memberBirthday: '',
			memberBirthdayCon: '请选择生日',
			isBindMobile: true
		};
	},
	components: {
		uniPopup,
		uniPopupDialog
	},
	computed: {
		...mapState(['userInfo', 'userCenterData'])
	},
	onLoad() {
		this.getMmeberInfo();
		this.getIsBindMobile();
	},
	onReady() {},
	methods: {
		//获取个人中心数据
		...mapMutations(['logout']),
		initData() {
			if (this.userInfo.access_token) {
				this.$request({
					url: 'v3/member/front/member/getInfo'
				})
					.then((res) => {
						if (res.state == 200) {
							this.setUserCenterData(res.data);
						} else {
							this.$api.msg(res.msg);
						}
					})
					.catch((e) => {});
			}
		},
		//获取会员是否绑定手机号
		getIsBindMobile() {
			let param = {};
			param.url = 'v3/member/front/member/isBindMobile';
			param.data = {};
			param.method = 'GET';
			this.$request(param)
				.then((res) => {
					if (res.state == 200) {
						let result = res;
						this.isBindMobile = true;
					} else if (res.state == 267) {
						this.isBindMobile = false;
					} else {
						this.$api.msg(res.msg);
					}
				})
				.catch((e) => {
					//异常处理
				});
		},
		//去绑定手机号
		goBindModile() {
			this.$Router.push('/pages/public/bindMobile?source=account');
		},
		//获取会员信息
		getMmeberInfo() {
			let _this = this;
			this.$request({
				url: 'v3/member/front/member/memberInfo',
				method: 'GET'
			})
				.then((res) => {
					if (res.state == 200) {
						let result = res.data;
						_this.memberAvatar = result.memberAvatar;
						_this.memberName = result.memberName;
						_this.memberNickName = result.memberName;
						_this.gender = result.gender;
						_this.memberBirthday = result.memberBirthday;
						_this.memberBirthdayCon = result.memberBirthday ? result.memberBirthday : '请选择生日';
					} else {
						this.$api.msg(res.msg);
					}
				})
				.catch((e) => {});
		},
		navTo(url, type) {
			if (this.isBindMobile) {
				this.$Router[type ? type : 'push'](url);
			} else {
				uni.showModal({
					title: '提示',
					content: '请先进行手机号绑定',
					cancelText: '返回',
					confirmText: '前往绑定',
					success: (res) => {
						if (res.confirm) {
						} else if (res.cancel) {
						}
					}
				});
			}
		},
		//选择生日
		selBirthDay(e) {
			this.saveMemInfo('memberBirthday', e.detail.value);
		},
		//选择性别
		selSex(e) {
			this.saveMemInfo('gender', e.detail.value);
		},
		//保存会员信息
		saveMemInfo(index, val) {
			let param = {};
			param.url = 'v3/member/front/member/updateInfo';
			param.data = {};
			param.method = 'POST';
			param.data[index] = val;
			this.$request(param)
				.then((res) => {
					this.$api.msg(res.msg);
					if (res.state != 200) {
						this.$api.msg(res.msg);
					} else {
						if (index == 'memberAvatar') {
							this.memberAvatar = this.memberAvatarLocal;
						} else if (index == 'memberBirthday') {
							this[index] = val;
							this.memberBirthdayCon = val;
						} else {
							this[index] = val;
						}
					}
				})
				.catch((e) => {
					//异常处理
				});
		},
		//设置头像
		setAvatar() {
			let _this = this;
			uni.chooseImage({
				count: 1,
				sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
				success: function (res) {
					uni.uploadFile({
						url: getApp().globalData.apiUrl + 'v3/oss/front/upload',
						filePath: res.tempFilePaths[0],
						name: 'imageFile',
						formData: {
							source: 'headImg'
						},
						header: {
							Authorization: 'Bearer ' + _this.userInfo.access_token
						},
						success: (uploadFileRes) => {
							// #ifndef MP-ALIPAY||MP-BAIDU
							let result = JSON.parse(uploadFileRes.data);
							// #endif
							// #ifdef MP-ALIPAY||MP-BAIDU
							let result = uploadFileRes.data;
							// #endif
							if (result.state == 200) {
								_this.memberAvatarLocal = result.data.url;
								_this.saveMemInfo('memberAvatar', result.data.path);
							}
						}
					});
				}
			});
		},
		//修改昵称事件
		changeNickName() {
			this.$Router.push({
				path: '/pages/user/changeInfo',
				query: {
					nickName: encodeURIComponent(this.memberNickName)
				}
			});
		},
		confirmWo() {
			uni.showLoading();
			this.$request({
				url: 'v3/member/front/login/remove',
				method: 'POST'
			}).then((res) => {
				if (res.state == 200) {
					uni.hideLoading();
					this.$refs.wo.close();
					this.$refs.woCom.open();
				} else {
					this.$refs.wo.close();
					this.$api.msg(res.msg);
				}
			});
		},

		toLogin() {
			this.logout();
			this.$Router.replaceAll('/pages/public/login');
		}
	}
};
</script>

<style lang="scss">
page {
	background: $bg-color-split;
	width: 750rpx;
	margin: 0 auto;
}

.list_cell {
	display: flex;
	align-items: center;
	padding: 0 20rpx;
	line-height: 100rpx;
	height: 100rpx;
	position: relative;
	background: #fff;
	justify-content: center;

	&.cell_hover {
		background: #fafafa;
	}

	&.b_b:after {
		left: 20rpx;
	}

	&.m_t {
		margin-top: 20rpx;
	}

	.cell_more {
		color: $main-third-color;
		font-size: 18rpx;
		margin-left: 10rpx;
	}

	.cell_tit {
		flex: 1;
		font-size: 28rpx;
		color: #2d2d2d;
		margin-right: 10rpx;
	}

	.cell_right_con,
	.uni_birthday {
		color: #949494;
		font-size: 26rpx;
	}

	.active {
		color: var(--color_main);
	}

	&.mem_avatar_wrap {
		height: 120rpx;
		line-height: 120rpx;
	}

	.avatar {
		width: 82rpx;
		height: 82rpx;
		border-radius: 50%;
		background-size: contain;
		background-position: center center;
		background-repeat: no-repeat;
		overflow: hidden;
		background-color: #f8f6f7;
	}
}

.wo_com {
	background: #fff;
	border-radius: 30rpx;
	width: 520rpx;

	.wo_tip {
		color: #666;
		font-size: 26rpx;
		padding: 40rpx 20rpx;
		text-align: center;
		line-height: 40rpx;
	}

	.wo_tip_title {
		text-align: center;
		padding-top: 30rpx;
		font-size: 32rpx;
	}

	.confirm {
		border-top: 1rpx solid;
		border-top-color: rgba(0, 0, 0, 0.05);
		height: 80rpx;
		color: var(--color_main);
		font-size: 30rpx;
	}
}
</style>
