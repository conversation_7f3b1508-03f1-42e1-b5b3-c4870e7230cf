<template>
	<view v-if="isready" class="balance_account" @click="closeTab" :style="mix_diyStyle">
		<block v-if="list.length > 0">
			<view class="list" v-for="(item, index) in list" :key="item.accountId">
				<view class="logo flex_row_start_center">
					<img :src="imgUrl +(item.accountCode == 'ALIPAY'? 'pay/alipay_pay_icon.png': 'pay/wx_pay_icon.png')" mode="aspectFit" lazy-load />
					<span>{{ item.bankName }}</span>
				</view>
				<view class="item" v-if="item.accountCode == 'ALIPAY'">
					<span class="left">{{ $L('支付宝账号：') }}</span>
					<span class="right">{{ item.accountNumber }}</span>
				</view>
				<view class="item">
					<span class="left">{{ $L('真实姓名：') }}</span>
					<span class="right">{{ item.accountName }}</span>
				</view>
				<image @click.stop="showTab(index)" class="more" :src="imgUrl + 'store/more_black.png'" mode="aspectFit"
					lazy-load></image>
				<view class="tab" v-if="tabIndex == index" @click.stop="eventTap">
					<view v-if="item.accountCode == 'ALIPAY'" class="edit" @click="edit(index)">{{ $L('编辑') }}</view>
					<view @click="delet(item.accountId)">{{ $L('删除') }}</view>
				</view>
			</view>
			<view class="bottom"></view>
		</block>
		<view v-else class="empty_part flex_column_start_center">
			<image :src="imgUrl + 'no_credit_card.png'" />
			<text>{{ $L('暂无提现账号～') }}</text>
		</view>
		<view v-if="!hasALIPAY || !hasWXPAY" class="btn" @click.stop="add">{{$L('添加账号')}}</view>
		<uni-popup ref="addPop" type="bottom">
			<view class="pop_btn">
				<view v-if="!hasALIPAY" @click="gotoAdd('ALIPAY')" class="radius">{{$L('添加提现支付宝')}}</view>
				<view v-if="!hasWXPAY" @click="gotoAdd('WXPAY')">{{$L('添加提现微信')}}</view>
				<view @click="cancle">{{ $L('取消') }}</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import uniPopup from '@/components/uni-popup/uni-popup.vue'
	export default {
		components: {
			uniPopup
		},
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				list: [],
				tabIndex: -1,
				current: 1,
				pageSize: 10,
				hasMore: true,
				firstLoad: true,
				hasALIPAY: false, //是否已有支付宝支付方式
				hasWXPAY: false, //是否已有微信支付方式
				isready: false,
				isWeiXinH5: false,
				outputAli: false,
				outputWx: false
			}
		},
		onLoad() {
			setTimeout(() => {
				uni.setNavigationBarTitle({
					title: this.$L('提现账号管理')
				})
			}, 0);

			// #ifdef H5
			this.isWeiXinH5 = this.$isWeiXinBrower()
			if (!this.isWeiXinH5) {
				this.hasWXPAY = true
			}
			// #endif
			this.getOutputSet()
		},
		onShow() {
			if (this.firstLoad) {
				this.firstLoad = false
			} else {
				this.isready = false
				this.list = []
				this.getOutputSet()
			}

			// #ifdef H5
			let refreashPage = uni.getStorageSync('refreashPage')
			if (refreashPage) {
				uni.removeStorageSync('refreashPage')
				if (uni.getDeviceInfo().platform == 'ios') {
					setTimeout(() => {
						this.$router.go(0)
					}, 300)
				}
			}
			// #endif
		},
		methods: {
			getOutputSet() {
				let param = {}
				param.url = 'v3/system/front/setting/getSettings'
				param.method = 'GET'
				param.data = {}
				param.data.names = 'withdraw_alipay_is_enable,withdraw_wxpay_is_enable'
				this.$request(param).then((res) => {
					if (res.state == 200) {
						this.outputAli = res.data[0] == '1' ? true : false
						this.outputWx = res.data[1] == '1' ? true : false
					} else {
						this.$api.msg(res.msg)
					}
					this.getList()
				})
			},
			getList() {
				let param = {}
				param.method = 'GET'
				param.url = 'v3/member/front/member/cash/list'
				param.data = {}
				param.data.current = this.current
				param.data.pageSize = this.pageSize
				this.$request(param).then((res) => {
					if (res.state == 200) {
						let hasALIPAY = false
						let hasWXPAY = false
						this.list = res.data.list
						this.list.forEach((item) => {
							if (item.accountCode == 'ALIPAY') {
								hasALIPAY = true
							} else if (item.accountCode == 'WXPAY') {
								hasWXPAY = true
							}
						})
						if (!this.outputAli) {
							hasALIPAY = true
						}
						if (!this.outputWx) {
							hasWXPAY = true
						}
						this.hasALIPAY = hasALIPAY
						// #ifdef H5
						this.hasWXPAY = !this.isWeiXinH5 ? true : hasWXPAY
						// #endif
						// #ifndef H5
						this.hasWXPAY = hasWXPAY
						// #endif
						this.isready = true
					}
				})
			},
			showTab(index) {
				this.tabIndex = index
			},
			closeTab() {
				this.tabIndex = -1
			},
			eventTap() {},
			edit(index) {
				this.tabIndex = -1
				this.$Router.push({
					path: '/pages/balance/add',
					query: {
						type: this.list[index].accountCode,
						id: this.list[index].accountId,
						card: this.list[index].accountNumber,
						name: this.list[index].accountName
					}
				})
			},
			delet(id) {
				let param = {}
				param.method = 'POST'
				param.url = 'v3/member/front/member/cash/delete'
				param.data = {}
				param.data.accountId = id
				this.$request(param).then((res) => {
					if (res.state == 200) {
						this.tabIndex = -1
						this.current = 1
						this.hasMore = true
						this.list = []
						this.getList()
					}
					this.$api.msg(res.msg)
				})
			},
			add() {
				this.tabIndex = -1
				this.$refs.addPop.open()
			},
			gotoAdd(type) {
				this.$refs.addPop.close()
				this.$Router.push({
					path: '/pages/balance/add',
					query: {
						type
					}
				})
			},
			cancle() {
				this.$refs.addPop.close()
			}
		}
	}
</script>

<style lang="scss">
	page {
		overflow: auto;
		background-color: #f5f5f5;
	}

	.balance_account {
		width: 750rpx;
		min-height: 80vh;

		.list {
			position: relative;
			width: 100%;
			background-color: #ffffff;
			margin-top: 20rpx;
			padding: 30rpx;

			.logo {
				img {
					width: 64rpx;
					height: 64rpx;
				}

				span {
					line-height: 40rpx;
					color: #333333;
					font-size: 32rpx;
					font-family: PingFang SC;
					font-weight: bold;
					margin-left: 24rpx;
				}
			}

			.item {
				line-height: 40rpx;
				font-size: 26rpx;
				font-family: PingFang SC;
				font-weight: 400;
				margin-top: 20rpx;

				.left {
					display: inline-block;
					width: 170rpx;
					color: #666666;
					white-space: nowrap;
					flex-shrink: 0;
				}

				.right {
					color: #333333;
				}
			}

			.more {
				position: absolute;
				right: 40rpx;
				top: 50%;
				margin-top: -26rpx;
				width: 50rpx;
				height: 52rpx;
			}

			.tab {
				position: absolute;
				right: 64rpx;
				top: 50%;
				z-index: 9;
				margin-top: 20rpx;
				width: 100rpx;
				line-height: 84rpx;
				font-size: 26rpx;
				text-align: center;
				background: #ffffff;
				box-shadow: 0 0 16rpx 0px rgba(59, 59, 59, 0.2);
				border-radius: 10rpx;
				padding: 10rpx 20rpx;

				view:nth-child(2) {
					border-top: 1rpx solid #d1d1d1;
				}
			}
		}

		.bottom {
			width: 100%;
			height: 140rpx;
		}

		.btn {
			position: fixed;
			bottom: 60rpx;
			left: 50%;
			margin-left: -303rpx;
			width: 606rpx;
			height: 78rpx;
			line-height: 78rpx;
			color: #ffffff;
			font-size: 30rpx;
			font-family: PingFang SC;
			font-weight: 500;
			text-align: center;
			background: var(--color_main_bg);
			border-radius: 40rpx;
		}

		.pop_btn {
			width: 100%;
			padding-left: 40rpx;
			padding-right: 40rpx;
			background-color: #ffffff;
			border-top-left-radius: 28rpx;
			border-top-right-radius: 28rpx;
			overflow: hidden;

			view {
				height: 100rpx;
				line-height: 100rpx;
				font-size: 30rpx;
				text-align: center;
				border-top: 1rpx solid #dfdfdf;

				&.radius {
					border-top: none;
				}
			}
		}

		.empty_part {
			padding-top: 160rpx;

			image {
				width: 380rpx;
				height: 280rpx;
			}

			text {
				color: $main-third-color;
				font-size: 26rpx;
				margin-top: 57rpx;
			}

			button {
				width: 245rpx;
				height: 66rpx;
				background: rgba(252, 28, 28, 0.05);
				border-radius: 33rpx;
				color: var(--color_main);
				font-size: 30rpx;
				font-weight: bold;
				margin-top: 29rpx;
			}

			uni-button:after {
				border-radius: 200rpx;
				border-color: #fff;
			}
		}
	}
</style>
