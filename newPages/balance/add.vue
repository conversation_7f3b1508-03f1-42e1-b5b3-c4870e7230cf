<template>
  <view class="balance_add" :style="mix_diyStyle">
    <view class="item flex_row_start_center" v-if="type == 'ALIPAY'">
      <view
        ><span>{{ $L('支付宝账号：') }}</span></view
      >
      <input
        v-model="accountNumber"
        :placeholder="$L('请输入支付宝账号')"
        maxlength="20"
      />
    </view>
    <view class="item flex_row_start_center">
      <view
        ><span>{{ $L('真实姓名：') }}</span></view
      >
      <input
        v-model="accountName"
        :placeholder="$L('请输入账号使用者的真实姓名')"
        maxlength="20"
      />
    </view>
    <view class="btn" @click="submit"
      >{{ $L('确认') }}{{ isEdit ? $L('修改') : $L('添加') }}</view
    >
  </view>
</template>

<script>
export default {
  data() {
    return {
      isEdit: false, //是否是编辑
      type: 'ALIPAY',
      accountId: '',
      accountName: '',
      accountNumber: '',
      wxCode: '', //微信h5端获取code
      isWeiXinH5: false,
      clickEvent: false //防抖动
    }
  },
  onLoad() {
    setTimeout(()=>{
      uni.setNavigationBarTitle({
        title: this.$L('购物车')
      })
    },0);
    
    if (this.$Route.query.name) {
      this.isEdit = true
      this.accountId = this.$Route.query.id
      this.accountNumber = this.$Route.query.card
      this.accountName = this.$Route.query.name
    }
    this.type = this.$Route.query.type

    // #ifdef H5
    let code = this.$getQueryVariable('code')
    let isWeiXinH5 = this.$isWeiXinBrower()
    this.isWeiXinH5 = isWeiXinH5
    if (isWeiXinH5 && this.type == 'WXPAY' && !code) {
      let tar_url = location.href
      tar_url += location.href.indexOf('?') > -1 ? '&' : '?'
      tar_url += 'ori_url=' + location.href
      let uricode = encodeURIComponent(tar_url)
      window.location.href =
        'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' +
        getApp().globalData.h5AppId +
        '&redirect_uri=' +
        uricode +
        '&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect'
      return
    } else if (isWeiXinH5 && code) {
      this.type = 'WXPAY'
      this.wxCode = code

      let oriUrl = this.$Route.query.ori_url + 'pages/balance/add'
      let tmp_data = ''
      for (let i in this.$Route.query) {
        if (i != 'ori_url') {
          tmp_data += i + '=' + this.$Route.query[i] + '&'
        }
      }
      oriUrl += '?' + tmp_data
      this.oriUrl = oriUrl
      //微信浏览器的话要把浏览器地址里面的code去掉
      history.replaceState({}, '', this.oriUrl)
    }
    // #endif

    let title = ''
    if (this.type == 'ALIPAY') {
      title = this.$L('支付宝')
    } else {
      title = this.$L('微信')
    }
    setTimeout(()=>{
      uni.setNavigationBarTitle({
      title:
        (this.isEdit ? this.$L('编辑') : this.$L('添加')) +
        this.$L('提现') +
        title
    })
    },1);
    
  },
  onBackPress() {
    if (this.wxCode) {
      window.history.go(-2)
      return true
    } else {
      return false
    }
  },
  methods: {
    submit() {
      if (this.clickEvent) {
        return
      } else if (!this.accountNumber && this.type == 'ALIPAY') {
        this.$api.msg(this.$L('请输入支付宝账号'))
      } else if (!this.accountName) {
        this.$api.msg(this.$L('请输入账号使用者的真实姓名'))
      } else if (this.accountId) {
        this.clickEvent = true
        let param = {}
        param.method = 'POST'
        param.url = 'v3/member/front/member/cash/update'
        param.data = {}
        param.data.accountId = this.accountId
        param.data.accountName = this.accountName
        param.data.accountNumber = this.accountNumber
        this.$request(param).then((res) => {
          if (res.state == 200) {
            if (res.state == 200) {
              setTimeout(() => {
                this.$Router.back()
              }, 500)
            } else {
              this.clickEvent = false
            }
            this.$api.msg(res.msg)
          }
        })
      } else {
        let _this = this
        this.clickEvent = true
        let param = {}
        param.method = 'POST'
        param.url = 'v3/member/front/member/cash/add'
        param.data = {}
        param.data.accountCode = this.type
        param.data.accountName = this.accountName
        if (this.type == 'WXPAY') {
          // #ifdef H5
          if (this.isWeiXinH5) {
            param.data.codeSource = 2 //用户code来源：1==小程序，2==微信内部浏览器， 3==APP
            param.data.accountNumber = this.wxCode
          } else {
            this.clickEvent = false
            return
          }
          // #endif
		  //wx-1-start
          // #ifdef MP-WEIXIN
          uni.login({
            success: (code) => {
              param.data.codeSource = 1
              param.data.accountNumber = code.code
              this.$request(param).then((res) => {
                if (res.state == 200) {
                  setTimeout(() => {
                    this.$Router.back()
                  }, 500)
                } else {
                  this.clickEvent = false
                }
                this.$api.msg(res.msg)
              })
            },
            fail: (err) => {
              this.clickEvent = false
            }
          })
          return
          // #endif
		  //wx-1-end
		  //app-1-start
          // #ifdef APP-VUE
          uni.login({
            provider: 'weixin',
            success: function (loginRes) {
              if (loginRes.errMsg == 'login:ok') {
                //授权登录成功
                param.data.codeSource = 3
                param.data.accountNumber = loginRes.authResult.openid
                _this.$request(param).then((res) => {
                  if (res.state == 200) {
                    setTimeout(() => {
                      _this.$Router.back()
                    }, 500)
                  } else {
                    _this.clickEvent = false
                  }
                  _this.$api.msg(res.msg)
                })
              } else {
                _this.clickEvent = false
              }
            },
            fail: function () {
              _this.clickEvent = false
            }
          })
          return
          // #endif
		  //app-1-end
        } else {
          param.data.accountNumber = this.accountNumber
        }
        this.$request(param).then((res) => {
          this.$api.msg(res.msg)
          if (res.state == 200) {
            // #ifdef H5
            uni.setStorage({
              key: 'refreashPage',
              data: true
            })
            // #endif
            setTimeout(() => {
              if (this.wxCode) {
                // #ifdef H5
                window.history.go(-2)
                // #endif
              } else {
                this.$Router.back()
              }
            }, 1000)
          } else {
            this.clickEvent = false
          }
        })
      }
    }
  }
}
</script>

<style lang="scss">
page {
  padding-top: 20rpx;
  background-color: #f5f5f5;
}
.balance_add {
  .item {
    width: 100%;
    background-color: #ffffff;
    padding: 22rpx 48rpx;
    view {
      width: 180rpx;
      height: 40rpx;
      line-height: 40rpx;
      font-size: 26rpx;
      font-family: PingFang SC;
      font-weight: 500;
      text-align: right;
      margin-right: 20rpx;
      span {
        position: relative;
        &:before {
          content: '*';
          color: #ff0000;
          position: absolute;
          top: 4rpx;
          left: -14rpx;
          z-index: 9;
        }
      }
    }
    input {
      width: 450rpx;
      height: 40rpx;
      line-height: 40rpx;
      font-size: 28rpx;
    }
    &:nth-child(2) {
      border-top: 1rpx solid #d1d1d1;
    }
  }
  .btn {
    position: fixed;
    bottom: 60rpx;
    left: 50%;
    margin-left: -303rpx;
    width: 606rpx;
    height: 78rpx;
    line-height: 78rpx;
    color: #ffffff;
    font-size: 30rpx;
    font-family: PingFang SC;
    font-weight: 500;
    text-align: center;
    background: var(--color_main_bg);
    border-radius: 40rpx;
  }
}
</style>
