<template>
  <view class="output_info" v-if="info.cashId" :style="mix_diyStyle">
    <view class="top"></view>
    <view class="title">{{ $L('提现金额') }}</view>
    <view class="price"
      >{{ $L('￥') }}<span>{{ info.cashAmount }}</span></view
    >
    <view class="line"></view>
    <view class="item flex_row_start_start">
      <span class="left">{{ $L('申请单号:') }}</span>
      <span class="right">{{ info.cashSn }}</span>
    </view>
    <view class="item flex_row_start_start">
      <span class="left">{{ $L('状态:') }}</span>
      <span class="right">{{ info.stateValue }}</span>
    </view>
    <view class="item flex_row_start_start">
      <span class="left">{{ $L('手续费:') }}</span>
      <span class="right">￥{{ info.serviceFee }}</span>
    </view>
    <view class="item flex_row_start_start">
      <span class="left">{{ $L('提现方式:') }}</span>
      <span class="right">{{ info.receiveBank }}</span>
    </view>
    <view class="item flex_row_start_start" v-if="info.receiveType == 'ALIPAY'">
      <span class="left">{{ $L('支付宝账号:') }}</span>
      <span class="right">{{ info.receiveAccount }}</span>
    </view>
    <view class="item flex_row_start_start">
      <span class="left">{{ $L('真实姓名:') }}</span>
      <span class="right">{{ info.receiveName }}</span>
    </view>
    <view class="item flex_row_start_start">
      <span class="left">{{ $L('申请时间:') }}</span>
      <span class="right">{{ info.applyTime }}</span>
    </view>
    <view class="item flex_row_start_start" v-if="info.state == 2">
      <span class="left">{{ $L('完成时间:') }}</span>
      <span class="right">{{ info.finishTime }}</span>
    </view>
    <view
      class="item flex_row_start_start"
      v-if="info.state == 3 || info.state == 4"
    >
      <span class="left">{{ $L('失败原因:') }}</span>
      <span class="right">{{ info.failReason || '--' }}</span>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      info: {}
    }
  },
  onLoad() {
    setTimeout(()=>{
      uni.setNavigationBarTitle({
      title: this.$L('提现记录详情')
    })
    },0);
    
    this.getInfo()
  },
  methods: {
    getInfo() {
      let param = {}
      param.method = 'GET'
      param.url = 'v3/member/front/member/cash/log/detail'
      param.data = {}
      param.data.cashId = this.$Route.query.cashId
      this.$request(param).then((res) => {
        if (res.state == 200) {
          this.info = res.data
        } else {
          this.$api.msg(res.msg)
          setTimeout(() => {
            this.$Router.back()
          }, 1000)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.output_info {
  .top {
    width: 100%;
    height: 20rpx;
    background-color: #f5f5f5;
  }
  .title {
    line-height: 40rpx;
    color: #333333;
    font-size: 28rpx;
    font-family: PingFang SC;
    font-weight: 500;
    text-align: center;
    margin-top: 70rpx;
    margin-bottom: 48rpx;
  }
  .price {
    line-height: 40rpx;
    color: var(--color_main);
    font-size: 46rpx;
    font-family: PingFang SC;
    font-weight: 800;
    text-align: center;
    span {
      font-size: 66rpx;
    }
  }
  .line {
    margin: 90rpx 30rpx 50rpx;
    height: 2rpx;
    background-color: #dfdfdf;
  }
  .item {
    line-height: 40rpx;
    font-size: 28rpx;
    font-family: PingFang SC;
    font-weight: 500;
    margin: 16rpx 40rpx;

    .left {
      width: 150rpx;
      margin-right: 30rpx;
      flex-shrink: 0;
      color: #666666;
      text-align: right;
    }
    .right {
      color: #333333;
      word-break: break-all;
    }
  }
}
</style>
