<template>
  <view class="content" :style="mix_diyStyle">
    <view class="navbar">
      <view
        v-for="(item, index) in navList"
        :key="index"
        class="nav-item"
        :class="{ current: tabCurrentIndex === index }"
        @click="tabClick(index)"
      >
        {{ item.text }}
      </view>
    </view>
    <swiper
      :current="tabCurrentIndex"
      class="swiper-box"
      duration="300"
      @change="changeTab"
      :disable-touch="disTouch"
    >
      <swiper-item
        class="tab-content"
        v-for="(tabItem, tabIndex) in navList"
        :key="tabIndex"
      >
        <scroll-view
          class="list-scroll-content"
          scroll-y
          @scrolltolower="loadData"
        >
          <view
            v-if="
              tabItem.loadingState != 'first_loading' &&
              tabItem.rechargeList.length == 0
            "
            class="empty_part flex_column_start_center"
          >
            <image :src="imgUrl + 'empty_goods.png'" />
            <text>{{ $L('暂无提现记录～') }}</text>
          </view>
          <template v-if="tabItem.rechargeList.length > 0">
            <view
              class="list"
              v-for="item in tabItem.rechargeList"
              :key="item.cashId"
            >
              <view class="item" @click="info(item.cashId)">
                <view class="top flex_row_between_center">
                  <view class="sn"
                    >{{ $L('提现单号：') }}{{ item.cashSn }}</view
                  >
                  <view class="time">{{ item.applyTime }}</view>
                </view>
                <view class="bottom">
                  <view class="flex_row_start_start">
                    <span class="title">{{ $L('提现金额：') }}</span>
                    <span class="price"
                      >{{ $L('￥') }}{{ Number(item.cashAmount).toFixed(2) }}</span
                    >
                  </view>
                  <view class="flex_row_start_start">
                    <span class="title">{{ $L('手续费：') }}</span>
                    <span class="price"
                      >{{ $L('￥') }}{{ Number(item.serviceFee).toFixed(2) }}</span
                    >
                  </view>
                  <view
                    v-if="item.state == 3 || item.state == 4"
                    class="flex_row_start_start"
                  >
                    <span class="title">{{ $L('失败原因：') }}</span>
                    <span class="reason">{{ item.failReason || '--' }}</span>
                  </view>
                </view>
              </view>
            </view>
          </template>
          <loadingState
            v-if="
              tabItem.loadingState == 'first_loading' ||
              tabItem.rechargeList.length > 0
            "
            :state="tabItem.loadingState"
          />
        </scroll-view>
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
import loadingState from "@/components/loading-state.vue";
import {
	mapState
} from 'vuex';
export default {
	components: {
		loadingState
	},
	data() {
		return {
			imgUrl: getApp().globalData.imgUrl,
			tabCurrentIndex: 0,
			navList: [
				{
					state: 0,
					text: this.$L('提现中'),
					loadingState: 'first_loading',
					rechargeList: [],
					current: 1,
				},
				{
					state: 1,
					text: this.$L('提现成功'),
					loadingState: 'first_loading',
					rechargeList: [],
					current: 1,
				},
				{
					state: 2,
					text: this.$L('提现失败'),
					loadingState: 'first_loading',
					rechargeList: [],
					current: 1,
				},
			],
			stopPullDownRefresh: false, //是否下拉刷新中
			// #ifdef H5
			disTouch:true
			// #endif
			// #ifndef H5
			disTouch:false
			// #endif
		};
	},
	onLoad(options) {
      setTimeout(()=>{
        uni.setNavigationBarTitle({
			   title: this.$L('提现记录')
			});
      },0);
			
		/**
		 * 修复app端点击除全部订单外的按钮进入时不加载数据的问题
		 * 替换onLoad下代码即可
		 */
		// #ifndef MP
		this.loadData()
		// #endif
		// #ifdef MP
		this.loadData()
		// #endif

	},
	computed: {
		...mapState(['userInfo'])
	},
	//下拉刷新
	onPullDownRefresh() {
		let index = this.tabCurrentIndex;
		let navItem = this.navList[index];
		navItem.loadingState = 'first_loading';
		navItem.rechargeList = [];
		navItem.current = 1;
		this.stopPullDownRefresh = true; //下拉刷新状态
		this.loadData();
	},
	methods: {
		// 获取列表数据
		loadData(source) {
			let index = this.tabCurrentIndex;
			let navItem = this.navList.filter(item => item.state == index)[0];
			let state = navItem.state;

			if (source === 'tabChange' && navItem.loadingState !== 'first_loading') {
				//tab切换只有第一次需要加载数据
				return;
			}
			if (navItem.loadingState === 'loading') {
				//防止重复加载
				return;
			} else if (navItem.loadingState == 'no_more_data') {
				//已经没有数据，无需再请求
				return;
			}

			let param = {};
			param.url = 'v3/member/front/member/cash/log/list';
			param.data = {};
			param.data.pageSize = 10;
			param.data.current = navItem.current;
			navItem.loadingState = navItem.loadingState == 'first_loading' ? navItem.loadingState : 'loading';
			param.data.state = navItem.state+1; //状态：1、待处理；2、提现成功；3、提现失败
			this.$request(param).then(res => {
				if (res.state == 200) {
					navItem.rechargeList = navItem.rechargeList.concat(res.data.list);
					let hasMore = this.$checkPaginationHasMore(res.data.pagination); //是否还有数据
					if (hasMore) {
						navItem.current++;
						navItem.loadingState = 'allow_loading_more';
					} else {
						navItem.loadingState = 'no_more_data';
					}
				} else {
					this.$api.msg(res.msg);
				}
				if (this.stopPullDownRefresh) {
					this.stopPullDownRefresh = false;
					uni.stopPullDownRefresh();
				}
			}).catch((e) => {
				//异常处理
			})
		},

		//swiper 切换
		changeTab(e) {
			this.tabCurrentIndex = e.target.current;
			this.loadData('tabChange');
		},

		//顶部tab点击
		tabClick(index) {
			this.tabCurrentIndex = index;
			// #ifdef MP-ALIPAY
			this.loadData('tabChange');
			// #endif
		},

		//查看详情
		info(cashId) {
			this.$Router.push({
				path: '/pages/balance/outputInfo',
				query: {
					cashId
				}
			})
		},
	},
}
</script>

<style lang="scss">
.content {
  background: $bg-color-split;
  height: 100%;
  width: 750rpx;
  margin: 0 auto;
}

.swiper-box {
  // #ifdef H5
  height: calc(100vh - 168rpx);
  // #endif
  // #ifndef H5
  height: calc(100vh - 80rpx);
  // #endif
}

.list-scroll-content {
  height: 100%;
  padding-top: 20rpx;
}

.navbar {
  display: flex;
  height: 80rpx;
  padding: 0 5px;
  background: #fff;
  position: relative;
  z-index: 10;

  .nav-item {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    font-size: 28rpx;
    color: $main-font-color;
    position: relative;

    &.current {
      color: var(--color_main);
      font-size: 32rpx;

      &:after {
        content: '';
        position: absolute;
        left: 50%;
        bottom: 0;
        transform: translateX(-50%);
        width: 35rpx;
        height: 8rpx;
        background-color: var(--color_main);
        border-radius: 4rpx;
      }
    }
  }
}

.uni-swiper-item {
  height: auto;
}

.list {
  position: relative;
  overflow: hidden;
  width: 100%;
  margin-bottom: 20rpx;
  padding: 20rpx 30rpx 6rpx;
  background-color: #ffffff;
  .item {
    overflow: hidden;
    .top {
      line-height: 40rpx;
      color: #666666;
      font-size: 26rpx;
      font-family: PingFang SC;
      font-weight: 500;
      padding-bottom: 20rpx;
      border-bottom: 1rpx solid #dfdfdf;

      .sn {
      }
      .time {
      }
    }
    .bottom {
      view {
        line-height: 40rpx;
        color: #333333;
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: 500;
        margin-top: 20rpx;
        margin-bottom: 20rpx;

        .title {
          width: 140rpx;
          text-align: right;
          flex-shrink: 0;
          white-space: nowrap;
        }
        .price {
          color: var(--color_price);
        }
        .reason {
          display: -webkit-box;
          overflow: hidden;
          word-break: break-word;
          text-overflow: ellipsis;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
      }
    }
  }
}

.empty_part {
  padding-top: 108rpx;

  image {
   width: 380rpx;
   height: 280rpx;
  }

  text {
    color: $main-third-color;
    font-size: 26rpx;
    margin-top: 57rpx;
  }

  button {
    width: 245rpx;
    height: 66rpx;
    background: rgba(252, 28, 28, 0.05);
    border-radius: 33rpx;
    color: var(--color_main);
    font-size: 30rpx;
    font-weight: bold;
    margin-top: 29rpx;
  }

  uni-button:after {
    border-radius: 200rpx;
    border-color: #fff;
  }
}
</style>
