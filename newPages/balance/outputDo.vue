<template>
	<view class="output_do" :style="mix_diyStyle">
		<view class="tips flex_row_between_center" v-if="showTip">
			<view class="tips_text">
				<span>{{ $L('温馨提示：提现手续费为') }}{{ extra }}%，</span>
				<span>{{$L('单笔最低提现金额为￥')}}{{ Number(minMoney).toFixed(2) }}</span>
				<span>{{accInfo.accountCode == 'WXPAY'&&Number(maxMoney)>0?'、':'。'}}</span>
				<span v-if="accInfo.accountCode == 'WXPAY'&&Number(maxMoney)>0">{{$L('最高提现金额为￥')}}{{ Number(maxMoney).toFixed(2) }}。</span>
			</view>
			<text class="iconfont iconziyuan51" @click="closeTip"></text>
		</view>
		<view class="money">
			<view class="top">{{ $L('提现金额') }}</view>
			<view class="middle flex_row_start_center">
				<span>{{ $L('￥') }}</span>
				<input v-model="cashAmount" maxlength="6" @blur="handleBur" />
			</view>
			<view class="bottom" :class="{ err }">
				{{err? err: $L('剩余可提现金额￥') +((userCenterData.memberBalance!=undefined&&userCenterData.memberBalance)?Number(userCenterData.memberBalance).toFixed(2):'')}}
			</view>
		</view>
		<view class="account">
			<view class="account_top">{{ $L('提现账号') }}</view>
			<view class="account_bottom flex_row_between_center" @click="openPop">
				<block v-if="accInfo.accountId">
					<view class="left flex_row_start_center">
						<image class="logo"
							:src="imgUrl +(accInfo.accountCode == 'ALIPAY'? 'pay/alipay_pay_icon.png': 'pay/wx_pay_icon.png')">
						</image>
						<view class="middle"
							:class="accInfo.accountCode == 'ALIPAY'? 'flex_column_between_start': 'flex_column_center_start'">
							<view v-if="accInfo.accountCode == 'ALIPAY'">
								<span>{{ $L('支付宝账号：') }}</span>{{ accInfo.accountNumber }}
							</view>
							<view>
								<span>{{ $L('真实姓名：') }}</span>{{ accInfo.accountName }}
							</view>
						</view>
					</view>
					<view class="right">
						<text class="iconfont iconziyuan11"></text>
					</view>
				</block>
				<block v-else>
					<view>{{ $L('请选择提现账号') }}</view>
					<text class="iconfont iconziyuan11"></text>
				</block>
			</view>
		</view>
		<view class="btn" @click.stop="apply">{{ $L('申请提现') }}</view>

		<uni-popup ref="accountPop" type="bottom">
			<view class="account_box">
				<view class="title">
					{{ $L('选择提现账号') }}
					<text class="iconfont iconziyuan51" @click="closePop"></text>
				</view>
				<view v-for="(item, index) in account" :key="item.id" class="item flex_row_between_center"
					:class="{ border: index != 0 }" @click="changeAcc(index)">
					<view class="left flex_row_start_center">
						<image class="logo"
							:src="imgUrl +(item.accountCode == 'ALIPAY'? 'pay/alipay_pay_icon.png': 'pay/wx_pay_icon.png')">
						</image>
						<view class="middle"
							:class="item.accountCode == 'ALIPAY'? 'flex_column_between_start': 'flex_column_center_start'">
							<view v-if="item.accountCode == 'ALIPAY'">
								<span>{{ $L('支付宝账号：') }}</span>{{ item.accountNumber }}
							</view>
							<view>
								<span>{{ $L('真实姓名：') }}</span>{{ item.accountName }}
							</view>
						</view>
					</view>
					<view class="right">
				<svgGroup type="checked" :color="diyStyle_var['--color_main']" width="36" height="36" px="rpx"
              	 v-if="accIndex == index"></svgGroup>
					</view>
				</view>
				<block v-for="(item, index) in [$L('支付宝'), $L('微信')]" :key="index">
					<view v-if="(index == 0 && noAli&&ali_enable) || (index == 1 && noWechat&&wxpay_enable)" class="item flex_row_between_center"
						:class="{ border: index != 0 }" @click="addType(index)">
						<view class="left flex_row_start_center">
							<image class="logo" :src="imgUrl + 'addAccount.png'"></image>
							<view class="middle flex_column_center_start">
								<view>
									<span>{{ $L('添加') }}{{ item }}{{ $L('提现账号') }}</span>
								</view>
							</view>
						</view>
						<view class="right">
							<text class="iconfont iconziyuan11"></text>
						</view>
					</view>
				</block>
			</view>
		</uni-popup>

		<uni-popup ref="submitPop" @close="closeSubmit">
			<view class="submit">
				<view class="submit_title">
					{{ $L('提现金额') }}
					<text class="iconfont iconziyuan51" @click="closeSubmit"></text>
				</view>
				<view class="submit_money flex_row_center_center">{{ $L('￥') }}<span>{{ finalOut||0 }}</span></view>
				<view class="submit_main">
					<view class="submit_item flex_row_between_center">
						<span class="submit_left">{{ $L('手续费：') }}</span>
						<span class="submit_right">￥{{extra ?commission:0}}</span>
					</view>
					<view class="submit_item flex_row_between_center">
						<span class="submit_left">{{ $L('手续费比例：') }}</span>
						<span class="submit_right">{{ extra }}%</span>
					</view>
					<input v-model="payPwd" type="password" :placeholder="$L('请输入平台支付密码')" maxlength="20"
						cursor-spacing="0" />
				</view>
				<view class="submit_err" v-if="submitErr">{{ submitErr }}</view>
				<view class="submit_btn" @click="submit">{{ $L('确定') }}</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import uniPopup from '@/components/uni-popup/uni-popup.vue'
	import {
		mapState
	} from 'vuex'
	export default {
		components: {
			uniPopup
		},
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				showTip: true,
				minMoney: 10,
				maxMoney:10,
				extra: 1,
				cashAmount: '',
				err: '',
				account: [],
				accIndex: -1,
				accInfo: {},
				payPwd: '',
				submitErr: '',
				noAli: true,
				noWechat: true,
				isClick: false,
				ali_enable:true,
				wxpay_enable:true
			}
		},
		computed: {
			...mapState(['userCenterData']),
			finalOut() {
				return Number(this.cashAmount) - (Number(this.cashAmount) * Number(this.extra) / 100).toFixed(2)
			},

			commission() {
				return (Number(this.cashAmount) * Number(this.extra) / 100).toFixed(2)
			}
		},
		onLoad() {
			setTimeout(() => {
				uni.setNavigationBarTitle({
					title: this.$L('提现')
				})
			}, 0);

			// #ifdef H5
			let isWeiXinH5 = this.$isWeiXinBrower()
			if (!isWeiXinH5) {
				this.noWechat = false
			}
			// #endif
			this.getSet()
		},
		onShow() {
			this.getList()
		},
		watch: {
			cashAmount(e) {
				this.handleCashAmount()
				if (String(e).split('.')[1] && String(e).split('.')[1].length > 2) {
					setTimeout(() => {
						this.cashAmount = String(e).split('.')[0] + '.' + String(e).split('.')[1].substr(0, 2)
					})
				}
			}
		},
		methods: {
			
			handleCashAmount(){
				let cashAmount = Number(this.cashAmount)
				let minMoney = Number(this.minMoney)
				let maxMoney = Number(this.maxMoney)
				let memberBalance = this.userCenterData.memberBalance != undefined && this.userCenterData.memberBalance ?
					Number(this.userCenterData.memberBalance) : 0;
				if (!cashAmount || cashAmount == 0) {
					this.err = this.$L('请输入提现金额')
				} else if (cashAmount < minMoney) {
					this.err = this.$L('单笔最低提现金额为￥') + minMoney.toFixed(2)
				} else if (this.accInfo.accountCode == 'WXPAY'&&(cashAmount > maxMoney)) {
					this.err = this.$L('单笔提现金额最高为￥') + maxMoney.toFixed(2)
				} else if (cashAmount > memberBalance) {
					this.err = this.$L('剩余可提现金额不足')
				} else {
					this.err = ''
				}
			},
			
			
			
			getSet() {
				let param = {}
				param.method = 'GET'
				param.url = 'v3/system/front/setting/getSettings'
				param.data = {}
				param.data.names = 'min_withdraw_amount,withdraw_fee,withdraw_alipay_is_enable,withdraw_wxpay_is_enable,wx_max_withdraw_amount'
				this.$request(param).then((res) => {
					if (res.state == 200) {
						this.minMoney = res.data[0]
						this.maxMoney = res.data[res.data.length-1]
						this.extra = res.data[1],
						this.ali_enable = Boolean(res.data[2]=="1")
						this.wxpay_enable = Boolean(res.data[3]=="1")
					}
				})
			},
			getList() {
				let param = {}
				param.method = 'GET'
				param.url = 'v3/member/front/member/cash/list'
				param.data = {}
				param.data.current = this.current
				param.data.pageSize = this.pageSize
				this.$request(param).then((res) => {
					if (res.state == 200) {
						this.account = res.data.list
						this.account.forEach((item) => {
							if (item.accountCode == 'ALIPAY') {
								this.noAli = false
							} else if (item.accountCode == 'WXPAY') {
								this.noWechat = false
							}
						})
					}
				})
			},
			closeTip() {
				this.showTip = false
			},
			openPop() {
				this.$refs.accountPop.open()
			},
			closePop() {
				this.$refs.accountPop.close()
			},
			changeAcc(index) {
				this.accIndex = index
				this.accInfo = this.account[index]
				this.handleCashAmount()
				this.$refs.accountPop.close()
			},
			addType(type) {
				this.accIndex = -1
				this.accInfo = {}
				this.$refs.accountPop.close()
				this.$Router.push({
					path: '/pages/balance/add',
					query: {
						type: type == 0 ? 'ALIPAY' : 'WXPAY'
					}
				})
			},
			apply() {
				let cashAmount = Number(this.cashAmount)
				let minMoney = Number(this.minMoney)
				let maxMoney = Number(this.maxMoney)
				let memberBalance = Number(this.userCenterData.memberBalance)
				if (!cashAmount || cashAmount == 0) {
					this.err = this.$L('请输入提现金额')
				} else if (cashAmount < minMoney) {
					this.err = this.$L('单笔最低提现金额为￥') + minMoney.toFixed(2)
				}  else if (this.accInfo.accountCode == 'WXPAY'&&(cashAmount > maxMoney)) {
					this.err = this.$L('单笔提现金额最高为￥') + maxMoney.toFixed(2)
				} else if (cashAmount > memberBalance) {
					this.err = this.$L('剩余可提现金额不足')
				} else if (!this.accInfo.accountNumber) {
					this.$api.msg(this.$L('请选择提现账号'))
				} else {
					this.err = ''
					this.$refs.submitPop.open()
				}
			},
			closeSubmit() {
				this.$refs.submitPop.close()
				this.payPwd = ''
				this.submitErr = ''
			},
			submit() {
				if (!this.payPwd) {
					this.submitErr = this.$L('请输入平台支付密码')
				} else if (this.isClick) {
					return
				} else {
					let param = {}
					param.method = 'GET'
					param.url = 'v3/member/front/member/cash/log/verifyPwd'
					param.data = {}
					param.data.payPwd = this.$base64Encrypt(this.payPwd)
					let sthis = this;
					this.$request(param).then((res) => {
						if (res.state == 200) {
							this.isClick = true
							let params = {}
							params.method = 'POST'
							params.url = 'v3/member/front/member/cash/log/applyWithdraw'
							params.data = {}
							params.data.accountId = this.accInfo.accountId
							params.data.cashAmount = this.cashAmount
							params.data.payPwd = this.$base64Encrypt(this.payPwd)
							params.data.accountName = this.accInfo.accountName
							if (this.accInfo.accountNumber) {
								params.data.accountNumber = this.accInfo.accountNumber
							}
							this.$request(params).then((response) => {
								if (response.state == 200) {
									this.closeSubmit()
									setTimeout(() => {
										this.$Router.replace('/pages/balance/outputList')
									}, 1000)
								} else {
									this.isClick = false
								}
								this.$api.msg(response.msg)
							})
						} else {
							sthis.$api.msg(res.msg)
						}
					})
				}
			},
			handleBur() {
				this.cashAmount = this.cashAmount ? parseInt(this.cashAmount) : ''
			}
		}
	}
</script>

<style lang="scss">
	page {
		overflow: auto;
		background-color: #f5f5f5;
	}

	.output_do {
		.tips {
			width: 100%;
			min-height: 70rpx;
			padding: 20rpx;
			background: var(--color_halo);

			span {
				color: #000000;
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
				white-space: normal;
				word-break: break-all;
			}
			
			.tips_text{
				width: 640rpx;
				color: #000000;
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
				white-space: normal;
				word-break: break-all;
			}

			.iconziyuan51 {
				color: #333333;
				font-size: 24rpx;
				transform: scale(0.8);
			}
		}

		.money {
			overflow: hidden;
			margin-top: 20rpx;
			padding-left: 50rpx;
			padding-right: 50rpx;
			background-color: #ffffff;

			.top {
				line-height: 40rpx;
				color: #333333;
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				margin-top: 38rpx;
				margin-bottom: 38rpx;
			}

			.middle {
				span {
					line-height: 40rpx;
					color: #000000;
					font-size: 46rpx;
					font-family: PingFang SC;
					font-weight: 800;
					margin-left: 20rpx;
				}

				input {
					width: 460rpx;
					height: 40rpx;
					line-height: 40rpx;
					font-size: 42rpx;
					margin-left: 20rpx;
				}
			}

			.bottom {
				line-height: 40rpx;
				color: #6b6b71;
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
				border-top: 1rpx solid #dfdfdf;
				margin-top: 20rpx;
				padding-top: 24rpx;
				padding-bottom: 24rpx;

				&.err {
					color: #fa0000;
				}
			}
		}

		.account {
			overflow: hidden;
			margin-top: 20rpx;
			padding: 30rpx 50rpx;
			background-color: #ffffff;

			.account_top {
				line-height: 40rpx;
				color: #333333;
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				margin-bottom: 30rpx;
			}

			.account_bottom {
				view {
					line-height: 40rpx;
					color: #999999;
					font-size: 24rpx;
					font-family: PingFang SC;
					font-weight: 500;
				}

				.iconziyuan11 {
					color: #333333;
					font-size: 24rpx;
					transform: scale(0.9);
				}
			}
		}

		.btn {
			position: fixed;
			bottom: 60rpx;
			left: 50%;
			margin-left: -303rpx;
			width: 606rpx;
			height: 78rpx;
			line-height: 78rpx;
			color: #ffffff;
			font-size: 30rpx;
			font-family: PingFang SC;
			font-weight: 500;
			text-align: center;
			background: var(--color_main_bg);
			box-shadow: 0 2rpx 10rpx 0 var(--color_halo);
			border-radius: 40rpx;
		}

		.account_box {
			width: 100%;
			padding-left: 40rpx;
			padding-right: 40rpx;
			padding-bottom: 20rpx;
			background-color: #ffffff;
			border-top-left-radius: 28rpx;
			border-top-right-radius: 28rpx;
			overflow: hidden;

			.title {
				position: relative;
				line-height: 40rpx;
				color: #000000;
				font-size: 34rpx;
				font-family: PingFang SC;
				font-weight: 700;
				text-align: center;
				margin-top: 44rpx;
				margin-bottom: 44rpx;

				.iconziyuan51 {
					position: absolute;
					top: -18rpx;
					right: 0;
					z-index: 9;
					color: #666666;
					font-size: 24rpx;
				}
			}

			.item {
				height: 140rpx;

				&.border {
					border-top: 1rpx solid #d1d1d1;
				}
			}
		}

		.item,
		.account_bottom {
			.left {
				.logo {
					width: 64rpx;
					height: 64rpx;
					margin-right: 24rpx;
				}

				.middle {
					height: 90rpx;

					view {
						line-height: 40rpx;
						color: #666666;
						font-size: 28rpx;
						font-family: PingFang SC;
						font-weight: 500;

						span {
							color: #333333;
							margin-right: 10rpx;
						}
					}
				}
			}

			.right {
				image {
					width: 36rpx;
				}

				.iconziyuan11 {
					font-size: 24rpx;
				}
			}
		}

		.submit {
			position: relative;
			width: 580rpx;
			padding: 44rpx 30rpx 50rpx;
			background-color: #ffffff;
			border-radius: 20rpx;

			.submit_title {
				line-height: 44rpx;
				color: #111111;
				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: 500;
				text-align: center;
				margin-bottom: 46rpx;

				.iconziyuan51 {
					position: absolute;
					right: 36rpx;
					top: 26rpx;
					z-index: 8;
					color: #333333;
					font-size: 28rpx;
				}
			}

			.submit_money {
				color: #000000;
				font-size: 46rpx;
				font-family: PingFang SC;
				font-weight: 800;
				margin-bottom: 50rpx;

				span {
					font-size: 66rpx;
				}
			}

			.submit_main {
				padding: 30rpx 10rpx;
				border-top: 1rpx solid #e7e1e1;
				border-bottom: 1rpx solid #e7e1e1;

				.submit_item {
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
					margin-bottom: 20rpx;

					.submit_left {
						color: #7f7f7f;
					}

					.submit_right {
						color: #000001;
					}
				}

				input {
					line-height: 42rpx;
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
					margin-top: 50rpx;
				}
			}

			.submit_err {
				height: 0;
				line-height: 42rpx;
				color: #f50202;
				font-size: 26rpx;
				font-family: PingFang SC;
				font-weight: 500;
				margin-top: 10rpx;
				margin-left: 10rpx;
			}

			.submit_btn {
				width: 250rpx;
				height: 70rpx;
				line-height: 70rpx;
				color: #ffffff;
				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: 500;
				text-align: center;
				background: var(--color_main_bg);
				border-radius: 34rpx;
				margin: 56rpx auto 0;
			}
		}
	}
</style>
