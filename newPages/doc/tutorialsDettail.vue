<template>
    <view class="page">
        <jyfParser :html="detail.content" style="width: 100%; overflow: hidden; flex-shrink: 0" :isAll="true" />
    </view>
</template>
<script>
import jyfParser from "@/components/jyf-parser/jyf-parser";
import {
    mapState
} from "vuex";
export default {
    components: {
        jyfParser
    },
    data() {
        return {
            id: '',
            detail: {
                content: '', // 内容
                description: '', // 描述
                endTime: '', // 结束时间
                startTime: '', // 开始时间
                pic: '', // 图片
                title: '', // 名称
                video: '', // 类型
                isTop: false, // 是否置顶
                videoImg: ''
            }
        };
    },
    onLoad() {
        this.id = this.$Route.query.id
        if (this.id) {
            this.fetchDetails();
        }
    },
    computed: {
        ...mapState(["userCenterData"])
    },
    methods: {
        // 定义方法
        fetchDetails() {
            this.$request({
                url: 'v3/system/front/beginnerGuide/detail',
                data: {
                    id: this.id
                }
            }).then((res) => {
                if (res.state == 200) {
                    this.detail = res.data;
                    uni.setNavigationBarTitle({
                        title: this.detail.title || '新手教程'
                    });
                }
            });
        }
    }
};
</script>
<style lang="scss">
.page {
    padding: 20rpx;
}
</style>