<template>
    <view class="detail-page" :style="{ backgroundImage: `url(${imgUrl + 'default_bg.jpg'})` }">
        <KTabbar :title="$L(tabbarTitle)" :showLeft="true" :bgColor="'transparent'" :scrollTop="scrollTop" :bgImg="imgUrl + 'default_bg.jpg'" />
        <view class="card_wrap" :class="{ show: !initLoading }">
            <view class="item_row" style="margin-bottom: 60rpx">
                <view class="flex_row_start_center">
                    <view class="tag">Q</view>
                    <view class="title">{{ detail.title }}</view>
                </view>
                <view class="value">{{ detail.problemDetails }}</view>
            </view>
            <view class="item_row">
                <view class="flex_row_start_center">
                    <view class="tag">A</view>
                    <view class="title">解决方案</view>
                </view>
                <view class="value">{{ detail.solution }}</view>
                <view class="value" style="margin-top: 20rpx" v-if="detail.safetyTips">{{ detail.safetyTips }}</view>
            </view>

            <!-- 底部按钮 -->
            <view class="bottom-btns">
                <view class="btn btn-solid" @click="back">已解决</view>
                <view class="btn btn-outline" @click="tofix">未解决，立即报修</view>
            </view>
        </view>
        <view class="safe-bottom-height"></view>
    </view>
</template>

<script>
import KTabbar from '@/components/ktabbar.vue';
export default {
    components: {
        KTabbar
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            initLoading: true, // 初始化加载状态
            tabbarTitle: '问题详情',
            detail: {
                id: '', // 问题ID
                faultCode: '', // 故障代码
                title: '', // 问题标题
                symptom: '', // 问题症状
                problemDetails: '', // 问题详情描述
                solution: '', // 解决方案
                safetyTips: '' // 安全提示
            } // 问题详情数据
        };
    },
    onLoad() {
        const { id } = this.$Route.query;
        if (id) {
            this.getDetail(id); // 获取详情
        } else {
            this.$api.msg('未查询到相关问题，请稍后再试');
        }
    },
    methods: {
        // 获取详情
        getDetail(id) {
            uni.showLoading();
            this.$request({
                url: 'v3/system/front/faqContent/detail',
                data: { id }
            })
                .then((res) => {
                    this.initLoading = false;
                    uni.hideLoading();
                    if (res.state === 200) {
                        this.detail = res.data;
                        this.tabbarTitle = this.detail.title || '问题详情';
                    } else {
                        this.$api.msg(res.message || '获取详情失败');
                    }
                })
                .catch((error) => {
                    uni.hideLoading();
                    this.$api.msg('网络错误，请稍后再试');
                });
        },
        // 返回上一页
        back() {
            this.$Router.back();
        },
        // 跳转到报修页面
        tofix() {
            this.$Router.push({
                path: '/pages/service/maintenance/form'
            });
        }
    }
};
</script>

<style lang="scss">
.safe-bottom-height {
    height: 260rpx;
}

.detail-page {
    max-width: 750rpx;
    background-color: $bg1;
    min-height: 100vh;
    background: $bg1;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    background-attachment: fixed;

    .card_wrap {
        width: 92%;
        margin: 0 auto;
        padding: 80rpx 40rpx;
        background: #fff;
        border-radius: 30rpx;
        box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
        &.show {
            opacity: 1;
        }
        .item_row {
            display: flex;
            flex-direction: column;
            row-gap: 40rpx;
            .value {
                font-size: $fs-base;
                line-height: 1.4;
            }
        }
        .title,
        .solution {
            font-size: $fs-base;
            color: #333;
            line-height: 1.4;
            width: calc(100% - 80rpx);
        }
        .title {
            font-weight: 700;
            @extend .omitLine1;
        }
        .tag {
            width: 60rpx;
            height: 60rpx;
            font-size: $fs-base;
            font-weight: 900;
            color: #000;
            text-align: center;
            line-height: 60rpx;
            border-radius: 30rpx;
            background-color: #f2f2f2;
            margin-right: 20rpx;
        }
    }
}

.bottom-btns {
    position: fixed;
    left: 0;
    bottom: 50rpx;
    width: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
    background: transparent;
    z-index: 10;
    row-gap: 25rpx;
    .btn {
        text-align: center;
        height: 80rpx;
        line-height: 80rpx;
        border-radius: 40rpx;
        font-size: $fs-base;
        font-weight: 500;
        margin: 0 10rpx;
    }

    .btn-solid {
        width: 92%;
        margin: 0 auto;
        height: 80rpx;
        border-radius: 40rpx;
        background-color: #494949;
        font-size: $fs-base;
        color: #fff;
        line-height: 80rpx;
        text-align: center;
    }

    .btn-outline {
        width: 92%;
        margin: 0 auto;
        height: 80rpx;
        border-radius: 40rpx;
        background-color: rgba(0, 0, 0, 0.2);
        font-size: $fs-base;
        color: rgba(0, 0, 0, 0.5);
        line-height: 80rpx;
        text-align: center;
    }
}
</style>
