<template>
    <view class="page" :style="{ backgroundImage: `url(${imgUrl + 'default_bg.jpg'})` }">
        <KTabbar :title="$L('自检手册')" :showLeft="true" :bgColor="'transparent'" :bgImg="imgUrl + 'default_bg.jpg'" />
        <view class="card_wrap" :class="{ show: !initLoading }">
            <!-- 问题大标题 -->
            <view class="main-title">您的车出了什么问题？</view>

            <!-- 故障代码输入框+搜索按钮 -->
            <view class="search-bar">
                <view class="search-input-wrap">
                    <img :src="imgUrl + 'stord_ser1.png'" class="search-icon" mode="" />
                    <input class="search-input" placeholder="请输入故障代码" v-model="searchCode" />
                </view>
                <view class="search-btn" @click="onSearch">搜索</view>
            </view>

            <!-- 问题分类 -->
            <view class="cate-section">
                <view class="cate-list">
                    <view v-for="(item, idx) in catelist" :key="item.id" class="cate-item" @click="jumpToCate(item)">
                        <image class="cate-img" :src="item.pic" mode="widthFix" />
                        <text class="cate-text">{{ item.name || '' }}</text>
                    </view>
                </view>
            </view>
        </view>
        <view style="width: 100%; height: 120rpx"></view>
        <!-- 常见问题 -->
        <view class="button-fix">
            <view class="button" @click="gofaq">常见问题</view>
        </view>
    </view>
</template>
<script>
import KTabbar from '@/components/ktabbar.vue';
export default {
    components: {
        KTabbar
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            initLoading: true, // 初始化加载状态
            catelist: [], // 一级分类数据
            questionList: [], // 常见问题列表数据
            searchCode: ''
        };
    },
    onLoad() {
        uni.showLoading();
        this.fetchCateList();
        this.fetchBaseQuestionList();
    },
    methods: {
        // 获取手册所有一级分类
        fetchCateList() {
            this.$request({
                url: 'v3/system/front/faqCategory/tree'
            }).then((res) => {
                if (res.state == 200) {
                    this.catelist = res.data;
                } else {
                    this.$api.msg(res.msg);
                }
            });
        },
        // 获取常见问题10条
        fetchBaseQuestionList() {
            this.$request({
                url: 'v3/system/front/faqContent/hotList'
            })
                .then((res) => {
                    this.initLoading = false; // 初始化加载完成
                    uni.hideLoading();
                    if (res.state == 200) {
                        this.questionList = res.data;
                    } else {
                        this.$api.msg(res.msg);
                    }
                })
                .catch(() => {
                    uni.hideLoading();
                });
        },
        // 搜索按钮
        onSearch() {
            if (!this.searchCode) return;
            uni.showLoading({ title: '查询中', mask: true });
            this.$request({
                url: 'v3/system/front/faqContent/list',
                data: {
                    faultCode: this.searchCode
                }
            }).then((res) => {
                uni.hideLoading();
                if (res.state == 200) {
                    const { list } = res.data;
                    if (list && list.length > 0) {
                        this.searchCode = ''
                        this.$Router.push({
                            path: '/newPages/doc/detail',
                            query: { id: list[0].id }
                        });
                    } else {
                        this.$api.msg('未查询到相关故障');
                    }
                } else {
                    this.$api.msg(res.msg);
                }
            });
        },
        // 跳转分类
        jumpToCate(item) {
            this.$Router.push({
                path: '/newPages/doc/cateList',
                query: { id: item.id, name: item.name, pic: item.pic }
            });
        },
        // 跳转至详情页
        jumpToDetail(item) {
            const { id } = item;
            this.$Router.push({
                path: '/newPages/doc/detail',
                query: { id }
            });
        },
        // 跳转至常见问题页面
        gofaq() {
            this.$Router.push({
                path: '/newPages/doc/faq'
            });
        }
    }
};
</script>
<style lang="scss">
.search-bar {
    display: flex;
    align-items: center;
    margin: 0 0 30rpx;
    justify-content: space-between;
    padding: 0 20rpx;
    .search-input-wrap {
        flex: 1;
        display: flex;
        align-items: center;
        height: 70rpx;
        border-radius: 35rpx;
        box-sizing: border-box;
        padding: 0rpx 20rpx;
        background: linear-gradient(#fff, #fff) padding-box, linear-gradient(135deg, rgba(151, 148, 255, 1) 0%, rgba(255, 199, 200, 0.5) 100%) border-box;
        border: 1rpx solid transparent;
        background-origin: border-box;
        background-clip: padding-box, border-box;
        .search-icon {
            margin-right: 10rpx;
            width: 30rpx;
            height: 30rpx;
        }
        .search-input {
            flex: 1;
            font-size: $fs-s;
        }
    }
    .search-btn {
        margin-left: 20rpx;
        color: #000;
        font-size: $fs-s;
    }
}
.button-fix {
    width: 100%;
    position: fixed;
    left: 0;
    bottom: 50rpx;
    .button {
        width: 92%;
        margin: 0 auto;
        height: 80rpx;
        border-radius: 40rpx;
        background-color: #494949;
        font-size: $fs-base;
        color: #fff;
        line-height: 80rpx;
        text-align: center;
    }
}
.page {
    max-width: 750rpx;
    background-color: #f7f8fa;
    min-height: 100vh;
    padding-bottom: 50rpx;
    padding-top: 20rpx;
    background: $bg1;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    background-attachment: fixed;
    .card_wrap {
        width: 92%;
        margin: 0 auto;
        padding: 20rpx;
        background: #fff;
        border-radius: 30rpx;
        box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
        &.show {
            opacity: 1;
        }
    }
    .main-title {
        width: 92%;
        margin: 0 auto;
        margin-bottom: 20rpx;
        font-size: 36rpx;
        font-weight: bold;
    }
    .cate-section {
        width: 100%;
        padding: 20rpx 0 50rpx 0;
        .cate-title {
            padding: 20rpx 4%;
            font-size: $fs-base;
            font-weight: 500;
        }

        .cate-list {
            width: 100%;
            display: flex;
            flex-direction: column;
            flex-wrap: wrap;
            justify-content: flex-start;
            background: #fff;
            box-sizing: border-box;
        }

        .cate-item {
            width: 100%;
            display: flex;
            align-items: center;
            padding: 20rpx 0;
            padding-bottom: 20rpx;
            border-bottom: 1rpx solid rgba(0, 0, 0, 0.2);
            .cate-img {
                width: 90rpx;
                height: 90rpx;
                margin-right: 20rpx;
            }

            .cate-text {
                font-size: $fs-base;
                color: #000;
                text-align: center;
            }
        }
    }
}
</style>
