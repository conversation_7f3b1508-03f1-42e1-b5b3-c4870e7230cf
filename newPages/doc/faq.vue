<template>
    <view class="page" :style="{ backgroundImage: `url(${imgUrl + 'default_bg.jpg'})` }">
        <KTabbar :title="$L('常见问题')" :showLeft="true" :bgColor="'transparent'" :bgImg="imgUrl + 'default_bg.jpg'" />
        <view class="card_wrap" :class="{ show: !initLoading }">
            <!-- 常见问题 -->
            <view class="common-section">
                <view class="common-list">
                    <view v-for="item in questionList" :key="item.id" class="common-item" @click="jumpToDetail(item)">
                        <view class="flex_row_start_center item_row">
                            <view class="tag">Q</view>
                            <view class="title">{{ item.title }}</view>
                        </view>
                        <view class="flex_row_start_start item_row">
                            <view class="tag">A</view>
                            <view class="solution">{{ item.solution }}</view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>
<script>
import KTabbar from '@/components/ktabbar.vue';
export default {
    components: {
        KTabbar
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            initLoading: true, // 初始化加载状态
            catelist: [], // 一级分类数据
            questionList: [], // 常见问题列表数据
            searchCode: ''
        };
    },
    onLoad() {
        uni.showLoading();
        // this.fetchCateList();
        this.fetchBaseQuestionList();
    },
    methods: {
        // 获取手册所有一级分类
        fetchCateList() {
            this.$request({
                url: 'v3/system/front/faqCategory/tree'
            }).then((res) => {
                if (res.state == 200) {
                    this.catelist = res.data;
                } else {
                    this.$api.msg(res.msg);
                }
            });
        },
        // 获取常见问题10条
        fetchBaseQuestionList() {
            this.$request({
                url: 'v3/system/front/faqContent/hotList'
            })
                .then((res) => {
                    this.initLoading = false; // 初始化加载完成
                    uni.hideLoading();
                    if (res.state == 200) {
                        this.questionList = res.data;
                    } else {
                        this.$api.msg(res.msg);
                    }
                })
                .catch(() => {
                    uni.hideLoading();
                });
        },
        // 搜索按钮
        onSearch() {
            if (!this.searchCode) return;
            this.$Router.push({
                path: '/newPages/doc/detail',
                query: { code: this.searchCode }
            });
        },
        // 跳转分类
        jumpToCate(item) {
            this.$Router.push({
                path: '/newPages/doc/cateList',
                query: { id: item.id, name: item.name, pic: item.pic }
            });
        },
        // 跳转至详情页
        jumpToDetail(item) {
            const { id } = item;
            this.$Router.push({
                path: '/newPages/doc/detail',
                query: { id }
            });
        }
    }
};
</script>
<style lang="scss">
.page {
    max-width: 750rpx;
    min-height: 100vh;
    padding-bottom: 50rpx;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    background-attachment: fixed;
    .card_wrap {
        width: 100%;
        margin: 0 auto;
        padding: 20rpx;
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
        &.show {
            opacity: 1;
        }
    }
    .common-section {
        width: 100%;
        .common-list {
            width: 100%;
            box-sizing: border-box;
            .common-item {
                width: 94%;
                margin: 0 auto;
                padding: 40rpx;
                border-radius: 40rpx;
                background-color: #fff;
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                row-gap: 40rpx;
                margin-bottom: 20rpx;
                box-shadow: 0px 4px 10px -2px rgba(0,0,0,0.1);
            }
            .title,
            .solution {
                font-size: $fs-base;
                color: #333;
                line-height: 1.4;
                width: calc(100% - 80rpx);
            }
            .title {
                font-weight: 700;
                @extend .omitLine1;
            }
            .tag {
                width: 60rpx;
                height: 60rpx;
                font-size: $fs-base;
                font-weight: 900;
                color: #000;
                text-align: center;
                line-height: 60rpx;
                border-radius: 30rpx;
                background-color: #f2f2f2;
                margin-right: 20rpx;
            }
        }
    }
}
</style>
