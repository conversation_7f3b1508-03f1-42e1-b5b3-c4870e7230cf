<template>
    <view class="page" :style="{ backgroundImage: `url(${imgUrl + 'default_bg.jpg'})` }">
        <KTabbar :title="$L('自检手册')" :showLeft="true" :bgColor="'transparent'" :bgImg="imgUrl + 'default_bg.jpg'" />
        <view class="card_wrap" :class="{ show: !initLoading }">
            <!-- 主分类 -->
            <view class="first-cate flex_row_start_center">
                <image class="cate-img" :src="firstCateInfo.pic" mode="widthFix" />
                <text class="cate-text">{{ firstCateInfo.name || '' }}</text>
            </view>
            <view v-for="(item, idx) in catelist" :key="item.id" class="cate-item">
                <view class="cate-item-header" @click="onChangeOpen(item, idx)">
                    <text class="cate-title">{{ item.name || '' }}</text>
                    <uv-icon :name="item.open ? 'arrow-down' : 'arrow-right'" size="16" />
                </view>
                <view v-if="item.open && item.list && item.list.length" class="sub-list">
                    <view v-for="(sub, subIdx) in item.list" :key="sub.id" class="sub-item" @click.stop="toDetail(sub)">
                        <text class="sub-title">{{ sub.title }}</text>
                        <uv-icon name="arrow-right" size="16" />
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import KTabbar from '@/components/ktabbar.vue';
export default {
    components: {
        KTabbar
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            initLoading: true, // 初始化加载状态
            catelist: [], // 二级分类数据
            firstCateInfo: {
                pic: '', // 主分类图片
                name: '', // 主分类名称
                id: '' // 主分类ID
            } // 主分类信息
        };
    },
    onLoad() {
        const { id, name, pic } = this.$Route.query;
        if (id) {
            this.firstCateInfo = { pic: decodeURIComponent(pic), name, id };
            this.getSecendCate(id);
        } else {
            this.$api.msg('未查询到相关问题，请稍后再试');
        }
    },
    methods: {
        //  获取二级分类
        getSecendCate(id) {
            uni.showLoading();
            this.$request({
                url: 'v3/system/front/faqCategory/listByPid',
                data: { pid: id }
            })
                .then((res) => {
                    this.initLoading = false;
                    uni.hideLoading();
                    if (res.state === 200) {
                        // 初始化open和list字段
                        this.catelist = (res.data || []).map((item) => ({ ...item, open: false, list: [] }));
                    } else {
                        this.$api.msg(res.message);
                    }
                })
                .catch((error) => {
                    uni.hideLoading();
                    this.$api.msg('网络错误，请稍后再试');
                });
        },
        //  根据分类id获取手册列表
        getListByCateId(id, index) {
            // 先全部收起
            this.catelist.forEach((c, i) => {
                c.open = false;
            });
            // 如果已加载过三级列表，直接切换展开
            if (this.catelist[index].list && this.catelist[index].list.length > 0) {
                this.catelist[index].open = true;
                return;
            }
            uni.showLoading();
            this.$request({
                url: 'v3/system/front/faqContent/list',
                data: { typeSecondId: id, pageSize: 100 }
            })
                .then((res) => {
                    uni.hideLoading();
                    if (res.state === 200) {
                        this.catelist[index].list = res.data.list || [];
                        this.catelist[index].open = true;
                    }
                })
                .catch((error) => {
                    this.$api.msg('网络错误，请稍后再试');
                });
        },
        // 展开/收起逻辑
        onChangeOpen(item, idx) {
            // 只展开当前项，其它全部收起
            this.catelist.forEach((c, i) => {
                if (i !== idx) c.open = false;
            });
            if (item.list && item.list.length > 0) {
                this.catelist[idx].open = !item.open;
            } else {
                this.getListByCateId(item.id, idx);
            }
        },
        // 跳转到三级问题详情
        toDetail(sub) {
            this.$Router.push({ path: '/newPages/doc/detail', query: { id: sub.id } });
        }
    }
};
</script>

<style lang="scss">
.safe-bottom-height {
    height: 160rpx;
}
.page {
    max-width: 750rpx;
    background-color: $bg1;
    min-height: 100vh;
    padding-bottom: 50rpx;
    padding-top: 20rpx;
    background: $bg1;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    background-attachment: fixed;

    .card_wrap {
        width: 92%;
        margin: 0 auto;
        padding: 20rpx;
        background: #fff;
        border-radius: 30rpx;
        box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
        opacity: 0;
        transition: opacity 0.3s ease-in-out;

        &.show {
            opacity: 1;
        }
    }
    .first-cate {
        width: 100%;
        padding: 20rpx 0;
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
        border-bottom: 1px solid #9d9d9d;
        .cate-img {
            width: 70rpx;
            height: 70rpx;
            margin-right: 15rpx;
        }
        .cate-text {
            font-size: $fs-base;
            color: #333;
        }
    }
    .cate-item {
        margin-top: 24rpx;
        border-radius: 20rpx;
        background: #fff;
        box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.06);
        overflow: hidden;

        .cate-item-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 32rpx 24rpx;
            font-size: $fs-base;
            border-bottom: 1rpx solid #f2f2f2;
        }

        .sub-list {
            .sub-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 28rpx 24rpx 28rpx 36rpx;
                font-size: $fs-s;
                color: #333;
                border-bottom: 1rpx solid #f2f2f2;

                &:last-child {
                    border-bottom: none;
                }
            }
        }
    }
}
</style>
