<template>
    <view class="page">
        <!-- 教程卡片列表 -->
        <view class="card-list">
            <view v-for="(item, idx) in dataList" :key="idx" class="card" :class="item.type" @click="jumpToDetail(item)">
                <!-- 封面图片 -->
                <view class="cover-wrap">
                    <image :src="item.pic" class="cover-img" mode="aspectFill" />
                    <view v-if="item.isTop" class="tag-top">置顶</view>
                </view>
                <view class="card-content">
                    <view class="card-title">{{ item.title }}</view>
                    <view class="card-desc">{{ item.desc }}</view>
                </view>
            </view>
        </view>
    </view>
</template>
<script>
export default {
    data() {
        return {
            dataList: [],
            query: {
                current: 1,
                pageSize: 10
            },
            noMoreData: false // 是否没有更多数据
        };
    },
    onLoad() {
        this.fetchList();
    },
    methods: {
        // 获取列表
        fetchList() {
            return new Promise((resolve) => {
                this.$request({
                    url: 'v3/system/front/beginnerGuide/list',
                    data: this.query
                }).then((res) => {
                    if (res.state == 200) {
                        const { list, pagination } = res.data;
                        if (pagination.current === 1) {
                            this.dataList = list;
                        } else {
                            this.dataList = this.dataList.concat(list);
                        }
                        // 如果当前页数据小于每页数量，说明没有更多数据了
                        if (pagination.total > pagination.current * pagination.pageSize) {
                            this.query.current += 1;
                        } else {
                            this.noMoreData = true;
                        }
                        resolve()
                    } else {
                        this.$api.msg(res.msg)
                    }
                });
            })
        },
        // 跳转至详情页
        jumpToDetail(item) {
            const { id } = item;
            this.$Router.push({
                path: '/newPages/doc/tutorialsDettail',
                query: {
                    id
                }
            });
        }
    },
    // 下拉刷新
    onPullDownRefresh() {
        this.query.current = 1; // 重置页码
        this.noMoreData = false; // 重置没有更多数据标志
        this.fetchList().then(() => {
            uni.stopPullDownRefresh(); // 停止下拉刷新
        });
    },
    // 上拉加载更多
    onReachBottom() {
        if (!this.noMoreData) {
            this.fetchList();
        }
    }
};
</script>
<style lang="scss">
.page {
    max-width: 750rpx;
    width: 100%;
    background-color: $bg1;

    .card-list {
        padding: 24rpx 0 40rpx 0;
    }

    .card {
        background: #fff;
        border-radius: 15rpx;
        margin: 0 24rpx 32rpx 24rpx;
        box-shadow: 0 4rpx 16rpx 0 rgba(0, 0, 0, 0.04);
        overflow: hidden;
        position: relative;
    }

    .cover-wrap {
        position: relative;
        width: 100%;
        height: 220rpx;

        .cover-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 18rpx 18rpx 0 0;
        }

        .tag-top {
            position: absolute;
            top: 0;
            right: 0;
            background: #ff9800;
            color: #fff;
            font-size: 24rpx;
            border-radius: 8rpx;
            padding: 4rpx 18rpx;
            z-index: 2;
        }
    }

    .card-content {
        padding: 24rpx 24rpx 18rpx 24rpx;

        .card-title {
            font-size: $fs-base;
            margin-bottom: 8rpx;
            color: #222;
        }

        .card-desc {
            font-size: $fs-m;
            color: #888;
        }
    }
}
</style>
