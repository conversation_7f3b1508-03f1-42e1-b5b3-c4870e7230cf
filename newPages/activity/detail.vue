<template>
    <view class="page" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }">
        <ktabbar :bgImg="imgUrl + 'default_bg.jpg'" :title="'活动详情'" />
        <view class="container">
            <view class="activity_header">
                <image class="header_img" :src="detail.headImg || imgUrl + 'default_activity.png'" mode="aspectFill" />
                <view class="activity_info">
                    <view class="info_row">
                        <view class="time">
                            <image :src="imgUrl + 'time.png'" class="icon" mode="scaleToFill" />
                            <text>活动时间</text>
                        </view>
                        <view class="value">{{ formatTime(detail.startTime) }} - {{ formatTime(detail.endTime) }}</view>
                    </view>
                    <view class="info_row">
                        <view class="time">
                            <image :src="imgUrl + 'location.png'" class="icon" mode="scaleToFill" />
                            <text>活动地点</text>
                        </view>
                        <view class="value">{{ detail.address || '--'}}</view>
                    </view>
                </view>
            </view>
            <view class="activity-detail">
                <jyfParser :html="detail.content || ''" style="width: 100%; overflow: hidden; flex-shrink: 0" :isAll="true" />
            </view>
        </view>
        <!-- 底部留资按钮 -->
        <view class="activity-footer safe-bottom" v-if="buttonVisible">
            <view class="activity-footer-btn" @click="jump">
                <text>{{ detail.btnText || '参加活动' }}</text>
            </view>
        </view>
    </view>
</template>
<script>
import ktabbar from '@/components/ktabbar.vue';
import jyfParser from '@/components/jyf-parser/jyf-parser';
import { mapState } from 'vuex';
export default {
    components: {
        jyfParser,
        ktabbar
    },
    data() {
        return {
            id: '', // 活动ID
            imgUrl: getApp().globalData.imgUrl, // 图片基础路径
            detail: {
                content: '', // 活动内容
                description: '', // 活动描述
                endTime: '', // 活动结束时间
                headImg: '', // 活动图片
                questionnaireName: '', // 活动名称
                startTime: '', // 活动开始时间
                type: '', // 活动类型
                isTop: false, // 是否置顶
                state: '', // 活动状态
                isJoin: 0, // 是否已参与活动
                form: '', // 活动表单配置
                btnText: '', // 按钮文本
                btnInfo: {
                    // 底部按钮配置  --新增
                    text: '', // 按钮文本
                    linkUrl: '', // 按钮链接类型
                    query: {} // 按钮链接参数
                },
                btnState: 0, // 按钮状态 0-不可点击 1-可点击
                btnType: '' // mini-小程序类型 H5-网页类型 draw-抽奖活动
            }
        };
    },
    onLoad() {
        this.id = this.$Route.query.id;
        // 页面加载时获取活动详情
        if (this.id) {
            this.fetchActivityDetails();
        }
    },
    computed: {
        ...mapState(['hasLogin']),
        buttonVisible() {
            const { detail } = this;
            // 根据活动类型和状态决定底部按钮是否显示
            let visible = false;
            if (detail.type != 1 && detail.isJoin != 1) {
                visible = true; // 问卷类型、留资只在活动进行中且没有参与过时显示
            } else if (detail.type == 1 && detail.btnState) {
                // 普通活动类型、 根据配置是否显示
                visible = true;
            }
            return visible;
        }
    },
    methods: {
        // 格式化时间
        formatTime(time) {
            if (!time) return '';
            return this.dayjs(time).format('YYYY.MM.DD HH.mm');
        },
        // 活动详情获取
        fetchActivityDetails() {
            this.$request({
                url: 'v3/promotion/front/questionnaire/detail',
                data: {
                    questionnaireId: this.id
                }
            }).then((res) => {
                if (res.state == 200) {
                    const data = res.data;
                    if (data.btnInfo) {
                        data.btnInfo = JSON.parse(data.btnInfo);
                    }
                    this.detail = data;
                }
            });
        },
        // 跳转操作
        jump() {
            if (!this.hasLogin) {
                this.$api.msg('请先登录');
                return;
            }
            if (this.detail.state !== 2) {
                this.$api.msg('活动已结束或未开始');
                return;
            }
            // 如果是问卷类型，跳转到问卷详情页
            if (this.detail.type == 2) {
                this.$Router.push({
                    path: '/newPages/activity/qform',
                    query: {
                        id: this.id
                    }
                });
            }
            // 如果是留资类型，跳转到留资表单页
            if (this.detail.type == 3) {
                this.$Router.push({ path: '/newPages/activity/signup', query: { id: this.id, form: this.detail.form } });
            }
            // 如果是普通活动类型，跳转到活动详情页
            if (this.detail.type == 1) {
                if (this.detail.btnType === 'mini') {
                    uni.navigateTo({ url: this.detail.btnInfo.url });
                } else if (this.detail.btnType === 'H5') {
                    this.$Router.push({ path: '/pages/public/webView', query: { url: this.detail.btnInfo.url } });
                } else if (this.detail.btnType === 'draw') {
                    this.$Router.push({ path: '/standard/lottery/detail', query: { drawId: this.detail.btnInfo.drawId } });
                }
            }
        }
    }
};
</script>
<style lang="scss">
.page {
    width: 750rpx;
    min-height: 100vh;
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    // 背景图片固定，不滚动
    background-attachment: fixed;
    padding-bottom: 160rpx; // 底部按钮高度

    .container {
        width: 96%;
        margin: 0 auto;
        background-color: #fff;
        border-radius: 40rpx;
        padding-bottom: 30rpx;
        .activity_header {
            .header_img {
                width: 100%;
                height: 250rpx;
                border-radius: 40rpx 40rpx 0 0;
                object-fit: cover;
            }
        }
        .activity_info {
            width: 95%;
            margin: 30rpx auto;
            background-color: rgba(242, 242, 242, 1);
            padding: 20rpx;
            .info_row {
                display: flex;
                flex-direction: column;
                margin-bottom: 30rpx;
                .time {
                    display: flex;
                    align-items: center;
                    font-size: 28rpx;
                    color: rgba(153, 153, 153, 1);
                    margin-bottom: 15rpx;
                    .icon {
                        width: 30rpx;
                        height: 30rpx;
                        margin-right: 10rpx;
                    }
                }
                .value {
                    flex: 1;
                    font-size: 28rpx;
                    color: #333;
                }
            }
        }
        .activity-detail {
            width: 95%;
            margin: 30rpx auto;
        }
    }
    .activity-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        display: flex;
        height: 110rpx;
        justify-content: center;
        align-items: center;
        padding-bottom: env(safe-area-inset-bottom); // 适配iPhone X等机型底部安全区域
        z-index: 100;
        &-btn {
            width: 90%;
            height: 80rpx;
            background: rgba(73, 73, 73, 1);
            color: #fff;
            border-radius: 40rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 32rpx;
            font-weight: 600;
            letter-spacing: 2rpx;
            position: relative;
            overflow: hidden;
        }
    }
}

// 底部按钮弹出动画
@keyframes slideUp {
    0% {
        transform: translateY(0);
    }

    70% {
        transform: translateY(120rpx);
    }

    100% {
        transform: translateY(160rpx);
    }
}

// 为底部安全区域做适配
@supports (bottom: constant(safe-area-inset-bottom)) {
    .activity-footer {
        padding-bottom: constant(safe-area-inset-bottom);
    }
}
</style>
