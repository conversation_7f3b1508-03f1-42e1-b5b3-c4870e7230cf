<template>
    <view class="page" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }">
        <ktabbar :bgImg="imgUrl + 'default_bg.jpg'" :title="'活动列表'"/>
        <view class="activity-list">
            <view class="activity-item" v-for="(item, index) in dataList" :key="index">
                <ActivityItem :detail="item" />
            </view>

            <!-- 空状态展示 -->
            <view class="empty-state" v-if="dataList.length === 0 && !isLoading">
                <image class="empty-image" :src="imgUrl + 'empty_goods.png'" mode="aspectFit"></image>
                <view class="empty-text">暂无活动信息</view>
            </view>

            <!-- 加载状态 -->
            <view class="loading-more" v-if="isLoading">
                <view class="loading-icon"></view>
                <text>加载中...</text>
            </view>

            <!-- 底部提示 -->
            <view class="bottom-tip" v-if="dataList.length > 0 && noMoreData">
                <text>— 已经到底啦 —</text>
            </view>
        </view>
    </view>
</template>
<script>
import ActivityItem from '@/components/activityItem.vue';
import ktabbar from '@/components/ktabbar.vue';
export default {
    components: {
        ActivityItem,
        ktabbar
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            dataList: [], // 活动列表数据
            query: {
                current: 1,
                pageSize: 10
            },
            isLoading: false, // 是否正在加载数据
            noMoreData: false // 是否没有更多数据
        };
    },
    onLoad() {
        // 获取活动列表
        this.fetchActivityList();
    },
    methods: {
        // 获取活动列表
        fetchActivityList() {
            this.isLoading = true; // 开始加载
            return new Promise((resolve) => {
                this.$request({
                    url: 'v3/promotion/front/questionnaire/list',
                    data: this.query
                }).then((res) => {
                    this.isLoading = false; // 结束加载
                    if (res.state == 200) {
                        const { list, pagination } = res.data;
                        if (this.query.current === 1) {
                            this.dataList = list;
                        } else {
                            this.dataList = this.dataList.concat(list);
                        }
                        // 如果当前页数据小于每页数量，说明没有更多数据了
                        if (pagination.total > pagination.current * pagination.pageSize) {
                            this.query.current += 1;
                        } else {
                            this.noMoreData = true;
                        }
                        resolve();
                    } else {
                        this.$api.msg(res.msg);
                    }
                });
            });
        },
        // 跳转至详情页
        jumpToDetail(item) {
            const { questionnaireId, type } = item;
            this.$Router.push({
                path: '/newPages/activity/detail',
                query: {
                    id: questionnaireId
                }
            });
        }
    },
    // 下拉刷新
    onPullDownRefresh() {
        this.query.current = 1; // 重置页码
        this.noMoreData = false; // 重置没有更多数据标志
        this.fetchActivityList().then(() => {
            uni.stopPullDownRefresh(); // 停止下拉刷新
        });
    },
    // 上拉加载更多
    onReachBottom() {
        if (!this.noMoreData) {
            this.fetchActivityList();
        }
    }
};
</script>
<style lang="scss">
.page {
    width: 100%;
    max-width: 750rpx;
    min-height: 100vh;
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    // 背景图片固定，不滚动
    background-attachment: fixed;

    .activity-list {
        padding: 20rpx;

        .activity-item {
            margin-bottom: 20rpx;
            &:last-child {
                margin-bottom: 0;
            }
        }

        // 空状态样式
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 100rpx 0;

            .empty-image {
                width: 200rpx;
                height: 200rpx;
                margin-bottom: 30rpx;
            }

            .empty-text {
                font-size: $fs-s;
                color: #999999;
            }
        }

        // 骨架屏加载状态
        .skeleton {
            background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
            background-size: 400% 100%;
            animation: skeleton-loading 1.5s ease infinite;
            border-radius: 8rpx;
        }

        // 底部提示样式
        .bottom-tip {
            text-align: center;
            padding: 30rpx 0;
            color: #999;
            font-size: 24rpx;
            position: relative;

            text {
                padding: 0 30rpx;
                position: relative;
                z-index: 1;
            }

            &::before {
                content: '';
                position: absolute;
                top: 50%;
                left: 15%;
                right: 15%;
                height: 1rpx;
                z-index: 0;
            }
        }

        // 加载更多样式
        .loading-more {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 30rpx 0;

            .loading-icon {
                width: 40rpx;
                height: 40rpx;
                margin-right: 12rpx;
                border: 3rpx solid #f3f3f3;
                border-top: 3rpx solid #666;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            text {
                font-size: 26rpx;
                color: #999;
            }
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    }
}
</style>
