<template>
    <view class="page">
        <form class="form" @submit="submitForm">
            <view class="kk-form-group border-line" v-if="query.form.includes(1)">
                <view class="form_title label_left">
                    <text>姓名</text>
                    <text class="request">*</text>
                </view>
                <view class="form_value">
                    <input v-model="formData.name" class="form_input" placeholder="请输入姓名" placeholder-class="placeholder" name="name" />
                </view>
            </view>
            <view class="kk-form-group border-line" v-if="query.form.includes(2)">
                <view class="form_title label_left">
                    <text>手机号</text>
                    <text class="request">*</text>
                </view>
                <view class="form_value">
                    <input v-model="formData.mobile" class="form_input" placeholder="请输入手机号" placeholder-class="placeholder" name="mobile" maxlength="11" />
                </view>
            </view>
            <view class="kk-form-group border-line" v-if="query.form.includes(3)">
                <view class="form_title label_left">
                    <text>同行人数量</text>
                    <!-- <text class="request">*</text> -->
                </view>
                <view class="form_value">
                    <input v-model="formData.num" class="form_input" placeholder="请输入同行人数量" placeholder-class="placeholder" name="num" />
                </view>
            </view>
            <view class="kk-submit-btn">
                <button class="submit-btn" form-type="submit">立即参与</button>
                <view class="submit_tips">点击提交即同意本应用收集您填写的信息</view>
            </view>
        </form>
    </view>
</template>

<script>
import { mapState } from 'vuex';
export default {
    data() {
        return {
            query: {
                id: null,
                form: [] // 表单配置数据
            },
            formData: {
                name: '', // 姓名
                mobile: '',
                num: '' // 同行人数量
            },
            rules: {
                name: {
                    type: 'string',
                    required: true,
                    message: '请填写姓名',
                    trigger: ['blur', 'change']
                },
                mobile: {
                    type: 'number',
                    required: true,
                    message: '请填写手机号',
                    trigger: ['blur', 'change']
                }
            },
            submiting: false // 提交状态
        };
    },
    computed: {
        ...mapState(['hasLogin']) // 是否登录
    },
    onLoad() {
        const { id, form } = this.$Route.query;
        this.query = {
            id: id || null,
            form: form && JSON.parse(form).formKeys ? JSON.parse(form).formKeys : []
        };
    },
    methods: {
        // 提交
        submitForm() {
            if (!this.hasLogin) {
                this.$api.msg('请先登录');
                return;
            }
            const { name, mobile, num } = this.formData;
            const { form } = this.query;
            if (form.includes(1) && !name) {
                this.$api.msg('请填写姓名');
                return;
            }
            if (form.includes(2) && !mobile) {
                this.$api.msg('请填写手机号');
                return;
            }
            if (form.includes(2) && !/^1[3-9]\d{9}$/.test(mobile)) {
                this.$api.msg('手机号格式不正确');
                return;
            }
            if (form.includes(3) && num && isNaN(num)) {
                this.$api.msg('同行人数量必须为数字');
                return;
            }
            // 组合content数据
            const content = [
                {
                    title: '姓名',
                    type:'Input',
                    value: name || '' // 如果没有填写姓名，默认为空
                },
                {
                    title: '手机号',
                    type:'Input',
                    value: mobile || '' // 如果没有填写手机号，默认为空
                },
                {
                    title: '同行人数量',
                    type:'Input',
                    value: num || 0 // 如果没有填写同行人数量，默认为0
                }
            ];
            this.$request({
                url: 'v3/promotion/front/questionnaire/answer',
                method: 'POST',
                header: {
                    'Content-Type': 'application/json'
                },
                data: {
                    questionnaireId: this.query.id,
                    content: JSON.stringify(content)
                }
            }).then((res) => {
                if (res.state == 200) {
                    this.$api.msg('提交成功');
                    this.$Router.back();
                } else {
                    this.$api.msg(res.msg);
                }
            });
        }
    }
};
</script>
<style lang="scss">
.page {
    width: 100%;
    max-width: 750rpx;
    min-height: 100vh;
    background-color: $bg1;
    .request {
        color: #ff0000;
    }
    .border-line {
        border-bottom: 1px solid #e5e5e5;
    }
    .form {
        width: 100%;
        box-sizing: border-box;
        background-color: #ffffff;
        display: flex;
        flex-direction: column;
        padding: 30rpx 4%;
    }
    .kk-form-group {
        display: flex;
        align-items: center;
        column-gap: 20rpx;
        min-height: 120rpx;
        --label-width: calc(4em + 50rpx);
        .form_title {
            font-size: $fs-base;
            min-width: var(--label-width);
        }
        .label_right {
            text-align: right;
        }
        .label_left {
            text-align: left;
        }
        .form_value {
            flex: 1 1 calc(100% - var(--label-width) - 20rpx);
            display: flex;
            align-items: center;
            padding-right: 20rpx;
            .form_input {
                width: 100%;
                font-size: $fs-base;
                &.left {
                    text-align: left;
                }
                &.right {
                    text-align: right;
                }
            }
        }

        .picker {
            text-align: left;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: $fs-base;
            .picker-value {
                flex: 1;
            }
            .el-placeholder {
                color: #9d9d9d;
            }
        }
    }
    .kk-submit-btn {
        width: 100%;
        display: flex;
        justify-content: center;
        margin-top: 100rpx;
        flex-direction: column;
        .submit-btn {
            width: 100%;
            height: 80rpx;
            background-color: $color1;
            color: #ffffff;
            border-radius: 40rpx;
            font-size: $fs-base;
            text-align: center;
            line-height: 80rpx;
        }
        .submit_tips {
            width: 100%;
            text-align: center;
            color: #9d9d9d;
            margin-top: 20rpx;
            font-size: $fs-m;
        }
    }
}
</style>
