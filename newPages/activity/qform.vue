<template>
    <view class="page">
        <!-- 表单 -->
        <view class="form-container">
            <!-- <view class="form-title">{{ detail.questionnaireName }}</view> -->
            <!-- <view class="form-description">{{ detail.description }}</view> -->
            <view class="form-content">
                <view v-for="(item, index) in formOptions" :key="item.key" class="kk-form-group"
                    v-show="formData[item.key].visible">
                    <view class="form_title" v-if="item['x-component'] !== 'Text'">
                        <text class="request">*</text>
                        <text>{{ item.title }}</text>
                    </view>
                    <view class="form_value">
                        <!-- 文本展示 -->
                        <view v-if="item['x-component'] == 'Text'" class="form-warp">
                            <view class="form_text"> {{ item['x-component-props'].content }} </view>
                        </view>
                        <!-- 输入框 -->
                        <view v-if="item['x-component'] == 'Input'" class="form-warp">
                            <input class="form_input" v-model="formData[item.key].value"
                                :placeholder="item['x-component-props'].placeholder"
                                @input="(e) => inputChange(e, item)" />
                        </view>
                        <!-- 文本框 -->
                        <view v-if="item['x-component'] == 'Input.TextArea'" class="form-warp">
                            <textarea class="form_textarea" v-model="formData[item.key].value"
                                :placeholder="item['x-component-props'].placeholder"
                                :maxlength="item['x-component-props'].maxLength"
                                @input="(e) => inputChange(e, item)"></textarea>
                        </view>
                        <!-- 多选 -->
                        <view v-if="item['x-component'] == 'Checkbox.Group'" class="form-warp">
                            <checkbox-group @change="(e) => checkboxChange(e, item)" class="form_checkbox">
                                <!-- :class="{ checked: formData[item.key] ? formData[item.key].includes(option.value) : false }" -->
                                <label class="check_label" v-for="(option, idx) in item.options" :key="idx">
                                    <view class="cell_hd">
                                        <checkbox :value="option.value" color="#c70e2d" style="transform: scale(0.8)" />
                                    </view>
                                    <view class="cell_bd">{{ option.label }}</view>
                                </label>
                            </checkbox-group>
                        </view>
                        <!-- 单选 -->
                        <view v-if="item['x-component'] == 'Radio.Group'" class="form-warp">
                            <radio-group @change="(e) => radioChange(e, item)" class="form_radiobox">
                                <label class="radio_label" v-for="option in item.options" :key="option.value">
                                    <view>
                                        <radio :value="option.value" color="#c70e2d" style="transform: scale(0.8)" />
                                    </view>
                                    <view>{{ option.label }}</view>
                                </label>
                            </radio-group>
                        </view>
                        <!-- 评分 -->
                        <view v-if="item['x-component'] == 'Rate'" class="form-warp">
                            <view class="form_rate">
                                <uni-rate v-model="formData[item.key].value" :margin="10" color="#c5c5c5"
                                    activeColor="#c70e2d" @change="(e) => onChangeRate(e, item)" />
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <!-- 底部留资按钮 -->
        <view class="kk-submit-btn" v-show="hasLogin && formOptions.length > 0">
            <view class="submit-btn" @click="submit()">提 交</view>
            <view class="submit_tips">点击提交即同意本应用收集您填写的信息</view>
        </view>
        <!-- 底部高度填充 -->
        <view style="height: 50rpx;"></view>
    </view>
</template>

<script>
import { mapState } from 'vuex';
import uniRate from '@/components/uni-rate/uni-rate.vue';
// 新增：兼容小程序的简单表达式解析函数
function parseSimpleExpr(expr, $deps) {
    // 替换 $deps.xxx 为实际值
    expr = expr.replace(/\$deps\.([a-zA-Z0-9_]+)/g, (match, p1) => {
        let v = $deps[p1];
        if (typeof v === 'string') return `"${v}"`;
        if (typeof v === 'undefined' || v === null) return 'null';
        return v;
    });
    // console.log('解析表达式:', expr, $deps);
    // 只支持 ==, !=, >, <, >=, <=, &&, ||，不支持复杂嵌套
    // 拆分 ||
    let orParts = expr.split('||').map((part) => part.trim());
    for (let orPart of orParts) {
        let andParts = orPart.split('&&').map((p) => p.trim());
        let andResult = true;
        for (let andPart of andParts) {
            // 匹配比较表达式
            let m = andPart.match(/^(.+?)(==|!=|>=|<=|>|<)(.+)$/);
            if (m) {
                let left = m[1].trim();
                let op = m[2];
                let right = m[3].trim();
                // 转换为数字或字符串
                if (!isNaN(left)) left = Number(left);
                if (!isNaN(right)) right = Number(right);
                // 比较
                let cmp = false;
                switch (op) {
                    case '==':
                        cmp = left == right;
                        break;
                    case '!=':
                        cmp = left != right;
                        break;
                    case '>=':
                        cmp = left >= right;
                        break;
                    case '<=':
                        cmp = left <= right;
                        break;
                    case '>':
                        cmp = left > right;
                        break;
                    case '<':
                        cmp = left < right;
                        break;
                }
                andResult = andResult && cmp;
            } else {
                // 不是比较表达式，直接判断真值
                andResult = andResult && !!andPart;
            }
        }
        if (andResult) return true;
    }
    return false;
}
export default {
    components: {
        uniRate
    },
    data() {
        return {
            id: '',
            detail: {},
            formData: {}, // 表单数据
            formOptions: [], // 表单选项
            reactionsMap: {} // 记录依赖关系和表达式
        };
    },
    computed: {
        ...mapState(['hasLogin']) // 是否登录
    },
    onLoad() {
        this.id = this.$Route.query.id || '';
        // 如果有ID，则获取活动详情
        if (this.id) {
            this.fetchActivityDetails();
        } else {
            this.$api.msg('活动不存在或已过期');
        }
    },
    methods: {
        // 提交表单
        submit() {
            if (!this.hasLogin) {
                this.$api.msg('请先登录');
                return;
            }
            // 检查必填项
            for (const key in this.formData) {
                if (this.formData[key].visible && this.formData[key].value === null && this.formData[key].title) {
                    this.$api.msg(`请填写${this.formData[key].title}`);
                    return;
                }
            }
            // 提交数据
            const submitData = [];
            for (const key in this.formData) {
                if (!this.formData[key].value) continue; // 跳过未填写的项
                submitData.push({
                    title: this.formData[key].title,
                    type: this.formData[key].type || 'text', // 默认类型为text
                    value: this.formData[key].valueName || this.formData[key].value
                })
            }
            // console.log('提交数据:', submitData,this.formData);
            // return;
            this.$request({
                url: 'v3/promotion/front/questionnaire/answer',
                method: 'POST',
                header: {
                    'Content-Type': 'application/json'
                },
                data: {
                    questionnaireId: this.id,
                    content: JSON.stringify(submitData)
                }
            }).then((res) => {
                if (res.state == 200) {
                    this.$api.msg('提交成功');
                    this.$Router.back();
                } else {
                    this.$api.msg(res.msg);
                }
            });
        },
        // 多选change
        checkboxChange(e, record) {
            const { value } = e.detail;
            const { key, title, options } = record;
            const valueName = options.filter((opt) => value.includes(String(opt.value))).map((item) => item.label);
            this.formData[key] = {
                ...this.formData[key],
                title,
                value: value.map((item) => Number(item)), // 选中的值
                valueName: valueName // 选中的名称
            };
            this.updateAllVisible(); // 新增：联动显示隐藏
        },
        // 单选change
        radioChange(e, record) {
            const { value } = e.detail;
            this.formData[record.key] = {
                ...this.formData[record.key],
                title: record.title,
                value: Number(value), // 选中的值
                valueName: record.options.find((item) => item.value == value).label // 选中的名称
            };
            this.updateAllVisible(); // 新增：联动显示隐藏
        },
        // 输入框change
        inputChange(e, record) {
            this.formData[record.key] = {
                ...this.formData[record.key],
                value: e.detail.value
            };
            this.updateAllVisible(); // 新增：联动显示隐藏
        },
        // 评分change
        onChangeRate(e, record) {
            const value = e.value;
            this.formData[record.key] = {
                ...this.formData[record.key],
                value: Number(value), // 评分值
                valueName: value.toString() // 评分名称
            };
            this.updateAllVisible(); // 新增：联动显示隐藏
        },

        // 获取活动详情
        fetchActivityDetails() {
            this.$request({
                url: 'v3/promotion/front/questionnaire/detail',
                data: {
                    questionnaireId: this.id
                }
            }).then((res) => {
                if (res.state == 200) {
                    this.detail = res.data;
                    uni.setNavigationBarTitle({
                        title: this.detail.questionnaireName || '问卷调查'
                    });
                    this.formatFormOptions(res.data.form);
                } else {
                    this.$api.msg(res.msg);
                }
            });
        },
        // 格式化表单选项
        formatFormOptions(formJson) {
            const { form, schema } = JSON.parse(formJson);
            const formArr = [];
            const formKeys = {};
            this.reactionsMap = {}; // 新增
            if (schema.properties) {
                for (const key in schema.properties) {
                    formArr.push({
                        ...schema.properties[key],
                        options: schema.properties[key].enum || [],
                        key: key
                    });
                    // 记录依赖关系和表达式
                    const reactions = schema.properties[key]['x-reactions'];
                    if (reactions && reactions.fulfill && reactions.fulfill.state && reactions.fulfill.state.visible) {
                        this.reactionsMap[key] = {
                            dependencies: reactions.dependencies,
                            expression: reactions.fulfill.state.visible
                        };
                    }
                    formKeys[key] = {
                        type: schema.properties[key]['x-component'] || 'text', // 默认类型为text
                        visible: true, // 初始化为true，后续统一计算
                        title: schema.properties[key].title || '',
                        value: null, // 关键：初始为null
                        valueName: ''
                    }; // 初始化表单数据
                }
                this.formOptions = formArr;
                this.formData = formKeys;
                this.$nextTick(() => {
                    this.updateAllVisible(); // 新增：初始化时计算一次
                });
            } else {
                this.$api.msg('问卷格式不正确');
            }
        },
        // 更新所有字段的visible
        updateAllVisible() {
            Object.keys(this.reactionsMap).forEach((key) => {
                this.updateVisible(key);
            });
        },
        // 更新单个字段的visible
        updateVisible(key) {
            const { dependencies, expression } = this.reactionsMap[key];
            let $deps = {};
            if (dependencies && dependencies.length) {
                dependencies.forEach((dep) => {
                    let v = this.formData[dep.source]?.value;
                    if (dep.type && dep.type.includes('number') && v !== null && v !== undefined && v !== '') {
                        v = Number(v);
                    }
                    $deps[dep.name] = v;
                });
            }
            let visible = true;
            try {
                let jsExpr = typeof expression === 'string' ? expression.replace(/\{\{\s*|\s*\}\}/g, '') : '';
                if (!jsExpr.trim()) {
                    visible = true; // 没有表达式默认可见
                } else {
                    visible = parseSimpleExpr(jsExpr, $deps);
                }
            } catch (e) {
                visible = false; // 报错时默认不可见
            }
            if (this.formData[key] && this.formData[key].visible !== visible) {
                this.$set(this.formData[key], 'visible', visible);
            }
        }
    }
};
</script>
<style lang="scss">
.page {
    width: 100%;
    max-width: 750rpx;
    min-height: 100vh;
    background: $bg1;

    .form-container {
        width: 100%;
        padding: 20rpx 4%;

        .form-title {
            padding: 20rpx 0;
            text-align: center;
            font-size: $fs-lg;
            font-weight: bold;
            color: #000;
        }

        .form-description {
            padding: 10rpx 0;
            text-align: left;
            text-indent: 2em;
            font-size: $fs-base;
            color: #666;
            line-height: 1.5;
        }

        .form-content {
            margin-top: 50rpx;
            display: flex;
            flex-direction: column;
            row-gap: 60rpx;
        }
    }
}

.request {
    color: #ff0000;
    margin-right: 4px;
}

.kk-form-group {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    row-gap: 20rpx;

    .form_title {
        font-size: $fs-base;
        width: 100%;
    }

    .label_right {
        text-align: right;
    }

    .label_left {
        text-align: left;
    }

    .form_value {
        width: 100%;
        display: flex;
        align-items: center;

        .form-warp {
            width: 100%;
        }

        .form_input {
            width: 100%;
            font-size: $fs-base;
            border: 1px solid #e5e5e5;
            border-radius: 8rpx;
            padding: 0 15rpx;
            height: 80rpx;
            line-height: 80rpx;
            box-sizing: border-box;

            &.left {
                text-align: left;
            }

            &.right {
                text-align: right;
            }
        }
    }

    .form_text {
        width: 100%;
        font-size: $fs-base;
        color: #333;
        line-height: 1.5;
    }

    .form_checkbox {
        display: flex;
        flex-direction: column;
        width: 100%;
        row-gap: 15rpx;

        .check_label {
            display: flex;
            align-items: center;
            width: 100%;
            border-radius: 8rpx;
            border: 1px solid;
            border-color: #e5e5e5;
            padding: 10rpx;

            .cell_hd {
                margin-right: 10rpx;
            }

            .cell_bd {
                font-size: $fs-base;
                color: #333;
            }

            &.checked {
                border-color: $color1;
            }
        }
    }

    .form_radiobox {
        display: flex;
        flex-direction: column;
        width: 100%;
        row-gap: 15rpx;

        .radio_label {
            display: flex;
            align-items: center;
            border-radius: 8rpx;
            border: 1px solid;
            border-color: #e5e5e5;
            padding: 10rpx;

            .cell_hd {
                margin-right: 10rpx;
            }

            .cell_bd {
                font-size: $fs-base;
                color: #333;
            }

            &.checked {
                border-color: $color1;
            }
        }
    }

    .form_textarea {
        width: 100%;
        font-size: $fs-base;
        border: 1px solid #e5e5e5;
        border-radius: 8rpx;
        padding: 15rpx;
        height: 200rpx;
        line-height: 1.5;
        box-sizing: border-box;
    }

    .form_rate {
        width: 100%;
        padding: 20rpx 20rpx;
        background-color: #efefef;
        border-radius: 8rpx;
    }

    .picker {
        text-align: left;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: $fs-base;

        .picker-value {
            flex: 1;
        }

        .el-placeholder {
            color: #9d9d9d;
        }
    }
}

.kk-submit-btn {
    width: 100%;
    padding: 0 4%;
    display: flex;
    justify-content: center;
    margin-top: 100rpx;
    flex-direction: column;

    .submit-btn {
        width: 100%;
        height: 80rpx;
        background-color: $color1;
        color: #ffffff;
        border-radius: 40rpx;
        font-size: $fs-base;
        text-align: center;
        line-height: 80rpx;
    }

    .submit_tips {
        width: 100%;
        text-align: center;
        color: #9d9d9d;
        margin-top: 20rpx;
        font-size: $fs-m;
    }
}
</style>
