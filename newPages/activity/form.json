{"form": {"labelCol": 6, "wrapperCol": 12, "layout": "vertical", "size": "default", "fullness": false, "wrapperWrap": false, "labelWrap": false}, "schema": {"type": "object", "properties": {"dznoqp74bpq": {"type": "string", "title": "您所在的城市", "x-decorator": "FormItem", "x-component": "Input", "x-validator": [], "x-component-props": {"placeholder": "请输入你所在的城市"}, "x-decorator-props": {"tooltip": ""}, "x-designable-id": "dznoqp74bpq", "x-index": 0}, "xfnmiskivd6": {"type": "string | number", "title": "性别", "x-decorator": "FormItem", "x-component": "Radio.Group", "enum": [{"children": [], "label": "男", "value": 1}, {"children": [], "label": "女", "value": 2}], "x-validator": [], "x-component-props": {}, "x-decorator-props": {}, "x-designable-id": "xfnmiskivd6", "x-index": 1}, "fabqlfdninx": {"type": "Array<string | number>", "title": "喜欢舒适型还是运动型", "x-decorator": "FormItem", "x-component": "Checkbox.Group", "enum": [{"children": [], "label": "舒适", "value": 1}, {"children": [], "label": "运动", "value": 2}], "x-validator": [], "x-decorator-props": {}, "x-designable-id": "fabqlfdninx", "x-index": 2, "x-reactions": {"dependencies": [{"property": "value", "type": "string | number", "source": "xfnmiskivd6", "name": "six"}], "fulfill": {"state": {"visible": "{{$deps.six == 1}}"}}}}, "6yropkv8f00": {"type": "string | number", "title": "您的职业或工作类型是？（单选）", "x-decorator": "FormItem", "x-component": "Radio.Group", "enum": [{"children": [], "label": "公司职员 ", "value": 1}, {"children": [], "label": "企业中/高层管理者 ", "value": 2}, {"children": [], "label": "个体户 ", "value": "3"}], "x-validator": [], "x-component-props": {}, "x-decorator-props": {}, "x-designable-id": "6yropkv8f00", "x-index": 3}, "yc9oly780uq": {"type": "Array<string | number>", "title": "你日常用车的用途是", "x-decorator": "FormItem", "x-component": "Checkbox.Group", "enum": [{"children": [], "label": "上下班", "value": 1}, {"children": [], "label": "上下学", "value": 2}, {"children": [], "label": "快递/外卖", "value": "3"}, {"children": [], "label": "日常代步", "value": "4"}], "x-validator": [], "x-decorator-props": {}, "x-designable-id": "yc9oly780uq", "x-index": 4}, "nl3bt67kajj": {"type": "number", "title": "根据您的产品使用体验，对以下内容进行满意度评价：", "x-decorator": "FormItem", "x-component": "Rate", "x-validator": [], "x-component-props": {}, "x-decorator-props": {}, "x-designable-id": "nl3bt67kajj", "x-index": 5}, "6rw4qptsd70": {"type": "string", "title": "你最满意的地方是什么", "x-decorator": "FormItem", "x-component": "Input.TextArea", "x-validator": [], "x-component-props": {"maxLength": 200, "placeholder": "请输入你满意的地方"}, "x-decorator-props": {}, "x-designable-id": "6rw4qptsd70", "x-index": 6, "x-reactions": {"dependencies": [{"property": "value", "type": "any", "source": "nl3bt67kajj", "name": "rate"}, {"property": "value", "type": "string | number", "source": "xfnmiskivd6", "name": "v_99cxrtqs1he"}], "fulfill": {"state": {"visible": "{{$deps.rate > 4 && $deps.v_99cxrtqs1he == 1}}"}}}}, "p0whdsoq2e5": {"type": "string", "title": "你不满意的地方是什么", "x-decorator": "FormItem", "x-component": "Input.TextArea", "x-validator": [], "x-component-props": {"maxLength": 200, "placeholder": "请输入你不满意的地方"}, "x-decorator-props": {}, "x-reactions": {"dependencies": [{"property": "value", "type": "number", "source": "nl3bt67kajj", "name": "rate"}], "fulfill": {"state": {"visible": "{{$deps.rate < 3}}"}}}, "x-designable-id": "p0whdsoq2e5", "x-index": 7}}, "x-designable-id": "bcpyfr92qfw"}}