<template>
	<view class="container flex_column_start_start" :style="mix_diyStyle">
		<view class="top flex_column_start_center">
			<view class="member_info flex_row_center_center">
				<view class="avatar" :style="{backgroundImage: 'url(' + userCenterData.memberAvatar + ')'}" />
				<text class="name">{{ rechargeDetail.memberName }}</text>
			</view>
			<view :class="{amount: true,success: rechargeDetail.rechargeState == 2,flex_row_center_center: true}">
				<text class="flag">+</text>
				<text>{{ rechargeDetail.payAmount }}</text>
			</view>
			<view class="tip">{{ $L('交易金额') }}</view>
		</view>
		<view v-for="(item, key, index) in detail" :key="index" class="detail_item flex_row_between_center">
			<text class="left">{{ item.name }}</text>
			<text class="right">{{ item.value }}</text>
		</view>
		<view v-if="rechargeDetail.payState == 1&&rechargeEnable" class="recharge_btn flex_row_center_center" @click="confirmRecharge">
			{{ $L('继续充值') }}
		</view>
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex'
	export default {
		data() {
			return {
				rechargeDetail: {}, //充值数据
				detail: [],
				rechargeId: '', //充值id
				rechargeEnable:true,
				showState:false
			}
		},
		onShow(){
			if(this.showState){
				this.getDetail(this.rechargeId)
				this.showState = false
			}
		},
		onLoad(options) {
			setTimeout(() => {
				uni.setNavigationBarTitle({
					title: this.$L('充值详情')
				})
			}, 0);

			if (this.$Route.query.rechargeId) {
				this.rechargeId = this.$Route.query.rechargeId
				this.getDetail(this.rechargeId)
			}
			
			this.getOutputSet()
		},
		computed: {
			...mapState(['userInfo', 'userCenterData'])
		},
		mounted() {},
		methods: {
			//获取充值详情
			getDetail(id) {
				this.detail = []
				this.$request({
						url: 'v3/member/front/balanceRecharge/detail',
						data: {
							rechargeId: id
						}
					})
					.then((res) => {
						if (res.state == 200) {
							this.rechargeDetail = res.data
							if (res.data.rechargeState == 2) {
								this.detail.push({
									name: this.$L('交易状态'),
									value: res.data.payStateValue
								})
								this.detail.push({
									name: this.$L('交易方式'),
									value: res.data.rechargeName
								})
								this.detail.push({
									name: this.$L('交易时间'),
									value: res.data.createTime
								})
								this.detail.push({
									name: this.$L('交易单号'),
									value: res.data.rechargeSn
								})
								this.detail.push({
									name: this.$L('交易流水号'),
									value: res.data.tradeSn
								})
							} else {
								this.detail.push({
									name: this.$L('交易状态'),
									value: res.data.payStateValue
								})
								this.detail.push({
									name: this.$L('交易时间'),
									value: res.data.createTime
								})
							}
						} else {
							this.$api.msg(res.msg)
						}
					})
					.catch((e) => {
						//异常处理
					})
			},
			//继续充值
			confirmRecharge() {
				this.showState = true
				this.$Router.push({
					path: '/pages/recharge/recharge',
					query: {
						rechargeSn: this.rechargeDetail.rechargeSn,
						payMethodType: 'rechargeDetail',
						rechargeId: this.rechargeId
					}
				})
			},
			//获取配置开关
			getOutputSet() {
			  let param = {}
			  param.url = 'v3/system/front/setting/getSettings'
			  param.method = 'GET'
			  param.data = {}
			  param.data.names = 'recharge_is_enable'
			  this.$request(param).then((res) => {
			    if (res.state == 200) {
			      this.rechargeEnable = res.data[0] == '1' ? true : false
			    } else {
			      this.$api.msg(res.msg)
			    }
			  })
			},
			
		}
	}
</script>

<style lang="scss">
	/* 高度自适应  $height为设计图上的高度*/
	@function calcAdaptHeight($height) {
		@return $height/1334 * 100 * 1vh;
	}

	page {
		background: #ffffff;
		width: 750rpx;
		margin: 0 auto;

		.container {
			margin-top: 20rpx;
			flex: 1;
			background: #fff;

			.top {
				width: 100%;
				height: calcAdaptHeight(354);
				border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);

				.member_info {
					margin-top: calcAdaptHeight(95);

					.avatar {
						background-size: cover;
						background-position: center center;
						background-repeat: no-repeat;
						width: 51rpx;
						height: 51rpx;
						overflow: hidden;
						background-color: #f8f6f7;
						border-radius: 50%;
					}

					.name {
						color: $main-font-color;
						font-size: 32rpx;
						margin-left: 10rpx;
					}
				}

				.amount {
					color: $main-third-color;
					font-size: 48rpx;
					font-weight: bold;
					margin-top: calcAdaptHeight(40);

					&.success {
						color: #ff2121;
					}

					.flag {
						margin-top: -5rpx;
						margin-right: 3rpx;
					}
				}

				.tip {
					color: $main-third-color;
					font-size: 28rpx;
					margin-top: calcAdaptHeight(12);
				}
			}

			.detail_item {
				width: 100%;
				padding: calcAdaptHeight(50) 40rpx 0;

				.left {
					color: $main-font-color;
					font-size: 28rpx;
				}

				.right {
					color: $main-third-color;
					font-size: 26rpx;
				}
			}

			.recharge_btn {
				position: absolute;
				left: 50%;
				transform: translateX(-375rpx);
				bottom: 40rpx;
				width: 670rpx;
				margin: 0 40rpx;
				height: 88rpx;
				background:  var(--color_main_bg);
				box-shadow: 0px 3rpx 20rpx 0px var(--color_halo);
				border-radius: 44rpx;
				color: #fff;
				font-size: 36rpx;
			}
		}
	}

	uni-page-body {
		display: flex;
		height: 100%;
	}
</style>
