<template>
    <view class="container" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }">
        <ktabbar id="ktabbar" :bgImg="imgUrl + 'default_bg.jpg'" :title="title" />
        <view class="content" v-if="addressList.length">
            <view class="address_list">
                <view v-for="(item, index) in addressList" :key="index" @click="checkAddress(item)" :class="{ list: true, 'b-b': index != addressList.length - 1 }">
                    <view class="wrapper flex_column_start_center">
                        <view class="u-box flex_row_start_center">
                            <view class="flex_row_start_center">
                                <text v-if="source != 2 && sourceId == item.addressId" class="iconfont iconziyuan33" style="margin-right: 10rpx"></text>
                                <text class="name">{{ item.memberName }}</text>
                            </view>
                            <text class="mobile">{{ item.telMobile }}</text>
                        </view>
                        <view class="address-box">
                            <text class="address">{{ item.addressAll }} {{ item.detailAddress }}</text>
                        </view>
                    </view>
                    <view class="operate_con flex_row_between_center">
                        <view class="set_default flex_row_start_center" @click="setDefault(item, index)">
                            <text class="iconfont iconziyuan33" v-if="item.addressId == defaultAddressId"></text>
                            <text v-else class="iconfont iconziyuan43"></text>
                            <text>默认</text>
                        </view>

                        <view class="op_left flex_row_end_center">
                            <view class="op_left_item flex_row_start_center" @click.stop="operateAddress('edit', item.addressId)">
                                <image :src="imgUrl + 'yixuan-selectAddress/edit_address.png'" mode=""></image>
                                <text>编辑</text>
                            </view>

                            <view class="op_left_item flex_row_start_center" @click.stop="delAddress(item.addressId)">
                                <image :src="imgUrl + 'yixuan-selectAddress/del_address.png'" mode=""></image>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <loadingState v-if="loadingState == 'first_loading' || addressList.length > 0" :state="loadingState" />
            <uni-popup ref="popup" type="dialog">
                <uni-popup-dialog
                    type="input"
                    :before-close="true"
                    :title="$L('提示')"
                    :content="popTip"
                    :duration="2000"
                    @close="cancelChange"
                    @confirm="confirmChange"
                ></uni-popup-dialog>
            </uni-popup>
        </view>
        <view class="flex_column_start_center empty_part" v-if="!addressList.length && loadingState != 'first_loading'">
            <view :style="{ backgroundImage: `url(${imgUrl + 'empty_address.png'})` }" class="img"></view>
            <text class="tip_con">{{ $L('还没有收货地址哦') }}~</text>
        </view>
        <view class="add_btn_bottom flex_row_center_center">
            <button class="add_btn flex_row_center_center" @click="operateAddress('add')">{{ $L('新建收货地址') }}</button>
        </view>
    </view>
</template>

<script>
import loadingState from '@/components/loading-state.vue';
import ktabbar from '@/components/ktabbar.vue';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue';
import { mapState, mapMutations } from 'vuex';
export default {
    components: {
        loadingState,
        uniPopup,
        uniPopupDialog,
        ktabbar
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            title: '',
            source: 0,
            sourceId: '', //下单页面选中的地址addressId
            curOperateId: '',
            loadingState: 'first_loading2',
            orderSn: '', //订单号
            currentAddressId: '', //记录当前长按点击的地址
            editing: false,
            addressId: 0,
            popTip: '',
            popType: '',
            defaultAddressId: 0,
            addressList: []
        };
    },
    onLoad() {
        this.title = this.$L('我的地址');
        this.orderSn = this.$Route.query.orderSn;
        this.source = this.$Route.query.source ? this.$Route.query.source : 0; // source ：3 从订单详情页的修改地址进入的    2：从订单列表的修改地址进入    1：从确认下单页面的修改地址进入的

        if (this.$Route.query.sourceOrderAddress) {
            this.sourceOrderAddress = decodeURIComponent(this.$Route.query.sourceOrderAddress);
        }

        if (this.$Route.query.sourceId) {
            this.sourceId = this.$Route.query.sourceId;
        }

        this.getAddressList();
    },
    computed: {
        ...mapState(['userInfo'])
    },
    methods: {
        ...mapMutations(['operateAddressData']),
        //获取地址列表
        getAddressList() {
            this.$request({
                url: 'v3/member/front/memberAddress/list',
                method: 'GET'
            })
                .then((res) => {
                    if (res.state == 200) {
                        this.operateAddressData(res.data.list);
                        const default_addr = res.data.list.find((item) => item.isDefault == 1);
                        if (default_addr) {
                            this.defaultAddressId = default_addr.addressId;
                        } else {
                            this.defaultAddressId = 0;
                        }
                        this.addressList = res.data.list;
                        // 如果是从订单修改地址进入的，选中对应的地址
                        res.data.list.forEach((item) => {
                            if (this.sourceOrderAddress) {
                                let orderaddress = this.sourceOrderAddress.split(',');
                                if (item.detailAddress == orderaddress[0] && item.telMobile == orderaddress[1]) {
                                    this.sourceId = item.addressId;
                                }
                            }
                        });
                    } else {
                        this.$api.msg(res.msg);
                    }
                    this.loadingState = 'complete';
                })
                .catch((e) => {
                    //异常处理
                });
        },
        cancelChange() {
            this.editing = false;
            this.$refs.popup.close();
        },
        confirmChange() {
            if (this.inComfirm) {
                return;
            }
            this.$refs.popup.close();
            if (this.popType == 'edit') {
                this.inComfirm = true;
                this.$request({
                    url: 'v3/business/front/orderOperate/updateAddress',
                    data: {
                        orderSn: this.orderSn,
                        addressId: this.addressId
                    },
                    method: 'POST'
                }).then((res) => {
                    if (res.state == 200) {
                        if (this.source == 3) {
                            //从订单详情页的修改地址进入的
                            this.$api.msg(res.msg);
                            setTimeout(() => {
                                this.$api.prePage().getOrderDetail();
                                this.$Router.back(1);
                                this.editing = false;
                                uni.setStorageSync('addressId', this.addressId);
                            }, 1500);
                        } else if (this.source == 2) {
                            //从订单列表的修改地址进入的
                            this.$api.msg(res.msg);
                            setTimeout(() => {
                                this.$Router.back(1);
                                this.editing = false;
                            }, 1500);
                        }
                    } else {
                        this.editing = false;
                        this.$api.msg(res.msg);
                    }
                });
            } else if (this.popType == 'del') {
                this.operateAddressData(this.addressList);
                this.$request({
                    url: 'v3/member/front/memberAddress/del',
                    data: {
                        addressIds: this.addressId
                    },
                    method: 'POST'
                }).then((res) => {
                    this.$api.msg(res.msg);
                    if (res.state == 200) {
                        //更新数据
                        let tmp_data_index = this.addressList.findIndex((item) => item.addressId == this.addressId);
                        this.addressList.splice(tmp_data_index, 1);
                        this.operateAddressData(this.addressList);
                    }
                });
            }
        },

        //选择地址
        checkAddress(item) {
            if (this.editing) {
                return;
            }
            if (this.source == 1) {
                // this.$api.prePage().orderAddress=item
                this.$api.prePage() && this.$api.prePage().changeAddress(item);
                this.$Router.back(1);
            } else if (this.source == 2 || this.source == 3) {
                this.editing = true;
                //从订单详情或订单列表里面进入的地址列表
                this.addressId = item.addressId;
                this.popTip = '确认修改地址?';
                this.popType = 'edit';
                this.$refs.popup.open();
            }
        },
        operateAddress(type, addressId) {
            if (type == 'add') {
                if (this.addressList.length >= 20) {
                    this.$api.msg('最多添加20个收货地址');
                    return;
                }
            }

            this.curOperateId = '';
            let query = {
                type
            };
            let url = `/newPages/address/operate?type=${type}`;
            if (type == 'edit') {
                query.addressId = addressId;
            }
            this.$Router.push({
                path: '/newPages/address/operate',
                query
            });
        },

        //删除地址事件
        delAddress(addressId) {
            this.popTip = '确定删除地址?';
            this.popType = 'del';
            this.addressId = addressId;
            this.$refs.popup.open();
        },

        //设置默认地址
        setDefault(item, index) {
            this.$request({
                url: 'v3/member/front/memberAddress/edit',
                method: 'POST',
                data: {
                    addressId: item.addressId,
                    isDefault: this.defaultAddressId == item.addressId ? 0 : 1
                }
            }).then((res) => {
                if (res.state == 200) {
                    if (this.defaultAddressId == item.addressId) {
                        this.defaultAddressId = '';
                    } else {
                        this.defaultAddressId = item.addressId;
                    }
                } else {
                    this.$api.msg(res.msg);
                }
            });
        }
    }
};
</script>

<style lang="scss">
.container {
    width: 750rpx;
    min-height: 100vh;
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    // 背景图片固定，不滚动
    background-attachment: fixed;
    -webkit-touch-callout: none;
    /*系统默认菜单被禁用*/
    -webkit-user-select: none;
    /*webkit浏览器*/
    -khtml-user-select: none;
    /*早期浏览器*/
    -moz-user-select: none;
    /*火狐*/
    -ms-user-select: none;
    /*IE10*/
    user-select: none;
    -webkit-touch-callout: none;
    -moz-touch-callout: none;
    -ms-touch-callout: none;
    .content {
        position: relative;
        width: 94%;
        margin: 0 auto;
        margin-top: 20rpx;
        padding-bottom: 148rpx;
        .list {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: flex-start;
            background: #fff;
            position: relative;
            margin-bottom: 32rpx;
            border-radius: 40rpx;
            box-shadow: 0 8rpx 32rpx 0 rgba(0, 0, 0, 0.06);
            padding: 30rpx 30rpx;
            row-gap: 30rpx;
            &.b-b {
                &:after {
                    display: none;
                }
            }
        }
        .wrapper {
            width: 100%;
            .u-box {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: space-between;
                width: 100%;
                font-size: 32rpx;
                color: #222;
                margin-bottom: 15rpx;

                .name {
                    margin-right: 24rpx;
                    max-width: 240rpx;
                    font-size: 32rpx;
                }
                .mobile {
                    color: #222;
                    font-weight: bold;
                    font-size: 32rpx;
                }
            }

            .address-box {
                width: 100%;
                display: flex;
                align-items: center;
                .address {
                    font-size: 28rpx;
                    color: #999;
                    line-height: 38rpx;
                    margin-top: 5rpx;
                    word-break: break-all;
                }
            }
        }
        .operate_con {
            width: 100%;
            padding-top: 20rpx;
            border-top: 1px solid #eee;
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 400;
            color: #333;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            .set_default {
                display: flex;
                align-items: center;
                font-size: 28rpx;
                color: #999;
                margin-right: 32rpx;
                .iconfont {
                    font-size: 32rpx;
                    margin-right: 8rpx;
                    color: #999;
                }
                .iconziyuan33 {
                    color: #e93b3b;
                }
            }
            .op_left {
                display: flex;
                flex-direction: row;
                align-items: center;
                column-gap: 30rpx;
                .op_left_item {
                    display: flex;
                    align-items: center;
                    margin-left: 32rpx;
                    color: #999;
                    font-size: 28rpx;
                    image {
                        width: 32rpx;
                        height: 32rpx;
                        margin-right: 8rpx;
                    }
                }
            }
        }
    }
}

.add_btn_bottom {
    position: fixed;
    width: 94%;
    height: 88rpx;
    bottom: 40rpx;
    left: 20rpx;
    padding: 0;
    margin: 0 auto;
    z-index: 95;
    background: transparent;
    .add_btn {
        width: 100%;
        font-size: 32rpx;
        color: #fff;
        height: 88rpx;
        background: #444;
        border-radius: 44rpx;
        letter-spacing: 1rpx;
        box-shadow: 0 8rpx 32rpx 0 rgba(0, 0, 0, 0.08);
    }
}

.empty_part {
    display: flex;
    flex: 1;
    width: 750rpx;
    padding-top: 150rpx;

    .img {
        width: 380rpx;
        height: 280rpx;
		background-position: center;
		background-repeat: no-repeat;
		background-size: contain;
        margin-bottom: 37rpx;
        margin-top: 88rpx;
    }

    .tip_con {
        color: $main-third-color;
        font-size: 26rpx;
    }

    .ope_btn {
        color: var(--color_main);
        font-size: 28rpx;
        padding: 0 25rpx;
        height: 54rpx;
        background: var(--color_halo);
        border-radius: 27rpx;
        margin-top: 20rpx;
    }
}
</style>
