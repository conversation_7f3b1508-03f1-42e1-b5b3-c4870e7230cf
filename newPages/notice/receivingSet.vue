<!-- 接收设置 -->
<template>
    <view class="page" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }">
        <ktabbar :bgImg="imgUrl + 'default_bg.jpg'" :title="'推送设置'" />
        <!-- 资产消息 -->
        <view class="container">
            <view class="setting_item" v-for="(item, index) in settingList" :key="index">
                <text>{{ item.tplName }}</text>
                <uv-switch
                    v-model="item.isReceive"
                    size="22"
                    :inactive-value="0"
                    :active-value="1"
                    inactive-color="#B4B4B4"
                    active-color="#c70e2d"
                    @change="modifySetting(item.tplCode, item.isReceive)"
                ></uv-switch>
            </view>
        </view>
    </view>
</template>

<script>
import { mapState } from 'vuex';
import ktabbar from '@/components/ktabbar.vue';
export default {
    components: {
        ktabbar
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            settingList: [
                {
                    tplName: '车辆消息',
                    tplCode: 'vehicle_msg',
                    isReceive: 1
                },
                {
                    tplName: '互动消息',
                    tplCode: 'interaction_msg',
                    isReceive: 1
                },
                {
                    tplName: '订单消息',
                    tplCode: 'order_msg',
                    isReceive: 1
                },
                {
                    tplName: '系统消息',
                    tplCode: 'system_msg',
                    isReceive: 0
                }
            ] //接收设置列表
        };
    },
    computed: {
        ...mapState(['userInfo'])
    },
    onLoad() {
        // this.loadData();
    },
    methods: {
        loadData() {
            let param = {};
            param.url = 'v3/msg/front/msg/setting/list';
            param.method = 'GET';
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    this.settingList = res.data;
                } else {
                    this.$api.msg(res.msg);
                }
            });
        },
        // 接收设置开关
        modifySetting(tplCode, isReceive) {
            return;
            let param = {};
            param.url = 'v3/msg/front/msg/setting/isReceive';
            param.method = 'POST';
            param.data = {
                tplCode,
                isReceive: isReceive == 0 ? 1 : 0
            };
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    this.loadData();
                } else {
                    this.$api.msg(res.msg);
                }
            });
        }
    }
};
</script>

<style lang="scss">
.page {
    width: 750rpx;
    min-height: 100vh;
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    // 背景图片固定，不滚动
    background-attachment: fixed;
}
.container {
    width: 92%;
    margin: 0 auto;
    margin-top: 50rpx;
    background: #fff;
    border-radius: 40rpx;
    padding: 40rpx 0rpx;
    display: flex;
    flex-direction: column;
    row-gap: 20rpx;
    .setting_item {
        display: flex;
        align-items: center;
        padding: 0 40rpx;
        line-height: 90rpx;
        height: 90rpx;
        justify-content: space-between;
        font-size: 28rpx;
        color: #000;
    }
}
</style>
