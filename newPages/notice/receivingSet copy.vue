<!-- 接收设置 -->
<template>
    <view class="page" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }">
        <ktabbar :bgImg="imgUrl + 'default_bg.jpg'" :title="'关于立马'" />
        <!-- 资产消息 -->
        <view class="setting_item_wrap" v-for="(item, index) in settingList" :key="index">
            <view class="setting_title">{{ item.tplName }}</view>
            <view class="setting_item" v-for="(item2, index2) in item.memberTplList" :key="index2">
                <text>{{ item2.tplName }}</text>
                <switch color="var(--color_main)" :checked="item2.isReceive == 1 ? true : false" class="switch_btn" @change="modifySetting(item2.tplCode, item2.isReceive)" />
            </view>
        </view>
    </view>
</template>

<script>
import { mapState } from 'vuex';
import ktabbar from '@/components/ktabbar.vue';
export default {
    components: {
        ktabbar
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            settingList: [] //接收设置列表
        };
    },
    computed: {
        ...mapState(['userInfo'])
    },
    onLoad() {
        setTimeout(() => {
            uni.setNavigationBarTitle({
                title: this.$L('接收设置')
            });
        }, 0);

        this.loadData();
    },
    methods: {
        loadData() {
            let param = {};
            param.url = 'v3/msg/front/msg/setting/list';
            param.method = 'GET';
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    this.settingList = res.data;
                } else {
                    this.$api.msg(res.msg);
                }
            });
        },
        // 接收设置开关
        modifySetting(tplCode, isReceive) {
            let param = {};
            param.url = 'v3/msg/front/msg/setting/isReceive';
            param.method = 'POST';
            param.data = {
                tplCode,
                isReceive: isReceive == 0 ? 1 : 0
            };
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    this.loadData();
                } else {
                    this.$api.msg(res.msg);
                }
            });
        }
    }
};
</script>

<style lang="scss">
.page {
    width: 750rpx;
    min-height: 100vh;
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    // 背景图片固定，不滚动
    background-attachment: fixed;
}
.container {
    width: 92%;
    margin: 0 auto;
    margin-top: 50rpx;
    background: #fff;
    border-radius: 40rpx;
    padding: 40rpx 0rpx;
    display: flex;
    flex-direction: column;
    row-gap: 20rpx;
}

.setting_item_wrap {
    width: 750rpx;
    box-sizing: border-box;
    padding-left: 20rpx;
    padding-right: 20rpx;
    background: #fff;
    margin-top: 20rpx;
    box-sizing: border-box;
    .setting_title {
        width: 100%;
        height: 81rpx;
        font-size: 32rpx;
        color: #333;
        font-weight: bold;
        display: flex;
        align-items: center;
        border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
    }

    .setting_item {
        display: flex;
        align-items: center;
        height: 110rpx;
        justify-content: space-between;
        font-size: 28rpx;
        color: #333;
        font-weight: 600;
    }
}

.switch_btn ::v-deep .uni-switch-wrapper ::v-deep .uni-switch-input {
    width: 80rpx;
    height: 40rpx;
    margin-right: 30rpx;
}

.switch_btn ::v-deep .uni-switch-wrapper ::v-deep .uni-switch-input:after {
    width: 36rpx;
    height: 36rpx;
}

.switch_btn ::v-deep .uni-switch-wrapper ::v-deep .uni-switch-input:before {
    width: 36rpx;
    height: 36rpx;
}
</style>
