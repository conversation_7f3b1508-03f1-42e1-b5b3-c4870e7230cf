<template>
    <view class="page" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }">
        <ktabbar :bgImg="imgUrl + 'default_bg.jpg'" title="我的车辆" />
        <view v-if="vehicleData.length === 0" class="el-empty">
            <image class="el-vehicle-img" :src="imgUrl + 'default/empty.svg'" />
            <view class="el-vehicle-title">还没绑定任何车辆，添加一辆车</view>
        </view>
        <scroll-view v-else scroll-y class="vehicle-container">
            <view v-for="item in vehicleData" :key="item.deviceNo" class="el-card-item">
                <view class="el-card-container margin-top" @click="toVehicleDetailPage(item)">
                    <image class="card-image" :src="imgUrl + 'default/defaultVehicle.png'" mode="" />
                    <view class="card-info">
                        <text class="card-info-title">{{ item.identifier ? `车架号 ${item.identifier}` : '' }}</text>
                        <text class="card-info-title">{{ item.carModel ? `型号 ${item.carModel}` : '' }}</text>
                        <text v-if="item.vehicleSource === 1" class="card-info-shop">购车门店：{{ item.salesOutletsName }}</text>
                        <text class="card-info-shop">购车日期：{{ dayjs(item.gmtBind).format('YYYY-MM-DD') }}</text>
                    </view>
                </view>
            </view>
        </scroll-view>
        <view class="el-btn-view">
            <button class="cu-btn block el-btn" @click="toBandVehiclePage">添加车辆</button>
        </view>
    </view>
</template>

<script>
import ktabbar from '@/components/ktabbar.vue';
export default {
    components: {
        ktabbar
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            vehicleData: []
        };
    },
    onLoad() {
        const { pageName, pagePath, pageQuery } = this.$Route.query;
        console.log(pageName, pagePath, pageQuery, 'pageName,pagePath, pageQuery');
    },
    onShow() {
        this.getVehicleInfo();
    },
    methods: {
        // 车辆查询
        getVehicleInfo() {
            uni.showLoading({
                title: '加载中',
                mask: true
            });
            this.$request({
                url: 'v3/vehicle/front/vehicle/bindList',
                method: 'GET'
            })
                .then((res) => {
                    uni.hideLoading();
                    if (res.state === 200) {
                        this.vehicleData = res.data || [];
                    } else {
                        this.$dialog.toast(res.msg);
                    }
                })
                .catch((error) => {
                    uni.hideLoading();
                    console.error('获取车辆信息失败:', error);
                    this.$dialog.toast('获取车辆信息失败');
                });
        },
        // 跳转到车辆详情页面
        toVehicleDetailPage(item) {
            uni.setStorageSync('vehicleInfo', item);
            this.$Router.push({
                path: '/newPages/vehicle/vehicleDetail'
            });
        },
        // 跳转到绑定车辆页面
        toBandVehiclePage() {
            this.$Router.push({
                path: '/newPages/vehicle/bindVehicle'
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.page {
    width: 750rpx;
    min-height: 100vh;
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    // 背景图片固定，不滚动
    background-attachment: fixed;
}
.vehicle-container {
    padding-bottom: calc(130rpx + env(safe-area-inset-bottom));
}
.el-empty {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin: 24rpx;
    border-radius: 16rpx;
    height: 296rpx;
    background: #fff;
    .el-vehicle-img {
        width: 128rpx;
        height: 128rpx;
    }
    .el-vehicle-title {
        margin-top: 20rpx;
        color: rgba(0, 0, 0, 0.4);
    }
}
.el-card-item {
    margin-left: 24rpx;
    margin-right: 24rpx;
    margin-top: 24rpx;
    border-radius: 16rpx;
    background: #fff;
}
.el-card-container {
    display: flex;
    justify-content: baseline;
    margin: 0 32rpx 16rpx;
    padding-top: 32rpx;
    height: 240rpx;
    .card-image {
        border-radius: 8rpx;
        width: 176rpx;
        height: 176rpx;
        background: #f8f8f8;
    }
    .card-info {
        display: flex;
        flex-direction: column;
        margin-left: 24rpx;
        width: 436rpx;
        .card-info-title {
            /* stylelint-disable-next-line value-no-vendor-prefix */
            display: -webkit-box;
            overflow: hidden;
            width: 100%;
            line-height: 40rpx;
            -webkit-line-clamp: 2;
            text-overflow: ellipsis;
            font-weight: 500;
            font-size: 28rpx;
            color: rgba(0, 0, 0, 0.8);
            word-break: break-all;
            -webkit-box-orient: vertical;
        }
        .card-info-shop {
            margin-top: 24rpx;
            line-height: 24rpx;
            font-weight: 400;
            font-size: 24rpx;
            color: rgba(0, 0, 0, 0.4);
        }
    }
}
.el-btn-view {
    display: flex;
    position: fixed;
    bottom: 0;
    justify-content: center;
    width: 100%;
    height: calc(100rpx + env(safe-area-inset-bottom));
    .el-btn {
        margin-top: 10rpx;
        border-radius: 40rpx;
        width: 96%;
        height: 80rpx;
        background: rgba(73, 73, 73, 1);
        font-size: 30rpx;
        color: #fff;
    }
}
</style>
