<template>
	<view>
		<form @submit="handleVehicleSubmit">
			<view class="el-card vehicle-card-top" :class="isShowInsure ? ' vehicle-card' : ''">
				<view class="cu-form-group">
					<view class="title">购买门店<text class="request">*</text></view>
					<view class="picker" style="width: 100%" @click="choiceShop">
						<text v-if="!!lmVehicleInfo.salesOutletsName"
							class="">{{ lmVehicleInfo.salesOutletsName }}</text>
						<text v-else class="placeholder">请选择门店</text>
						<view class="arrow-icons" />
					</view>
				</view>
				<view class="cu-form-group">
					<view class="title">车架号<text class="request">*</text></view>
					<view class="avatar-wrapper">
						<input v-model="lmVehicleInfo.vehicleNo" type="vehicleNo" placeholder="请扫描或手动输入"
							placeholder-class="el-placeholder" name="vehicleNo" @blur="vehicleNoBlur" />
						<image class="avatar" src="/static/icons/icon-scan-black.svg"
							@click="() => scanCode('vehicleNo', 0)" />
					</view>
				</view>
				<view class="cu-form-group">
					<view class="title">车型</view>
					<input v-model="lmVehicleInfo.vehicleType" type="vehicleType" placeholder="车型"
						placeholder-class="el-placeholder" name="vehicleType" disabled />
				</view>
				<view class="cu-form-group">
					<view class="title">电池编号</view>
					<view class="avatar-wrapper">
						<input v-model="batteryOrderArr[batteryOrderArr.length - 1]" type="batteryNum" placeholder="请扫描"
							placeholder-class="el-placeholder" name="batteryNum" disabled />
						<image class="avatar" src="/static/icons/icon-scan-black.svg"
							@click="() => scanCode('batteryNum', 1)" />
					</view>
				</view>
				<view>
					<view v-for="item in batteryOrderArr" :key="item" class="battery-item">
						<view>{{ item }}</view>
						<view>
							<image class="delete-icon" src="/static/icons/icon-add-grey.svg"
								@click="() => deleteCode('batteryNum', item)" />
						</view>
					</view>
				</view>
				<view class="cu-form-group">
					<view class="title">电充编码</view>
					<view class="avatar-wrapper">
						<input v-model="chargerCodes[chargerCodes.length - 1]" type="chargerCodes" placeholder="请扫描"
							placeholder-class="el-placeholder" name="chargerCodes" disabled />
						<image class="avatar" src="/static/icons/icon-scan-black.svg"
							@click="() => scanCode('chargerCodes', 1)" />
					</view>
				</view>
				<view>
					<view v-for="item in chargerCodes" :key="item" class="battery-item">
						<view>{{ item }}</view>
						<view>
							<image class="delete-icon" src="/static/icons/icon-add-grey.svg"
								@click="() => deleteCode('chargerCodes', item)" />
						</view>
					</view>
				</view>
				<view class="cu-form-group">
					<view class="title">手机号<text class="request">*</text></view>
					<input v-model="lmVehicleInfo.userPhone" type="userPhone" placeholder="请填写手机号"
						placeholder-class="el-placeholder" name="userPhone" disabled />
				</view>
				<view class="cu-form-group">
					<view class="title">姓名</view>
					<input v-model="lmVehicleInfo.userName" type="userName" placeholder="请填写姓名"
						placeholder-class="el-placeholder" name="userName" />
				</view>

				<view class="cu-form-group">
					<view class="title">地区</view>
					<input v-model="lmVehicleInfo.storeAddress" type="storeAddress" placeholder="门店地址"
						placeholder-class="el-placeholder" name="storeAddress" disabled />
				</view>
				<view class="cu-form-group">
					<view class="title">购买日期<text class="request">*</text></view>
					<ElDateTimePicker class="picker" title="请选择购买日期" :end="dayjs().format('YYYY-MM-DD')"
						:value="lmVehicleInfo.purchaseTime" style="width: 100%" placeholder="请选择购买日期"
						@change="handleDateChange" />
				</view>
				<view class="cu-form-group border-line">
					<view class="title">车辆价格<text class="request"
							v-show="isInsure.TabCur == 1 && navData.TabCur == 31">*</text>
					</view>
					<input v-model="lmVehicleInfo.vehicleBuyPrice" type="vehicleBuyPrice" placeholder="请填写车辆价格"
						placeholder-class="el-placeholder" name="vehicleBuyPrice" maxlength="8" />
				</view>
				<!-- <view class="cu-form-group" @click="goCuttingScan" v-if="!isInsure.TabCur">
	            <view class="title">身份证号</view>
	            <view class="avatar-wrapper">
	              <input v-model="lmVehicleInfo.userIdCard" type="userIdCard" placeholder="请扫描身份证件正面" disabled
	                name="userIdCard" placeholder-class="el-placeholder" class="custom-input" maxlength="18"
	                @input="handleInput" />
	              <image class="avatar" src="/static/icons/icon-identity-black.svg"  />
	            </view>
	        </view> -->
				<view style="overflow: hidden; border-radius: 0 0 32rpx 32rpx">
					<ElImageUpload text-style="padding-left: 0;" img-style="padding-left: 0;"
						title-text="上传车辆图片 (合格证、唛头、车架钢印)" name="pictureOrderSaveVOList"
						title-style="font-size: 30rpx;color: rgba(0, 0, 0); font-weight:400;" @change="handleChangeImg"
						:is-required="isRequired" />
				</view>
				<view class="cu-form-group">
					<view class="title">身份证号<text class="request">*</text></view>
					<view class="avatar-wrapper">
						<input v-model="lmVehicleInfo.userIdCard" type="userIdCard" placeholder="请扫描身份证件正面或输入"
							name="userIdCard" placeholder-class="el-placeholder" class="custom-input" maxlength="18"
							@blur="handleInput" :disabled="manualInput" />
						<image class="avatar" src="/static/icons/icon-identity-black.svg" @click="goCuttingScan" />
					</view>
				</view>

				<view v-if="isInsure.TabCur">
					<!-- <view v-if="isShowArea" class="cu-form-group">
	            <view class="title">地区</view>
	            <view class="avatar-wrapper">
	              <PickerAddress :value="lmVehicleInfo.userArea" class="picker" title="请选择城市" style="width: 100%;"
	                :is-show-area='true' @change="handleCityChange" />
	            </view>
	          </view> -->
					<view class="cu-form-group">
						<view class="title">电子签名<text class="request">*</text></view>
					</view>
					<view class="cu-form-group signature-status">
						<checkbox-group @change="handleCheckChange">
							<checkbox class="radio red round signature-check" :checked="check" />
						</checkbox-group>
						<view>本人已阅读并知晓
							<text class="notice-letter" @click="previewImage()">《保障告知书》</text>
							内容，同意并遵循上述规则：若违反规则，自愿承担一切后果。签字确认：
						</view>
					</view>
					<view class="cu-form-group">
						<view class="signImg" v-if="signImg !== ''">
							<image :src="signImg" mode="aspectFit" />
							<view class="btn-box">
								<view class="signImg-btn" @click="signRewrite">重写</view>
							</view>
						</view>
						<view class="sign-box" v-else>
							<canvans-sign canvasId="canvas" @save="save" @clear="signRewrite" ref="sign" />
						</view>
					</view>
				</view>
			</view>

			<view class="el-submit-btn-view">
				<button class="cu-btn block el-submit-btn" form-type="submit" :disabled="activeDisabled">
					<!-- {{  isInsure.TabCur ?  '确认激活绑定与保险' :'确认激活并绑定' }} -->
					确认激活并绑定
				</button>
			</view>
		</form>
	</view>
</template>

<script>
	import {
		mapState,
		mapMutations
	} from 'vuex';
	export default {
		data() {
			return {
			isShowInsure:false,
			}
		},
		methods:{
			handleVehicleSubmit(){
				
			}
		}
	}
</script>
<style lang="scss" scoped>
	.lima-bg-red {
		background-color: #c70e2d !important;
		color: #fff;
	}

	.request {
		color: #ff474a;
	}

	.arrow-icons {
		float: right;
		margin-left: 20rpx;
		margin-right: 8rpx;
		width: 24rpx;
		height: 24rpx;
		box-shadow: -4rpx 4rpx 0 0rpx rgba(0, 0, 0, 0.25) inset;
		transform: rotate(45deg);
	}

	.placeholder {
		font-weight: 400;
		font-size: 28rpx;
		color: rgba(0, 0, 0, 0.25);
	}

	.brand-nav {
		overflow: hidden;
		margin-top: 24rpx;
		padding: 0 32rpx;
		border-bottom-left-radius: 0;
		border-bottom-right-radius: 0;
		background: #fff;
	}

	.vehicle-card-top {
		margin-top: 24rpx;
		padding: 0 32rpx;
		padding-bottom: 200rpx;
		padding-bottom: 300rpx;
		background: #fff;
	}

	.vehicle-card {
		border-top-left-radius: 0;
		border-top-right-radius: 0;
		margin-top: 0rpx;
	}

	.brand-container {
		justify-content: flex-start;

		.brand-btn {
			display: flex;
			justify-content: flex-start;

			.item {
				margin-right: 50rpx;
				padding: 10rpx 30rpx;
				border-radius: 40rpx;
				background-color: #f5f5f5;
				text-align: center;
				font-size: 28rpx;
				color: #000;
			}

			.item.active {
				background-color: #c70e2d;
				color: #fff;
			}
		}
	}

	.cu-form-group {
		padding: 0;
	}

	.cu-form-group .title {
		min-width: calc(4em + 50rpx);
	}

	.cu-form-group .picker {
		text-align: left;
	}

	.border-radius-top {
		border-radius: 16rpx 16rpx 0 0;
	}

	.border-radius-bottom {
		border-radius: 0 0 16rpx 16rpx;
	}

	.avatar-wrapper {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 100%;

		.avatar-text {
			font-weight: 400;
			font-size: 28rpx;
			color: rgba(0, 0, 0, 0.25);
		}

		.avatar {
			width: 32rpx;
			height: 32rpx;
			background: #f8f8f8;
		}
	}

	.el-submit-btn-view {
		display: flex;
		position: fixed;
		bottom: 0;
		z-index: 999;
		justify-content: center;
		width: 100%;
		height: calc(100rpx + env(safe-area-inset-bottom));
		background: #fff;
		box-shadow: 0 -2px 10px 0 rgba(0, 21, 61, 0.08);

		.el-submit-btn {
			margin-top: 10rpx;
			border-radius: 40rpx;
			width: 686rpx;
			height: 80rpx;
			background: #c70e2d;
			font-size: 30rpx;
			color: #fff;
		}
	}

	.border-line {
		border-bottom: 1rpx solid #eee;
	}

	.signImg {
		width: 100%;

		image {
			width: 100%;
			height: 350rpx;
		}

		.btn-box {
			display: flex;
			flex-direction: row-reverse;
			margin-top: 20rpx;

			.signImg-btn {
				padding: 0 20rpx;
				border-radius: 20rpx;
				height: 60rpx;
				background: #c70e2d;
				line-height: 60rpx;
				text-align: center;
				color: #fff;
			}
		}
	}

	.signature-status {
		padding: 20rpx 0;
		border-top: none;

		.signature-check {
			margin-right: 20rpx;
		}

		.notice-letter {
			color: #c70e2d;
		}
	}

	.battery-item {
		font-size: 26rpx;
		color: #c70e2d;
		margin: 14rpx 0;
		display: flex;
		justify-content: space-between;

		image {
			width: 32rpx;
			height: 32rpx;
			transform: rotate(45deg);
		}
	}
</style>