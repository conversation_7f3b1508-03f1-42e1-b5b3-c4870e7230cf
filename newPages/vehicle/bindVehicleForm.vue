<template>
    <view class="vehicle-info" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }">
        <ktabbar :bgImg="imgUrl + 'default_bg.jpg'" title="我的车辆" />
        <view class="form-main">
            <form @submit="handleSubmit">
                <view class="info-group" v-for="(gt, index) in colunms" :key="index">
                    <view class="info-title">{{ gt.title }}</view>
                    <view class="info-warp">
                        <view class="cu-form-group" v-for="item in gt.colunms" :key="item.name">
                            <view class="title">
                                {{ item.label }}
                                <text v-if="item.required" class="request">*</text>
                            </view>
                            <view class="form-wrapper" @click="handleOpenPicker(item)">
                                <input
                                    v-if="item.type == 'input'"
                                    v-model="formData[item.name]"
                                    class="form_input"
                                    :placeholder="item.placeholder"
                                    placeholder-class="el-placeholder"
                                    :name="item.name"
                                />
                                <view v-if="item.type == 'select'" class="form_input">
                                    <text v-if="!!formData[item.name]" class="">{{ formData[item.name + 'Name'] }}</text>
                                    <text v-else class="el-placeholder form_input">{{ item.placeholder }}</text>
                                </view>
                                <uv-icon v-if="item.scan" class="suffix-icon" name="scan" size="24" @click="scanCode(item.name)"></uv-icon>
                                <view class="arrow-icon suffix-icon" v-if="item.type == 'select'"></view>
                            </view>
                        </view>
                    </view>
                </view>
                <view class="el-submit-btn-view">
                    <button class="cu-btn block el-submit-btn" form-type="submit">确认并绑定</button>
                </view>
            </form>
        </view>
        <!-- 通用picker -->
        <uv-picker ref="picker" :columns="pickerColunms" :keyName="cur_picker.sel_key || 'name'" @confirm="pickerConfirm" confirmColor="rgba(199, 14, 45, 1)"></uv-picker>
    </view>
</template>
<script>
import ktabbar from '@/components/ktabbar.vue';
export default {
    components: {
        ktabbar
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            colunms: [
                {
                    title: '基本信息',
                    colunms: [
                        {
                            type: 'input',
                            scan: true,
                            name: 'identity',
                            label: '车辆识别码',
                            placeholder: '请扫描或输入',
                            required: true
                        },
                        {
                            type: 'input',
                            scan: true,
                            name: 'deviceNo',
                            label: '车架号',
                            placeholder: '车型',
                            required: true
                        },
                        {
                            type: 'select',
                            name: 'brandId',
                            label: '品牌',
                            placeholder: '请选择',
                            required: true,
                            initialValue: 0,
                            sel_key: 'brandName',
                            // 这里可以根据实际情况调整数据源
                            data: []
                        }
                    ]
                },
                {
                    title: '电池信息',
                    colunms: [
                        {
                            type: 'select',
                            name: 'batteryType',
                            label: '电池类型',
                            placeholder: '请选择',
                            required: false,
                            sel_key: 'name',
                            data: []
                        },
                        {
                            type: 'select',
                            name: 'batteryVoltage',
                            label: '电池电压',
                            placeholder: '车型',
                            required: false,
                            sel_key: 'name',
                            data: []
                        },
                        {
                            type: 'select',
                            name: 'batteryCapacity',
                            label: '电池容量',
                            placeholder: '请选择',
                            required: false,
                            data: []
                        }
                    ]
                }
            ],
            cur_picker: {
                name: '',
                value: '',
                sel_key: ''
            },
            pickerColunms: [],
            formData: {
                deviceNo: '',
                identity: '',
                brandId: 1,
                brandIdName: '立马',
                batteryType: '',
                batteryCapacity: '',
                batteryVoltage: ''
            }
        };
    },
    onLoad() {
        const { vin } = this.$Route.query;
        if (vin) {
            this.formData.identity = vin;
            this.formData.deviceNo = vin;
        }
        this.getBrandInfo();
        this.getBatteryType();
    },
    mounted() {},
    methods: {
        // 选择器改变
        pickerConfirm(val) {
            if (this.cur_picker.name) {
                this.formData[this.cur_picker.name] = val.value[0].id;
                this.formData[this.cur_picker.name + 'Name'] = val.value[0][this.cur_picker.sel_key || 'name'];
            }
        },
        // 打开选择器
        handleOpenPicker(item) {
            if (item.type !== 'select') return;
            this.cur_picker = item;
            this.pickerColunms = [item.data];
            this.$refs.picker.open();
        },
        // 选择门店
        choiceShop() {
            this.$Router.push({
                path: '/pages/store/storeList'
            });
        },
        // 扫码
        scanCode(type) {
            // #ifdef H5
            window.jsBridgeHelper?.sendMessage('qrCode').then((res) => {
                // code: string; msg: string; data: string
                if (res.code === '200') {
                    // 正常二维码格式L1ZL1ZEH8P0503159、062310230361
                    // 旧版本格式 http://www.lima-info.com/wx/062310230361.aspx
                    const value = res.data.replace(/^.*\/([A-Za-z0-9]+)\.aspx$/, '$1').trim();
                    this.vin = value;
                }
            });
            // #endif
            // #ifndef H5
            uni.scanCode({
                onlyFromCamera: false,
                scanType: ['qrCode'],
                success: (res) => {
                    // res.result 可能是二维码内容
                    const value = res.result.replace(/^.*\/([A-Za-z0-9]+)\.aspx$/, '$1').trim();
                    this.vin = value;
                },
                fail: (err) => {
                    console.error('扫码失败:', err);
                }
            });
            // #endif
        },
        // 确认并绑定 接口
        handleSubmit(e) {
            const params = { ...this.formData };
            if (!params.identity) {
                this.$api.msg('请输入车辆识别码');
            }
            if (!params.deviceNo) {
                this.$api.msg('请输入车架号');
            }
            if (!params.brandId) {
                this.$api.msg('请选择车辆品牌');
            }
            this.bindVehicleSave(params);
        },
        // 绑定车辆请求
        bindVehicleSave(params) {
            uni.showLoading({
                title: '正在绑定车辆',
                mask: true
            });
            this.$request({
                url: '/v3/vehicle/front/vehicle/bind',
                method: 'post',
                data: params
            })
                .then((res) => {
                    uni.hideLoading();
                    if (res.state === 200) {
                        this.$api.msg('车辆绑定成功');
                        this.$Router.back();
                    } else {
                        this.$api.msg(res.msg);
                    }
                })
                .catch((err) => {
                    uni.hideLoading();
                    this.$api.msg(err.msg);
                    console.error('绑定车辆失败:', err);
                });
        },
        /*
         * 获取品牌
         */
        getBrandInfo() {
            this.$request({
                url: '/v3/vehicle/front/vehicle/allBrand',
                method: 'get'
            }).then((res) => {
                if (res.state === 200) {
                    this.colunms[0].colunms[2].data = res.data;
                } else {
                    this.$api.msg(res.msg);
                }
            });
        },
        /*
         * 获取电池信息
         */
        getBatteryType() {
            this.$request({
                url: '/v3/vehicle/front/vehicle/enumBattery',
                method: 'get'
            }).then((res) => {
                if (res.state === 200) {
                    const { enumBatteryCapacity, enumBatteryType, enumBatteryVtg } = res.data;
                    this.colunms[1].colunms[0].data = this.enmuToArray(enumBatteryType);
                    this.colunms[1].colunms[1].data = this.enmuToArray(enumBatteryVtg);
                    this.colunms[1].colunms[2].data = this.enmuToArray(enumBatteryCapacity);
                } else {
                    this.$api.msg(res.msg);
                }
            });
        },
        // 将枚举转换为数组
        enmuToArray(enumData) {
            return Object.keys(enumData).map((key) => {
                return {
                    id: key,
                    name: enumData[key]
                };
            });
        }
    }
};
</script>

<style lang="scss">
.vehicle-info {
    width: 750rpx;
    min-height: 100vh;
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    // 背景图片固定，不滚动
    background-attachment: fixed;

    .form-main {
        width: 100%;
        padding: 40rpx 0;
        box-sizing: border-box;

        .request {
            color: #c70e2d;
        }

        .info-group {
            width: 92%;
            margin: 0 auto;

            .info-title {
                font-size: $fs-base;
                color: #908f94;
                margin-bottom: 20rpx;
            }

            .info-warp {
                background: #fff;
                border-radius: 40rpx;
                padding: 30rpx;
                box-sizing: border-box;
                margin-bottom: 30rpx;

                .cu-form-group {
                    margin-bottom: 20rpx;
                }

                .form-wrapper {
                    display: flex;
                    align-items: center;

                    .input-wrapper {
                        flex: 1;
                    }
                }
            }
        }
    }

    .cu-form-group {
        background-color: #ffffff;
        display: flex;
        justify-content: space-between;
        align-items: center;
        min-height: 100rpx;

        &:not(:last-child) {
            border-bottom: 1rpx solid #eee;
        }

        .form_input {
            font-size: $fs-base;
        }

        .title {
            width: 200rpx;
            font-size: $fs-base;
        }

        .form-wrapper {
            flex: 1 1 calc(100% - 200rpx);
            display: flex;
            align-items: center;
            justify-content: space-between;

            .form_input {
                flex: 1 1 calc(100% - 50rpx);
                text-align: right;
            }

            .suffix-icon {
                width: 50rpx;
            }

            .arrow-icon {
                width: 24rpx;
                height: 24rpx;
                -webkit-transform: rotate(45deg);
                transform: rotate(45deg);
                margin-left: 10rpx;
                margin-right: 20rpx;
                box-shadow: -2px 2px 0 0px #c7c7c7 inset;
            }
        }

        .picker {
            text-align: left;
        }

        .placeholder {
            font-weight: 400;
            font-size: $fs-base;
            color: rgba(0, 0, 0, 0.25);
        }

        .el-placeholder {
            color: #9d9d9d;
        }
    }
}

.el-submit-btn-view {
    display: flex;
    position: fixed;
    bottom: 0;
    justify-content: center;
    width: 100%;
    height: calc(100rpx + env(safe-area-inset-bottom));
    z-index: 5;

    .el-submit-btn {
        margin-top: 10rpx;
        border-radius: 40rpx;
        width: 94%;
        height: 80rpx;
        background: rgba(73, 73, 73, 1);
        font-size: $fs-base;
        color: #fff;
    }
}
</style>
