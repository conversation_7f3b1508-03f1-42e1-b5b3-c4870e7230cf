<template>
    <view class="bind-vehicle" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }">
        <ktabbar :bgImg="imgUrl + 'default_bg.jpg'" title="我的车辆" />
        <view class="main">
            <image :src="imgUrl + 'vehicle/bind_vehicle_logo.png'" mode="scaleToFill" class="logo" />
            <view class="title-wrap">
                <text class="title">扫码来绑定车辆</text>
                <uv-icon name="question-circle" color="#E63C3C" size="20" @click="showTipsPop" />
            </view>
            <view class="input-wrap">
                <view class="input_box">
                    <input class="input" v-model="vin" placeholder="请输入车架号/车辆识别码" placeholder-style="color:#C0C4CC" />
                    <uv-icon class="scan-icon" name="scan" color="#565656" size="28" @click="handleScan" />
                </view>
            </view>
            <!-- <view class="scan-tip">请扫描整车二维码来绑定车辆～</view> -->
            <view class="confirm-btn">
                <view class="btn" :class="{ canuse: vin && vin.length > 0 }" @click="confirm">绑定车辆</view>
            </view>
        </view>
        <unipopup ref="popupRef" type="bottom" :mask-click="true">
            <view class="pop-main">
                <view class="q">1.车主如何绑定？</view>
                <view class="a">答：当车辆没有账户绑定时，用户可通过 : a.输入车辆识别码;b.扫描整车二维码来绑定车辆，绑定成功后，即可成为车主。</view>
                <view class="q">2.如何邀请家人朋友共享车辆？</view>
                <view class="a">
                    答：车主账号可以通过添加手机号邀请家人朋友使用爱车（“车况”—“共享”—“共享账号管理”—“添加账号”），受邀请的账号登录后可看到邀请信息，接受邀请后则邀请成功。
                </view>
                <view class="q">3.一个账户最多绑定几辆车？</view>
                <view class="a">答：最多绑定8辆，当用户已绑定8辆车（包括车主）为拥有车辆已达上限，请先解绑部分车辆再绑定。</view>
                <view class="q">4.一辆车最多能被几个账户绑定？</view>
                <view class="a">答：一辆车最多是能被6位账户绑定（包括车主）。</view>
            </view>
        </unipopup>
    </view>
</template>

<script>
import ktabbar from '@/components/ktabbar.vue';
import { mapState } from 'vuex';
import unipopup from '@/components/uni-popup/uni-popup.vue';
export default {
    components: {
        unipopup,
        ktabbar
    },
    data() {
        return {
            vin: '',
            imgUrl: getApp().globalData.imgUrl
        };
    },
    computed: {
        ...mapState(['hasLogin', 'userInfo'])
    },
    methods: {
        // 确认绑定
        confirm() {
            if (!this.vin) return;
            this.$Router.push({
                path: '/newPages/vehicle/bindVehicleForm',
                query: {
                    vin: this.vin
                }
            });
        },
        // 扫一扫
        handleScan() {
            // #ifdef H5
            window.jsBridgeHelper?.sendMessage('qrCode').then((res) => {
                // code: string; msg: string; data: string
                if (res.code === '200') {
                    // 正常二维码格式L1ZL1ZEH8P0503159、062310230361
                    // 旧版本格式 http://www.lima-info.com/wx/062310230361.aspx
                    const value = res.data.replace(/^.*\/([A-Za-z0-9]+)\.aspx$/, '$1').trim();
                    this.vin = value;
                }
            });
            // #endif
            // #ifndef H5
            uni.scanCode({
                onlyFromCamera: false,
                scanType: ['qrCode'],
                success: (res) => {
                    // res.result 可能是二维码内容
                    const value = res.result.replace(/^.*\/([A-Za-z0-9]+)\.aspx$/, '$1').trim();
                    this.vin = value;
                },
                fail: (err) => {
                    console.error('扫码失败:', err);
                }
            });
            // #endif
        },
        // 显示提示弹窗
        showTipsPop() {
            this.$refs.popupRef.open();
        }
    }
};
</script>

<style lang="scss">
.bind-vehicle {
    width: 750rpx;
    min-height: 100vh;
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    // 背景图片固定，不滚动
    background-attachment: fixed;

    .main {
        display: flex;
        flex-direction: column;
        align-items: center;
        .logo {
            width: 360rpx;
            height: 320rpx;
            margin: 0 auto;
            margin-top: 100rpx;
        }
        .title-wrap {
            display: flex;
            align-items: center;
            margin-top: 40rpx;
            margin-bottom: 30rpx;

            .title {
                font-size: 30rpx;
                color: #303133;
                margin-right: 10rpx;
            }
        }

        .input-wrap {
            width: 94%;
            background-color: #ffffff;
            border-radius: 40rpx;
            padding: 50rpx 20rpx;
            margin-bottom: 20rpx;
            .input_box {
                width: 100%;
                display: flex;
                align-items: center;
                justify-content: space-between;
                position: relative;
                padding-bottom: 15rpx;
                border-bottom: 1px solid #dcdfe6;
                .input {
                    height: 30rpx;
                    flex: 1 1 calc(100% - 60rpx);
                    font-size: 28rpx;
                    color: #303133;
                    border: none;
                    outline: none;
                }

                .scan-icon {
                    // position: relative;
                    margin-right: 10rpx;
                }
            }
        }

        .scan-tip {
            font-size: 24rpx;
            color: #909399;
            margin-bottom: 80rpx;
        }

        .confirm-btn {
            padding: 0 10rpx;
            width: 94%;
            .btn {
                width: 100%;
                color: #fff;
                font-size: $fs-base;
                height: 80rpx;
                border-radius: 40rpx;
                line-height: 80rpx;
                text-align: center;
                background: rgba(73, 73, 73, 1);
            }
        }
    }
}
.pop-main {
    overflow-y: scroll;
    padding: 30px;
    background: #fff;
    border-radius: 20rpx 20rpx 0 0;
    padding: 20rpx 4% 30rpx;
    .q {
        font-size: 34rpx;
        font-weight: bold;
        line-height: 1.4;
        margin-bottom: 15rpx 0;
    }
    .a {
        font-size: 28rpx;
        line-height: 1.4;
        color: #606266;
        margin-bottom: 30rpx;
    }
}
</style>
