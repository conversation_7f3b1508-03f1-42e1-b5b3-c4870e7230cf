<template>
    <view class="bind-vehicle">
        <form @submit="handleVehicleSubmit">
            <view class="el-card" :class="isShowInsure ? ' vehicle-card' : ''">
                <view class="cu-form-group border-line">
                    <view class="title">
                        购买门店
                        <text class="request">*</text>
                    </view>
                    <view class="picker" style="width: 100%" @click="choiceShop">
                        <text v-if="!!lmVehicleInfo.salesOutletsName" class="picker-value">{{ lmVehicleInfo.salesOutletsName }}</text>
                        <text v-else class="el-placeholder">请选择门店</text>
                        <view class="arrow-icon" />
                    </view>
                </view>
                <view class="cu-form-group border-line">
                    <view class="title">
                        车架号
                        <text class="request">*</text>
                    </view>
                    <view class="avatar-wrapper">
                        <input
                            v-model="lmVehicleInfo.vehicleNo"
                            class="form_input"
                            placeholder="请扫描或手动输入"
                            placeholder-class="el-placeholder"
                            name="vehicleNo"
                            @blur="vehicleNoBlur"
                        />
                        <uv-icon class="avatar" name="scan" size="24" @click="scanCode('vehicleNo', 0)"></uv-icon>
                    </view>
                </view>
                <view class="cu-form-group border-line">
                    <view class="title">车型</view>
                    <input v-model="lmVehicleInfo.vehicleType" class="form_input" placeholder="车型" placeholder-class="el-placeholder" name="vehicleType" disabled />
                </view>
                <view class="cu-form-group border-line">
                    <view class="title">电池编号</view>
                    <view class="avatar-wrapper">
                        <input
                            v-model="batteryOrderArr[batteryOrderArr.length - 1]"
                            class="form_input"
                            placeholder="请扫描"
                            placeholder-class="el-placeholder"
                            name="batteryNum"
                            disabled
                        />
                        <uv-icon class="avatar" name="scan" size="24" @click="scanCode('batteryNum', 1)"></uv-icon>
                    </view>
                </view>
                <view>
                    <view v-for="item in batteryOrderArr" :key="item" class="battery-item">
                        <view>{{ item }}</view>
                        <view>
                            <image class="delete-icon" src="/static/icons/icon-add-grey.svg" @click="() => deleteCode('batteryNum', item)" />
                        </view>
                    </view>
                </view>
                <view class="cu-form-group border-line">
                    <view class="title">电充编码</view>
                    <view class="avatar-wrapper">
                        <input
                            v-model="chargerCodes[chargerCodes.length - 1]"
                            class="form_input"
                            placeholder="请扫描"
                            placeholder-class="el-placeholder"
                            name="chargerCodes"
                            disabled
                        />
                        <uv-icon class="avatar" name="scan" size="24" @click="scanCode('chargerCodes', 1)"></uv-icon>
                    </view>
                </view>
                <view>
                    <view v-for="item in chargerCodes" :key="item" class="battery-item">
                        <view>{{ item }}</view>
                        <view>
                            <image class="delete-icon" src="/static/icons/icon-add-grey.svg" @click="() => deleteCode('chargerCodes', item)" />
                        </view>
                    </view>
                </view>
                <!-- <view class="cu-form-group border-line">
                    <view class="title">
                        手机号
                        <text class="request">*</text>
                    </view>
                    <input v-model="lmVehicleInfo.userPhone" class="form_input" placeholder="请填写手机号" placeholder-class="el-placeholder" name="userPhone" disabled />
                </view> -->
                <view class="cu-form-group">
                    <view class="title">姓名</view>
                    <input v-model="lmVehicleInfo.userName" class="form_input" placeholder="请填写姓名" placeholder-class="el-placeholder" name="userName" />
                </view>

                <view class="cu-form-group border-line">
                    <view class="title">地区</view>
                    <input v-model="lmVehicleInfo.storeAddress" class="form_input" placeholder="门店地址" placeholder-class="el-placeholder" name="storeAddress" disabled />
                </view>
                <view class="cu-form-group">
                    <view class="title">
                        购买日期
                        <text class="request">*</text>
                    </view>
                    <dateTimePicker
                        class="picker"
                        title="请选择购买日期"
                        :end="dayjs().format('YYYY-MM-DD')"
                        :value="lmVehicleInfo.purchaseTime"
                        style="width: 100%"
                        placeholder="请选择购买日期"
                        @change="handleDateChange"
                    />
                </view>
                <view class="cu-form-group border-line">
                    <view class="title">
                        车辆价格
                        <text class="request" v-show="isInsure.TabCur == 1 && navData.TabCur == 31">*</text>
                    </view>
                    <input
                        v-model="lmVehicleInfo.vehicleBuyPrice"
                        class="form_input"
                        placeholder="请填写车辆价格"
                        placeholder-class="el-placeholder"
                        name="vehicleBuyPrice"
                        maxlength="8"
                    />
                </view>
                <view class="cu-form-group border-line">
                    <imageUpload
                        img-max-count="4"
                        style="width: 100%"
                        text-style="padding-left: 0;"
                        img-style="padding-left: 0;"
                        title-text="上传车辆图片 (合格证、唛头、车架钢印)"
                        name="pictureOrderSaveVOList"
                        title-style="font-size: 30rpx;color: rgba(0, 0, 0); font-weight:400;"
                        @change="handleChangeImg"
                        :is-required="isRequired"
                    />
                </view>
                <view class="cu-form-group">
                    <view class="title">
                        身份证号
                        <text class="request">*</text>
                    </view>
                    <view class="avatar-wrapper">
                        <input
                            v-model="lmVehicleInfo.userIdCard"
                            class="form_input custom-input"
                            placeholder="请扫描身份证件正面或输入"
                            name="userIdCard"
                            placeholder-class="el-placeholder"
                            maxlength="18"
                            @blur="handleInput"
                            :disabled="manualInput"
                        />
                        <!-- <uv-icon class="avatar" name="scan" size="24" @click="goCuttingScan"></uv-icon> -->
                    </view>
                </view>

                <view v-if="isInsure.TabCur">
                    <view class="cu-form-group">
                        <view class="title">
                            电子签名
                            <text class="request">*</text>
                        </view>
                    </view>
                    <view class="cu-form-group signature-status">
                        <checkbox-group @change="handleCheckChange">
                            <checkbox class="radio red round signature-check" :checked="check" />
                        </checkbox-group>
                        <view>
                            本人已阅读并知晓
                            <text class="notice-letter" @click="previewImage()">《保障告知书》</text>
                            内容，同意并遵循上述规则：若违反规则，自愿承担一切后果。签字确认：
                        </view>
                    </view>
                    <view class="cu-form-group">
                        <view class="signImg" v-if="signImg !== ''">
                            <image :src="signImg" mode="aspectFit" />
                            <view class="btn-box">
                                <view class="signImg-btn" @click="signRewrite">重写</view>
                            </view>
                        </view>
                        <view class="sign-box" v-else>
                            <canvans-sign canvasId="canvas" @save="save" @clear="signRewrite" ref="sign" />
                        </view>
                    </view>
                </view>
            </view>
            <view class="el-submit-btn-view">
                <button class="cu-btn block el-submit-btn" form-type="submit" :disabled="activeDisabled">确认激活并绑定</button>
            </view>
        </form>
    </view>
</template>
<script>
import CanvansSign from './canvans-sign.vue';
import dateTimePicker from '@/components/dateTimePicker.vue';
import imageUpload from '@/components/imageUpload.vue';
// import { base64ToPath, pathToBase64 } from 'image-tools'
import { mapState } from 'vuex';
export default {
    components: {
        CanvansSign,
        dateTimePicker,
        imageUpload
    },
    data() {
        return {
            currentTimestamp: Date.now(),
            storeInfo: {}, // 门店信息
            isShowInsure: false,
            navData: {
                TabCur: 21,
                TabCurName: '车损险',
                oderNav: [
                    {
                        id: 21,
                        name: '车损险',
                        tagItemId: '8914f79d4e554946877a39214174659c'
                    },
                    {
                        id: 31,
                        name: '盗抢险',
                        tagItemId: '7cdec7528fce45259d15df073ebda148'
                    }
                ],
                TagItemId: '8914f79d4e554946877a39214174659c', // 车损无忧、盗抢险
                ActiveTagId: '7a2582f4e1774ac6a7bb41ff9f108a1a' // 激活三包
            },
            check: false,
            signImg: '',
            base64Prefix: 'data:image/png;base64,',
            lmVehicleInfo: {
                purchaseTime: this.dayjs().format('YYYY-MM-DD'),

                // 销售门店信息
                custCode: '',
                salesOutletsCode: '',
                salesOutletsName: '',
                salesOutletsId: '',

                // 销售门店位置信息
                provinceName: '',
                province: '',
                cityName: '',
                city: '',
                countyName: '',
                county: '',
                storeAddress: '',
                saleRegion: '',
                saleRegionName: '',

                //  车辆信息
                vehicleNo: '',
                vehicleSpecs: '',
                vehicleColor: '',
                vehicleName: '',
                vehicleType: '',
                vehicleBuyPrice: '',

                // 用户信息
                userPhone: '',
                userName: '',
                userIdCard: '',
                userIdCardBase64Img: '',
                userSignBase64Img: '',
                userSex: null,
                userBirthday: '',
                userArea: '',

                // 车辆品牌
                brandName: '',
                orderNo: '', //激活保险的订单号
                manufactureDate: '', // 生产日期

                valueName: '' //
            },
            vehicleImgList: [], // 车辆图片
            batteryOrderArr: [],
            chargerCodes: [],
            soreParams: {
                storeStatus: 'ACTIVE',
                size: 1,
                current: 1,
                storeCodeOrNameLike: '',
                city: '',
                province: '',
                county: '',
                lat: '',
                lon: '',
                addressType: 'DEFAULT',
                storeName: ''
            },
            isRequired: false,
            activeDisabled: false,
            manualInput: false,
            vin: '',
            isInsure: {
                TabCur: 0,
                TabCurName: '否',
                oderNav: [
                    {
                        id: 0,
                        name: '否'
                    },
                    {
                        id: 1,
                        name: '是'
                    }
                ]
            }
        };
    },
    computed: {
        ...mapState(['userCenterData', 'userInfo', 'hasLogin'])
    },
    onLoad(option) {
        console.log('激活车辆页面加载', this.userCenterData);

        return;
        if (option.storeCode) {
            this.soreParams.storeCodeOrNameLike = JSON.parse(decodeURIComponent(option.storeCode));
            if (this.soreParams.storeCodeOrNameLike) {
                this.getStoreList(this.soreParams);
            }
        }
        // 从聚合页携带车架号进入
        if (option.vin) {
            this.vin = JSON.parse(decodeURIComponent(option.vin));
            if (this.vin) {
                this.queryVehicleInfoByVehicleNo(this.vin);
            }
        }

        const accountInfo = uni.getAccountInfoSync();
        const systemInfo = uni.getSystemInfoSync();
        if (accountInfo.miniProgram.envVersion == 'trial' || accountInfo.miniProgram.envVersion == 'develop') {
            this.actParams.appVersion = '测试|开发版本';
        } else {
            this.actParams.appVersion = accountInfo.miniProgram.version;
        }
        this.actParams.deviceMode = systemInfo.model;
    },
    methods: {
        tabSelect(id, name, tagId) {
            this.navData.TabCur = id;
            this.navData.TabCurName = name;
            this.navData.TagItemId = tagId;
        },

        isInsureSelect(id, name) {
            this.isInsure.TabCur = id;
            this.isInsure.TabCurName = name;
            if (id == 1) {
                this.manualInput = true;
            } else {
                this.manualInput = false;
            }
        },
        // 车架号输入失去焦点
        vehicleNoBlur() {
            // 手动输入车架号需要上传车辆图片
            this.isRequired = true;
            const vehicleNoLength = this.lmVehicleInfo.vehicleNo.length;

            // if (vehicleNoLength >= 12) {
            //     this.queryVehicleInfoByVehicleNo(this.lmVehicleInfo.vehicleNo);
            // }
        },

        // 跳转到选择门店
        choiceShop() {
            this.$Router.push({
                path: '/pages/service/store/storeList',
                query: { type: 'activation', serviceType: 'all' }
            });
        },
        // 选择门店的回调方法
        updateStore(store) {
            this.storeInfo = store;
            this.lmVehicleInfo = {
                ...this.lmVehicleInfo,
                salesOutletsCode: store.storeCode,
                salesOutletsName: store.storeName,
                salesOutletsId: store.id,
                custCode: store.custCode,
                provinceName: store.province,
                province: store.provinceCode,
                cityName: store.city,
                city: store.cityCode,
                countyName: store.area,
                county: store.areaCode,
                storeAddress: store.address,
                saleRegion: '',
                saleRegionName: ''
            };
            console.log('选择的门店信息:', store);
        },

        // 扫码
        scanCode(type, index) {
            const that = this;
            uni.scanCode({
                success: function (res) {
                    if (type === 'vehicleNo') {
                        that.isRequired = false;
                        that.lmVehicleInfo.vehicleType = '';
                        that.lmVehicleInfo.vehicleNo = '';

                        if (res.result.includes('http')) {
                            let vehicleNo = res.result.slice(28).split('.')[0];
                            that.queryVehicleInfoByVehicleNo(vehicleNo);
                        } else {
                            that.queryVehicleInfoByVehicleNo(res.result);
                        }
                    }

                    if (type === 'batteryNum') {
                        if (that.batteryOrderArr.includes(res.result)) {
                            that.$dialog.toast('电池编号重复');
                        } else {
                            that.batteryOrderArr.push(res.result);
                        }
                    }
                    if (type === 'chargerCodes') {
                        if (that.chargerCodes.includes(res.result)) {
                            that.$dialog.toast('电充编号重复');
                        } else {
                            that.chargerCodes[0] = res.result;
                        }
                    }
                }
            });
        },

        deleteCode(type, item) {
            if (type === 'batteryNum') {
                const batteryOrderNewArr = this.batteryOrderArr.filter((i) => {
                    return i != item;
                });
                this.batteryOrderArr = batteryOrderNewArr;
            }
            if (type === 'chargerCodes') {
                const chargerCodesNewArr = this.chargerCodes.filter((i) => {
                    return i != item;
                });
                this.chargerCodes = chargerCodesNewArr;
            }
        },

        // 提交判断
        async validateForm(params) {
            const checkPhone = new RegExp(/^[1]([3-9])[0-9]{9}$/);
            const PriceType = isNaN(Number(params.vehicleBuyPrice));
            if (!params.salesOutletsName) {
                this.$api.msg('请选择购买门店');
                return false;
            }
            if (!params.storeAddress) {
                this.$api.msg('地区不能为空请选择购买门店');
                return false;
            }
            if (!params.vehicleNo) {
                this.$api.msg('请输入车架号');
                return false;
            }
            // if (!params.vehicleType) {
            // 	this.$api.msg('请输入正确车架号');
            //     return false;
            // }
            // 手机号不需要填写，默认使用会员手机号
            // if (!params.userPhone || params.userPhone.length === 0) {
            //      this.$api.msg('请输入手机号');
            //     return false;
            // }
            // if (!checkPhone.test(params.userPhone)) {
            //      this.$api.msg('请输入正确的手机号');
            //     return false;
            // }

            if (!params.purchaseTime) {
                this.$api.msg('请选择购买日期');
                return false;
            }
            if (params.vehicleBuyPrice !== '' && PriceType) {
                this.$api.msg('请输入正确购买价格');
                return false;
            }
            if (this.isRequired == true && this.vehicleImgList.length <= 0) {
                this.$api.msg('请上传车辆图片');
                return false;
            }

            if (this.isInsure.TabCur) {
                if (params.userIdCard == '') {
                    this.$api.msg('请扫描身份证件正面');
                    return false;
                }

                if (params.userSignBase64Img == '') {
                    this.$api.msg('电子签名不能为空');
                    return false;
                }
                if (this.isInsure.TabCur == 1 && this.navData.TabCur == 31 && params.vehicleBuyPrice == '') {
                    this.$api.msg('请填写车辆价格');
                    return false;
                }
                if (!this.check) {
                    this.$api.msg('请阅读并勾选保障告知书');
                    return false;
                }
            } else {
                if (params.userIdCard == '') {
                    this.$api.msg('请输入身份证号码');
                    return false;
                }
            }

            let userIdCardCheckRes = this.userIdCardCheck(params.userIdCard);
            if (params.userIdCard != '' && userIdCardCheckRes == false) {
                this.$api.msg('身份证号码有误请重新输入');
                this.lmVehicleInfo.userIdCard = null;
                return false;
            }
            let ageCheckRes = this.userIdCardCheck(params.userIdCard);
            if (params.userIdCard != '' && ageCheckRes == false) {
                this.$api.msg('年龄不符请绑定14-80岁的有效身份证号');
                this.lmVehicleInfo.userIdCard = null;
                return false;
            }
            return true;
        },

        // 保障告知书
        handleCheckChange() {
            this.check = !this.check;
        },

        // 提交
        async handleVehicleSubmit(e) {
            // this.activeDisabled = true;
            if (this.lmVehicleInfo.valueName !== '') {
                this.lmVehicleInfo.brandName = `${this.lmVehicleInfo.brandName}-${this.lmVehicleInfo.valueName}`;
            }
            let params = {
                ...this.lmVehicleInfo,
                ...e.detail.value,
                // 车辆来源	1 立马  0 非立马
                vehicleSource: 1,
                pictureOrderParamList: this.vehicleImgList,
                brandName: '立马',
                batteryCodes: this.batteryOrderArr,
                chargerCodes: this.chargerCodes
            };
            if (!(await this.validateForm(params))) {
                this.activeDisabled = false;
                return false;
            }
            console.log('提交的车辆', params);
            // return;
            if (this.isInsure.TabCur) {
                this.activeInsure(params);
            } else {
                this.bindVehicleSave(params);
            }
        },

        // 激活第三方保险服务
        async activeInsure(params) {
            const insureParams = {
                vinNo: params.vehicleNo,
                dealerId: params.custCode,
                storeId: params.salesOutletsCode,
                proCity: params.storeAddress,
                ownerName: params.userName,
                ownerIdNo: params.userIdCard,
                ownerTel: params.userPhone,
                ownIdBase64: params.userIdCardBase64Img,
                sqGzBase64: params.userSignBase64Img,
                outFactoryTime: params.offlineDate,
                planId: this.navData.TabCur,
                planName: this.navData.TabCurName,
                carPrice: params.vehicleBuyPrice,
                modelName: params.vehicleType,
                provinceName: params.provinceName,
                province: params.province,
                cityName: params.cityName,
                city: params.city,
                districtName: params.countyName,
                district: params.county,
                buyTime: this.dayjs(params.purchaseTime).format('YYYY-MM-DD 00:00:00')
            };

            this.$dialog.loading();
            const res = await this.$http.post('/yst/aftersale/insure/InsureApplication', {
                ...insureParams
            });
            this.$dialog.loaded();
            if (res.success) {
                this.$dialog.success('保险激活成功');
                this.lmVehicleInfo.orderNo = res.data.data.order_no;
                this.bindVehicleSave(params);
                // 调用达摩添加会员标签
                await this.$http.post('/yst/aftersale/demogic/saveMemberTag', {
                    mobile: this.userInfo.userPhone,
                    tagItemIds: this.navData.TagItemId
                });
            } else {
                this.$api.msg(res.msg);
            }
            this.$dialog.loaded();
            this.activeDisabled = false;
        },

        // 激活绑定
        async bindVehicleSave(params) {
            uni.showLoading({
                title: '激活中...'
            });
            // 激活绑定接口
            this.$request({
                url: 'v3/vehicle/front/vehicle/activateThreePackage',
                method: 'post',
                header: {
                    'Content-Type': 'application/json;charset=UTF-8',
                },
                data: {
                    ...params,
                    vehicleImgList: this.vehicleImgList,
                    batteryOrderArr: this.batteryOrderArr,
                    chargerCodes: this.chargerCodes
                }
            })
                .then((res) => {
                    uni.hideLoading();
                    if (res.state !== 200) {
                        this.$api.msg(res.msg || '激活失败，请稍后再试');
                        return;
                    } else {
                        this.$api.msg('三包激活成功');
                        this.lmVehicleInfo.vehicleNo = '';
                        this.lmVehicleInfo.vehicleType = '';
                        this.$Router.back();
                    }
                    this.activeDisabled = false;
                })
                .catch((err) => {
                    uni.hideLoading();
                    this.activeDisabled = false;
                    this.$api.msg(err.msg || '激活失败，请稍后再试');
                });
        },

        // 撤销保险
        async revokeInsurance() {
            this.$dialog.loading();
            // 激活绑定接口
            const res = await this.$http.post('/yst/aftersale/insure/InsureCancel', {
                orderNo: this.lmVehicleInfo.orderNo
            });
            this.$dialog.loaded();
            if (res.success) {
                // 保险取消成功
            } else {
                this.$api.msg(res.msg);
            }
        },

        // 购买日期
        handleDateChange(value) {
            this.lmVehicleInfo.purchaseTime = value;
        },

        // 根据整车号查询车辆基本信息
        async queryVehicleInfoByVehicleNo(vehicleNo) {
            const res = await this.$http.post('/yst/aftersale/miniApp/car/carQuery', {
                vehicleNo
            });
            if (res.success) {
                // 限制激活日期与当前车辆生产日期比较
                const vehicleProductionDate = this.dayjs(res.data.manufactureDate).unix();
                const limitationData = this.dayjs('2022-12-31 00:00:00').unix();
                if (limitationData >= vehicleProductionDate) {
                    this.lmVehicleInfo.vehicleNo = null;
                    return this.$api.msg('不在可激活时间范围内');
                }

                this.lmVehicleInfo.vehicleType = res.data?.vehicleType;
                this.lmVehicleInfo.vehicleNo = res.data?.vehicleNo;
                const limaCarData = {
                    vehicleName: res.data.vehicleName,
                    purchaseTime: res.data.purchaseTime || this.lmVehicleInfo.purchaseTime,
                    machineNo: res.data.machineNo,
                    controlNo: res.data.controlNo,
                    alarmNo: res.data.alarmNo,
                    manufactureDate: res.data.manufactureDate,
                    bindingTime: res.data.bindingTime,
                    invMatter: res.data.invMatter,
                    vehicleSeq: res.data.vehicleSeq,
                    eNo: res.data.eNo,
                    sourceSysType: res.data.sourceSysType,
                    produceOrderNo: res.data.produceOrderNo,
                    offlineDate: res.data.offlineDate,
                    vehicleSpecs: res.data.vehicleSpecs,
                    vehicleColor: res.data.vehicleColor,
                    itemType3: res.data.itemType3,
                    spuCode: res.data.spuCode,
                    mtnrv: res.data.mtnrv,
                    spuName: res.data.spuName,
                    itemGroup2: res.data.itemGroup2
                };

                this.lmVehicleInfo = {
                    ...this.lmVehicleInfo,
                    ...limaCarData
                };
            } else {
                this.lmVehicleInfo.vehicleType = '';
                this.$api.msg(res.msg || '车架号不存在');
            }
        },

        // 上传车辆图片
        handleChangeImg(val) {
            this.vehicleImgList = val?.map((x) => ({
                ...x
            }));
        },

        // 手动输入身份证
        handleInput() {
            let checksums = this.userIdCardCheck(this.lmVehicleInfo.userIdCard);
            if (checksums) {
                this.ageCheck(this.lmVehicleInfo.userIdCard);
            } else {
                return this.$api.msg('请输入18位有效身份证号');
            }
        },

        // 用户身份证年龄校验 14岁-80岁为业务有效值
        ageCheck(IdCard) {
            // 用户出生年份
            let userYear = parseInt(IdCard.slice(6, 10));
            // 当前年份
            let currentYear = parseInt(this.dayjs().format('YYYY'));

            if (currentYear - userYear >= 14 && currentYear - userYear <= 80) {
                // this.getPersonInfo(IdCard);
                return true;
            } else {
                this.$api.msg('年龄不符请绑定14-80岁的有效身份证号');
                return false;
            }
        },

        // 用户身份证校验
        userIdCardCheck(IdCard) {
            // 检查长度
            if (IdCard.length !== 18) {
                return false;
            }
            // 加权因子
            const weightFactors = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
            // 校验码值
            const checkCodeValues = '10X98765432';
            let sum = 0;
            for (let i = 0; i < 17; i++) {
                sum += parseInt(IdCard[i]) * weightFactors[i];
            }
            const remainder = sum % 11;
            const checkCode = checkCodeValues[remainder];
            return IdCard[17].toUpperCase() === checkCode;
        },

        // ocr识别
        goCuttingScan() {
            const that = this;
            uni.showActionSheet({
                itemList: ['相机', '从相册中选择'],
                success: (res) => {
                    uni.chooseMedia({
                        count: 1, // 默认上传图片数
                        sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
                        sourceType: res.tapIndex === 1 ? ['album'] : ['camera'], //图片来源
                        camera: 'back',
                        success: (res) => {
                            console.log(res.tempFiles[0].tempFilePath);
                            console.log('====================================');

                            const tempFilePaths = res.tempFiles[0].tempFilePath;
                            // base64 图片增加前缀
                            pathToBase64(tempFilePaths).then((base64) => {
                                that.lmVehicleInfo.userIdCardBase64Img = `${that.base64Prefix}${base64.substring(base64.indexOf(',') + 1)}`;
                            });
                            that.$dialog.loading();
                            uni.uploadFile({
                                url: `${configService.apiUrl}/yst/aftersale/miniApp/bindVehicle/ocrScan`,
                                filePath: tempFilePaths,
                                name: 'file',
                                success: (uploadFileRes) => {
                                    that.$dialog.loaded();
                                    const ocrData = JSON.parse(uploadFileRes.data);
                                    if (ocrData.code === 200) {
                                        const ocrDataPares = JSON.parse(ocrData.data?.body.data);
                                        const personData = ocrDataPares?.data.face.data;

                                        let returnResult = that.userIdCardCheck(personData.idNumber);

                                        if (returnResult) {
                                            let ageCheckRes = that.ageCheck(personData.idNumber);
                                            if (ageCheckRes) {
                                                that.lmVehicleInfo.userIdCard = personData.idNumber;
                                                that.lmVehicleInfo.userName = personData.name;
                                            }
                                        } else {
                                            that.lmVehicleInfo.userIdCard = null;
                                            return that.$dialog.toast('请扫描有效身份证');
                                        }
                                    } else {
                                        that.$dialog.toast('请传入可识别的身份证');
                                    }
                                }
                            });
                        }
                    });
                }
            });
        },

        // 根据证件号码查询信息
        async getPersonInfo(userIdCard) {
            this.$dialog.loading();
            const res = await this.$http.get(`/yst/aftersale/miniApp/bindVehicle/queryByUserCardId/${userIdCard}`);
            if (res.success) {
                this.lmVehicleInfo.userArea = `${res.data.province}${res.data.city === null ? '' : res.data.city}`;
                this.lmVehicleInfo.userBirthday = res.data.userBirthday;
                this.lmVehicleInfo.userSex = res.data.userSex;
            } else {
                this.$api.msg(res.msg);
            }
            this.$dialog.loaded();
        },

        // 保存签名
        save(tempPath) {
            this.signImg = tempPath;
            pathToBase64(tempPath).then((base64) => {
                this.lmVehicleInfo.userSignBase64Img = `${this.base64Prefix}${base64.substring(base64.indexOf(',') + 1)}`;
            });
        },

        // 签名重写
        signRewrite() {
            this.signImg = '';
            this.lmVehicleInfo.userSignBase64Img = '';
        },

        previewImage() {
            if (this.navData.TabCur == 21) {
                // 车损险
                uni.previewImage({
                    current: 1,
                    urls: [`https://lima-uat2.oss-cn-hangzhou.aliyuncs.com/2B/activity/LM_InsuranceNotice/8d4dbf12daf7909e5288935a2c9fb78.png?time=${this.currentTimestamp}`]
                });
            } else {
                // 盗抢险
                uni.previewImage({
                    current: 1,
                    urls: [`https://lima-uat2.oss-cn-hangzhou.aliyuncs.com/2B/activity/LM_InsuranceNotice/5d4ec494d0d7666ad3715afe3ed9c9d.png?time=${this.currentTimestamp}`]
                });
            }
        },

        actFn() {
            if (this.getExternalLinkData) {
                console.log(this.getExternalLinkData);
                this.actParams.enterProgm = '立马车服';
                uni.removeStorageSync('externalLinkData');
                console.log(this.actParams);
                this.actSending(this.actParams);
            } else {
                console.log(this.actParams);
                this.actSending(this.actParams);
            }
        },

        async actSending(values) {
            if (APP_ID == 'wxf8ca49759c4a3acc') return;
            uni.request({
                method: 'POST',
                url: `https://lmhdxcx.lima-info.com:8082/api/tracking/evtTracking/save`,
                data: {
                    ...values
                },
                success: (res) => {
                    console.log('====================================');
                    console.log(res);
                    console.log('====================================');
                }
            });
        }
    }
};
</script>
<style lang="scss">
.bind-vehicle {
    width: 100%;
    min-height: 100vh;
    background: $bg1;
    padding-top: 30rpx;
    padding-bottom: calc(130rpx + env(safe-area-inset-bottom));

    .brand-nav {
        overflow: hidden;
        margin-top: 24rpx;
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
        background: #fff;
    }

    .vehicle-card {
        border-top-left-radius: 0;
        border-top-right-radius: 0;
        background: #fff;
    }

    .cu-form-group {
        background-color: #ffffff;
        display: flex;
        align-items: center;
        min-height: 100rpx;

        .form_input {
            font-size: $fs-base;
        }

        .title {
            font-size: $fs-base;
            min-width: calc(4em + 50rpx);
        }

        .picker {
            text-align: left;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: $fs-base;
            .picker-value {
                flex: 1;
            }
            .el-placeholder {
                color: #9d9d9d;
            }
        }
    }

    .brand-container {
        justify-content: flex-start;

        .brand-btn {
            display: flex;
            justify-content: flex-start;

            .item {
                margin-right: 50rpx;
                padding: 10rpx 30rpx;
                border-radius: 40rpx;
                background-color: #f5f5f5;
                text-align: center;
                font-size: 28rpx;
                color: #000;
            }

            .item.active {
                background-color: #c70e2d;
                color: #fff;
            }
        }
    }

    .tips {
        font-size: $fs-s;
    }

    .border-line {
        border-bottom: 1rpx solid #eee;
    }
}

.arrow-icon {
    width: 24rpx;
    height: 24rpx;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    margin-left: 20rpx;
    margin-right: 8rpx;
    box-shadow: -2px 2px 0 0px #c7c7c7 inset;
}

.el-card {
    margin-left: 30rpx;
    margin-right: 30rpx;
    border-radius: 16rpx;
    padding: 0rpx 30rpx;
    background-color: #fff;
}

.lima-bg-red {
    background-color: #c70e2d !important;
    color: #fff;
}

.request {
    color: #ff474a;
}

.border-radius-top {
    border-radius: 16rpx 16rpx 0 0;
}

.border-radius-bottom {
    border-radius: 0 0 16rpx 16rpx;
}

.avatar-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .avatar-text {
        font-weight: 400;
        font-size: 28rpx;
        color: rgba(0, 0, 0, 0.25);
    }

    .avatar {
        width: 32rpx;
        height: 32rpx;
        background: #f8f8f8;
    }
}

.el-submit-btn-view {
    display: flex;
    position: fixed;
    bottom: 0;
    justify-content: center;
    width: 100%;
    height: calc(100rpx + env(safe-area-inset-bottom));
    background: #fff;
    box-shadow: 0 -2px 10px 0 rgba(0, 21, 61, 0.08);
    z-index: 5;

    .el-submit-btn {
        margin-top: 10rpx;
        border-radius: 40rpx;
        width: 686rpx;
        height: 80rpx;
        background: #c70e2d;
        font-size: 30rpx;
        color: #fff;
    }
}

.active-vehicle {
    color: #c6242f;
}

.signImg {
    width: 100%;

    image {
        width: 100%;
        height: 350rpx;
    }

    .btn-box {
        display: flex;
        flex-direction: row-reverse;
        margin-top: 20rpx;

        .signImg-btn {
            padding: 0 20rpx;
            border-radius: 20rpx;
            height: 60rpx;
            background: #c70e2d;
            line-height: 60rpx;
            text-align: center;
            color: #fff;
        }
    }
}

.signature-status {
    padding: 20rpx 0;
    border-top: none;

    .signature-check {
        margin-right: 20rpx;
    }

    .notice-letter {
        color: #c70e2d;
    }
}

.battery-item {
    font-size: 26rpx;
    color: #c70e2d;
    margin: 14rpx 0;
    display: flex;
    justify-content: space-between;

    image {
        width: 32rpx;
        height: 32rpx;
        transform: rotate(45deg);
    }
}
</style>
