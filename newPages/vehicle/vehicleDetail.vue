<template>
    <view class="vehicle-card">
        <view class="cu-form-group">
            <view class="title">车辆来源</view>
            <text class="picker" style="width: 100%">{{ vehicleInfo.vehicleSourceName || '--' }}</text>
        </view>
        <view v-if="vehicleInfo.deviceNo" class="cu-form-group">
            <view class="title">车架号</view>
            <text class="picker" style="width: 100%">{{ vehicleInfo.deviceNo }}</text>
        </view>
        <view class="cu-form-group">
            <view class="title">车辆信息</view>
            <text class="picker" style="width: 100%">{{ `${vehicleInfo.carModel || ''}` }}</text>
        </view>
        <view class="cu-form-group">
            <view class="title">购车时间</view>
            <text class="picker" style="width: 100%">{{ vehicleInfo.bindTime }}</text>
        </view>
        <view v-if="vehicleInfo.salesOutletsName" class="cu-form-group">
            <view class="title">购车门店</view>
            <text class="picker" style="width: 100%">{{ vehicleInfo.salesOutletsName || '--' }}</text>
        </view>
        <view class="btn-content">
            <button v-if="source" class="cu-btn el-btn" @click="confirmSelection">确定</button>
            <button class="cu-btn el-btn" @click="handleUnbind">解绑</button>
        </view>
    </view>
</template>

<script>
export default {
    name: 'VehicleDetail',
    data() {
        return {
            vehicleInfo: {},
            source: null,
            bindId: null
        };
    },
    onLoad(option) {
        const locaData = uni.getStorageSync('vehicleInfo');

        if (!this.vehicleInfo) {
            this.$api.msg('车辆信息不存在，请重新选择');
            uni.navigateBack({ delta: 1 });
            return;
        } else {
            locaData.bindTime = this.dayjs(locaData.gmtBind).format('YYYY-MM-DD');
            this.vehicleInfo = locaData;
        }
    },
    onUnload() {
        uni.removeStorage({
            key: 'vehicleInfo'
        });
    },
    methods: {
        // 解绑方法
        handleUnbind() {
            uni.showModal({
                title: '解绑确认',
                content: '您确定要解绑此车辆吗？',
                cancelText: '取消',
                confirmText: '确认',
                success: async (res) => {
                    if (res.confirm) {
                        this.$request({
                            url: 'v3/vehicle/front/vehicle/unbind',
                            method: 'POST',
                            data: {
                                deviceNo: this.vehicleInfo.deviceNo
                            }
                        }).then((res) => {
                            if (res.success) {
                                this.$api.msg('解绑成功');
                                this.$Router.back();
                            } else {
                                this.$api.msg(res.msg || '解绑失败，请稍后再试');
                            }
                        });
                    }
                }
            });
        },

        confirmSelection() {
            uni.setStorageSync('selectCarValue', this.vehicleInfo);
            uni.navigateBack({ delta: 1 });
        }
    }
};
</script>

<style lang="scss" scoped>
page {
    background-color: #f5f5f5;
}

.vehicle-card {
    margin: 20rpx;
    background: #fff;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    overflow: hidden;

    .cu-form-group {
        display: flex;
        align-items: center;
        padding: 30rpx 32rpx;
        border-bottom: 1rpx solid #eee;
        position: relative;

        &:last-child {
            border-bottom: none;
        }

        &::after {
            content: '';
            position: absolute;
            left: 32rpx;
            right: 32rpx;
            bottom: 0;
            height: 1rpx;
            background: linear-gradient(90deg, transparent, #eee 50%, transparent);
        }

        &:last-child::after {
            display: none;
        }

        .title {
            font-size: 28rpx;
            color: #666;
            font-weight: 500;
            min-width: 140rpx;
            margin-right: 20rpx;
            position: relative;

            &::before {
                content: '';
                position: absolute;
                left: -12rpx;
                top: 50%;
                transform: translateY(-50%);
                width: 6rpx;
                height: 16rpx;
                background: linear-gradient(180deg, #f15670 0%, #c70e2d 100%);
                border-radius: 3rpx;
            }
        }

        .picker {
            flex: 1;
            font-size: 30rpx;
            color: #333;
            text-align: right;
            font-weight: 400;
            line-height: 1.4;
            word-break: break-all;

            &:empty::after {
                content: '--';
                color: #999;
            }
        }
    }
}

.btn-content {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20rpx 20rpx 40rpx;
    background: #fff;
    border-top: 1rpx solid #eee;
    box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.05);

    .cu-btn {
        width: 100%;
        height: 88rpx;
        border-radius: 44rpx;
        font-size: 32rpx;
        font-weight: 600;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &.el-btn {
            background: #c70e2d;
            color: #fff;
            box-shadow: 0 8rpx 24rpx rgba(76, 175, 80, 0.3);

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                transition: left 0.6s ease;
            }

            &:active {
                transform: translateY(2rpx);
                box-shadow: 0 4rpx 12rpx #c70e2d;

                &::before {
                    left: 100%;
                }
            }
        }

        &:active {
            opacity: 0.8;
        }
    }
}

// 添加页面底部安全区域
.btn-content {
    padding-bottom: env(safe-area-inset-bottom);
}

// 响应式适配
@media screen and (max-width: 375px) {
    .vehicle-card {
        margin: 16rpx;

        .cu-form-group {
            padding: 24rpx 28rpx;

            .title {
                font-size: 26rpx;
                min-width: 120rpx;
            }

            .picker {
                font-size: 28rpx;
            }
        }
    }

    .btn-content .cu-btn {
        height: 80rpx;
        font-size: 30rpx;
    }
}

// 暗色模式适配
@media (prefers-color-scheme: dark) {
    page {
        background-color: #1a1a1a;
    }

    .vehicle-card {
        background: #2d2d2d;
        box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.3);

        .cu-form-group {
            border-bottom-color: #404040;

            &::after {
                background: linear-gradient(90deg, transparent, #404040 50%, transparent);
            }

            .title {
                color: #ccc;
            }

            .picker {
                color: #fff;
            }
        }
    }

    .btn-content {
        background: #2d2d2d;
        border-top-color: #404040;
        box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.3);
    }
}
</style>
