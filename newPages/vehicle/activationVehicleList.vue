<template>
    <view class="activation-list"></view>
</template>
<script>
import { mapState } from 'vuex';
export default {
    data() {
        return {
            list: []
        };
    },
    computed: {
        ...mapState(['userCenterData', 'userInfo', 'hasLogin'])
    },
    onLoad(option) {
        this.getActivationVehicleList();
    },
    methods: {
        getActivationVehicleList(params) {
            this.$request({
                url: 'v3/vehicle/front/vehicle/threePackageQueryByDeviceNo',
                method: 'GET'
            })
                .then((res) => {
                    if (res.code === 0) {
                        this.list = res.data;
                    } else {
                        this.$api.msg(res.msg);
                    }
                })
                .catch((err) => {
                    console.error('获取激活车辆列表失败', err);
                    this.$api.msg('获取激活车辆列表失败，请稍后重试');
                });
        }
    }
};
</script>
<style lang="scss" scoped></style>
