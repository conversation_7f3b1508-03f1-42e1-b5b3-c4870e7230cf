<template>
	<view class="container">
		<view class="header flex_row_center_center">
			<view class="search_input">
				<image class="search_icon" :src="imgUrl + 'business/search_small.png'" mode="" @click="search"></image>
				<input focus type="text" placeholder="请输入详细地址" v-model="address" @confirm="search" />
			</view>
		</view>
		<view class="content">
			<view class="address_item" v-for="(item, index) in poistionList" :key="index" @click="goStore(item)">
				<p>{{ `${item.cityname}${item.adname}${item.name}` }}</p>
				<p>{{ item.address }}</p>
			</view>
		</view>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	export default {
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				address: '',
				poistionList: [],
			}
		},
		mounted() {
		},
		computed: {
			...mapState(['locationObj'])
		},
		methods: {
			...mapMutations(['setLocation']),
			goStore(info){
				const { location } = info;
				this.setLocation({ location });
				uni.navigateBack({
					delta: 1
				});
			},
			search(){
				if(this.address == ''){
					return;
				}
				const { cityCode } = this.locationObj;
				let _key = getApp().globalData.h5GdFwKey; //高德API key  web服务
				uni.request({
					method: 'GET',
					url: 'https://restapi.amap.com/v3/place/text',
					data: {
						key: _key,
						extensions: 'all',
						keywords: this.address,
						citylimit: true,
						city: cityCode,
						output: 'JSON',
					},
					success: (res) => {
						if(res.data.status == '1'){
							const poistionList = res.data.pois;
							this.poistionList = poistionList;
						}
					},
					fail: r => {
						console.log(r);
					}
				  });
			},
		
		}
	}
</script>

<style lang="scss" scoped>
	page {
		height: 100%;
		background-color: #f5f5f5;
	}
	.container {
		padding-bottom: 100rpx;
		.header{
			height: 100rpx;
			padding: 0 22rpx 0 28rpx;
			background-color: #fff;
			.search_input{
				position: relative;
				width: 100%;
				.search_icon{
					position: absolute;
					top: 12rpx;
					left: 18rpx;
					width: 38rpx;
					height: 38rpx;
				}
				input{
					height: 60rpx;
					background-color: #F5F5F5;
					border-radius: 32rpx;
					padding-left: 70rpx;
					padding-right: 64rpx;
					.input-placeholder{
						font-size: 24rpx;
					}
				}
			}
		}
		.content{
			.address_item{
				padding-left: 20rpx;
				padding-right: 20rpx;
				padding-top: 10rpx;
				border-bottom: 2rpx solid #ccc;
				background-color: #fff;
				p:first-of-type{
					font-weight: bold;
					font-size: 30rpx;
				}
				p:last-of-type{
					margin-top: 10rpx;
					font-size: 28rpx;
				}
			}
		}
	}
</style>
