<template>
	<view class="container">
		<view class="header flex_row_center_center">
			<view class="search_input">
				<image class="search_icon" :src="imgUrl + 'business/search_small.png'" mode="" @click="search"></image>
				<input focus type="text" placeholder="请输入城市名称" v-model="address" @confirm="getCityList" />
			</view>
		</view>
		<view class="locateBox">
			<view class="">
				当前定位
			</view>
			<view class="store_locate flex_row_between_center">
				<view class="sl_left">
					<image :src="imgUrl + 'business/locate2.png'" mode=""></image>
					<text>
						{{ locationObj.cityName }}
					</text>
				</view>
				<view class="sl_right" @click="reLocate">
					重新定位
				</view>
			</view>
		</view>
		<view class="city_box" v-for="(item,index) in cityList" :key="index">
			<view class="letter flex_row_start_center">
				{{ item.initial }}
			</view>
			<view class="city_list">
				<view class="city_item flex_row_start_center" v-for="(city,c_index) in item.data" :key="c_index" @click="() => chooseCity(city)">
					{{ city.cityName }}
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex';
	import { } from '@/utils/stat.js'
	export default {
		data() {
			return {
				cityList: [],
				address: '',
				imgUrl: getApp().globalData.imgUrl,
			}
		},
		mounted() {
			this.getCityList()
		},
		computed: {
			...mapState(['locationObj']),
		},
		methods: {
			...mapMutations(['setLocation']),
			async reLocate(){
				const data = await this.$getLocation()
				this.setLocation(data);
			},
			chooseCity(city) {
				console.log(city);
				let address = city.provinceName + city.cityName;
				uni.request({
					method: 'GET',
					url: 'https://restapi.amap.com/v3/geocode/geo',
					data: {
						key: getApp().globalData.h5GdFwKey,
						address,
						output: 'JSON',
					},
					success: (res) => {
						//用户所在的地理位置信息
						if(res.data.status == '1'){
							const location = res.data.geocodes[0].location;
							
							const cityCode = city.nativeCode.slice(0,4)+'00'
							this.setLocation({ cityCode, cityName: city.cityName, location });
							
							let pages = getCurrentPages(); //获取所有页面栈实例列
							let prevPage = pages[pages.length - 2]; //上一页页面实例
							if (prevPage && prevPage.route == 'newPages/chooseAddress/chooseAddress') {
								uni.navigateBack({
									delta: 1
								});
							} else {
								this.$Router.replace({
									path: '/newPages/chooseAddress/chooseAddress',
								})
							}
						}
					},
					fail: r => {
						console.log(r);
					}
				  });
				
			},
			getCityList(){
				let param = {}
				param.url = 'v3/seller/front/store/cityList'
				param.method = 'GET'
				param.data = {
					cityName: this.address,
				}
				this.$request(param).then(res => {
					if (res.state == 200) {
						const data = res.data;
						const cityList = [];
						for (let item in data) {
							const obj = {
								initial: item,
								data: data[item],
							};
							cityList.push(obj)
						}
						this.cityList = cityList;
					}
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	page{
		height: 100%;
	}
	.container {
		background-color: #f5f5f5;
		padding-bottom: 100rpx;
		height: 100%;
		.header{
			height: 100rpx;
			padding: 0 22rpx 0 28rpx;
			background-color: #fff;
			.search_input{
				position: relative;
				width: 100%;
				.search_icon{
					position: absolute;
					top: 12rpx;
					left: 18rpx;
					width: 38rpx;
					height: 38rpx;
				}
				input{
					height: 60rpx;
					background-color: #F5F5F5;
					border-radius: 32rpx;
					padding-left: 70rpx;
					padding-right: 64rpx;
					.input-placeholder{
						font-size: 24rpx;
					}
				}
			}
		}
		.city_box{
			font-size: 28rpx;
			.letter{
				height: 80rpx;
				padding-left: 34rpx;
				color: #101010;
				font-weight: bold;
			}
			.city_item{
				padding-left: 68rpx;
				height: 80rpx;
				background-color: #fff;
				border-bottom: 2rpx solid #F5F5F5;
			}
		}
		.locateBox{
			background-color: #fff;
			font-size: 28rpx;
			padding: 0 24rpx;
			margin-bottom: 6rpx;
			.store_locate{
				height: 80rpx;
				.sl_left{
					display: flex;
					align-items: center;
					flex: 1;
					overflow: hidden;
					text{
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}
					image{
						width: 28rpx;
						height: 28rpx;
						margin-right: 6rpx;
						flex-shrink: 0;
					}
				}
				.sl_right{
					display: flex;
					align-items: center;
					color: #F81C05;
					image{
						width: 38rpx;
						height: 38rpx;
						margin-right: 6rpx;
						flex-shrink: 0;
					}
				}
			}
		}
	}
</style>
