<template>
	<view class="choose_sotre">
		<view class="choose_sotre_con">
			<view class="choose_store_top flex_row_between_center">
				<span class="flex city_select" @click="manualSelection">
					<text style="margin-right: 16rpx;">{{ locationObj.cityName || '--' }}</text>
					<image :src="imgUrl + 'business/icon_down.png'" mode=""></image>
				</span>
				<view class="search_input" @click="chooseAddress">
					<image class="search_icon" :src="imgUrl + 'business/search_small.png'" mode=""></image>
					<input type="text" placeholder="请输入详细地址" />
				</view>
			</view>

			<view class="mapBox">
				<map style="width: 100%; height: 414rpx;" :latitude="latitude" :longitude="longitude" :markers="covers">
				</map>
			</view>
			<view class="near_content" v-if="poistionList.length> 0">
				<view class="address_item" v-for="(item, index) in poistionList" :key="index" @click="setItem(item)">
					<p>{{ `${item.name}` }}</p>
					<p>{{ item.address }}</p>
				</view>
			</view>
			<view v-else class="no_data">暂无信息~</view>
		</view>
	</view>
</template>

<script>
	import { mounted } from 'vue'
	import { mapState, mapMutations } from 'vuex'
	import {
		getLocation,
		getAreaInfo,
	} from '@/utils/stat.js'
	export default {
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				latitude: '',
				longitude: '',
				covers: [],
				poistionList: [],
				img_locate: getApp().globalData.imgUrl + 'business/location_red.png',
			}
		},
		onShow() {
			const { location } = this.locationObj;
			this.getPositionList(location);
			this.setMapCovers(location);
		},
		onLoad() {
			
		},
		computed: {
			...mapState(['locationObj'])
		},
		methods: {
			...mapMutations(['setLocation']),
			setMapCovers(location){
				const locationArr = location.split(',');
				const lng = locationArr[0];
				const lat = locationArr[1];
				this.longitude = lng;
				this.latitude = lat;
				const cover = {
					id: 6,
					latitude: lat,
					longitude: lng,
					iconPath: this.img_locate,
					width: 40, //宽
					height: 40, //高
				};
				this.covers.splice(0,1,cover);
			},
			setItem(item) {
				this.setLocation({ location: item.location });
				this.setMapCovers(item.location)
			},
			// 手动选择
			manualSelection() {
				uni.navigateTo({
					url: '/newPages/chooseAddress/manualSelection'
				});
			},
			chooseAddress() {
				uni.navigateTo({
					url: '/newPages/chooseAddress/chooseDetailAddress'
				});
			},

			getPositionList(location) {
				uni.request({
					method: 'GET',
					url: 'https://restapi.amap.com/v3/geocode/regeo',
					data: {
						key: getApp().globalData.h5GdFwKey,
						location,
						extensions: 'all',
						output: 'JSON',
					},
					success: (res) => {
						if(res.data.status == '1'){
							const poistionList = res.data.regeocode.pois;
							this.poistionList = poistionList;
						}
					},
					fail: r => {
						console.log(r);
					}
				  });
			},
			
		}
	}
</script>

<style lang="scss" scoped>
	.choose_sotre {
		background-color: #fff;
		padding-bottom: 100rpx;
		// height: 100vh;
		overflow-y: auto;
		.choose_sotre_con{
			.choose_store_top {
				height:100rpx;
				padding: 0 24rpx 0 30rpx;
				.city_select{
					font-size: 30rpx;
					image{
						width: 24rpx;
						height: 24rpx;
					}
				}
				.search_input{
					flex: 1;
					margin-left: 20rpx;
					position: relative;
					font-size: 26rpx;
					.search_icon{
						position: absolute;
						top: 14rpx;
						left: 18rpx;
						width: 38rpx;
						height: 38rpx;
					}
					input{
						font-size: 26rpx;
						height: 66rpx;
						background-color: #F5F5F5;
						border-radius: 32rpx;
						padding-left: 64rpx;
						.input-placeholder{
							font-size: 26rpx;
						}
					}
				}
			}
			.store_locate{
				height: 80rpx;
				padding: 0 24rpx;
				font-size: 28rpx;
				margin-bottom: 6rpx;
				.sl_left{
					display: flex;
					align-items: center;
					flex: 1;
					overflow: hidden;
					text{
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}
					image{
						width: 38rpx;
						height: 38rpx;
						margin-right: 6rpx;
						flex-shrink: 0;
					}
				}
				.sl_right{
					display: flex;
					align-items: center;
					color: #1D8248;
					image{
						width: 38rpx;
						height: 38rpx;
						margin-right: 6rpx;
						flex-shrink: 0;
					}
				}
			}
			.mapBox{
			}
			.near_content{
				.address_item{
					padding-left: 20rpx;
					padding-right: 20rpx;
					padding-top: 10rpx;
					border-bottom: 2rpx solid #ccc;
					background-color: #fff;
					p:first-of-type{
						font-weight: bold;
						font-size: 30rpx;
					}
					p:last-of-type{
						margin-top: 10rpx;
						font-size: 28rpx;
					}
				}
		}
		}
	}
	.no_data {
		position: fixed;
		top: 50%;
		left: 50%;
		transform: translate(-50%,-50%);
		font-size: 28rpx;
		color: #BEBEBE;
	}
</style>