<!-- 我的收藏页面 -->
<template>
	<view class="container" :class="{ container_FF: recommendLen == 0 && collectList.length == 0 }" ref="container" :style="mix_diyStyle">
		<!-- 空白页 start-->
		<view
			v-if="!collectList.length && loadingState != 'first_loading'"
			class="flex_column_start_center empty_part"
			:class="{ empty_part_FF: recommendLen == 0 && collectList.length == 0 }"
		>
			<image class="img" :src="imgUrl + 'empty_collect.png'" />
			<text class="tip_con">{{ $L('暂无收藏的商品哦') }}~</text>
			<view class="ope_btn flex_row_center_center" @click="goGoodsList()">
				{{ $L('马上去逛逛') }}
			</view>
		</view>
		<!-- 空白页 end-->

		<!-- 收藏的商品 start-->
		<view v-if="collectList.length" class="goods_list flex_column_start_start">
			<view v-for="(item, index) in collectList" :key="index" @touchstart="handleTouchStart($event, index)" @touchmove="handleTouchMove($event, index)">
				<goodsCollectItemH
					:isWeiXinBrower="isWeiXinBrower"
					:goods_info="item"
					@delGoods="delCollectGoods"
					@goShare="shareGoods1"
					:style="{ left: is_show_btn && followId == index ? '-160rpx' : '0' }"
					:left="is_show_btn && followId == index"
				/>
			</view>
		</view>
		<loadingState v-if="loadingState == 'first_loading' || collectList.length > 0" :state="loadingState" />
		<!-- 收藏的商品 end-->

		<!-- 推荐商品 start-->
		<!-- <view v-if="!hasMore">
			<recommendGoods ref="recomment_goods" />
		</view> -->
		<!-- 推荐商品 end-->

		<!-- 微信浏览器分享提示  start-->
		<view class="wx_brower_share_mask" v-if="showWeiXinBrowerTip">
			<view class="wx_brower_share_top_wrap">
				<image :src="imgUrl + 'wx_share_tip.png'" mode="widthFix" @tap="closeShareModel" class="wx_brower_share_img"></image>
			</view>
		</view>
		<!-- 微信浏览器分享提示  end-->
		<!-- 分享弹框 start -->
		<view class="share_model" v-if="share_model" @touchmove.stop.prevent="moveHandle">
			<view class="share_model_list">
				<!-- #ifdef H5 -->
				<view class="share_model_pre" @tap.stop="sldShareBrower(1)" v-if="isWeiXinBrower">
					<image :src="imgUrl + 'goods_detail/wx_share.png'" mode="aspectFit"></image>
					<text>{{ $L('微信好友') }}</text>
				</view>
				<view class="share_model_pre" @tap.stop="sldShareBrower(2)" v-if="isWeiXinBrower">
					<image :src="imgUrl + 'goods_detail/wechat_moments.png'" mode="aspectFit"></image>
					<text>{{ $L('微信朋友圈') }}</text>
				</view>
				<!-- #endif -->
				<!-- wx-1-start -->
				<!-- #ifdef MP-WEIXIN -->
				<button open-type="share" class="share_model_pre" @click="share_model = false">
					<image :src="imgUrl + 'goods_detail/wx_share.png'" mode="aspectFit"></image>
					<text>{{ $L('微信好友') }}</text>
				</button>
				<!-- #endif -->
				<!-- wx-1-end -->
				<!-- app-1-start -->
				<!-- #ifdef APP-PLUS -->
				<view class="share_model_pre" @tap.stop="sldShare(0, 'WXSceneSession')">
					<image :src="imgUrl + 'goods_detail/wx_share.png'" mode="aspectFit"></image>
					<text>{{ $L('微信好友') }}</text>
				</view>
				<view class="share_model_pre" @tap.stop="sldShare(0, 'WXSenceTimeline')">
					<image :src="imgUrl + 'goods_detail/wechat_moments.png'" mode="aspectFit"></image>
					<text>{{ $L('微信朋友圈') }}</text>
				</view>
				<!-- #endif -->
				<!-- app-1-end -->
			</view>
			<view class="share_model_close" @click="closeShareModel">
				<image :src="imgUrl + 'goods_detail/share_close.png'" mode="aspectFit"></image>
			</view>
		</view>
		<!-- 分享弹框 end -->
	</view>
</template>

<script>
import recommendGoods from '@/components/recommend-goods.vue';
import goodsCollectItemH from '@/components/goods_collect_item_h.vue';
import loadingState from '@/components/loading-state.vue';
import { mapState } from 'vuex';
export default {
	components: {
		recommendGoods,
		goodsCollectItemH,
		loadingState
	},
	data() {
		return {
			imgUrl: getApp().globalData.imgUrl,
			collectList: [], //收藏列表
			hasMore: true, //是否还有数据
			pageSize: 10,
			current: 1,
			loadingState: 'first_loading',
			share_model: false, //分享弹框
			isWeiXinBrower: false, //是否微信浏览器
			showWeiXinBrowerTip: false, //微信浏览器分享的提示操作
			recommendLen: 0, //推荐商品的length长度
			followId: '',
			startX: '',
			startY: '',
			is_show_btn: false //是否展示
		};
	},
	onLoad(option) {
		setTimeout(() => {
			uni.setNavigationBarTitle({
				title: this.$L('我的收藏')
			});
		}, 0);
		// #ifdef H5
		this.isWeiXinBrower = this.$isWeiXinBrower();
		// #endif
		// 父页面接收子组件recommend——goods.vue传过来的值
		uni.$on('recommendGoods', (options) => {
			this.recommendLen = JSON.parse(options.recommendLen);
		});
	},
	onShow() {
		this.getList();
	},
	onHide() {
		this.followId = -1;
	},
	computed: {
		...mapState(['userInfo'])
	},
	onShareAppMessage: function () {
		return {
			title: this.shareGoods.goodsName,
			path: '/standard/product/detail?productId=' + this.shareGoods.productId + '&goodsId=' + this.shareGoods.goodsId,
			imageUrl: this.shareGoods.goodsImage
		};
	},
	methods: {
		//浏览器分享
		sldShareBrower(type) {
			this.showWeiXinBrowerTip = true;
			this.share_model = false;
			this.$WXBrowserShareThen(type, {
				title: this.shareGoods.goodsName,
				desc: this.shareGoods.goodsBrief,
				link: this.shareGoods.shareLink,
				imgUrl: this.shareGoods.productImage
			});
		},

		//分享 type：分享类型 0 图文 2图片，scene 场景 WXSceneSession：分享朋友  WXSenceTimeline：分享朋友圈
		sldShare: function (type, scene) {
			let shareData = {};
			shareData.href = this.shareGoods.shareLink;
			shareData.title = this.shareGoods.goodsName;
			shareData.summary = this.shareGoods.goodsBrief;
			shareData.imageUrl = this.shareGoods.productImage;
			this.$weiXinAppShare(type, scene, shareData);
			this.closeShareModel(); //关闭分享
		},
		//分享当前商品
		shareGoods1(shareGoods) {
			this.shareGoods = shareGoods;
			this.share_model = true;
			this.followId = -1;
			this.is_show_btn = false;
		},
		//关闭分享弹框
		closeShareModel() {
			this.share_model = false;
			this.showWeiXinBrowerTip = false;
		},
		//获取收藏的商品
		getList() {
			let params = {};
			params.url = 'v3/member/front/followProduct/list';
			params.method = 'GET';
			params.data = {};
			params.data.pageSize = this.pageSize;
			params.data.current = this.current;
			this.loadingState = this.loadingState == 'first_loading' ? this.loadingState : 'loading';
			this.$request(params).then((res) => {
				if (res.state == 200) {
					if (this.current == 1) {
						this.collectList = res.data.list;
					} else {
						this.collectList = this.collectList.concat(res.data.list);
					}
					this.collectList.map((item) => {
						item.is_show_btn = false;
					});
					this.hasMore = this.$checkPaginationHasMore(res.data.pagination); //是否还有数据
					if (this.hasMore) {
						this.current++;
						this.loadingState = 'allow_loading_more';
					} else {
						this.loadingState = 'no_more_data';
					}
				} else {
					this.$api.msg(res.msg);
					this.loadingState = 'no_more_data';
				}
			});
		},
		//马上去逛逛事件
		goGoodsList() {
			this.$Router.push(`/standard/product/list`);
		},
		//页面触底事件
		onReachBottom() {
			if (this.hasMore == false) {
				this.$refs.recomment_goods.getMoreData();
			} else {
				this.getList();
			}
		},
		//取消收藏
		delCollectGoods(id) {
			this.$request({
				url: 'v3/member/front/followProduct/edit',
				data: {
					key: this.userInfo.access_token,
					productIds: id,
					isCollect: false
				},
				method: 'POST'
			}).then((res) => {
				this.$api.msg(res.msg);
				if (res.state == 200) {
					this.collectList.splice(this.followId, 1);
					this.followId = -1;
					this.is_show_btn = false;
				}
			});
		},

		handleTouchStart(e, followId) {
			this.startX = e.touches[0].clientX;
			this.startY = e.touches[0].clientY;
		},
		handleTouchMove(e, idx) {
			// 获得当前坐标
			this.followId = idx;
			this.currentX = e.touches[0].clientX;
			this.currentY = e.touches[0].clientY;
			const x = this.startX - this.currentX; //横向移动距离
			const y = Math.abs(this.startY - this.currentY); //纵向移动距离，若向左移动有点倾斜也可以接受
			// #ifndef H5
			if (x > 5) {
				//向左滑显示
				e.preventDefault();
				this.is_show_btn = true;
			} else if (x < 5) {
				//向右滑隐藏
				this.is_show_btn = false;
			}
			if (y > 150) {
				this.is_show_btn = false;
			}
			// #endif

			// #ifdef H5
			if (x > 5 && this.startX > 30) {
				//向左滑显示
				e.preventDefault();
				this.is_show_btn = true;
			} else if (x < 5) {
				//向右滑隐藏
				this.is_show_btn = false;
			}
			if (y > 150) {
				this.is_show_btn = false;
			}
			// #endif
		}
	}
};
</script>

<style lang="scss">
page {
	width: 750rpx;
	margin: 0 auto;
	background-color: #f5f5f5;
}

.container {
	background: $bg-color-split;
}

.container_FF {
	background: #ffffff;
}

.empty_part {
	display: flex;
	flex: 1;
	width: 100%;
	height: 586rpx;
	background: #fff;
	border-top: 20rpx solid $bg-color-split;

	.img {
		width: 380rpx;
		height: 280rpx;
		margin-bottom: 37rpx;
		margin-top: 88rpx;
	}

	.tip_con {
		color: $main-third-color;
		font-size: 26rpx;
	}

	.ope_btn {
		color: var(--color_main);
		font-size: 28rpx;
		padding: 0 25rpx;
		height: 54rpx;
		background: var(--color_halo);
		border-radius: 27rpx;
		margin-top: 20rpx;
	}
}

.empty_part_FF {
	height: 100vh;
	padding-top: 200rpx;
}

.goods_list {
	border-top: 20rpx solid $bg-color-split;
	width: 750rpx;
	overflow-x: hidden;
}

/* 分享弹框 start */
.share_model {
	width: 750rpx;
	height: 100%;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	margin: 0 auto;
	background: rgba(0, 0, 0, 0.6);
	z-index: 100;
}

.share_model_list {
	display: flex;
	justify-content: space-around;
	padding: 0 50rpx;
	box-sizing: border-box;
	position: fixed;
	bottom: 150rpx;
	z-index: 110;
	width: 750rpx;

	.share_model_pre {
		display: flex;
		flex-direction: column;
		align-items: center;
		background: transparent;
		border-radius: 0;
		height: auto;
		line-height: auto;

		&::after {
			border-width: 0;
		}

		image {
			width: 105rpx;
			height: 105rpx;
		}

		text {
			font-size: 24rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ffffff;
			line-height: 36rpx;
			margin-top: 30rpx;
		}
	}
}

.share_model_close {
	width: 46rpx;
	height: 46rpx;
	bottom: 60rpx;
	position: fixed;
	z-index: 110;
	left: 0;
	right: 0;
	margin: 0 auto;

	image {
		width: 46rpx;
		height: 46rpx;
	}
}

button {
	padding: 0;
	margin: 0;
}

/* 分享弹框 end */
</style>
