<template>
    <view class="page" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }">
        <ktabbar :bgImg="imgUrl + 'default_bg.jpg'" :title="'关于立马'" />
        <view class="about_us_img_wrap">
            <image :src="site_logo" mode="aspectFit" class="about_us_img"></image>
            <text class="app_version">v{{ app_version }}</text>
        </view>
        <view class="container">
            <view class="list_cell">
                <text class="cell_tit">{{ $L('检查更新') }}</text>
                <text class="cell_more iconfont iconziyuan11"></text>
            </view>
        </view>
    </view>
</template>

<script>
import ktabbar from '@/components/ktabbar.vue';
export default {
    components: {
        ktabbar
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            app_version: '1.0.0', //应用版本号
            site_logo: '' //网站logo
        };
    },
    onLoad() {
        this.getSettingInfo();
    },
    methods: {
        // 获取设置项
        getSettingInfo() {
            let param = {};
            param.url = 'v3/system/front/setting/getSettings';
            param.method = 'GET';
            param.data = {
                names: 'basic_site_name,main_site_logo'
            };
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    this.site_name = res.data[0];
                    this.site_logo = res.data[1];
                }
            });
        }
        //检查更新
    }
};
</script>

<style lang="scss">
.page {
    width: 750rpx;
    min-height: 100vh;
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    // 背景图片固定，不滚动
    background-attachment: fixed;
    .about_us_img_wrap {
        margin-top: 80rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        row-gap: 30rpx;
        .about_us_img {
            width: 180rpx;
            height: 180rpx;
            object-fit: contain;
        }
        .app_version {
            font-size: 24rpx;
            color: rgba(0, 0, 0, 0.5);
        }
    }

    .container {
        width: 92%;
        margin: 0 auto;
        margin-top: 50rpx;
        background: #fff;
        border-radius: 40rpx;
        padding: 40rpx 0rpx;
        display: flex;
        flex-direction: column;
        row-gap: 20rpx;

        .list_cell {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 40rpx;
            line-height: 90rpx;
            height: 90rpx;
            .cell_tit {
                font-size: 28rpx;
                color: #000;
                font-weight: 500;
            }
            .cell_more {
                color: $main-third-color;
                font-size: 18rpx;
                margin-left: 10rpx;
            }
            .cell_value {
                font-size: 28rpx;
                color: #666;

                &.call-link {
                    color: #007aff;
                    position: relative;
                    padding: 6rpx 16rpx;
                }
            }
        }
    }
}
</style>
