<template>
    <view class="page" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }">
        <ktabbar :bgImg="imgUrl + 'default_bg.jpg'" :title="'设置'" />
        <view class="list_card">
            <view class="list_cell" @click="navTo('/pages/user/info')" hover-class="cell_hover" :hover-stay-time="50">
                <text class="cell_tit">{{ $L('个人信息') }}</text>
                <text class="cell_more iconfont iconziyuan11"></text>
            </view>
            <view class="list_cell" @click="navTo('/newPages/notice/receivingSet')" hover-class="cell_hover"
                :hover-stay-time="50">
                <text class="cell_tit">{{ $L('推送设置') }}</text>
                <text class="cell_more iconfont iconziyuan11"></text>
            </view>
            <view class="list_cell_col">
                <view class="flex_row_between_center">
                    <text class="cell_tit">{{ $L('里程') }}</text>
                    <uv-switch v-model="licheng" size="22" :inactive-value="0" :active-value="1"
                        inactive-color="#B4B4B4" active-color="#c70e2d" @change="handleChange"></uv-switch>
                </view>
                <text class="cell_tips">开启后车况页显示剩余里程，关闭显示总里程</text>
            </view>
            <view class="list_cell" @click="navTo('/newPages/address/list')" hover-class="cell_hover"
                :hover-stay-time="50">
                <text class="cell_tit">{{ $L('收货地址') }}</text>
                <text class="cell_more iconfont iconziyuan11"></text>
            </view>
            <view class="list_cell" @click="navTo('/newPages/account/account')" hover-class="cell_hover"
                :hover-stay-time="50">
                <text class="cell_tit">{{ $L('账号安全') }}</text>
                <text class="cell_more iconfont iconziyuan11"></text>
            </view>
            <view class="list_cell" @click="clearCache">
                <text class="cell_tit">{{ $L('清空缓存') }}</text>
                <view>
                    <text class="celll_more_text">{{ fileCace }}MB</text>
                    <text class="cell_more iconfont iconziyuan11"></text>
                </view>
            </view>
            <view class="list_cell" @click="agreement('register_agreement')" hover-class="cell_hover"
                :hover-stay-time="50">
                <text class="cell_tit">{{ $L('用户协议') }}</text>
                <text class="cell_more iconfont iconziyuan11"></text>
            </view>
            <view class="list_cell" @click="agreement('privacy_policy')" hover-class="cell_hover" :hover-stay-time="50">
                <text class="cell_tit">{{ $L('隐私政策') }}</text>
                <text class="cell_more iconfont iconziyuan11"></text>
            </view>
            <view class="list_cell" @click="navTo('/newPages/set/update')" hover-class="cell_hover"
                :hover-stay-time="50">
                <text class="cell_tit">{{ $L('检查更新') }}</text>
                <text class="cell_more iconfont iconziyuan11"></text>
            </view>
            <view class="list_cell" @click="navTo('/newPages/set/about')" hover-class="cell_hover"
                :hover-stay-time="50">
                <text class="cell_tit">{{ $L('关于立马') }}</text>
                <text class="cell_more iconfont iconziyuan11"></text>
            </view>
        </view>
        <view style="width: 100%; height: 169rpx"></view>
        <view class="log_out_btn flex_row_center_center" @click="loginOutDialog(true)">
            {{ $L('退出登录') }}
        </view>
        <uni-popup ref="popup" type="dialog">
            <uni-popup-dialog type="input" :title="$L('提示')" :content="$L('确定要退出登录吗?')" :duration="2000"
                @close="loginOutDialog(false)" @confirm="confirmLoginOut"></uni-popup-dialog>
        </uni-popup>
    </view>
</template>

<script>
import ktabbar from '@/components/ktabbar.vue';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue';
import { mapMutations, mapState } from 'vuex';
export default {
    components: {
        uniPopup,
        ktabbar,
        uniPopupDialog
    },
    data() {
        return {
            fileCace: 0,
            licheng: false, // 是否显示里程
            imgUrl: getApp().globalData.imgUrl,
            fileSizeString: '' //app文件缓存大小
        };
    },
    computed: {
        ...mapState(['hasLogin', 'userCenterData']),
        userInfo: {
            get() {
                return this.$store.state.userInfo;
            },
            set() { }
        }
    },
    onLoad() {
        setTimeout(() => {
            uni.setNavigationBarTitle({
                title: this.$L('设置')
            });
        }, 0);
        this.formatSize();
        //app-4-start
        // #ifdef APP-PLUS
        //获取文件缓存
        // #endif
        //app-4-end
    },
    methods: {
        ...mapMutations(['login', 'logout', 'setUserCenterData', 'userInfo']),
        // 切换里程显示
        handleChange(e) {
            console.log('切换里程显示', e);
            this.licheng = e;
            // 这里可以添加逻辑处理，比如保存到用户设置中
        },
        navTo(url) {
            this.$Router.push(url);
        },
        agreement(type) {
            this.$Router.push({
                path: '/pages/privacyPolicy/privacyPolicy',
                query: {
                    type: type
                }
            });
        },
        //退出登录
        confirmLoginOut() {
            // #ifdef H5
            if (this.$isAppInset()) {
                this.$appLogout();
                return;
            }
            // #endif
            this.$request({
                url: 'v3/frontLogin/oauth/logout',
                method: 'POST',
                data: {
                    refresh_token: this.userInfo.refresh_token
                }
            })
                .then((res) => {
                    this.logout();
                    this.$Router.replace({
                        path: `/pages/public/login`,
                        query: {
                            source: 'loginOut'
                        }
                    });
                })
                .catch((e) => { });
        },
        //退出登录提示
        loginOutDialog(type) {
            if (type) {
                this.$refs.popup.open();
            } else {
                this.$refs.popup.close();
            }
        },
        //获取文件缓存
        async formatSize() {
            // let that = this;
            // console.log('获取缓存');
            if (window.jsBridgeHelper) {
                const info = await window.jsBridgeHelper.sendMessage('getCacheInfo', { type: 'tempfiles' });
                this.fileCace = info?.data?.sizeMB;
            }
            // plus.cache.calculate(function (size) {
            //     let sizeCache = parseInt(size);
            //     if (sizeCache == 0) {
            //         that.fileSizeString = '0B';
            //     } else if (sizeCache < 1024) {
            //         that.fileSizeString = sizeCache + 'B';
            //     } else if (sizeCache < 1048576) {
            //         that.fileSizeString = (sizeCache / 1024).toFixed(2) + 'KB';
            //     } else if (sizeCache < 1073741824) {
            //         that.fileSizeString = (sizeCache / 1048576).toFixed(2) + 'MB';
            //     } else {
            //         that.fileSizeString = (sizeCache / 1073741824).toFixed(2) + 'GB';
            //     }
            // });
        },
        //清除文件缓存
        clearCache() {
            let that = this;
            uni.showModal({
                title: that.$L('提示'),
                content: that.$L('确定清除缓存?'),
                success: async (res) => {
                    if (res.confirm) {
                        const result = await window.jsBridgeHelper.sendMessage('clearCache', {
                            type: 'tempfiles'
                        });
                        if (result.success) {
                            const info = await window.jsBridgeHelper.sendMessage('getCacheInfo', { type: 'tempfiles' });
                            this.fileCace = info?.data?.sizeMB;
                            uni.showToast({
                                title: that.$L('缓存清理成功'),
                                duration: 2000
                            });
                        } else {
                            uni.showToast({
                                title: that.$L('缓存清理失败'),
                                duration: 2000
                            });
                        }
                    } else if (res.cancel) {
                    }
                }
            });
        }
    }
};
</script>

<style lang="scss">
.page {
    width: 750rpx;
    min-height: 100vh;
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    // 背景图片固定，不滚动
    background-attachment: fixed;

    .list_card {
        width: 92%;
        margin: 0 auto;
        margin-top: 50rpx;
        background: #fff;
        border-radius: 40rpx;
        padding: 40rpx 0rpx;
        display: flex;
        flex-direction: column;
        row-gap: 20rpx;
    }

    .list_cell_col {
        display: flex;
        flex-direction: column;
        width: 100%;
        margin: 0 auto;
        padding: 0 40rpx 0 40rpx;
    }

    .list_cell {
        width: 100%;
        margin: 0 auto;
        padding: 0 40rpx;
        display: flex;
        align-items: center;
        line-height: 90rpx;
        height: 90rpx;
        position: relative;
        justify-content: center;

        .cell_more {
            color: $main-third-color;
            font-size: 18rpx;
            margin-left: 10rpx;
        }

        .celll_more_text {
            font-size: $fs-m;
            color: $main-third-color;
        }

        .cell_tit {
            flex: 1;
            font-size: $fs-base;
            color: #000;
            margin-right: 10rpx;
        }
    }

    .cell_hover {
        background: #fafafa;
    }

    .cell_tips {
        flex: 1;
        font-size: $fs-m;
        color: rgba(0, 0, 0, 0.5);
    }

    .size_cache {
        font-size: 28rpx;
        color: var(--color_main);
    }

    .list_cell_col ::v-deep .uni-switch-wrapper .uni-switch-input:before {
        background-color: rgba(180, 180, 180, 1);
    }
}

.log_out_btn {
    position: fixed;
    bottom: 50rpx;
    width: 92%;
    left: 4%;
    height: 80rpx;
    border-radius: 40rpx;
    background-color: #494949;
    font-size: $fs-base;
    color: #fff;
    line-height: 80rpx;
    text-align: center;
}
</style>
