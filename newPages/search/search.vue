<template>
    <view class="main_search" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }">
        <!-- 自定义顶部导航栏 -->
        <!-- #ifdef MP -->
        <view class="fixed_top_status_bar">
            <view class="top_status_bar_seat" :style="{ height: topBar.navBarHeight + 'px' }"></view>
            <view
                class="custom-nav-taber"
                :style="{ height: topBar.navBarHeight + 'px', paddingTop: topBar.offsettop - 5 + 'px', backgroundImage: `url(${imgUrl}default_bg.jpg)` }"
            >
                <view :style="{ height: topBar.menuHeight + 'px', lineHeight: topBar.menuHeight + 'px', width: `calc(100% - ${topBar.menuWidth}px)` }" class="search_tap_bar">
                    <image class="back_icon" :src="imgUrl + 'to_right.png'" mode="" @click="navBack" />
                    <view class="search_wrap" :style="{ borderRadius: topBar.menuHeight / 2 + 'px' }">
                        <image class="search_icon" :src="imgUrl + 'search3.png'" mode="" />
                        <input class="sea_input" type="text" :value="input_val" :placeholder="$L('请输入关键词')" @input="inputChange" @confirm="search" maxlength="50" />
                        <image class="clear_content" v-show="input_val" @click="clearInputVal" :src="imgUrl + 'input_clear.png'" />
                    </view>
                </view>
            </view>
        </view>
        <!-- #endif -->
        <!-- #ifdef H5 -->
        <view class="fixed_top_status_bar">
            <view class="top_status_bar_seat"></view>
            <view class="custom-nav-taber" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }">
                <view class="search_tap_bar">
                    <image class="back_icon" :src="imgUrl + 'to_right.png'" mode="" @click="navBack" />
                    <view class="search_wrap">
                        <image class="search_icon" :src="imgUrl + 'search3.png'" mode="" />
                        <input class="sea_input" type="text" :value="input_val" :placeholder="$L('请输入关键词')" @input="inputChange" @confirm="search" maxlength="50" />
                        <image class="clear_content" v-show="input_val" @click="clearInputVal" :src="imgUrl + 'input_clear.png'" />
                    </view>
                </view>
            </view>
        </view>
        <!-- #endif -->
        <!-- 自定义顶部导航栏 end-->
        <view v-if="!searchStatus">
            <!-- 搜索历史 start -->
            <view v-if="history_val && history_val.length" class="search-item searchBottom">
                <view class="search-title">
                    <text>{{ $L('搜索历史') }}</text>
                </view>
                <view class="flex_row_center_between">
                    <view class="search-con">
                        <block v-for="(item, index) in history_val" :key="index">
                            <view v-if="index < 4" class="item" @click="btnSearch(item)">{{ item }}</view>
                        </block>
                    </view>
                    <view class="del" @click="clearHistory">
                        <image class="icon" :src="imgUrl + 'del_search2.png'" mode="" />
                    </view>
                </view>
            </view>
            <!-- 搜索历史 end -->

            <!-- 热门话题 start -->
            <view class="hort-item" v-if="hotTopic.length">
                <view class="search-title">
                    <text>{{ $L('热门话题') }}</text>
                </view>
                <view class="search-con">
                    <view class="item" @click="goTopicTheme(item)" v-for="(item, index) in hotTopic" :key="index">
                        <view class="index" :class="'index-' + index">{{ index + 1 }}</view>
                        <view class="topic-info">
                            <text class="topic-name">{{ item.themeName }}</text>
                            <text class="topic-video-num">{{ item.videoNum }}次参与</text>
                        </view>
                        <view class="tag" v-if="item.labelName" :style="{ backgroundColor: item.labelColor }">{{ item.labelName }}</view>
                    </view>
                </view>
            </view>
            <!-- 热门搜索 end -->
        </view>
        <!-- 展示搜索内容 -->
        <view class="search_result" v-else>
            <view class="search_content">
                <view class="cate_top_bar">
                    <view class="cate_item" v-for="(item, index) in cate_tab" :key="index" @click="searchTabChange(item.value)">
                        <text class="tab_text" :class="{ active: item.value === searchType }">{{ item.name }}</text>
                    </view>
                </view>
                <!-- 发帖内容 -->
                <view class="video-scroll" v-show="searchType === 'topic' && topic_result.length > 0">
                    <scroll-view scroll-y class="video_scroll_view" @scrolltolower="loadMore('topic')" :style="{ height: `calc(100vh - ${topBar.navBarHeight}px - 90rpx)` }">
                        <view class="video-list" v-show="searchType === 'topic'">
                            <view class="video-item" v-for="(item, index) in topic_result" :key="index">
                                <VideoItem :detail="item.videoSearchVO" @changeFollow="changeFollow" />
                            </view>
                        </view>
                    </scroll-view>
                </view>
                <!-- 用户内容 -->
                <view class="video-scroll" v-show="searchType === 'user' && user_result.length > 0">
                    <scroll-view scroll-y class="video_scroll_view" @scrolltolower="loadMore('user')" :style="{ height: `calc(100vh - ${topBar.navBarHeight}px - 90rpx)` }">
                        <view class="video-list" v-show="searchType === 'user'">
                            <view class="video-item" v-for="(item, index) in user_result" :key="index">
                                <AuthorItem :detail="item.authorSearchVO" />
                            </view>
                        </view>
                    </scroll-view>
                </view>
                <!-- 活动内容 -->
                <view class="video-scroll" v-show="searchType === 'activity' && activity_result.length > 0">
                    <scroll-view scroll-y class="video_scroll_view" @scrolltolower="loadMore('activity')" :style="{ height: `calc(100vh - ${topBar.navBarHeight}px - 90rpx)` }">
                        <view class="video-list" v-show="searchType === 'activity'">
                            <view class="video-item" v-for="(item, index) in activity_result" :key="index">
                                <ActivityItem :detail="item.questionnaireVO" />
                            </view>
                        </view>
                    </scroll-view>
                </view>
            </view>
            <!-- 搜索空 -->
            <view
                class="search_empty flex_column_center_center"
                v-if="
                    (searchType == 'topic' && topic_result.length === 0) ||
                    (searchType == 'user' && user_result.length === 0) ||
                    (searchType == 'activity' && activity_result.length === 0)
                "
            >
                <image :src="imgUrl + 'empty_search.png'" class="empty_icon" mode="scaleToFill" />
                <text class="empty_text">没有找到相关内容，请换一个关键词试试</text>
            </view>
        </view>
    </view>
</template>

<script>
import VideoItem from '@/components/videoItem.vue';
import AuthorItem from '@/components/authorItem.vue';
import ActivityItem from '@/components/activityItem.vue';
export default {
    components: {
        VideoItem,
        AuthorItem,
        ActivityItem
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            topBar: {}, // 顶部导航栏信息
            searchStatus: false, // 搜索状态
            input_val: '', //搜索内容
            history_val: [],
            hotTopic: [],
            user_result: [], // 用户搜索结果
            topic_result: [], // 搜索结果
            activity_result: [], // 活动搜索结果
            cate_tab: [
                { name: '内容', value: 'topic' },
                { name: '用户', value: 'user' },
                { name: '活动', value: 'activity' }
            ],
            topic_current: 0, // 当前页码
            user_current: 0, // 用户页码
            activity_current: 0, // 活动页码
            searchType: 'topic', // 当前搜索类型
            topic_hasMore: true, // 是否有下一页
            user_hasMore: true, // 用户搜索是否有下一页
            activity_hasMore: true // 活动搜索是否有下一页
        };
    },
    onLoad() {
        this.getHotTopicData();
        this.getHistoryList();
        // #ifdef MP
        // 胶囊按钮位置信息
        const systemInfo = uni.getWindowInfo();
        // 导航栏高度 = 状态栏高度 + 44
        const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
        this.navBarHeight = systemInfo.statusBarHeight + 44 + 'px';
        this.menuRight = systemInfo.screenWidth - menuButtonInfo.right + 'px';
        let globalData = {};
        globalData.navBarHeight = systemInfo.statusBarHeight + 44;
        globalData.menuRight = systemInfo.screenWidth - menuButtonInfo.right;
        globalData.menuHeight = menuButtonInfo.height;
        globalData.menuWidth = menuButtonInfo.width;
        globalData.offsettop = menuButtonInfo.top;
        this.topBar = globalData;
        // #endif
    },
    methods: {
        loadMore(type) {
            if (type === 'topic' && this.topic_hasMore) {
                this.topic_current++;
                this.searchAssociation(this.input_val);
            } else if (type === 'user' && this.user_hasMore) {
                this.user_current++;
                this.searchAssociation(this.input_val);
            } else if (type === 'activity' && this.activity_hasMore) {
                this.activity_current++;
                this.searchAssociation(this.input_val);
            }
        },
        // 切换关注状态
        changeFollow(item) {
            const authorId = item.authorId;
            // 更新视频列表中的关注状态
            const newVideoList = this.topic_result.map((video) => {
                video.videoSearchVO.isFollow = video.videoSearchVO.authorId === authorId ? item.isFollow : video.videoSearchVO.isFollow;
                return video;
            });
            this.topic_result = newVideoList;
            // 强制更新组件
            this.$forceUpdate();
        },
        // 返回上一页
        navBack() {
            this.$Router.back(1);
        },
        // 跳转到话题专题页
        goTopicTheme(item) {
            this.$Router.push({ path: '/extra/svideo/svideoRecTopic', query: { theme_id: item.themeId } });
        },
        // 选择搜索类型
        searchTabChange(value) {
            this.searchType = value;
            if (value === 'topic') {
                this.topic_current = 0;
                this.topic_result = [];
                this.topic_hasMore = true;
                this.searchAssociation(this.input_val);
            }
            if (value === 'user') {
                this.user_current = 0;
                this.user_result = [];
                this.user_hasMore = true;
                this.searchAssociation(this.input_val);
            }
            if (value === 'activity') {
                this.activity_current = 0;
                this.activity_result = [];
                this.activity_hasMore = true;
                this.searchAssociation(this.input_val);
            }
        },
        //获取热门话题数据
        getHotTopicData() {
            let param = {
                pageSize: 5
            };
            param.url = 'v3/video/front/video/themeList';
            param.method = 'GET';
            this.$request(param).then((res) => {
                this.loading = false;
                if (res.state == 200) {
                    this.hotTopic = res.data.list || [];
                }
            });
        },
        //获取历史记录
        getHistoryList() {
            let history_data = uni.getStorageSync('community_keyword');
            if (history_data) {
                let his_array = history_data.split('~');
                let last_arr = [];
                for (var i = 0; i < his_array.length; i++) {
                    !this.$checkSpace(his_array[i]) && last_arr.push(his_array[i]);
                }
                this.history_val = last_arr;
            }
        },
        //清除搜索历史
        clearHistory() {
            uni.removeStorageSync('community_keyword');
            this.history_val = [];
        },
        inputChange(e) {
            this.input_val = e.detail.value.trim();
        },
        //点击弹起的键盘按钮时触发
        search() {
            if (this.input_val != '') {
                // 先清空之前的搜索结果
                this.topic_result = [];
                this.user_result = [];
                this.activity_result = [];
                this.btnSearch();
            }
        },
        //搜索事件
        btnSearch(val = '') {
            let { input_val } = this;

            if (val) {
                input_val = val;
                this.input_val = val;
            }

            // const ranges = [
            //   '\ud83c[\udf00-\udfff]', // U+1F300 to U+1F3FF
            //   '\ud83d[\udc00-\ude4f]', // U+1F400 to U+1F64F
            //   '\ud83d[\ude80-\udeff]', // U+1F680 to U+1F6FF
            //   ' ', // Also allow spaces
            // ].join('|');

            // const removeEmoji = str => str.replace(new RegExp(ranges, 'g'), '');
            // input_val = input_val.trim();
            // var patrn = /[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|？：“”、；‘'。、]/g

            // if (patrn.test(input_val)) {
            // 	this.$api.msg('不能输入特殊字符')
            // 	return;
            // }
            if (input_val.length > 0) {
                this.setHistoryData();
                this.searchAssociation(input_val);
            }
        },
        //设置缓存
        setHistoryData() {
            let { history_val, input_val } = this;
            let tmp_data = [...history_val];
            tmp_data.unshift(input_val);
            // 最多取4条，不重复且不为空的数据
            tmp_data = tmp_data.reduce((a, b) => {
                a.length <= 4 && b && a.indexOf(b) == -1 ? a.push(b) : null;
                return a;
            }, []);
            let history_val_str = tmp_data.join('~');
            this.history_val = tmp_data;
            uni.setStorageSync('community_keyword', history_val_str);
        },
        //清空输入内容
        clearInputVal() {
            this.input_val = '';
            this.topic_result = [];
            this.user_result = [];
            this.activity_result = [];
            this.topic_current = 0;
            this.user_current = 0;
            this.activity_current = 0;
            this.topic_hasMore = true;
            this.user_hasMore = true;
            this.activity_hasMore = true;
            this.searchType = 'topic';
            this.searchStatus = false;
        },
        searchAssociation(value) {
            uni.showLoading({
                title: '搜索中',
                mask: true
            });
            this.searchStatus = true;
            this.$request({
                url: 'v3/video/front/content/search/list',
                data: {
                    keyword: value,
                    pageSize: 10,
                    current: this.searchType === 'topic' ? this.topic_current : this.searchType === 'user' ? this.user_current : this.activity_current,
                    type: this.searchType == 'topic' ? 1 : this.searchType == 'user' ? 2 : 3
                }
            }).then((res) => {
                uni.hideLoading();
                if (res.data) {
                    const { pagination, list } = res.data;
                    if (pagination.current == 1) {
                        if (this.searchType === 'user') {
                            this.user_result = list || [];
                        } else if (this.searchType === 'activity') {
                            this.activity_result = list || [];
                        } else {
                            this.topic_result = list || [];
                        }
                    } else {
                        if (this.searchType === 'user') {
                            this.user_result = this.user_result.concat(list || []);
                        } else if (this.searchType === 'activity') {
                            this.activity_result = this.activity_result.concat(list || []);
                        } else {
                            this.topic_result = this.topic_result.concat(list || []);
                        }
                    }
                    if (pagination.current * pagination.pageSize >= pagination.total) {
                        if (this.searchType === 'topic') {
                            this.topic_hasMore = false;
                        } else if (this.searchType === 'user') {
                            this.user_hasMore = false;
                        } else {
                            this.activity_hasMore = false;
                        }
                    } else {
                        if (this.searchType === 'topic') {
                            this.topic_hasMore = true;
                        } else if (this.searchType === 'user') {
                            this.user_hasMore = true;
                        } else {
                            this.activity_hasMore = true;
                        }
                    }
                }
            });
        }
    }
};
</script>

<style lang="scss">
page {
    background-color: #f7f7f7;
    width: 750rpx;
    margin: 0 auto;
}

.video-list {
    padding: 30rpx 0;
    display: flex;
    flex-direction: column;
    row-gap: 20rpx;
    width: 92%;
    margin: 0 auto;
    .video_scroll_view {
        /* #ifdef H5 */
        height: calc(100vh - 270rpx);
        /* #endif */
    }
}
.fixed_top_status_bar {
    .custom-nav-taber {
        position: fixed;
        top: 0;
        // #ifdef MP
        left: 0;
        width: 100%;
        // #endif
        z-index: 10;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        // #ifdef H5
        padding-top: 80rpx;
        box-sizing: content-box;
        left: 50%;
        width: 750rpx;
        height: 100rpx;
        transform: translateX(-50%);
        // #endif
        background-repeat: no-repeat;
        background-position: top center;
        background-size: 100% auto;
    }
    .search_tap_bar {
        display: flex;
        align-items: center;
        width: 100%;
        // #ifdef MP
        padding-left: 30rpx;
        justify-content: flex-start;
        // #endif
        // #ifdef H5
        width: 100%;
        padding: 0 5%;
        height: 100%;
        justify-content: space-between;
        // #endif
    }
    .search_wrap {
        position: relative;
        display: flex;
        align-items: center;
        // #ifdef MP
        width: 400rpx;
        padding: 0 20rpx;
        height: 100%;
        // #endif
        // #ifdef H5
        width: calc(100% - 70rpx);
        height: 80rpx;
        border-radius: 40rpx;
        padding: 0 20rpx;
        // #endif
        border: 1px solid #fff;
        background: rgba(255, 255, 255, 0.6);
        box-sizing: border-box;
    }
    .search_icon {
        /* #ifdef MP-WEIXIN */
        width: 36rpx;
        height: 36rpx;
        /* #endif */
        /* #ifdef H5 */
        width: 40rpx;
        height: 40rpx;
        /* #endif */
        object-fit: contain;
    }
    .sea_input {
        width: calc(100% - 80rpx);
        font-size: 26rpx;
        /* #ifdef H5 */
        font-size: 30rpx;
        /* #endif */
        color: #6e6e6e;
        margin-left: 20rpx;
    }
    .clear_content {
        /* #ifdef MP-WEIXIN */
        width: 45rpx;
        height: 45rpx;
        /* #endif */
        /* #ifdef H5 */
        width: 60rpx;
        height: 60rpx;
        /* #endif */
        position: absolute;
        right: 20rpx;
        top: 50%;
        z-index: 3;
        transform: translateY(-50%);
    }
    .add_icon {
        /* #ifdef MP-WEIXIN */
        width: 40rpx;
        height: 40rpx;
        /* #endif */
        /* #ifdef H5 */
        width: 50rpx;
        height: 50rpx;
        /* #endif */
        object-fit: contain;
    }
    .back_icon {
        object-fit: contain;
        /* #ifdef MP-WEIXIN */
        width: 30rpx;
        height: 30rpx;
        margin-right: 30rpx;
        /* #endif */
        /* #ifdef H5 */
        width: 40rpx;
        height: 40rpx;
        margin-right: 20rpx;
        /* #endif */
    }
    .top_status_bar_seat {
        // #ifdef MP
        width: 100%;
        // #endif
        /* app-2-start */
        // #ifdef H5
        width: 750rpx;
        height: 180rpx;
        // #endif
    }
}
.search_result {
    width: 100%;
    padding-top: 20rpx;
}
.cate_top_bar {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 92%;
    margin: 0 auto;
    height: 70rpx;
    .cate_item {
        flex: 1;
        text-align: center;
        font-size: $fs-base;
        color: #000;
        font-weight: 700;
        .tab_text {
            display: inline-block;
            padding-bottom: 8rpx;
            border-bottom: 2px solid;
            border-color: transparent;
            &.active {
                border-color: $color1;
            }
        }
    }
}
.search_empty {
    width: 100%;
    text-align: center;
    margin-top: 120rpx;
    font-size: 28rpx;
    .empty_icon {
        width: 200rpx;
        height: 200rpx;
        margin-bottom: 50rpx;
    }
    .empty_text {
        color: #595757;
        font-size: $fs-s;
    }
}
.main_search {
    min-height: 100vh;
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    // 背景图片固定，不滚动
    background-attachment: fixed;
}
.hort-item {
    padding: 30rpx 28rpx 0;

    .search-title {
        padding-top: 40rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #2d2d2d;
        font-size: 28rpx;
        font-weight: bold;
    }
    .search-con {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        flex-wrap: wrap;
        margin-top: 30rpx;
        row-gap: 20rpx;
        .item {
            width: 100%;
            padding: 10rpx 18rpx;
            display: flex;
            align-items: flex-start;
            justify-content: flex-start;
            .index {
                font-size: $fs-base;
                width: 40rpx;
                height: 50rpx;
                font-weight: bold;
                text-align: center;
                line-height: 50rpx;
                margin-right: 30rpx;
                &-0,
                &-1,
                &-2 {
                    background: rgba(201, 86, 88, 0.1);
                    color: rgba(201, 86, 88, 1);
                }
                &-3,
                &-4 {
                    background: rgba(227, 233, 255, 1);
                    color: rgba(101, 125, 249, 1);
                }
            }
            .topic-info {
                width: calc(100% - 150rpx);
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
                .topic-name {
                    // 超出...
                    width: 100%;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    line-height: 1.5;
                    font-size: 28rpx;
                    color: #2d2d2d;
                    font-weight: 500;
                    margin-bottom: 10rpx;
                }
                .topic-video-num {
                    color: #999;
                    font-size: 24rpx;
                }
            }
            .tag {
                width: 70rpx;
                padding: 6rpx 0rpx;
                text-align: center;
                font-size: 20rpx;
                color: #fff;
                margin-left: 10rpx;
                background-color: #ff5722;
            }
        }
    }
}
.search-item {
    padding: 30rpx 28rpx 0;

    &.searchBottom {
        padding-bottom: 30rpx;
    }

    .search-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 48rpx;
        color: #2d2d2d;
        font-size: 28rpx;
        font-weight: bold;
    }
    .del {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60rpx;
        height: 60rpx;
        .icon {
            width: 30rpx;
            height: 30rpx;
            object-fit: contain;
        }
    }
    .search-con {
        width: calc(100% - 70rpx);
        display: flex;
        align-items: center;
        flex-wrap: wrap;

        .item {
            min-width: 100rpx;
            text-align: center;
            height: 50rpx;
            padding: 0 18rpx;
            color: #2d2d2d;
            line-height: 50rpx;
            font-size: 24rpx;
            background-color: #f5f5f5;
            border-radius: 25rpx;
            margin-right: 20rpx;
            margin-top: 20rpx;
            max-width: 274rpx;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
        }
    }
}

.search_association {
    position: absolute;
    top: 92rpx;
    width: 750rpx;
    background: #fff;
    padding-left: 20rpx;
    z-index: 999;

    .s_a_item {
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #232326;
        font-size: 26rpx;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        -webkit-box-flex: 1;
        padding-right: 20rpx;
        border-bottom: 1rpx solid #f0f2f5;
        text:last-child {
            color: #aaa;
        }

        text:first-child {
            display: block;
            max-width: 540rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}
</style>
