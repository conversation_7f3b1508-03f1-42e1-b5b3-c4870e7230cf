<template>
  <view class="container" :style="mix_diyStyle">
    <view class="gap"></view>
    <view class="is_need_invoice">{{ $L('是否开具发票') }}</view>
    <view class="select_invoice">
      <view class="not_need_wrap" @click="selectInvoice(false)">
        <text
          :class="{ item_check: true, iconfont: true, iconziyuan33: not_need }"
          v-if="not_need == true"
        ></text>
        <text
          :class="{ iconfont: true, iconziyuan43: !not_need }"
          v-if="not_need == false"
        ></text>
        <text :style="{ color: not_need == true ? '#333' : '#666' }">{{
          $L('不需要')
        }}</text>
      </view>
      <view class="is_need_wrap" @click="selectInvoice(true)">
        <text
          :class="{ iconfont: true, iconziyuan43: !is_need }"
          v-if="is_need == false"
        ></text>
        <text
          :class="{ item_check: true, iconfont: true, iconziyuan33: is_need }"
          v-if="is_need == true"
        ></text>
        <text :style="{ color: not_need == true ? '#666' : '#333' }">{{
          $L('需要')
        }}</text>
      </view>
    </view>
    <view v-if="is_need == true">
      <view class="gap"></view>
      <view class="is_need_invoice">{{ $L('发票内容') }}</view>
      <view class="select_invoice">
        <view class="not_need_wrap" @click="selectInvoiceContent">
          <text
            :class="{
              item_check: true,
              iconfont: true,
              iconziyuan33: goods_detail == true
            }"
            v-if="goods_detail == true"
          ></text>
          <text
            :class="{ iconfont: true, iconziyuan43: !goods_detail }"
            v-if="goods_detail == false"
          ></text>
          <text :style="{ color: goods_detail == true ? '#333' : '#666' }">{{
            $L('商品明细')
          }}</text>
        </view>
        <view class="is_need_wrap" @click="selectInvoiceContent">
          <text
            :class="{ iconfont: true, iconziyuan43: !goods_type }"
            v-if="goods_type == false"
          ></text>
          <text
            :class="{
              item_check: true,
              iconfont: true,
              iconziyuan33: goods_type
            }"
            v-if="goods_type == true"
          ></text>
          <text :style="{ color: goods_type == true ? '#666' : '#333' }">{{
            $L('商品类别')
          }}</text>
        </view>
      </view>
      <view class="invoice_content_tip">
        {{
          goods_detail == true
            ? $L(
                '发票内容将显示商品详情名称与价格信息，发票金额为实际支付金额，不含优惠等扣减金额。'
              )
            : $L(
                '发票内容将显示本单商品所属类别及价格信息，发票金额为实际支付金额，不含优惠等扣减金额。'
              )
        }}
      </view>

      <!-- 有发票记录 -->
      <view class="invoice_history" v-if="invoice_list.length > 0">
        <view class="is_need_invoice">{{ $L('历史记录') }}</view>
        <view class="history_item_wrap">
          <view
            class="history_item"
            v-for="(item, index) in invoice_list"
            :key="index"
            @click="changeInvoice(index)"
            @touchstart="touchStart"
            @touchmove="touchMove($event, item.invoiceId)"
            :style="{
              left: is_show_btn && invoiceId == item.invoiceId ? '-160rpx' : '0'
            }"
          >
            <text
              :class="{
                item_check: true,
                iconfont: true,
                iconziyuan33: index == currentIdx
              }"
              :style="{
                display: index == currentIdx ? 'inline-block' : 'none'
              }"
            ></text>
            <text
              :class="{ iconfont: true, iconziyuan43: index != currentIdx }"
              :style="{
                display: index == currentIdx ? 'none' : 'inline-block'
              }"
            ></text>
            <text>{{
              item.invoiceTitle ? item.invoiceTitle : item.companyName
            }}</text>
            <view
              class="delete_btn hide_del"
              @click="deleteHistory(item.invoiceId, index)"
              v-if="invoiceId == item.invoiceId"
              >{{ $L('删除') }}</view
            >
          </view>
        </view>
        <view class="gap"></view>
      </view>

      <view class="is_need_wrap goods_type" @click="createContent">
        <text
          :class="{ iconfont: true, iconziyuan43: !invoice_content }"
          v-if="invoice_content == false"
        ></text>
        <text
          :class="{
            item_check: true,
            iconfont: true,
            iconziyuan33: invoice_content == true
          }"
          :style="{
            display: invoice_content == true ? 'inline-block' : 'none'
          }"
        ></text>
        <text :style="{ color: invoice_content == true ? '#666' : '#333' }">{{
          $L('新增发票内容')
        }}</text>
      </view>
    </view>

    <view
      class="invoice_content_wrap"
      v-if="invoice_content == true && is_need == true"
      id="createInvoice"
    >
      <view class="invoice_content_title">
        <view
          :class="
            is_personal == true
              ? 'selected_content content_title_item'
              : 'content_title_item'
          "
          @click="invoiceContent(true)"
          >{{ $L('个人') }}</view
        >
        <view
          :class="
            is_personal == false
              ? 'selected_content content_title_item'
              : 'content_title_item'
          "
          @click="invoiceContent(false)"
          >{{ $L('企业') }}</view
        >
      </view>
      <!-- 个人发票 -->
      <view v-if="is_personal == true">
        <view class="invoice_content_item">
          <view class="">{{ $L('发票抬头') }}</view>
          <input
            type="text"
            v-model="invoice_title"
            maxlength="30"
            placeholder-class="invoice_item_right"
            :placeholder="$L('请填写正确的发票抬头')"
            cursor-spacing="10"
            :adjust-position="true"
          />
        </view>
        <view class="invoice_content_item">
          <view class="">{{ $L('收票邮箱') }}</view>
          <input
            type="text"
            v-model="invoice_email"
            placeholder-class="invoice_item_right"
            :placeholder="$L('请输入收票邮箱')"
            @blur="handlePersonBlur('invoice_email')"
            cursor-spacing="10"
            :adjust-position="true"
          />
        </view>
      </view>
      <!-- 企业发票 -->
      <view v-if="is_personal == false">
        <view class="invoice_content_item">
          <view class="">{{ $L('发票类型') }}</view>
          <view class="select_invoice">
            <view
              :class="{
                not_need_wrap: true,
                margin: isVatInvoice,
                noMargin: !isVatInvoice
              }"
              @click="invoiceType(true)"
            >
              <text
                :class="{
                  item_check: true,
                  iconfont: true,
                  iconziyuan33: plain_invoice
                }"
                v-if="plain_invoice == true"
              ></text>
              <text
                :class="{ iconfont: true, iconziyuan43: !plain_invoice }"
                v-if="plain_invoice == false"
              ></text>
              <text
                :style="{ color: plain_invoice == true ? '#333' : '#999' }"
                >{{ $L('普通发票') }}</text
              >
            </view>
            <view
              class="is_need_wrap"
              v-if="isVatInvoice"
              @click="invoiceType(false)"
            >
              <text
                :class="{ iconfont: true, iconziyuan43: !special_invoice }"
                v-if="special_invoice == false"
              ></text>
              <text
                :class="{
                  item_check: true,
                  iconfont: true,
                  iconziyuan33: special_invoice
                }"
                v-if="special_invoice == true"
              ></text>
              <text
                :style="{ color: special_invoice == true ? '#333' : '#999' }"
                >{{ $L('增值税专用发票') }}</text
              >
            </view>
          </view>
        </view>
        <view class="invoice_content_item">
          <view class="">{{ $L('单位名称') }}</view>
          <input
            type="text"
            v-model="company_name"
            maxlength="30"
            placeholder-class="invoice_item_right"
            :placeholder="$L('请输入单位名称')"
            cursor-spacing="10"
            :adjust-position="true"
          />
        </view>
        <view class="invoice_content_item">
          <view class="">{{ $L('税号') }}</view>
          <input
            type="text"
            v-model="duty_paragraph"
            maxlength="20"
            placeholder-class="invoice_item_right"
            :placeholder="$L('请输入纳税人识别号')"
            @blur="checkTaxId"
            cursor-spacing="10"
            :adjust-position="true"
          />
        </view>
        <view v-if="special_invoice == true">
          <view class="invoice_content_item">
            <view class="">{{ $L('注册地址') }}</view>
            <input
              type="text"
              v-model="registered_address"
              maxlength="50"
              placeholder-class="invoice_item_right"
              :placeholder="$L('请输入注册地址')"
              cursor-spacing="10"
              :adjust-position="true"
            />
          </view>
          <view class="invoice_content_item">
            <view class="">{{ $L('注册电话') }}</view>
            <input
              type="number"
              v-model="registered_phone"
              maxlength="11"
              placeholder-class="invoice_item_right"
              :placeholder="$L('请输入注册电话')"
              cursor-spacing="10"
              :adjust-position="true"
            />
          </view>
          <view class="invoice_content_item">
            <view class="">{{ $L('开户银行') }}</view>
            <input
              type="text"
              v-model="deposit_bank"
              maxlength="30"
              placeholder-class="invoice_item_right"
              :placeholder="$L('请输入开户银行')"
              cursor-spacing="10"
              :adjust-position="true"
            />
          </view>
          <view class="invoice_content_item">
            <view class="">{{ $L('银行账户') }}</view>
            <input
              type="number"
              v-model="bank_account"
              maxlength="20"
              placeholder-class="invoice_item_right"
              :placeholder="$L('请输入银行账户')"
              cursor-spacing="10"
              :adjust-position="true"
            />
          </view>
          <view class="invoice_content_item">
            <view class="">{{ $L('收票人') }}</view>
            <input
              type="text"
              v-model="check_taker"
              maxlength="5"
              placeholder-class="invoice_item_right"
              :placeholder="$L('请输入收票人')"
              cursor-spacing="10"
              :adjust-position="true"
            />
          </view>
          <view class="invoice_content_item">
            <view class="">{{ $L('收票人电话') }}</view>
            <input
              type="number"
              v-model="bill_to_phone"
              maxlength="11"
              placeholder-class="invoice_item_right"
              :placeholder="$L('请输入收票人电话')"
              cursor-spacing="10"
              :adjust-position="true"
            />
          </view>
          <view class="invoice_content_item">
            <view class="">{{ $L('收票地址') }}</view>
            <input
              type="text"
              v-model="bill_to_address"
              maxlength="50"
              placeholder-class="invoice_item_right"
              :placeholder="$L('请输入收票地址')"
              cursor-spacing="10"
              :adjust-position="true"
            />
          </view>
        </view>
        <view class="invoice_content_item" v-if="plain_invoice == true">
          <view class="">{{ $L('收票邮箱') }}</view>
          <input
            type="text"
            v-model="bill_to_email"
            placeholder-class="invoice_item_right"
            :placeholder="$L('请输入收票邮箱')"
            @blur="handlePersonBlur('bill_to_email')"
            cursor-spacing="10"
            :adjust-position="true"
            maxlength="50"
          />
        </view>
      </view>
    </view>

    <view
      class="is_need_wrap default_invoice"
      @click="isSetDefault"
      v-if="invoice_content == true && is_need == true"
    >
      <text
        :class="{ iconfont: true, iconziyuan43: !is_default }"
        v-if="is_default == false"
      ></text>
      <text
        :class="{ item_check: true, iconfont: true, iconziyuan33: is_default }"
        v-if="is_default == true"
      ></text>
      <text :style="{ color: invoice_content == true ? '#666' : '#333' }">{{
        $L('设为默认发票')
      }}</text>
    </view>
    <!-- 底部确定按钮 -->
    <view class="confirm_btn_wrap">
      <view class="confirm_btn" @click="invoiceSubmit">{{ $L('确定') }}</view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
export default {
  data() {
    return {
      currentIdx: 0,
      not_need: true, //是否开具发票，暂不需要
      is_need: false, //是否开具发票，需要
      goods_detail: true, //商品明细
      goods_type: false, //商品类别
      invoice_content: false, //新增发票内容
      is_personal: true, //个人还是企业
      is_default: false, //是否设为默认发票
      plain_invoice: true, //普通发票
      special_invoice: false, //增值专用发票
      invoice_title: '', //发票抬头
      invoice_email: '', //收票邮箱
      company_name: '', //单位名称
      duty_paragraph: '', //税号
      registered_address: '', //注册地址
      registered_phone: '', //注册电话
      deposit_bank: '', //开户银行
      bank_account: '', //银行账户
      check_taker: '', //收票人
      bill_to_phone: '', //收票电话
      bill_to_address: '', //收票地址
      bill_to_email: '', //收票邮箱
      invoice_list: [], //发票历史记录
      startX: '',
      startY: '',
      is_show_btn: false, //是否展示删除按钮
      slideClass: false, //样式切换
      invoiceId: '', //发票id
      isVatInvoice: true,
      resHeight: 0
    }
  },
  computed: {
    ...mapState(['userInfo'])
  },
  onLoad(options) {
    setTimeout(()=>{
      uni.setNavigationBarTitle({
        title: this.$L('我的发票')
      })
    },0);
    
    this.isVatInvoice = this.$Route.query.isVatInvoice==1?true:false
    uni.removeStorage({
      key: 'invoice_info'
    })
    uni.removeStorage({
      key: 'is_need_invoice'
    })
    this.getInvoiceHistory()
  },
  methods: {
    touchStart(e) {
      this.startX = e.touches[0].clientX
      this.startY = e.touches[0].clientY
    },
    touchMove(e, invoiceId) {
      // 获得当前坐标
      this.invoiceId = invoiceId
      this.currentX = e.touches[0].clientX
      this.currentY = e.touches[0].clientY
      const x = this.startX - this.currentX //横向移动距离
      const y = Math.abs(this.startY - this.currentY) //纵向移动距离，若向左移动有点倾斜也可以接受
      if (x > 0) {
        //向左滑显示
        this.is_show_btn = true
      } else if (x < 0) {
        //向右滑隐藏
        this.is_show_btn = false
      }
    },
    selectInvoice(prop) {
      if (this.is_need == prop || this.not_need == !prop) {
        return
      }

      this.not_need = this.not_need == true ? false : true
      this.is_need = this.is_need == true ? false : true
    },
    selectInvoiceContent() {
      this.goods_detail = !this.goods_detail
      this.goods_type = !this.goods_type
    },
    createContent() {
      this.current = 100
      this.invoice_content = this.invoice_content == true ? false : true
      this.currentIdx = -1
      setTimeout(() => {
        uni.pageScrollTo({
          scrollTop: 1000
        })
      }, 20)
    },
    // 发票内容选择公司或个人
    invoiceContent(val) {
      this.is_personal = val
      this.bill_to_email = ''
      this.invoice_email = ''
    },
    // 是否设为默认发票
    isSetDefault() {
      this.is_default = this.is_default == true ? false : true
    },
    // 选择发票类型
    invoiceType(bool) {
      if (!this.isVatInvoice) {
        return
      }

      if (this.plain_invoice == bool || this.special_invoice == !bool) {
        return
      }

      this.plain_invoice = this.plain_invoice == true ? false : true
      this.special_invoice = this.special_invoice == true ? false : true
      this.registered_address = ''
      this.registered_phone = ''
      this.deposit_bank = ''
      this.bank_account = ''
      this.check_taker = ''
      this.bill_to_phone = ''
      this.bill_to_address = ''
      this.bill_to_email = ''
    },
    // 检查税号格式是否正确
    checkTaxId() {
      let regExp = /[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}/
      if (regExp.test(this.duty_paragraph) == false) {
        uni.showToast({
          title: this.$L('请填写正确的税号！'),
          icon: 'none'
        })
        this.duty_paragraph = ''
      }
    },

    handlePersonBlur(prop) {
      if (this.is_personal) {
        let flag = this.$checkEmail(this[prop])
        if (!flag) {
          this[prop] = ''
        }
      }
    },

    // 保存发票
    invoiceSubmit() {
      if (!this.invoice_list.length && this.is_need && !this.invoice_content) {
        uni.showToast({
          title: this.$L('请选择发票！'),
          icon: 'none'
        })
        return false
      }
      if (this.is_need) {
        //需要发票
        if (this.invoice_content == true) {
          //无历史记录
          if (this.is_personal == true) {
            //个人
            if (this.invoice_title == '') {
              uni.showToast({
                title: this.$L('请填写发票抬头！'),
                icon: 'none'
              })
              return
            }
            if (this.invoice_email == '') {
              uni.showToast({
                title: this.$L('请填写收票邮箱！'),
                icon: 'none'
              })
              return
            }
          } else {
            //企业
            if (this.company_name == '') {
              uni.showToast({
                title: this.$L('请填写单位名称！'),
                icon: 'none'
              })
              return
            }
            if (this.duty_paragraph == '') {
              uni.showToast({
                title: this.$L('请填写税号！'),
                icon: 'none'
              })
              return
            }
            this.checkTaxId()
            if (this.plain_invoice == true) {
              //普通发票
              if (this.bill_to_email == '') {
                uni.showToast({
                  title: this.$L('请填写收票邮箱！'),
                  icon: 'none'
                })
                return
              }
            } else {
              //专用发票
              if (this.registered_address == '') {
                uni.showToast({
                  title: this.$L('请填写注册地址！'),
                  icon: 'none'
                })
                return
              }
              if (this.registered_phone == '') {
                uni.showToast({
                  title: this.$L('请填写注册电话！'),
                  icon: 'none'
                })
                return
              }

              if (!this.$checkMobile(this.registered_phone)) {
                return false
              }

              if (this.deposit_bank == '') {
                uni.showToast({
                  title: this.$L('请填写开户银行！'),
                  icon: 'none'
                })
                return
              }
              if (this.bank_account == '') {
                uni.showToast({
                  title: this.$L('请填写银行账户！'),
                  icon: 'none'
                })
                return
              }
              if (this.check_taker == '') {
                uni.showToast({
                  title: this.$L('请填写收票人！'),
                  icon: 'none'
                })
                return
              }
              if (this.bill_to_phone == '') {
                uni.showToast({
                  title: this.$L('请填写收票电话！'),
                  icon: 'none'
                })
                return
              }

              if (!this.$checkMobile(this.bill_to_phone)) {
                return false
              }

              if (this.bill_to_address == '') {
                uni.showToast({
                  title: this.$L('请填写收票地址！'),
                  icon: 'none'
                })
                return
              }
            }
          }
          let param = {}
          param.url = 'v3/member/front/memberInvoice/add'
          param.method = 'POST'
          param.data = {}
          param.data.isDefault = this.is_default == true ? 1 : 0 //是否为默认发票
          param.data.invoiceContent = this.goods_detail == true ? 1 : 2
          param.data.invoiceType = this.is_personal == true ? 1 : 2 //发票类型
          param.data.titleType = this.is_personal ? 1 : 2
          if (this.is_personal == true) {
            //个人发票
            param.data.invoiceTitle = this.invoice_title
            param.data.receiverEmail = this.invoice_email
          } else {
            //企业发票
            param.data.invoiceType = this.plain_invoice == true ? 1 : 2
            param.data.invoiceTitle = this.company_name
            param.data.taxCode = this.duty_paragraph
            if (this.plain_invoice == true) {
              //普通发票
              param.data.receiverEmail = this.bill_to_email
            } else {
              //增值税发票
              param.data.registerAddr = this.registered_address
              param.data.registerPhone = this.registered_phone
              param.data.bankName = this.deposit_bank
              param.data.bankAccount = this.bank_account
              param.data.receiverName = this.check_taker
              param.data.receiverMobile = this.bill_to_phone
              param.data.receiverAddress = this.bill_to_address
            }
          }
          this.$request(param).then((res) => {
            if (res.state == 200) {
              this.invoiceId = res.data
              let invoice_info = {}
              if (this.is_personal == true) {
                // 个人
                invoice_info = {
                  invoice_title: this.invoice_title,
                  invoice_content: this.goods_detail == true ? '1' : '2',
                  invoiceId: this.invoiceId,
                  invoiceType: this.plain_invoice == true ? 1 : 2
                }
              } else {
                // 企业
                invoice_info = {
                  company_name: this.company_name,
                  invoice_content: this.goods_detail == true ? '1' : '2',
                  invoiceId: this.invoiceId,
                  invoiceType: this.plain_invoice == true ? 1 : 2
                }
              }
              uni.showToast({
                title: '新增发票成功！'
              })
              uni.setStorageSync('invoice_info', invoice_info)
              setTimeout(() => {
                this.$Router.back(1)
              }, 1500)
            }
          })
        } else {
          //有历史记录
          let invoice_info = {}
          this.invoice_list.map((item) => {
            if (item.is_check == true) {
              if (item.companyName == null) {
                //个人
                invoice_info = {
                  invoice_title: item.invoiceTitle,
                  invoice_content: this.goods_detail ? 1 : 2,
                  invoiceId: item.invoiceId,
                  invoiceType: item.invoiceType
                }
              } else {
                //企业
                invoice_info = {
                  company_name: item.companyName,
                  invoice_content: this.goods_detail ? 1 : 2,
                  invoiceId: this.invoiceId,
                  invoiceType: item.invoiceType
                }
              }
            }
          })
          uni.setStorageSync('invoice_info', invoice_info)
          this.$Router.back(1)
        }
      } else {
        //不需要发票
        uni.setStorageSync('is_need_invoice', this.is_need)
        this.$Router.back(1)
      }
    },
    // 获取历史发票列表
    getInvoiceHistory() {
      let param = {}
      param.url = 'v3/member/front/memberInvoice/list'
      this.$request(param).then((res) => {
        if (this.isVatInvoice) {
          this.invoice_list = res.data.list
        } else {
          this.invoice_list = res.data.list.filter(
            (item) => item.invoiceType != 2
          )
        }
        if (this.invoice_list.length > 0) {
          this.invoice_list.map((item) => {
            item.is_check = false
          })
          let idx = this.invoice_list.findIndex((item) => item.isDefault == 1)
          if (idx >= 0) {
            this.currentIdx = idx
            this.invoice_list[idx].is_check = true
            this.invoiceId = this.invoice_list[idx].invoiceId
          } else {
            this.currentIdx = 0
            this.invoice_list[0].is_check = true
            this.invoiceId = this.invoice_list[0].invoiceId
          }
        }
      })
    },

    changeInvoice(index) {
      this.invoice_content = false
      this.currentIdx = index
      this.invoice_list.map((item) => {
        item.is_check = false
      })
      this.invoice_list[index].is_check = true
    },
    // 删除发票
    deleteHistory(invoiceId, index) {
      let _this = this
      let param = {}
      param.url = 'v3/member/front/memberInvoice/del'
      param.method = 'POST'
      param.data = {
        invoiceIds: invoiceId
      }
      this.$request(param).then((res) => {
        if (res.state == 200) {
          uni.showToast({
            title: _this.$L('删除成功！')
          })
          this.invoice_list.splice(index, 1)
        }
      })
    }
  }
}
</script>

<style lang="scss">
.container {
  overflow-y: scroll;
  padding-bottom: 168rpx;
  width: 750rpx;
  margin: 0 auto;
  box-sizing: border-box;
  overflow-x: hidden;
}

.gap {
  width: 750rpx;
  height: 20rpx;
  background-color: #f8f8f8;
}

.is_need_invoice {
  padding-left: 20rpx;
  height: 100rpx;
  line-height: 100rpx;
  font-size: 30rpx;
  font-weight: 600;
  color: #999;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.select_invoice {
  display: flex;
  height: 100rpx;
  align-items: center;
  padding-left: 20rpx;
  font-size: 28rpx;

  .not_need_wrap {
    margin-right: 163rpx;

    &.margin {
      margin-right: 48rpx;
    }

    &.noMargin {
      margin-right: 0rpx;
    }
  }
}

.iconfont {
  font-size: 32rpx;
  color: #bbbbbb;
  margin-right: 20rpx;

  &.item_check {
    color: var(--color_main) !important;
  }
}

.confirm_btn_wrap {
  width: 750rpx;
  height: 168rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  z-index: 99;

  .confirm_btn {
    width: 668rpx;
    height: 88rpx;
    text-align: center;
    line-height: 88rpx;
    font-size: 36rpx;
    letter-spacing: 4rpx;
    color: #fefefe;
    background: var(--color_main_bg);
    box-shadow: 0px 10px 20px 0px var(--color_halo);
    border-radius: 44px;
  }
}

.invoice_content_tip {
  height: 100rpx;
  font-size: 24rpx;
  color: #999;
  padding: 20rpx;
  box-sizing: border-box;
  background: #f8f8f8;
}

.goods_type {
  height: 100rpx;
  display: flex;
  padding-left: 20rpx;
  box-sizing: border-box;
  align-items: center;
  font-size: 28rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.invoice_content_wrap {
  margin: 20rpx;
  // height:298rpx;
  font-size: 28rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  border-radius: 6rpx;
  box-sizing: border-box;

  .invoice_content_title {
    height: 95rpx;
    display: flex;

    .content_title_item {
      flex: 1;
      color: #666;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .invoice_content_item {
    display: flex;
    height: 101rpx;
    justify-content: space-between;
    align-items: center;
    padding: 0 20rpx;
    color: #333;
    border-top: 1rpx solid rgba(0, 0, 0, 0.1);
    box-sizing: border-box;

    .invoice_item_right {
      color: #999;
      font-size: 26rpx;
      text-align: right;
    }

    input {
      text-align: right;
      background: transparent;
    }
  }
}

.selected_content {
  background: #f8f8f8;
  border: 1px solid var(--color_main) !important;
  color: var(--color_main) !important;
}

.invoice_content_item ::v-deep .uni-input-wrapper ::v-deep .uni-input-input {
  width: 500rpx;
}

/* #ifdef MP */
.invoice_content_item input {
  width: 500rpx;
}

/* #endif */
.default_invoice {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  color: #333;
  font-size: 28rpx;
  padding-right: 20rpx;
}

.history_item {
  height: 100rpx;
  font-size: 32rpx;
  color: #333;
  padding-left: 20rpx;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
  position: relative;
  transition: all 0.3s;

  .delete_btn {
    width: 160rpx;
    height: 100rpx;
    background-color: var(--color_vice);
    color: #fff;
    font-size: 28rpx;
    font-weight: 600;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 0;
    right: -160rpx;
    transition: all 0.3s;
    box-sizing: border-box;
  }
}

.history_item_wrap > view:nth-last-child(1) {
  border-bottom: none;
}

.show_del {
  display: flex;
}

.hide_del {
  display: none;
}
</style>
