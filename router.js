import Vue from 'vue'
import Router from 'uni-simple-router'
import store from './store'
import {
	sldStatEvent,
	updateStatCommonProperty
} from "./utils/stat.js";
import {
	WXBrowserShareThen,
	isAppInset,
	callNativeLogin
} from './utils/common.js'
import {
	isWeiXinBrower
} from './utils/base.js'
//app-1-start
// #ifdef APP
import request from './utils/request.js'
// #endif
//app-1-end
import diyStyle from './diyStyle'
//wx-1-start
// #ifdef MP-WEIXIN
const oldPush = Router.prototype.push
Router.prototype.push = function (arg) {
	if (getCurrentPages() && getCurrentPages().length > 8) {
		Router.prototype.replace.apply(this, [arg])
	} else {
		oldPush.apply(this, [arg])
	}
}
// #endif
//wx-1-end
//#ifdef H5 ||MP-WEIXIN
const oldBack = Router.prototype.back
Router.prototype.back = function (arg) {
	const pages = getCurrentPages()
	const prePage = pages[pages.length - 2]
	if (prePage) {
		oldBack.apply(this, [arg])
	} else {
		Router.prototype.pushTab.apply(this, ['/pages/index/index'])
	}
}
// #endif

Vue.use(Router)
//初始化
const router = new Router({
	encodeURI: false, //默认为true
	routes: ROUTES //路由表
});
const setStyleBefore = diyStyle.getDiyStyleOnce()

//全局路由前置守卫
router.beforeEach((to, from, next) => {
	//在前置守卫里调用，不然进入页面会出现颜色闪动的情况
	setStyleBefore()
	if (to.meta && to.meta.checkLogin) {
		if (!store.state.hasLogin && !uni.getStorageSync('userInfo')) {
			// #ifdef MP-WEIXIN
			let url = to.path;
			const query = to.query;
			uni.setStorageSync('fromurl', {
				url,
				query
			});
			next({
				path: '/pages/public/login'
			})
			// #endif
			// #ifndef MP-WEIXIN
			if (isAppInset()) {
				//如果是app内嵌的h5页面 调用app登录方法
				callNativeLogin()
			}
			// #endif
		} else {
			next();
		}
	} else {
		next();
	}

})
//app-2-start
//#ifdef APP
let timer = '1'
let getNetworkType_time = '';
let netNetworkFlag_time = '';
//#endif
//app-2-end
// 全局路由后置守卫
router.afterEach((to, from) => {
	let url = getApp().globalData.apiUrl.substring(0, getApp().globalData.apiUrl.length - 1);
	//商品详情页、店铺的页面需要单独统计，但是需要把pageUrl和referrerPageUrl先存进去
	let specialPages = [
		'/pages/index/guidePage', //引导页
		'/standard/product/detail', //商品详情页
		'/standard/store/shopHomePage', //店铺首页
		'/standard/store/storeIntroduction', //店铺信息页
		'/standard/store/productSearch', //店铺商品列表页
		'/extra/tshou/goods/detail', //推手商品详情页
	];

	let statPvFlag = true;
	for (let i in specialPages) {
		if (specialPages[i].indexOf(to.path) > -1) {
			statPvFlag = false;
			break;
		}
	}
	if (!statPvFlag) {
		//不需要pv类型的统计
		updateStatCommonProperty({
			pageUrl: url + to.path,
			referrerPageUrl: url + from.path
		});
	} else {
		setTimeout(() => {
			//wx-2-start
			//#ifdef MP-WEIXIN
			if (to.path !== '/pages/index/index') {
				sldStatEvent({
					behaviorType: 'pv',
					pageUrl: url + to.path,
					referrerPageUrl: url + from.path
				});
			}
			//#endif
			//wx-2-end
			//#ifndef MP-WEIXIN
			sldStatEvent({
				behaviorType: 'pv',
				pageUrl: url + to.path,
				referrerPageUrl: url + from.path
			});
			//#endif
		}, 3000)
	}


	if (isWeiXinBrower()) {
		let shareData = {
			title: document.title,
			desc: document.title,
			link: window.location.href,
			imgUrl: '',
		}

		WXBrowserShareThen(1, shareData);
		WXBrowserShareThen(2, shareData);
	}
})
export default router;
// #ifdef H5
// 设置全局导航方法
window.appNavigate = function (navigationData) {
	console.log('收到原生App导航请求:', navigationData);
	const { targetPage, forceRefresh, fromNative, timestamp } = navigationData;
	// 普通导航
	console.log('执行普通导航到:', targetPage);
	router.push({
		path: targetPage
	});
};
// #endif

