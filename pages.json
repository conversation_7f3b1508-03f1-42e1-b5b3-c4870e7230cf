{
	"pages": [
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "立马小程序",
				"enablePullDownRefresh": false,
				"navigationBarTextStyle": "black",
				"navigationStyle": "custom",
				"app-plus": {
					"titleNView": false,
					"bounce": "none",
					"pullToRefresh": {
						"style": "default"
					}
				}
			}
		},
		{
			"path": "pages/vehicle/home",
			"style": {
				"navigationBarTitleText": "车辆",
				"navigationStyle": "custom",
				"app-plus": {
					"titleNView": false,
					"bounce": "none",
					"pullToRefresh": {
						"style": "default"
					}
				}
			}
		},
		{
			"path": "pages/service/home",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTitleText": "服务"
			},
			"meta": {
				"checkLogin": false
			}
		},
		{
			"path": "pages/service/store/storeList",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTitleText": "附近门店"
			},
			"meta": {
				"checkLogin": false
			}
		},
		{
			"path": "pages/index/information",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": true,
				"navigationBarTitleText": "发现",
				"navigationBarTextStyle": "white",
				"app-plus": {
					"pullToRefresh": {
						"style": "default"
					}
				}
			}
		},
		{
			"path": "pages/privacyPolicy/privacyPolicy",
			"style": {
				"navigationBarTitleText": ""
			},
			"meta": {
				"checkLogin": false
			}
		},
		{
			"path": "pages/index/skip_to",
			"style": {}
		},
		{
			"path": "pages/index/skip_in",
			"style": {
				"navigationBarTitleText": "",
				"navigationBarBackgroundColor": "#1584e6",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/public/login",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/public/webView",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/user/user",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTitleText": "我的",
				"enablePullDownRefresh": true,
				"app-plus": {
					"titleNView": false,
					"pullToRefresh": {
						"style": "default"
					}
				}
			}
		},
		{
			"path": "pages/user/myGrowth",
			"style": {
				"navigationBarTitleText": "成长值详情",
				"enablePullDownRefresh": false
			},
			"meta": {
				"checkLogin": false
			}
		},
		{
			"path": "pages/user/activityShare",
			"style": {
				"navigationBarTitleText": "活动分享",
				"navigationBarTextStyle": "white",
				"navigationBarBackgroundColor": "#ff662a",
				"enablePullDownRefresh": false
			},
			"meta": {
				"checkLogin": false
			}
		},
		{
			"path": "pages/user/changeInfo",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/user/info",
			"style": {
				"navigationBarTitleText": ""
			},
			"meta": {
				"checkLogin": false
			}
		},
		{
			"path": "pages/category/category",
			"style": {
				"navigationBarTitleText": "",
				"app-plus": {
					"bounce": "none"
				}
			}
		},
		{
			"path": "pages/search/search",
			"style": {
				"navigationBarTitleText": ""
			},
			"meta": {
				"checkLogin": false
			}
		},
		{
			"path": "pages/user/myIntegral",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTitleText": "我的积分"
			},
			"meta": {
				"checkLogin": false
			}
		},
		{
			"path": "pages/service/maintenance/index",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTitleText": "到店维修"
			},
			"meta": {
				"checkLogin": false
			}
		},
		{
			"path": "pages/service/maintenance/form",
			"style": {
				"navigationBarTitleText": "到店维修"
			},
			"meta": {
				"checkLogin": false
			}
		},
		{
			"path": "pages/service/feedback/index",
			"style": {
				"navigationBarTitleText": "建议与反馈"
			},
			"meta": {
				"checkLogin": false
			}
		},
		{
			"path": "pages/service/feedback/list/index",
			"style": {
				"navigationBarTitleText": "我的建议与反馈"
			},
			"meta": {
				"checkLogin": false
			}
		},
		{
			"path": "pages/service/feedback/detail/index",
			"style": {
				"navigationBarTitleText": "建议与反馈详情"
			},
			"meta": {
				"checkLogin": false
			}
		},
		{
			"path": "pages/user/kefu",
			"style": {
				"navigationBarTitleText": "在线客服"
			},
			"meta": {
				"checkLogin": false
			}
		},
		{
			"path": "pages/service/order/index",
			"style": {
				"navigationBarTitleText": "我的服务",
				"app-plus": {
					"bounce": "none"
				}
			},
			"meta": {
				"checkLogin": false
			}
		},
		{
			"path": "pages/service/order/repairList",
			"style": {
				"navigationBarTitleText": "维修工单"
			},
			"meta": {
				"checkLogin": false
			}
		},
		{
			"path": "pages/service/order/repairDetail",
			"style": {
				"navigationBarTitleText": "维修工单"
			},
			"meta": {
				"checkLogin": false
			}
		},
		{
			"path": "pages/service/order/publishEvaluation",
			"style": {
				"navigationBarTitleText": "工单评价"
			},
			"meta": {
				"checkLogin": false
			}
		},
		{
			"path": "pages/vehicle/webview",
			"style": {
				"navigationBarTitleText": ""
			},
			"meta": {
				"checkLogin": false
			}
		},
		{
			"path": "pages/shop/home/<USER>",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTitleText": "商城",
				"enablePullDownRefresh": true
			},
			"meta": {
				"checkLogin": false
			}
		},
		{
			"path": "pages/shop/home/<USER>",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTitleText": "全部商品",
				"enablePullDownRefresh": true
			},
			"meta": {
				"checkLogin": false
			}
		}
	],
	"subPackages": [
		{
			"root": "extra",
			"pages": [
				{
					"path": "svideo/svideoRecTopic",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "topic/list",
					"style": {
						"navigationBarTitleText": "话题",
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "topic/theme",
					"style": {
						"navigationBarTitleText": "话题",
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "user/my",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "user/authorInfo",
					"style": {
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "user/editAuthorInfo",
					"style": {
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "user/attention",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": true
					}
				},
				{
					"path": "svideo/svideoList",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom",
						"navigationBarTextStyle": "white"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "svideo/svideoPlay",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom",
						"navigationBarTextStyle": "white"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "svideo/svideoSearch",
					"style": {
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "svideo/svideoRelease",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "svideo/svideoComments",
					"style": {
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "graphic/graphicRelease",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "graphic/graphicDetail",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": false
					}
				}
			]
		},
		{
			"root": "order",
			"pages": [
				{
					"path": "list",
					"style": {
						"navigationBarTitleText": "",
						"app-plus": {
							"bounce": "none"
						}
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "detail",
					"style": {
						"navigationBarTitleText": "订单详情",
						"navigationBarBackgroundColor": "#F2F2F2",
						"app-plus": {
							"titleNView": {
								"type": "transparent",
								"backButton": {
									"background": ""
								},
								"autoBackButton": true
							}
						}
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "detailPickup",
					"style": {
						"navigationBarTitleText": "订单详情",
						"navigationBarBackgroundColor": "#F2F2F2",
						"app-plus": {
							"titleNView": {
								"type": "transparent",
								"backButton": {
									"background": ""
								},
								"autoBackButton": true
							}
						}
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "detailTakeaway",
					"style": {
						"navigationBarTitleText": "订单详情",
						"navigationBarBackgroundColor": "#F2F2F2",
						"app-plus": {
							"titleNView": {
								"type": "transparent",
								"backButton": {
									"background": ""
								},
								"autoBackButton": true
							}
						}
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "confirmOrder",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": true
					}
				},
				{
					"path": "pay",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": true
					}
				},
				{
					"path": "tradeSuccess",
					"style": {
						"navigationBarTitleText": "",
						"app-plus": {
							"titleNView": {
								"type": "transparent"
							}
						}
					}
				},
				{
					"path": "publishEvaluation",
					"style": {
						"navigationBarTitleText": "",
						"app-plus": {
							"bounce": "none"
						}
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "lookLogistics",
					"style": {
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "logisticsBill",
					"style": {
						"navigationBarTitleText": "填写物流信息"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "logisticsCompany",
					"style": {
						"navigationBarTitleText": "选择物流公司"
					},
					"meta": {
						"checkLogin": false
					}
				}
			]
		},
		{
			"root": "standard",
			"pages": [
				{
					"path": "point/task",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": "积分任务",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "cards/index",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": "会员卡包",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "cart/cart",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": "购物车",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "point/index/index",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": "积分商城",
						"app-plus": {
							"bounce": "none"
						}
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "point/product/detail",
					"style": {
						"navigationBarTitleText": "详情展示"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "point/search/search",
					"style": {
						"navigationBarTitleText": "商品搜索"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "point/order/list",
					"style": {
						"navigationBarTitleText": "我的积分订单"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "point/order/detail",
					"style": {
						"navigationBarTitleText": "订单详情",
						"app-plus": {
							"titleNView": {
								"type": "transparent"
							}
						}
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "point/order/lookLogistics",
					"style": {
						"navigationBarTitleText": "查看物流"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "point/search/good_list",
					"style": {
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "point/product/confirm_order",
					"style": {
						"navigationBarTitleText": "确认订单"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "point/product/pay",
					"style": {
						"navigationBarTitleText": "收银台"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "presale/index/list",
					"style": {
						"navigationBarTitleText": "",
						"navigationBarTextStyle": "white",
						"navigationStyle": "custom",
						"app-plus": {
							"titleNView": false
						}
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "ladder/index/index",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom",
						"navigationBarTextStyle": "white",
						"app-plus": {
							"titleNView": false
						}
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "pinGroup/index/index",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom",
						"navigationBarTextStyle": "white",
						"app-plus": {
							"titleNView": false
						}
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "store/shopHomePage",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "store/attentionStore",
					"style": {
						"navigationBarTitleText": "关注店铺"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "store/productSearch",
					"style": {
						"navigationBarTitleText": "商品搜索"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "store/storeIntroduction",
					"style": {
						"navigationBarTitleText": "店铺信息"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "chat/list",
					"style": {
						"navigationBarTitleText": "消息中心",
						"navigationStyle": "custom",
						"onReachBottomDistance": 50
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "chat/detail",
					"style": {
						"navigationBarTitleText": "聊天详情"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "product/detail",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": "详情展示"
					}
				},
				{
					"path": "product/video",
					"style": {
						"navigationBarTitleText": "视频播放"
					}
				},
				{
					"path": "product/list",
					"style": {
						"enablePullDownRefresh": true,
						"navigationBarTitleText": "商品列表"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "product/evaluation",
					"style": {
						"navigationBarTitleText": "商品评价"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "lottery/detail",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": "抽奖"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "lottery/lotRec",
					"style": {
						"navigationBarTitleText": "我的中奖记录"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "signIn/signIn",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom",
						"navigationBarTextStyle": "white",
						"app-plus": {
							"titleNView": false
						}
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "coupon/myCoupon",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": "优惠券"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "coupon/couponDetail",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "seckill/seckill",
					"style": {
						"navigationBarTitleText": "秒杀首页",
						"navigationStyle": "custom",
						"app-plus": {
							"bounce": "none"
						}
					}
				},
				{
					"path": "coupon/couponCenter",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": "领券中心"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "refund/applyRefund",
					"style": {
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "refund/refundDetail",
					"style": {
						"navigationBarTitleText": "退款详情"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "memLev/index",
					"style": {
						"navigationBarTitleText": "会员等级",
						"navigationStyle": "custom",
						"app-plus": {
							"titleNView": false
						}
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "memLev/rule",
					"style": {
						"navigationBarTitleText": "等级规则"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "memLev/equity",
					"style": {
						"navigationBarTitleText": "全部权益"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "refund/selectService",
					"style": {
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "refund/batchSel",
					"style": {
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "refund/progressDetail",
					"style": {
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "refund/returnAndRefundList",
					"style": {
						"enablePullDownRefresh": true,
						"navigationBarTitleText": "",
						"app-plus": {
							"bounce": "none"
						}
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "super/index",
					"style": {
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "super/history",
					"style": {
						"navigationBarTitleText": "我的会员"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "super/pay",
					"style": {
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "super/superPay",
					"style": {
						"navigationBarTitleText": "收银台"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "super/policy",
					"style": {
						"navigationBarTitleText": "付费会员用户协议"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "super/tradeSuccess",
					"style": {
						"navigationBarTitleText": "支付成功",
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": false
					}
				}
			]
		},
		{
			"root": "newPages",
			"pages": [
				{
					"path": "vehicle/myVehicle",
					"style": {
						"navigationBarTitleText": "我的车辆",
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "vehicle/vehicleDetail",
					"style": {
						"navigationBarTitleText": "车辆详情",
						"app-plus": {
							"bounce": "none"
						}
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "vehicle/bindVehicle",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": "车辆绑定"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "vehicle/bindVehicleForm",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": "填写车辆信息"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "vehicle/activationVehicle",
					"style": {
						"navigationBarTitleText": "激活车辆"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "vehicle/activationVehicleList",
					"style": {
						"navigationBarTitleText": "三包查询"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "invoice/myInvoice",
					"style": {
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "member/history",
					"style": {
						"navigationBarTitleText": "",
						"onReachBottomDistance": 150
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "member/collect",
					"style": {
						"navigationBarTitleText": "",
						"onReachBottomDistance": 150
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "account/account",
					"style": {
						"navigationBarTitleText": "账号安全"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "account/changeMobile",
					"style": {
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "account/bindEmail",
					"style": {
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "account/changeEmail",
					"style": {
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "account/managePwd",
					"style": {
						"navigationBarTitleText": "",
						"app-plus": {
							"softinputmode": "adjustPan"
						}
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "account/changePwd",
					"style": {
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "address/list",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "address/operate",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "notice/noticeCenter",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "notice/notice",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": "",
						"enablePullDownRefresh": true
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "notice/receivingSet",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "set/set",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": ""
					},
					"meta": {
						"checkLogin": true
					}
				},
				{
					"path": "set/about",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "set/update",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "set/privacyMg",
					"style": {
						"navigationBarTitleText": "隐私管理"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "search/search",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "doc/selfInspection",
					"style": {
						"navigationBarTitleText": "自检手册",
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "doc/faq",
					"style": {
						"navigationBarTitleText": "常见问题",
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "doc/detail",
					"style": {
						"navigationBarTitleText": "问题详情",
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "doc/cateList",
					"style": {
						"navigationBarTitleText": "手册分类",
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "doc/tutorials",
					"style": {
						"navigationBarTitleText": "新手教程",
						"enablePullDownRefresh": true
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "doc/tutorialsDettail",
					"style": {
						"navigationBarTitleText": "教程详情"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "activity/list",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": "活动列表",
						"enablePullDownRefresh": true
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "activity/signup",
					"style": {
						"navigationBarTitleText": "参加活动"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "activity/detail",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": "活动详情",
						"enablePullDownRefresh": false
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "activity/qform",
					"style": {
						"navigationBarTitleText": "问卷调查",
						"enablePullDownRefresh": false
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "chooseAddress/chooseAddress",
					"style": {
						"navigationBarTitleText": "选择所在地",
						"enablePullDownRefresh": false
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "chooseAddress/manualSelection",
					"style": {
						"navigationBarTitleText": "选择城市",
						"enablePullDownRefresh": false
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "chooseAddress/chooseDetailAddress",
					"style": {
						"navigationBarTitleText": "选择详细地址",
						"enablePullDownRefresh": false
					},
					"meta": {
						"checkLogin": false
					}
				}
			]
		},
		{
			"root": "merchants",
			"pages": [
				{
					"path": "pages/index/scanOff",
					"style": {
						"navigationBarTitleText": "扫码核销"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "pages/index/inputOff",
					"style": {
						"navigationBarTitleText": "输入核销"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "pages/index/set",
					"style": {
						"navigationBarTitleText": "设置"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "pages/index/index",
					"style": {
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "pages/goods/goods",
					"style": {
						"navigationBarTitleText": "商品管理",
						"enablePullDownRefresh": true,
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "pages/goods/goodsRelease",
					"style": {
						"navigationBarTitleText": "发布商品"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "pages/goods/choose_type",
					"style": {
						"navigationBarTitleText": "选择商品分类"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "pages/goods/reservedInfo",
					"style": {
						"navigationBarTitleText": "设置预留信息"
					}
				},
				{
					"path": "pages/goods/goodsSpec",
					"style": {
						"navigationBarTitleText": "商品规格"
					}
				},
				{
					"path": "pages/order/order",
					"style": {
						"navigationBarTitleText": "订单管理",
						"navigationStyle": "custom",
						"enablePullDownRefresh": true
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "pages/order/order_detail",
					"style": {
						"navigationBarTitleText": "订单详情"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "pages/order/order_delivery",
					"style": {
						"navigationBarTitleText": "发货"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "pages/chat/list",
					"style": {
						"navigationBarTitleText": "消息",
						"navigationStyle": "custom"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "pages/chat/detail",
					"style": {
						"navigationBarTitleText": "聊天详情"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "pages/shopaddress/address",
					"style": {
						"navigationBarTitleText": "发货地址",
						"app-plus": {
							"bounce": "none", //关闭窗口回弹效果
							"titleNView": {
								"buttons": [
									//原生标题栏按钮配置,
									{
										"type": "none",
										"text": "+",
										"float": "right"
									}
								]
							}
						}
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "pages/shopaddress/add",
					"style": {
						"navigationBarTitleText": "新增发货地址"
					},
					"meta": {
						"checkLogin": false
					}
				},
				{
					"path": "pages/transform/transform",
					"style": {
						"navigationBarTitleText": "选择物流公司",
						"app-plus": {
							"bounce": "none", //关闭窗口回弹效果
							"titleNView": {
								"buttons": [
									//原生标题栏按钮配置,
									{
										"type": "none",
										"text": "保存",
										"fontSize": "15",
										"float": "right"
									}
								]
							}
						}
					},
					"meta": {
						"checkLogin": false
					}
				}
			]
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "立马微信小程序",
		"navigationBarBackgroundColor": "#FFFFFF",
		"backgroundColor": "#f8f8f8",
		"backgroundColorBottom": "#fff",
		"backgroundColorTop": "#fff",
		"pageOrientation": "portrait",
		//#ifdef H5
		"rpxCalcMaxDeviceWidth": 640, // rpx 计算所支持的最大设备宽度，单位 px，默认值为 960
		"rpxCalcBaseDeviceWidth": 375, // rpx 计算使用的基准设备宽度，设备实际宽度超出 rpx 计算所支持的最大设备宽度时将按基准宽度计算，单位 px，默认值为 375
		"rpxCalcIncludeWidth": 749, // rpx 计算特殊处理的值，始终按实际的设备宽度计算，单位 rpx，默认值为 750
		"scrollIndicator": "none",
		//#endif
		"pageOrientation": "portrait",
		"app-plus": {
			"scrollIndicator": "none" //全局 在APP页面都不显示滚动条
		}
	},
	"lazyCodeLoading": "requiredComponents",
	"tabBar": {
		"custom": true,
		"color": "#6E6E6E",
		"selectedColor": "#d10d11",
		// #ifndef H5
		"list": [
			{
				"pagePath": "pages/index/index",
				"iconPath": "/static/tab-bar/home.png",
				"selectedIconPath": "/static/tab-bar/home_sel.png",
				"text": "首页"
			},
			{
				"pagePath": "pages/shop/home/<USER>",
				"iconPath": "/static/tab-bar/shop.png",
				"selectedIconPath": "/static/tab-bar/shop_sel.png",
				"text": "商城"
			},
			{
				"pagePath": "pages/vehicle/home",
				"iconPath": "/static/tab-bar/vehicle.png",
				"selectedIconPath": "/static/tab-bar/vehicle_sel.png",
				"text": "车辆"
			},
			{
				"pagePath": "pages/service/home",
				"iconPath": "/static/tab-bar/service.png",
				"selectedIconPath": "/static/tab-bar/service_sel.png",
				"text": "服务"
			},
			{
				"pagePath": "pages/user/user",
				"iconPath": "/static/tab-bar/user.png",
				"selectedIconPath": "/static/tab-bar/user_sel.png",
				"text": "我的"
			}
		],
		//#endif
		"backgroundColor": "#ffffff"
	},
	"usingComponents": {}
}