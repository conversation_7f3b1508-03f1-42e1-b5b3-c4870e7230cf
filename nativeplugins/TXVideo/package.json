{"name": "TXVideo", "id": "TXVideo", "version": "1.0", "description": "腾讯短视频", "_dp_type": "nativeplugin", "_dp_nativeplugin": {"android": {"plugins": [{"type": "module", "name": "TXVideo", "class": "io.dcloud.uniplugin.TXVideo"}], "dependencies": ["com.tencent.liteav:LiteAVSDK_Professional:latest.release", "androidx.constraintlayout:constraintlayout:2.1.3", "com.google.code.gson:gson:2.8.6", "com.tencent.mediacloud:TencentEffect_S1-04:latest.release", "me.dm7.barcodescanner:zxing:1.8.4", "com.qcloud.cos:cos-android-nobeacon:5.8.5", "com.qcloud.cos:quic:1.5.38", "com.squareup.okio:okio:1.14.0", "com.squareup.okhttp3:okhttp:3.11.0", "org.greenrobot:eventbus:3.0.0", "com.github.castorflex.verticalviewpager:library:19.0.1", "de.hdodenhof:circleimageview:3.1.0", "com.github.bumptech.glide:glide:4.12.0", "com.facebook.fresco:fresco:1.13.0", "com.blankj:utilcode:1.30.7"], "integrateType": "aar", "minSdkVersion": 16}, "ios": {"plugins": [{"type": "module", "name": "TestModule", "class": "TestModule"}], "frameworks": ["MapKit.framework"], "embedFrameworks": ["Alamofire.framework"], "integrateType": "framework", "deploymentTarget": "11.0", "embedSwift": true}}}