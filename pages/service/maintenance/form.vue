<template>
    <view class="shopRepair">
        <view class="cu-list">
            <view class="store-name" @click="selectStore">
                <view class="content">
                    <text class="text-black">{{ storeId ? storeInfo.storeName : '请选择门店' }}</text>
                </view>
                <text class="iconfont iconarrow"></text>
            </view>
            <view class="base-info flex_row_between_center">
                <text class="form_label">营业时间：</text>
                <text class="form_value">{{ storeInfo.businessTime }}</text>
            </view>
            <view class="base-info flex_row_between_center">
                <text class="form_label">服务电话：</text>
                <text class="form_value">{{ storeInfo.storeTelephone }}</text>
            </view>
            <view class="base-info flex_row_between_start">
                <text class="form_label">门店地址：</text>
                <text class="form_value">{{ storeInfo.address }}</text>
            </view>
        </view>

        <view class="cu-list">
            <view class="base-info flex_row_between_center">
                <text class="title">服务项目：</text>
                <radio-group @change="serviceTypeChange">
                    <label class="radio" v-for="item in serviceTypeList" :key="item.value">
                        <radio color="#c70e2d" style="transform: scale(0.8)" :value="item.value"
                            :checked="formData.serviceItem == item.value" />
                        {{ $L(item.name) }}
                    </label>
                </radio-group>
            </view>
            <view class="base-info flex_row_between_center">
                <view class="title">
                    姓名：
                    <text class="require">*</text>
                </view>
                <input v-model="formData.memberName" maxlength="11" class="form-input" />
            </view>
            <view class="base-info flex_row_between_center">
                <view class="title">
                    手机号码：
                    <text class="require">*</text>
                </view>
                <input v-model="formData.telephone" maxlength="11" class="form-input" />
            </view>
        </view>

        <!--  车辆信息    -->
        <view class="cu-list">
            <view class="cu-title">
                <text class="text-black">车辆信息</text>
            </view>
            <view class="base-info flex_row_between_center">
                <text class="form_label">品牌：</text>
                <radio-group :rule="{ required: true }" @change="brandChange">
                    <label class="radio" style="margin-right: 30rpx">
                        <radio color="#c70e2d" style="transform: scale(0.8)" :value="1"
                            :checked="formData.vehicleSource == 1" />
                        立马
                    </label>
                    <label class="radio">
                        <radio color="#c70e2d" style="transform: scale(0.8)" :value="0"
                            :checked="formData.vehicleSource == 0" />
                        其他
                    </label>
                </radio-group>
            </view>
            <template v-if="formData.vehicleSource == 1">
                <view class="base-info flex_row_between_center">
                    <view class="form_label">我的车辆：</view>
                    <picker-vehicle v-model:value="formData.vehicleType" class="picker" title="请选择"
                        style="width: calc(100% - 170rpx)" placeholder-font-size="28rpx" :picker-list="vehicleList"
                        :transfer="{ label: 'vehicleType', value: 'vehicleNo' }" :selectValue="formData.vehicleType"
                        textAlign="right" @change="vehicleChange" />
                </view>
                <view class="base-info flex_row_between_center">
                    <view class="title">
                        车架号：
                        <text class="require">*</text>
                    </view>
                    <view class="avatar-wrapper">
                        <input class="form-input" v-model="formData.deviceNo" placeholder="请扫描或输入" />
                        <uv-icon class="avatar" name="scan" size="24" @click="scanCode('vehicleNo', 0)"></uv-icon>
                    </view>
                </view>
                <view class="base-info flex_row_between_center" @click="selectStore('purchaseStore')">
                    <text class="form_label">购买门店：</text>
                    <text class="form_value" :class="{ placeholder: !formData.purchaseStoreName }">{{
                        formData.purchaseStoreName || '请选择' }}</text>
                    <text class="iconfont iconarrow"></text>
                </view>
                <view class="base-info flex_row_between_center">
                    <view class="form_label">购买时间：</view>
                    <dateTimePicker class="picker" title="请选择购买时间" :pickerCon="false"
                        :end="dayjs().format('YYYY-MM-DD')" :value="formData.purchaseTime" style="width: 100%"
                        textAlign="right" placeholder="请选择购买时间" @change="buyTimeChange" />
                </view>
            </template>
            <template v-else>
                <view class="base-info flex_row_between_center">
                    <view class="form_label">
                        所属品牌：
                        <text class="require">*</text>
                    </view>
                    <CuPicker title="其他车辆品牌" v-model="formData.brandName" style="width: 100%" :picker-list="brandList"
                        :transfer="{ label: 'brandName', value: 'id' }" textAlign="right" @change="brandSelect" />
                </view>
            </template>
            <view class="base-info flex_col_start_start">
                <text class="form_label">故障描述：</text>
                <view class="tag_list">
                    <view :class="item.isActive ? 'tag sel' : 'tag'" v-for="(item, index) in tagList"
                        :key="item.classifyCode" @click="tagClick(item)">
                        {{ item.classifyName }}
                    </view>
                </view>
            </view>
            <view class="textarea-wrapper">
                <textarea class="text-area" v-model="formData.issueDescribe" placeholder="输入故障描述" />
            </view>
            <view class="base-info flex_row_between_center">
                <text class="form_label">故障图片:</text>
            </view>
            <view class="pic_part_sel">
                <view class="cover_image" v-for="(item, index) in imageList" :key="index">
                    <image :src="item.url" class="image" mode="aspectFit"></image>
                    <image :src="closeImg" mode="" class="close_img" @click="delUploadImg(index)"></image>
                </view>
                <view class="cover" @tap="uploadCover" v-if="imageList.length < maxUpImageCount">
                    <image class="cover_icon" :src="imgUrl + 'svideo/cover_icon.png'"></image>
                    <text class="cover_tip">{{ imageList.length }}/{{ maxUpImageCount }}</text>
                </view>
            </view>
        </view>
        <view class="save-area"></view>
        <view class="el-submit-btn-view">
            <button class="el-submit-btn" @click="submitSave">提 交</button>
        </view>
    </view>
</template>

<script>
import CuPicker from '@/components/picker.vue';
import pickerVehicle from '@/components/pickerVehicle.vue';
import dateTimePicker from '@/components/dateTimePicker.vue';
// #ifdef H5
import EXIF from '@/utils/exif.js';
// #endif
import { mapState } from 'vuex';
import dayjs from 'dayjs';
export default {
    components: {
        CuPicker,
        pickerVehicle,
        dateTimePicker
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            storeId: '',
            storeInfo: {},
            selectStoreKey: '', // 选择门店的操作key
            brandList: [], // 品牌列表
            tagList: [], // 故障原因列表
            vehicleList: [
                { vehicleType: '轿车', vehicleNo: 'car' },
                { vehicleType: 'SUV', vehicleNo: 'suv' }
            ], // 车辆列表
            serviceTypeList: [
                { name: '维修', value: 1 },
                { name: '保养', value: 2 },
                { name: '洗车', value: 3 },
                { name: '安装', value: 4 }
            ],
            maxUpImageCount: 3, // 最大上传图片数量
            formData: {
                telephone: '', //电话
                memberName: '', // 姓名
                serviceItem: 1, // 服务类型 1=维修, 2=保养, 3=洗车, 4=安装
                state: 0, //1=待处理, 2=已分配, 3=处理中, 4=已完成, 5=用户取消, 6=网点取消, 7=待评价
                serverStoreId: '', // 服务门店ID
                vehicleSource: 1, // 车辆来源 1=立马, 0=其他
                deviceNo: '', // 车架号
                vehicleType: '', // 车辆类型
                brandId: '', // 品牌ID
                brandName: '', // 品牌名称
                purchaseTime: dayjs().format('YYYY-MM-DD'), // 购买时间
                buyStoreName: '', // 购买门店名称
                buyStoreId: '', // 购买门店ID
                appointTime: '', // 预约时间
                arriveTime: '', // 到店时间
                finishTime: '', // 完成时间
                issueDescribe: '', // 故障描述
                issueImg: [] // 故障图片
            },
            closeImg: getApp().globalData.imgUrl + 'order-detail/guanbi.png',
            imageList: [],
            imageSize: [] // 图片宽高
        };
    },
    computed: {
        ...mapState(['hasLogin', 'userInfo'])
    },
    onShow() {
        // #ifdef H5
        // 查看缓存中选中的门店
        const storeId = localStorage.getItem('selectedStoreInfo');
        if (storeId) {
            this.getStoreInfoById(storeId);
            // 清除缓存中的门店
            localStorage.removeItem('selectedStoreId');
        }
        // #endif
    },
    onLoad(options) {
        const { id } = this.$Route.query;
        if (id) {
            this.storeId = id;
            this.getStoreInfoById(id);
        }
        // 获取我的车辆
        this.getVehicleType();
        // 获取品牌列表
        this.getBrandList();
        // 获取故障原因
        this.getbadReason();
    },
    methods: {
        tagClick(item) {
            item.isActive = !item.isActive;
            if (item.isActive) {
                this.formData.issueDescribe += item.classifyName;
            } else {
                this.formData.issueDescribe = this.formData.issueDescribe.replaceAll(item.classifyName, '');
            }
            this.formData.issueDescribeIds = this.tagList.filter((item) => item.isActive).map((item) => item.id);
        },
        /*
         *服务类型变更
         */
        serviceTypeChange(e) {
            this.formData.serviceItem = e.detail.value;
        },
        /*
         * 车辆类型变更
         */
        vehicleChange(e) {
            const brandInfo = e.currentTarget.dataset.brandInfo || {};
            this.formData.vehicleType = e.vehicleType;
            this.formData.vehicleNo = e.vehicleNo;
        },
        /*
         * 选择购买时间
         */
        buyTimeChange(value) {
            this.formData.purchaseTime = value;
        },
        // 提交维修工单
        submitSave() {
            if (!this.storeId) {
                return this.$api.msg('请选择服务门店');
            }
            if (!this.formData.memberName) {
                return this.$api.msg('姓名不能为空');
            }
            if (!this.formData.telephone) {
                return this.$api.msg('手机号不能为空');
            }
            if (!this.formData.deviceNo) {
                return this.$api.msg('车架号不能为空');
            }
            if (!/^1(3|4|5|6|7|8|9)\d{9}$/.test(this.formData.telephone)) {
                return this.$api.msg('手机号码格式不正确');
            }

            // 维修工单提交
            uni.showLoading({
                title: '提交中...',
                mask: true
            });
            const body = {
                ...this.formData,
                issueImg: this.imageList.map((item) => item.url).join(','),
                serverStoreId: this.storeId
            };
            this.$request({
                url: 'v3/vehicle/front/repairApply/add',
                method: 'POST',
                data: body
            })
                .then((res) => {
                    uni.hideLoading();
                    if (res && res.state == 200) {
                        this.$api.msg('提交成功');
                        // 提交成功后，清空表单数据
                        this.formData = {
                            ...body,
                            telephone: '',
                            issueDescribe: '',

                            issueImg: '',
                            memberName: ''
                        };
                        this.imageList = [];
                        // 清除选中的故障原因
                        this.tagList.forEach((item) => {
                            item.isActive = false;
                        });
                        //
                        this.$Route.replace({
                            path: '/pages/service/order/repairList',
                        })
                    } else {
                        this.$api.msg(res.msg || '提交失败');
                    }
                })
                .catch((e) => {
                    uni.hideLoading();
                    this.$api.msg('网络异常，请稍后再试');
                });
        },
        // 全部门店列表选中后
        updateStore(info) {
            this.getStoreInfoById(info.id);
        },
        /*
         * 根据门店ID获取门店信息
         * */
        getStoreInfoById(vehicleStoreId) {
            this.$request({
                url: 'v3/vehicle/front/store/detail',
                method: 'GET',
                data: {
                    vehicleStoreId
                }
            })
                .then((res) => {
                    if (res && res.state == 200) {
                        if (this.selectStoreKey === 'purchaseStore') {
                            this.formData.buyStoreName = res.data.storeName;
                            this.formData.buyStoreId = res.data.id;
                        } else {
                            this.formData.serverStoreId = res.data.id;
                            this.formData.serverStoreId = vehicleStoreId;
                            this.storeId = vehicleStoreId;
                            this.storeInfo = res.data;
                        }
                    }
                })
                .catch((e) => { });
        },
        /*
         * 获取我的车辆
         */
        getVehicleType() {
            this.$request({
                url: 'v3/vehicle/front/vehicle/bindList',
                method: 'GET'
            }).then((res) => {
                uni.hideLoading();
                if (res.state === 200) {
                    const data = res.data || [];
                    if (data.length > 0) {
                        this.formData.vehicleType = data[0].carModel;
                        this.formData.deviceNo = data[0].deviceNo;
                        this.vehicleList = data.map((item) => {
                            return {
                                vehicleType: item.carModel,
                                vehicleNo: item.deviceNo
                            };
                        });
                    }
                }
            });
        },
        /*
         * 品牌变更
         */
        brandChange(e) {
            this.formData.vehicleSource = e.detail.value;
        },
        /*
         * 品牌选择
         */
        brandSelect(e) {
            this.formData.brandId = e.id;
            this.formData.brandName = e.brandName;
        },
        /*
         * 获取品牌
         */
        getBrandList() {
            this.$request({
                url: 'v3/vehicle/front/vehicle/allBrand',
                method: 'GET'
            })
                .then((res) => {
                    if (res && res.state == 200) {
                        this.brandList = res.data;
                    }
                })
                .catch((e) => { });
        },
        /*
         * 获取故障原因
         */
        getbadReason() {
            this.$request({
                url: 'v3/vehicle/front/repairApply/faultDescription',
                method: 'GET'
            })
                .then((res) => {
                    if (res && res.state == 200) {
                        this.tagList = res.data;
                    }
                })
                .catch((e) => { });
        },
        /*
         * 重新选择门店
         */
        selectStore(key) {
            this.selectStoreKey = key || '';
            this.$Router.push({
                path: '/pages/service/store/storeList',
                query: { type: 'select' }
            });
        },
        // 扫码
        scanCode(type) {
            uni.scanCode({
                success: (res) => {
                    if (type === 'vehicleNo') {
                        this.formData.vehicleNo = res.result.slice(28).split('.')[0];
                    }
                }
            });
        },
        // 选图片
        uploadCover() {
            let that = this;
            const count = this.maxUpImageCount - this.imageList.length;
            if (count <= 0) {
                uni.showToast({
                    title: `最多上传${this.maxUpImageCount}张图片！`,
                    icon: 'none'
                });
                return;
            }
            uni.chooseImage({
                count: count,
                sizeType: ['original', 'compressed'],
                //可选择原图或压缩后的图片
                success: (res) => {
                    uni.showLoading();
                    for (let index = 0; index < res.tempFilePaths.length; index++) {
                        uni.uploadFile({
                            url: getApp().globalData.apiUrl + 'v3/oss/front/upload',
                            filePath: res.tempFilePaths[index],
                            name: 'file',
                            formData: {
                                source: 'afterSale'
                            },
                            header: {
                                Authorization: 'Bearer ' + that.userInfo.access_token
                            },
                            success: (uploadFileRes) => {
                                let result = JSON.parse(uploadFileRes.data);
                                if (result.state == 200) {
                                    this.imageList.push({ url: result.data.url, path: result.data.path });
                                    this.imageSize.push({ width: result.data.width, height: result.data.height });
                                    // #ifdef H5
                                    /** setSize方法 处理部分机型像素过高时图片旋转导致的宽高值不正确问题 */
                                    this.setSize(this.imageList.length - 1, res.tempFilePaths[index]);
                                    // #endif
                                }
                            },
                            complete: () => {
                                uni.hideLoading();
                            }
                        });
                    }
                }
            });
        },

        // #ifdef H5
        async setSize(index, url) {
            let Orientation = 0; // 0: 正常无旋转 1: 横屏拍摄 6:竖屏拍摄照片旋转
            //获取图片META信息
            await this.getImageTag(url, 'Orientation', function (e) {
                if (e != undefined) Orientation = e;
            });
            if (Orientation > 1) {
                //竖屏拍照时照片旋转的话则图片的宽高值互换
                let w = this.imageSize[index].width;
                let h = this.imageSize[index].height;
                this.imageSize[index].width = h;
                this.imageSize[index].height = w;
            }
        },
        getImageTag(file, tag, suc) {
            if (!file) return 0;
            return new Promise((resolve, reject) => {
                /* eslint-disable func-names */
                // 箭头函数会修改this，所以这里不能用箭头函数
                let imgObj = new Image();
                imgObj.src = file;
                uni.getImageInfo({
                    src: file,
                    success(res) {
                        EXIF.getData(imgObj, function () {
                            EXIF.getAllTags(this);
                            let or = EXIF.getTag(this, 'Orientation');
                            resolve(suc(or));
                        });
                    }
                });
            });
        },
        // #endif
        // 删除评价图片
        delUploadImg(index) {
            this.imageList.splice(index, 1);
            this.imageSize.splice(index, 1);
        }
    }
};
</script>

<style lang="scss" scoped>
.shopRepair {
    padding-top: 30rpx;
    background: $bg1;
    width: 750rpx;
    box-sizing: border-box;
    min-height: 100vh;

    .placeholder {
        color: rgba(0, 0, 0, 0.5) !important;
    }

    .cu-list {
        width: 94%;
        margin: 0 auto;
        box-sizing: border-box;
        padding: 30rpx 20rpx;
        border-radius: 16rpx;
        background-color: #fff;
        margin-bottom: 20rpx;

        .cu-title {
            font-size: $fs-lg;
            color: #000000;
            font-weight: bold;
            margin-bottom: 30rpx;
        }

        .iconarrow {
            text-align: center;
            font-size: 28rpx;
            color: #757575;
            width: 50rpx;
        }

        .store-name {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20rpx;

            .content {
                width: calc(100% - 40rpx);
                flex: 1 1 calc(100% - 40rpx);

                .text-black {
                    font-size: $fs-lg;
                    font-weight: bold;
                    width: 100%;
                    @extend .line1;
                }
            }
        }

        .base-info {
            margin-bottom: 30rpx;

            .title {
                font-size: $fs-base;
                color: #333;
                width: 200rpx;
            }

            .form_label {
                font-size: $fs-base;
                color: #333;
                width: 200rpx;
            }

            .form_value {
                text-align: right;
                font-size: $fs-base;
                color: #333;
                width: calc(100% - 200rpx);
            }

            .picker {
                width: 100%;

                .picker-value {
                    font-size: $fs-base;
                    color: #000;
                    width: 100%;
                    text-align: right;

                    &.placeholder {
                        color: rgba(0, 0, 0, 0.5);
                    }
                }
            }

            .text-black {
                font-size: $fs-base;
                color: #333;
            }
        }

        .tag_list {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            flex-wrap: wrap;
            gap: 20rpx;
            margin-top: 20rpx;

            .tag {
                padding: 10rpx 15rpx;
                box-sizing: border-box;
                font-size: $fs-m;
                line-height: $fs-m;
                color: #6b6b6b;
                border: 1px solid;
                border-color: #6b6b6b;

                &.sel {
                    background: #c70e2d;
                    color: #fff;
                    border-color: #c70e2d;
                }
            }
        }

        .radio {
            display: inline-flex;
            align-items: center;
            margin-right: 10rpx;
            font-size: 26rpx;
            line-height: 0.8;

            radio::before {
                right: 6rpx;
            }
        }

        .form-input {
            width: calc(100% - 200rpx);
            font-weight: 400;
            font-size: 28rpx;
            color: #000;
            text-align: right;
        }

        .avatar-wrapper {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: calc(100% - 200rpx);

            .form-input {
                width: calc(100% - 32rpx);
            }

            .avatar {
                width: 32rpx;
                height: 32rpx;
                background: #f8f8f8;
            }
        }

        .textarea-wrapper {
            width: 100%;
            height: 200rpx;
            margin-top: 20rpx;
            margin-bottom: 30rpx;

            .text-area {
                width: 100%;
                height: 100%;
                padding: 20rpx;
                font-size: 28rpx;
                color: #333;
                border-radius: 10rpx;
                background-color: #f8f8f8;
                box-sizing: border-box;
            }
        }
    }

    .require {
        color: red;
    }

    .pic_part_sel {
        margin-top: 26rpx;
        display: flex;
        overflow: auto;
        width: 100%;

        .cover {
            width: 125rpx;
            height: 125rpx;
            background: rgba(238, 238, 238, 1);
            border-radius: 15rpx;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            flex-shrink: 0;

            .cover_icon {
                width: 40rpx;
                height: 40rpx;
            }

            .cover_tip {
                font-size: 22rpx;
                color: #949494;
                margin-top: 10rpx;
            }
        }

        .cover_image {
            position: relative;
            margin-right: 20rpx;

            .image {
                width: 125rpx;
                height: 125rpx;
                border-radius: 15rpx;
            }

            .close_img {
                width: 30rpx;
                height: 30rpx;
                position: absolute;
                right: -10rpx;
                top: 0rpx;
                z-index: 99;
            }
        }
    }

    .save-area {
        width: 100%;
        height: 200rpx;
    }

    .el-submit-btn-view {
        display: flex;
        position: fixed;
        // bottom: 100rpx;
        bottom: calc(100rpx + env(safe-area-inset-bottom) / 2);
        z-index: 99;
        // left: 50%;
        justify-content: center;
        align-items: center;
        width: 100%;
        background: #fff;
        box-shadow: 0 -2px 10px 0 rgba(0, 21, 61, 0.08);
        height: 200rpx;
        bottom: 0;

        // transform: translateX(-50%);
        .el-submit-btn {
            border-radius: 40rpx;
            width: 686rpx;
            height: 80rpx;
            background: #c70e2d;
            font-size: 30rpx;
            color: #fff;
        }
    }
}
</style>
