<template>
    <view class="service-home" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }">
        <ktabbar :title="pageTitle" />
        <map class="map" :latitude="moveLocation.latitude" :longitude="moveLocation.longitude" :markers="covers" @markertap="markertap" @regionchange="regionchange"></map>
        <!-- 门店信息 选择门店下单时 展示-->
        <view class="store-section" v-if="actionType == '' || actionType == 'select'">
            <view class="section-header">
                <text class="section-title">为你推荐门店</text>
                <view class="section-more flex_row_center_center" @click="navToStoreList">
                    <text class="t">全部门店</text>
                    <uv-icon top="1" name="arrow-right" size="12" color="#c3c3c3"></uv-icon>
                </view>
            </view>
            <!-- 门店信息 -->
            <view class="store-card" v-if="storeInfo.id">
                <view class="store-info-box">
                    <view class="store-name">{{ storeInfo.storeName }}</view>
                    <view class="store-address">{{ storeInfo.address }}</view>
                    <view class="store-distance">
                        <image :src="imgUrl + 'location2.png'" class="location-logo" mode="widthFix" />
                        <view v-if="storeInfo.distance >= 1000" class="store-distance-bottom">
                            <!-- 距离省略小数位 -->
                            <text v-if="storeInfo.distance">{{ (storeInfo.distance / 1000).toFixed(1) }}km</text>
                        </view>
                        <view v-else class="store-distance-bottom">
                            <!-- 距离省略小数位 -->
                            <text v-if="storeInfo.distance">{{ storeInfo.distance ? storeInfo.distance.toFixed(0) : '--' }}m</text>
                        </view>
                    </view>
                </view>
            </view>
            <!-- 暂无门店 -->
            <view class="empty-store" v-if="storeList.length == 0">
                <text class="t">~{{ $L('暂无门店') }}</text>
            </view>
            <!-- 暂无门店 end-->
            <!-- 立即下单 -->
            <view class="footer-btn" v-if="storeInfo.id">
                <view class="submit-btn" @click="toMaintenanceForm">{{ $L('立即下单') }}</view>
            </view>
        </view>
        <!-- 门店信息 查看门店详情时展示-->
        <view class="store-detail" v-else>
            <!-- 距离 -->
            <view class="store-distance-fix" v-if="formData.distance">
                <!-- 距离省略小数位 -->
                <text v-if="formData.distance && formData.distance >= 1000">{{ (formData.distance / 1000).toFixed(1) }}km</text>
                <text v-else>{{ formData.distance ? formData.distance.toFixed(0) : '--' }}m</text>
            </view>
            <!-- 门店信息 -->
            <view class="store-info-box">
                <!-- 门店名 -->
                <view class="store-title-row">
                    <image :src="imgUrl + 'lm_logo_2025.png'" class="lima-logo" mode="widthFix" />
                    <text class="store-name">{{ formData.storeName }}</text>
                </view>
                <!-- 门店评分 -->
                <view class="store-rate">
                    <view>门店评分 {{ formData.averageStore || 5 }}</view>
                    <image :src="imgUrl + 'star.png'" class="star-icon" mode="widthFix" />
                </view>
                <!-- 门店营业时间 -->
                <view class="store-time flex_row_between_center">
                    <view>营业时间：{{ formData.businessTime || '--' }}</view>
                </view>
                <!-- 门店详细地址 -->
                <view class="store-address">{{ formData.address }}</view>
                <!-- 服务类型 -->
                <view class="storeList-tag-list" v-if="formData.serviceTypes">
                    <view v-for="(type, index) in formData.serviceTypes" :key="type.id" class="storeList-tag">
                        {{ type.name }}
                    </view>
                </view>
                <!-- 底部操作 -->
                <view class="store-contact">
                    <view class="store-btn" @click="navToStore">
                        <image :src="imgUrl + 'location2.png'" class="btn-icon" mode="widthFix" />
                        <text>地图导航</text>
                    </view>
                    <view class="store-btn black" @click="toContact">
                        <image :src="imgUrl + 'tel_white.png'" class="btn-icon tel" mode="widthFix" />
                        <text>联系门店</text>
                    </view>
                </view>
            </view>
        </view>
        <!-- 附近门店 end-->
    </view>
</template>
<script>
import ktabbar from '@/components/ktabbar.vue';
import { customTabbarShow, customTabbarHide } from '@/utils/common';
import { mapState } from 'vuex';
export default {
    components: {
        ktabbar
    },
    data() {
        return {
            actionType: '', // 页面类型 view 门店详情 默认选择门店下单
            pageTitle: this.$L('预约维修'),
            covers: [],
            imgUrl: getApp().globalData.imgUrl,
            locationData: uni.getStorageSync('locationData'),
            storeInfo: {
                storeName: '',
                storeGpsLat: 0,
                storeGpsLng: 0,
                distance: 0,
                storeTelephone: '',
                address: '',
                city: '',
                province: '',
                area: '',
                id: 0
            },
            storeList: [],
            formData: {
                storeName: '',
                storeGpsLat: 0,
                storeGpsLng: 0,
                distance: 0,
                storeTelephone: '',
                address: '',
                city: '',
                province: '',
                area: '',
                repairStoreId: 0,
                countyName: '',
                cityName: '',
                repairStoreCode: '',
                repairStoreName: ''
            },
            params: {
                lat: 0,
                lng: 0,
                distance: 50000,
                storeGpsLat: 0,
                storeGpsLng: 0,
                page: 1,
                pageSize: 10
            },
            // 当前位置信息
            currentLocation: {
                id: 0,
                name: '当前位置',
                iconPath: getApp().globalData.imgUrl + 'location_on.png',
                latitude: 0,
                longitude: 0,
                width: 45,
                height: 45
            },
            // 地图中心点 控制地图显示
            moveLocation: {
                latitude: 0,
                longitude: 0
            },
            // 拖动地图结束时的参数
            moveParams: {
                lat: 0,
                lng: 0,
                addressType: 'DEFAULT',
                current: 1,
                size: 20,
                storeStatus: 'ACTIVE'
            }
        };
    },
    computed: {
        ...mapState(['hasLogin'])
    },
    onLoad() {
        // type view 门店详情
        const { id, type } = this.$Route.query;
        if (type == 'view') {
            this.pageTitle = this.$L('门店详情');
            this.actionType = 'view';
            this.formData.distance = this.$Route.query.distance || ''; // 距离
        }
        if (id) {
            this.getStoreInfoById(id);
        } else {
            if (this.locationData) {
                this.params.lat = this.locationData.latitude;
                this.params.lng = this.locationData.longitude;
                this.currentLocation.latitude = this.locationData.latitude;
                this.currentLocation.longitude = this.locationData.longitude;
                this.params.storeGpsLat = this.locationData.latitude;
                this.params.storeGpsLng = this.locationData.longitude;
                this.getStoreList(this.params);
            } else {
                // 检查授权信息。如已授权直接获取位置数据，否则请求授权
                // #ifdef MP-WEIXIN
                uni.getSetting({
                    success: (res) => {
                        console.log('获取授权设置成功', res);
                        if (res.authSetting['scope.userFuzzyLocation']) {
                            this.getLocationData();
                        } else {
                            this.requestLocationAuthorization();
                        }
                    }
                });
                // #endif
                // #ifdef H5
                this.getLocationData();
                // #endif
            }
        }
    },
    onShow() {
        customTabbarShow(2);
    },
    onHide() {
        customTabbarHide(2);
    },
    methods: {
        // 点击标记点
        markertap(val) {
            if (this.actionType == 'view') return; // 门店详情页面不处理点击事件
            let markerId = null;
            // #ifdef MP-WEIXIN
            markerId = val.markerId;
            // #endif
            // #ifdef H5
            markerId = val.detail.markerId;
            // #endif
            if (markerId == 0) return; // 当前位置标记点不处理
            // 点击门店
            const store = this.storeList.find((item) => item.id == markerId);
            this.covers = this.covers.map((item) => {
                if (item.id == markerId) {
                    item.iconPath = this.imgUrl + 'map_cover_sel.png'; // 点击的门店图标变为选中状态
                } else if (item.id != 0) {
                    item.iconPath = this.imgUrl + 'map_cover.png'; // 其他门店图标恢复默认状态
                }
                return item;
            });
            if (store) {
                this.storeInfo = store;
            }
        },
        // 全部门店列表选中后
        updateStore(info) {
            console.log('全部门店列表选中后', info);
            this.getStoreList(
                {
                    storeId: info.id,
                    storeGpsLat: info.storeGpsLat,
                    storeGpsLng: info.storeGpsLng
                },
                'select'
            );
        },
        /*
         *  维修下单
         */
        toMaintenanceForm() {
            if (!this.hasLogin) {
                getApp().globalData.goLogin(this.$Route);
            } else {
                this.$Router.push({
                    path: '/pages/service/maintenance/form',
                    query: { id: this.storeInfo.id }
                });
            }
        },
        /*
         *拖动地图结束
         */
        regionchange(e) {
            if (this.actionType == 'view') return; // 门店详情页面不处理拖动地图结束
            //在安卓中是 end 事件
            if (e.type === 'end') {
                // 缩放、移动地图后不更新当前位置
                // this.currentLocation.latitude = e.detail.centerLocation.latitude;
                // this.currentLocation.longitude = e.detail.centerLocation.longitude;
                const moveParams = {
                    storeGpsLat: e.detail.centerLocation.latitude,
                    storeGpsLng: e.detail.centerLocation.longitude
                };
                if (this.moveParams.storeGpsLng != 0) {
                    this.getStoreList(moveParams, 'regionchange');
                }
            }
        },
        /*
         * 根据门店ID获取门店信息
         * */
        getStoreInfoById(vehicleStoreId) {
            this.$request({
                url: 'v3/vehicle/front/store/detail',
                method: 'GET',
                data: {
                    vehicleStoreId
                }
            })
                .then((res) => {
                    if (res && res.state == 200) {
                        this.covers = [];
                        this.storeInfo = res.data;
                        this.params.lat = res.data.storeGpsLat;
                        this.params.lng = res.data.storeGpsLng;
                        this.storeList = [res.data];
                        // 获取门店信息，处理成标记点
                        const transition = {
                            ...res.data,
                            id: parseInt(res.data.id),
                            name: res.data.storeName,
                            iconPath: this.imgUrl + 'map_cover_sel.png',
                            latitude: res.data.storeGpsLat,
                            longitude: res.data.storeGpsLng,
                            width: 55,
                            height: 55
                        };
                        this.covers.push(transition);
                        // 当前位置标记点
                        this.moveLocation.latitude = res.data.storeGpsLat;
                        this.moveLocation.longitude = res.data.storeGpsLng;
                        // 店铺名
                        const detail = { ...this.formData, ...res.data };
                        console.log('门店详情', detail);
                        this.formData = detail;
                    }
                })
                .catch((e) => {});
        },
        // 更具位置信息获取门店信息
        getStoreList(values, type) {
            this.$request({
                url: 'v3/vehicle/front/store/list',
                method: 'GET',
                data: {
                    ...values,
                    userLat: this.currentLocation.latitude, // 用户纬度 以第一次定位结果，后续不更新
                    userLng: this.currentLocation.longitude, // 用户经度
                    serverItem: '2' //售后服务网点
                }
            })
                .then((res) => {
                    if (res && res.state == 200) {
                        this.covers = [];
                        const list = res.data.list;
                        this.storeList = list;

                        if (list.length) {
                            // 如果当前还没有选择门店，则默认选择第一个门店
                            if (!this.storeInfo.id) {
                                this.storeInfo = list[0];
                                // 设置地图中心点
                                this.moveLocation.latitude = list[0].storeGpsLat;
                                this.moveLocation.longitude = list[0].storeGpsLng;
                            }
                            //取第0条数据是因为默认按照距离排序 全部门店列表选择后返回
                            if (type == 'select') {
                                const currentStore = list.find((item) => item.id == values.storeId);
                                this.storeInfo = currentStore || list[0];
                                // 设置地图中心点
                                this.moveLocation.latitude = currentStore.storeGpsLat || list[0].storeGpsLat;
                                this.moveLocation.longitude = currentStore.storeGpsLng || list[0].storeGpsLng;
                            }
                            // 获取门店信息，处理成标记点
                            list.forEach((value, index) => {
                                const transition = {
                                    ...value,
                                    id: parseInt(value.id),
                                    name: value.storeName,
                                    iconPath: this.storeInfo.id == value.id ? this.imgUrl + 'map_cover_sel.png' : this.imgUrl + 'map_cover.png',
                                    latitude: value.storeGpsLat,
                                    longitude: value.storeGpsLng,
                                    width: 55,
                                    height: 55
                                };
                                this.covers.push(transition);
                            });
                            this.covers.push(this.currentLocation);
                        } else {
                            this.storeInfo = {};
                        }
                    }
                })
                .catch((e) => {});
        },
        // 请求位置授权
        requestLocationAuthorization() {
            uni.authorize({
                scope: 'scope.userFuzzyLocation',
                success: () => {
                    this.getLocationData();
                },
                fail: (err) => {
                    console.log('获取位置授权失败', err);
                    this.$api.msg('请授权获取位置信息');
                    this.getStoreList(this.params);
                }
            });
        },
        // 获取当前位置
        getLocationData() {
            console.log('获取当前位置');
            // #ifdef MP-WEIXIN
            uni.getFuzzyLocation({
                type: 'gcj02',
                success: (res) => {
                    console.log('获取当前位置成功', res);
                    // 缓存当前用户位置信息
                    uni.setStorageSync('locationData', res);
                    this.params.lat = res.latitude;
                    this.params.lng = res.longitude;
                    this.getStoreList(this.params);
                    // this.addressResolution({
                    // 	location: `${res.longitude},${res.latitude}`
                    // })
                },
                fail: (err) => {
                    console.log('获取当前位置err:', err);
                }
            });
            // #endif
            // #ifdef H5
            uni.getLocation({
                type: 'gcj02',
                success: (res) => {
                    console.log('获取当前位置成功', res);
                    // 缓存当前用户位置信息
                    uni.setStorageSync('locationData', res);
                    this.params.lat = res.latitude;
                    this.params.lng = res.longitude;
                    this.getStoreList(this.params);
                },
                fail: (err) => {
                    console.log('获取当前位置err:', err);
                }
            });
            // #endif
        },
        // 高德逆地址解析
        addressResolution(params) {
            uni.request({
                url: 'https://restapi.amap.com/v3/geocode/regeo',
                data: {
                    key: 'bb684dcb5969944eb5d67a66690457eb',
                    ...params
                },
                method: 'GET',
                header: {
                    'content-type': 'application/json'
                },
                success: (res) => {
                    console.log('高德逆地址解析成功', res);
                    if (res.data.infocode == '10000') {
                        this.pickerAddressVue = `${res.data.regeocode.addressComponent.province}${res.data.regeocode.addressComponent.city}${res.data.regeocode.addressComponent.district}`;
                    } else {
                        this.$dialog.toast('定位失败请选择省市区');
                    }
                },
                fail: (rej) => {
                    console.log('====================================');
                    console.log(rej);
                    console.log('====================================');
                    this.$dialog.toast('定位失败请选择省市区');
                }
            });
        },
        // 去门店列表选择门店
        navToStoreList() {
            this.$Router.push({
                path: '/pages/service/store/storeList',
                query: { type: 'select', serviceType: 2 }
            });
        },
        // 导航到门店
        navToStore() {
            const { storeGpsLat, storeGpsLng, storeName } = this.formData;
            uni.openLocation({
                latitude: Number(storeGpsLat),
                longitude: Number(storeGpsLng),
                name: storeName,
                success: () => {
                    console.log('导航成功');
                },
                fail: (err) => {
                    console.error('导航失败', err);
                    this.$api.msg('导航失败，门店位置不正确或未设置');
                }
            });
        },
        //联系门店
        toContact() {
            if (!this.formData.storeTelephone) {
                this.$api.msg('门店电话未设置');
                return;
            }
            uni.makePhoneCall({
                phoneNumber: this.formData.storeTelephone,
                success: () => {
                    // console.log('拨打电话成功');
                },
                fail: (err) => {
                    // console.error('拨打电话失败', err);
                }
            });
        }
    }
};
</script>
<style lang="scss" scoped>
.service-home {
    background: $bg1;
    min-height: 100vh;
    position: relative;
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    // 背景图片固定，不滚动
    background-attachment: fixed;
    .map {
        width: 100%;
        height: 65vh;
        z-index: 5;
    }
    .store-section {
        width: 98%;
        left: 1%;
        bottom: 0;
        padding: 40rpx 4% 50rpx 4%;
        height: 40vh;
        overflow-y: scroll;
        box-sizing: border-box;
        background-color: #fff;
        position: absolute;
        z-index: 10;
        border-radius: 40rpx 40rpx 0 0;
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24rpx;

            .section-title {
                font-size: 32rpx;
                font-weight: bold;
                color: $main-font-color;
            }

            .section-more {
                padding: 5rpx 0 5rpx 10rpx;

                .t {
                    color: $main-third-color;
                    font-size: $fs-s;
                }

                .icon {
                    margin-top: 5rpx;
                }
            }
        }

        .store-card {
            background: #fff;
            border-radius: 20rpx;
            padding: 24rpx 24rpx 48rpx 24rpx;
            position: relative;
            display: flex;
            flex-direction: column;
            margin-bottom: 32rpx;
        }

        .store-img-box {
            position: relative;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .store-info-box {
            .store-name {
                font-size: $fs-base;
                font-weight: bold;
                color: #000;
            }
            .store-address {
                font-size: 26rpx;
                color: rgba(0, 0, 0, 0.5);
                margin: 10rpx 0;
            }
            .store-distance {
                display: flex;
                align-items: center;
                justify-content: flex-start;
                .location-logo {
                    width: 50rpx;
                    height: 50rpx;
                    margin-right: 8rpx;
                }
                .store-distance-bottom {
                    font-size: 24rpx;
                    color: rgba(0, 0, 0, 0.5);
                }
            }

            .store-time {
                font-size: 24rpx;
                color: $main-third-color;
                margin-bottom: 8rpx;
            }

            .store-tags {
                display: flex;
                flex-wrap: wrap;
                margin: 20rpx 0;

                .tag {
                    font-size: 22rpx;
                    color: $main-color;
                    background: #fff0f0;
                    border-radius: 8rpx;
                    padding: 4rpx 16rpx;
                    margin-right: 12rpx;
                    margin-bottom: 6rpx;
                }
            }
        }

        .store-contact {
            position: absolute;
            right: 24rpx;
            top: 80rpx;
            display: flex;
            flex-direction: column;

            .icon-btn {
                width: 56rpx;
                height: 56rpx;
                background: $bg1;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 32rpx;
                color: $main-color;
                margin-bottom: 12rpx;
                box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
            }
        }

        .footer-btn {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;

            .submit-btn {
                width: 100%;
                height: 80rpx;
                border-radius: 40rpx;
                background-color: #494949;
                font-size: $fs-base;
                color: #fff;
                line-height: 80rpx;
                text-align: center;
            }
        }
    }
    .store-detail {
        width: 98%;
        left: 1%;
        bottom: 0;
        padding: 40rpx 4% 50rpx 4%;
        height: 40vh;
        overflow-y: scroll;
        box-sizing: border-box;
        background-color: #fff;
        position: absolute;
        z-index: 10;
        border-radius: 40rpx 40rpx 0 0;
        .store-distance-fix {
            position: absolute;
            top: 0;
            right: 0;
            height: 40rpx;
            line-height: 40rpx;
            text-align: center;
            padding: 0 40rpx;
            border-radius: 0 0 0 40rpx;
            background-color: rgba(178, 161, 240, 1);
            font-size: 20rpx;
            color: #fff;
        }
        .store-info-box {
            padding-top: 20rpx;
            .store-title-row {
                display: flex;
                align-items: center;
                margin-bottom: 20rpx;

                .lima-logo {
                    width: 60rpx;
                    height: 60rpx;
                    margin-right: 16rpx;
                }
                .store-name {
                    font-size: $fs-base;
                    font-weight: bold;
                    color: #000;
                }
            }
            .store-rate {
                display: flex;
                align-items: center;
                justify-content: flex-start;
                font-size: 24rpx;
                color: rgba(89, 87, 87, 1);
                margin-bottom: 15rpx;
                .star-icon {
                    width: 24rpx;
                    height: 24rpx;
                    margin-left: 8rpx;
                }
            }
            .store-time {
                font-size: 24rpx;
                color: rgba(89, 87, 87, 1);
                margin-bottom: 15rpx;
            }
            .store-address {
                font-size: 26rpx;
                color: rgba(89, 87, 87, 1);
                margin: 10rpx 0;
            }
            .storeList-tag-list {
                display: flex;
                width: 100%;
                margin: 15rpx 0;
                flex-wrap: wrap;
                row-gap: 10rpx;
                .storeList-tag {
                    float: left;
                    margin: 0 8rpx;
                    padding: 0 15rpx;
                    border-radius: 20rpx;
                    height: 40rpx;
                    background: rgba(178, 161, 240, 1);
                    line-height: 40rpx;
                    font-size: 22rpx;
                    color: #fff;
                }
            }
            .store-contact {
                width: 100%;
                display: flex;
                align-items: center;
                justify-content: space-between;
                box-sizing: border-box;
                padding-top: 40rpx;
                margin-top: 40rpx;
                border-top: 0.5px solid rgba(0, 0, 0, 0.1);
                .store-btn {
                    width: 45%;
                    height: 70rpx;
                    border-radius: 35rpx;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    column-gap: 10rpx;
                    font-size: $fs-base;
                    color: rgba(73, 73, 73, 1);
                    border: 1px solid;
                    border-color: rgba(73, 73, 73, 1);
                    &.black {
                        background-color: rgba(73, 73, 73, 1);
                        color: #fff;
                        border-color: rgba(73, 73, 73, 1);
                    }
                }
                .btn-icon {
                    width: 46rpx;
                    height: 46rpx;
                    &.tel {
                        width: 40rpx;
                        height: 40rpx;
                    }
                }
            }
        }
    }
    .empty-store {
        width: 100%;
        margin: 50rpx 0;
        text-align: center;

        .t {
            font-size: $fs-base;
            color: #938a8a;
        }
    }
}
</style>
