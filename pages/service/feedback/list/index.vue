<template>
    <view class="page">
        <view class="container" :class="{ show: !loading }">
            <view class="card-container bb">
                <view class="message-block bb" v-for="(item, b_index) in list" :key="b_index" @tap="navigatorDetail(item)">
                    <view class="message-time">{{ getTimeDifference(item.createTime) }}</view>
                    <view class="message-content">
                        <view class="content-info">
                            <view class="message-type">
                                <view class="type-tag">{{ type_dict[item.type].name }}</view>
                            </view>
                            <view class="info-content">
                                <view class="info-content-text ellipsis">{{ item.content }}</view>
                            </view>
                            <view class="info-status">
                                <text>{{ item.answer ? '已回复' : '已提交' }}</text>
                                <uv-icon name="arrow-right" color="#909399" size="24rpx"></uv-icon>
                            </view>
                        </view>
                    </view>
                </view>
                <!-- <uv-load-more v-if="list.length != 0" :status="status" /> -->
                <!-- <mescroll-empty v-else :option="optEmpty"></mescroll-empty> -->
            </view>
        </view>
        <!-- <uv-loading-page :loading="loading" loading-text="加载中..." font-size="24rpx"></uv-loading-page> -->
    </view>
</template>

<script>
export default {
    data() {
        return {
            staticUrl: getApp().globalData.staticUrl,
            navBarHeight: '',
            startBarHeight: '',
            statusbarActive: false,
            tabberColor: '#fff',
            list: [],
            type_dict: {
                1: {
                    name: '服务'
                },
                2: {
                    name: '产品'
                },
                3: {
                    name: 'APP'
                },
                4: {
                    name: '其他'
                }
            },
            query: {
                pageSize: 20,
                current: 1
            },
            status: 'loading',
            optEmpty: {
                icon: '',
                tip: '暂无诉求记录'
            },
            loading: false
        };
    },
    onLoad() {
        this.getList();
    },
    methods: {
        getList(cb) {
            this.$request({
                url: 'v3/vehicle/front/feedBack/list',
                method: 'get',
                data: this.query
            })
                .then((res) => {
                    uni.hideLoading();
                    if (res && res.state == 200) {
                        const { list, pagination } = res.data;
                        if (pagination.current == 1) {
                            this.list = list;
                        } else {
                            this.list.push(...list);
                        }
                        if (list.length < pagination.pageSize) {
                            this.status = 'nomore';
                        } else {
                            this.status = 'loadmore';
                        }
                    }
                    cb && cb();
                })
                .catch((e) => {});
        },
        // 详情
        navigatorDetail(row) {
            uni.navigateTo({
                url: '/pages/service/feedback/detail/index?id=' + row.id
            });
        },
        //计算时间差
        getTimeDifference(inputTime) {
            if (!inputTime) return '未知时间';
            var currentTime = new Date();
            var inputDate = new Date(inputTime.replaceAll('-', '/'));
            var timeDifference = currentTime - inputDate;
            var minutes = Math.floor(timeDifference / (1000 * 60));
            var hours = Math.floor(timeDifference / (1000 * 60 * 60));
            var days = Math.floor(timeDifference / (1000 * 60 * 60 * 24));

            if (minutes < 60) {
                return minutes + '分钟';
            } else if (hours < 24) {
                return hours + '小时';
            } else {
                return inputTime;
            }
        }
    },
    // 触底加载数据
    onReachBottom() {
        if (this.status === 'loading' || this.status === 'nomore') return;
        this.status = 'loading';
        this.query.current += 1;
        this.getList();
    },
    onPullDownRefresh() {
        this.status == 'loading';
        this.query_list.currentPage = 1;
        this.getList(() => {
            console.log('加载成功回调');
            uni.stopPullDownRefresh();
        });
    }
};
</script>
<style lang="scss">
.page {
    width: 100%;
    background-color: $bg1;
}

.container {
    border-radius: 20rpx 20rpx 0 0;
    overflow: hidden;
    min-height: 100vh;
    opacity: 0;

    &.show {
        opacity: 1;
    }

    .card-container {
        width: 94%;
        margin: 0 auto;
        position: relative;
        padding: 50rpx 0;
        font-size: 27rpx;
        line-height: 1.6;

        .message-type {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10rpx;

            .type-tag {
                font-size: $fs-m;
                height: 50rpx;
                line-height: 50rpx;
                color: #fff;
                background-color: #367fff;
                padding: 0 25rpx;
                border-radius: 25rpx;
            }
        }

        .title-center {
            width: 94%;
            height: 120rpx;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            margin: 0 auto;

            .set-btn {
                display: inline-block;
                margin: 0;
                padding: 0 35rpx;
                font-size: 27rpx;
                color: #fff;
                height: 58rpx;
                line-height: 58rpx;
                border-radius: 29rpx;
                background: linear-gradient(225deg, #367fff 0%, #367fff 0%, #03bfd4 100%);
            }
        }

        .notice-list {
            width: 94%;
            margin: 0 auto;
        }

        .message-block {
            margin-bottom: 19rpx;

            .message-time {
                width: 100%;
                text-align: right;
                font-size: $fs-m;
                color: #000;
                margin-bottom: 15rpx;
            }

            .message-content {
                width: 100%;
                box-sizing: border-box;
                border-radius: 19rpx;
                overflow: hidden;
                background: rgba(255, 255, 255, 1);
                box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.1);
            }

            .content-info {
                padding: 35rpx 29rpx;

                .info-title {
                    justify-content: space-between;
                    font-size: $fs-base;
                    margin-bottom: 10rpx;

                    .status {
                        font-weight: 700;
                    }
                }

                .info-content {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    gap: 19rpx;

                    &-text {
                        width: 100%;
                        font-size: $fs-base;
                        line-height: 1.6;
                        font-weight: 400;
                        color: #808080;
                    }

                    .ellipsis {
                        display: -webkit-box;
                        -webkit-line-clamp: 3;
                        -webkit-box-orient: vertical;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    .status-icon {
                        width: 135rpx;
                        height: 135rpx;
                    }
                }

                .info-status {
                    width: 100%;
                    color: #909399;
                    font-size: 26rpx;
                    text-align: right;
                    margin-top: 20rpx;
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                }
            }

            .action-content {
                padding: 25rpx 30rpx;
                display: flex;
                align-items: center;
                justify-content: center;

                .btn {
                    flex-grow: 1;
                    font-size: 27rpx;
                    color: #fff;
                    height: 96rpx;
                    line-height: 96rpx;
                    border-radius: 48rpx;
                    background: linear-gradient(225deg, #367fff 0%, #367fff 0%, #03bfd4 100%);
                }
            }
        }
    }
}

.set-main {
    .pop-title {
        height: 88rpx;
        line-height: 88rpx;
        text-align: center;
        position: relative;
        background: linear-gradient(90deg, #05bdd6 0%, #3383fc 100%);
        font-size: 31rpx;
        color: #fff;
        font-weight: 500;

        .close-icon {
            width: 54rpx;
            position: absolute;
            top: 56%;
            right: 29rpx;
            transform: translateY(-50%);
        }
    }

    .settings-group {
        padding: 50rpx 33rpx;

        .settings-title {
            line-height: 2;
        }

        .settings-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 19rpx;

            .label {
                width: 70%;
                font-size: 25rpx;
                line-height: 1.5;
                color: #000;
            }
        }
    }
}

.flex-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}

.flex-col {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.custom-nav-taber {
    position: fixed;
    width: 100%;
    z-index: 15;
    top: 0;
    left: 0;
    background-color: rgba(255, 255, 255, 0);
    box-sizing: border-box;

    .uni-navbar__header {
        padding: 0;
    }

    .uni-navbar__header-container {
        flex: unset !important;
    }

    .uni-navbar__header-btns-left {
        width: auto !important;
    }

    .uni-icons {
        font-size: 46rpx !important;
    }

    .uni-nav-bar-text {
        font-size: 35rpx !important;
    }

    &.active {
        animation: bg-fadeIn ease 0.5s both;

        .taber-title {
            color: #000;
        }
    }

    @keyframes bg-fadeIn {
        0% {
            background: rgba(255, 255, 255, 0);
        }

        100% {
            background: rgba(255, 255, 255, 1);
        }
    }
}
</style>
