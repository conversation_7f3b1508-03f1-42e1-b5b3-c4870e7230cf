<template>
<view class="feedback-container">
    <view class="subtitle">
        <text>请选择您要反馈的问题</text>
    </view>
    <view class="type-selection">
        <view v-for="item in typeOptions" :key="item.value" class="type-item"
            :class="{ active: selectedType === item.key }" @tap="selectType(item.key)">
            <text :class="{ 'active-text': selectedType === item.key }">{{ item.label }}</text>
        </view>
    </view>

    <view class="feedback-content">
        <view class="content-header">备注</view>
        <textarea class="content-textarea" placeholder="请填写您的问题或建议" v-model="feedbackContent" maxlength="100"
            @input="updateCharCount" />
        <view class="char-count">{{ charCount }}/100</view>
    </view>

    <view class="upload-section">
        <view class="upload-header">上传图片 ({{ imageList.length }}/{{ maxImageCount }})</view>
        <view class="image-list">
            <view v-for="(image, index) in imageList" :key="index" class="image-item">
                <image :src="image.url" mode="aspectFill" />
                <view class="delete-btn" @tap="deleteImage(index)">×</view>
            </view>
            <view class="upload-btn" @tap="chooseImage" v-if="imageList.length < maxImageCount">
                <text class="plus-icon">+</text>
            </view>
        </view>
    </view>
    <view class="upload-section">
        <view class="upload-header">上传视频</view>
        <view class="image-list">
            <view class="video-item" v-if="videoFile.url">
                <!-- 隐藏播放按钮 -->
                <video class="choose-video" :src="videoFile.url" muted="true" :show-center-play-btn="false" :controls="false" object-fit="contain"/>
                <view class="delete-btn" @tap="deleteVideo(index)">×</view>
            </view>
            <view class="upload-btn" @tap="chooseVideo" v-if="!videoFile.url">
                <text class="plus-icon">+</text>
            </view>
        </view>
    </view>
    <view class="submit-section">
        <view class="submit-btn" @tap="submitFeedback">确定</view>
        <view class="submit-btn plain" @tap="myFeedbackHistory">我的反馈</view>
    </view>

</view>
</template>

<script>
import {
    mapState
} from 'vuex'
export default {
    data() {
        return {
            selectedType: 'service',
            typeOptions: [{
                label: '服务',
                value: '1',
                key: 'service'
            }, {
                label: '产品',
                value: '2',
                key: 'product'
            }, {
                label: 'APP',
                value: '3',
                key: 'app'
            }, {
                label: '其他',
                value: '4',
                key: 'other'
            }],
            feedbackContent: '',
            charCount: 0,
            imageList: [],
            maxImageCount: 3, // 最大图片上传数量
            videoFile: {
                url: '',
                path: null
            }
        }
    },
    computed: {
        ...mapState(['userCenterData', 'hasLogin', 'userInfo']),
    },
    methods: {
        selectType(type) {
            this.selectedType = type
        },
        updateCharCount(e) {
            this.charCount = e.detail.value.length
        },
        chooseImage() {
            if (this.imageList.length >= this.maxImageCount) {
                uni.showToast({
                    title: '最多只能上传' + this.maxImageCount + '张图片',
                    icon: 'none'
                })
                return
            }
            uni.chooseImage({
                count: this.maxImageCount - this.imageList.length,
                sizeType: ['compressed'],
                sourceType: ['album', 'camera'],
                success: (res) => {
                    uni.showLoading();
                    for (let index = 0; index < res.tempFilePaths.length; index++) {
                        uni.uploadFile({
                            url: getApp().globalData.apiUrl + 'v3/oss/front/upload',
                            filePath: res.tempFilePaths[index],
                            name: 'file',
                            formData: {
                                'source': 'afterSale',
                            },
                            header: {
                                Authorization: 'Bearer ' + this.userInfo.access_token
                            },
                            success: (uploadFileRes) => {
                                let result = JSON.parse(uploadFileRes.data);
                                if (result.state == 200) {
                                    this.imageList.push({
                                        url: result.data.url,
                                        path: result.data.path
                                    });
                                }
                            },
                            complete: () => {
                                uni.hideLoading();
                            }
                        });
                    }
                }
            })
        },
        deleteImage(index) {
            this.imageList.splice(index, 1)
        },
        // 选择视频
        chooseVideo() {
            uni.chooseVideo({
                sourceType: ['album', 'camera'],
                maxDuration: 30,
                camera: 'back',
                success: (res) => {
                    // 检查视频大小 (单位是B，需要转换为MB)
                    const fileSizeInMB = res.size / (1024 * 1024);
                    if (fileSizeInMB > 20) {
                        uni.showToast({
                            title: '视频大小不能超过20MB',
                            icon: 'none',
                            duration: 2000
                        });
                        return;
                    }
                    uni.showLoading();
                    uni.uploadFile({
                        url: getApp().globalData.apiUrl + 'v3/oss/front/upload',
                        filePath: res.tempFilePath,
                        name: 'file',
                        formData: {
                            'source': 'video',
                        },
                        header: {
                            Authorization: 'Bearer ' + this.userInfo.access_token
                        },
                        success: (uploadFileRes) => {
                            let result = JSON.parse(uploadFileRes.data);
                            if (result.state == 200) {
                                this.videoFile = {
                                    url: result.data.url,
                                    path: result.data.path
                                };
                            }
                        },
                        complete: () => {
                            uni.hideLoading();
                        }
                    });
                }
            });
        },
        // 删除视频
        deleteVideo(index) {
            this.videoFile = {};
        },
        submitFeedback() {
            if (!this.feedbackContent.trim()) {
                uni.showToast({
                    title: '请填写反馈内容',
                    icon: 'none'
                })
                return
            }
            uni.showLoading({
                title: '提交中...',
                mask: true
            });
            const params = {
                // 反馈类型 取value值
                type: this.typeOptions.find(item => item.key === this.selectedType).value,
                content: this.feedbackContent,
                img: this.imageList.map(image => image.url).join(',')
            };
            if (this.videoFile.url) {
                params.video = this.videoFile.url;
            }
            this.$request({
                url: 'v3/vehicle/front/feedBack/add',
                method: 'POST',
                data: params
            }).then((res) => {
                uni.hideLoading();
                if (res && res.state == 200) {
                    uni.showToast({
                        title: '提交成功',
                        icon: 'success'
                    });
                    // 清空表单
                    this.selectedType = 'service';
                    this.feedbackContent = '';
                    this.charCount = 0;
                    this.imageList = [];
                    this.videoFile = {};
                }
            }).catch((e) => { });
        },
        myFeedbackHistory() {
            this.$Router.push({
                path: '/pages/service/feedback/list/index'
            });
        }
    }
}
</script>

<style lang="scss" scoped>
.feedback-container {
    background-color: #f5f7fa;
    min-height: 100vh;
    padding: 50rpx 4% 50rpx;
    box-sizing: border-box;
}

.subtitle {
    font-size: 28rpx;
    color: #666;
    margin: 0rpx 0 30rpx;
    text-align: left;
}

.type-selection {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-bottom: 30rpx;
}

.type-item {
    width: 48%;
    height: 90rpx;
    background-color: #fff;
    border-radius: 12rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20rpx;

    &.active {
        background-color: #f0f9ff;
        border: 1rpx solid #0099ff;
    }

    text {
        font-size: 30rpx;
        color: #333;

        &.active-text {
            color: #0099ff;
        }
    }
}

.feedback-content {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 20rpx;
    margin-bottom: 30rpx;
}

.content-header {
    font-size: 30rpx;
    color: #333;
    margin-bottom: 20rpx;
}

.content-textarea {
    width: 100%;
    height: 200rpx;
    font-size: 28rpx;
    line-height: 1.5;
}

.char-count {
    text-align: right;
    font-size: 26rpx;
    color: #999;
    margin-top: 10rpx;
}

.upload-section {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 20rpx;
    margin-bottom: 50rpx;

    .upload-header {
        font-size: 30rpx;
        color: #333;
        margin-bottom: 30rpx;
    }
}


.image-list {
    display: flex;
    flex-wrap: wrap;
    height: 180rpx;
    .video-item {
        width: 150rpx;
        height: 150rpx;
        margin-right: 20rpx;
        margin-bottom: 20rpx;
        position: relative;

        .choose-video {
            width: 150rpx;
            height: 150rpx;
            object-fit: contain;
        }
    }

    .image-item {
        width: 150rpx;
        height: 150rpx;
        margin-right: 20rpx;
        margin-bottom: 20rpx;
        position: relative;

        image {
            width: 100%;
            height: 100%;
            border-radius: 8rpx;
        }


    }

    .delete-btn {
        position: absolute;
        top: -15rpx;
        right: -15rpx;
        width: 40rpx;
        height: 40rpx;
        background-color: rgba(0, 0, 0, 0.5);
        color: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
    }
}


.upload-btn {
    width: 150rpx;
    height: 150rpx;
    background-color: #f5f5f5;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .plus-icon {
        font-size: 60rpx;
        line-height: 60rpx;
        top: -10rpx;
        color: #ccc;
    }
}

.submit-section {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    row-gap: 20rpx;

    .submit-btn {
        width: 100%;
        height: 90rpx;
        background: linear-gradient(90deg, #42a7ff, #42d9c8);
        border-radius: 15rpx;
        color: #fff;
        font-size: 32rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        &.plain {
            background: transparent;
            color: #3a3a3a;
            border: 1rpx solid #3a3a3a;
        }
    }
}
</style>