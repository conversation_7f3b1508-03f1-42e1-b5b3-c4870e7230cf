<template>
<view class="page">
	<view class="container bb" :class="{ show: !loading }">
		<view class="detail-main bb">
			<view class="step-list">
				<uv-steps :current="currentSetp" direction="column" dot inactiveColor="#f3f4f6">
					<uv-steps-item>
						<template v-slot:title>
							<view class="step-title flex_row_center_center">
								<text class="status" :class="{ active: currentSetp == 0 }">{{ $L('已提交') }}</text>
								<text class="time">{{ order_detail.createTime }}</text>
							</view>
						</template>
						<template v-slot:desc>
							<view class="complaint-main">
								<view class="complaint-items">
									<view class="tag-text">{{ order_detail.typeValue }}</view>
								</view>
								<view class="complaint-content">{{ order_detail.content }}</view>
								<view class="complaint-group" v-if="order_detail.imgList.length">
									<view class="label">{{ $L('图片') }}</view>
									<view class="img-list">
										<view class="img-box" v-for="(img, index) in order_detail.imgList" :key="index"
											@tap="previewImage(order_detail.imgList, index)">
											<image :src="img" mode="aspectFill" class="img" />
										</view>
									</view>
								</view>
								<view class="complaint-group" v-if="order_detail.video">
									<view class="label">视频</view>
									<view class="file-list">
										<view class="video-box">
											<video id="myVideo" :src="order_detail.video"></video>
										</view>
									</view>
								</view>
							</view>
						</template>
					</uv-steps-item>
					<uv-steps-item v-if="order_detail.state == 1">
						<template v-slot:title>
							<view class="step-title flex_row_center_center">
								<text class="status" :class="{ active: currentSetp == 1 }">{{ order_detail.answer
									? $L('已回复') : $L('已处理') }}</text>
								<text class="time">{{ order_detail.updateTime }}</text>
							</view>
						</template>
						<template v-slot:desc v-if="order_detail.answer">
							<view class="complaint-main">
								<view class="complaint-content">{{ order_detail.answer }}</view>
							</view>
						</template>
					</uv-steps-item>
				</uv-steps>
			</view>
		</view>
	</view>
	<uv-loading-page :loading="loading" loading-text="加载中..." font-size="24rpx"></uv-loading-page>
</view>
</template>
<script>
export default {
	data() {
		return {
			id: null,
			imgUrl: getApp().globalData.imgUrl,
			order_detail: {
				imgList: [],
				answer: '',
				content: '',
				createTime: '',
				updateTime: '',
				state: '',
				typeValue: '',
				type: '',
				id: ''
			},
			currentSetp: 0,
			loading: true,
		}
	},
	onLoad(options) {
		const { id } = this.$Route.query;
		if (id) {
			this.id = id;
			this.getDetail(id);
		}
	},
	methods: {
		// 获取诉求详情
		getDetail(id) {
			this.$request({
				url: 'v3/vehicle/front/feedBack/detail',
				method: 'get',
				data: {
					feedBackId: id
				}
			}).then((res) => {
				console.log('order_detail', res);
				if (res && res.state == 200) {
					const data = res.data;
					const imgArr = data.img ? data.img.split(',') : [];
					this.order_detail = {
						...data,
						imgList: imgArr,
					};
					this.currentSetp = data.state || 0; // 根据状态设置当前步骤
				}
				this.loading = false;
			}).catch((e) => { });
		},
		// 复制内容到粘贴板
		copyData(data) {
			uni.setClipboardData({
				data: data,
				success(res) {
					uni.showToast({
						icon: 'none',
						title: '复制成功'
					});
				}
			});
		},
		//
		previewImage(imgs, index) {
			uni.previewImage({
				urls: imgs,
				current: index
			});
		}
	}
};
</script>
<style lang="scss">
.page {
	width: 100%;
	background: $bg1;

	.uv-steps-item__line {
		height: 100% !important;
	}

	.container {
		position: relative;
		min-height: 100vh;
		opacity: 0;

		&.show {
			opacity: 1;
		}

		.detail-main {
			width: 100%;
			height: 100%;
			overflow-y: scroll;
			background-color: #ffffff;
			border-radius: 20rpx;
			padding: 50rpx 5% 50rpx 3%;
		}

		.order-title {
			font-size: 38rpx;
			color: #383838;
			padding-bottom: 38rpx;
			border-bottom: 1px solid #e5e5e5;
			margin-bottom: 50rpx;

			.title-group {
				display: flex;
				align-items: center;
				justify-content: space-between;
				font-size: 27rpx;
				color: #6e6e6e;
				margin-bottom: 38rpx;
			}
		}

		.step-title {
			font-size: $fs-base;
			justify-content: space-between;

			.active {
				color: #3c9cff;
			}

			.time {
				color: #6e6e6e;
			}
		}

		.complaint-main {
			background-color: #f3f4f6;
			width: 100%;
			box-sizing: border-box;
			padding: 20rpx;
			border-radius: 20rpx;
			margin: 20rpx 0;
			font-size: $fs-base;
			display: flex;
			flex-direction: column;
			gap: 20rpx;

			.tag-text {
				background-color: #3c9cff;
				color: #fff;
				text-align: center;
				display: inline-block;
				height: 40rpx;
				border-radius: 20rpx;
				padding: 0 20rpx;
				line-height: 40rpx;
				font-size: $fs-m;
			}
		}

		.complaint-group {
			margin-top: 20rpx;

			.label {
				color: #6e6e6e;
				font-size: $fs-base;
				margin-bottom: 20rpx;
			}

			.items-text {
				color: #3c9cff;
			}

			.img-list {
				display: flex;
				align-items: center;
				gap: 3%;

				.img-box {
					width: 170rpx;
					height: 170rpx;
				}

				.img {
					width: 100%;
					height: 100%;
				}
			}
		}

		.complaint-content {
			color: #6e6e6e;
		}
	}
}
</style>