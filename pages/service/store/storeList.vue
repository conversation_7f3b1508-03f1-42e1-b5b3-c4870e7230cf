<template>
    <view class="storeList" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }">
        <ktabbar :bgImg="imgUrl + 'default_bg.jpg'" :title="pageTitle" />
        <view class="storeList-content">
            <view class="storeList-area flex_row_center_start">
                <view class="address-info">
                    <PickerAddress :onlyShowCity="true" :isShowArea="false" :value="pickerAddressVue" class="picker" title="请选择城市" @change="handleCityChange" />
                </view>
                <view class="cu-bar bg-white search storeList-search">
                    <view class="search-form round">
                        <uv-search
                            placeholder="门店名称/地址信息"
                            borderColor="#e5e5e5"
                            bgColor="transparent"
                            v-model="params.storeName"
                            clearabled
                            :showAction="false"
                            @clear="onSearch"
                            @search="onSearch"
                        ></uv-search>
                    </view>
                </view>
            </view>
            <view class="storeList-list">
                <view v-for="(item, index) in storeList" :key="index" class="storeList-list-item" @click="onClickStore(item)">
                    <view class="storeList-list-item-lineOne">
                        <view class="storeList-list-item-lineOne-title">{{ item.storeName }}</view>
                        <view class="storeList-list-item-lineOne-distance" v-if="locationData.latitude && locationData.longitude">
                            <image :src="imgUrl + 'location2.png'" class="location-logo" mode="widthFix" />
                            <!-- 距离省略小数位 -->
                            <text v-if="item.distance && item.distance >= 1000">{{ (item.distance / 1000).toFixed(1) }}km</text>
                            <text v-else>{{ item.distance ? item.distance.toFixed(0) : '--' }}m</text>
                        </view>
                    </view>
                    <view class="storeList-list-item-lineTwo">
                        <view class="storeList-list-item-lineTwo-time">营业时间：{{ item.businessTime }}</view>
                    </view>
                    <view class="store-list-address-container">
                        <view class="store-list-address">
                            {{ `${item.address || '--'}` }}
                        </view>
                    </view>
                    <view class="storeList-tag-list">
                        <view v-for="(type, index) in item.serviceTypes" :key="type.id" class="storeList-tag">
                            {{ type.name }}
                        </view>
                    </view>
                </view>
                <!-- 暂无门店 -->
                <view v-if="storeList.length === 0 && loadingState != 'first_loading'" class="empty_store flex_column_center_start">
                    <text class="cuIcon-info text-xxl text-gray"></text>
                    <text class="text-gray">~暂无门店</text>
                </view>
                <!-- 加载状态 -->
                <LoadingState v-else :state="loadingState" />
                <!-- 底部填充高度-->
                <view style="height: 50rpx"></view>
            </view>
        </view>
    </view>
</template>

<script>
import ktabbar from '@/components/ktabbar.vue';
import LoadingState from '@/components/loading-state.vue';
import PickerAddress from '@/components/picker-address/pickerAddress.vue';
export default {
    components: {
        PickerAddress,
        ktabbar,
        LoadingState
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            pageTitle: '附近门店',
            pickerAddressVue: '定位中...',
            params: {
                pageSize: 10,
                current: 1,
                city: '',
                cityCode: '',
                province: '',
                provinceCode: '',
                area: '',
                areaCode: '',
                lat: '',
                lon: '',
                userLat: '',
                userLng: '',
                storeName: ''
            },
            current: 1,
            total: 0,
            loadingState: 'first_loading', // first_loading、 no_more_data 、loading 、refreshing
            storeList: [],
            locationData: uni.getStorageSync('locationData'),
            acdParams: {
                eventType: 'PAGE_VIEW',
                pageTitle: '附近门店',
                pageName: 'storeList',
                pagePath: 'pages/storeList/storeList',
                elementName: '',
                enterProgm: '',
                currProgm: '立马好车主',
                deviceMode: '',
                appVersion: '',
                visitor: uni.getStorageSync('customerInfo').userPhone,
                visitorName: uni.getStorageSync('customerInfo').userName
            },
            type: null, // 门店操作类型 select 选择门店，default 默认门店
            serviceType: null // 门店类型 1 售前门店 2 维修门店
        };
    },

    onLoad() {
        const { type, serviceType } = this.$Route.query;
        this.type = type;
        this.serviceType = serviceType;
        if (type && type === 'select') {
            this.pageTitle = '选择门店';
        }
        if (this.locationData) {
            this.params.lat = this.locationData.latitude;
            this.params.lon = this.locationData.longitude;
            this.params.storeGpsLat = this.locationData.latitude;
            this.params.storeGpsLng = this.locationData.longitude;
            this.params.userLat = this.locationData.latitude;
            this.params.userLng = this.locationData.longitude;
            this.getStoreList(this.params);
            this.addressResolution({ location: `${this.locationData.longitude},${this.locationData.latitude}` });
        } else {
            // 检查授权信息。如已授权直接获取位置数据，否则请求授权
            // #ifdef MP-WEIXIN
            uni.getSetting({
                success: (res) => {
                    console.log('获取授权设置成功', res);
                    if (res.authSetting['scope.userFuzzyLocation']) {
                        this.getLocationData();
                    } else {
                        this.requestLocationAuthorization();
                    }
                }
            });
            // #endif
            // #ifdef H5
            this.getLocationData();
            // #endif
        }
    },
    onReachBottom() {
        if (this.loadingState !== 'no_more_data' && this.loadingState !== 'refreshing') {
            this.params.current++;
            this.getStoreList(this.params);
        }
    },

    methods: {
        // 请求位置授权
        requestLocationAuthorization() {
            uni.authorize({
                scope: 'scope.userFuzzyLocation',
                success: () => {
                    this.getLocationData();
                },
                fail: (err) => {
                    console.log('获取位置授权失败', err);
                    this.$api.msg('请授权获取位置信息');
                    this.getStoreList(this.params);
                }
            });
        },
        // 获取当前位置
        getLocationData() {
            // #ifdef MP-WEIXIN
            uni.getFuzzyLocation({
                type: 'gcj02',
                success: (res) => {
                    // 缓存当前用户位置信息
                    uni.setStorageSync('locationData', res);
                    // this.params.lat = res.latitude
                    // this.params.lon = res.longitude
                    this.params.storeGpsLat = this.locationData.latitude;
                    this.params.storeGpsLng = this.locationData.longitude;
                    this.getStoreList(this.params);
                    this.addressResolution({ location: `${res.longitude},${res.latitude}` });
                },
                fail: (err) => {
                    console.log('获取当前位置err:', err);
                }
            });
            // #endif
            console.log('获取当前位置');
            // #ifdef H5
            uni.getLocation({
                type: 'gcj02',
                success: (res) => {
                    console.log('获取当前位置成功', res);
                    // 缓存当前用户位置信息
                    uni.setStorageSync('locationData', res);
                    // this.params.lat = res.latitude
                    // this.params.lon = res.longitude
                    this.params.storeGpsLat = this.locationData.latitude;
                    this.params.storeGpsLng = this.locationData.longitude;
                    this.getStoreList(this.params);
                    this.addressResolution({ location: `${res.longitude},${res.latitude}` });
                },
                fail: (err) => {
                    console.log('获取当前位置err:', err);
                }
            });
            // #endif
        },
        // 高德逆地址解析
        addressResolution(params) {
            uni.request({
                url: 'https://restapi.amap.com/v3/geocode/regeo',
                data: {
                    key: 'bb684dcb5969944eb5d67a66690457eb',
                    ...params
                },
                method: 'GET',
                header: {
                    'content-type': 'application/json'
                },
                success: (res) => {
                    console.log('高德逆地址解析成功', res);
                    if (res.data.infocode == '10000') {
                        this.pickerAddressVue = `${res.data.regeocode.addressComponent.province}${res.data.regeocode.addressComponent.city}${res.data.regeocode.addressComponent.district}`;
                        // this.getStoreList(this.params)
                    } else {
                        this.$dialog.toast('定位失败请选择省市区');
                    }
                },
                fail: (rej) => {
                    console.log('====================================');
                    console.log(rej);
                    console.log('====================================');
                    this.$dialog.toast('定位失败请选择省市区');
                }
            });
        },
        // 选择省市区
        handleCityChange(val) {
            console.log('选择省市区', val);
            this.params.city = val?.city.value;
            this.params.cityCode = val?.city.code;
            this.params.province = val?.province.value;
            this.params.provinceCode = val?.province.code;
            // this.params.area = val?.area.value;
            // this.params.areaCode = val?.area.code;
            this.params.current = 1;
            this.getStoreList(this.params);
        },
        contact(tel) {
            this.acdParams.eventType = 'BTN_CLICK';
            this.acdParams.elementName = '附近门店联系售后';
            uni.makePhoneCall({
                phoneNumber: tel,
                success: () => {
                    this.acdParams.elementName = '附近门店呼叫商家';
                }
            });
        },
        // 选择门店
        onClickStore(item) {
            // 到店维修地图页选择门店
            if (this.type && this.type === 'select') {
                this.$api.prePage().updateStore({ storeGpsLat: item.storeGpsLat, storeGpsLng: item.storeGpsLng, id: item.id }, 'select');
                this.$Router.back();
                return;
            }
            // 激活车辆进入选择门店
            if (this.type && this.type === 'activation') {
                this.$api.prePage().updateStore(item);
                this.$Router.back();
                return;
            }
            // 门店详情
            this.$Router.push({ path: '/pages/service/maintenance/index', query: { id: item.id, distance: item.distance, type: 'view' } });
        },
        // 获取门店信息
        getStoreList(values) {
            const params = {
                ...values
            };
            if (this.serviceType && this.serviceType !== 'all') {
                params.serverItem = this.serviceType;
            }
            // 过滤没有值的字段
            Object.keys(params).forEach((key) => {
                if (params[key] === '' || params[key] === undefined || params[key] === null) {
                    delete params[key];
                }
            });
            this.loadingState = 'loading';
            this.$request({
                url: 'v3/vehicle/front/store/list',
                method: 'GET',
                data: {
                    ...params
                }
            })
                .then((res) => {
                    if (res && res.state == 200) {
                        const { pagination, list } = res.data;
                        this.total = pagination.total;
                        if (this.params.current >= 2) {
                            this.storeList = [...this.storeList, ...list];
                        } else {
                            this.storeList = list;
                        }
                        if (pagination.total <= pagination.current * pagination.pageSize) {
                            this.loadingState = 'no_more_data';
                        }
                    }
                })
                .catch((e) => {});
        },
        // 搜索
        onSearch() {
            this.params.current = 1;
            this.storeList = [];
            this.loadingState = 'loading';
            this.getStoreList(this.params);
        },
        // 导航
        navigation(item) {
            const { storeGpsLat, storeGpsLng, storeName } = item;
            uni.openLocation({
                latitude: storeGpsLat,
                longitude: storeGpsLng,
                name: storeName
            });
        }
    }
};
</script>

<style lang="scss">
.storeList {
    width: 100%;
    min-height: 100vh;
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    // 背景图片固定，不滚动
    background-attachment: fixed;
    .storeList-content {
        width: 94%;
        margin: 0 auto;
        background-color: #fff;
        border-radius: 40rpx 40rpx 0 0;
        padding: 20rpx 20rpx;
        min-height: 100vh;
        padding-bottom: 60rpx; // 底部安全距离
    }
    .storeList-area {
        position: sticky;
        top: 0;
        left: 0;
        z-index: 2;
        column-gap: 20rpx;
        width: 100%;
        padding: 20rpx 0;
        box-sizing: border-box;
        .address-info {
            padding: 10rpx 0;
            width: 150rpx;
        }
        .storeList-search {
            flex: 1;
        }
    }

    .storeList-list {
        width: 100%;
        //列表项
        .storeList-list-item {
            overflow: hidden;
            position: relative;
            margin-bottom: 24rpx;
            padding: 25rpx 20rpx 15rpx;
            border-radius: 0;
            width: 100%;
            background: rgba(242, 242, 242, 1);

            .storeList-tag-list {
                display: flex;
                width: calc(100% - 120rpx);
                flex-wrap: wrap;
                row-gap: 10rpx;

                .storeList-tag {
                    float: left;
                    margin: 0 8rpx;
                    padding: 0 15rpx;
                    border-radius: 16rpx;
                    height: 32rpx;
                    background: rgba(178, 161, 240, 1);
                    line-height: 32rpx;
                    font-size: 22rpx;
                    color: #fff;
                }
            }

            .storeList-list-item-lineOne {
                height: 40rpx;
                display: flex;
                justify-content: space-between;
                align-items: center;

                .distance {
                    margin-right: 10rpx;
                }

                .storeList-list-item-lineOne-title {
                    @extend .line1;
                    line-height: 40rpx;
                    font-size: $fs-base;
                    color: #000;
                }

                .storeList-list-item-lineOne-distance {
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                    text-align: right;
                    width: 200rpx;
                    font-size: 12px;
                    color: rgba(0, 0, 0, 0.4);
                    .location-logo {
                        width: 38rpx;
                        height: 38rpx;
                        margin-right: 6rpx;
                    }
                }
            }

            .storeList-list-item-lineTwo {
                margin: 14rpx 0;
                height: 24rpx;

                .storeList-list-item-lineTwo-time {
                    float: left;
                    line-height: 24rpx;
                    font-weight: 400;
                    font-size: 12px;
                    color: rgba(0, 0, 0, 0.4);
                }
            }
            .store-list-address-container {
                display: flex;
                justify-content: space-between;
                margin-bottom: 20rpx;
                .store-list-address {
                    font-size: $fs-m;
                    color: rgba(0, 0, 0, 0.7);
                    white-space: normal;
                }
            }
        }
    }

    .empty_store {
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        margin-top: 50rpx;

        .cuIcon-info {
            font-size: $fs-base;
            color: #999;
            margin-bottom: 20rpx;
        }

        text {
            font-size: $fs-base;
            color: #999;
        }
    }
}
</style>
