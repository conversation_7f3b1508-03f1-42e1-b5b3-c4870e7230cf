<template>
    <view class="service-home" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }">
        <!-- 自定义顶部导航栏 -->
        <view class="fixed_top_status_bar">
            <view class="top_status_bar_seat" :style="{ height: topBar.navBarHeight + 'px' }"></view>
            <view class="custom-nav-taber" :style="{ paddingTop: topBar.offsettop + 'px', background: pageBgColor }">
                <!-- 搜索栏 -->
                <view :style="{ height: topBar.menuHeight + 'px', lineHeight: topBar.menuHeight + 'px', width: `calc(100% - ${topBar.menuWidth}px)` }" class="tap_bar">
                    <view class="tap_text">品质服务 ，无忧生活</view>
                    <view class="right_btns" v-if="imSettingData.h5ImEnable">
                        <image class="add_icon" :src="imgUrl + 'im_black.png'" mode="" @click="toIm" />
                    </view>
                </view>
            </view>
        </view>
        <view class="service-section">
            <view class="service-row">
                <view class="service-card_1" :style="{ backgroundImage: `url(${imgUrl}weixiu.png)` }" @click="goMaintenance()">
                    <view class="flex_column_center_center t">
                        <view class="service-title">到店维修</view>
                        <view class="service-desc">专业维修服务</view>
                    </view>
                </view>
                <view class="service-card_2">
                    <view class="service-card_3" @click="toActivationList()">
                        <view class="service-icon sanbao" :style="{ backgroundImage: `url(${imgUrl}sanbao.png)` }"></view>
                        <view class="service-title">三包查询</view>
                    </view>
                    <view class="service-card_3" v-if="imSettingData.h5ImEnable" @click="toIm()">
                        <view class="service-icon im" :style="{ backgroundImage: `url(${imgUrl}im_white.png)` }"></view>
                        <view class="service-title">智能客服</view>
                    </view>
                </view>
            </view>
            <!-- 四个tab -->
            <view class="service-circle-row">
                <view class="circle-btn" @click="toFixDoc()">
                    <image :src="imgUrl + 'service_icon1.png'" class="nav-icon" mode="widthFix" />
                    <text class="t">自检手册</text>
                </view>
                <view class="circle-btn" @click="toTutorials()">
                    <image :src="imgUrl + 'service_icon2.png'" class="nav-icon" mode="widthFix" />
                    <text class="t">新手教程</text>
                </view>
                <view class="circle-btn" @click="toProductDoc()">
                    <image :src="imgUrl + 'service_icon3.png'" class="nav-icon" mode="widthFix" />
                    <text class="t">产品说明书</text>
                </view>
                <view class="circle-btn" @click="toServiceOrder()">
                    <image :src="imgUrl + 'service_icon4.png'" class="nav-icon" mode="widthFix" />
                    <text class="t">我的工单</text>
                </view>
            </view>
            <!-- 常见问题 -->
            <view class="qa-row">
                <view class="row-list">
                    <view class="row-btn" v-for="item in questionList" :key="item.id" @click="toDocDetail(item)">
                        <text class="t">{{ item.title }}</text>
                        <uv-icon name="arrow-right" size="16" />
                    </view>
                </view>
                <view class="view-more" @click="goFqa">查看更多常见问题</view>
            </view>
        </view>
        <!-- 附近门店 -->
        <view class="store-section">
            <view class="sectio-title flex_row_between_center">
                <text class="t">附近门店</text>
                <view class="nav-btn" @click="navToStoreList">
                    <text class="t">全部门店</text>
                    <uv-icon name="arrow-right" size="14" color="rgba(0,0,0,0.4)" />
                </view>
            </view>
            <!-- 门店信息-->
            <view class="store-detail" v-for="store in storeList" :key="store.id">
                <!-- 距离 -->
                <view class="store-distance-fix">
                    <!-- 距离省略小数位 -->
                    <text v-if="store.distance && store.distance >= 1000">{{ (store.distance / 1000).toFixed(1) }}km</text>
                    <text v-else>{{ store.distance ? store.distance.toFixed(0) : '--' }}m</text>
                </view>
                <!-- 门店信息 -->
                <view class="store-info-box">
                    <!-- 门店名 -->
                    <view class="store-title-row">
                        <image :src="imgUrl + 'lm_logo_2025.png'" class="lima-logo" mode="widthFix" />
                        <text class="store-name">{{ store.storeName }}</text>
                    </view>
                    <!-- 门店评分 -->
                    <!-- <view class="store-rate">
                        <view>门店评分 {{ store.averageStore || 5 }}</view>
                        <image :src="imgUrl + 'star.png'" class="star-icon" mode="widthFix" />
                    </view> -->
                    <!-- 门店营业时间 -->
                    <view class="store-time flex_row_between_center">
                        <view>营业时间：{{ store.businessTime || '--' }}</view>
                    </view>
                    <!-- 门店详细地址 -->
                    <view class="store-address">{{ store.address }}</view>
                    <!-- 服务类型 -->
                    <view class="storeList-tag-list" v-if="store.serviceTypes">
                        <view v-for="(type, index) in store.serviceTypes" :key="type.id" class="storeList-tag">
                            {{ type.name }}
                        </view>
                    </view>
                    <!-- 底部操作 -->
                    <view class="store-contact">
                        <view class="store-btn" @click="navToStore">
                            <image :src="imgUrl + 'location2.png'" class="btn-icon" mode="widthFix" />
                            <text>地图导航</text>
                        </view>
                        <view class="store-btn black" @click="toContact">
                            <image :src="imgUrl + 'tel_white.png'" class="btn-icon tel" mode="widthFix" />
                            <text>联系门店</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <!-- 附近门店 end-->
        <!-- 暂无门店 -->
        <view class="empty-store" v-if="storeList.length == 0">
            <text class="t">{{ $L('附近暂无门店') }}~</text>
        </view>
        <!-- 暂无门店 end-->
        <!-- 底部安全高度 -->
        <view :style="{ height: 160 + 'rpx', width: '100%' }"></view>
    </view>
</template>
<script>
import { customTabbarShow, customTabbarHide } from '@/utils/common';
import { mapState } from 'vuex';
export default {
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            topBar: {},
            pageBgColor: 'transparent',
            questionList: [], // 常见问题列表数据
            covers: [],
            locationData: uni.getStorageSync('locationData'),
            storeList: [],
            imSettingData: { appImUrl: '', h5ImEnable: null, miniImUrl: '' }, // 客服设置
            params: {
                lat: 0,
                lng: 0,
                page: 1,
                pageSize: 10
            },
            currentLocation: {
                id: 0,
                name: '当前位置',
                iconPath: '/static/favicon.png',
                latitude: 0,
                longitude: 0,
                width: 24,
                height: 32
            }
        };
    },
    computed: {
        ...mapState(['hasLogin'])
    },
    onLoad() {
        this.fetchBaseQuestionList();
        if (this.locationData) {
            this.params.lat = this.locationData.latitude;
            this.params.lng = this.locationData.longitude;
            this.getStoreList(this.params);
        } else {
            // 检查授权信息。如已授权直接获取位置数据，否则请求授权
            // #ifdef MP-WEIXIN
            uni.getSetting({
                success: (res) => {
                    console.log('获取授权设置成功', res);
                    if (res.authSetting['scope.userFuzzyLocation']) {
                        this.getLocationData();
                    } else {
                        this.requestLocationAuthorization();
                    }
                }
            });
            // #endif
            // #ifdef H5
            this.getLocationData();
            // #endif
        }
        // #ifdef MP-WEIXIN
        // 胶囊按钮位置信息
        const systemInfo = uni.getWindowInfo();
        // 导航栏高度 = 状态栏高度 + 44
        const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
        this.navBarHeight = systemInfo.statusBarHeight + 44 + 'px';
        this.menuRight = systemInfo.screenWidth - menuButtonInfo.right + 'px';
        let globalData = {};
        globalData.navBarHeight = systemInfo.statusBarHeight + 44;
        globalData.menuRight = systemInfo.screenWidth - menuButtonInfo.right;
        globalData.menuHeight = menuButtonInfo.height;
        globalData.menuWidth = menuButtonInfo.width;
        globalData.offsettop = menuButtonInfo.top;
        this.topBar = globalData;
        // #endif
    },
    onShow() {
        customTabbarShow(3);
        this.getImSetting();
    },
    onHide() {
        customTabbarHide(3);
    },
    onUnload() {
        // 页面卸载时，清楚位置数据缓存
        uni.removeStorageSync('locationData');
    },
    onPageScroll({ scrollTop }) {
        // 页面滚动时，修改顶部导航栏背景色
        if (scrollTop > 50) {
            this.pageBgColor = '#fff';
        } else {
            this.pageBgColor = 'transparent';
        }
    },
    methods: {
        // 获取客服设置
        getImSetting() {
            this.$request({
                url: 'v3/system/front/setting/helper',
                method: 'POST',
                data: {
                    ...this.imSettingData
                }
            }).then((res) => {
                if (res.state == 200) {
                    this.imSettingData = res.data;
                    if (res.data.h5ImEnable == '1') {
                        uni.setStorageSync('imConfig', res.data);
                    } else {
                        uni.removeStorageSync('imConfig');
                    }
                }
            });
        },
        // 获取常见问题4条
        fetchBaseQuestionList() {
            this.$request({
                url: 'v3/system/front/faqContent/hotList'
            })
                .then((res) => {
                    this.initLoading = false; // 初始化加载完成
                    uni.hideLoading();
                    if (res.state == 200) {
                        this.questionList = res.data.slice(0, 4);
                    } else {
                        this.$api.msg(res.msg);
                    }
                })
                .catch(() => {
                    uni.hideLoading();
                });
        },
        // 跳转到常见问题详情
        toDocDetail(item) {
            this.$Router.push({ path: '/newPages/doc/detail', query: { id: item.id } });
        },
        // 跳转到常见问题
        goFqa() {
            this.$Router.push({ path: '/newPages/doc/faq' });
        },
        // 跳转到智能客服
        toIm() {
            if (!this.hasLogin) {
                getApp().globalData.goLogin(this.$Route);
            } else {
                this.$Router.push({ path: '/pages/user/kefu' });
            }
        },
        /*
         * 服务工单
         */
        toServiceOrder() {
            if (!this.hasLogin) {
                getApp().globalData.goLogin(this.$Route);
            } else {
                this.$Router.push({
                    path: '/pages/service/order/repairList'
                });
            }
        },

        /*
         * 三包激活
         */
        toActivationVehicle() {
            if (!this.hasLogin) {
                getApp().globalData.goLogin(this.$Route);
            } else {
                this.$Router.push({
                    path: '/newPages/vehicle/activationVehicle'
                });
            }
        },
        /*
         * 三包查询
         */
        toActivationList() {
            if (!this.hasLogin) {
                getApp().globalData.goLogin(this.$Route);
            } else {
                this.$Router.push({
                    path: '/newPages/vehicle/activationVehicleList'
                });
            }
        },

        /*
         *  到店维修
         */
        goMaintenance() {
            if (!this.hasLogin) {
                getApp().globalData.goLogin(this.$Route);
            } else {
                this.$Router.push({
                    path: '/pages/service/maintenance/index'
                });
            }
        },
        // 获取门店信息
        getStoreList(values) {
            this.$request({
                url: 'v3/vehicle/front/store/list',
                method: 'GET',
                data: {
                    pageSize: 1,
                    current: 1,
                    state: 1,
                    userLat: values.lat, // 用户纬度
                    userLng: values.lng, // 用户经度
                    storeGpsLat: values.lat, // 门店纬度
                    storeGpsLng: values.lng // 门店经度
                    // serverItem: '2' //售后服务网点
                }
            })
                .then((res) => {
                    if (res && res.state == 200) {
                        this.covers = [];
                        this.storeList = res.data.list;
                    }
                })
                .catch((e) => {});
        },
        // 请求位置授权
        requestLocationAuthorization() {
            uni.authorize({
                scope: 'scope.userFuzzyLocation',
                success: () => {
                    this.getLocationData();
                },
                fail: (err) => {
                    uni.showModal({
                        title: '温馨提示',
                        content: '要查看附近门店，请授权获取位置信息，以便为您提供更好的服务',
                        showCancel: true,
                        confirmText: '去设置',
                        success: (res) => {
                            if (res.confirm) {
                                // 跳转到设置页面
                                uni.openSetting({
                                    success: (settingRes) => {
                                        if (settingRes.authSetting['scope.userFuzzyLocation']) {
                                            this.getLocationData();
                                        } else {
                                            // this.$api.toast('未授权无法获取位置信息');
                                        }
                                    }
                                });
                            }
                        }
                    });
                }
            });
        },
        // 获取当前位置
        getLocationData() {
            // #ifdef MP-WEIXIN
            uni.getFuzzyLocation({
                type: 'gcj02',
                success: (res) => {
                    // 缓存当前用户位置信息
                    uni.setStorageSync('locationData', res);
                    this.params.lat = res.latitude;
                    this.params.lng = res.longitude;
                    this.getStoreList(this.params);
                },
                fail: (err) => {
                    console.log('获取当前位置err:', err);
                }
            });
            // #endif
            // #ifdef H5
            uni.getLocation({
                type: 'gcj02',
                success: (res) => {
                    console.log('获取当前位置:', err);
                    // 缓存当前用户位置信息
                    uni.setStorageSync('locationData', res);
                    this.params.lat = res.latitude;
                    this.params.lng = res.longitude;
                    this.getStoreList(this.params);
                },
                fail: (err) => {
                    console.log('获取当前位置err:', err);
                }
            });
            // #endif
        },
        // 跳转到自检手册
        toFixDoc() {
            this.$Router.push({
                path: '/newPages/doc/selfInspection'
            });
        },
        // 跳转到产品说明书
        toProductDoc() {
            // this.$Router.push({path: '/newPages/doc/productDoc'});
        },
        // 跳转到新手教程
        toTutorials() {
            this.$Router.push({ path: '/newPages/doc/tutorials' });
        },
        // 跳转到全部门店
        navToStoreList() {
            this.$Router.push({
                path: '/pages/service/store/storeList'
            });
        }
    }
};
</script>
<style lang="scss" scoped>
.fixed_top_status_bar {
    .custom-nav-taber {
        position: fixed;
        z-index: 10;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        top: 0;
        // #ifdef MP
        left: 0;
        width: 100%;
        // #endif
        // #ifdef H5
        padding-top: 80rpx;
        box-sizing: content-box;
        left: 50%;
        width: 750rpx;
        height: 120rpx;
        transform: translateX(-50%);
        // #endif
        background-repeat: no-repeat;
        background-position: top center;
        background-size: 100% auto;
    }
    .tap_bar {
        display: flex;
        align-items: center;
        width: 100%;
        // #ifdef MP
        justify-content: flex-start;
        padding-left: 30rpx;
        column-gap: 30rpx;
        // #endif
        // #ifdef H5
        justify-content: space-between;
        width: 100%;
        padding: 0 4%;
        height: 100%;
        // #endif
        .tap_text {
            font-size: 30rpx;
            color: #000;
            font-weight: bold;
            // #ifdef MP
            // width: calc(100% - 180rpx);
            // #endif
            /* #ifdef H5 */
            font-size: 32rpx;
            width: calc(100% - 180rpx);
            /* #endif */
        }
    }
    .right_btns {
        display: flex;
        align-items: center;
        width: 180rpx;
        column-gap: 30rpx;
        // #ifdef MP
        justify-content: flex-start;
        // #endif
        /* #ifdef H5 */
        justify-content: flex-end;
        /* #endif */
    }
    .add_icon {
        object-fit: contain;
        /* #ifdef MP-WEIXIN */
        width: 40rpx;
        height: 40rpx;
        /* #endif */
        /* #ifdef H5 */
        width: 45rpx;
        height: 45rpx;
        /* #endif */
    }
    .top_status_bar_seat {
        // #ifdef MP
        width: 100%;
        // #endif
        /* app-2-start */
        // #ifdef H5
        width: 750rpx;
        height: 180rpx;
        // #endif
    }
}
.service-home {
    width: 100%;
    padding: 0 3%;
    box-sizing: border-box;
    background: $bg1;
    min-height: 100vh;
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    // 背景图片固定，不滚动
    background-attachment: fixed;
    .service-section {
        margin-top: 48rpx;
    }
    .service-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 32rpx;
        column-gap: 20rpx;
        .service-card_1 {
            flex: 1;
            height: 320rpx;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background-color: #fff;
            border-radius: 40rpx;
            background-position: -20rpx -70rpx;
            background-repeat: no-repeat;
            background-size: cover;
            .t {
                margin-top: 150rpx;
            }
            .service-desc {
                margin-top: 4rpx;
                color: rgba(0, 0, 0, 0.5);
                font-size: $fs-s;
            }
        }
        .service-card_2 {
            flex: 1;
            height: 320rpx;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }
        .service-card_3 {
            width: 100%;
            padding: 0 50rpx;
            height: calc(100% / 2 - 10rpx);
            background-color: #fff;
            border-radius: 40rpx;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            .service-icon {
                width: 90rpx;
                height: 90rpx;
                border-radius: 45rpx;
                background-position: center;
                background-repeat: no-repeat;
                background-size: 60%;
                margin-right: 20rpx;
                &.sanbao {
                    background-color: #b2a1f0;
                }
                &.im {
                    background-color: rgba(161, 201, 247, 1);
                }
            }
        }
    }
    .service-circle-row {
        background-color: #fff;
        width: 100%;
        border-radius: 40rpx;
        overflow: hidden;
        padding: 40rpx 30rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        margin-bottom: 32rpx;
        .circle-btn {
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: center;
            row-gap: 20rpx;
            .nav-icon {
                width: 60rpx;
                height: 60rpx;
            }
            .t {
                font-size: $fs-base;
                color: #000;
            }
        }
    }
    .qa-row {
        background-color: #fff;
        width: 100%;
        border-radius: 40rpx;
        overflow: hidden;
        padding: 40rpx 30rpx 0 30rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        box-sizing: border-box;
        margin-bottom: 32rpx;
        .row-list {
            width: 100%;
            display: flex;
            flex-direction: column;
            row-gap: 10rpx;
            .row-btn {
                height: 60rpx;
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: $fs-base;
                color: #000;
            }
        }
        .view-more {
            width: 100%;
            text-align: center;
            line-height: 1.5;
            font-size: $fs-base;
            color: rgba(0, 0, 0, 0.5);
            padding: 10rpx 0;
            text-align: center;
            margin-top: 32rpx;
            padding-top: 20rpx;
            padding-bottom: 20rpx;
            border-top: 0.5px solid rgba(0, 0, 0, 0.1);
        }
    }
    .store-section {
        width: 100%;
        margin-top: 30rpx;
        .sectio-title {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-sizing: border-box;
            padding: 20rpx 0;
            .t {
                font-size: 32rpx;
                color: #000;
                font-weight: bold;
            }
            .nav-btn {
                display: flex;
                align-items: center;
                column-gap: 0rpx;
                .t {
                    font-size: $fs-s;
                    font-weight: 500;
                    color: rgba(89, 87, 87, 1);
                }
            }
        }
        .store-detail {
            position: relative;
            width: 100%;
            overflow: hidden;
            padding: 40rpx 4% 50rpx 4%;
            box-sizing: border-box;
            background-color: #fff;
            border-radius: 40rpx;
            .store-distance-fix {
                position: absolute;
                top: 0;
                right: 0;
                height: 40rpx;
                line-height: 40rpx;
                text-align: center;
                padding: 0 40rpx;
                border-radius: 0 0 0 40rpx;
                background-color: rgba(178, 161, 240, 1);
                font-size: 20rpx;
                color: #fff;
            }
            .store-info-box {
                padding-top: 20rpx;
                .store-title-row {
                    display: flex;
                    align-items: center;
                    margin-bottom: 20rpx;

                    .lima-logo {
                        width: 60rpx;
                        height: 60rpx;
                        margin-right: 16rpx;
                    }
                    .store-name {
                        font-size: $fs-base;
                        font-weight: bold;
                        color: #000;
                    }
                }
                .store-rate {
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    font-size: 24rpx;
                    color: rgba(89, 87, 87, 1);
                    margin-bottom: 15rpx;
                    .star-icon {
                        width: 24rpx;
                        height: 24rpx;
                        margin-left: 8rpx;
                    }
                }
                .store-time {
                    font-size: 24rpx;
                    color: rgba(89, 87, 87, 1);
                    margin-bottom: 15rpx;
                }
                .store-address {
                    font-size: 26rpx;
                    color: rgba(89, 87, 87, 1);
                    margin: 10rpx 0;
                }
                .storeList-tag-list {
                    display: flex;
                    width: 100%;
                    margin: 15rpx 0;
                    flex-wrap: wrap;
                    row-gap: 10rpx;
                    .storeList-tag {
                        float: left;
                        margin: 0 8rpx;
                        padding: 0 15rpx;
                        border-radius: 20rpx;
                        height: 40rpx;
                        background: rgba(178, 161, 240, 1);
                        line-height: 40rpx;
                        font-size: 22rpx;
                        color: #fff;
                    }
                }
                .store-contact {
                    width: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    box-sizing: border-box;
                    padding-top: 40rpx;
                    margin-top: 40rpx;
                    border-top: 0.5px solid rgba(0, 0, 0, 0.1);
                    .store-btn {
                        width: 45%;
                        height: 70rpx;
                        border-radius: 35rpx;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        column-gap: 10rpx;
                        font-size: $fs-base;
                        color: rgba(73, 73, 73, 1);
                        border: 1px solid;
                        border-color: rgba(73, 73, 73, 1);
                        &.black {
                            background-color: rgba(73, 73, 73, 1);
                            color: #fff;
                            border-color: rgba(73, 73, 73, 1);
                        }
                    }
                    .btn-icon {
                        width: 46rpx;
                        height: 46rpx;
                        &.tel {
                            width: 40rpx;
                            height: 40rpx;
                        }
                    }
                }
            }
        }
    }

    .empty-store {
        width: 100%;
        margin: 50rpx 0;
        text-align: center;

        .t {
            font-size: $fs-s;
            color: #938a8a;
        }
    }
}
</style>
