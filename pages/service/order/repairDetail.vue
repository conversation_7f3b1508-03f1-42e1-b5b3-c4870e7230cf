<!-- 工单详情页面 -->
<template>
    <view class="order_detail" :style="mix_diyStyle">
        <scroll-view class="container" scroll-y @scrolltolower="getData" v-if="allData && isShow">
            <view class="main_content">
                <view class="main_content_box">
                    <!-- 工单状态 start -->
                    <view class="order_state">
                        <block v-if="allData.state == 1 || allData.state == 2 || allData.state == 3 || allData.state == 4">
                            <view class="state_title await">
                                <text>{{ allData.stateValue }}</text>
                            </view>
                        </block>
                        <!-- 待评价 -->
                        <block v-if="allData.state == 7">
                            <view class="state_title await">
                                <image :src="imgUrl + 'order_new/success.png'" mode="aspectFit" style="width: 46rpx; height: 51rpx"></image>
                                <text>{{ allData.stateValue }}</text>
                            </view>
                        </block>
                        <!-- 取消工单，交易关闭 end-->
                        <block v-if="allData.state == 5 || allData.state == 6">
                            <view class="state_title await">
                                <text>{{ allData.stateValue }}</text>
                            </view>
                        </block>
                    </view>
                    <!-- 工单状态 end -->
                    <!-- 门店信息 start-->
                    <view class="buyer_info" v-if="!allData.isPickup">
                        <view class="info_det">
                            <view class="info_detail">
                                <view class="info_name">
                                    <text class="buyer_namer">{{ allData.serverStoreName }}</text>
                                    <text class="buyer_phone">{{ allData.receiverMobile }}</text>
                                </view>
                                <view class="info_address">{{ allData.address || '' }}</view>
                            </view>
                        </view>
                        <image :src="imgUrl + 'order-detail/order_address1.png'" mode="aspectFit" class="buyer_map"></image>
                    </view>
                    <!-- 门店信息 end-->

                    <!-- 工单信息  start-->
                    <view class="order_des">
                        <view class="order_des_title">{{ $L('工单信息') }}</view>
                        <view class="order_des_pre">
                            <text>{{ $L('工单类型') }}：</text>
                            <text>{{ $L('到店维修') }}</text>
                        </view>
                        <!-- <view class="order_des_pre">
                            <text>{{ $L('工单编号') }}：</text>
                            <text>{{ allData.orderSn }}</text>
                        </view> -->
                        <view class="order_des_pre">
                            <text>{{ $L('故障描述') }}：</text>
                            <text>{{ allData.issueDescribe || '' }}</text>
                        </view>
                        <view class="order_des_pre">
                            <text>{{ $L('故障图片') }}：</text>
                            <view class="img_list" v-if="allData.imgList && allData.imgList.length > 0">
                                <view class="list_item" v-for="(item, index) in allData.imgList" :key="index" @click="preView(index)">
                                    <image :src="item" mode="" class="image"></image>
                                </view>
                            </view>
                        </view>
                        <view class="order_des_pre">
                            <text>创建时间：</text>
                            <text>{{ allData.createTime }}</text>
                        </view>
                    </view>
                    <!-- 工单信息  end-->
                </view>
            </view>
        </scroll-view>
        <!-- 详情底部操作按钮 start-->
        <view class="order_det_bottom" v-if="bottomShow">
            <!-- 待评价 -->
             <!-- v-if="allData.state == 7" -->
            <block>
                <view class="go_pay" @click="goEvaluate()" v-if="allData.evaluateState != 3">{{ $L('评价') }}</view>
            </block>
            <!-- 工单取消 -->
            <block v-if="[1,2].includes(allData.state)">
                <view class="edit_address_btn" @click="cancleOrder()">{{ $L('取消工单') }}</view>
            </block>
        </view>
        <!-- 详情底部操作按钮 end-->
    </view>
</template>
<script>
import { mapState, mapMutations } from 'vuex';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue';
let startY = 0,
    moveY = 0,
    pageAtTop = true;
export default {
    components: {
        uniPopup,
        uniPopupDialog
    },
    data() {
        return {
            // 1=待处理, 2=已分配, 3=处理中, 4=已完成, 5=用户取消, 6=网点取消, 7=待评价
            imgUrl: getApp().globalData.imgUrl,
            repairApplyId: '', //工单号
            allData: {}, //工单详细信息
            cancelList: [], //取消原因列表
            current: '0', //取消原因当前点击的是第0项
            reasonId: -1, //取消原因当前点击的原因id
            isShow: false,
            orderLogs: [], //工单日志
            secInterval: '', //定时器
            state: '', //返跳转的时候用到这个参数
            commonBg: getApp().globalData.imgUrl + 'order/detail_bg.png', //公共背景图
            bottomShow: true, //底部按钮显示状态
            memberId: 0
        };
    },
    async onLoad(option) {
        //工单号
        this.repairApplyId = this.$Route.query.id;
    },
    onShow(option) {
        this.getOrderDetail();
    },
    onUnload() {
        if (this.secInterval) {
            clearInterval(this.secInterval);
        }
    },

    computed: {
        ...mapState(['hasLogin', 'userInfo', 'userCenterData'])
    },
    methods: {
        ...mapMutations(['saveChatBaseInfo']),
        //去门店
        goStore(storeid) {
            this.$Router.push({
                path: '/standard/store/shopHomePage',
                query: {
                    vid: storeid
                }
            });
        },
        // 预览图片
        preView(index) {
            let imgList = this.allData.imgList || [];
            if (imgList.length > 0) {
                uni.previewImage({
                    current: index,
                    urls: imgList
                });
            }
        },
        /**
         * 统一跳转接口,拦截未登录路由
         * navigator标签现在默认没有转场动画，所以用view
         */
        navTo(url) {
            if (!this.hasLogin) {
                let urls = this.$Route.path;
                const query = this.$Route.query;
                uni.setStorageSync('fromurl', {
                    url: urls,
                    query
                });
                url = '/pages/public/login';
            }
            this.$Router.push(url);
        },

        //获取工单详情信息
        getOrderDetail() {
            uni.showLoading({
                title: '加载中'
            });
            let param = {};
            param.url = 'v3/vehicle/front/repairApply/detail';
            param.method = 'get';
            param.data = {};
            param.data.vehicleRepairApplyId = this.repairApplyId;
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    uni.hideLoading();
                    let result = res.data || {};
                    // 处理工单图片信息
                    if (result.issueImg) {
                        result.imgList = result.issueImg.split(',');
                    }
                    this.allData = result;
                    this.isShow = true;
                } else if (res.state == 267) {
                    this.$api.msg(res.msg);
                    setTimeout(() => {
                        this.$Router.back(1);
                    }, 1500);
                } else {
                    this.$api.msg(res.msg);
                }
            });
        },

        //拨打电话
        goCall(store) {
            if (!store.servicePhone) {
                uni.showToast({
                    title: '该商家暂未设置电话',
                    icon: 'none'
                });
                return;
            }
            uni.makePhoneCall({
                phoneNumber: store.servicePhone
            });
        },
        // 取消工单
        cancleOrder(){
            this.confirmCancel()
        },
        //确定取消工单
        confirmCancel() {
            let that = this;
            uni.showModal({
                title: '提示',
                content: '确定取消该工单?',
                confirmColor: this.diyStyle_var['--color_main'],
                success: function (res) {
                    if (res.confirm) {
                        let param = {};
                        param.url = 'v3/vehicle/front/repairApply/cancel';
                        param.method = 'POST';
                        param.data = {};
                        param.data.vehicleRepairApplyId = that.repairApplyId;
                        that.$request(param)
                            .then((res) => {
                                if (res.state == 200) {
                                    that.$api.msg(res.msg);
                                    that.bottomShow = true;
                                    that.goRefresh();
                                } else {
                                    that.$api.msg(res.msg);
                                    that.bottomShow = true;
                                }
                            })
                            .catch((e) => {
                                //异常处理
                            });
                    } else if (res.cancel) {
                        that.bottomShow = true;
                    }
                }
            });
        },
        //更新当前页面方法
        goRefresh() {
            let pages = getCurrentPages();
            let currPage = pages[pages.length - 1]; //当前页面
            let beforePage = pages[pages.length - 2]; //上一页
            currPage.$vm.getOrderDetail(); //更新当前页面数据
            beforePage.$vm.loadData(); //更新上一页数据
        },
        //去评价页面
        goEvaluate(orderSn) {
            this.$Router.push({
                path: '/pages/service/order/publishEvaluation',
                query: {
                    id: this.repairApplyId
                }
            });
        }
    }
};
</script>
<style lang="scss">
page {
    background: #f2f2f2;
    width: 750rpx;
    margin: 0 auto;
}
.contact_phone {
    .item_con {
        flex: 1;

        &.border {
            border-right: 1px solid #eaeaea;
        }

        padding: 30rpx 0;

        image {
            width: 30rpx;
            height: 30rpx;
            margin-right: 20rpx;
        }

        text {
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #333333;
            line-height: 24rpx;
        }
    }
}

.order_mark {
    display: flex;
    padding: 5px 11px;
    box-sizing: border-box;
    justify-content: space-between;
    align-items: flex-start;
    font-size: 28rpx;
    font-family: PingFang SC;
    font-weight: 500;
    color: #343434;
    line-height: 32rpx;

    .order_mark_text {
        max-width: 450rpx;
        word-break: break-all;
        color: #999;
        text-align: right;
    }
}

.container {
    display: flex;
    flex: 1;
    width: 100%;
    min-height: 100vh;
    position: relative;
    background-color: #ffffff;

    .main_content {
        width: 100%;
        // min-height: calc(var(--status-bar-height) + 452rpx);
        min-height: 100vh;
        padding-top: 20rpx;
        /* app-1-start */
        /* #ifdef APP */
        padding-top: calc(var(--status-bar-height) + 20rpx);
        /* #endif */
        /* app-1-end */
        /* #ifdef H5 */
        padding-top: 20rpx;
        /* #endif */
        background-repeat: no-repeat;
        background-size: 750rpx 452rpx;
        padding-bottom: 180rpx;
        background-color: #f2f2f2;

        .main_content_box {
            padding: 0 20rpx;
        }

        .ladder_group {
            /* border-top: 10px solid #F5F5F5; */
            margin-top: 20rpx;
            /* width: 750rpx; */
            background: #fff;
            border-radius: 24rpx;

            .item {
                margin-left: 20rpx;
                width: 690rpx;
                box-sizing: border-box;
                padding-right: 20rpx;

                &.split {
                    border-bottom: 1rpx solid #f2f2f2;
                }

                .title {
                    margin-top: 22rpx;

                    .right_split {
                        width: 5rpx;
                        height: 26rpx;
                        background: var(--color_main_bg);
                        border-radius: 3rpx;
                        margin-right: 18rpx;
                    }

                    .content {
                        color: var(--color_price);
                        font-size: 28rpx;
                    }
                }

                .goods_amount {
                    margin-top: 20rpx;
                    color: #666666;
                    font-size: 26rpx;
                    line-height: 30rpx;
                }

                .need_pay_amount {
                    margin-top: 20rpx;
                    color: #2d2d2d;
                    font-size: 26rpx;
                    line-height: 30rpx;
                    margin-bottom: 25rpx;

                    .cur {
                        color: var(--color_price);
                    }
                }
            }
        }

        .order_state {
            /* #ifndef H5 */
            padding-top: 0;
            /* #endif */
            /* #ifdef H5 */
            /* padding-top: 96rpx; */
            /* #endif */
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            /* app-2-start */
            /* #ifdef APP-PLUS */
            /* height: 216rpx; */
            /* #endif */
            /* app-2-end */
            /* #ifndef APP-PLUS */
            /* height: 296rpx; */
            /* #endif */
            margin-bottom: 19rpx;
            padding-left: 20rpx;
            padding-right: 20rpx;

            .state_btn {
                width: 195rpx;
                height: 66rpx;
                background: var(--color_main_bg);
                border-radius: 33rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 28rpx;
                font-family: PingFang SC;
                font-weight: 500;
                color: #ffffff;
                cursor: pointer;
            }

            .state_title {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;

                image {
                    width: 45rpx;
                    height: 45rpx;
                    margin-right: 10rpx;
                }

                .text_1 {
                    color: var(--color_main);
                }

                text {
                    font-size: 38rpx;
                    font-family: PingFang SC;
                    font-weight: bold;
                    line-height: 50rpx;
                }
            }

            .state_reason {
                font-size: 24rpx;
                font-family: PingFang SC;
                font-weight: 500;
                color: #333;
                line-height: 32rpx;
                margin: 20rpx 0;
            }

            .state_remark {
                font-size: 24rpx;
                font-family: PingFang SC;
                font-weight: 500;
                color: #333;
                line-height: 32rpx;
            }

            .await {
                /* margin-bottom: 139rpx; */
            }

            .state_time_box {
                width: 100%;
                height: 85rpx;
                text-align: center;
            }

            .state_time {
                font-size: 28rpx;
                font-family: PingFang SC;
                font-weight: 500;
                color: #121212;
                margin: 22rpx 0 32rpx;
                line-height: 32rpx;

                .unit {
                    font-size: 24rpx;
                }
            }
        }

        .logistics_information {
            background: #f8f8f8;
            margin: 0 auto;
            padding: 40rpx 0 30rpx 20rpx;
            border-radius: 10rpx;
            border-bottom: 1rpx solid #f4f4f4;
            word-break: break-all;
            line-height: 40rpx;
            /* margin-bottom: 20rpx; */

            .logistics_image {
                width: 34rpx;
                height: 28rpx;
                margin-right: 18rpx;
            }

            .logistics_time {
                font-size: 26rpx;
                font-family: PingFang SC;
                font-weight: 500;
                color: #949494;
                line-height: 45rpx;
            }

            .logistics_des {
                font-size: 26rpx;
                font-family: PingFang SC;
                font-weight: 400;
                color: #333333;
                line-height: 39rpx;
                text-overflow: -o-ellipsis-lastline;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                line-clamp: 2;
                -webkit-box-orient: vertical;
                margin-top: 14rpx;
            }

            .logistics_des_right {
                display: flex;
                align-items: center;

                .right_down {
                    width: 46rpx;
                    height: 46rpx;
                }
            }
        }

        .logistics_information_type1 {
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 500;

            &.mutiple {
                background: #f8f8f8;
                padding: 20rpx;
                margin-bottom: 20rpx;
            }

            .deli_name {
                color: #666666;
            }

            .deli_value {
                color: #121212;
            }
        }

        .buyer_info {
            /* width: 710rpx; */
            background: #ffffff;
            /* box-shadow: 1rpx 3rpx 30rpx 0rpx rgba(102, 102, 102, 0.1); */
            border-radius: 20rpx;
            margin: 0 auto;
            display: flex;
            padding: 41rpx 20rpx 30rpx;
            box-sizing: border-box;

            .buyer_map {
                width: 34rpx;
                height: 32rpx;
                margin-right: 13rpx;
            }

            .info_det {
                display: flex;
                width: 100%;
                justify-content: space-between;

                .info_detail {
                    display: flex;
                    flex-direction: column;

                    .info_name {
                        display: flex;
                        align-items: flex-start;
                        flex-direction: column;
                        font-size: 28rpx;
                        font-family: PingFang SC;
                        font-weight: 500;
                        color: #2e2e2e;
                        line-height: 28rpx;
                        font-weight: bold;
                        row-gap: 15rpx;
                        .buyer_namer {
                            font-size: 32rpx;
                            color: #2e2e2e;
                        }

                        .buyer_phone {
                            color: #2e2e2e;
                        }
                    }

                    .info_address {
                        /* width: 560rpx; */
                        font-size: 28rpx;
                        font-family: PingFang SC;
                        font-weight: 500;
                        color: #666;
                        line-height: 39rpx;
                        margin-top: 24rpx;
                        word-break: break-all;
                        display: -webkit-box;
                        -webkit-box-orient: vertical;
                        -webkit-line-clamp: 2;
                        overflow: hidden;
                    }
                }
            }
        }

        .virtual_msg {
            width: 710rpx;
            background: #ffffff;
            box-shadow: 1rpx 3rpx 30rpx 0rpx rgba(102, 102, 102, 0.1);
            border-radius: 8px;
            margin: 0 auto;
            padding: 20px;

            .virtual_msg_item {
                font-size: 26rpx;
                line-height: 50rpx;
                color: #333;

                .msg_item_l {
                    white-space: nowrap;
                }

                .msg_item_r {
                    margin-left: 10rpx;
                    word-break: break-all;
                }
            }
        }

        .order_goods {
            .goods_list {
                padding: 20rpx 0 0 0;

                .store_item:not(:first-child) {
                    border-top: 20rpx solid #f5f5f5;
                }

                .store_item {
                    background: #fff;
                    border-radius: 20rpx;
                    /* padding: 20rpx; */
                }

                .goods_pre {
                    display: flex;
                    margin: 0 20rpx;
                    box-sizing: border-box;
                    padding: 20rpx 0;
                    border-bottom: 1rpx solid #f2f2f2;

                    .goods_image {
                        width: 200rpx;
                        height: 200rpx;
                        background: #f3f3f3;
                        border-radius: 14px;

                        image {
                            width: 200rpx;
                            height: 200rpx;
                            border-radius: 14rpx;
                        }
                    }

                    .vop_state {
                        position: absolute;
                        bottom: 20rpx;
                        right: 0px;
                        font-size: 24rpx;
                        color: #fc1c1c;
                    }

                    .goods-img {
                        background-size: cover;
                        background-position: center center;
                        background-repeat: no-repeat;
                        width: 174rpx;
                        height: 174rpx;
                        overflow: hidden;
                        background-color: #f8f6f7;
                        border-radius: 14rpx;
                        flex-shrink: 0;
                    }

                    .goods_pre_right {
                        display: flex;
                        justify-content: space-between;
                        width: 585rpx;

                        /* width: 100%; */
                        .goods_des {
                            margin-left: 25rpx;
                            padding-top: 8rpx;
                            box-sizing: border-box;
                            flex: 1;
                            display: flex;
                            flex-direction: column;
                            justify-content: space-between;
                            align-items: flex-start;

                            .goods_name {
                                font-size: 28rpx;
                                font-family: PingFang SC;
                                font-weight: 500;
                                color: #343434;
                                line-height: 39rpx;
                                text-overflow: -o-ellipsis-lastline;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                overflow: hidden;
                                white-space: nowrap;
                                text-overflow: ellipsis;
                                width: 450rpx;
                            }

                            .goods_content {
                                display: flex;
                                align-items: center;
                                justify-content: flex-start;
                                margin-top: 20rpx;
                            }

                            .goods_spec {
                                font-size: 24rpx;
                                font-family: PingFang SC;
                                font-weight: 400;
                                color: #949494;
                                line-height: 30rpx;
                                /* width: 280rpx; */
                                overflow: hidden;
                                text-overflow: ellipsis;
                            }
                        }

                        .goods_num_give {
                            color: #2d2d2d;
                            font-size: 26rpx;
                            /* font-weight: bold; */
                        }

                        .goods_prices {
                            display: flex;
                            width: 100%;
                            /* flex-direction: column; */
                            justify-content: space-between;
                            align-items: center;

                            .goods_price {
                                white-space: nowrap;

                                text {
                                    display: inline-block;
                                    font-family: PingFang SC;
                                    font-weight: 500;
                                    color: #343434;
                                    line-height: 30rpx;
                                }

                                .unit {
                                    font-size: 24rpx;
                                }

                                .price_int {
                                    font-size: 32rpx;
                                }

                                .price_decimal {
                                    font-size: 24rpx;
                                }
                            }

                            .goods_num {
                                font-size: 24rpx;
                                font-family: PingFang SC;
                                font-weight: 500;
                                color: #2d2d2d;
                                line-height: 30rpx;
                            }

                            .refund_btn {
                                /* padding: 12rpx 15rpx; */
                                width: 140rpx;
                                height: 56rpx;
                                box-sizing: border-box;
                                border: 1rpx solid #aaa;
                                border-radius: 28rpx;
                                font-size: 26rpx;
                                font-family: PingFang SC;
                                font-weight: 400;
                                color: #333333;
                                white-space: nowrap;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                            }

                            .refund_btn_o {
                                /* border: 1rpx solid var(--color_main);
									color: var(--color_main); */
                            }
                        }
                    }
                }
            }
        }

        .order_des {
            /* border-top: 20rpx solid #F5F5F5; */
            margin-top: 20rpx;
            /* padding: 29rpx 40rpx 0 20rpx; */
            padding: 35rpx 20rpx;
            box-sizing: border-box;
            background: #fff;
            border-radius: 20rpx;
            margin-bottom: 20rpx;

            &.look_des {
                padding-top: 37rpx;
            }

            .order_des_title {
                font-size: 30rpx;
                font-family: PingFang SC;
                font-weight: bold;
                color: #343434;
                line-height: 24rpx;
                margin-bottom: 35rpx;

                &.look_des_title {
                    margin-bottom: 28rpx;
                }
            }

            .order_send {
                overflow-x: auto;
                padding-bottom: 25rpx;

                /* border-bottom: 1rpx solid #F2F2F2; */
                .order_send_item {
                    color: #111111;
                    font-size: 28rpx;
                    font-family: PingFang SC;
                    font-weight: 500;
                    margin-right: 72rpx;
                    white-space: nowrap;

                    &.active {
                        color: #fc1c1c;
                        font-weight: bold;
                    }
                }
            }

            .noLogistics {
                height: 100rpx;
                background: #f8f8f8;
                border-radius: 10rpx;
                font-size: 26rpx;
                color: #999999;
                font-family: PingFang SC;
                font-weight: 500;
                display: flex;
                align-items: center;
                padding-left: 30rpx;
            }

            .logistics_information {
                padding: 20rpx 20rpx 30rpx;
            }

            .order_des_pre {
                font-size: 28rpx;
                font-family: PingFang SC;
                font-weight: 500;
                color: #9a9a9a;
                line-height: 28rpx;
                display: flex;
                margin-bottom: 19rpx;
                line-height: 39rpx;
                display: flex;
                justify-content: space-between;

                text:nth-child(1) {
                    color: #9a9a9a;
                }

                text:nth-child(2) {
                    width: 488rpx;
                    color: #343434;
                    word-break: break-all;
                }
                .img_list {
                    width: 488rpx;
                    display: flex;
                    flex-wrap: wrap;
                    gap: 10rpx;
                    .list_item{
                        --img-width: calc(100% / 3 - 10rpx);
                        width: var(--img-width);
                        // 图片宽高1:1
                        aspect-ratio: 1 / 1;
                        border-radius: 14px;
                        overflow: hidden;
                    }
                    .image {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }
                }
                &:last-child {
                    margin-bottom: 0;
                }

                .invoice {
                    height: 13px;
                    width: 52px;
                    display: inline-block;
                    text-align: justify;
                    vertical-align: top;
                }

                .invoice::after {
                    content: '';
                    display: inline-block;
                    width: 100%;
                    overflow: hidden;
                    height: 0;
                }

                .order_des_pre_tit {
                    flex-shrink: 0;
                }

                .order_voucher {
                    width: 254rpx;
                    height: 254rpx;
                    margin-right: 234rpx;
                }
            }
        }

        .recomment {
            background: #f5f5f5;
            box-sizing: border-box;
        }
    }
}

.cancel_popup {
    width: 100%;
    height: 700rpx;
    background: #ffffff;
    border-radius: 15rpx 15rpx 0 0;
    position: fixed;
    width: 100% !important;
    z-index: 20;
    bottom: 0;

    .popup_top {
        height: 100rpx;
        width: 100%;
        display: flex;
        padding: 0 39rpx;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1rpx solid #f8f8f8;

        text {
            font-size: 32rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #343434;
            line-height: 32rpx;
        }

        image {
            width: 30rpx;
            height: 30rpx;
        }
    }

    .cancel_list {
        padding-bottom: 128rpx;
        box-sizing: border-box;
        height: 600rpx;

        .cancle_pre {
            width: 100%;
            padding: 29rpx 40rpx;
            box-sizing: border-box;
            display: flex;
            justify-content: space-between;

            text {
                font-size: 28rpx;
                font-family: PingFang SC;
                font-weight: 500;
                color: #666666;
                line-height: 32rpx;
            }
        }
    }

    .cancel_popup_btn {
        position: fixed;
        bottom: 40rpx;
        z-index: 30;
        display: flex;
        width: 100%;
        justify-content: center;

        text:nth-child(1) {
            width: 334rpx;
            height: 70rpx;
            background: var(--color_vice_bg);
            border-radius: 35rpx 0 0 35rpx;
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #ffffff;
            line-height: 32rpx;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        text:nth-child(2) {
            width: 334rpx;
            height: 70rpx;
            background: var(--color_main_bg);
            border-radius: 0 35rpx 35rpx 0;
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #ffffff;
            line-height: 32rpx;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}

.store_name {
    padding-left: 20rpx;
    padding-bottom: 30rpx;
    padding-top: 30rpx;
    display: flex;
    align-items: center;

    image {
        width: 34rpx;
        height: 32rpx;
    }

    .store_name_text {
        font-size: 32rpx;
        color: #2d2d2d;
        font-weight: bold;
        margin-left: 10rpx;
    }

    .iconfont {
        // width: 13rpx;
        // height: 22rpx;
        font-size: 24rpx;
        margin-left: 10rpx;
    }
}

.store_price_info {
    padding-top: 5rpx;
    margin-top: 20rpx;
    border-radius: 20rpx;
    padding: 20rpx 0;
    background: #fff;

    .store_price_all {
        margin: 18rpx 0;
    }

    .freight {
        display: flex;
        padding: 10rpx 20rpx;
        box-sizing: border-box;
        justify-content: space-between;

        .freight_title {
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #343434;
            line-height: 30rpx;
        }

        .freight_price {
            font-size: 24rpx;
            font-family: PingFang SC;
            font-weight: bold;
            color: #2e2e2e;
            line-height: 30rpx;
        }
    }

    .actual_payment {
        display: flex;
        justify-content: space-between;
        padding: 0 20rpx;
        box-sizing: border-box;
        margin: 18rpx 0;

        .actual_payment_title {
            display: flex;
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 500;
            line-height: 30rpx;
            color: #343434;

            text:nth-child(2) {
                color: #949494;
            }
        }

        .actual_payment_price {
            font-size: 24rpx;
            font-family: PingFang SC;
            font-weight: bold;
            color: var(--color_price);
            line-height: 30rpx;

            text:nth-child(2) {
                font-size: 32rpx;
            }
        }
    }
}

.no_top {
    border-top: none;
    margin-top: 20rpx;
}

.Giveaway {
    width: 100rpx;
    height: 40rpx;
    border: 1rpx solid red;
    line-height: 40rpx;
    text-align: center;
    color: red;
    font-size: 25rpx;
    border-radius: 10rpx;
}

.order_det_bottom {
    position: fixed;
    bottom: 0;
    width: 100%;
    padding-bottom: env(safe-area-inset-bottom);
    height: calc(env(safe-area-inset-bottom) + 90rpx);
    background: #ffffff;
    box-shadow: 1rpx 1rpx 20rpx 0rpx rgba(86, 86, 86, 0.11);
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-right: 20rpx;
    box-sizing: border-box;
    column-gap: 20rpx;
    .edit_address_btn {
        width: 160rpx;
        height: 66rpx;
        border: 1rpx solid #aaa;
        border-radius: 33rpx;
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: 400;
        color: #343434;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .cancel_order {
        width: 160rpx;
        height: 66rpx;
        border: 1rpx solid #aaa;
        border-radius: 33rpx;
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: 400;
        color: #343434;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 10rpx;
    }

    .go_pay {
        width: 160rpx;
        height: 66rpx;
        background: var(--color_main);
        box-shadow: 1rpx 3rpx 15rpx 0rpx var(--color_halo);
        border-radius: 33rpx;
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: 400;
        color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .confirm_receipt {
        width: 160rpx;
        height: 66rpx;
        background: var(--color_vice);
        border-radius: 33rpx;
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: 400;
        color: #ffffff;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}
</style>
