<template>
    <view class="order-container">
        <view class="navigation-cards">
            <view v-for="(item, index) in orderTypes" :key="index" class="nav-card" @tap="navigateToOrderType(item.path)">
                <view class="icon-wrapper" :style="{ backgroundColor: item.bgColor }">
                    <text class="iconfont" :class="item.icon"></text>
                </view>
                <view class="card-content">
                    <view class="card-info">
                        <text class="card-title">{{ item.title }}</text>
                        <text class="card-desc">{{ item.description }}</text>
                    </view>
                    <text class="count" v-if="item.count > 0">{{ item.count }}</text>
                    <text class="iconfont icon-right"></text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            orderTypes: [
                {
                    title: '维修工单',
                    description: '查看您的车辆维修记录',
                    icon: 'icon-repair',
                    count: 0,
                    path: '/pages/service/order/repairList',
                    bgColor: '#E0F7FF'
                },
                {
                    title: '道路救援',
                    description: '紧急救援服务记录',
                    icon: 'icon-rescue',
                    count: 1,
                    path: '/pages/service/rescue-orders',
                    bgColor: '#FFF3D8'
                },
                {
                    title: '意见反馈',
                    description: '您提交的意见与建议',
                    icon: 'icon-feedback',
                    count: 0,
                    path: '/pages/service/feedback/list/index',
                    bgColor: '#E8F5E9'
                }
            ],
            recentActivities: [
                {
                    id: '1001',
                    type: 'mall',
                    title: '购买轮胎保养套餐',
                    time: '2025-06-25 15:30',
                    status: 'processing'
                },
                {
                    id: '1002',
                    type: 'rescue',
                    title: '高速道路救援服务',
                    time: '2025-06-22 09:15',
                    status: 'completed'
                }
            ]
        };
    },
    onLoad() {
        this.fetchOrderData();
    },
    methods: {
        fetchOrderData() {
            // TODO: 从API获取订单数据
            uni.showLoading({
                title: '加载中'
            });

            setTimeout(() => {
                uni.hideLoading();
            }, 500);
        },
        navigateToOrderType(path) {
            uni.navigateTo({
                url: path
            });
        },
        viewAllActivity() {
            uni.navigateTo({
                url: '/pages/user/all-activities'
            });
        },
        navigateToDetail(activity) {
            let path = '';
            switch (activity.type) {
                case 'mall':
                    path = `/pages/mall/order-detail?id=${activity.id}`;
                    break;
                case 'repair':
                    path = `/pages/service/repair-detail?id=${activity.id}`;
                    break;
                case 'rescue':
                    path = `/pages/service/rescue-detail?id=${activity.id}`;
                    break;
                case 'feedback':
                    path = `/pages/user/feedback-detail?id=${activity.id}`;
                    break;
            }

            if (path) {
                uni.navigateTo({ url: path });
            }
        },
        getActivityBgColor(type) {
            const typeMap = {
                mall: '#FFE8E0',
                repair: '#E0F7FF',
                rescue: '#FFF3D8',
                feedback: '#E8F5E9'
            };
            return typeMap[type] || '#F5F5F5';
        },
        getActivityIcon(type) {
            const iconMap = {
                mall: 'icon-shop',
                repair: 'icon-repair',
                rescue: 'icon-rescue',
                feedback: 'icon-feedback'
            };
            return iconMap[type] || 'icon-default';
        },
        getStatusText(status) {
            const statusMap = {
                pending: '待处理',
                processing: '处理中',
                completed: '已完成',
                canceled: '已取消'
            };
            return statusMap[status] || '未知状态';
        }
    }
};
</script>

<style lang="scss" scoped>
.order-container {
    min-height: 100vh;
    background-color: #f5f7fa;
    padding-bottom: 30rpx;

    .header {
        position: relative;
        height: 200rpx;
        overflow: hidden;

        .bg-image {
            position: absolute;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .header-content {
            position: relative;
            z-index: 2;
            display: flex;
            align-items: center;
            height: 100%;
            padding: 0 40rpx;

            .title {
                font-size: 36rpx;
                color: #ffffff;
                font-weight: bold;
            }
        }
    }

    .navigation-cards {
        padding: 20rpx;

        .nav-card {
            display: flex;
            align-items: center;
            background-color: #ffffff;
            border-radius: 16rpx;
            padding: 30rpx 24rpx;
            margin-bottom: 20rpx;
            box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

            .icon-wrapper {
                width: 80rpx;
                height: 80rpx;
                border-radius: 50%;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-right: 24rpx;

                .iconfont {
                    font-size: 40rpx;
                    color: #333;
                }
            }

            .card-content {
                flex: 1;
                display: flex;
                align-items: center;

                .card-info {
                    flex: 1;
                    display: flex;
                    flex-direction: column;

                    .card-title {
                        font-size: 32rpx;
                        color: #333;
                        font-weight: 500;
                        margin-bottom: 16rpx; // 增加标题与描述之间的间距
                    }

                    .card-desc {
                        font-size: 24rpx;
                        color: #999;
                    }
                }

                .count {
                    background-color: #ff6b6b;
                    color: white;
                    font-size: 24rpx;
                    height: 36rpx;
                    min-width: 36rpx;
                    border-radius: 18rpx;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 0 10rpx;
                    margin-right: 16rpx;
                }

                .icon-right {
                    font-size: 32rpx;
                    color: #ccc;
                }
            }

            &:active {
                opacity: 0.7;
            }
        }
    }

    .recent-activity {
        padding: 20rpx;
        background-color: #ffffff;
        border-radius: 16rpx;
        margin: 0 20rpx;

        .section-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 20rpx;
            font-size: 32rpx;
            font-weight: 500;
            border-bottom: 1rpx solid #f5f5f5;

            .view-all {
                font-size: 26rpx;
                color: #666;
                font-weight: normal;
            }
        }

        .empty-activity {
            padding: 60rpx 0;
            display: flex;
            flex-direction: column;
            align-items: center;

            image {
                width: 200rpx;
                height: 200rpx;
                margin-bottom: 20rpx;
            }

            text {
                color: #999;
                font-size: 28rpx;
            }
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 30rpx 0;
            border-bottom: 1rpx solid #f5f5f5;

            &:last-child {
                border-bottom: none;
            }

            .activity-icon {
                width: 60rpx;
                height: 60rpx;
                border-radius: 50%;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-right: 20rpx;

                .iconfont {
                    font-size: 30rpx;
                    color: #333;
                }
            }

            .activity-content {
                flex: 1;

                .activity-title {
                    font-size: 28rpx;
                    color: #333;
                    margin-bottom: 8rpx;
                }

                .activity-time {
                    font-size: 24rpx;
                    color: #999;
                }
            }

            .activity-status {
                font-size: 26rpx;
                padding: 4rpx 16rpx;
                border-radius: 6rpx;

                &.pending {
                    color: #ff9800;
                    background-color: rgba(255, 152, 0, 0.1);
                }

                &.processing {
                    color: #2196f3;
                    background-color: rgba(33, 150, 243, 0.1);
                }

                &.completed {
                    color: #4caf50;
                    background-color: rgba(76, 175, 80, 0.1);
                }

                &.canceled {
                    color: #9e9e9e;
                    background-color: rgba(158, 158, 158, 0.1);
                }
            }
        }
    }
}
</style>
