<template>
    <view class="content" :style="mix_diyStyle">
        <scroll-view class="navbar" scroll-x="true" :scroll-into-view="targetView">
            <view class="flex_row_start_center" style="height: 100%">
                <view
                    v-for="(item, index) in navList"
                    :key="index"
                    class="nav-item"
                    :id="'item' + item.state"
                    :class="{ current: tabCurrentIndex === index }"
                    @click="tabClick(index)"
                >
                    {{ item.text }}
                </view>
            </view>
        </scroll-view>
        <swiper :current="tabCurrentIndex" class="swiper-box" duration="300" @change="changeTab" :disable-touch="clientH5">
            <swiper-item class="tab-content" v-for="(tabItem, tabIndex) in navList" :key="tabIndex">
                <view v-if="triggered" :class="{ loading: true, show_loading: triggered }">
                    <image :src="imgUrl + 'loading_icon.gif'" mode="aspectFit"></image>
                </view>
                <scroll-view
                    class="list-scroll-content"
                    scroll-y
                    @scrolltolower="scrollLoadData"
                    :lower-threshold="150"
                    @scroll="(e) => handleScroll(e, tabIndex)"
                    :scroll-top="scrollToTop"
                    :refresher-enabled="false"
                    :refresher-threshold="45"
                    refresher-background="#f8f8f8"
                    :refresher-triggered="triggered"
                    @refresherpulling="onPulling"
                    :refresher-default-style="refreshStyle"
                    @refresherrefresh="onRefresh"
                >
                    <!-- 工单列表 -->
                    <template v-if="tabItem.orderList.length > 0">
                        <view v-for="(item, index) in tabItem.orderList" :key="index" class="order-item flex_column_start_start" @click.stop="goOrderDetail(item)">
                            <view class="i-top">
                                <!-- <text class="order_sn">工单编号：{{item.orderSn}}</text> -->
                                <view class="store_name" @click.stop="">
                                    <image class="store_logo" :src="imgUrl + 'goods_detail/store_logo.png'"></image>
                                    <text class="store_name_text">{{ item.serverStoreName }}</text>
                                    <!-- <text class="iconfont iconziyuan11"></text> -->
                                </view>
                                <text class="state" :style="{ color: item.stateTipColor }">{{ item.stateValue }}</text>
                            </view>
                            <!-- 循环更改 -->
                            <view class="goods-box flex_row_between_center">
                                <!-- <view class="left flex_row_start_start">
                                    <view class="goods-img" :style="{ backgroundImage: 'url(' + item.issueImg.split(',')[0] + ')' }"></view>
                                </view> -->
                                <view class="issue_desc">{{ item.issueDescribe }}</view>
                            </view>
                            <view class="action-box">
                                <!-- 待评价工单可以评价 -->
                                <button v-if="item.state == 7" class="action-btn recom flex_row_center_center" @click.stop="remainEvaluated(item.id)">
                                    {{ $L('评价') }}
                                </button>
                                <!-- 待处理工单可以取消 -->
                                <button v-if="item.state == 1" class="action-btn flex_row_center_center" @click.stop="confirmCancel(item)">{{ $L('取消工单') }}</button>

                                <!-- 已取消、全部评价完成工单可以删除工单 -->
                                <!-- <button v-if="item.state == 5" class="action-btn flex_row_center_center" @click.stop="delOrder(item.orderSn)">
                                    {{ $L('删除工单') }}
                                </button> -->
                            </view>
                        </view>
                    </template>
                    <view v-if="tabItem.loadingState != 'first_loading' && tabItem.orderList.length == 0" class="empty_part flex_column_start_center">
                        <image :src="imgUrl + 'empty_orders.png'" />
                        <text>{{ $L('空空如也~') }}</text>
                    </view>
                    <view v-else :class="scrollTop > 0 && scrollCurrent > 0 ? 'scroll_loading' : ''">
                        <loadingState :state="tabItem.loadingState" />
                    </view>
                </scroll-view>
            </swiper-item>
        </swiper>
    </view>
</template>

<script>
import loadingState from '@/components/loading-state.vue';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue';
import { mapState } from 'vuex';
export default {
    components: {
        loadingState,
        uniPopup,
        uniPopupDialog
    },
    data() {
        return {
            //状态: 1=待处理, 2=已分配, 3=处理中, 4=已完成, 5=用户取消, 6=网点取消, 7=待评价
            imgUrl: getApp().globalData.imgUrl,
            tabCurrentIndex: 0,
            navList: [
                {
                    state: 0,
                    text: this.$L('全部'),
                    loadingState: 'first_loading',
                    orderList: [],
                    current: 1 //分页
                },
                {
                    state: 1,
                    text: this.$L('待处理'),
                    loadingState: 'first_loading',
                    orderList: [],
                    current: 1 //分页
                },
                {
                    state: 3,
                    text: this.$L('处理中'),
                    loadingState: 'first_loading',
                    orderList: [],
                    current: 1 //分页
                },
                {
                    state: 4,
                    text: this.$L('已完成'),
                    loadingState: 'first_loading',
                    orderList: [],
                    current: 1 //分页
                },
                {
                    state: 5,
                    text: '已取消',
                    loadingState: 'first_loading',
                    orderList: [],
                    current: 1 //分页
                },
                {
                    state: 7,
                    text: this.$L('待评价'),
                    loadingState: 'first_loading',
                    orderList: [],
                    current: 1 //分页
                }
            ],
            stopPullDownRefresh: false, //是否下拉刷新中
            current: '0', //取消原因当前点击的是第0项
            reasonId: -1, //取消原因当前点击的原因id
            cancelList: [], //取消原因列表
            curOrderSn: '', //当前工单的工单号
            isHasMore: true,
            pn: 1,
            recGoodsList: [],
            isloading: 'first_loading',
            isShow: false,
            recommendShow: false, //推荐商品是否显示
            ifOnShow: false,
            selOrderSn: '',
            showState: false,
            keyword: '', //搜索关键词
            refresh: false, //从搜索页返回后是否需要刷新
            timeState: 0, //工单列表时间筛选条件
            storeId: '',

            //下拉刷新start
            triggered: false,
            refreshStyle: 'white',
            refresherEnabled: false,
            _freshing: false,

            targetView: 'item0',

            scrolling: false, //是否进入滚动事件处理中
            scrollTop: 0, //滚动条滚动高度
            scrollToTop: 0, //返回列表页时当前tab滚动到的高度
            scrollCurrent: 0 //返回列表页时当前tab滚动到的页数
        };
    },
    computed: {
        ...mapState(['userInfo', 'hasLogin']),
        clientH5() {
            // #ifdef H5
            return true;
            // #endif
            // #ifndef H5
            return false;
            // #endif
        }
    },
    onLoad(options) {
        setTimeout(() => {
            uni.setNavigationBarTitle({
                title: this.$L('我的工单')
            });
        }, 0);

        /**
         * 修复app端点击除全部工单外的按钮进入时不加载数据的问题
         * 替换onLoad下代码即可
         */
        if (this.$Route.query.state) {
            this.tabCurrentIndex = +this.$Route.query.state;
        } else {
            this.tabCurrentIndex = 0;
        }

        this.targetView = 'item01';
        this.$nextTick(() => {
            this.targetView = `item${this.tabCurrentIndex}`;
        });
        this.initialList();

        //app-1-start
        // #ifdef APP-PLUS
        this.refreshStyle = 'none';
        // #endif
        //app-1-end

        // #ifdef APP-PLUS||MP-WEIXIN
        this.refresherEnabled = true;
        // #endif
    },

    onShow() {
        // 返回列表页判断是否进入滚动定位-start
        if (this.scrollTop > 0 && this.navList[this.tabCurrentIndex].current > 1) {
            this.scrollToTop = 0;
            this.scrollCurrent = this.navList[this.tabCurrentIndex].current;
        }
        // 返回列表页判断是否进入滚动定位-end
        if (this.showState || this.refresh) {
            let index = this.tabCurrentIndex;
            if (index > 0) {
                this.navList[0].loadingState = 'first_loading';
                this.navList[0].orderList = [];
                this.navList[0].current = 1;
            }
            this.initialList();
            this.showState = false;
            this.recommendShow = false;
            this.refresh = false;
        }
    },

    //下拉刷新
    onPullDownRefresh() {
        this.stopPullDownRefresh = true; //下拉刷新状态
        this.initialList();
    },
    methods: {
        initialList() {
            let index = this.tabCurrentIndex;
            let navItem = this.navList[index];
            const userInfo = uni.getStorageSync('userInfo');
            if (this.hasLogin || userInfo) {
                uni.showLoading({
                    title: this.$L('加载中'),
                    icon: 'none'
                });
                navItem.loadingState = 'first_loading';
                navItem.orderList = [];
                navItem.current = 1;
                this.loadData();
            } else {
                this.$refs.loginPop.openLogin('no_replace');
                navItem.loadingState = 'no_more_data';
            }
        },
        //获取工单列表
        loadData(source) {
            //将工单挂载到tab列表下,起到缓存的效果，避免多次请求
            let index = this.tabCurrentIndex;
            let navItem = this.navList[index];
            let state = navItem.state;

            if (navItem.loadingState === 'loading') {
                //防止重复加载
                return;
            }
            this.getOrderList();
        },
        //滚动触底事件
        scrollLoadData() {
            if (this.scrolling || this.scrollCurrent > 0) return;
            let index = this.tabCurrentIndex;
            let navItem = this.navList.filter((item) => item.state == index)[0];
            if (navItem.loadingState != 'no_more_data') {
                this.scrolling = true;
                this.getOrderList();
            }

            if (navItem.loadingState == 'no_more_data') {
                this.getData();
            }
        },
        //滚动条滚动事件
        handleScroll(e, tabIndex) {
            if (this.tabCurrentIndex == tabIndex && !this.scrollCurrent) {
                this.scrollTop = e.target.scrollTop;
            }
        },
        //下拉刷新事件start
        onPulling(e) {
            this.triggered = true;
        },
        onRefresh() {
            if (this._freshing) return;
            this._freshing = true;
            let index = this.tabCurrentIndex;
            let navItem = this.navList[index];
            navItem.current = 1;
            navItem.loadingState = 'refreshing';
            setTimeout(() => {
                this.loadData();
            }, 1000);
        },
        onRestore() {},
        onAbort() {},
        //下拉刷新事件end

        //此方法只有删除工单，取消工单等需要从列表中删除工单时调用，其余获取工单列表请调用loadData
        getOrderList() {
            let _this = this;
            let index = this.tabCurrentIndex;
            let navItem = this.navList[index];
            let state = navItem.state;

            let param = {};
            param.url = 'v3/vehicle/front/repairApply/list';
            param.data = {};
            param.data.pageSize = 10;
            param.data.current = navItem.current;
            if (this.keyword) {
                param.data.keyword = this.keyword;
            }
            navItem.loadingState = navItem.loadingState == 'first_loading' ? navItem.loadingState : 'loading';
            //状态处理
            if (navItem.state == 0) {
                //全部工单
            } else if (navItem.state == 1) {
                param.data.state = 1; //待处理
            } else if (navItem.state == 3) {
                param.data.state = 3; //处理中
            } else if (navItem.state == 4) {
                param.data.state = 4; //已完成
            } else if (navItem.state == 5) {
                param.data.state = 5; //已取消
            } else if (navItem.state == 7) {
                param.data.state = 7; //待评价
            }

            this.$request(param).then((res) => {
                if (this.scrolling) {
                    this.scrolling = false;
                }
                if (this._freshing) {
                    this.triggered = false;
                }
                this._freshing = false;
                if (res.state == 200) {
                    if (navItem.current == 1) {
                        navItem.orderList = res.data.list;
                    } else {
                        navItem.orderList = navItem.orderList.concat(res.data.list);
                    }
                    let hasMore = this.$checkPaginationHasMore(res.data.pagination); //是否还有数据
                    if (hasMore) {
                        navItem.current++;
                        if (!(this.scrollCurrent > 0 && this.scrollCurrent >= navItem.current)) {
                            navItem.loadingState = 'allow_loading_more';
                        }
                        this.recommendShow = false;
                    } else {
                        navItem.loadingState = 'no_more_data';
                        this.recommendShow = true;
                    }
                    uni.hideLoading();

                    // 返回列表页判断是否进入滚动定位-start
                    if (this.scrollCurrent > 0) {
                        if (hasMore && this.scrollCurrent >= navItem.current) {
                            navItem.loadingState = 'loading';
                            this.getOrderList();
                        } else if ((!hasMore && this.scrollCurrent == navItem.current) || (hasMore && this.scrollCurrent < navItem.current)) {
                            //滚动到最后一个需要获取的页面时，再给滚动高度赋值
                            this.scrollCurrent = 0;
                            setTimeout(() => {
                                _this.scrollToTop = _this.scrollTop;
                            }, 200);
                        }
                    }
                    // 返回列表页判断是否进入滚动定位-end
                } else {
                    navItem.loadingState = 'no_more_data';
                    this.$api.msg(res.msg);
                }
                if (this.stopPullDownRefresh) {
                    this.stopPullDownRefresh = false;
                    uni.stopPullDownRefresh();
                }
            });
        },
        //刷新当前列表
        refreshOrderList() {
            let index = this.tabCurrentIndex;
            let navItem = this.navList.filter((item) => item.state == index)[0];
            navItem.current = 1;
            this.getOrderList();
        },
        //swiper 切换
        changeTab(e) {
            this.tabCurrentIndex = e.target.current;
            this.isHasMore = true;
            this.pn = 1;
            this.recGoodsList = [];
            this.isloading = 'first_loading';
            this.loading = false;
            this.loadData('tabChange');
        },
        //顶部tab点击
        tabClick(index) {
            this.tabCurrentIndex = index;
            this.isHasMore = true;
            this.pn = 1;
            this.isloading = 'first_loading';
            this.loading = false;
            this.recGoodsList = [];
            // #ifdef MP-ALIPAY
            this.loadData('tabChange');
            // #endif
        },

        //跳转工单详情页
        goOrderDetail(item) {
            this.showState = true;
            this.$Router.push({
                path: '/pages/service/order/repairDetail',
                query: {
                    id: item.id
                }
            });
        },
        //删除工单操作
        delOrder(orderSn) {
            let that = this;
            uni.showModal({
                title: that.$L('提示'),
                content: that.$L('确定删除该工单?'),
                confirmColor: this.diyStyle_var['--color_main'],
                success: function (res) {
                    if (res.confirm) {
                        let param = {};
                        param.url = 'v3/business/front/orderOperate/delete';
                        param.method = 'POST';
                        param.data = {};
                        param.data.orderSn = orderSn;
                        that.$request(param)
                            .then((res) => {
                                if (res.state == 200) {
                                    that.refreshOrderList();
                                    that.refreshOrder();
                                    that.$api.msg(res.msg);
                                } else {
                                    that.$api.msg(res.msg);
                                }
                            })
                            .catch((e) => {
                                //异常处理
                            });
                    } else if (res.cancel) {
                    }
                }
            });
        },
        //确定取消工单
        confirmCancel() {
            let that = this;
            uni.showModal({
                title: that.$L('提示'),
                content: that.$L('确定取消该工单?'),
                success: function (res) {
                    if (res.confirm) {
                        let param = {};
                        param.url = 'v3/vehicle/front/repairApply/cancel';
                        param.method = 'POST';
                        param.data = {};
                        param.data.vehicleRepairApplyId = that.repairApplyId;
                        that.$request(param)
                            .then((res) => {
                                if (res.state == 200) {
                                    that.refreshOrderList();
                                    that.refreshOrder();
                                    that.$api.msg(res.msg);
                                } else {
                                    that.$api.msg(res.msg);
                                }
                            })
                            .catch((e) => {
                                //异常处理
                            });
                    }
                }
            });
        },
        //更新当前页面方法
        goRefresh() {
            let pages = getCurrentPages();
            let currPage = pages[pages.length - 1]; //当前页面
            let index = this.tabCurrentIndex;
            let navItem = this.navList[index];
            navItem.loadingState = 'first_loading';
            navItem.orderList = [];
            navItem.current = 1;
            currPage.loadData(); //更新当前页面数据
        },
        // 评价
        remainEvaluated(orderSn) {
            this.showState = true;
            this.$Router.push({
                path: '/order/publishEvaluation',
                query: {
                    orderSn
                }
            });
        },
        //当全部的部分工单操作后，其他状态的列表要更新
        refreshOrder() {
            let index = this.tabCurrentIndex;
            if (index == 0) {
                this.navList[1].loadingState = 'first_loading';
                this.navList[2].loadingState = 'first_loading';
                this.navList[3].loadingState = 'first_loading';
                this.navList[4].loadingState = 'first_loading';
            }
        }
    }
};
</script>

<style lang="scss">
.content {
    background: $bg-color-split;
    height: 100%;
    width: 750rpx;
    margin: 0 auto;
}

.tab-content {
    position: relative;
}

.swiper-box {
    height: calc(100vh - 40px);
}

swiper {
    height: calc(100vh - 40px);
}

button::after {
    border: none;
}

.list-scroll-content {
    height: 100%;

    .scroll_loading {
        width: 100%;
        position: fixed;
        top: 80rpx;
        left: 50%;
        transform: translateX(-50%);
    }
}

.label_con {
    position: relative;
    left: 0;

    .act_label {
        height: 36rpx;
        border-radius: 15rpx;
        line-height: 36rpx;
        padding: 0 14rpx;
    }

    .preSale {
        // width: 38px;
        // height: 14px;
        background: linear-gradient(90deg, #ec0093 0%, #ff085b 100%);
        border-radius: 4rpx;
        color: #fff;
        font-size: 24rpx;
    }

    .pinGroup {
        background: linear-gradient(45deg, #ff6000 0%, #ff9c00 100%);
        border-radius: 4rpx;
        color: #fff;
        font-size: 24rpx;
    }

    .ladder {
        background: linear-gradient(22deg, #fe901e 0%, #fead28 100%);
        color: #fff;
        font-size: 24rpx;
        border-radius: 4rpx;
    }

    .seckill {
        background: linear-gradient(to right, #fc5300, #ff1353);
        color: #fff;
        font-size: 24rpx;
        border-radius: 4rpx;
    }
}

.sea_input_part {
    display: flex;
    align-items: center;
    width: 750rpx;
    height: 88rpx;
    background-color: #fff;
    margin: 0 auto;
    padding-left: 10rpx;
    padding-right: 10rpx;

    .search_center {
        overflow: hidden;
        display: flex;
        align-items: center;
        flex: 1;
        height: 65rpx;
        border-radius: 32.5rpx;
        background-color: #f5f5f5;
        border: 1rpx solid #f5f5f5;
        margin-left: 10rpx;
        padding-left: 20rpx;

        .search_icon {
            width: 30rpx;
            height: 30rpx;
            margin-right: 22rpx;
        }

        .sea_input {
            flex: 1;
            height: 65rpx;
            font-size: 28rpx;
            color: #333;
            background-color: #f5f5f5;
        }
    }

    .clear_content {
        width: 45rpx !important;
        height: 45rpx !important;
        margin-right: 15rpx !important;
    }

    .filter {
        min-width: 66rpx;
        font-size: 28rpx;
        text-align: center;
        margin-left: 10rpx;
    }

    &:after {
        position: absolute;
        content: '';
        left: 0;
        bottom: 0;
        width: 100%;
        height: 1rpx;
        background-color: #eee;
        transform: scaleY(0.5);
    }
}

.navbar {
    display: flex;
    height: 80rpx;
    padding: 0 5px;
    background: #fff;
    position: relative;
    z-index: 10;
    overflow-x: scroll;
    width: 750rpx;
    .nav-item {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        font-size: 32rpx;
        color: $main-font-color;
        position: relative;
        white-space: nowrap;
        padding: 0 28rpx;
        &.current {
            color: var(--color_main);
            font-size: 32rpx;
            font-weight: bold;

            &:after {
                content: '';
                position: absolute;
                left: 50%;
                bottom: 0;
                transform: translateX(-50%);
                width: 35rpx;
                height: 8rpx;
                background-color: var(--color_main);
                border-radius: 4rpx;
            }
        }
    }
}

.uni-swiper-item {
    height: auto;
}

.order-item {
    width: 100%;
    padding-left: 20rpx;
    background: #fff;
    margin-top: 20rpx;

    .i-top {
        display: flex;
        align-items: center;
        padding-right: 20rpx;
        position: relative;
        width: 100%;
        margin-top: 30rpx;
        border-bottom: 1rpx solid #f2f2f2;
        padding-bottom: 30rpx;

        .store_name {
            flex: 1;
            display: flex;
            align-items: center;

            image {
                width: 34rpx;
                height: 32rpx;
                transform: scale(0.9);
            }

            .store_name_text {
                font-size: 26rpx;
                color: #2d2d2d;
                font-weight: bold;
                margin-left: 10rpx;
            }

            .store_logo_right {
                width: 13rpx;
                height: 22rpx;
                margin-left: 10rpx;
            }
            .iconfont {
                font-size: 20rpx;
                color: #949494;
                margin-left: 10rpx;
            }
        }

        .state {
            font-size: 26rpx;
            color: var(--color_price);
        }
    }

    .goods-box {
        position: relative;
        padding: 20rpx 0;
        box-sizing: border-box;
        width: 100%;

        .issue_desc {
            width: 100%;
            height: 100%;
            font-size: $fs-base;
            height: 90rpx;
            line-height: 1.4;
            color: #2d2d2d;
            @extend .line2;
        }
    }
    .action-box {
        display: flex;
        justify-content: flex-end;
        align-items: flex-start;
        height: 70rpx;
        position: relative;
        padding-right: 20rpx;
        width: 100%;
        padding-bottom: 20rpx;
    }

    .action-btn {
        width: 125rpx;
        height: 50rpx;
        margin: 0;
        margin-left: 10rpx;
        padding: 0;
        text-align: center;
        line-height: 50rpx;
        font-size: 24rpx;
        color: $main-font-color;
        background: #fff;
        border-radius: 25rpx;
        border: 1rpx solid #eeeeee;

        &:after {
            border: none;
        }

        &.recom {
            color: #fff;
            background: var(--color_main);
            border: none;
        }
    }
}

.empty_part {
    padding-top: 108rpx;

    image {
        width: 380rpx;
        height: 280rpx;
    }

    text {
        color: $main-third-color;
        font-size: 26rpx;
        margin-top: 57rpx;
    }

    button {
        width: 245rpx;
        height: 66rpx;
        background: var(--color_halo);
        border-radius: 33rpx;
        color: var(--color_main);
        font-size: 30rpx;
        font-weight: bold;
        margin-top: 29rpx;
        border: none;
    }

    uni-button:after {
        border-radius: 200rpx;
        border-color: #fff;
    }
}

.cancel_popup {
    width: 100%;
    height: 700rpx;
    background: #ffffff;
    border-radius: 15rpx 15rpx 0 0;
    position: fixed;
    width: 100% !important;
    z-index: 20;
    bottom: 0;

    .popup_top {
        height: 100rpx;
        width: 100%;
        display: flex;
        padding: 0 39rpx;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1rpx solid #f8f8f8;

        text {
            font-size: 32rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #343434;
            line-height: 32rpx;
        }

        image {
            width: 30rpx;
            height: 30rpx;
        }
    }

    .cancel_list {
        // padding-bottom: 128rpx;
        box-sizing: border-box;
        height: 468rpx;
        z-index: 150;

        .cancle_pre {
            width: 100%;
            padding: 29rpx 40rpx;
            box-sizing: border-box;
            display: flex;
            justify-content: space-between;

            text {
                font-size: 28rpx;
                font-family: PingFang SC;
                font-weight: 500;
                color: #666666;
                line-height: 32rpx;
            }
        }
    }

    .cancel_popup_btn {
        position: fixed;
        bottom: 40rpx;
        z-index: 30;
        display: flex;
        width: 100%;
        justify-content: center;

        text:nth-child(1) {
            width: 334rpx;
            height: 70rpx;
            background: var(--color_vice);
            border-radius: 35rpx 0 0 35rpx;
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #ffffff;
            line-height: 32rpx;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        text:nth-child(2) {
            width: 334rpx;
            height: 70rpx;
            background: var(--color_main);
            border-radius: 0 35rpx 35rpx 0;
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #ffffff;
            line-height: 32rpx;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}

.Giveaway {
    width: 100rpx;
    height: 40rpx;
    border: 1rpx solid red;
    line-height: 40rpx;
    text-align: center;
    color: red;
    font-size: 25rpx;
    border-radius: 10rpx;
}
</style>
