<template>
	<view :style="mix_diyStyle">
		<view class="fixed_top_status_bar flex_row_start_center" :style="{ height: menuButtonHeight, top: menuButtonTop, width: menuButtonleft, color: '#000' }">
			<view class="left_back" @tap="toBack">
				<text class="iconfont iconziyuan2" style="top: 20rpx"></text>
			</view>
			<view class="status_bar_title">
				<text>品牌墙</text>
			</view>
		</view>
		<image class="fullWidth" :src="imgUrl + 'brand/branding_wall.jpg'" mode="widthFix"></image>
		<!-- <view class="screen-bg" :style="{backgroundImage:`url(${imgUrl}jcshop/mp-qq/brand/branding_wall.jpg)`}"></view> -->
	</view>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
export default {
	data() {
		return {
			imgUrl: getApp().globalData.imgUrl,
			//#ifdef MP
			menuButtonHeight: uni.getMenuButtonBoundingClientRect().height + 'px',
			menuButtonTop: uni.getMenuButtonBoundingClientRect().top + 'px',
			menuButtonleft: uni.getMenuButtonBoundingClientRect().left + 'px'
			//#endif
		};
	},
	onLoad() {
		let _this = this;
	},
	onShow() {},
	methods: {
		needLogin() {
			this.$refs.loginPop.openLogin();
		},
		confirmLogin() {
			this.$refs.loginPop.close();
		},
		toBack(){
			this.$Router.back(1)
		}
	}
};
</script>

<style lang="scss">
.fixed_top_status_bar {
	position: fixed;
	//app-5-start
	/* #ifdef APP-PLUS */
	height: var(--status-bar-height);
	/* #endif */
	//app-5-end
	/* #ifndef APP-PLUS */
	height: 0;
	/* #endif */
	top: 0;
	left: 0;
	right: 0;
	z-index: 99;
	background: transparent;
	// 顶部taber
	padding-left: 4%;
	.left_back {
		margin-right: 50rpx;
	}
}

page {
	background: #f5f5f5;
}
.fullWidth {
	display: block;
	width: 100%;
}
</style>
