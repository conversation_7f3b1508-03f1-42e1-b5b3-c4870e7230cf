<template>
    <view :style="mix_diyStyle">
        <view class="page" :style="{ backgroundImage: pageBgImage }" v-show="loaded">
            <!-- 自定义顶部导航栏 -->
            <!-- #ifdef MP -->
            <view class="fixed_top_status_bar">
                <view class="top_status_bar_seat" :style="{ height: topBar.navBarHeight + tabsHeight + 'px' }"></view>
                <view class="custom-nav-taber" :style="{ paddingTop: topBar.offsettop - 5 + 'px', backgroundImage: pageBgImage }">
                    <!-- 搜索栏 -->
                    <view :style="{ height: topBar.menuHeight + 'px', lineHeight: topBar.menuHeight + 'px', width: `calc(100% - ${topBar.menuWidth}px)` }" class="search_tap_bar">
                        <view class="search_wrap" @click="toSearchPage" :style="{ borderRadius: topBar.menuHeight / 2 + 'px' }">
                            <image class="search_icon" :src="imgUrl + 'search3.png'" mode="" />
                            <text class="search_text">立马新活动</text>
                        </view>
                        <view class="right_btns">
                            <image class="add_icon" v-if="noticeList.length" :src="imgUrl + 'notice.png'" mode="" @click="toNotice" />
                            <image class="add_icon" v-else :src="imgUrl + 'notice_null.png'" mode="" @click="toNotice" />
                            <image class="add_icon" :src="imgUrl + 'add_black.png'" mode="" @click="doRelease" />
                        </view>
                    </view>
                    <!-- tab切换 -->
                    <view class="top_tabs">
                        <view
                            v-for="tab in top_tabs"
                            :key="tab.value"
                            :class="{ tab_item: true, active: cur_tab == tab.value }"
                            :style="{ backgroundImage: cur_tab == tab.value ? navBg : '' }"
                        >
                            <text :class="{ tab_item_text: true, custom: navBg }" :style="{ color: cur_tab == tab.value ? navTextColor : '#000' }" @click="changeTab(tab)">
                                {{ tab.name }}
                            </text>
                        </view>
                    </view>
                    <!-- tab切换 end-->
                </view>
            </view>
            <!-- #endif -->
            <!-- #ifdef H5 -->
            <view class="fixed_top_status_bar">
                <view class="top_status_bar_seat"></view>
                <view class="custom-nav-taber" :style="{ backgroundImage: pageBgImage }">
                    <view class="search_tap_bar">
                        <view class="search_wrap" @click="toSearchPage">
                            <image class="search_icon" :src="imgUrl + 'search3.png'" mode="" />
                            <text class="search_text">立马新活动</text>
                        </view>
                        <view class="right_btns">
                            <image class="add_icon" :src="imgUrl + 'notice.png'" mode="" @click="toNotice" />
                            <image class="add_icon" :src="imgUrl + 'add_black.png'" mode="" @click="toArt" />
                        </view>
                    </view>
                    <!-- tab切换 -->
                    <view class="top_tabs">
                        <view
                            v-for="tab in top_tabs"
                            :key="tab.value"
                            :class="{ tab_item: true, active: cur_tab == tab.value }"
                            :style="{ backgroundImage: cur_tab == tab.value ? navBg : '' }"
                        >
                            <text :class="{ tab_item_text: true, custom: navBg }" :style="{ color: cur_tab == tab.value ? navTextColor : '#000' }" @click="changeTab(tab)">
                                {{ tab.name }}
                            </text>
                        </view>
                    </view>
                    <!-- tab切换 end-->
                </view>
            </view>
            <!-- #endif -->
            <!-- 自定义顶部导航栏 end-->
            <view class="container">
                <!-- 推荐列表 -->
                <scroll-view
                    v-show="cur_tab == 1"
                    refresher-enabled
                    :refresher-triggered="refresherTriggered.recommend"
                    scroll-y
                    class="scroll-view"
                    @scrolltolower="scrolltolower('RecommendRef')"
                    @refresherrefresh="reloadData('RecommendRef')"
                    :style="{ height: scrollViewHeight }"
                >
                    <recommend ref="RecommendRef" @reloadfinished="reloadFinished" />
                </scroll-view>
                <!-- 关注列表 -->
                <scroll-view
                    v-show="cur_tab == 2"
                    refresher-enabled
                    :refresher-triggered="refresherTriggered.followList"
                    scroll-y
                    class="scroll-view"
                    @scrolltolower="scrolltolower('FollowListRef')"
                    @refresherrefresh="reloadData('FollowListRef')"
                    :style="{ height: scrollViewHeight }"
                >
                    <followList ref="FollowListRef" @reloadfinished="reloadFinished" />
                </scroll-view>
                <!-- 活动列表 -->
                <scroll-view
                    v-show="cur_tab == 3"
                    refresher-enabled
                    :refresher-triggered="refresherTriggered.activityList"
                    scroll-y
                    class="scroll-view"
                    @scrolltolower="scrolltolower('ActivityListRef')"
                    @refresherrefresh="reloadData('ActivityListRef')"
                    :style="{ height: scrollViewHeight }"
                >
                    <activityList ref="ActivityListRef" @reloadfinished="reloadFinished" />
                </scroll-view>

                <!-- 咨询列表 -->
                <scroll-view
                    v-show="cur_tab == 4"
                    refresher-enabled
                    :refresher-triggered="refresherTriggered.newsList"
                    scroll-y
                    class="scroll-view"
                    @scrolltolower="scrolltolower('NewsListRef')"
                    @refresherrefresh="reloadData('NewsListRef')"
                    :style="{ height: scrollViewHeight }"
                >
                    <newsList ref="NewsListRef" @reloadfinished="reloadFinished" />
                </scroll-view>
            </view>
        </view>
        <!-- 发布按钮弹层 start -->
        <releasePop ref="releasePop" offsetBottom="120rpx"></releasePop>
        <!-- 发布按钮弹层 end -->
        <!-- 登录弹框 -->
        <loginPop ref="loginPop" @confirmLogin="confirmLogin"></loginPop>
        <!-- 登录弹框 -->
        <!-- #ifdef MP -->
        <privacyPop ref="priPop"></privacyPop>
        <!-- #endif -->
    </view>
</template>

<script>
import releasePop from '@/components/releaseLive/releasePop.vue';
import recommend from './components/recommend.vue';
import activityList from './components/activityList.vue';
import newsList from './components/newsList.vue';
import followList from './components/followList.vue';
import { customTabbarShow, customTabbarHide } from '@/utils/common';
// #ifdef MP
import privacyPop from '@/components/privacy-pop.vue';
// #endif
import { mapState, mapMutations } from 'vuex';
export default {
    components: {
        // #ifdef MP
        privacyPop,
        // #endif
        releasePop,
        recommend,
        followList,
        activityList,
        newsList
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            loaded: false, // 页面加载状态
            themeConfig: {}, // 主题配置
            pageBgImage: `url(${getApp().globalData.imgUrl}default_bg.jpg)`,
            navBg: '', // 导航栏背景
            navTextColor: '#000', // 导航栏文字颜色
            statusbarActive: false,
            // #ifdef MP
            topBar: {}, // 顶部导航栏信息
            navBarHeight: '',
            menuRight: '',
            menuButtonHeight: uni.getMenuButtonBoundingClientRect().height + 'px',
            menuButtonTop: uni.getMenuButtonBoundingClientRect().top + 'px',
            tabsHeight: uni.rpx2px(100), // 顶部tab高度
            // #endif
            noticeList: [], // 通知列表
            tabsConfig: {
                // 主题设置
                bgImg: '',
                color: '#FFF'
            },
            cur_tab: 1, // 当前选中的tab
            top_tabs: [
                {
                    value: 1,
                    name: '推荐',
                    component: 'RecommendRef'
                },
                {
                    value: 2,
                    name: '关注',
                    component: 'FollowListRef'
                },
                {
                    value: 3,
                    name: '活动',
                    component: 'ActivityListRef'
                },
                {
                    value: 4,
                    name: '资讯',
                    component: 'NewsListRef'
                }
            ],
            refresherTriggered: {
                recommend: false,
                followList: false,
                activityList: false,
                newsList: false
            }, // 刷新状态
            deco_data: [], //首页装修数据
            home_is_show_top_cat: true, //是否显示顶部分类，默认显示
            home_page_img: [],
            width: '',
            height: '',
            shareData: {},
            privacyPolicyHtml: '', //隐私政策协议的富文本
            privacyPolicyModel: false,
            triggered: false
        };
    },

    computed: {
        ...mapState(['hasLogin', 'userInfo', 'userCenterData']),
        scrollViewHeight() {
            // #ifdef MP
            return uni.getSystemInfoSync().windowHeight - this.topBar.navBarHeight - this.tabsHeight + 'px';
            // #endif
            // #ifdef H5
            return '';
            // #endif
        }
    },
    onLoad() {
        let _this = this;
        this.getThemeConfig();
        // #ifdef MP
        // 判断用户隐私授权
        if (!getApp().globalData.allow_privacy) {
            if (wx.getPrivacySetting == undefined) {
                //微信低版本不适配该授权方法
                getApp().globalData.allow_privacy = true;
            } else {
                wx.getPrivacySetting({
                    success(res) {
                        if (res.needAuthorization) {
                            _this.$refs.priPop.PrivacyProtocol = {
                                needAuthorization: res.needAuthorization,
                                privacyContractName: res.privacyContractName
                            };
                            _this.$refs.priPop.open();
                        } else {
                            getApp().globalData.allow_privacy = true;
                        }
                    }
                });
            }
        }
        // #endif

        //app-2-start
        // #ifdef APP-PLUS
        this.getAppInfo(0); //获取线上APP版本信息  参数type 0自动检查  1手动检查（手动检查时，之前取消更新的版本也会提示出来）
        // #endif
        //app-2-end

        // #ifdef MP-WEIXIN
        let url = getApp().globalData.apiUrl.substring(0, getApp().globalData.apiUrl.length - 1);
        this.$sldStatEvent({
            behaviorType: 'pv',
            pageUrl: url + '/pages/index/index',
            referrerPageUrl: ''
        });
        // 胶囊按钮位置信息
        const systemInfo = uni.getWindowInfo();
        // 导航栏高度 = 状态栏高度 + 44
        const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
        this.navBarHeight = systemInfo.statusBarHeight + 44 + 'px';
        this.menuRight = systemInfo.screenWidth - menuButtonInfo.right + 'px';
        let globalData = {};
        globalData.navBarHeight = systemInfo.statusBarHeight + 44;
        globalData.menuRight = systemInfo.screenWidth - menuButtonInfo.right;
        globalData.menuHeight = menuButtonInfo.height;
        globalData.menuWidth = menuButtonInfo.width;
        globalData.offsettop = menuButtonInfo.top;
        this.topBar = globalData;
        // #endif
    },
    onShow() {
        customTabbarShow(0);
        // 获取未读消息
        if (this.hasLogin) {
            let param = {};
            param.url = 'v3/msg/front/msg/msgListNum';
            param.method = 'GET';
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    this.noticeList = res.data;
                } else {
                    this.$api.msg(res.msg);
                }
            });
        }
    },
    onHide() {
        customTabbarHide(0);
    },
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {
        const { shareData } = this;
        return shareData;
    },

    onShareTimeline: function () {
        const { shareData } = this;
        return shareData;
    },
    onTabItemTap(e) {
        // this.$refs.homeCustom.restoreTab();
    },

    methods: {
        ...mapMutations(['login', 'setUserCenterData']),
        // 获取主题配置
        getThemeConfig() {
            this.$request({
                url: 'v3/video/front/video/special/theme/config',
                method: 'GET'
            })
                .then((res) => {
                    if (res.state == 200) {
                        if (res.data && res.data.id) {
                            this.getThemeConfig = res.data;
                            const content = res.data.content ? JSON.parse(res.data.content) : {};
                            this.pageBgImage = `url(${content.bgImage})`;
                            this.navBg = content.navImage ? `url(${content.navImage})` : 'tansparent';
                            this.navTextColor = content.textColor || '#000';
                        }
                        this.loaded = true; // 设置页面加载完成
                    }
                })
                .catch((e) => {
                    console.error('获取主题配置失败:', e);
                    this.loaded = true; // 设置页面加载完成
                });
        },
        // scrollview 下拉刷新数据 key: scrollview组件的ref
        reloadData(key) {
            // 设置刷新状态为true，显示刷新动画
            const refresherKey = this.getRefresherKey(key);
            if (refresherKey) {
                this.refresherTriggered[refresherKey] = true;
            }
            console.log('reloadData', key);
            this.$refs[key].reload();
        },

        // 获取刷新状态对应的键名
        getRefresherKey(refKey) {
            const keyMap = {
                RecommendRef: 'recommend',
                FollowListRef: 'followList',
                ActivityListRef: 'activityList',
                NewsListRef: 'newsList'
            };
            return keyMap[refKey] || null;
        },

        // scrollView 触发下拉刷新完成回调 key: scrollview组件的ref
        reloadFinished(key) {
            console.log('reloadFinished', key);
            // 根据不同的组件ref设置对应的刷新状态为false
            const refresherKey = this.getRefresherKey(key + 'Ref') || key.toLowerCase();
            if (this.refresherTriggered.hasOwnProperty(refresherKey)) {
                this.refresherTriggered[refresherKey] = false;
            }
            // 如果传入的key直接是refresherTriggered的键名
            if (this.refresherTriggered.hasOwnProperty(key)) {
                this.refresherTriggered[key] = false;
            }
        },
        // scrollView 触底加载
        scrolltolower(key) {
            this.$refs[key].loadMore();
        },
        // 切换tab
        changeTab(tab) {
            this.cur_tab = tab.value;
            this.$refs[tab.component]?.initData();
        },
        // 跳转到搜索页面
        toSearchPage() {
            const page = getCurrentPages();
            let curPage = page[page.length - 1];
            if (curPage.route == 'extra/tshou/index/index') {
                this.$Router.push('/extra/tshou/search/search');
            } else {
                // this.$Router.push('/pages/search/search');
                this.$Router.push('/newPages/search/search');
            }
        },
        // 跳转到通知页面
        toNotice() {
            this.$Router.push('/standard/chat/list');
        },
        // 去种草中心
        doRelease() {
            if (!this.hasLogin) {
                this.needLogin();
                return;
            }
            this.$refs.releasePop.open();
        },
        //app-3-start
        // #ifdef APP-PLUS
        //获取线上APP版本信息
        getAppInfo(type) {
            const platform = uni.getDeviceInfo().platform; //本机设备操作系统  android / ios

            //请求获取最新版本
            //这里自行请求API获取版本信息 建议传入操作系统标识，返回本机对应的操作系统最新版本信息，也就是安卓的返回就是安卓的版本信息  ios返回就是ios的版本信息
            let param = {};
            param.url = 'v3/system/front/setting/getSettings';
            param.method = 'GET';
            param.data = {};
            if (platform == 'android') {
                //android
                param.data.names =
                    'app_android_hot_edition,' + //热更新版本号
                    'app_android_hot_link,' + //热更新地址
                    'app_android_hot_tip,' + //更新提示内容
                    'app_android_package_edition,' + //整包升级版本号
                    'app_android_package_link'; //整包升级地址
            } else if (platform == 'ios') {
                //ios
                param.data.names =
                    'app_ios_hot_edition,' + //热更新版本号
                    'app_ios_hot_link,' + //热更新地址
                    'app_ios_hot_tip,' + //更新提示内容
                    'app_ios_package_edition,' + //整包升级版本号
                    'app_ios_package_link'; //整包升级地址
            } else {
                return false;
            }

            this.$request(param).then((res) => {
                if (res.state == 200) {
                    let update_info = {
                        version: res.data[3], //整包版本
                        versionCode: res.data[0], //热更新版本号
                        now_url: '', //更新链接
                        version_url: res.data[4], //整包下载地址
                        versionCode_url: res.data[1], //热更新下载地址
                        silent: 0, //是否是静默更新
                        force: 1, //是否是强制更新
                        net_check: 1, //非WIfi是否提示
                        note: res.data[2] //更新内容
                    };
                    checkUpdate(update_info, type).then((res) => {
                        if (res.data) {
                            plus.nativeUI.toast(res.data.msg);
                        }
                    }); ///检查更新 线上版本号与本地版本号做对比,如果需要更新 根据静默、强制、wifi等信息执行静默更新或跳转到升级页面
                }
            });
        },
        // #endif
        //app-3-end

        needLogin() {
            this.$refs.loginPop.openLogin();
        },
        confirmLogin() {
            this.$refs.loginPop.close();
        },

        async loadData() {
            uni.showLoading({
                title: this.$L('加载中...')
            });
            // #ifdef H5
            this.client = 'h5';
            // #endif

            //app-4-start
            // #ifdef APP-PLUS
            switch (uni.getDeviceInfo().platform) {
                case 'android':
                    this.client = 'android';
                    break;
                case 'ios':
                    this.client = 'ios';
                    break;
                default:
                    break;
            }
            // #endif
            //app-4-end

            // #ifdef MP
            this.client = 'weixinXcx';
            // #endif
            let param = {};
            param.url = 'v3/system/front/deco/index?os=' + this.client;
            param.method = 'GET';
            this.$request(param).then((res) => {
                uni.stopPullDownRefresh();
                if (res.state == 200) {
                    if (JSON.stringify(res.data) == '{}') {
                        this.deco_data = null;
                        uni.hideLoading();
                        return;
                    }
                    if (res.data.data != '') {
                        this.deco_data = JSON.parse(res.data.data);
                        // console.log(JSON.parse(res.data.data))
                    } else {
                        this.deco_data = null;
                    }

                    uni.stopPullDownRefresh();
                    // #ifdef MP
                    this.shareData = {
                        title: res.data.siteName,
                        path: '/pages/index/index',
                        imageUrl: res.data.xcxImage
                    };
                    // #endif
                    if (res.data.showTip != null) {
                        this.home_page_img = JSON.parse(res.data.showTip);
                        const { windowWidth, windowHeight } = uni.getSystemInfoSync();
                        this.width = this.home_page_img[0].width || windowWidth * 0.75 * 1.8;
                        this.height = this.home_page_img[0].height || windowHeight * 0.56 * 1.8;
                    } else {
                        this.home_page_img = [];
                    }

                    if (this.deco_data && this.deco_data.length != undefined && this.deco_data.length > 0) {
                        this.home_is_show_top_cat = this.deco_data[0].type == 'top_cat_nav' ? true : false;
                    }
                    uni.hideLoading();
                }
                uni.hideLoading();
            });
        },
        //阻止模态框下页面滚动
        moveHandle() {},
        //获取购物车数据
        getCartNum() {
            if (this.hasLogin) {
                let param = {};
                param.url = 'v3/business/front/cart/cartNum';
                param.method = 'GET';
                param.data = {};
                // param.data.key = this.userInfo.access_token;
                this.$request(param)
                    .then((res) => {
                        if (res.state == 200) {
                            if (res.data > 0) {
                                uni.setTabBarBadge({
                                    index: 3,
                                    text: res.data.toString()
                                });
                            } else {
                                uni.hideTabBarRedDot({
                                    index: 3
                                });
                            }
                        } else {
                            this.$api.msg(res.msg);
                        }
                    })
                    .catch((e) => {
                        //异常处理
                    });
            } else {
                this.getNoLoginCartNum();
            }
        },
        //获取未登录，购物车数量
        getNoLoginCartNum() {
            let cartNum = 0;
            let cart_list = uni.getStorageSync('cart_list');
            if (cart_list && cart_list.storeCartGroupList) {
                cart_list.storeCartGroupList.map((item) => {
                    item.promotionCartGroupList.map((item1) => {
                        item1.cartList.map((item2) => {
                            cartNum++;
                        });
                    });
                });
            }
            if (cartNum > 0) {
                uni.setTabBarBadge({
                    index: 3,
                    text: cartNum.toString()
                });
            } else {
                uni.hideTabBarRedDot({
                    index: 3
                });
            }
        }
    }
};
</script>

<style lang="scss">
page {
    background: $bg1;
    .page {
        min-height: 100vh;
        background-repeat: no-repeat;
        background-position: top center;
        background-size: 100% auto;
        // 背景图片固定，不滚动
        background-attachment: fixed;
    }
    .fixed_top_status_bar {
        .custom-nav-taber {
            position: fixed;
            z-index: 10;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
            top: 0;
            // #ifdef MP
            left: 0;
            width: 100%;
            // #endif
            // #ifdef H5
            padding-top: 80rpx;
            box-sizing: content-box;
            left: 50%;
            width: 750rpx;
            transform: translateX(-50%);
            // #endif
            background-repeat: no-repeat;
            background-position: top center;
            background-size: 100% auto;
        }
        .search_tap_bar {
            display: flex;
            align-items: center;
            width: 100%;
            // #ifdef MP
            justify-content: flex-start;
            padding-left: 30rpx;
            column-gap: 30rpx;
            // #endif
            // #ifdef H5
            justify-content: space-between;
            width: 100%;
            padding: 0 4%;
            height: 100%;
            // #endif
        }
        .search_wrap {
            display: flex;
            align-items: center;
            // #ifdef MP
            width: 350rpx;
            padding: 0 20rpx;
            // #endif
            // #ifdef H5
            flex: 1;
            height: 80rpx;
            border-radius: 40rpx;
            padding: 0 20rpx;
            // #endif
            border: 1px solid #fff;
            background: rgba(255, 255, 255, 0.6);
            box-sizing: border-box;
        }
        .search_icon {
            /* #ifdef MP-WEIXIN */
            width: 36rpx;
            height: 36rpx;
            /* #endif */
            /* #ifdef H5 */
            width: 40rpx;
            height: 40rpx;
            /* #endif */
            object-fit: contain;
        }
        .search_text {
            font-size: 26rpx;
            /* #ifdef H5 */
            font-size: 30rpx;
            /* #endif */
            color: #6e6e6e;
            margin-left: 20rpx;
        }
        .right_btns {
            display: flex;
            align-items: center;
            width: 180rpx;
            column-gap: 30rpx;
            // #ifdef MP
            justify-content: flex-start;
            // #endif
            /* #ifdef H5 */
            justify-content: flex-end;
            /* #endif */
        }
        .add_icon {
            object-fit: contain;
            /* #ifdef MP-WEIXIN */
            width: 40rpx;
            height: 40rpx;
            /* #endif */
            /* #ifdef H5 */
            width: 45rpx;
            height: 45rpx;
            /* #endif */
        }
        .top_status_bar_seat {
            // #ifdef MP
            width: 100%;
            // #endif
            /* app-2-start */
            // #ifdef H5
            width: 750rpx;
            height: 280rpx;
            // #endif
        }
    }
    .top_tabs {
        width: 92%;
        margin: 0 auto;
        // height: 80rpx;
        height: 100rpx;
        margin-top: 20rpx;
        background-color: transparent;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .tab_item {
            width: 100rpx;
            height: 100rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            background-position: center;
            background-repeat: no-repeat;
            background-size: 140%;

            &_text {
                position: relative;
                display: inline-block;
                font-size: 30rpx;
                color: #161616;
                padding-bottom: 20rpx;
                line-height: 1;
                &.custom {
                    padding-bottom: 0;
                }
                &:not(.custom)::after {
                    content: '';
                    height: 2px;
                    border-radius: 1px;
                    width: 100%;
                    position: absolute;
                    left: 0;
                    bottom: 0rpx;
                    background-color: transparent;
                    transform: scaleX(0);
                    transition: transform 0.3s ease-in-out;
                }
            }
            &.active {
                .tab_item_text:not(.custom)::after {
                    background-color: #bf0000;
                    transform: scaleX(1);
                }
            }
        }
    }
    .container {
        width: 100%;
        position: relative;
        .scroll-view {
            /* #ifdef MP-WEIXIN */

            /* #endif */
            /* #ifdef H5 */
            height: calc(100vh - 280rpx);
            /* #endif */
        }
    }
}

.privacy_policy_model {
    width: 750rpx;
    height: 100vh;
    background: rgba(0, 0, 0, 0.7);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999999;
    overflow: hidden;

    .privacy_model {
        width: 625rpx;
        height: 70vh;
        background: #ffffff;
        border-radius: 15rpx;
        margin: 0 auto;
        margin-top: 17vh;
        padding-bottom: 24rpx;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .privacy_model_title {
            height: 104rpx;
            font-size: 34rpx;
            font-family: Source Han Sans CN;
            font-weight: bold;
            color: #000000;
            line-height: 104rpx;
            text-align: center;
            border-bottom: 1rpx solid #f2f2f2;
        }

        .privacy_policy {
            width: 625rpx;
            height: 50vh;
            padding: 0 43rpx;
            box-sizing: border-box;
        }

        .privacy_model_confirm {
            width: 542rpx;
            height: 72rpx;
            background: linear-gradient(90deg, var(--color_main) 0%, #ff7918 100%);
            border-radius: 36rpx;
            font-size: 28rpx;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #ffffff;
            line-height: 72rpx;
            text-align: center;
            margin: 20rpx auto;
        }

        .privacy_model_cancel {
            font-size: 28rpx;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #ff991e;
            text-align: center;
            line-height: 28rpx;
        }
    }
}
</style>
