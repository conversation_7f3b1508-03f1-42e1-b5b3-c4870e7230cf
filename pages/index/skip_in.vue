<template>
	<view :style="mix_diyStyle">
		<div class="main">
			<!-- {{ info }} -->
		</div>
	</view>
</template>
<script>
// 小程序跳转中间页，处理登录等流程
import { mapState, mapMutations, mapActions } from 'vuex';
export default {
	data() {
		return {
			info: '',
			adminSerAvatar: '',
			adminSerName: '',
			adminTel: '',
			hasLogin: false
		};
	},
	onLoad(options) {
		let userInfo = uni.getStorageSync('userInfo') || '';
		if (userInfo.access_token) {
			this.hasLogin = true;
		}
		const option = options || getCurrentPages()[0].options;
		console.log(option, this.hasLogin);
		// this.info = JSON.stringify(option)
		if (option && option.path) {
			const url = option.path;
			const query = option.query ? JSON.parse(option.query.replace(/(['”])?([a-zA-Z0-9_]+)(['"])?:/g, '"$2": ')) : '';
			// 需要登录的跳转，先判断登录状态
			if (option.needLogin && !this.hasLogin) {
				this.$Router.pushTab({ path: '/pages/user/user' });
			}
			// console.log('不需要登录，跳转地址为：', option.path);
			this.$Router.replace({ path: url, query: query });
			return;
		}
		this.$Router.replace({ path: '/pages/index/index' });
	}
};
</script>
<style lang="scss">
.main {
	width: 100%;
	height: 100vh;
}
</style>
