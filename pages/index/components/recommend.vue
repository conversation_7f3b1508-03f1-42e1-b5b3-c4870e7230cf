<template>
    <view class="recommend-content">
        <view class="home_swiper">
            <uv-swiper
                height="420rpx"
                radius="20px"
                :list="swiperList"
                keyName="imageUrl"
                indicator
                indicatorMode="line"
                @click="swiperClick"
                circular
                :autoplay="true"
            ></uv-swiper>
        </view>
        <!-- 图标导航 -->
        <view class="circle-nav" v-if="tab_list.length > 0">
            <view class="circle-item" v-for="(tab, index) in tab_list" :key="tab.adsId" @click="tabClick(tab)">
                <view :class="['circle-icon', `w_${tab_list.length}`]" :style="{ backgroundImage: `url(${tab.imageUrl})` }"></view>
                <text class="tab-name">{{ tab.adsName }}</text>
            </view>
        </view>
        <!-- 作品列表-->
        <view class="hot-explore">
            <view class="video-item" v-for="(item, index) in videoList" :key="item.videoId">
                <VideoItem :detail="item" @changeFollow="changeFollow" />
            </view>
        </view>
        <!-- 空状态展示 -->
        <view class="empty-state" v-if="videoList.length === 0 && !loading">
            <image class="empty-image" :src="imgUrl + 'empty_default.png'" mode="aspectFit"></image>
            <view class="empty-text">空空如也什么也没有哦~</view>
        </view>

        <!-- 加载状态 -->
        <view class="loading-more" v-if="loading">
            <view class="loading-icon"></view>
            <text class="loading-text">加载中...</text>
        </view>

        <!-- 底部提示 -->
        <view class="bottom-tip" v-if="videoList.length > 0 && !hasmore">
            <text>~ 已经到底啦 ~</text>
        </view>
        <!-- 底部安全高度填充 -->
        <view class="tab-page-safe-bottom"></view>
    </view>
</template>

<script>
import { checkPageHasMore, initNum } from '@/utils/live';
import VideoItem from '@/components/videoItem.vue';
export default {
    name: 'Recommend',
    components: {
        VideoItem
    },
    emits: ['reloadfinished'],
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl, // 图片前缀
            swiperList: [], // @type 1探索-推荐广告位；2探索-轮播；3探索-四大按钮；4商城-轮播；5商城-四大按钮；6车辆-广告位；7我的-广告位
            // 社区内容
            loading: true, // 是否正在加载数据
            //是否还有数据，用于页面展示
            hasmore: true,
            pageSize: 10,
            current: 1,
            //主题列表
            themeList: [],
            //短视频列表
            videoList: [],
            adv_data: [], // 广告数据
            tab_list: [] // 轮播下方Tab数据
        };
    },
    mounted() {
        this.reload();
    },
    created() {
        // 更新浏览次数
        console.log('组件创建');
    },
    destroyed() {
        console.log('组件销毁');
    },
    methods: {
        // 初始化数据 主要提供给父组件调用
        initData() {
            // 此组件不需要处理
        },
        reload() {
            this.current = 1;
            this.hasmore = true;
            this.getVideoList('reload'); // 重新加载短视频列表
            this.advConfig(1); // 获取轮播配置
            this.advConfig(2); // 获取轮播下方Tab配置
            this.advConfig(3); // 获取广告配置
        },
        loadMore() {
            if (this.hasmore) {
                this.getVideoList();
            }
        },
        // 切换关注状态
        changeFollow(item) {
            const authorId = item.authorId;
            // 更新视频列表中的关注状态
            const newVideoList = this.videoList.map((video) => {
                return {
                    ...video,
                    isFollow: video.authorId === authorId ? item.isFollow : video.isFollow
                };
            });
            this.videoList = newVideoList;
            // 强制更新组件
            this.$forceUpdate();
        },
        //获取广告配置
        advConfig(type = 1) {
            // platform 1:小程序 2:H5
            let param = {};
            param.url = 'v3/video/front/ads/list';
            param.method = 'GET';
            param.data = {
                platform: 1,
                type
            };
            // #ifdef H5
            param.data.platform = 2;
            // #endif
            this.$request(param)
                .then((res) => {
                    if (res.state == 200) {
                        if (type == 2) {
                            this.swiperList = res.data.list;
                        } else if (type == 1) {
                            this.adv_data = res.data.list;
                        } else if (type == 3) {
                            this.tab_list = res.data.list;
                        }
                    }
                })
                .catch((err) => {
                    console.error('获取广告配置失败:', err);
                });
        },
        //获取短视频列表
        getVideoList(type) {
            this.loading = true;
            let { videoList, hasmore, themeList } = this;
            let param = {};
            param.data = {};
            param.data.pageSize = this.pageSize;
            param.data.current = this.current;
            param.url = 'v3/video/front/video/list';
            param.method = 'GET';
            this.$request(param)
                .then((res) => {
                    uni.stopPullDownRefresh();
                    if (res.state == 200) {
                        if (this.current == 1) {
                            videoList = res.data.videoList;
                            themeList = res.data.themeList;
                        } else {
                            videoList = videoList.concat(res.data.videoList);
                        }
                        if (checkPageHasMore(res.data.pagination)) {
                            this.current++;
                        } else {
                            this.hasmore = false;
                            hasmore = false;
                        }
                    }
                    this.loading = false;
                    this.hasmore = hasmore;
                    this.videoList = videoList;
                    this.themeList = themeList;
                    if (type == 'reload') {
                        console.log('$emit reloadfinished', type);
                        this.$emit('reloadfinished', 'recommend');
                    }
                })
                .catch((err) => {
                    this.loading = false;
                    this.hasmore = false;
                    console.error('获取短视频列表失败:', err);
                    if (type == 'reload') {
                        this.$emit('reloadFinished', 'recommend');
                    }
                });
        },
        //进入播放页面
        goVideoPlay(item, index) {
            this.navIndex = index;
            if (item.videoType == 1) {
                this.$videoPlayNav({
                    video_id: item.videoId,
                    author_id: item.authorId
                });
            } else {
                this.$Router.push({
                    path: '/extra/graphic/graphicDetail',
                    query: {
                        video_id: item.videoId,
                        author_id: item.authorId
                    }
                });
            }
        },
        // 轮播图点击事件
        swiperClick(index) {
            const currentItem = this.swiperList[index];
            if (!currentItem || !currentItem.link) {
                console.warn('轮播图数据不完整:', currentItem);
                return;
            }

            const { link, adsName, adsId, type } = currentItem;
            console.log('轮播图点击:', currentItem);

            // 如果 link 是字符串，尝试解析为对象
            let navInfo;
            if (typeof link === 'string') {
                try {
                    navInfo = JSON.parse(link);
                } catch (e) {
                    // 如果解析失败，可能是直接的URL
                    navInfo = {
                        type: 2, // H5页面
                        path: link
                    };
                }
            } else {
                navInfo = link;
            }

            this.navigatorTo(navInfo);
        },
        // Tab点击事件
        tabClick(tab) {
            const { link, adsName, adsId, type } = tab;
            // 如果 link 是字符串，尝试解析为对象
            let navInfo;
            if (typeof link === 'string') {
                try {
                    navInfo = JSON.parse(link);
                } catch (e) {
                    // 如果解析失败 不操作
                    navInfo = { type: 0 };
                }
            } else {
                navInfo = link;
            }
            this.navigatorTo(navInfo);
        },
        navigatorTo(nav) {
            // 参数验证
            if (!nav || typeof nav !== 'object') {
                console.warn('导航参数无效:', nav);
                return;
            }

            // type 0、无操作，1、小程序页面 2、H5  3、其他小程序 4、抽奖活动
            if (nav.type == 0) return;

            if (nav.type == 1) {
                // 小程序页面跳转
                if (!nav.path) {
                    console.warn('小程序页面路径不能为空');
                    return;
                }
                // 把path中？后的参数转为对象
                const path = nav.path.split('?')[0];
                let query = {};

                // 解析查询参数
                if (nav.path.includes('?')) {
                    const queryString = nav.path.split('?')[1];
                    if (queryString) {
                        const params = queryString.split('&');
                        params.forEach((param) => {
                            const [key, value] = param.split('=');
                            if (key && value) {
                                query[decodeURIComponent(key)] = decodeURIComponent(value);
                            }
                        });
                    }
                }

                this.$Router.push({ path, query });
            }

            if (nav.type == 2) {
                // H5页面跳转
                if (!nav.path) {
                    console.warn('H5页面路径不能为空');
                    return;
                }
                this.$Router.push({
                    path: '/pages/public/webView',
                    query: { url: nav.path }
                });
            }

            if (nav.type == 3) {
                // 跳转其他小程序
                uni.navigateToMiniProgram({
                    appId: nav.appId || '',
                    shortLink: nav.shortLink || '',
                    path: nav.path || '',
                    extraData: nav.extraData || {},
                    envVersion: nav.envVersion,
                    success: (res) => {
                        console.log('跳转小程序成功:', res);
                    },
                    fail: (err) => {
                        console.error('跳转小程序失败:', err);
                    }
                });
            }

            if (nav.type == 4) {
                // 抽奖活动
                const { drawId } = nav.drawInfo;
                this.$Router.push({
                    path: '/standard/lottery/detail',
                    query: { drawId }
                });
            }
        }
    }
};
</script>
<style lang="scss">
.recommend-content {
    background-color: transparent;
    .home_swiper {
        width: 100%;
        padding: 20rpx 4%;
        box-sizing: border-box;
    }
    .circle-nav {
        width: 100%;
        padding: 0 4%;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        margin: 50rpx 0;
        column-gap: 15rpx;
        .circle-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            row-gap: 10rpx;
            // &.w_1 {
            //     width: 100%;
            // }
            // &.w_2 {
            //     width: 140rpx;
            // }
            // &.w_3 {
            //     width: 100rpx;
            // }
            .circle-icon {
                width: 100%;
                height: 120rpx;
                background-color: #f3f3f3;
                background-position: center;
                background-repeat: no-repeat;
                background-size: cover;
                border-radius: 30rpx;
                overflow: hidden;
            }

            .tab-name {
                margin-top: 10rpx;
                font-size: 28rpx;
                color: #333;
                text-align: center;
            }
        }
    }
    .hot-explore {
        width: 100%;
        padding: 0 4%;
        box-sizing: border-box;
        .video-item {
            margin-bottom: 20rpx;
        }
    }
}
// 空状态样式
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;

    .empty-image {
        width: 200rpx;
        height: 200rpx;
        margin-bottom: 30rpx;
    }

    .empty-text {
        font-size: $fs-s;
        color: #999999;
    }
}

// 底部提示样式
.bottom-tip {
    text-align: center;
    padding: 30rpx 0;
    color: #999;
    font-size: 24rpx;
    position: relative;

    text {
        padding: 0 30rpx;
        position: relative;
        z-index: 1;
    }

    &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 15%;
        right: 15%;
        height: 1rpx;
        z-index: 0;
    }
}

// 加载更多样式
.loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30rpx 0;

    .loading-icon {
        width: 40rpx;
        height: 40rpx;
        margin-right: 12rpx;
        border: 3rpx solid #f3f3f3;
        border-top: 3rpx solid #aa0404;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    .loading-text {
        font-size: 26rpx;
        color: #ffffff;
    }
}
//#ifdef H5
::v-deep .uv-swiper__wrapper__item__wrapper__image {
    img {
        display: none;
    }
}
//#endif
</style>
