<template>
    <view class="activity-list">
        <view class="activity-item" v-for="(item, index) in dataList" :key="index">
            <VideoItem :detail="item" @changeFollow="changeFollow" />
        </view>
        <!-- 空状态展示 -->
        <view class="empty-state" v-if="dataList.length === 0 && !isLoading">
            <image class="empty-image" :src="imgUrl + 'empty_default.png'" mode="aspectFit"></image>
            <view class="empty-text">空空如也什么也没有哦~</view>
        </view>

        <!-- 加载状态 -->
        <view class="loading-more" v-if="isLoading">
            <view class="loading-icon"></view>
            <text class="loading-text">加载中...</text>
        </view>

        <!-- 底部提示 -->
        <view class="bottom-tip" v-if="dataList.length > 0 && noMoreData">
            <text>~ 已经到底啦 ~</text>
        </view>
    </view>
</template>
<script>
import VideoItem from '@/components/videoItem.vue';
export default {
    components: {
        VideoItem
    },
    emits: ['reloadfinished'],
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            dataList: [], // 活动列表数据
            query: {
                memberType: 1, // 0普通，1官方账号
                current: 1,
                pageSize: 10
            },
            isFirstLoad: true, // 是否是第一次加载
            isLoading: false, // 是否正在加载数据
            noMoreData: false // 是否没有更多数据
        };
    },
    mounted() {
        // 获取活动列表
        // this.fetchList();
    },
    methods: {
        // 初始化数据 主要提供给父组件调用
        initData() {
            if (this.isFirstLoad) {
                this.query = {
                    memberType: 1,
                    current: 1,
                    pageSize: 10
                };
                this.dataList = [];
                this.noMoreData = false;
                this.fetchList(); // 获取短视频列表
            }
        },
        // 刷新数据
        reload() {
            this.query = {
                ...this.query,
                current: 1,
                pageSize: 10
            };
            this.noMoreData = false;
            this.fetchList('reload'); // 重新加载短视频列表
        },
        // 加载更多数据
        loadMore() {
            if (!this.noMoreData) {
                this.fetchList();
            }
        },
        // 切换关注状态
        changeFollow(item) {
            const authorId = item.authorId;
            // 更新视频列表中的关注状态
            const newVideoList = this.videoList.map((video) => {
                return {
                    ...video,
                    isFollow: video.authorId === authorId ? item.isFollow : video.isFollow
                };
            });
            this.videoList = newVideoList;
            // 强制更新组件
            this.$forceUpdate();
        },
        // 获取列表
        fetchList(type = '') {
            this.isLoading = true; // 开始加载
            this.$request({
                url: 'v3/video/front/video/list',
                data: this.query
            })
                .then((res) => {
                    this.isLoading = false; // 结束加载
                    this.isFirstLoad = false; // 标记为非第一次加载
                    if (res.state == 200) {
                        const { videoList, pagination } = res.data;
                        if (this.query.current === 1) {
                            this.dataList = videoList;
                        } else {
                            this.dataList = this.dataList.concat(videoList);
                        }
                        // 如果当前页数据小于每页数量，说明没有更多数据了
                        if (pagination.total > pagination.current * pagination.pageSize) {
                            this.query.current += 1;
                        } else {
                            this.noMoreData = true;
                        }
                    } else {
                        this.$api.msg(res.msg);
                    }
                    if (type === 'reload') {
                        this.$emit('reloadfinished', 'newsList');
                    }
                })
                .catch((err) => {
                    this.isLoading = false; // 结束加载
                    console.error('获取活动列表失败:', err);
                    if (type === 'reload') {
                        this.$emit('reloadfinished', 'newsList');
                    }
                });
        }
    }
};
</script>
<style lang="scss">
.activity-list {
    padding: 20rpx 4% 120rpx 4%;
    box-sizing: border-box;
    .activity-item {
        margin-bottom: 20rpx;
        &:last-child {
            margin-bottom: 0;
        }
    }

    // 空状态样式
    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 100rpx 0;

        .empty-image {
            width: 200rpx;
            height: 200rpx;
            margin-bottom: 30rpx;
        }

        .empty-text {
            font-size: $fs-s;
            color: #999999;
        }
    }

    // 骨架屏加载状态
    .skeleton {
        background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
        background-size: 400% 100%;
        animation: skeleton-loading 1.5s ease infinite;
        border-radius: 8rpx;
    }

    // 底部提示样式
    .bottom-tip {
        text-align: center;
        padding: 30rpx 0;
        color: #999;
        font-size: 24rpx;
        position: relative;

        text {
            padding: 0 30rpx;
            position: relative;
            z-index: 1;
        }

        &::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 15%;
            right: 15%;
            height: 1rpx;
            z-index: 0;
        }
    }

    // 加载更多样式
    .loading-more {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 30rpx 0;
        .loading-icon {
            width: 40rpx;
            height: 40rpx;
            margin-right: 12rpx;
            border: 3rpx solid #f3f3f3;
            border-top: 3rpx solid #aa0404;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        .loading-text {
            font-size: 26rpx;
            color: #ffffff;
        }
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }
}
</style>
