<template>
  <view class="content" :style="screenHeight">
    <!-- 图片 start -->
    <block v-if="guideInfo.type == 'image'">
      <view
        class="guide_image"
        :style="`background-image:url('` + guideInfo.url[0] + `')`"
      ></view>
      <view class="guide_image_btn" @tap="nextStep('jump')">{{
        experience
      }}</view>
    </block>
    <!-- 图片 end -->
    <!-- 轮播图 start -->
    <swiper
      v-else-if="guideInfo.type == 'swiper'"
      class="swiper"
      :autoplay="autoplay"
      :duration="duration"
      :indicator-dots="true"
      :current="current"
      @change="changeSwiper"
    >
      <swiper-item v-for="(item, index) in guideInfo.url" :key="index">
        <view
          class="swiper-item"
          :style="`background-image:url('` + item + `')`"
        ></view>
        <view
          :class="guideInfo.url.length - 1 > index ? 'jump-over' : 'experience'"
          @tap="nextStep(index)"
          >{{ guideInfo.url.length - 1 > index ? jumpover : experience }}</view
        >
      </swiper-item>
    </swiper>
    <!-- 轮播图 end -->
    <!-- 视频 start -->
    <block v-else-if="guideInfo.type == 'video'">
      <video
        class="guide_video"
        :src="guideInfo.url[0]"
        object-fit="cover"
        :autoplay="true"
        :controls="false"
        :loop="false"
        :show-progress="false"
        :show-fullscreen-btn="false"
        :show-play-btn="false"
        :show-center-play-btn="false"
        :show-loading="false"
        :enable-progress-gesture="false"
        :vslide-gesture-in-fullscreen="false"
        :enable-play-gesture="false"
        @timeupdate="videoTime"
        id="myVideo"
      >
        <cover-view class="guide_video_btn" @tap="nextStep('jump')">
          <cover-view class="guide_video_time" @tap="nextStep('jump')">{{
            endTime > 0 ? endTime + 's' : ''
          }}</cover-view>
          <cover-view
            class="guide_video_content"
            :class="{ left: endTime <= 0 }"
            @tap="nextStep('jump')"
            >{{ experience }}</cover-view
          >
        </cover-view>
      </video>
    </block>
    <!-- 视频 end -->
  </view>
</template>

<script>
export default {
  data() {
    return {
      showGuide: false,
      autoplay: false,
      duration: 500,
      jumpover: this.$L('下一步'),
      experience: this.$L('立即体验'),
      current: 0,
      guideInfo: {},
      screenHeight: '',
      endTime: -1, //倒计时
      timeout: ''
    }
  },
  onLoad() {
    let info = uni.getSystemInfoSync()
    this.screenHeight = 'height:' + info.windowHeight + 'px'
    if (uni.getStorageSync('appGuide')) {
      uni.switchTab({
        url: '/pages/index/index'
      })
    } else {
      this.getSetting()
    }
  },
  onUnload() {
    if (this.timeout) {
      clearInterval(this.timeout)
      this.timeout = ''
    }
  },
  watch: {
    endTime(e) {
      if (this.timeout && e == 0) {
        clearInterval(this.timeout)
        this.timeout = ''
        uni.setStorage({
          key: 'appGuide',
          data: true
        })
        uni.switchTab({
          url: '/pages/index/index'
        })
      }
    }
  },
  methods: {
    //获取引导信息
    getSetting() {
      let param = {
        url: 'v3/system/front/setting/getSettings',
        data: { names: 'app_guide_page' },
        method: 'GET'
      }
      this.$request(param).then((res) => {
        if (res.state == 200 && res.data) {
          if (
            JSON.stringify(res.data) == `[""]` ||
            JSON.stringify(res.data) == `['']`
          ) {
            uni.switchTab({
              url: '/pages/index/index'
            })
            return
          }
          let result = JSON.parse(res.data)
          if (result.type == 'image' && result.data.length) {
            this.guideInfo = {
              type: result.data.length == 1 ? 'image' : 'swiper',
              url: result.data
            } // 图片/轮播图
            this.showGuide = true
          } else if (result.type == 'video' && result.data.length) {
            this.guideInfo = { type: 'video', url: result.data } //视频
            this.showGuide = true
          } else {
            uni.switchTab({
              url: '/pages/index/index'
            })
          }
        } else {
          uni.switchTab({
            url: '/pages/index/index'
          })
        }
      })
    },
    changeSwiper(e) {
      this.current = e.target.current
    },
    //下一步
    nextStep(index) {
      if (index == 'jump' || index + 1 >= this.guideInfo.url.length) {
        uni.setStorage({
          key: 'appGuide',
          data: true
        })
        uni.switchTab({
          url: '/pages/index/index'
        })
      } else if (index + 1 < this.guideInfo.url.length) {
        this.current = index + 1
      }
    },
    //视频播放信息
    videoTime(e) {
      if (this.endTime == -1 && e.detail.duration > 0) {
        this.endTime = parseInt(e.detail.duration)
        this.timeout = setInterval(() => {
          this.endTime--
        }, 1000)
      }
    }
  }
}
</script>
<style lang="scss">
page,
.content {
  width: 750rpx;
  height: 100%;
  background-size: 750rpx auto;
  margin: 0;
  padding: 0;
  overflow: hidden;

  .guide_image {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }

  .guide_video {
    width: 100%;
    height: 100%;
  }

  .swiper {
    width: 100%;
    height: 100%;
    background: #ffffff;

    .swiper-item {
      display: flex;
      align-items: flex-end;
      flex-direction: column-reverse;
      position: relative;
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
    }

    .jump-over,
    .experience {
      position: absolute;
      z-index: 999;
      height: 60rpx;
      line-height: 60rpx;
      color: #454343;
      font-size: 32rpx;
      text-align: center;
      padding: 0 40rpx;
      border-radius: 30rpx;
      border: 1rpx solid #454343;
    }
    .jump-over {
      top: 100rpx;
      right: 45rpx;
    }
    .experience {
      bottom: 100rpx;
      left: 50%;
      margin-left: -110rpx;
      width: 220rpx;
    }
  }

  .guide_image_btn,
  .guide_video_btn {
    position: fixed;
    left: 50%;
    bottom: 100rpx;
    z-index: 999;
    color: #454343;
    font-size: 32rpx;
    text-align: center;
    border-radius: 30rpx;
    border: 1rpx solid #454343;
    white-space: nowrap;
  }
  .guide_image_btn {
    margin-left: -110rpx;
    width: 220rpx;
    height: 60rpx;
    line-height: 60rpx;
    padding: 0 40rpx;
  }
  .guide_video_btn {
    margin-left: -120rpx;
    z-index: 999;
    width: 240rpx;
    height: 62rpx;
    line-height: 46rpx;
    overflow: hidden;
  }
  .guide_video_time {
    width: 50rpx;
    line-height: 48rpx;
    float: left;
    margin-left: 25rpx;
  }
  .guide_video_content {
    width: 140rpx;
    line-height: 48rpx;
    float: left;
    &.left {
      margin-left: 50rpx;
    }
  }
}
</style>
