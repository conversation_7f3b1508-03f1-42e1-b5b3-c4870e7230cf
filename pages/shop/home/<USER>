<template>
    <view class="page" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }">
        <ktabbar :bgImg="imgUrl + 'default_bg.jpg'" :title="'全部商品'" />
        <!-- 好物推荐 -->
        <view class="goods-section">
            <view class="goods-list">
                <view class="goods-item" v-for="goods in goodsList" :key="goods.id">
                    <goods-item :goods_info="goods" :page_margin="20" :goods_margin="0" :show_sale="false" :border_radius="0" :height="225"></goods-item>
                </view>
            </view>
            <loading-state :state="loadingState" mTop="200rpx" />
            <view style="width: 100%; height: 150rpx"></view>
        </view>
    </view>
</template>

<script>
import ktabbar from '@/components/ktabbar.vue';
import loadingState from '@/components/loading-state.vue';
import goodsItem from '@/components/goodsItem.vue';
import { mapState } from 'vuex';
export default {
    components: {
        ktabbar,
        goodsItem,
        loadingState
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            tapBarBgColor: 'transparent', // 顶部导航栏背景色
            // #ifdef MP
            topBar: {}, // 顶部导航栏信息
            // #endif
            current: 1, //当前页
            goodsList: [], // 商品列表
            loadingState: 'first_loading'
        };
    },
    computed: {
        ...mapState(['hasLogin'])
    },
    onLoad() {
        this.loadData();
        // #ifdef MP
        // 胶囊按钮位置信息
        const systemInfo = uni.getWindowInfo();
        // 导航栏高度 = 状态栏高度 + 44
        const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
        this.navBarHeight = systemInfo.statusBarHeight + 44 + 'px';
        this.menuRight = systemInfo.screenWidth - menuButtonInfo.right + 'px';
        let globalData = {};
        globalData.navBarHeight = systemInfo.statusBarHeight + 44;
        globalData.menuRight = systemInfo.screenWidth - menuButtonInfo.right;
        globalData.menuHeight = menuButtonInfo.height;
        globalData.menuWidth = menuButtonInfo.width;
        globalData.offsettop = menuButtonInfo.top;
        this.topBar = globalData;
        // #endif
    },
    // 触底加载
    onReachBottom() {
        this.loadMore();
    },
    onPageScroll: function (e) {
        //nvue暂不支持滚动监听，可用bindingx代替
        if (e.scrollTop > 150) {
            this.statusbarActive = true;
        } else {
            this.statusbarActive = false;
        }
    },
    // 下拉刷新
    onPullDownRefresh() {
        this.loadData('refresh');
    },
    methods: {
        loadData(type) {
            this.current = 1;
            this.getGoods(type || null); // 获取热门商品
        },
        loadMore() {
            if (this.loadingState != 'allow_loading_more') {
                return;
            }
            this.current++;
            this.getGoods(); // 获取更多商品
        },
        // 获取商品
        getGoods(type) {
            let param = {};
            param.url = 'v3/goods/front/goods/goodsList';
            param.method = 'GET';
            param.data = {
                page: this.current,
                pageSize: 10
            };
            this.loadingState = 'loading';
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    const { list, pagination } = res.data;
                    if (this.current == 1) {
                        this.goodsList = list;
                    } else {
                        this.goodsList = this.goodsList.concat(list);
                    }
                    if (pagination.total <= pagination.pageSize * pagination.current) {
                        this.loadingState = 'no_more_data';
                    } else {
                        this.loadingState = 'allow_loading_more';
                    }
                }
                if (type == 'refresh') {
                    uni.stopPullDownRefresh();
                }
            });
        }
    }
};
</script>

<style lang="scss">
.page {
    width: 750rpx;
    min-height: 100vh;
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    // 背景图片固定，不滚动
    background-attachment: fixed;
    .goods-section {
        width: 94%;
        margin: 0 auto;
        margin-top: 40rpx;
        .goods-list {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            row-gap: 30rpx;
        }
        .goods-list > .goods-item:nth-child(2n) {
            margin-right: 0 !important;
        }
    }
}
</style>
