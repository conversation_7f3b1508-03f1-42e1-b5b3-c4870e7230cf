<template>
    <view class="shop_home" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }" v-show="loaded">
        <!-- 自定义顶部导航栏 -->
        <!-- #ifdef MP -->
        <view class="fixed_top_status_bar">
            <view class="custom-nav-taber" :style="{ height: topBar.navBarHeight + 'px', paddingTop: topBar.offsettop - 5 + 'px', backgroundColor: tapBarBgColor }">
                <view :style="{ height: topBar.menuHeight + 'px', lineHeight: topBar.menuHeight + 'px', width: `calc(100% - ${topBar.menuWidth + 50}px)` }" class="search_tap_bar">
                    <view class="search_wrap" :style="{ borderRadius: topBar.menuHeight / 2 + 'px' }" @click="toSearchPage">
                        <image class="search_icon" :src="imgUrl + 'search3.png'" mode="" />
                        <text class="search_text">搜索商品</text>
                    </view>
                    <view class="cart" @click="myCart">
                        <image class="cart_icon" :src="imgUrl + 'shop/shopping-cart.png'" mode="" />
                    </view>
                </view>
            </view>
        </view>
        <!-- #endif -->
        <!-- #ifdef H5 -->
        <view class="fixed_top_status_bar">
            <view class="custom-nav-taber" :style="{ backgroundColor: tapBarBgColor }">
                <view class="search_tap_bar">
                    <view class="search_wrap" @click="toSearchPage">
                        <image class="search_icon" :src="imgUrl + 'search3.png'" mode="" />
                        <text class="search_text">搜索商品</text>
                    </view>
                    <view class="cart" @click="myCart">
                        <image class="cart_icon" :src="imgUrl + 'shop/shopping-cart.png'" mode="" />
                    </view>
                </view>
            </view>
        </view>
        <!-- #endif -->
        <view class="home_container">
            <!-- 首页轮播 -->
            <view class="home_swiper">
                <uv-swiper
                    height="100vw"
                    radius="0"
                    :list="swiperList"
                    keyName="imageUrl"
                    indicator
                    indicatorMode="line"
                    @click="swiperClick"
                    circular
                    :autoplay="false"
                ></uv-swiper>
            </view>
            <!-- 图标导航 -->
            <view class="circle-nav" v-if="tab_list.length > 0">
                <view class="circle-item" v-for="(tab, index) in tab_list" :key="tab.adsId" @click="tabClick(tab)">
                    <view :class="['circle-icon', `w_${tab_list.length}`]" :style="{ backgroundImage: `url(${tab.imageUrl})` }"></view>
                    <view class="tab-name">{{ tab.adsName }}</view>
                </view>
            </view>
            <!-- 好物推荐 -->
            <view class="hot-section">
                <view class="section-title flex_row_between_center">
                    <text class="t">好物推荐</text>
                    <view class="nav-btn" @click="navToAllGoods">
                        <text class="t">全部商品</text>
                        <uv-icon name="arrow-right" size="14" color="rgba(0,0,0,0.4)" />
                    </view>
                </view>
                <!-- 好物推荐-->
                <view class="goods-list">
                    <view class="goods-item" v-for="goods in goodsList" :key="goods.id">
                        <goods-item :goods_info="goods" :page_margin="20" :goods_margin="0" :show_sale="false" :border_radius="0" :height="225"></goods-item>
                    </view>
                </view>
                <loading-state :state="loadingState" mTop="200rpx" />
                <view style="width: 100%; height: 150rpx"></view>
            </view>
        </view>
    </view>
</template>

<script>
import { customTabbarShow, customTabbarHide } from '@/utils/common';
import loadingState from '@/components/loading-state.vue';
import goodsItem from '@/components/goodsItem.vue';
import { mapState } from 'vuex';
export default {
    components: {
        goodsItem,
        loadingState
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            tapBarBgColor: 'transparent', // 顶部导航栏背景色
            statusbarActive: false,
            // #ifdef MP
            topBar: {}, // 顶部导航栏信息
            // #endif
            loaded: false, // 页面是否加载完成
            current: 1, //当前页
            cartNum: 0, // 购物车数量
            swiperList: [], //轮播图数据
            tab_list: [], //轮播下方Tab数据
            goodsList: [], // 热门商品列表
            loadingState: 'first_loading',
            shareData: {}
        };
    },
    computed: {
        ...mapState(['hasLogin'])
    },
    onLoad() {
        this.loadData();
        // #ifdef MP-WEIXIN
        let url = getApp().globalData.apiUrl.substring(0, getApp().globalData.apiUrl.length - 1);
        this.$sldStatEvent({ behaviorType: 'pv', pageUrl: url + '/pages/shop/home/<USER>', referrerPageUrl: '' });
        // #endif
        // #ifdef MP
        // 胶囊按钮位置信息
        const systemInfo = uni.getWindowInfo();
        // 导航栏高度 = 状态栏高度 + 44
        const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
        this.navBarHeight = systemInfo.statusBarHeight + 44 + 'px';
        this.menuRight = systemInfo.screenWidth - menuButtonInfo.right + 'px';
        let globalData = {};
        globalData.navBarHeight = systemInfo.statusBarHeight + 44;
        globalData.menuRight = systemInfo.screenWidth - menuButtonInfo.right;
        globalData.menuHeight = menuButtonInfo.height;
        globalData.menuWidth = menuButtonInfo.width;
        globalData.offsettop = menuButtonInfo.top;
        this.topBar = globalData;
        // #endif
    },
    // 触底加载
    onReachBottom() {
        this.loadMore();
    },
    onPageScroll: function (e) {
        //nvue暂不支持滚动监听，可用bindingx代替
        if (e.scrollTop > 150) {
            this.tapBarBgColor = '#000';
        } else {
            this.tapBarBgColor = 'transparent';
        }
    },
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {
        const { shareData } = this;
        return shareData;
    },

    onShareTimeline: function () {
        const { shareData } = this;
        return shareData;
    },
    // 下拉刷新
    onPullDownRefresh() {
        this.loadData();
    },

    onShow() {
        customTabbarShow(1);
        // this.getCartNum();
    },
    onHide() {
        customTabbarHide(1);
    },
    methods: {
        loadData() {
            this.current = 1;
            this.advConfig(4); // 获取轮播配置
            this.advConfig(5); // 获取轮播下方Tab配置
            this.getHotGoods(); // 获取热门商品
        },
        // 加载更多商品
        loadMore() {
            if (this.loadingState != 'allow_loading_more') {
                return;
            }
            this.current++;
            this.getHotGoods(); // 获取更多商品
        },
        //获取广告配置
        advConfig(type = 1) {
            // platform 1:小程序 2:H5
            let param = {};
            param.url = 'v3/video/front/ads/list';
            param.method = 'GET';
            param.data = {
                platform: 1,
                type
            };
            // #ifdef H5
            param.data.platform = 2;
            // #endif
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    if (type == 4) {
                        this.swiperList = res.data.list;
                        this.loaded = true; // 页面加载完成
                    } else if (type == 5) {
                        this.tab_list = res.data.list;
                    }
                }
            });
        },
        // 调转搜索页面
        toSearchPage() {
            const page = getCurrentPages();
            let curPage = page[page.length - 1];
            if (curPage.route == 'extra/tshou/index/index') {
                this.$Router.push('/extra/tshou/search/search');
            } else {
                this.$Router.push('/pages/search/search');
            }
        },
        // 跳转到购物车
        myCart() {
            if (this.hasLogin) {
                this.$Router.push('/standard/cart/cart');
            } else {
                getApp().globalData.goLogin(this.$Route);
            }
        },
        //获取购物车数据
        getCartNum() {
            if (this.hasLogin) {
                let param = {};
                param.url = 'v3/business/front/cart/cartNum';
                param.method = 'GET';
                param.data = {};
                this.$request(param).then((res) => {
                    if (res.state == 200) {
                        if (res.data > 0) {
                            this.cartNum = res.data;
                            // uni.setTabBarBadge({
                            //     index: 3,
                            //     text: res.data.toString()
                            // });
                        } else {
                            // uni.hideTabBarRedDot({
                            //     index: 3
                            // });
                        }
                    }
                });
            }
        },
        // 获取热门商品
        getHotGoods() {
            let param = {};
            param.url = 'v3/goods/front/goods/homeRecommendList';
            param.method = 'GET';
            param.data = {
                page: this.current,
                pageSize: 10
            };
            this.loadingState = 'loading';
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    const { list, pagination } = res.data;
                    if (this.current == 1) {
                        this.goodsList = list;
                    } else {
                        this.goodsList = this.goodsList.concat(list);
                    }
                    if (pagination.total <= pagination.pageSize * pagination.current) {
                        this.loadingState = 'no_more_data';
                    } else {
                        this.loadingState = 'allow_loading_more';
                    }
                }
                if (this.current == 1) {
                    uni.stopPullDownRefresh();
                }
            });
        },
        // 全部商品
        navToAllGoods() {
            this.$Router.push('/pages/shop/home/<USER>');
        },
        // 轮播图点击事件
        swiperClick(index) {
            const { link, adsName, adsId, type } = currentItem;
            console.log('轮播图点击:', currentItem);
            // 如果 link 是字符串，尝试解析为对象
            let navInfo;
            if (typeof link === 'string') {
                try {
                    navInfo = JSON.parse(link);
                } catch (e) {
                    // 如果解析失败，可能是直接的URL
                    navInfo = {
                        type: 2, // H5页面
                        path: link
                    };
                }
            } else {
                navInfo = link;
            }

            this.navigatorTo(navInfo);
        },
        // Tab点击事件
        tabClick(tab) {
            const { link, adsName, adsId, type } = tab;
            // 如果 link 是字符串，尝试解析为对象
            let navInfo;
            if (typeof link === 'string') {
                try {
                    navInfo = JSON.parse(link);
                } catch (e) {
                    // 如果解析失败 不操作
                    navInfo = { type: 0 };
                }
            } else {
                navInfo = link;
            }
            this.navigatorTo(navInfo);
        }
    }
};
</script>

<style lang="scss">
.fixed_top_status_bar {
    .custom-nav-taber {
        position: fixed;
        top: 0;
        // #ifdef MP
        left: 0;
        width: 100%;
        // #endif
        z-index: 10;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        // #ifdef H5
        padding-top: 80rpx;
        padding-bottom: 20rpx;
        box-sizing: content-box;
        left: 50%;
        width: 750rpx;
        height: 80rpx;
        transform: translateX(-50%);
        // #endif
        background-repeat: no-repeat;
        background-position: top center;
        background-size: 100% auto;
    }
    .search_tap_bar {
        display: flex;
        align-items: center;
        width: 100%;
        // #ifdef MP
        padding-left: 30rpx;
        justify-content: flex-start;
        // #endif
        // #ifdef H5
        width: 100%;
        padding: 0 8%;
        height: 100%;
        justify-content: space-between;
        // #endif
        .cart {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            // #ifdef MP
            width: 100rpx;
            // #endif
            // #ifdef H5
            width: 150rpx;
            // #endif
            .cart_icon {
                /* #ifdef MP-WEIXIN */
                width: 50rpx;
                height: 50rpx;
                /* #endif */
                /* #ifdef H5 */
                width: 60rpx;
                height: 60rpx;
                /* #endif */
                object-fit: contain;
            }
        }
    }
    .search_wrap {
        position: relative;
        display: flex;
        align-items: center;
        // #ifdef MP
        width: 400rpx;
        padding: 0 20rpx;
        height: 100%;
        // #endif
        // #ifdef H5
        width: calc(100% - 70rpx);
        height: 80rpx;
        border-radius: 40rpx;
        padding: 0 20rpx;
        // #endif
        border: 1px solid #fff;
        background: rgba(255, 255, 255, 0.6);
        box-sizing: border-box;
    }
    .search_icon {
        /* #ifdef MP-WEIXIN */
        width: 36rpx;
        height: 36rpx;
        /* #endif */
        /* #ifdef H5 */
        width: 40rpx;
        height: 40rpx;
        /* #endif */
        object-fit: contain;
    }
    .search_text {
        font-size: 26rpx;
        /* #ifdef H5 */
        font-size: 30rpx;
        /* #endif */
        color: #6e6e6e;
        margin-left: 20rpx;
    }
    .clear_content {
        /* #ifdef MP-WEIXIN */
        width: 45rpx;
        height: 45rpx;
        /* #endif */
        /* #ifdef H5 */
        width: 60rpx;
        height: 60rpx;
        /* #endif */
        position: absolute;
        right: 20rpx;
        top: 50%;
        z-index: 3;
        transform: translateY(-50%);
    }
    .add_icon {
        /* #ifdef MP-WEIXIN */
        width: 40rpx;
        height: 40rpx;
        /* #endif */
        /* #ifdef H5 */
        width: 50rpx;
        height: 50rpx;
        /* #endif */
        object-fit: contain;
    }
    .back_icon {
        object-fit: contain;
        /* #ifdef MP-WEIXIN */
        width: 30rpx;
        height: 30rpx;
        margin-right: 30rpx;
        /* #endif */
        /* #ifdef H5 */
        width: 40rpx;
        height: 40rpx;
        margin-right: 20rpx;
        /* #endif */
    }
    .top_status_bar_seat {
        // #ifdef MP
        width: 100%;
        // #endif
        /* app-2-start */
        // #ifdef H5
        width: 750rpx;
        height: 180rpx;
        // #endif
    }
}
.shop_home {
    min-height: 100vh;
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    // 背景图片固定，不滚动
    background-attachment: fixed;
    .home_container {
        width: 100%;
        height: 100%;
        position: relative;
        overflow: hidden;
        .home_swiper {
            width: 100%;
            box-sizing: border-box;
        }
    }
    .circle-nav {
        width: 100%;
        padding: 0 3%;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        margin: 40rpx 0;
        column-gap: 15rpx;
        .circle-item {
            flex: 1;
            height: 180rpx;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-radius: 30rpx;
            overflow: hidden;
            row-gap: 10rpx;
            .circle-icon {
                width: 100%;
                height: 100%;
                background-color: #f3f3f3;
                background-position: center;
                background-repeat: no-repeat;
                background-size: cover;
                border-radius: 30rpx;
                overflow: hidden;
            }

            .tab-name {
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                backdrop-filter: blur(10px);
                padding: 6rpx 0;
                font-size: 24rpx;
                color: #fff;
                text-align: center;
            }
        }
    }
    .hot-section {
        width: 94%;
        margin: 0 auto;
        margin-top: 40rpx;
        .section-title {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-sizing: border-box;
            padding: 20rpx 0;
            .t {
                font-size: 32rpx;
                color: #000;
                font-weight: bold;
            }
            .nav-btn {
                display: flex;
                align-items: center;
                column-gap: 0rpx;
                .t {
                    font-size: $fs-s;
                    font-weight: 500;
                    color: rgba(89, 87, 87, 1);
                }
            }
        }
        .goods-list {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            row-gap: 30rpx;
        }
        .goods-list > .goods-item:nth-child(2n) {
            margin-right: 0 !important;
        }
    }
}
//#ifdef H5
::v-deep .uv-swiper__wrapper__item__wrapper__image {
    img {
        display: none;
    }
}
//#endif
</style>
