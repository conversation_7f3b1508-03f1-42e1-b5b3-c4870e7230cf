//用户协议、隐私协议页面
<template>
	<view class="container_pri">
		<jyfParser :isAll="true" :html="h5_wx_html"></jyfParser>
	</view>
</template>

<script>
import jyfParser from '@/components/jyf-parser/jyf-parser.vue'
import { quillEscapeToHtml } from '@/utils/common.js'
export default {
  data() {
    return {
      type: '',
      // banance: 余额   points: 积分
      key: '',
      imgUrl: getApp().globalData.imgUrl,
      isLoading: true,
      title: '',
      num: "",
      list: "",
	  pn: 1,//当前页
	  hasmore: true,
	  h5_wx_html:''
    };
  },
  components: {jyfParser},
  props: {},
  onLoad(){
  	  this.getPrivacyPolicy();
	  let title = this.$Route.query.type == 'register_agreement' ? this.$L('用户协议') : this.$L('隐私协议')
    setTimeout(()=>{
      uni.setNavigationBarTitle({
        title: title
      })
    },0);
	  
  },
  methods:{
  	  getPrivacyPolicy(){
	    let param = {}
	    param.data = {}
	    // register_agreement 用户协议  privacy_policy 隐私协议
	    param.data.agreementCode= this.$Route.query.type;
	    param.url = 'v3/system/front/agreement/detail'
	    this.$request(param).then(res=>{
			this.h5_wx_html = res.data.content ? quillEscapeToHtml(res.data.content) : '';
	    })
  	  }
  }
};
</script>
<style>
	page{
		width: 750rpx;
		overflow: auto;
		margin: 0 auto;
	}
	
	.container_pri{
		width: 680rpx;
		margin: 0 auto 20rpx;
		word-break: break-all;
	}
	
	p{
		margin-top: 20px;
	}
	
	.container_pri ::v-deep .rich_text_image {
		max-width: 680rpx; 
	}
</style>
