<template>
	<view class="bind-vehicle">
		<view class="el-card brand-nav">
			<view class="cu-form-group brand-container">
				<view class="title">品牌</view>
				<view class="brand-btn">
					<view v-for="item in oderNav" :key="item.id" :class="item.id == TabCur ? 'item active' : 'item'"
						@click="tabSelect(item.id)">{{ item.name }}
					</view>
				</view>
			</view>
		</view>
		<form @submit="handleVehicleSubmit">
			<view v-show="TabCur === 1" class="el-card vehicle-card">
				<view class="cu-form-group">
					<view class="title">车架号<text class="request">*</text></view>
					<view class="avatar-wrapper">
						<input v-model="lmVehicleInfo.vehicleNo" class="form_input" placeholder="请扫描或输入"
							placeholder-class="el-placeholder" name="vehicleNo" @blur="vehicleNoBlur" />
						<uv-icon class="avatar" name="scan" size="24" @click="scanCode('vehicleNo')"></uv-icon>
					</view>
				</view>
				<view class="cu-form-group">
					<view class="title">车型</view>
					<input v-model="lmVehicleInfo.vehicleType" class="form_input" placeholder="车型"
						placeholder-class="el-placeholder" name="vehicleType" disabled />
				</view>
				<view class="cu-form-group">
					<view class="title">电池编号</view>
					<view class="avatar-wrapper">
						<input v-model="lmVehicleInfo.batteryNum" class="form_input" placeholder="请扫描"
							placeholder-class="el-placeholder" name="batteryNum" disabled />
						<uv-icon class="avatar" name="scan" size="24" @click="scanCode('batteryNum')"></uv-icon>
					</view>
				</view>
				<view class="cu-form-group">
					<view class="title">手机号<text class="request">*</text></view>
					<input v-model="lmVehicleInfo.userPhone" class="form_input" placeholder="请填写手机号"
						placeholder-class="el-placeholder" name="userPhone" />
				</view>
				<view class="cu-form-group">
					<view class="title">姓名</view>
					<input v-model="lmVehicleInfo.userName" class="form_input" placeholder="请填写姓名"
						placeholder-class="el-placeholder" name="userName" />
				</view>
				<view class="cu-form-group">
					<view class="title">购买门店<text class="request">*</text></view>
					<view class="picker flex_row_between_center" style="width: 100%;" @click="choiceShop">
						<text v-if="!!lmVehicleInfo.salesOutletsName"
							class="">{{ lmVehicleInfo.salesOutletsName }}</text>
						<text v-else class="el-placeholder form_input">请选择门店</text>
						<view class="arrow-icon"></view>
					</view>
				</view>
				<view class="cu-form-group">
					<view class="title">购买日期<text class="request">*</text></view>
					<dateTimePicker class="picker" title="请选择购买日期" :end="dayjs().format('YYYY-MM-DD')"
						:value="lmVehicleInfo.purchaseTime" style="width: 100%;" placeholder="请选择购买日期"
						@change="handleDateChange" />
				</view>
				<view class="cu-form-group">
					<view class="title">购买金额</view>
					<input v-model="lmVehicleInfo.vehicleBuyPrice" type="vehicleBuyPrice" placeholder="请填写购买金额"
						placeholder-class="el-placeholder" class="form_input" maxlength="8" />
				</view>
				<view class="cu-form-group">
					<view @click="skips" class="tips">
						如需要三包激活请在三包激活功能中进行，<span class="active-vehicle">点击开始三包激活</span>
					</view>
				</view>
			</view>
			<view v-show="TabCur === 0" class="el-card vehicle-card">
				<view class="cu-form-group" @click="getVehicleType">
					<view class="title">所属品牌<text class="request">*</text></view>
					<ElPicker title="其他车辆品牌" :value="lmVehicleInfo.brandName" style="width: 100%;"
						:picker-list="vehicleTypeUdc" :transfer="{ label: 'udcValueName', value: 'udcValueName' }"
						@change="handleChangeIssueType" />
				</view>
				<view class="cu-form-group">
					<view class="title">车辆名称</view>
					<input v-model="lmVehicleInfo.valueName" placeholder="请输入车辆名称或型号" placeholder-class="el-placeholder"
						name="valueName" class="form_input" />
				</view>
				<view class="cu-form-group">
					<view class="title">手机号<text class="request">*</text></view>
					<input v-model="lmVehicleInfo.userPhone" placeholder="请填写手机号" type="number" maxlength="11"
						class="form_input" placeholder-class="el-placeholder" name="userPhone" />
				</view>
				<view class="cu-form-group">
					<view class="title">姓名</view>
					<input v-model="lmVehicleInfo.userName" placeholder="请填写姓名" class="form_input"
						placeholder-class="el-placeholder" name="userName" />
				</view>
				<view class="cu-form-group">
					<view class="title">绑定门店</view>
					<view class="picker flex_row_between_center" style="width: 100%;" @click="choiceShop">
						<text v-if="!!lmVehicleInfo.salesOutletsName"
							class="">{{ lmVehicleInfo.salesOutletsName }}</text>
						<text v-else class="placeholder el-placeholder">请选择门店</text>
						<view class="arrow-icon"></view>
					</view>
				</view>
				<view class="cu-form-group">
					<view class="title">购买日期<text class="request">*</text></view>
					<dateTimePicker class="picker" title="请选择购买日期" :end="dayjs().format('YYYY-MM-DD')"
						:value="lmVehicleInfo.purchaseTime" style="width: 100%;" placeholder="请选择购买日期"
						@change="handleDateChange" />
				</view>
				<view class="cu-form-group">
					<view class="title">购买金额</view>
					<input v-model="lmVehicleInfo.vehicleBuyPrice" class="form_input" placeholder="请填写购买金额"
						placeholder-class="el-placeholder" name="vehicleBuyPrice" maxlength="8" />
				</view>
			</view>
			<view class="el-submit-btn-view">
				<button class="cu-btn block el-submit-btn" form-type="submit">确认并绑定</button>
			</view>
		</form>
	</view>
</template>
<script>
	import dateTimePicker from '@/components/dateTimePicker.vue';
	const customerInfo = uni.getStorageSync('customerInfo');
	const getExternalLinkData = uni.getStorageSync('externalLinkData');
	export default {
		components: {
			dateTimePicker,
		},
		data() {
			return {
				TabCur: 1,
				oderNav: [{
						id: 1,
						name: '立马'
					},
					{
						id: 0,
						name: '其他'
					}
				],
				tagItemIds: '405aec9c1eae4af080d04603e9cc455a', // 激活车辆
				lmVehicleInfo: {
					vehicleNo: '',
					vehicleSpecs: '',
					vehicleColor: '',
					batteryNum: '',
					brandName: '',
					vehicleName: '',
					userPhone: customerInfo.userPhone,
					salesOutletsCode: '',
					salesOutletsName: '',
					salesOutletsId: '',
					purchaseTime: this.dayjs().format('YYYY-MM-DD'),
					vehicleType: '',
					vehicleBuyPrice: '',
					userName: customerInfo.userName,
					userSex: null,
					userBirthday: '',
					userArea: '',
					manufactureDate: '',
					valueName: '',
				},
				vehicleTypeUdc: [],
				bindQty: 0,
				actParams: {
					eventType: 'PAGE_VIEW',
					pageTitle: '绑定车辆',
					pageName: 'bindVehicle',
					pagePath: 'pages/bindVehicle/bindVehicle',
					elementName: '',
					enterProgm: '',
					currProgm: '立马好车主',
					deviceMode: '',
					appVersion: '',
					visitor: customerInfo.userPhone,
					visitorName: customerInfo.userName,
				},
				userInfo: uni.getStorageSync('userInfo'),
				getExternalLinkData: getExternalLinkData
			};
		},
		onLoad() {
			const accountInfo = uni.getAccountInfoSync();
			const systemInfo = uni.getSystemInfoSync();
			if (accountInfo.miniProgram.envVersion == 'trial' || accountInfo.miniProgram.envVersion == 'develop') {
				this.actParams.appVersion = '测试|开发版本';
			} else {
				this.actParams.appVersion = accountInfo.miniProgram.version;
			}
			this.actParams.deviceMode = systemInfo.model;
		},
		mounted() {
			// this.getUserFlmVehicleBindQty();
			// this.actFn();
		},
		methods: {
			// 获取非立马车辆udc
			async getVehicleType() {
				const data = {
					appCode: 'yst-after-sale',
					udcCode: 'OTHER_CAR_BRAND'
				};
				const res = await this.$http.get('/cloudt/system/cloudt/core/udc/get', {
					data
				});
				if (res.success) {
					this.vehicleTypeUdc = res.data.valueList;
				} else {
					this.$dialog.toast(res.msg);
				}
			},

			// 车架号输入失去焦点
			vehicleNoBlur() {
				const vehicleNoLength = this.lmVehicleInfo.vehicleNo.length;
				if (vehicleNoLength >= 12) {
					this.queryVehicleInfoByVehicleNo(this.lmVehicleInfo.vehicleNo);
				}
			},

			// 选择门店
			choiceShop() {
				this.$Router.push({
					path: '/pages/store/storeList'
				});
			},

			// 扫码
			scanCode(type) {
				uni.scanCode({
					success: (res) => {
						if (type === 'vehicleNo') {
							this.lmVehicleInfo.vehicleType = '';
							this.lmVehicleInfo.vehicleNo = '';
							this.queryVehicleInfoByVehicleNo(res.result);
						}
						if (type === 'batteryNum') {
							this.lmVehicleInfo.batteryNum = res.result;
						}
					}
				});
			},

			tabSelect(index) {
				if (this.dayjs(this.dayjs().format('YYYY-MM-DD HH:mm:ss')).unix() >= this.dayjs('2024-02-25 00:00:00')
					.unix()) {
					this.TabCur = index;
				} else {
					if (index == 0 && this.bindQty > 0) {
						this.$dialog.toast('已绑定非立马品牌，无法再次绑定');
					} else {
						this.TabCur = index;
					}
				}
			},

			validateForm(params) {
				const checkPhone = new RegExp(/^[1]([3-9])[0-9]{9}$/);
				const PriceType = isNaN(Number(params.vehicleBuyPrice));
				if (this.TabCur === 0) {
					if (!params.brandName) {
						this.$dialog.toast('请选择所属品牌');
						return false;
					}
					if (!params.userPhone || params.userPhone.length === 0) {
						this.$dialog.toast('请输入手机号');
						return false;
					}
					if (!checkPhone.test(params.userPhone)) {
						this.$dialog.toast('请输入正确的手机号');
						return false;
					}
					if (!params.purchaseTime) {
						this.$dialog.toast('请选择购买日期');
						return false;
					}
					if (params.vehicleBuyPrice !== '' && PriceType) {
						this.$dialog.toast('请输入正确购买价格');
						return false;
					}
				} else {
					if (!params.vehicleNo) {
						this.$dialog.toast('请输入车架号');
						return false;
					}
					if (!params.vehicleType) {
						this.$dialog.toast('请输入正确车架号');
						return false;
					}
					if (!params.userPhone || params.userPhone.length === 0) {
						this.$dialog.toast('请输入手机号');
						return false;
					}
					if (!checkPhone.test(params.userPhone)) {
						this.$dialog.toast('请输入正确的手机号');
						return false;
					}
					if (!params.salesOutletsName) {
						this.$dialog.toast('请选择购买门店');
						return false;
					}
					if (!params.purchaseTime) {
						this.$dialog.toast('请选择购买日期');
						return false;
					}
					if (params.vehicleBuyPrice !== '' && PriceType) {
						this.$dialog.toast('请输入正确购买价格');
						return false;
					}
				}
				return true;
			},

			// 确认并绑定 接口
			async handleVehicleSubmit(e) {
				this.actParams.eventType = 'BTN_CLICK';
				this.actParams.elementName = '绑定车辆确认绑定';
				this.actFn();

				if (this.lmVehicleInfo.valueName !== '') {
					this.lmVehicleInfo.brandName = `${this.lmVehicleInfo.brandName}-${this.lmVehicleInfo.valueName}`;
				}

				let params = {
					...this.lmVehicleInfo,
					...e.detail.value,
					// 车辆来源	1 立马  0 非立马
					vehicleSource: this.TabCur,
					carOwnerId: this.userInfo.carOwnerId
				};

				if (this.TabCur === 0) {
					params = {
						...params,
						vehicleNo: '',
						vehicleSpecs: this.lmVehicleInfo.brandName,
						batteryNum: '',
						vehicleType: this.lmVehicleInfo.brandName,
						vehicleBuyPrice: this.lmVehicleInfo.vehicleBuyPrice,
					};
				} else {
					params = {
						...params,
						brandName: '立马',
					};
				}

				if (!await this.validateForm(params)) {
					return false;
				}

				this.bindVehicleSave(params);
			},

			async bindVehicleSave(params) {
				this.$dialog.loading();
				const res = await this.$http.post('/yst/aftersale/miniApp/bindVehicle/bindSoldVehicleSave2', {
					...params
				});
				this.$dialog.loaded();
				if (res.success) {
					this.$dialog.success('绑定成功');

					// 调用达摩添加会员标签
					await this.$http.post('/yst/aftersale/demogic/saveMemberTag', {
						mobile: this.userInfo.userPhone,
						tagItemIds: this.tagItemIds
					});

					const query = this.$mp && this.$mp.query;
					if (query?.page === 'profile') {
						uni.navigateTo({
							url: packageConfig.mine + '/pages/mineVehicle/mineVehicle'
						});
					} else {
						uni.navigateBack({
							delta: 1
						});
					}
				} else {
					this.$dialog.toast(res.msg);
				}
			},

			// 购买日期
			handleDateChange(value) {
				this.lmVehicleInfo.purchaseTime = value;
			},

			// 根据整车号查询车辆基本信息
			async queryVehicleInfoByVehicleNo(vehicleNo) {
				const res = await this.$http.post('/yst/aftersale/miniApp/car/carQuery', {
					vehicleNo
				});
				if (res.success) {
					this.lmVehicleInfo.vehicleType = res.data?.vehicleType;
					this.lmVehicleInfo.vehicleNo = res.data?.vehicleNo;

					const limaCarData = {
						vehicleName: res.data.vehicleName,
						purchaseTime: res.data.purchaseTime || this.lmVehicleInfo.purchaseTime,
						machineNo: res.data.machineNo,
						controlNo: res.data.controlNo,
						alarmNo: res.data.alarmNo,
						manufactureDate: res.data.manufactureDate,
						bindingTime: res.data.bindingTime,
						invMatter: res.data.invMatter,
						vehicleSeq: res.data.vehicleSeq,
						eNo: res.data.eNo,
						sourceSysType: res.data.sourceSysType,
						produceOrderNo: res.data.produceOrderNo,
						offlineDate: res.data.offlineDate,
						vehicleSpecs: res.data.vehicleSpecs,
						vehicleColor: res.data.vehicleColor,
						itemType3: res.data.itemType3,
					};

					this.lmVehicleInfo = {
						...this.lmVehicleInfo,
						...limaCarData
					};
				} else {
					this.lmVehicleInfo.vehicleType = '';
					this.$dialog.toast(res.msg || '车架号不存在');
				}
			},

			handleChangeIssueType({
				value
			}) {
				this.lmVehicleInfo.brandName = value;
			},

			// 获取用户非立马车辆绑定数量
			async getUserFlmVehicleBindQty() {
				const res = await this.$http.post('/yst/aftersale/miniApp/bindVehicle/getUserFlmVehicleBindQty');
				if (res.success) {
					this.bindQty = res.data;
				} else {
					this.$dialog.toast(res.msg);
				}
			},
			// 三包激活
			skips() {
				this.$Router.push({
					path: `/newPages/vehicle/activationVehicle`
				});
			},

			actFn() {
				if (this.getExternalLinkData) {
					this.actParams.enterProgm = '立马车服';
					uni.removeStorageSync('externalLinkData');
					this.actSending(this.actParams);
				} else {
					this.actSending(this.actParams);
				}
			},

			async actSending(values) {
				if (APP_ID == 'wxf8ca49759c4a3acc') return;
				uni.request({
					method: 'POST',
					url: `https://lmhdxcx.lima-info.com:8082/api/tracking/evtTracking/save`,
					data: {
						...values
					},
					success: (res) => {
						console.log('====================================');
						console.log(res);
						console.log('====================================');
					},
				});
			}
		}
	};
</script>

<style lang="scss">
	.bind-vehicle {
		width: 100%;
		min-height: 100vh;
		background: $bg1;
		overflow: hidden;

		.brand-nav {
			overflow: hidden;
			margin-top: 24rpx;
			padding: 0rpx 30rpx;
			border-bottom-left-radius: 0;
			border-bottom-right-radius: 0;
			background: #fff;
		}

		.vehicle-card {
			border-top-left-radius: 0;
			border-top-right-radius: 0;
			background: #fff;
			padding: 0rpx 30rpx;
			padding-bottom: 30rpx;
			box-sizing: border-box;
		}

		.cu-form-group {
			background-color: #ffffff;
			display: flex;
			align-items: center;
			min-height: 100rpx;

			&:not(:first-child) {
				border-top: 1rpx solid #eee;
			}

			.form_input {
				font-size: $fs-base;
			}

			.title {
				font-size: $fs-base;
				min-width: calc(4em + 50rpx);
			}

			.picker {
				text-align: left;
			}

			.placeholder {
				font-weight: 400;
				font-size: $fs-base;
				color: rgba(0, 0, 0, 0.25);
			}

			.el-placeholder {
				color: #9d9d9d;
				;
			}

		}

		.brand-container {
			justify-content: flex-start;

			.brand-btn {
				display: flex;
				justify-content: flex-start;

				.item {
					margin-right: 50rpx;
					padding: 10rpx 30rpx;
					border-radius: 40rpx;
					background-color: #f5f5f5;
					text-align: center;
					font-size: 28rpx;
					color: #000;
				}

				.item.active {
					background-color: #c70e2d;
					color: #fff;
				}
			}
		}

		.tips {
			font-size: $fs-s;
		}
	}

	.arrow-icon {
		width: 24rpx;
		height: 24rpx;
		-webkit-transform: rotate(45deg);
		transform: rotate(45deg);
		margin-left: 20rpx;
		margin-right: 8rpx;
		box-shadow: -2px 2px 0 0px #c7c7c7 inset;
	}

	.el-card {
		margin-left: 30rpx;
		margin-right: 30rpx;
		border-radius: 16rpx;

	}

	.lima-bg-red {
		background-color: #c70e2d !important;
		color: #fff;
	}

	.request {
		color: #ff474a;
	}

	.border-radius-top {
		border-radius: 16rpx 16rpx 0 0;
	}

	.border-radius-bottom {
		border-radius: 0 0 16rpx 16rpx;
	}

	.avatar-wrapper {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 100%;

		.avatar-text {
			font-weight: 400;
			font-size: 28rpx;
			color: rgba(0, 0, 0, 0.25);
		}

		.avatar {
			width: 32rpx;
			height: 32rpx;
			background: #f8f8f8;
		}
	}

	.el-submit-btn-view {
		display: flex;
		position: fixed;
		bottom: 0;
		justify-content: center;
		width: 100%;
		height: calc(100rpx + env(safe-area-inset-bottom));
		background: #fff;
		box-shadow: 0 -2px 10px 0 rgba(0, 21, 61, 0.08);
		z-index: 5;

		.el-submit-btn {
			margin-top: 10rpx;
			border-radius: 40rpx;
			width: 686rpx;
			height: 80rpx;
			background: #c70e2d;
			font-size: 30rpx;
			color: #fff;
		}
	}

	.active-vehicle {
		color: #c6242f;
	}
</style>