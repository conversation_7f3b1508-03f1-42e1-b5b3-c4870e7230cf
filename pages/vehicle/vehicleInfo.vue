<template>
<view class="vehicle-info">
	<view class="form-main">
		<form @submit="handleSubmit">
			<view class="info-group" v-for="(gt, index) in colunms" :key="index">
				<view class="info-title">{{ gt.title }}</view>
				<view class="info-warp">
					<view class="cu-form-group" v-for="item in gt.colunms" :key="item.name">
						<view class="title">{{ item.label }}<text v-if="item.required" class="request">*</text></view>
						<view class="form-wrapper">
							<input v-if="item.type == 'input'" v-model="formData[item.name]" class="form_input"
								:placeholder="item.placeholder" placeholder-class="el-placeholder" :name="item.name" />
							<view v-if="item.type == 'select'" class="form_input" @click="handleSelect(item.name)">
								<text v-if="!!formData[item.name]" class="">{{ formData[item.name] }}</text>
								<text v-else class="el-placeholder form_input">{{ item.placeholder }}</text>
							</view>
							<uv-icon v-if="item.scan" class="suffix-icon" name="scan" size="24"
								@click="scanCode(item.name)"></uv-icon>
							<view class="arrow-icon suffix-icon" v-if="item.type == 'select'"></view>
						</view>
					</view>
				</view>
			</view>
			<view class="el-submit-btn-view">
				<button class="cu-btn block el-submit-btn" form-type="submit">确认并绑定</button>
			</view>
		</form>
	</view>
</view>
</template>
<script>
import dateTimePicker from '@/components/dateTimePicker.vue';
export default {
	components: {
		dateTimePicker,
	},
	data() {
		return {
			colunms: [
				{
					title: '基本信息',
					colunms: [
						{
							type: 'input',
							scan: true,
							name: 'identity',
							label: '车辆识别码',
							placeholder: '请扫描或输入',
							required: true
						},
						{
							type: 'input',
							scan: true,
							name: 'deviceNo',
							label: '车架号',
							placeholder: '车型',
							required: true
						},
						{
							type: 'select',
							name: 'brandId',
							label: '品牌',
							placeholder: '请选择',
							required: false
						}
					]
				},
				{
					title: "电池信息",
					colunms: [
						{
							type: 'select',
							name: 'batteryType',
							label: '电池类型',
							placeholder: '请扫描或输入',
							required: true
						},
						{
							type: 'select',
							name: 'batteryVoltage',
							label: '电池电压',
							placeholder: '车型',
							required: true
						},
						{
							type: 'select',
							name: 'batteryCapacity',
							label: '电池容量',
							placeholder: '请选择',
							required: false
						}
					]
				}
			],
			formData: {
				deviceNo: '',
				identity: '',
				brandId: '',
				batteryNum: '',
				valueName: '',
			}
		};
	},
	onLoad() {

	},
	mounted() {
	},
	methods: {
		// 选择门店
		choiceShop() {
			this.$Router.push({
				path: '/pages/store/storeList'
			});
		},
		// 扫码
		scanCode(type) {
			// #ifdef H5
			window.jsBridgeHelper
				?.sendMessage('qrCode')
				.then((res) => {
					// code: string; msg: string; data: string
					if (res.code === '200') {
						// 正常二维码格式L1ZL1ZEH8P0503159、062310230361
						// 旧版本格式 http://www.lima-info.com/wx/062310230361.aspx
						const value = res.data.replace(/^.*\/([A-Za-z0-9]+)\.aspx$/, '$1').trim();
						this.vin = value;
					}
				});
			// #endif
			// #ifndef H5
			uni.scanCode({
				onlyFromCamera: false,
				scanType: ['qrCode'],
				success: (res) => {
					// res.result 可能是二维码内容
					const value = res.result.replace(/^.*\/([A-Za-z0-9]+)\.aspx$/, '$1').trim();
					this.vin = value;
				},
				fail: (err) => {
					console.error('扫码失败:', err);
				}
			});
			// #endif
		},
		validateForm(params) {
			return new Promise((resolve, reject) => {
				if (!params.identity) {
					reject({ errmsg: '请输入车辆识别码' });
				}
				if (!params.deviceNo) {
					reject({ errmsg: '请输入车架号' });
				}
				if (!params.brandId) {
					reject({ errmsg: '请选择品牌' });
				}
				resolve({
					errmsg: '',
					value: params
				});
			});
		},

		// 确认并绑定 接口
		handleSubmit(e) {
			const value = e.detail.value;
			this.validateForm(value).then((res) => {
				console.log('提交的车辆信息:', value);
				if (res.errmsg) return;
				// if (res) {
				// 	this.$dialog.loading();
				// 	const params = {
				// 		...value,
				// 		brandId: this.formData.brandId,
				// 		batteryNum: this.formData.batteryNum,
				// 		valueName: this.formData.valueName
				// 	};
				// 	this.bindVehicleSave(params);
				// }
			}).catch(err => {
				console.log('表单验证失败:', err.errmsg);
				this.$api.msg(err.errmsg);
			});
		},

		async bindVehicleSave(params) {
			this.$dialog.loading();
			const res = await this.$http.post('/yst/aftersale/miniApp/bindVehicle/bindSoldVehicleSave2', {
				...params
			});
			this.$dialog.loaded();
			if (res.success) {
				this.$dialog.success('绑定成功');

				// 调用达摩添加会员标签
				await this.$http.post('/yst/aftersale/demogic/saveMemberTag', {
					mobile: this.userInfo.userPhone,
					tagItemIds: this.tagItemIds
				});

				const query = this.$mp && this.$mp.query;
				if (query?.page === 'profile') {
					uni.navigateTo({
						url: packageConfig.mine + '/pages/mineVehicle/mineVehicle'
					});
				} else {
					uni.navigateBack({
						delta: 1
					});
				}
			} else {
				this.$dialog.toast(res.msg);
			}
		}
	}
};
</script>

<style lang="scss">
.vehicle-info {
	width: 100%;
	min-height: 100vh;
	background: #eef1f4;

	.form-main {
		width: 100%;
		padding: 40rpx 0;
		box-sizing: border-box;

		.request {
			color: #c70e2d;
		}

		.info-group {
			width: 92%;
			margin: 0 auto;

			.info-title {
				font-size: $fs-base;
				color: #908f94;
				margin-bottom: 20rpx;
			}

			.info-warp {
				background: #fff;
				border-radius: 16rpx;
				padding: 30rpx;
				box-sizing: border-box;
				margin-bottom: 30rpx;

				.cu-form-group {
					margin-bottom: 20rpx;
				}

				.form-wrapper {
					display: flex;
					align-items: center;

					.input-wrapper {
						flex: 1;
					}
				}
			}
		}
	}

	.cu-form-group {
		background-color: #ffffff;
		display: flex;
		justify-content: space-between;
		align-items: center;
		min-height: 100rpx;

		&:not(:last-child) {
			border-bottom: 1rpx solid #eee;
		}

		.form_input {
			font-size: $fs-base;
		}

		.title {
			width: 200rpx;
			font-size: $fs-base;
		}

		.form-wrapper {
			flex: 1 1 calc(100% - 200rpx);
			display: flex;
			align-items: center;
			justify-content: space-between;

			.form_input {
				flex: 1 1 calc(100% - 50rpx);
			}

			.suffix-icon {
				width: 50rpx;
			}

			.arrow-icon {
				width: 24rpx;
				height: 24rpx;
				-webkit-transform: rotate(45deg);
				transform: rotate(45deg);
				margin-left: 10rpx;
				margin-right: 20rpx;
				box-shadow: -2px 2px 0 0px #c7c7c7 inset;
			}
		}

		.picker {
			text-align: left;
		}

		.placeholder {
			font-weight: 400;
			font-size: $fs-base;
			color: rgba(0, 0, 0, 0.25);
		}

		.el-placeholder {
			color: #9d9d9d;
			;
		}

	}
}

.el-submit-btn-view {
	display: flex;
	position: fixed;
	bottom: 0;
	justify-content: center;
	width: 100%;
	height: calc(100rpx + env(safe-area-inset-bottom));
	background: #fff;
	box-shadow: 0 -2px 10px 0 rgba(0, 21, 61, 0.08);
	z-index: 5;

	.el-submit-btn {
		margin-top: 10rpx;
		border-radius: 40rpx;
		width: 686rpx;
		height: 80rpx;
		background: #c70e2d;
		font-size: 30rpx;
		color: #fff;
	}
}
</style>