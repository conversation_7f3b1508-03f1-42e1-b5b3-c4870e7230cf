<template>
    <view class="container" :style="mix_diyStyle">
        <view class="full_screen_swiper">
            <swiper class="swiper" :autoplay="false">
                <swiper-item v-for="item in modelInfo" :key="item.id">
                    <view class="full_screen" :style="{ backgroundImage: `url(${item.kvImgUrl})` }">
                        <view class="footer_action flex_row_center_center">
                            <view class="btn" @click="jumpDetail(item)">{{ $L('查看车辆') }}</view>
                            <view class="btn" @click="bindVehicle">{{ $L('绑定车辆') }}</view>
                        </view>
                    </view>
                </swiper-item>
            </swiper>
        </view>
    </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import { customTabbarShow, customTabbarHide } from '@/utils/common';
export default {
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            bikeBannerUrl: getApp().globalData.imgUrl + 'default/bike-banner.jpg', //车辆封面图
            modelInfo: []
        };
    },
    computed: {
        ...mapState(['userInfo', 'userCenterData'])
    },
    onLoad() {
        this.getVehicleModelInfo();
        setTimeout(() => {
            uni.setNavigationBarTitle({
                title: this.$L('车辆') + '-' + this.$L('首页')
            });
        }, 0);
    },
    onShow() {
        customTabbarShow(2);
    },
    onHide() {
        customTabbarHide(2);
    },
    methods: {
        // 跳转到车辆详情
        jumpDetail(item) {
            if (item.link) {
                this.$Router.push({
                    path: '/pages/vehicle/webview',
                    query: {
                        link: item.link
                    }
                });
            }
        },
        // 绑定车辆
        bindVehicle() {
            this.$Router.push({
                path: '/newPages/vehicle/bindVehicle'
            });
        },
        //获取车型信息
        getVehicleModelInfo() {
            this.$request({
                url: 'v3/vehicle/front/recommendModel/list',
                method: 'GET'
            })
                .then((res) => {
                    if (res.state == 200) {
                        let result = res.data;
                        this.modelInfo = result.list;
                    } else {
                        this.$api.msg(res.msg);
                    }
                })
                .catch((e) => {});
        },
        navTo(url) {
            this.$Router.push(url);
        }
    }
};
</script>

<style lang="scss">
.container {
    background: $bg1;
    width: 750rpx;
    margin: 0 auto;

    .full_screen_swiper {
        width: 100%;
        height: 100vh;

        .swiper {
            width: 100%;
            height: 100%;
        }
    }

    .full_screen {
        width: 100%;
        height: 100vh;
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;
        position: relative;

        .footer_action {
            position: absolute;
            bottom: 150rpx;
            left: 0;
            width: 100%;
            box-sizing: border-box;
            padding: 20rpx 4%;
            column-gap: 80rpx;

            .btn {
                width: 35%;
                height: 80rpx;
                font-size: $color1;
                text-align: center;
                line-height: 80rpx;
                border-radius: 10rpx;
                color: #000;
                background-color: #fff;
            }
        }
    }
}
</style>
