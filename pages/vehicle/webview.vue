<template>
	<web-view :src="url"></web-view>
</template>
<script>
	export default {
		data() {
			return {
				url: '' // 用于存储传入的链接
			};
		},
		onLoad() {
			const {
				link
			} = this.$Route.query;
			console.log('WebView link:', link);
			// 获取传入的链接
			if (link) {
				this.url = link;
			}
		},
		methods: {
			handleMessage(event) {
				// 处理来自WebView的消息
				console.log('Received message from WebView:', event.detail.data);
				// 可以在这里添加处理逻辑
			}
		}
	};
</script>