<template>
    <view class="container" :style="{ backgroundImage: `url(${imgUrl + 'default_bg.jpg'})` }">
        <KTabbar :title="$L('登录注册')" :showLeft="true" :bgColor="'transparent'" />
        <view class="login_logo flex_row_center_center">
            <image class="logo" :src="imgUrl + 'lm_logo_2025.png'" mode="widthFix" />
        </view>
        <view class="login_form">
            <view class="button-action">
                <button v-show="!check" @tap="handleLogin" class="authorization-button">手机号快捷登录</button>
                <button v-show="check" class="authorization-button" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">手机号快捷登录</button>
            </view>
            <view class="check-box">
                <view class="checkbox-icon">
                    <img class="icon" :src="imgUrl + 'register_uncheck.png'" v-if="!check" mode="widthFix" @click="check = true" />
                    <img class="icon" :src="imgUrl + 'register_checked.png'" v-if="check" mode="widthFix" @click="check = false" />
                </view>
                <view class="el-text-tip">
                    <text @click="check = !check">我已阅读并同意</text>
                    <text class="el-text-agreement" @click="toPrivacy('register_agreement')">《用户协议》</text>
                    <text>和</text>
                    <text class="el-text-agreement" @click="toPrivacy('privacy_policy')">《隐私政策》</text>
                </view>
            </view>
        </view>
        <!-- #ifdef MP -->
        <privacyPop ref="priPop"></privacyPop>
        <!-- #endif -->
    </view>
</template>

<script>
import KTabbar from '@/components/ktabbar.vue';
import privacyPop from '@/components/privacy-pop.vue';
import { mapMutations, mapState } from 'vuex';
import { imgUrl } from '../../utils/config';
export default {
    components: {
        KTabbar,
        // #ifdef MP
        privacyPop
        // #endif
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            check: false, // 是否同意隐私政策
            loginStatus: false, // 登录状态
            predesc: '在您使用立马车主小程序服务之前，请仔细阅读',
            subdesc: '如您同意该指引，请点击“同意”开始使用本小程序',
            code: '' // 微信登录code
        };
    },
    computed: {
        ...mapState(['userCenterData'])
    },
    onLoad() {
        uni.setNavigationBarTitle({ title: '登录注册' });
    },
    onShow() {
        let _this = this;
        // #ifdef MP
        // 判断用户隐私授权
        if (!getApp().globalData.allow_privacy) {
            if (wx.getPrivacySetting == undefined) {
                //微信低版本不适配该授权方法
                getApp().globalData.allow_privacy = true;
            } else {
                wx.getPrivacySetting({
                    success(res) {
                        console.log('getPrivacySetting', res);
                        if (res.needAuthorization) {
                            _this.$refs.priPop.PrivacyProtocol = {
                                needAuthorization: res.needAuthorization,
                                privacyContractName: res.privacyContractName
                            };
                            _this.$refs.priPop.open();
                        } else {
                            getApp().globalData.allow_privacy = true;
                        }
                    }
                });
            }
        }
        // #endif
    },
    mounted() {
        uni.login({
            success: (res) => {
                this.code = res.code;
            }
        });
    },
    methods: {
        ...mapMutations(['login', 'setUserCenterData']),
        // 点击登录按钮
        handleLogin() {
            if (!this.check) {
                uni.showToast({
                    title: '请先同意隐私政策',
                    icon: 'none'
                });
            }
        },
        // 协议勾选
        handleChange() {
            var _this = this;
            this.check = !this.check;
            if (!this.code) {
                uni.login({
                    success(res) {
                        _this.code = res.code;
                    },
                    fail(err) {
                        console.log('协议勾选后获取login code 失败：' + err);
                    }
                });
            }
        },
        // 授权获取微信手机号
        async getPhoneNumber(e) {
            // @e.detail.code 动态令牌
            // @e.detail.errMsg 回调信息（成功失败都会返回）
            // @e.detail.errno  错误码（失败时返回）
            if (this.loginStatus) return;
            this.loginStatus = true;
            if (!this.code) {
                uni.login({
                    success: (res) => {
                        this.code = res.code;
                    },
                    fail: (err) => {
                        console.log('授权获取微信手机号后--获取login code失败：' + err);
                    }
                });
                this.loginStatus = false;
                return;
            }
            const localStore = uni.getStorageSync(`openData`);
            const openData = localStore ? JSON.parse(localStore) : {};
            if (!e.detail.errno && e.detail.code) {
                let param = {
                    mobileCode: e.detail.code,
                    code: this.code,
                    path: openData.path || '', //打开的页面
                    scene: openData.scene || '', // 微信场景
                    pos: openData.pos || ''
                };
                if (openData.shareId) {
                    param.shareId = openData.shareId;
                }
                this.bindMobile(param);
            } else {
                this.$api.msg('获取手机号失败，请重试');
                this.loginStatus = false;
            }
        },
        // 手机号登录、注册
        bindMobile(data) {
            uni.showLoading({ title: '登录中...', mask: true });
            let param = {};
            param.url = 'v3/member/front/login/mini/mobileLogin';
            param.data = { ...data };
            //如果推手分享，则建立推手分享关系
            if (this.spreaderMemberId) {
                param.data.spreaderKey = this.spreaderMemberId;
            }
            param.method = 'POST';
            this.$request(param)
                .then((res) => {
                    uni.hideLoading();
                    this.loginStatus = false;
                    if (res.state == 200) {
                        //更新登录时间
                        uni.setStorage({ key: 'lm_login_time', data: new Date().getTime() });
                        res.data.loginTime = Date.parse(new Date()); //登录时间
                        this.login(res.data);
                        //统计埋点
                        this.$sldStatEvent({ behaviorType: 'reg' });
                        //获取个人中心的数据
                        this.$request({
                            url: 'v3/member/front/member/memberInfo'
                        }).then((result) => {
                            this.setUserCenterData(result.data);
                            this.$loginGoPage();
                        });
                        this.$api.msg(res.msg);
                    } else if (res.state == 267) {
                        //手机号已被绑定,提示用户
                        this.$refs.bind.open();
                        this.bindedAccount = res.data;
                    } else {
                        //错误提示
                        this.$api.msg(res.msg);
                    }
                })
                .catch((err) => {
                    uni.hideLoading();
                    this.$api.msg('登录失败，请稍后重试');
                    this.loginStatus = false;
                });
        },
        //跳转协议页面
        toPrivacy(type) {
            this.$Router.push({
                path: '/pages/privacyPolicy/privacyPolicy',
                query: {
                    type: type
                }
            });
        }
    }
};
</script>

<style lang="scss">
page {
    overflow: hidden;
    background-color: $bg1;
    background-repeat: no-repeat;
    background-position: top;
    background-size: 100% auto;
    .check-box {
        display: flex;
        align-items: center;
        justify-content: center;
        .checkbox-icon {
            .icon {
                margin-top: 12rpx;
                width: 50rpx;
                height: 50rpx;
            }
        }
        .el-text-tip {
            font-size: $fs-s;
            color: #000000;
        }
        .el-text-agreement {
        }
    }
    .login_logo {
        width: 100%;
        padding-top: 8vh;

        .logo {
            width: 45%;
        }
    }

    .login_form {
        margin-top: 100rpx;
    }
}

.container {
    width: 100%;
    height: 100vh;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    box-sizing: border-box;
    padding: 0 4%;
    display: flex;
    gap: 20rpx;
    flex-direction: column;

    .button-action {
        .authorization-button {
            width: 92%;
            height: 96rpx;
            border-radius: 48rpx;
            line-height: 96rpx;
            background: rgba(73, 73, 73, 1);
            color: #fff;
            margin-bottom: 20rpx;

            &::after {
                border: none;
                background: none !important;
            }
        }

        .phone-btn {
            width: 80%;
            height: 96rpx;
            border-radius: 48rpx;
            line-height: 96rpx;
            background: transparent;
            border: 1px solid $border-color-split;
            color: #666666;

            &::after {
                border: none;
                background: none !important;
            }
        }
    }
}
</style>
