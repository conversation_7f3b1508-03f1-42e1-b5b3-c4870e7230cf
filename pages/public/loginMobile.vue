<template>
	<view :style="mix_diyStyle">
		<view class="container" :style="{ height: screenHeight }">
			<!-- #ifndef MP -->
			<view class="back-btn" :class="{wx_back_btn:isWeiXinH5}">
				<text class="iconfont iconziyuan2" @click="navBack"></text>
			</view>
			<!-- #endif -->
			<!-- 设置白色背景防止软键盘把下部绝对定位元素顶上来盖住输入框等 -->
			<view class="wrapper">
				<view class="login-title">
					{{ $L('验证码登录') }}
				</view>
				<view class="input-content">
					<view class="input-item">
						<input type="number" :value="mobile" :placeholder="$L('请输入手机号')" maxlength="11" data-key="mobile"
							@input="inputChange" @focus="setFocus" placeholder-class="placeHoldMo" />
						<text class="clear-account iconfont iconziyuan34" v-show="mobile && curFocus == 'mobile'"
							@click="clearContent('mobile')"></text>
					</view>
					<view class="input-item">
						<input type="text" :value="imgCode" maxlength="4" :placeholder="$L('请输入图形验证码')" data-key="imgCode"
							@input="inputChange" @focus="setFocus" placeholder-class="placeHoldMo" />
						<view class="pwd-right">
							<text class="clear-pwd iconfont iconziyuan34" v-show="imgCode && curFocus == 'imgCode'"
								@click="clearContent('imgCode')"></text>
							<image @click="getImgCode" :src="showCodeImg" class="img_code"/>
						</view>
					</view>
					<view class="input-item pwd_wrap">
						<input type="number" :value="smsCode" maxlength="6" :placeholder="$L('请输入短信验证码')" data-key="smsCode"
							@input="inputChange" @confirm="toLogin" @focus="setFocus" placeholder-class="placeHoldMo" />
						<view class="pwd-right">
							<text class="clear-pwd iconfont iconziyuan34" v-show="smsCode && curFocus == 'smsCode'"
								@click="clearContent('smsCode')"></text>
							<view :style="{ opacity: countDownM ? 0.3 : 1 }" class="sms-code-view" @click="getSmsCode">
								<text class="sms-code">{{countDownM? `${countDownM}s${$L('后重新获取')}`: $L('获取验证码')}}</text>
							</view>
						</view>
					</view>
				</view>
				<button class="confirm-btn" :class="{wx_confirm_btn:isWeiXinH5}" @click="toLogin"
					:style="{ opacity: !(mobile && smsCode&&imgCode) || logining ? 0.5 : 1 }">
					{{ $L('登录') }}
				</button>
				<view class="login-register">
					<text class="mobile-login" @click="navTo('/pages/public/login', 1)">{{$L('密码登录')}}</text>
					<text class="register" @click="navTo('/pages/public/register', 1)">{{$L('用户注册')}}</text>
				</view>
			</view>
			<view class="other-login">
				<!-- #ifdef H5 -->
				<view class="title" v-if="isWeiXinH5">
					<text>{{ $L('其他登录') }}</text>
				</view>
				<!-- #endif -->

				<!-- #ifndef H5 -->
				<view class="title" v-if="isWxEnable == 1">
					<text>{{ $L('其他登录') }}</text>
				</view>
				<!-- #endif -->

				<view class="login-method">
					<!-- wx-1-start -->
					<!-- #ifdef MP-WEIXIN -->
					<button class="wechat-login" v-if="canIUseGetUserProfile" @tap="getUserProfile">
						<image class="wechat-icon" :src="imgUrl + 'wechat_icon.png'" mode="aspectFill" />
						<text>{{ $L('快捷登录') }}</text>
					</button>

					<button class="wechat-login" v-if="!canIUseGetUserProfile" open-type="getUserInfo"
						@getuserinfo="getUser">
						<image class="wechat-icon" :src="imgUrl + 'wechat_icon.png'" mode="aspectFill" />
						<text>{{ $L('快捷登录') }}</text>
					</button>
					<!-- #endif -->
					<!-- wx-1-end -->
					<!-- #ifdef H5-->
					<view class="wechat-login" @tap="quickLogin" v-if="isWeiXinH5">
						<image class="wechat-icon" :src="imgUrl + 'wechat_icon.png'" mode="aspectFill" />
						<text>{{ $L('快捷登录') }}</text>
					</view>
					<!-- #endif -->
					<!-- app-1-start -->
					<!-- #ifdef APP-PLUS -->
					<view class="wechat-login" @tap="quickLogin" v-if="isWxEnable == 1">
						<image class="wechat-icon" :src="imgUrl + 'wechat_icon.png'" mode="aspectFill" />
						<text>{{ $L('快捷登录') }}</text>
					</view>
					<!-- #endif -->
					<!-- app-1-end -->
				</view>
			</view>
			<!-- #ifndef APP-PLUS -->
			<view class="agreement-part">{{ $L('登录即代表您已同意') }}<text class="agreement" @click="agreement('register_agreement')">{{$L('《用户协议》')}}</text>
				{{ $L('和') }}
				<text class="agreement" @click="agreement('privacy_policy')">{{$L('《隐私协议》')}}</text>
			</view>
			<!-- #endif -->
			
			<!-- app-2-start -->
			<!-- #ifdef APP-PLUS -->
			<view class="agreement-part flex_row_center_center">
				<image @click="checkAgrement" class="register_icon" :src="show_check_icon" mode="aspectFill" />
				{{ $L('我已阅读并同意') }}
				<text class="agreement" @click="agreement('register_agreement')">{{$L('《用户协议》')}}</text>
				{{ $L('和') }}
				<text class="agreement" @click="agreement('privacy_policy')">{{$L('《隐私协议》')}}</text>
			</view>
			<!-- #endif -->
			<!-- app-2-end -->
			<!-- wx-2-start -->
			<!-- #ifdef MP-WEIXIN -->
			<wxBindPhone ref="wxBindPhone"></wxBindPhone>
			<!-- #endif -->
			<!-- wx-2-end -->
			<!-- #ifdef MP -->
			<privacyPop ref="priPop"></privacyPop>
			<!-- #endif -->
		</view>
	</view>
</template>

<script>
	import wxBindPhone from '@/components/wxBindPhone.vue'
	import { mapMutations } from 'vuex'
	import {fetchImgCode} from '@/utils/common.js'
	// #ifdef MP
	import privacyPop from '@/components/privacy-pop.vue';
	// #endif

	export default {
		components: {
			wxBindPhone,
			// #ifdef MP
			privacyPop,
			// #endif
		},
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				showCodeImg: '',
				imgCode: '', //图形验证码
				imgCodeKey: '', //图形验证码的key
				mobile: '',
				smsCode: '',
				logining: false,
				countDownM: 0, //短信验证码倒计时
				timeOutId: '', //定时器的返回值
				curFocus: '', //当前光标所在的位置
				client: 1, //终端类型， 1、H5(微信内部浏览器) 2、H5(微信小程序)；3、app
				oriUrl: '', //不带code的页面地址
				isWeiXinH5: true, //是否为微信浏览器h5
				check_agreement: false,
				show_check_icon: getApp().globalData.imgUrl + 'register_uncheck.png',
				canIUseGetUserProfile: false, //是否可以使用getUserProfile获取用户信息，用于微信小程序
				isWxEnable: 0,
				isPreClick: false,
				screenHeight: '100vh', //屏幕可用高
			}
		},
		onLoad(option) {
			this.client = this.$wxLoginClient()
			if (
				uni.getSystemInfoSync().safeArea &&
				uni.getSystemInfoSync().safeArea.height > 0
			) {
				this.screenHeight = uni.getSystemInfoSync().safeArea.height + 'px'
			}
			//wx-3-start
			//#ifdef MP-WEIXIN
			if (uni.getUserProfile) {
				this.canIUseGetUserProfile = true
			}
			//#endif
			//wx-3-end
			//#ifdef H5			
			if(uni.getStorageSync('u')){
				this.spreaderMemberId = uni.getStorageSync('u')
			}
			
			let code = this.$getQueryVariable('code')
			this.isWeiXinH5 = this.$isWeiXinBrower()
			if (code) {
				let oriUrl = this.$Route.query.ori_url + 'pages/public/loginMobile'
				let tmp_data = ''
				for (let i in this.$Route.query) {
					if (i != 'ori_url') {
						tmp_data += i + '=' + this.$Route.query[i] + '&'
					}
				}
				oriUrl += '?' + tmp_data
				this.oriUrl = oriUrl

				if (this.$isWeiXinBrower()) {
					//微信浏览器的话要把浏览器地址里面的code去掉
					history.replaceState({}, '', this.oriUrl)
				}

				let tar_params = {}
				tar_params.source = this.client
				tar_params.code = code
				this.goLogin(tar_params)
			}
			//#endif
			//app-3-start
			// #ifdef APP-PLUS
			this.getWxAuthority()
			// #endif
			//app-3-end
			this.getImgCode()
		},
		onShow() {
			let _this = this;
			// #ifdef MP
			// 判断用户隐私授权
			if (!getApp().globalData.allow_privacy) {
				if (wx.getPrivacySetting == undefined) {
					//微信低版本不适配该授权方法
					getApp().globalData.allow_privacy = true;
				} else {
					wx.getPrivacySetting({
						success(res) {
							if (res.needAuthorization) {
								_this.$refs.priPop.PrivacyProtocol = {
									needAuthorization: res.needAuthorization,
									privacyContractName: res.privacyContractName
								};
								_this.$refs.priPop.open();
							} else {
								getApp().globalData.allow_privacy = true;
							}
						}
					})
				}
			}
			// #endif
		},
		methods: {
			...mapMutations(['login', 'setUserCenterData']),
			//注册协议点击事件
			checkAgrement() {
				this.check_agreement = !this.check_agreement
				this.show_check_icon = this.check_agreement ?
					getApp().globalData.imgUrl + 'register_checked.png' :
					getApp().globalData.imgUrl + 'register_uncheck.png'
			},
			getUser(e) {
				// #ifdef MP
				if (!getApp().globalData.allow_privacy) {
					this.$refs.priPop.open();
					return;
				}
				// #endif
				if (e.detail.errMsg == 'getUserInfo:ok') {
					let userinfo = e.detail.userInfo
					this.getWxXcxCoce(userinfo)
				}
			},
			
			//获取图形验证码
			async getImgCode() {
				const result = await fetchImgCode()
				this.showCodeImg = result.codeImg
				this.imgCodeKey = result.imgCodeKey
			},

			//微信小程序根据用户信息获取code
			getWxXcxCoce(userinfo) {
			
				let {
					client
				} = this
				let _this = this
				uni.showLoading({
					title: _this.$L('正在请求...'),
					mask: true
				})
				uni.login({
					success: (code) => {
						let tar_params = {}
						tar_params.source = client
						tar_params.code = code.code
						tar_params.userInfo = JSON.stringify(userinfo)
						_this.goLogin(tar_params)
					}
				})
			},

			getUserProfile(e) {
				// #ifdef MP
				if (!getApp().globalData.allow_privacy) {
					this.$refs.priPop.open();
					return;
				}
				// #endif
				// 推荐使用wx.getUserProfile获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认
				// 开发者妥善保管用户快速填写的头像昵称，避免重复弹窗
				uni.getUserProfile({
					desc: '用于完善个人信息', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
					success: (res) => {
						if (res.errMsg == 'getUserProfile:ok') {
							let userinfo = res.userInfo
							this.getWxXcxCoce(userinfo)
						}
					}
				})
			},
			//授权登录
			quickLogin() {
				//app-4-start
				// #ifdef APP-PLUS
				if (!this.check_agreement) {
					this.$api.msg(this.$L('请同意用户隐私政策!'))
					return false
				}
				// #endif
				//app-4-end
			
				let {
					client
				} = this
				let _this = this
				//app-5-start
				//#ifdef APP-PLUS
				uni.login({
					provider: 'weixin',
					success: function(loginRes) {
						if (loginRes.errMsg == 'login:ok') {
							//授权登录成功
							// 获取用户信息
							uni.getUserInfo({
								provider: 'weixin',
								success: function(infoRes) {
									let tar_params = {}
									tar_params.unionid = loginRes.authResult.unionid
									tar_params.openid = loginRes.authResult.openid
									tar_params.userInfo = JSON.stringify(infoRes.userInfo)
									tar_params.source = client
									_this.goLogin(tar_params)
								}
							})
						}
					}
				})
				//#endif
				//app-5-end
				//#ifdef H5
				let tar_url = location.href
				tar_url += location.href.indexOf('?') > -1 ? '&' : '?'
				tar_url += 'ori_url=' + location.href
				let uricode = encodeURIComponent(tar_url)
				window.location.href =
					'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' +
					getApp().globalData.h5AppId +
					'&redirect_uri=' +
					uricode +
					'&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect'
				//#endif
			},
			//登录 data为除购物车信息外的接口参数，对象类型
			goLogin(data) {
				//app-6-start
				// #ifdef APP-PLUS
				if (!this.check_agreement) {
					this.$api.msg(this.$L('请同意用户隐私政策!'))
					return false
				}
				// #endif
				//app-6-end
				let _this = this
				let param = {}
				param.url = 'v3/member/front/login/wechat/login'
				param.data = {
					...data
				}
				//app-7-start
				// #ifdef APP-PLUS
				param.data.clientId = uni.getStorageSync('clientId');
				param.data.appType = uni.getDeviceInfo().platform == 'android' ? 1 : 2
				// #endif
				//app-7-end
				//如果有缓存的购物车数据，登录需要把数据同步，并清除本地缓存
				if (this.$getUnLoginCartParam()) {
				  param.data.cartInfo = this.$getUnLoginCartParam()
				}
				param.method = 'POST'
				this.$request(param)
					.then((res) => {
						if (res.state == 200) {
							//更新登录时间
							uni.setStorage({
								key: 'lm_login_time',
								data: new Date().getTime()
							})
							
							if (res.data.redirect == undefined) {
								uni.removeStorage({
									key: 'cart_list'
								}) //清除购物车数据
								res.data.loginTime = Date.parse(new Date()) //登录时间
								_this.login(res.data)
								//登录成功 获取个人中心的数据
								_this
									.$request({
										url: 'v3/member/front/member/memberInfo'
									})
									.then((result) => {
										_this.setUserCenterData(result.data)
										_this.$loginGoPage()
									})
									.catch((e) => {})
							} else if (res.data.redirect != undefined) {
								//用户未注册，需要绑定手机号进行注册
								//#ifdef H5
								let isWeiXinH5 = this.$isWeiXinBrower()
								if (isWeiXinH5 && this.$getQueryVariable('code')) {
									//如果是微信浏览器，而且浏览器地址里有code，需要把code再跳转下一页，防止返回的时候再去验证code
									history.replaceState({}, '', location.href.split('?')[0])
								}
								//#endif

								// #ifndef MP-WEIXIN
								let query = {
									code: res.data.bindKey
								}
								if (this.spreaderMemberId) {
									query.spreaderKey = this.spreaderMemberId
								}
								_this.$Router.push({
									path: '/pages/public/bindMobile',
									query
								})
								// #endif
								//wx-4-start
								// #ifdef MP-WEIXIN
								this.$refs.wxBindPhone.openKey(res.data.bindKey)
								// #endif
								//wx-4-end
							}
						} else {
							//错误提示
							_this.$api.msg(res.msg)
							this.getImgCode()
						}
						uni.hideLoading()
					})
					.catch((e) => {
						uni.hideLoading()
					})
			},
			//光标聚焦事件
			setFocus(e) {
				this.curFocus = e.currentTarget.dataset.key
			},
			inputChange(e) {
				const key = e.currentTarget.dataset.key
				this[key] = e.detail.value
			},
			navBack() {
				this.$Router.back(1)
			},
			toLogin() {
				const {
					mobile,
					smsCode,
					logining,
					imgCode,
					imgCodeKey
				} = this
				if (!(mobile && smsCode) || logining) {
					return
				}
				
				let codeCheck = this.$checkImgCode(this.imgCode)
				if(codeCheck!==true){
					this.imgCode = ''
					this.$api.msg(codeCheck)
					return false
				}
				
				//app-8-start
				// #ifdef APP-PLUS
				if (!this.check_agreement) {
					this.$api.msg(this.$L('请同意用户隐私政策!'))
					return false
				}
				// #endif
				//app-8-end
				
				let param = {}
				param.url = 'v3/member/front/login/freeSecretLogin'
				param.data = {}
				param.data.mobile = mobile
				param.data.smsCode = smsCode
				param.data.source = this.$getLoginClient()
				param.data.verifyCode = imgCode
				param.data.verifyKey = imgCodeKey
				if (!this.$checkMobile(mobile)) {
					return false
				}
				//如果有缓存的购物车数据，登录需要把数据同步，并清除本地缓存
				if (this.$getUnLoginCartParam()) {
				  param.data.cartInfo = this.$getUnLoginCartParam()
				}
				param.method = 'POST'
				this.logining = true
				this.$request(param)
					.then((res) => {
						if (res.state == 200) {
							//更新登录时间
							//更新登录时间
							uni.setStorage({
								key: 'lm_login_time',
								data: new Date().getTime()
							})
							uni.removeStorage({
								key: 'cart_list'
							}) //清除购物车数据
							//获取个人中心的数据
							res.data.loginTime = Date.parse(new Date()) //登录时间
							this.login(res.data)
							this.$request({
									url: 'v3/member/front/member/memberInfo'
								})
								.then((result) => {
									this.setUserCenterData(result.data)
									this.$loginGoPage()
								})
								.catch((e) => {})
						} else {
							//错误提示
							this.$api.msg(res.msg)
							this.getImgCode()
						}
						this.logining = false
					})
					.catch((e) => {})
			},

			//清空输入的内容
			clearContent(type) {
				this[type] = ''
			},

			//获取短信验证码
			getSmsCode() {
				if (this.isPreClick) {
					return
				}
				this.isPreClick = true

				if (this.countDownM) {
					return
				}
				if (!this.$checkMobile(this.mobile)) {
					this.isPreClick = false
					return false
				} else if (!this.imgCode) {
					this.isPreClick = false
					this.$api.msg(this.$L('请输入图形验证码!'))
				}  else {
					let param = {}
					param.url = 'v3/msg/front/commons/smsCode'
					param.data = {}
					param.data.mobile = this.mobile
					param.data.type = 'free'
					param.data.verifyCode = this.imgCode
					param.data.verifyKey = this.imgCodeKey
					this.$request(param).then((res) => {
						this.$api.msg(res.msg)
						if (res.state == 200) {
							this.countDownM = 60
							this.countDown()
						} else {
							this.isPreClick = false
						}
					})
				}
			},
			//账号登录跳转事件
			loginByAccount() {
				this.$Router.replace('/pages/public/login')
			},
			//跳转事件 type:跳转类型，1为redirectTo 2为navigateTo
			navTo(url, type) {
				if (type == 1) {
					this.$Router.replace(url)
				} else if (type == 2) {
					this.$Router.push(url)
				}
			},
			//倒计时
			countDown() {
				this.countDownM--
				if (this.countDownM == 0) {
					this.isPreClick = false
					clearTimeout(this.timeOutId)
				} else {
					this.timeOutId = setTimeout(this.countDown, 1000)
				}
			},
			//跳转协议页面
			agreement(type) {
				this.$Router.push({
					path: '/pages/privacyPolicy/privacyPolicy',
					query: {
						type: type
					}
				})
			},
			//微信登录互联开关
			getWxAuthority() {
				this.$request({
					url: 'v3/system/front/setting/getSettings',
					data: {
						names: 'login_wx_app_is_enable'
					}
				}).then((res) => {
					this.isWxEnable = res.data[0]
				})
			}
		}
	}
</script>

<style lang="scss">
	
	/* #ifdef H5 */
	@media screen and (max-height:736px) {
		.wx_back_btn{
			display: none !important;
		}
		
		.wx_confirm_btn{
			margin: 60rpx auto !important;
		}
	}
	/* #endif */
	
	
	page {
		background: #fff;
		width: 750rpx;
		margin: 0 auto;
	}

	.container {
		position: relative;
		width: 750rpx;
		height: 100vh;
		overflow: hidden;
		background: #fff;
	}

	.placeHoldMo {
		color: $main-third-color;
		font-size: 28rpx;
	}

	.back-btn {
		margin-left: 40rpx;
		margin-top: 40rpx;
		/* #ifndef H5 */
		margin-top: 88rpx;
		/* #endif */
		font-size: 32rpx;
		color: $main-font-color;
		display: inline-block;
	}

	.login-title {
		position: relative;
		margin-top: 60rpx;
		margin-bottom: 70rpx;
		margin-left: 65rpx;
		font-size: 36rpx;
		color: #333;
		font-weight: bold;

		&:after {
			position: absolute;
			left: 0;
			bottom: -10rpx;
			content: '';
			width: 76rpx;
			height: 6rpx;
			background: linear-gradient(90deg,
					var(--color_main) 0%,
					rgba(255, 138, 0, 0) 100%);
		}
	}

	.input-content {
		padding: 0 65rpx;
	}

	.input-item {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-content: center;
		height: 80rpx;
		margin-bottom: 50upx;
		border-bottom: 1px solid rgba(0, 0, 0, 0.1);
		position: relative;

		input {
			color: #2d2d2d;
			font-size: 30rpx;
		}

		.clear-account {
			position: absolute;
			right: 6rpx;
			top: 28rpx;
			font-size: 26rpx;
			color: #ddd;
		}

		image {
			width: 132rpx;
			height: 55rpx;
			box-shadow: 0px 5rpx 9rpx 1rpx rgba(102, 102, 102, 0.1);
			border-radius: 6rpx;
			margin-left: 20rpx;
		}

			.pwd-right {
				position: absolute;
				right: 6rpx;
				top: 6rpx;
				display: flex;
				align-items: center;

				.clear-pwd {
					font-size: 26rpx;
					color: #ddd;
				}

				.sms-code-view {
					border: 1px solid var(--color_main);
					padding: 14rpx;
					border-radius: 6rpx;
					line-height: 0;
					margin-left: 20rpx;

					.sms-code {
						color: var(--color_main);
						font-size: 24rpx;
						line-height: 24rpx;
					}
				}
			}
		

		.tit {
			height: 50upx;
			line-height: 56upx;
			font-size: $font-sm + 2upx;
			color: $font-color-base;
		}

		input {
			height: 60upx;
			font-size: $font-base + 2upx;
			color: $font-color-dark;
		}
	}

	.confirm-btn {
		width: 620rpx;
		height: 88rpx;
		line-height: 88rpx;
		margin-top: 90rpx;
		background: var(--color_main_bg);
		box-shadow: 0px 3rpx 14rpx 1rpx var(--color_halo);
		border-radius: 44rpx;
		color: #fff !important;
		font-size: 36rpx;
		margin: 90rpx auto;
	}

	.other-login {
		position: absolute;
		left: 0;
		bottom: 140rpx;
		width: 100%;
		display: flex;
		flex-direction: column;

		.title {
			display: flex;
			justify-content: center;
			align-items: center;

			&:before {
				content: ' ';
				width: 150rpx;
				height: 1rpx;
				background: #cbcbcb;
			}

			&:after {
				content: ' ';
				width: 150rpx;
				height: 1rpx;
				background: #cbcbcb;
			}

			text {
				color: #999999;
				font-size: 26rpx;
				margin: 0 20rpx;
			}
		}

		.login-method {
			display: flex;
			justify-content: center;
			margin-top: 20rpx;

			.wechat-login {
				display: flex;
				flex-direction: column;
				align-items: center;
				width: 100%;
				background: transparent;
				line-height: auto;
				height: auto;

				&::after {
					border: none;
				}

				.wechat-icon {
					width: 78rpx;
					height: 78rpx;
				}

				text {
					color: #666666;
					font-size: 26rpx;
				}
			}
		}
	}

	.agreement-part {
		position: absolute;
		left: 0;
		bottom: 60rpx;
		width: 100%;
		font-size: 26rpx;
		color: #999999;
		text-align: center;

		.register_icon {
			width: 46rpx;
			height: 46rpx;
		}

		.agreement {
			color: var(--color_main);
			border-bottom: 1rpx solid var(--color_main);
		}
	}

	.login-register {
		display: flex;
		justify-content: center;
		margin-top: 33rpx;

		.mobile-login {
			color: #2d2d2d;
			font-size: 28rpx;
			line-height: 34rpx;
			border-right: 1px solid rgba(0, 0, 0, 0.1);
			padding-right: 30rpx;
			margin-right: 30rpx;
		}

		.register {
			color: var(--color_main);
			font-size: 28rpx;
			line-height: 34rpx;
		}
	}
</style>
