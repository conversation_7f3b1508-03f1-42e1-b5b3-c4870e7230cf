<template>
	<view class="container" :style="mix_diyStyle">
		<view class="fixed_top_status_bar"></view>
		<view class="integral_top" :style="{ backgroundImage: 'url(' + top_bg + ')' }">
			<!-- #ifndef MP -->
			<image :src="imgUrl + 'go_back.png'" mode="aspectFit" class="go_back_icon" @click="goBack"></image>
			<!-- #endif -->
			<text class="points_num">{{ my_growth }}</text>
		</view>
		<view class="integral_main">
			<!-- <view class="integral_title">
				<view :class="is_default ? 'active' : 'integral_title_item'" @click="changeStyle(1)">{{ $L('收入') }}</view>
				<view :class="!is_default ? 'active' : 'integral_title_item'" @click="changeStyle(2)">{{ $L('支出') }}</view>
			</view> -->
			<view class="integral_item_wrap">
				<view class="integral_item" v-for="(item, index) in info_list" :key="index">
					<view class="integral_item_title">
						<text>{{ logType_options[item.logType].text }}</text>
						<text>{{ is_default ? '+' : '-' }}{{ item.growthValue }}</text>
					</view>
					<view class="integral_item_content">{{ item.description }}</view>
					<view class="integral_item_date">{{ item.createTime }}</view>
				</view>
				<loadingState :state="loadingState" v-if="!is_show_empty" />
			</view>
			<view class="empty_page" v-if="is_show_empty">
				<image :src="imgUrl + 'empty_goods.png'" mode="aspectFit" class="empty_img"></image>
				<view class="empty_text">{{ $L('暂无数据') }}</view>
			</view>
		</view>
	</view>
</template>

<script>
import loadingState from '@/components/loading-state.vue';
export default {
	components: {
		loadingState
	},
	data() {
		return {
			top_bg: getApp().globalData.imgUrl + 'growth_bg.jpg',
			is_default: true, //是否默认选择收入
			my_growth: '', //我的成长值
			info_list: [], //收支列表
			current: 1,
			pageSize: 10,
			loadingState: 'first_loading',
			logType_options:{
				1:{text:'完善信息'},
				2:{text:'购买商品'},
				3:{text:'发布评论'},
				4:{text:'登录'},
				5:{text:'签到'}
			},
			type: 1, //收入：1，支出：2
			imgUrl: getApp().globalData.imgUrl,
			is_show_empty: false,
			hasMore: false,
			statusBarHeight: 0
		};
	},
	onLoad() {
		this.getMemberLevelInfo();
		this.getGrowthList(this.type);
		// #ifdef MP-TOUTIAO
		var res = tt.getSystemInfoSync();
		this.statusBarHeight = res.statusBarHeight;
		// #endif

		// #ifndef MP-TOUTIAO
		var res = uni.getSystemInfoSync();
		this.statusBarHeight = res.statusBarHeight;
		// #endif
	},
	onReachBottom() {
		if (this.hasMore) {
			this.getGrowthList(this.type);
		}
	},
	methods: {
		getMemberLevelInfo() {
			this.$request({
				url: 'v3/member/front/memberLevel/getInfo'
			}).then((res) => {
				if (res.state == 200) {
					this.my_growth = res.data.memberGrowthValue;
				}
			});
		},
		getGrowthList(type) {
			let param = {};
			param.url = 'v3/member/front/growthLog/list';
			param.method = 'POST';
			param.data = {
				type,
				current: this.current,
				pageSize: this.pageSize
			};
			this.$request(param).then((res) => {
				if (this.current == 1) {
					this.info_list = res.data.list;
				} else {
					this.info_list = this.info_list.concat(res.data.list);
				}
				this.hasMore = this.$checkPaginationHasMore(res.data.pagination); //是否还有数据
				if (this.hasMore) {
					this.current++;
					this.loadingState = 'allow_loading_more';
				} else {
					this.loadingState = 'no_more_data';
				}
				if (this.info_list.length == 0) {
					this.is_show_empty = true;
					this.$forceUpdate();
				} else {
					this.is_show_empty = false;
				}
			});
		},
		// 切换收入支出
		changeStyle(type) {
			this.is_default = type == 1;
			this.type = type;
			this.current = 1;
			this.getGrowthList(this.type);
		},
		// 返回上一页
		goBack() {
			// #ifdef H5
			const pages = getCurrentPages();
			//有返回的页面则直接返回，uni.navigateBack默认返回失败之后会自动刷新页面，无法继续返回
			if (pages.length > 1) {
				this.$Router.back(1);
				return;
			}
			//vue router 可以返回uni.navigateBack失败的页面，但是会重新加载
			let a = this.$Router.back(1);
			//router.go失败之后则重定向到个人中心
			if (a == undefined) {
				this.$Router.replaceAll('/pages/user/user');
			}
			return;
			// #endif
			this.$Router.back(1);
		}
	}
};
</script>

<style lang="scss">
.fixed_top_status_bar {
	position: fixed;
	/* app-1-start */
	/* #ifdef APP-PLUS*/
	height: calc(var(--status-bar-height) + 18rpx);
	/* #endif */
	/* app-1-end */
	/* #ifdef H5*/
	height: 0;
	/* #endif */
	top: 0;
	left: 0;
	right: 0;
	z-index: 99;
	background: #fff;
}

.container {
	width: 750rpx;
	margin: 0 auto;
	overflow-x: hidden;

	.integral_top {
		/* app-2-start */
		/* #ifdef APP-PLUS*/
		margin-top: calc(var(--status-bar-height) + 18rpx);
		/* #endif */
		/* app-2-end */
		height: 396rpx;
		background-size: 100% 100%;
		position: relative;

		.go_back_icon {
			width: 50rpx;
			height: 50rpx;
			position: absolute;
			left: 25rpx;
			top: 25rpx;
			/* wx-1-start */
			/* #ifdef MP-WEIXIN */
			top: 70rpx;
			/* #endif */
			/* wx-1-end */
		}

		.points_num {
			display: block;
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			font-size: 54rpx;
			color: #ffe9a6;
		}
	}

	.integral_main {
		.integral_title {
			height: 87rpx;
			font-size: 32rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 200rpx;
			border-bottom: 1rpx solid #f2f2f2;
		}

		.integral_item_wrap {
			padding-left: 30rpx;
			box-sizing: border-box;

			.integral_item {
				padding: 30rpx 30rpx 30rpx 0;
				border-bottom: 1rpx solid #f2f2f2;

				.integral_item_title {
					font-weight: bold;
					display: flex;
					justify-content: space-between;
				}

				.integral_item_title text:nth-child(1) {
					font-size: 28rpx;
					color: #2d2d2d;
				}

				.integral_item_title text:nth-child(2) {
					font-size: 32rpx;
					color: var(--color_integral_main);
				}

				.integral_item_content {
					font-size: 28rpx;
					color: #666;
					margin-top: 10rpx;
				}

				.integral_item_date {
					font-size: 24rpx;
					color: #999;
					margin-top: 10rpx;
				}
			}
		}

		.integral_item_wrap > view:nth-last-child(1) {
			border-bottom: none;
		}
	}
}

.active {
	height: 100%;
	display: flex;
	align-items: center;
	color: var(--color_integral_main);
	font-weight: bold;
	border-bottom: 6rpx solid var(--color_integral_main);
	box-sizing: border-box;
}

.empty_page {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	margin-top: 150rpx;

	.empty_img {
		width: 380rpx;
		height: 280rpx;
		margin-bottom: 20rpx;
	}

	.empty_text {
		font-size: 26rpx;
		color: #666;
	}
}
</style>
