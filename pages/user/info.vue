<template>
	<view class="page" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }">
		<ktabbar :bgImg="imgUrl + 'default_bg.jpg'" :title="'个人信息'" />

		<view class="container">
			<!-- 头像设置 -->
			<view class="info-section">
				<view class="list_cell mem_avatar_wrap flex_row_between_center" hover-class="cell_hover" :hover-stay-time="50" @click="setAvatar">
					<text class="cell_tit">头像</text>
					<view class="flex_row_end_center">
						<view
							class="avatar"
							:style="{
								backgroundImage: 'url(' + memberAvatar + ')',
								backgroundSize: 'cover'
							}"
						/>
						<text class="cell_more iconfont iconziyuan11"></text>
					</view>
				</view>
			</view>

			<!-- 基本信息 -->
			<view class="info-section">
				<view class="list_cell" hover-class="cell_hover" :hover-stay-time="50">
					<text class="cell_tit">昵称</text>
					<view class="flex_row_end_center">
						<text class="cell_right_con">{{ memberNickName ? memberNickName : 'BOBO' }}</text>
						<text class="cell_more iconfont iconziyuan11" @click="changeName('nick')"></text>
					</view>
				</view>
				<view class="divider"></view>

				<view class="list_cell" hover-class="cell_hover" :hover-stay-time="50">
					<text class="cell_tit">手机号</text>
					<view class="flex_row_end_center">
						<text class="cell_right_con">{{ memberName || '15266555569' }}</text>
						<text class="cell_more iconfont iconziyuan11"></text>
					</view>
				</view>
				<view class="divider"></view>

				<view hover-class="cell_hover" :hover-stay-time="50">
					<picker @change="selSex" :value="gender" :range="sexArray">
						<view class="list_cell">
							<text class="cell_tit">性别</text>
							<view class="flex_row_end_center">
								<view class="cell_right_con">{{ sexArray[gender] || '请完善' }}</view>
								<text class="cell_more iconfont iconziyuan11"></text>
							</view>
						</view>
					</picker>
				</view>
				<view class="divider"></view>

				<view hover-class="cell_hover" :hover-stay-time="50">
					<picker mode="date" :end="filters.getDateTime(endtime)" :value="memberBirthday" @change="selBirthDay">
						<view class="list_cell">
							<text class="cell_tit">生日</text>
							<view class="flex_row_end_center">
								<view class="cell_right_con">{{ memberBirthdayCon || '请完善' }}</view>
								<text class="cell_more iconfont iconziyuan11"></text>
							</view>
						</view>
					</picker>
				</view>
				<view class="divider"></view>

				<view class="list_cell" @click="changeName('true')" hover-class="cell_hover" :hover-stay-time="50">
					<text class="cell_tit">真实姓名</text>
					<view class="flex_row_end_center">
						<text class="cell_right_con">{{ memberTrueName ? memberTrueName : '请完善' }}</text>
						<text class="cell_more iconfont iconziyuan11"></text>
					</view>
				</view>
				<view class="divider"></view>

				<view class="list_cell" hover-class="cell_hover" :hover-stay-time="50">
					<text class="cell_tit">身份证号码</text>
					<view class="flex_row_end_center">
						<text class="cell_right_con">{{ memberIdCard ? memberIdCard : '请完善' }}</text>
						<text class="cell_more iconfont iconziyuan11"></text>
					</view>
				</view>
				<view class="divider"></view>

				<view class="list_cell" hover-class="cell_hover" :hover-stay-time="50">
					<text class="cell_tit">所在城市</text>
					<view class="flex_row_end_center">
						<text class="cell_right_con">请完善</text>
						<text class="cell_more iconfont iconziyuan11"></text>
					</view>
				</view>
			</view>
		</view>

		<!-- #ifdef MP -->
		<privacyPop ref="priPop"></privacyPop>
		<!-- #endif -->
	</view>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import ktabbar from '@/components/ktabbar.vue';
// #ifdef MP
import privacyPop from '@/components/privacy-pop.vue';
// #endif
import filters from '@/utils/filter.js';
export default {
	components: {
		ktabbar,
		// #ifdef MP
		privacyPop
		// #endif
	},
	data() {
		return {
			imgUrl: getApp().globalData.imgUrl,
			memberAvatarLocal: '',
			memberAvatar: getApp().globalData.imgUrl + 'default/member-avatar.png', //会员头像
			memberName: '',
			memberNickName: '', //昵称
			memberTrueName: '', // 真实姓名
			memberIdCard: '', // 身份证号
			gender: 0,
			sexArray: [this.$L('保密'), this.$L('男'), this.$L('女')],
			memberBirthday: '',
			memberBirthdayCon: this.$L('请选择生日'),
			endtime: new Date().getTime(),
			filters
		};
	},
	onLoad() {
		this.getMmeberInfo();
	},
	onShow() {
		let _this = this;
		// #ifdef MP
		// 判断用户隐私授权
		if (!getApp().globalData.allow_privacy) {
			if (wx.getPrivacySetting == undefined) {
				//微信低版本不适配该授权方法
				getApp().globalData.allow_privacy = true;
			} else {
				wx.getPrivacySetting({
					success(res) {
						if (res.needAuthorization) {
							_this.$refs.priPop.PrivacyProtocol = {
								needAuthorization: res.needAuthorization,
								privacyContractName: res.privacyContractName
							};
							_this.$refs.priPop.open();
						} else {
							getApp().globalData.allow_privacy = true;
						}
					}
				});
			}
		}
		// #endif
	},
	computed: {
		...mapState(['userInfo', 'userCenterData'])
	},
	methods: {
		...mapMutations(['setUserCenterData']),
		//获取会员信息
		getMmeberInfo() {
			let _this = this;
			this.$request({
				url: 'v3/member/front/member/memberInfo',
				method: 'GET'
			})
				.then((res) => {
					if (res.state == 200) {
						let result = res.data;
						_this.memberAvatar = result.memberAvatar;
						_this.memberName = result.memberName;
						_this.memberNickName = result.memberNickName;
						_this.gender = result.gender;
						_this.memberBirthdayCon = result.memberBirthday ? result.memberBirthday : '请选择生日';
						this.memberTrueName = result.memberTrueName;
						this.memberIdCard = result.memberIdCard;
					} else {
						this.$api.msg(res.msg);
					}
				})
				.catch((e) => {});
		},
		navTo(url) {
			this.$Router.push(url);
		},
		//选择生日
		selBirthDay(e) {
			this.saveMemInfo('memberBirthday', e.detail.value);
		},
		//选择性别
		selSex(e) {
			this.saveMemInfo('gender', e.detail.value);
		},
		//更新会员昵称
		updateMemInfo(index, val) {
			this[index] = val;
			//更新个人信息数据
			this.userCenterData[index] = val;
			this.setUserCenterData(this.userCenterData);
		},
		//保存会员信息
		saveMemInfo(index, val) {
			if (val == this[index]) {
				return;
			}

			let param = {};
			param.url = 'v3/member/front/member/updateInfo';
			param.data = {};
			param.method = 'POST';
			param.data[index] = val;
			this.$request(param)
				.then((res) => {
					this.$api.msg(res.msg);
					if (res.state != 200) {
						this.$api.msg(res.msg);
					} else {
						if (index == 'memberAvatar') {
							this.memberAvatar = this.memberAvatarLocal;
							//更新个人信息数据
							this.userCenterData.profilePic = this.memberAvatar;
							this.userCenterData.memberAvatar = this.memberAvatar;
							this.setUserCenterData(this.userCenterData);
						} else if (index == 'memberBirthday') {
							this[index] = val;
							this.memberBirthdayCon = val;
						} else {
							this[index] = val;
						}
					}
				})
				.catch((e) => {
					//异常处理
				});
		},
		//设置头像
		setAvatar() {
			// #ifdef MP
			if (!getApp().globalData.allow_privacy) {
				this.$refs.priPop.open();
				return;
			}
			// #endif
			let _this = this;
			uni.chooseImage({
				count: 1,
				sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
				success: function (res) {
					uni.showLoading({
						title: _this.$L('上传中...')
					});
					if (res.tempFiles[0].size > Math.pow(1024, 2) * 4) {
						uni.hideLoading();
						uni.showToast({
							title: _this.$L('超出了图片大小限制4M'),
							icon: 'none',
							duration: 700
						});
					} else {
						uni.uploadFile({
							url: getApp().globalData.apiUrl + 'v3/oss/front/upload',
							filePath: res.tempFilePaths[0],
							name: 'file',
							formData: {
								source: 'headImg'
							},
							header: {
								Authorization: 'Bearer ' + _this.userInfo.access_token
							},
							success: (uploadFileRes) => {
								// #ifdef  MP-ALIPAY||MP-BAIDU
								let result = uploadFileRes.data;
								// #endif

								// #ifndef MP-ALIPAY||MP-BAIDU
								let result = JSON.parse(uploadFileRes.data);
								// #endif
								if (result.state == 200) {
									uni.hideLoading();
									_this.memberAvatarLocal = result.data.url;
									_this.saveMemInfo('memberAvatar', result.data.path);
								} else {
									uni.hideLoading();
									uni.showToast({
										title: uploadFileRes.msg,
										icon: 'none',
										duration: 700
									});
								}
							},
							fail: (uploadFileRes) => {
								uni.showToast({
									title: uploadFileRes.msg,
									icon: 'none',
									duration: 700
								});
							}
						});
					}
				}
			});
		},
		//修改昵称事件
		changeName(type) {
			switch (type) {
				case 'nick': {
					this.$Router.push({
						path: '/pages/user/changeInfo',
						query: {
							name: this.memberNickName ? encodeURIComponent(this.memberNickName) : '',
							modifier: 'nick'
						}
					});
					break;
				}
				case 'true': {
					this.$Router.push({
						path: '/pages/user/changeInfo',
						query: {
							name: this.memberTrueName ? encodeURIComponent(this.memberTrueName) : '',
							idCard: this.memberIdCard ? encodeURIComponent(this.memberIdCard) : '',
							modifier: 'true'
						}
					});
					break;
				}
			}
		}
	}
};
</script>

<style lang="scss">
.page {
	padding-bottom: 50rpx;
	width: 750rpx;
	min-height: 100vh;
	background-repeat: no-repeat;
	background-position: top center;
	background-size: 100% auto;
	background-attachment: fixed;

	.container {
		width: 92%;
		margin: 0 auto;
		margin-top: 50rpx;
		background: #fff;
		border-radius: 40rpx;
		padding: 40rpx 0;
		display: flex;
		flex-direction: column;
		row-gap: 40rpx;
	}
}

.info-section {
	background: #fff;
	border-radius: 20rpx;
	overflow: hidden;
}

.list_cell {
	display: flex;
	align-items: center;
	padding: 0 40rpx;
	line-height: 100rpx;
	height: 100rpx;
	position: relative;
	background: #fff;
	justify-content: space-between;

	&.cell_hover {
		background: rgba(0, 0, 0, 0.05);
	}

	&.mem_avatar_wrap {
		height: 120rpx;
		line-height: 120rpx;
		padding: 0 40rpx;
	}

	.cell_tit {
		font-size: 32rpx;
		color: #333;
		font-weight: 400;
	}

	.cell_right_con {
		color: #666;
		font-size: 30rpx;
		margin-right: 10rpx;
	}

	.cell_more {
		color: #999;
		font-size: 24rpx;
	}

	.avatar {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		background-position: center center;
		background-repeat: no-repeat;
		background-color: #f0f0f0;
		margin-right: 10rpx;
	}
}

.divider {
	height: 1rpx;
	background: rgba(0, 0, 0, 0.1);
	margin: 0 40rpx;
}

.flex_row_end_center {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.flex_row_between_center {
	display: flex;
	align-items: center;
	justify-content: space-between;
}
</style>
