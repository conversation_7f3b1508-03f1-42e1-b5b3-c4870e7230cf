<template>
    <web-view :src="webUrl"></web-view>
</template>

<script>
export default {
    data() {
        return {
            webUrl: ''
        };
    },
    onLoad() {
        const config = uni.getStorageSync('imConfig');
        if (config && config.h5ImEnable == '1') {
            // #ifdef MP-WEIXIN
            this.webUrl = config.miniImUrl;
            // "https://webchat-bj.clink.cn/chat.html?accessId=a2219ea0-f970-400f-9446-25951d82e01c&language=zh_CN"
            // #endif
            // #ifdef H5
            this.webUrl = config.appImUrl;
            // "https://webchat-bj.clink.cn/chat.html?accessId=2e78df7e-2676-4455-a9c4-fd8872d05a0a&language=zh_CN"
            // #endif
        } else {
            uni.showToast({
                title: '客服功能未开启',
                icon: 'none'
            });
            setTimeout(() => {
                uni.navigateBack();
            }, 2000);
        }
    }
};
</script>

<style></style>
