<template>
    <view class="page" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }">
        <view class="container">
            <!-- 自定义顶部导航栏高度占位 -->
            <view class="topbar-seat-style">
                <view class="top_btn">
                    <image class="add_icon" v-if="noticeList.length" :src="imgUrl + 'notice.png'" mode="" @click="goMsg" />
                    <image class="add_icon" v-else :src="imgUrl + 'notice_null.png'" mode="" @click="goMsg" />
                    <image class="add_icon" :src="imgUrl + 'user/user_setting.png'" mode="" @click="goSet" />
                </view>
            </view>
            <view class="member-header">
                <view class="userinfo bb" @tap="clickAvatar">
                    <view class="avatar">
                        <view class="portrait-bgc">
                            <image
                                :src="userCenterData && userCenterData.memberAvatar != undefined ? userCenterData.memberAvatar : defaultAvatar"
                                mode="aspectFill"
                                class="portrait"
                            ></image>
                        </view>
                        <view class="flex_column_center_center">
                            <view v-if="hasLogin" class="flex_column_center_start" style="row-gap: 10rpx">
                                <view class="flex_row_center_start">
                                    <text class="nickname">
                                        {{ userCenterData.memberNickName || userCenterData.memberId }}
                                    </text>
                                    <view class="vip-card" v-if="hasLogin && member_level">
                                        <image class="vip-icon" :src="imgUrl + `user/vip-${member_level}.png`" mode="scaleToFill" />
                                        <view class="level-name">{{ $L(level_info.levelName || '') }}</view>
                                    </view>
                                </view>
                                <view class="growth-row">
                                    <view class="row">
                                        <text class="text">{{ $L('马力值') }}</text>
                                        <text class="num">{{ 0 }}/{{ hasLogin ? nextLevelGrowthValue : 1500 }}</text>
                                    </view>
                                    <!-- member_level_percentage ||  -->

                                    <view style="width: 100%">
                                        <uv-line-progress :percentage="30" height="10rpx" :showText="false" activeColor="rgba(167, 133, 209, 1)"></uv-line-progress>
                                    </view>
                                </view>
                            </view>
                            <text v-else>{{ $L('去登录') }}</text>
                        </view>
                    </view>
                </view>
                <!-- 用户基本数据 -->
                <view class="user-data-list flex_row_between_center">
                    <view class="user-data flex_column_center_center" @click="navTo('follow')">
                        <text class="data-value">{{ 28 }}</text>
                        <text class="data-name">{{ $L('关注') }}</text>
                    </view>
                    <view class="user-data flex_column_center_center" @click="navTo('/extra/user/my?curTab=favorite')">
                        <text class="data-value">{{ 5 }}</text>
                        <text class="data-name">{{ $L('帖子') }}</text>
                    </view>
                    <view class="user-data flex_column_center_center" @click="navTo('/pages/user/myIntegral')">
                        <text class="data-value">{{ userCenterData.memberIntegral || 0 }}</text>
                        <text class="data-name">{{ $L('积分') }}</text>
                    </view>
                    <view class="user-data flex_column_center_center" @click="navTo('/standard/coupon/myCoupon')">
                        <text class="data-value">{{ userCenterData.couponNum || 0 }}</text>
                        <text class="data-name">{{ $L('卡券') }}</text>
                    </view>
                </view>
            </view>
            <!-- 绑定车辆信息 -->
            <view class="vehicle-block">
                <view class="vehicle-card">
                    <image :src="imgUrl + 'vehicle/default_vehicle.png'" mode="scaleToFill" class="vehicle_img" />
                    <view class="vehicle_info">
                        <text class="vehicle_name">{{ 'H9劲速版72V38A超威H9劲速版72V38A超威H9劲速版72V38A超威...' }}</text>
                        <view class="flex_row_between_center">
                            <!-- 电量信息 -->
                            <view class="electricity">
                                <view class="electricity_text">
                                    <text class="num">{{ 80 }}</text>
                                    <text class="symbol">%</text>
                                </view>
                                <view class="electricity_progress" :style="{ width: '80%' }"></view>
                            </view>
                            <!-- 里程信息 -->
                            <view class="mileage">
                                <view class="mileage_text">
                                    <text class="num">{{ 80 }}</text>
                                    <text class="symbol">km</text>
                                </view>
                                <view class="mileage_lable">里程</view>
                            </view>
                        </view>
                    </view>
                    <view class="fix_btn" @click="changeVehicle">
                        <view class="btn">使用中</view>
                        <uv-icon name="arrow-right" size="18" color="rgba(0,0,0,0.6)" />
                    </view>
                </view>
            </view>
            <!-- 绑定车辆信息 end  -->
            <!-- 1-2菜单布局 -->
            <view class="service-row">
                <view class="service-card_1" :style="{ backgroundImage: `url(${imgUrl}member_vip.png)` }" @click="goMaintenance()">
                    <view class="flex_column_center_start t">
                        <view class="service-card_3 un_bg" @click="toVipPage()">
                            <view class="service-icon vip" :style="{ backgroundImage: `url(${imgUrl}member.png)` }"></view>
                            <view class="service-title">立马会员</view>
                        </view>
                    </view>
                </view>
                <view class="service-card_2">
                    <view class="service-card_3" @click="toTaskList()">
                        <view class="service-icon task" :style="{ backgroundImage: `url(${imgUrl}task_list.png)` }"></view>
                        <view class="service-title">积分任务</view>
                    </view>
                    <view class="service-card_3" @click="toPointsMall()">
                        <view class="service-icon mall" :style="{ backgroundImage: `url(${imgUrl}points_mall.png)` }"></view>
                        <view class="service-title">积分商城</view>
                    </view>
                </view>
            </view>
            <!-- 1-2菜单布局 end -->
            <!-- 广告配置 -->
            <view class="adv_swiper" v-if="adv_data && adv_data.length > 0">
                <uv-swiper
                    height="220rpx"
                    radius="20px"
                    :list="adv_data"
                    keyName="imageUrl"
                    indicator
                    indicatorMode="line"
                    @click="swiperClick"
                    circular
                    :autoplay="true"
                ></uv-swiper>
            </view>
            <!-- 广告配置 end  -->
            <view class="round-pop">
                <!-- 常用服务 -->
                <view class="nav-main card">
                    <view class="card-content">
                        <template v-for="(nav, index) in nav_list">
                            <view
                                class="navigator-item"
                                @tap="navigator(nav)"
                                :key="index"
                                v-if="(nav.type == 'staffStores' && userCenterData.isEmployee) || nav.type != 'staffStores'"
                            >
                                <view class="nav_badge flex_row_center_center">
                                    <image :src="imgUrl + nav.icon" mode="widthFix" class="nav_icon"></image>
                                    <text class="badge_text" v-if="nav.name == '消息通知' && userCenterData && userCenterData.msgNum > 0">
                                        {{ userCenterData.msgNum > 9 ? '9+' : userCenterData.msgNum }}
                                    </text>
                                </view>
                                <text class="t">{{ nav.name }}</text>
                            </view>
                        </template>
                    </view>
                </view>
                <!-- 常用服务 end -->
            </view>
            <!-- 底部安全高度  -->
            <view style="height: 160rpx"></view>
        </view>
        <loginPop ref="loginPop" />
        <!-- #ifdef MP -->
        <privacyPop ref="priPop"></privacyPop>
        <!-- #endif -->
    </view>
</template>
<script>
import loginPop from '@/components/loginPop/loginPop.vue';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue';
import { customTabbarShow, customTabbarHide } from '@/utils/common';
import { mapState, mapMutations, mapActions } from 'vuex';
// #ifdef MP
import privacyPop from '@/components/privacy-pop.vue';
// #endif
export default {
    components: {
        loginPop,
        uniPopup,
        uniPopupDialog,
        // #ifdef MP
        privacyPop
        // #endif
    },
    data() {
        return {
            imgUrl: getApp().globalData.imgUrl,
            // #ifdef MP
            navBarHeight: '',
            menuButtonHeight: uni.getMenuButtonBoundingClientRect().height + 'px',
            menuButtonTop: uni.getMenuButtonBoundingClientRect().top + 'px',
            // #endif
            noticeList: [], // 通知列表
            defaultAvatar: getApp().globalData.imgUrl + 'user/member-avatar.png', //默认头像
            member_level: 1,
            nextLevelGrowthValue: 1500, //升级所需成长值
            member_level_percentage: 0, // 成长进度
            progress_color: '#09689e',
            level_info: {}, // 会员等级信息
            level_option: {
                1: { colorValue: 'rgba(196, 230, 129, 0.35)' },
                2: { colorValue: 'rgba(149, 169, 186, 1)' },
                3: { colorValue: 'rgba(229, 200, 128, 1)' },
                4: { colorValue: 'rgba(167, 133, 209, 1)' },
                5: { colorValue: 'rgba(167, 133, 209, 1)' }
            },
            adv_data: [], // 广告数据
            nav_list: [
                {
                    icon: 'lima/vehicle.png',
                    name: '我的车辆',
                    url: '/newPages/vehicle/myVehicle',
                    type: null,
                    needLogin: true
                },
                {
                    icon: 'lima/order.png',
                    name: '我的订单',
                    url: '/order/list',
                    type: null,
                    needLogin: true
                },
                {
                    icon: 'lima/service.png',
                    name: '我的服务',
                    url: '/pages/service/order/repairList',
                    type: '',
                    needLogin: true
                },
                {
                    icon: 'lima/enquiry.png',
                    name: '会员权益',
                    url: '/pages/user/myIntegral',
                    type: null,
                    needLogin: true
                },
                {
                    icon: 'lima/tick.png',
                    name: '我的保单',
                    url: '',
                    type: 'chat',
                    needLogin: true
                },
                {
                    icon: 'lima/address.png',
                    name: '地址管理',
                    url: '/newPages/address/list',
                    type: null,
                    needLogin: true
                },

                {
                    icon: 'lima/task.png',
                    name: '建议与反馈',
                    url: '/pages/service/feedback/index',
                    type: 'feedback',
                    needLogin: true
                },
                {
                    icon: 'lima/about.png',
                    name: '关于',
                    url: '/newPages/set/about',
                    type: '',
                    needLogin: false
                }
            ], //菜单数据
            bgStyle: 'background-size:contain;background-position:center center;background-repeat: no-repeat;',
            openscnImg: getApp().globalData.imgUrl + 'signIn/signner.png',
            signOpenCookie: false, //签到提醒flag
            isSignOpen: true, //签到活动是否可用
            isSign: false, //是否签到
            showState: false,
            imSettingData: { appImUrl: '', h5ImEnable: null, miniImUrl: '' } // 客服设置
        };
    },
    onLoad(option) {
        this.client = this.$wxLoginClient();
        this.initData();
        this.getSysSetting();

        this.getMemberConfig(['super_is_enable', 'super_custom_name', 'super_personal_center_icon']);
        if (this.hasLogin) {
            this.signOpenScreen();
        }
    },
    onPullDownRefresh() {
        this.initData();
    },
    onShow() {
        let _this = this;
        customTabbarShow(4);
        // #ifdef MP
        // 判断用户隐私授权
        if (!getApp().globalData.allow_privacy) {
            if (wx.getPrivacySetting == undefined) {
                //微信低版本不适配该授权方法
                getApp().globalData.allow_privacy = true;
            } else {
                wx.getPrivacySetting({
                    success(res) {
                        if (res.needAuthorization) {
                            _this.$refs.priPop.PrivacyProtocol = {
                                needAuthorization: res.needAuthorization,
                                privacyContractName: res.privacyContractName
                            };
                            _this.$refs.priPop.open();
                        } else {
                            getApp().globalData.allow_privacy = true;
                        }
                    }
                });
            }
        }
        // #endif
        if (this.showState) {
            this.initData();
            if (this.hasLogin) {
                this.signOpenScreen();
            }
            this.getMemberConfig(['super_is_enable', 'super_custom_name', 'super_personal_center_icon']);
        }
        //统计埋点方法--针对微信小程序
        // #ifdef MP-WEIXIN
        let url = getApp().globalData.apiUrl.substring(0, getApp().globalData.apiUrl.length - 1);
        this.$sldStatEvent({
            behaviorType: 'pv',
            pageUrl: url + '/pages/user/user',
            referrerPageUrl: ''
        });
        // #endif
        // 获取未读消息
        if (this.hasLogin) {
            let param = {};
            param.url = 'v3/msg/front/msg/msgListNum';
            param.method = 'GET';
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    this.noticeList = res.data;
                } else {
                    this.$api.msg(res.msg);
                }
            });
        }
        // this.getCartNum();
        this.getMemberLevelInfo();
        this.advConfig(7); // 获取广告配置
    },
    onHide() {
        customTabbarHide(4);
        this.signOpenCookie = false;
    },
    computed: {
        ...mapState(['hasLogin', 'userInfo', 'userCenterData', 'memberConfig'])
    },
    methods: {
        ...mapMutations(['login', 'setUserCenterData', 'saveChatBaseInfo', 'setuserInfo']),
        ...mapActions(['getMemberConfig']),
        //获取广告配置
        advConfig(type = 1) {
            // platform 1:小程序 2:H5
            let param = {};
            param.url = 'v3/video/front/ads/list';
            param.method = 'GET';
            param.data = {
                platform: 1,
                type
            };
            // #ifdef H5
            param.data.platform = 2;
            // #endif
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    this.adv_data = res.data.list;
                }
            });
        },
        //导航菜单点击
        navigator(nav) {
            if (nav.type == null && nav.url == null) {
                uni.showToast({
                    icon: 'none',
                    title: '敬请期待'
                });
                return;
            }
            if (nav.type == 'tab') {
                uni.switchTab({
                    url: nav.url
                });
                return;
            }
            if (nav.type == 'h5') {
                if (nav.needLogin) {
                    if (this.hasLogin) {
                        uni.navigateTo({
                            url: `/pages/index/skip_to?url=${nav.url}`
                        });
                    } else {
                        getApp().globalData.goLogin(this.$Route);
                    }
                } else {
                    uni.navigateTo({
                        url: `/pages/index/skip_to?url=${nav.url}`
                    });
                }
                return;
            }
            if (nav.type == 'mini') {
                if (nav.needLogin) {
                    if (this.hasLogin) {
                        uni.navigateToMiniProgram({
                            appId: nav.data.appId,
                            path: nav.url,
                            extraData: nav.data.extraData,
                            envVersion: nav.data.envVersion || '',
                            success() {},
                            fail(res) {
                                console.log('取消跳转');
                            }
                        });
                    } else {
                        getApp().globalData.goLogin(this.$Route);
                    }
                } else {
                    uni.navigateToMiniProgram({
                        appId: nav.data.appId,
                        path: nav.url,
                        extraData: nav.data.extraData,
                        envVersion: nav.data.envVersion || '',
                        success() {},
                        fail(res) {
                            console.log('取消跳转');
                        }
                    });
                }
                return;
            }
            if (nav.needLogin) {
                if (this.hasLogin) {
                    uni.navigateTo({
                        url: nav.url
                    });
                } else {
                    getApp().globalData.goLogin(this.$Route);
                }
            } else {
                uni.navigateTo({
                    url: nav.url
                });
            }
        },
        // 获取会员等级信息
        getMemberLevelInfo() {
            this.$request({
                url: 'v3/member/front/memberLevel/getInfo'
            }).then((res) => {
                if (res.state == 200) {
                    const level = res.data.currentLevel.level || 1;
                    let growthValue = '';
                    res.data.memberLevelList.map((val, index) => {
                        if (level != 4 && val.level == level + 1) {
                            growthValue = val.growthValue;
                        } else if (level == 4 && val.level == 4) {
                            growthValue = val.growthValue;
                        }
                    });
                    this.member_level = level;
                    this.level_info = res.data.currentLevel;
                    this.nextLevelGrowthValue = growthValue;
                    // 会员成长进度
                    this.member_level_percentage = parseInt((res.data.memberGrowthValue / growthValue) * 100);
                    // 成长条样式
                    this.progress_color = this.level_option[this.member_level].colorValue;
                }
            });
        },
        // 成长值详情
        toMyGrowthDetail() {
            this.$Router.push({
                path: '/pages/user/myGrowth'
            });
        },
        /*点击头像信息区域*/
        clickAvatar() {
            if (this.hasLogin) {
                this.$Router.push({
                    path: '/pages/user/info'
                });
            } else {
                // 去登录
                // #ifdef H5
                if (this.$isAppInset()) {
                    this.$appLogin();
                } else {
                    this.showState = true;
                    this.$Router.push({
                        path: '/pages/public/login'
                    });
                }
                // #endif
                // #ifdef MP
                this.showState = true;
                this.$Router.push({
                    path: '/pages/public/login'
                });
                // #endif
            }
        },
        //获取个人中心数据
        initData() {
            let that = this;
            if (this.hasLogin) {
                this.$request({
                    url: 'v3/member/front/member/getInfo'
                }).then((res) => {
                    uni.stopPullDownRefresh();
                    if (res.state == 200) {
                        this.sellerSwitch = res.data.sellerSwitch;
                        this.sellerStoreId = res.data.storeId;
                        this.delivermanId = res.data.delivermanId;
                        this.empStoreId = res.data.empStoreId;
                        if (res.data.superExpirationTime) {
                            let time = res.data.superExpirationTime.split(' ')[0].split('-');
                            res.data.superExpirationDay = time[0] + '年' + time[1] + '月' + time[2] + '日';
                        }
                        that.$request({
                            url: 'v3/helpdesk/front/chat/unReadMsgNum'
                        }).then((response) => {
                            const data = res.data;
                            if (response.state == 200) {
                                data.msgNum = response.data;
                                that.setUserCenterData(data);
                            } else {
                                that.setUserCenterData(data);
                            }
                        });
                    } else {
                        this.$api.msg(res.msg);
                    }
                });
            } else {
                uni.stopPullDownRefresh();
            }
        },
        getSysSetting() {
            let param = {
                url: 'v3/system/front/setting/getSettings',
                data: {
                    names: 'basic_site_phone,platform_customer_service_name,platform_customer_service_logo'
                }
            };
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    this.adminTel = res.data[0];
                    this.adminSerName = res.data[1];
                    this.adminSerAvatar = res.data[2];
                }
            });
        },

        /**
         * 统一跳转接口,拦截未登录路由
         * navigator标签现在默认没有转场动画，所以用view
         */
        navTo(url) {
            this.showState = true;
            if (url == '') {
                this.$sldCommonTip();
                return;
            }
            if (!this.hasLogin) {
                getApp().globalData.goLogin(this.$Route);
            } else {
                if (url == '/standard/signIn/signIn') {
                    this.signOpenCookie = false;
                }
                if (url == 'follow') {
                    this.$Router.push({ path: '/extra/user/attention', query: { type: 1 } });
                    return;
                }
                this.$Router.push(url);
            }
        },
        // 跳转会员卡包
        toVipPage() {
            if (!this.hasLogin) {
                this.$refs.loginPop.openLogin();
            } else {
                this.showState = true;
                this.$Router.push('/standard/cards/index');
            }
        },
        // 跳转到积分任务列表
        toTaskList() {
            if (!this.hasLogin) {
                this.$refs.loginPop.openLogin();
            } else {
                this.showState = true;
                this.$Router.push('/standard/point/task');
            }
        },
        // 跳转到积分商城
        toPointsMall() {
            if (!this.hasLogin) {
                this.$refs.loginPop.openLogin();
            } else {
                this.showState = true;
                this.$Router.push('/standard/point/index/index');
            }
        },
        //去设置页面
        goSet() {
            if (!this.hasLogin) {
                this.$refs.loginPop.openLogin();
                // getApp().globalData.goLogin(this.$Route);
            } else {
                this.showState = true;
                this.$Router.push('/newPages/set/set');
            }
        },
        //去消息页面
        goMsg() {
            if (!this.hasLogin) {
                this.$refs.loginPop.openLogin();
                // getApp().globalData.goLogin(this.$Route);
            } else {
                this.showState = true;
                this.$Router.push('/standard/chat/list');
            }
        },
        // 去我的积分页面
        toPointsPage() {
            if (!this.hasLogin) {
                this.$refs.loginPop.openLogin();
            } else {
                this.showState = true;
                this.$Router.push('/pages/user/myIntegral');
            }
        },
        //去我的优惠券页面
        goMyCoupon() {
            if (!this.hasLogin) {
                this.$refs.loginPop.openLogin();
            } else {
                this.showState = true;
                this.$Router.push('/standard/coupon/myCoupon');
            }
        },
        //去我的积分订单页面
        goMyPointOrder() {
            if (!this.hasLogin) {
                this.$refs.loginPop.openLogin();
            } else {
                this.showState = true;
                this.$Router.push({
                    path: '/standard/point/order/list',
                    query: {
                        state: 0
                    }
                });
            }
        },
        //去我的视频页面
        goMyVideo() {
            if (!this.hasLogin) {
                this.$refs.loginPop.openLogin();
            } else {
                this.showState = true;
                this.$Router.push('/extra/user/my');
            }
        },

        /*
         * 活动签到提醒
         * 从签到信息接口获取是否提醒的字段(isRemind)
         * isRemind为1，则开启提醒，设置cookie存入提醒字段，每天只显示一次
         *
         */
        signOpenScreen() {
            this.$request({
                url: 'v3/promotion/front/sign/activity/detail'
            }).then((res) => {
                if (res.state == 200) {
                    this.signNotice = res.data.notice.imgPath;
                    this.isSignOpen = true;
                    let isRemind = res.data.isRemind;
                    let signCookie = uni.getStorageSync('signCookie');
                    this.isSign = res.data.isSign == 2 ? true : false;
                    if (isRemind == 1) {
                        if (!signCookie) {
                            this.signOpenCookie = true;
                            uni.setStorage({
                                key: 'signCookie',
                                data: new Date().getTime()
                            });
                        } else {
                            if (new Date().getTime() > new Date(new Date().toLocaleDateString()).getTime() && new Date().getTime() > signCookie) {
                                this.signOpenCookie = false;
                            } else {
                                this.signOpenCookie = true;
                                uni.setStorage({
                                    key: 'signCookie',
                                    data: new Date().getTime()
                                });
                            }
                        }
                    } else {
                        this.signOpenCookie = false;
                        uni.removeStorage({
                            key: 'signCookie'
                        });
                    }
                } else {
                    this.isSignOpen = false;
                }
            });
        },
        //前往签到页面
        toInfo() {
            if (this.hasLogin) {
                this.$Router.push('/pages/user/info');
            }
        },

        //获取购物车数据
        getCartNum() {
            if (this.hasLogin) {
                let param = {};
                param.url = 'v3/business/front/cart/cartNum';
                param.method = 'GET';
                param.data = {};
                // param.data.key = this.userInfo.access_token;
                this.$request(param)
                    .then((res) => {
                        if (res.state == 200) {
                            if (res.data > 0) {
                                uni.setTabBarBadge({
                                    index: 3,
                                    text: res.data.toString()
                                });
                            } else {
                                uni.hideTabBarRedDot({
                                    index: 3
                                });
                            }
                        } else {
                            this.$api.msg(res.msg);
                        }
                    })
                    .catch((e) => {
                        //异常处理
                    });
            } else {
                this.getNoLoginCartNum();
            }
        },
        //获取未登录，购物车数量
        getNoLoginCartNum() {
            let cartNum = 0;
            let cart_list = uni.getStorageSync('cart_list');
            if (cart_list && cart_list.storeCartGroupList) {
                cart_list.storeCartGroupList.map((item) => {
                    item.promotionCartGroupList.map((item1) => {
                        item1.cartList.map((item2) => {
                            cartNum++;
                        });
                    });
                });
            }
            if (cartNum > 0) {
                uni.setTabBarBadge({
                    index: 3,
                    text: cartNum.toString()
                });
            } else {
                uni.hideTabBarRedDot({
                    index: 3
                });
            }
        },
        //获取购物车数据
        getCartNum() {
            if (this.hasLogin) {
                let param = {};
                param.url = 'v3/business/front/cart/cartNum';
                param.method = 'GET';
                param.data = {};
                // param.data.key = this.userInfo.access_token;
                this.$request(param)
                    .then((res) => {
                        if (res.state == 200) {
                            if (res.data > 0) {
                                uni.setTabBarBadge({
                                    index: 3,
                                    text: res.data.toString()
                                });
                            } else {
                                uni.hideTabBarRedDot({
                                    index: 3
                                });
                            }
                        } else {
                            this.$api.msg(res.msg);
                        }
                    })
                    .catch((e) => {
                        //异常处理
                    });
            } else {
                this.getNoLoginCartNum();
            }
        },
        //前往超级会员充值页
        navtoVip() {
            if (!this.hasLogin) {
                this.$refs.loginPop.openLogin();
            } else {
                this.$Router.push('/standard/super/index?type=' + this.userCenterData.isSuper);
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.page {
    width: 750rpx;
    margin: 0 auto;
    position: relative;
    background: #f5f5f5;
    width: 100%;
    min-height: 100vh;
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    // 背景图片固定，不滚动
    background-attachment: fixed;
    .topbar-seat-style {
        height: 180rpx;
        width: 100%;
        padding-top: 90rpx;
        background-color: transparent;
        .top_btn {
            height: 80rpx;
            width: 90%;
            margin: 0 auto;
            display: flex;
            align-items: center;
            /* #ifdef MP */
            justify-content: flex-start;
            /* #endif */
            /* #ifdef H5 */
            justify-content: flex-end;
            /* #endif */
            column-gap: 50rpx;
            .add_icon {
                width: 45rpx;
                height: 45rpx;
                object-fit: contain;
            }
        }
    }

    .member-header {
        width: 92%;
        margin: 0 auto;
        box-sizing: border-box;
        /* #ifdef MP-WEIXIN */
        margin-top: 20rpx;
        /* #endif */
        .userinfo {
            width: 100%;
            height: 200rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20rpx;

            .avatar {
                display: flex;
                align-items: center;
                gap: 10px;

                .portrait-bgc {
                    width: 110rpx;
                    height: 110rpx;
                    box-sizing: border-box;
                    border-radius: 50%;
                    overflow: hidden;
                    background-color: #949494;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    .portrait {
                        width: 100%;
                        height: 100%;
                        border-radius: 50%;
                    }
                }

                .nickname {
                    color: #000;
                    max-width: 150px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    word-break: keep-all;
                    font-size: 34.62rpx;
                    height: 57.69rpx;
                    line-height: 1.5;
                }
            }

            // 会员卡
            .vip-card {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 50rpx;
                border-radius: 25rpx;
                margin-left: 10rpx;
                border: 1px solid #000000;
                padding: 0 20rpx;

                .vip-icon {
                    width: 30rpx;
                    height: 30rpx;
                    margin-right: 5rpx;
                }

                .level-name {
                    color: #000;
                    font-size: 26rpx;
                    font-weight: bold;
                    word-break: keep-all;
                }
            }

            // 成长值
            .growth-row {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                justify-content: space-between;
                width: 320rpx;
                margin-top: 10rpx;

                .row {
                    display: flex;
                    align-items: center;
                    gap: 10rpx;
                    margin-bottom: 10rpx;
                    color: #000;
                    .text {
                        font-size: 24rpx;
                    }

                    .num {
                        font-size: 24rpx;
                    }
                }
            }
        }

        .user-data-list {
            width: 100%;
            display: flex;
            box-sizing: border-box;
            height: 140rpx;

            .user-data {
                width: 25%;
                row-gap: 10rpx;
            }

            .data-value {
                font-size: $fs-lg;
                font-weight: bold;
                color: #000;
            }

            .data-name {
                font-size: $fs-base;
                color: #000;
            }
        }
    }

    .container {
        min-height: 100vh;
        background-repeat: no-repeat;
        background-position: top center;
        background-size: cover;
        // 背景图片固定，不滚动
        background-attachment: fixed;

        .round-pop {
            width: 94%;
            margin: 20rpx auto;
            border-radius: 40rpx;
            background-color: #fff;
            //导航组
            .nav-main {
                width: 100%;
                margin: 0 auto;
                padding: 40rpx 0rpx;
                box-sizing: border-box;
                .card-content {
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    flex-wrap: wrap;
                    box-sizing: border-box;
                    row-gap: 60rpx;
                    .navigator-item {
                        width: 25%;
                        display: flex;
                        align-items: center;
                        flex-direction: column;
                    }

                    .nav_badge {
                        position: relative;
                        display: flex;
                        align-items: center;
                        .nav_icon {
                            width: 60rpx;
                            height: 60rpx;
                            margin-bottom: 10rpx;
                        }
                        .badge_text {
                            position: absolute;
                            top: 0rpx;
                            right: 6rpx;
                            width: 26rpx;
                            height: 26rpx;
                            line-height: 26rpx;
                            background: #ffffff;
                            border-radius: 50%;
                            font-size: 20rpx;
                            font-family: PingFang SC;
                            font-weight: 500;
                            color: #ffffff;
                            text-align: center;
                            background-color: #ff152c;
                        }
                    }

                    .t {
                        font-size: $fs-base;
                        color: #000;
                    }
                }
            }
        }
    }
    .vehicle-block {
        width: 94%;
        margin: 20rpx auto;
        border-radius: 40rpx;
        background-color: #fff;
        position: relative;
        .vehicle-card {
            width: 100%;
            padding: 30rpx 20rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .vehicle_info {
            width: calc(100% - 200rpx);
            height: 200rpx;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            box-sizing: border-box;
            row-gap: 20rpx;
            padding-top: 40rpx;
            .vehicle_name {
                font-size: 32rpx;
                color: #000;
                line-height: 1.5;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .electricity {
                width: 50%;
                border-radius: 10rpx;
                margin-top: 10rpx;
                position: relative;

                .electricity_text {
                    display: flex;
                    align-items: flex-end;
                    justify-content: flex-start;
                    margin-bottom: 10rpx;
                    .num {
                        font-size: 56rpx;
                        font-weight: bold;
                        color: #000;
                        line-height: 1;
                    }
                    .symbol {
                        font-size: $fs-s;
                        color: #000;
                        margin-left: 5rpx;
                    }
                }

                .electricity_progress {
                    height: 4px;
                    border-radius: 2px;
                    background-color: #b2a1f0;
                }
            }
            .mileage {
                width: 40%;
                border-radius: 10rpx;
                margin-top: 10rpx;
                position: relative;

                .mileage_text {
                    display: flex;
                    align-items: flex-end;
                    justify-content: flex-start;
                    margin-bottom: 10rpx;
                    .num {
                        font-size: $fs-base;
                        color: #000;
                        line-height: 1;
                    }
                    .symbol {
                        font-size: $fs-base;
                        color: #000;
                        margin-left: 5rpx;
                    }
                }

                .mileage_lable {
                    text-align: left;
                    font-size: $fs-base;
                }
            }
        }
        .vehicle_img {
            width: 140rpx;
            height: 200rpx;
        }
        .fix_btn {
            position: absolute;
            top: 20rpx;
            right: 20rpx;
            display: flex;
            align-items: center;
            column-gap: 15rpx;
            .btn {
                padding: 0 15rpx;
                height: 40rpx;
                line-height: 40rpx;
                border-radius: 20rpx;
                background-color: rgba(178, 161, 240, 1);
                text-align: center;
                font-size: $fs-s;
                color: #fff;
            }
        }
    }
    .service-row {
        width: 94%;
        margin: 20rpx auto;
        border-radius: 40rpx;
        position: relative;
        display: flex;
        justify-content: space-between;
        column-gap: 20rpx;
        .service-card_1 {
            flex: 1;
            height: 320rpx;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            background-color: #fff;
            border-radius: 40rpx;
            background-position: 34rpx 95rpx;
            background-repeat: no-repeat;
            background-size: 80%;
            .t {
                margin-top: 20rpx;
            }
            .service-desc {
                margin-top: 4rpx;
                color: rgba(0, 0, 0, 0.5);
                font-size: $fs-s;
            }
        }
        .service-card_2 {
            flex: 1;
            height: 320rpx;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }
        .service-card_3 {
            width: 100%;
            padding: 0 50rpx;
            height: calc(100% / 2 - 10rpx);
            background-color: #fff;
            border-radius: 40rpx;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            &.un_bg {
                background-color: transparent;
            }
            .service-icon {
                width: 90rpx;
                height: 90rpx;
                border-radius: 45rpx;
                background-position: center;
                background-repeat: no-repeat;
                background-size: 60%;
                margin-right: 20rpx;
                &.task {
                    background-color: rgba(255, 123, 125, 1);
                }
                &.mall {
                    background-color: rgba(228, 181, 255, 1);
                }
                &.vip {
                    background-color: rgba(130, 184, 255, 1);
                }
            }
        }
    }
    .adv_swiper {
        width: 100%;
        padding: 0rpx 3%;
        box-sizing: border-box;
    }
}
</style>
