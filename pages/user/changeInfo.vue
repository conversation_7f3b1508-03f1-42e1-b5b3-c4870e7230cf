<!-- 修改昵称 -->
<template>
	<view class="edit_nick_name" :style="mix_diyStyle">
		<view class="input_wrap flex_row_between_center">
			<input type="text" v-model="memberName" :maxlength="10" :placeholder="placeholder" placeholder-style="font-size:26rpx;color:#949494" />
			<text @click="clearCon" v-show="memberName" class="clear_con iconfont iconguanbi" />
		</view>
		<view class="input_wrap flex_row_between_center" v-if="modifier != 'nick'">
			<input type="text" v-model="memberIdCard" maxlength="22" placeholder="请输入身份证号" placeholder-style="font-size:26rpx;color:#949494" />
			<text @click="clearIdCode" v-show="memberIdCard" class="clear_con iconfont iconguanbi" />
		</view>
		<!-- <view v-show="memberName" class="count">
			<text class="cur_count">{{ memberName.length }}</text>
			<text class="totla_count">/{{ total }}</text>
		</view> -->
		<view class="member_nickname_btn" @click="saveCon">{{ $L('保存') }}</view>
	</view>
</template>

<script>
import { mapState } from 'vuex';
export default {
	data() {
		return {
			memberName: '',
			memberIdCard: '', //身份证号
			modifier: '',
			limitNum: 15,
			placeholder: '',
			total: 0
		};
	},
	// {
	// 	type: 'string',
	// 	required: true,
	// 	message: '请输入身份证号',
	// 	trigger: ['blur', 'change']
	// },
	// {
	// 	pattern: /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2][0-9]|3[0-1])\d{3}[0-9Xx]$/,
	// 	message: '请输入正确的身份证号',
	// 	trigger: ['blur']
	// }
	onLoad(option) {
		this.memberName = decodeURIComponent(decodeURIComponent(this.$Route.query.name));
		this.memberIdCard = decodeURIComponent(decodeURIComponent(this.$Route.query.idCard));
		this.modifier = this.$Route.query.modifier;
		if (this.modifier == 'nick') {
			this.total = 15;
			this.placeholder = this.$L('这么好的你，应该拥有更好的昵称~');
			setTimeout(() => {
				uni.setNavigationBarTitle({
					title: this.$L('修改昵称')
				});
			}, 0);
		} else {
			this.total = 10;
			this.placeholder = this.$L('请输入真实姓名');
			setTimeout(() => {
				uni.setNavigationBarTitle({
					title: this.$L('实名信息')
				});
			}, 0);
		}
	},
	computed: {
		...mapState(['userInfo'])
	},
	methods: {
		//保存数据
		saveCon() {
			let { memberName, memberIdCard } = this;

			if (this.modifier == 'nick') {
				if (memberName.trim().length > this.total) {
					this.$api.msg(`${this.$L('请输入')}${this.total}${this.$L('个字以内的昵称')}`);
				} else {
					this.saveMemInfo('memberNickName', memberName);
				}
			} else {
				if (memberName.trim().length > this.total) {
					this.$api.msg(`${this.$L(真实姓名最多输入)}${this.total}${this.$L('位')}`);
					return;
				}
				if (!/^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2][0-9]|3[0-1])\d{3}[0-9Xx]$/.test(memberIdCard)) {
					this.$api.msg('请输入正确的身份证号');
					return;
				}
				this.saveMemInfo(['memberTrueName', 'memberIdCard'], [memberName, memberIdCard]);
			}
		},
		//清空输入值
		clearCon() {
			this.memberName = '';
		},
		clearIdCode() {
			this.memberIdCard = '';
		},
		navTo(url) {
			this.$Router.push(url);
		},

		//保存会员信息
		saveMemInfo(index, val) {
			let param = {};
			param.url = 'v3/member/front/member/updateInfo';
			param.data = {};
			param.method = 'POST';
			if (typeof index == 'string') {
				param.data[index] = val;
			} else {
				index.map((key, i) => {
					param.data[key] = val[i];
				});
			}
			this.$request(param)
				.then((res) => {
					this.$api.msg(res.msg);
					if (res.state == 200) {
						uni.showToast({
							title: this.$L('修改成功！'),
							icon: 'none',
							duration: 500
						});
						setTimeout(() => {
							this[index] = val;
							//更新上个页面的会员昵称
							var pages = getCurrentPages(); //当前页面栈
							if (pages.length > 1) {
								var beforePage = pages[pages.length - 2]; //获取上一个页面实例对象
								//触发上个面中的方法updateMemInfo()
								if (typeof index == 'string') {
									beforePage.$vm.updateMemInfo(index, val);
								} else {
									index.map((key, i) => {
										beforePage.$vm.updateMemInfo(key, val[i]);
									});
								}
							}
							this.$Router.back(1);
						}, 700);
					}
				})
				.catch((e) => {
					//异常处理
				});
		}
	}
};
</script>

<style lang="scss">
page {
	background: $bg-color-split;
	width: 750rpx;
	margin: 0 auto;
}

.edit_nick_name {
	margin-top: 20rpx;
	width: 100%;
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	.member_nickname_btn {
		width: 680rpx;
		height: 80rpx;
		background: var(--color_main_bg);
		border-radius: 37rpx;
		margin: 40rpx auto 0;
		font-size: 34rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #ffffff;
		line-height: 80rpx;
		text-align: center;
	}
}

.edit_nick_name .input_wrap {
	height: 100rpx;
	padding: 0 20rpx;
	width: 100%;
	background-color: #fff;
	&:first-child {
		border-bottom: 1px solid;
		border-color: #f3f3f3;
	}
}

.edit_nick_name .input_wrap input {
	width: 450rpx;
	color: #2d2d2d;
	font-size: 28rpx;
}

.edit_nick_name .input_wrap .clear_con {
	font-size: 35rpx;
	color: #dcdcdc;
}

.edit_nick_name .count {
	width: 100%;
	display: flex;
	justify-content: flex-end;
	padding-right: 20rpx;
	margin-top: 20rpx;
}

.edit_nick_name .count text {
	font-size: 28rpx;
	color: #2d2d2d;
}

.edit_nick_name .count .cur_count {
	color: var(--color_main);
}
</style>
