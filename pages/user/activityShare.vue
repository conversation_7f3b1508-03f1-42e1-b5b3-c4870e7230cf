<template>
	<view class="page">
		<view class="container">
			<image class="share_bg" :src="imgUrl + shareBg" mode="aspectFill"></image>
			<view class="share_btn">
				<button class="btn" open-type="share">立即邀请</button>
			</view>
		</view>
	</view>
</template>
<script>
import { mapMutations, mapState } from "vuex";
// #ifdef MP-WEIXIN
import { onShow } from "@dcloudio/uni-app";
// #endif
export default {
	data() {
		return {
			imgUrl: getApp().globalData.imgUrl,
			shareBg: "activity/luck_draw/share_img_900.jpg"
		};
	},
	computed: {
		...mapState(["userInfo"])
	},
	onLoad(options) {
		this.pos = options ? options.pos : "";
	},
	onShareAppMessage() {
		return {
			title: "",
			imageUrl: "",
			path: `/pages/index/index?pos=${this.pos}&shareId=${this.userInfo.memberId}`
		};
	}
};
</script>
<style lang="scss">
.page {
	width: 100%;
	box-sizing: border-box;
	position: relative;
	.container {
		width: 100%;
		height: 100vh;
	}
	.share_bg {
		width: 100%;
		height: 100%;
		display: block;
	}
	.share_btn {
		position: absolute;
		bottom: 120rpx;
		left: 0;
		width: 100%;
		.btn {
			width: 350rpx;
			height: 96rpx;
			border-radius: 48rpx;
			line-height: 96rpx;
			// background: linear-gradient(135deg, #367fff 0%, #37edde 100%);
			background: linear-gradient(135deg, #ffcf7c 0%, #ed8446 100%);
			color: #fff;
			&::after {
				border: none;
				background: none !important;
			}
		}
	}
}
</style>
