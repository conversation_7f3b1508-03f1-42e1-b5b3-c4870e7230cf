<template>
    <view class="container" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }">
        <ktabbar :bgImg="imgUrl + 'default_bg.jpg'" :title="'我的积分'" />
        <view class="integral_top">
            <view class="flex_row_center_end">
                <image :src="imgUrl + 'integral.png'" mode="" class="integral_icon" />
                <text class="points_num">{{ my_points }}</text>
            </view>
            <text class="integral_tips">{{ $L('可用积分') }}</text>
        </view>
        <view class="integral_main">
            <view class="integral_title">
                <view :class="is_default ? 'integral_title_item active' : 'integral_title_item'">
                    <text class="text" @click="changeStyle(1)">{{ $L('积分获取明细') }}</text>
                </view>
                <view :class="!is_default ? 'integral_title_item active' : 'integral_title_item'">
                    <text class="text" @click="changeStyle(2)">{{ $L('积分兑换记录') }}</text>
                </view>
            </view>

            <view class="integral_item_wrap">
                <view class="integral_item" v-for="(item, index) in info_list" :key="index">
                    <view class="integral_item_title">
                        <text>{{ item.description }}</text>
                        <text>{{ is_default ? '+' : '-' }}{{ item.value }}</text>
                    </view>
                    <!-- <view class="integral_item_content">{{ item.description }}</view> -->
                    <view class="integral_item_date">{{ item.createTime }}</view>
                </view>
            </view>
            <loadingState :state="loadingState" v-if="!is_show_empty" />
            <view class="empty_page" v-if="is_show_empty">
                <image :src="imgUrl + 'empty_goods.png'" mode="aspectFit" class="empty_img"></image>
                <view class="empty_text">{{ $L('暂无数据') }}</view>
            </view>
        </view>
    </view>
</template>
<script>
import loadingState from '@/components/loading-state.vue';
import ktabbar from '@/components/ktabbar.vue';
export default {
    components: {
        loadingState,
        ktabbar
    },
    data() {
        return {
            is_default: true, //是否默认选择收入
            my_points: '', //我的积分
            info_list: [], //收支列表
            current: 1,
            pageSize: 10,
            loadingState: 'first_loading',
            type: 1, //收入：1，支出：2
            imgUrl: getApp().globalData.imgUrl,
            is_show_empty: false,
            hasMore: false,
            statusBarHeight: 0
        };
    },
    onLoad() {
        this.getUserPoints();
        this.getPointsList(this.type);
        // #ifdef MP-TOUTIAO
        var res = tt.getSystemInfoSync();
        this.statusBarHeight = res.statusBarHeight;
        // #endif

        // #ifndef MP-TOUTIAO
        var res = uni.getSystemInfoSync();
        this.statusBarHeight = res.statusBarHeight;
        // #endif
    },
    onReachBottom() {
        if (this.hasMore) {
            this.getPointsList(this.type);
        }
    },
    methods: {
        getUserPoints() {
            let param = {};
            param.url = 'v3/member/front/integralLog/getMemberIntegral';
            param.method = 'GET';
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    this.my_points = res.data.memberIntegral;
                }
            });
        },
        getPointsList(type) {
            let param = {};
            param.url = 'v3/member/front/integralLog/list';
            param.method = 'POST';
            param.data = {
                type,
                current: this.current,
                pageSize: this.pageSize
            };
            this.$request(param).then((res) => {
                if (this.current == 1) {
                    this.info_list = res.data.list;
                } else {
                    this.info_list = this.info_list.concat(res.data.list);
                }
                this.hasMore = this.$checkPaginationHasMore(res.data.pagination); //是否还有数据
                if (this.hasMore) {
                    this.current++;
                    this.loadingState = 'allow_loading_more';
                } else {
                    this.loadingState = 'no_more_data';
                }
                if (this.info_list.length == 0) {
                    this.is_show_empty = true;
                    this.$forceUpdate();
                } else {
                    this.is_show_empty = false;
                }
            });
        },
        // 切换收入支出
        changeStyle(type) {
            this.is_default = type == 1;
            this.type = type;
            this.current = 1;
            this.getPointsList(this.type);
        },
        // 返回上一页
        goBack() {
            // #ifdef H5
            const pages = getCurrentPages();
            //有返回的页面则直接返回，uni.navigateBack默认返回失败之后会自动刷新页面，无法继续返回
            if (pages.length > 1) {
                this.$Router.back(1);
                return;
            }
            //vue router 可以返回uni.navigateBack失败的页面，但是会重新加载
            let a = this.$Router.back(1);
            //router.go失败之后则重定向到个人中心
            if (a == undefined) {
                this.$Router.replaceAll('/pages/user/user');
            }
            return;
            // #endif
            this.$Router.back(1);
        }
    }
};
</script>

<style lang="scss">
.container {
    width: 750rpx;
    min-height: 100vh;
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    // 背景图片固定，不滚动
    background-attachment: fixed;

    .integral_top {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        row-gap: 20rpx;
        margin: 30rpx 0 80rpx 0;
        .integral_icon {
            width: 50rpx;
            height: 50rpx;
            margin-right: 10rpx;
        }
        .points_num {
            font-size: 72rpx;
            color: #000;
            line-height: 1;
        }
        .integral_tips {
            font-size: $fs-base;
            color: rgba(153, 153, 153, 1);
        }
    }

    .integral_main {
        width: 94%;
        margin: 0 auto;
        background-color: #fff;
        border-radius: 40rpx;
        padding: 40rpx 20rpx;
        .integral_title {
            width: 100%;
            height: 87rpx;
            font-size: 32rpx;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .integral_title_item {
                width: 50%;
                text-align: center;
                .text {
                    position: relative;
                    display: inline-block;
                    padding-bottom: 15rpx;
                    color: rgba(0, 0, 0, 0.5);
                    &::after {
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        content: '';
                        display: block;
                        width: 100%;
                        height: 2px;
                        border-radius: 1px;
                        transform: scaleX(0);
                        background-color: transparent;
                        will-change: transform;
                        // transition: transform 0.3s ease-in;
                    }
                }
                &.active {
                    .text {
                        font-weight: bold;
                        color: #000;
                        &::after {
                            background-color: $color1;
                            transform: scaleX(1);
                        }
                    }
                }
            }
        }

        .integral_item_wrap {
            padding-left: 30rpx;
            box-sizing: border-box;

            .integral_item {
                padding: 30rpx 30rpx 30rpx 0;
                border-bottom: 1rpx solid #f2f2f2;

                .integral_item_title {
                    color: #000;
                    display: flex;
                    justify-content: space-between;
                }

                .integral_item_title text:nth-child(1) {
                    font-size: 28rpx;
                    color: #2d2d2d;
                }

                .integral_item_title text:nth-child(2) {
                    font-size: 32rpx;
                    color: var(--color_integral_main);
                }

                .integral_item_content {
                    font-size: 28rpx;
                    color: #666;
                    margin-top: 10rpx;
                }

                .integral_item_date {
                    font-size: 24rpx;
                    color: #999;
                    margin-top: 10rpx;
                }
            }
        }

        .integral_item_wrap > view:nth-last-child(1) {
            border-bottom: none;
        }
    }
}

.empty_page {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: 150rpx;
    margin-bottom: 100rpx;
    .empty_img {
        width: 380rpx;
        height: 280rpx;
        margin-bottom: 20rpx;
    }

    .empty_text {
        font-size: 26rpx;
        color: #666;
    }
}
</style>
