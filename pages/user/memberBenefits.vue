<template>
	<view class="page" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }">
		<ktabbar :bgImg="imgUrl + 'default_bg.jpg'" :title="'会员权益'" />
		
		<!-- 会员等级容器 -->
		<view class="member-level-container">
			<view class="level-section">
				<view class="level-header">
					<view class="level-info">
						<text class="level-title">{{ memberLevel || '普通会员' }}</text>
						<text class="level-desc">{{ levelDesc || '享受基础权益' }}</text>
					</view>
					<view class="level-icon">
						<image :src="imgUrl + 'member_icon.png'" class="member-icon" mode="aspectFit"></image>
					</view>
				</view>
				
				<view class="progress-section" v-if="showProgress">
					<view class="progress-info">
						<text class="progress-text">距离下一等级还需 {{ needPoints || 0 }} 积分</text>
					</view>
					<view class="progress-bar">
						<view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
					</view>
				</view>
			</view>
		</view>

		<!-- 权益列表容器 -->
		<view class="benefits-container">
			<view class="benefits-section">
				<view class="section-title">
					<text class="title-text">会员权益</text>
				</view>
				
				<view class="benefits-list">
					<view 
						class="benefit-item" 
						v-for="(benefit, index) in benefitsList" 
						:key="index"
						hover-class="item_hover" 
						:hover-stay-time="50"
					>
						<view class="benefit-icon">
							<image :src="imgUrl + benefit.icon" class="icon-img" mode="aspectFit"></image>
						</view>
						<view class="benefit-content">
							<text class="benefit-title">{{ benefit.title }}</text>
							<text class="benefit-desc">{{ benefit.description }}</text>
						</view>
						<view class="benefit-status">
							<text class="status-text" :class="benefit.available ? 'available' : 'unavailable'">
								{{ benefit.available ? '已开通' : '未开通' }}
							</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 升级提示容器 -->
		<view class="upgrade-container" v-if="showUpgrade">
			<view class="upgrade-section">
				<view class="upgrade-content">
					<text class="upgrade-title">升级会员等级</text>
					<text class="upgrade-desc">完成更多任务，获取积分提升会员等级</text>
				</view>
				<view class="upgrade-btn" hover-class="btn_hover" :hover-stay-time="50" @click="goToTasks">
					<text class="btn-text">去完成任务</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import ktabbar from '@/components/ktabbar.vue';

export default {
	components: {
		ktabbar
	},
	data() {
		return {
			imgUrl: getApp().globalData.imgUrl,
			memberLevel: '',
			levelDesc: '',
			needPoints: 0,
			progressPercent: 0,
			showProgress: true,
			showUpgrade: true,
			benefitsList: [
				{
					icon: 'benefit_integral.png',
					title: '积分奖励',
					description: '完成任务获得双倍积分',
					available: true
				},
				{
					icon: 'benefit_discount.png',
					title: '专属折扣',
					description: '享受会员专属优惠价格',
					available: true
				},
				{
					icon: 'benefit_service.png',
					title: '优先服务',
					description: '享受优先客服服务',
					available: false
				},
				{
					icon: 'benefit_gift.png',
					title: '生日礼品',
					description: '生日当月获得专属礼品',
					available: false
				}
			]
		};
	},
	onLoad() {
		this.getMemberBenefits();
	},
	methods: {
		// 获取会员权益信息
		getMemberBenefits() {
			let _this = this;
			this.$request({
				url: 'v3/member/front/member/memberBenefits',
				method: 'GET'
			})
			.then((res) => {
				if (res.state == 200) {
					let result = res.data;
					_this.memberLevel = result.memberLevel;
					_this.levelDesc = result.levelDesc;
					_this.needPoints = result.needPoints;
					_this.progressPercent = result.progressPercent || 0;
					_this.showProgress = result.showProgress !== false;
					_this.showUpgrade = result.showUpgrade !== false;
					
					// 更新权益列表状态
					if (result.benefits && result.benefits.length > 0) {
						_this.benefitsList = result.benefits;
					}
				} else {
					this.$api.msg(res.msg);
				}
			})
			.catch((e) => {
				// 使用默认数据
			});
		},
		
		// 跳转到任务页面
		goToTasks() {
			this.$Router.push('/standard/point/task');
		}
	}
};
</script>

<style lang="scss">
.page {
	padding-bottom: 50rpx;
	width: 750rpx;
	min-height: 100vh;
	background-repeat: no-repeat;
	background-position: top center;
	background-size: 100% auto;
	background-attachment: fixed;
}

.member-level-container {
	width: 92%;
	margin: 0 auto;
	margin-top: 50rpx;
	background: #fff;
	border-radius: 40rpx;
	padding: 40rpx;
}

.benefits-container {
	width: 92%;
	margin: 0 auto;
	margin-top: 20rpx;
	background: #fff;
	border-radius: 40rpx;
	padding: 40rpx;
}

.upgrade-container {
	width: 92%;
	margin: 0 auto;
	margin-top: 20rpx;
	background: #fff;
	border-radius: 40rpx;
	padding: 40rpx;
}

.level-section {
	.level-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 30rpx;
		
		.level-info {
			flex: 1;
			
			.level-title {
				display: block;
				font-size: 32rpx;
				color: #000;
				font-weight: 600;
				margin-bottom: 10rpx;
			}
			
			.level-desc {
				display: block;
				font-size: 28rpx;
				color: #000;
			}
		}
		
		.level-icon {
			.member-icon {
				width: 80rpx;
				height: 80rpx;
			}
		}
	}
	
	.progress-section {
		.progress-info {
			margin-bottom: 20rpx;
			
			.progress-text {
				font-size: 28rpx;
				color: #000;
			}
		}
		
		.progress-bar {
			height: 8rpx;
			background: #f0f0f0;
			border-radius: 4rpx;
			overflow: hidden;
			
			.progress-fill {
				height: 100%;
				background: linear-gradient(90deg, #ff6b6b, #ff8e8e);
				border-radius: 4rpx;
				transition: width 0.3s ease;
			}
		}
	}
}

.benefits-section {
	.section-title {
		margin-bottom: 30rpx;
		
		.title-text {
			font-size: 32rpx;
			color: #000;
			font-weight: 600;
		}
	}
	
	.benefits-list {
		.benefit-item {
			display: flex;
			align-items: center;
			padding: 30rpx 0;
			
			&.item_hover {
				background: rgba(0, 0, 0, 0.05);
				border-radius: 20rpx;
				margin: 0 -20rpx;
				padding: 30rpx 20rpx;
			}
			
			.benefit-icon {
				margin-right: 30rpx;
				
				.icon-img {
					width: 60rpx;
					height: 60rpx;
				}
			}
			
			.benefit-content {
				flex: 1;
				
				.benefit-title {
					display: block;
					font-size: 28rpx;
					color: #000;
					font-weight: 500;
					margin-bottom: 8rpx;
				}
				
				.benefit-desc {
					display: block;
					font-size: 28rpx;
					color: #000;
					opacity: 0.7;
				}
			}
			
			.benefit-status {
				.status-text {
					font-size: 28rpx;
					padding: 8rpx 20rpx;
					border-radius: 20rpx;
					
					&.available {
						color: #4CAF50;
						background: rgba(76, 175, 80, 0.1);
					}
					
					&.unavailable {
						color: #999;
						background: rgba(153, 153, 153, 0.1);
					}
				}
			}
		}
	}
}

.upgrade-section {
	display: flex;
	align-items: center;
	justify-content: space-between;
	
	.upgrade-content {
		flex: 1;
		
		.upgrade-title {
			display: block;
			font-size: 28rpx;
			color: #000;
			font-weight: 500;
			margin-bottom: 8rpx;
		}
		
		.upgrade-desc {
			display: block;
			font-size: 28rpx;
			color: #000;
			opacity: 0.7;
		}
	}
	
	.upgrade-btn {
		background: linear-gradient(90deg, #ff6b6b, #ff8e8e);
		border-radius: 30rpx;
		padding: 20rpx 40rpx;
		
		&.btn_hover {
			opacity: 0.8;
		}
		
		.btn-text {
			font-size: 28rpx;
			color: #fff;
			font-weight: 500;
		}
	}
}
</style>
