<template>
	<view class="page" :style="{ backgroundImage: `url(${imgUrl}default_bg.jpg)` }">
		<ktabbar :bgImg="imgUrl + 'default_bg.jpg'" :title="'会员权益'" />
		
		<!-- 会员卡轮播容器 -->
		<view class="member-cards-container">
			<swiper
				class="member-swiper"
				:indicator-dots="true"
				:autoplay="true"
				:interval="3000"
				:duration="500"
				indicator-color="rgba(255,255,255,0.5)"
				indicator-active-color="#fff"
			>
				<swiper-item v-for="(card, index) in memberCards" :key="index">
					<view class="member-card">
						<image :src="card.image" class="card-image" mode="aspectFill"></image>
						<view class="card-content">
							<view class="card-level">{{ card.level }}</view>
							<view class="card-desc">{{ card.description }}</view>
							<view class="card-progress" v-if="card.showProgress">
								<text class="progress-text">{{ card.progressText }}</text>
								<view class="progress-bar">
									<view class="progress-fill" :style="{ width: card.progressPercent + '%' }"></view>
								</view>
							</view>
						</view>
					</view>
				</swiper-item>
			</swiper>
		</view>

		<!-- 权益列表容器 -->
		<view class="benefits-container">
			<view class="benefits-section">
				<view class="section-title">
					<text class="title-text">会员权益</text>
				</view>
				
				<view class="benefits-list">
					<view
						class="benefit-item"
						v-for="(benefit, index) in benefitsList"
						:key="index"
						hover-class="item_hover"
						:hover-stay-time="50"
					>
						<view class="benefit-icon">
							<image :src="benefit.icon" class="icon-img" mode="aspectFit"></image>
						</view>
						<view class="benefit-content">
							<text class="benefit-title">{{ benefit.title }}</text>
							<text class="benefit-desc">{{ benefit.description }}</text>
						</view>
						<view class="benefit-status">
							<text class="status-text" :class="benefit.available ? 'available' : 'unavailable'">
								{{ benefit.available ? '已开通' : '未开通' }}
							</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 升级提示容器 -->
		<view class="upgrade-container" v-if="showUpgrade">
			<view class="upgrade-section">
				<view class="upgrade-content">
					<text class="upgrade-title">升级会员等级</text>
					<text class="upgrade-desc">完成更多任务，获取积分提升会员等级</text>
				</view>
				<view class="upgrade-btn" hover-class="btn_hover" :hover-stay-time="50" @click="goToTasks">
					<text class="btn-text">去完成任务</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import ktabbar from '@/components/ktabbar.vue';

export default {
	components: {
		ktabbar
	},
	data() {
		return {
			imgUrl: getApp().globalData.imgUrl,
			memberLevel: '',
			levelDesc: '',
			needPoints: 0,
			progressPercent: 0,
			showProgress: true,
			showUpgrade: true,
			memberCards: [
				{
					image: '/static/mp-qq/benefits/member-01.png',
					level: '启程者',
					description: '享受基础权益',
					showProgress: true,
					progressText: '马力值 0/800',
					progressPercent: 0
				},
				{
					image: '/static/mp-qq/benefits/member-02.png',
					level: '探索者',
					description: '享受进阶权益',
					showProgress: true,
					progressText: '马力值 800/2000',
					progressPercent: 40
				},
				{
					image: '/static/mp-qq/benefits/member-03.png',
					level: '征服者',
					description: '享受高级权益',
					showProgress: true,
					progressText: '马力值 2000/5000',
					progressPercent: 40
				},
				{
					image: '/static/mp-qq/benefits/member-04.png',
					level: '传奇者',
					description: '享受顶级权益',
					showProgress: false,
					progressText: '',
					progressPercent: 100
				}
			],
			benefitsList: [
				{
					icon: '/static/mp-qq/benefits/Benefits-01.png',
					title: '等级展示',
					description: '专属等级标识展示',
					available: true
				},
				{
					icon: '/static/mp-qq/benefits/Benefits-02.png',
					title: '专属热线',
					description: '享受专属客服热线',
					available: true
				},
				{
					icon: '/static/mp-qq/benefits/Benefits-03.png',
					title: '商城会员专价',
					description: '享受会员专属价格',
					available: false
				},
				{
					icon: '/static/mp-qq/benefits/Benefits-04.png',
					title: '每年积分增送50积分',
					description: '每年额外获得积分奖励',
					available: false
				},
				{
					icon: '/static/mp-qq/benefits/Benefits-05.png',
					title: '整车抵扣券50元生日礼包',
					description: '生日当月获得专属礼包',
					available: false
				},
				{
					icon: '/static/mp-qq/benefits/Benefits-06.png',
					title: '商城券50元入会礼',
					description: '入会即可获得商城券',
					available: false
				}
			]
		};
	},
	onLoad() {
		this.getMemberBenefits();
	},
	methods: {
		// 获取会员权益信息
		getMemberBenefits() {
			let _this = this;
			this.$request({
				url: 'v3/member/front/member/memberBenefits',
				method: 'GET'
			})
			.then((res) => {
				if (res.state == 200) {
					let result = res.data;
					_this.memberLevel = result.memberLevel;
					_this.levelDesc = result.levelDesc;
					_this.needPoints = result.needPoints;
					_this.progressPercent = result.progressPercent || 0;
					_this.showProgress = result.showProgress !== false;
					_this.showUpgrade = result.showUpgrade !== false;
					
					// 更新权益列表状态
					if (result.benefits && result.benefits.length > 0) {
						_this.benefitsList = result.benefits;
					}
				} else {
					this.$api.msg(res.msg);
				}
			})
			.catch((e) => {
				// 使用默认数据
			});
		},
		
		// 跳转到任务页面
		goToTasks() {
			this.$Router.push('/standard/point/task');
		}
	}
};
</script>

<style lang="scss">
.page {
	padding-bottom: 50rpx;
	width: 750rpx;
	min-height: 100vh;
	background-repeat: no-repeat;
	background-position: top center;
	background-size: 100% auto;
	background-attachment: fixed;
}

.member-cards-container {
	width: 92%;
	margin: 0 auto;
	margin-top: 50rpx;
	height: 400rpx;
}

.member-swiper {
	width: 100%;
	height: 100%;
	border-radius: 40rpx;
	overflow: hidden;
}

.member-card {
	position: relative;
	width: 100%;
	height: 100%;
	border-radius: 40rpx;
	overflow: hidden;
}

.card-image {
	width: 100%;
	height: 100%;
	border-radius: 40rpx;
}

.card-content {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	padding: 40rpx;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.card-level {
	font-size: 48rpx;
	color: #fff;
	font-weight: 600;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.card-desc {
	font-size: 28rpx;
	color: #fff;
	opacity: 0.9;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.card-progress {
	margin-top: 20rpx;

	.progress-text {
		font-size: 24rpx;
		color: #fff;
		margin-bottom: 15rpx;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
	}

	.progress-bar {
		height: 8rpx;
		background: rgba(255, 255, 255, 0.3);
		border-radius: 4rpx;
		overflow: hidden;

		.progress-fill {
			height: 100%;
			background: #fff;
			border-radius: 4rpx;
			transition: width 0.3s ease;
		}
	}
}

.benefits-container {
	width: 92%;
	margin: 0 auto;
	margin-top: 20rpx;
	background: #fff;
	border-radius: 40rpx;
	padding: 40rpx;
}

.upgrade-container {
	width: 92%;
	margin: 0 auto;
	margin-top: 20rpx;
	background: #fff;
	border-radius: 40rpx;
	padding: 40rpx;
}



.benefits-section {
	.section-title {
		margin-bottom: 40rpx;
		text-align: center;

		.title-text {
			font-size: 32rpx;
			color: #000;
			font-weight: 600;
		}
	}

	.benefits-list {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 40rpx 20rpx;

		.benefit-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			text-align: center;
			padding: 20rpx;

			&.item_hover {
				background: rgba(0, 0, 0, 0.05);
				border-radius: 20rpx;
			}

			.benefit-icon {
				margin-bottom: 20rpx;

				.icon-img {
					width: 80rpx;
					height: 80rpx;
				}
			}

			.benefit-content {
				.benefit-title {
					display: block;
					font-size: 24rpx;
					color: #000;
					font-weight: 500;
					margin-bottom: 8rpx;
					line-height: 1.4;
				}

				.benefit-desc {
					display: block;
					font-size: 20rpx;
					color: #666;
					line-height: 1.3;
				}
			}

			.benefit-status {
				margin-top: 10rpx;

				.status-text {
					font-size: 20rpx;
					padding: 4rpx 12rpx;
					border-radius: 12rpx;

					&.available {
						color: #4CAF50;
						background: rgba(76, 175, 80, 0.1);
					}

					&.unavailable {
						color: #999;
						background: rgba(153, 153, 153, 0.1);
					}
				}
			}
		}
	}
}

.upgrade-section {
	display: flex;
	align-items: center;
	justify-content: space-between;
	
	.upgrade-content {
		flex: 1;
		
		.upgrade-title {
			display: block;
			font-size: 28rpx;
			color: #000;
			font-weight: 500;
			margin-bottom: 8rpx;
		}
		
		.upgrade-desc {
			display: block;
			font-size: 28rpx;
			color: #000;
			opacity: 0.7;
		}
	}
	
	.upgrade-btn {
		background: linear-gradient(90deg, #ff6b6b, #ff8e8e);
		border-radius: 30rpx;
		padding: 20rpx 40rpx;
		
		&.btn_hover {
			opacity: 0.8;
		}
		
		.btn-text {
			font-size: 28rpx;
			color: #fff;
			font-weight: 500;
		}
	}
}
</style>
