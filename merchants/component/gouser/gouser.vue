<template>
  <!-- <image class="goBack" @tap="goBack"></view> -->
  <view class="goBack">
    <image :src="img_url + 'gouuser.png'"  @tap="goBack" class="goBack" ></image>
  </view>
</template>

<script>
  export default{
    data(){
      return{
        img_url:getApp().globalData.img_url
      }
    },
    methods:{
      // 返回
      goBack(){
      	uni.switchTab({
      		url:"/pages/user/user"
      	})
      },
    }
  }
</script>

<style lang="scss" scoped>
  .goBack{
  	position: fixed;
  	right: 60rpx;
  	bottom: 150rpx;
  	border-radius: 50%;
  	background-color: #FFFFFF;
    image{
      width: 86rpx;
      height: 86rpx;
    }
  }
</style>
