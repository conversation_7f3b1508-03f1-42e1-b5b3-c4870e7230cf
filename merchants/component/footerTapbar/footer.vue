<template>
	<view>
		<template name="ldjFooterBar">
			<view class="ldj_footer" :style="{ paddingBottom: bottomSateArea, paddingTop: bottomPaddingTop }">
				<view class="footer_item" data-url="/merchants/pages/index/index" @tap="jumpPage">
					<image v-if="imgSrc=='index'" :src="imgUrl + 'business/page_select.png'" mode="aspectFit"></image>
					<image v-else :src="imgUrl + 'business/page_noselect.png'" mode="aspectFit"></image>
					<text :class="imgSrc=='index'?'on':''">首页</text>
				</view>
				
				<!-- <view class="footer_item" data-url="/merchants/pages/chat/list" @tap="jumpPage">
					<image v-if="imgSrc=='chat'" :src="imgUrl + 'business/message_select.png'" mode="aspectFit"></image>
					<image v-else :src="imgUrl + 'business/message_noselect.png'" mode="aspectFit"></image>
					<text :class="imgSrc=='chat'?'on':''">消息</text>
				</view> -->
				
				<view class="footer_item" data-url="/merchants/pages/order/order" @tap="jumpPage">
					<image v-if="imgSrc=='order'" :src="imgUrl + 'business/order_select.png'" mode="aspectFit"></image>
					<image v-else :src="imgUrl + 'business/order_noselect.png'" mode="aspectFit"></image>
					<text :class="'icon ' + (imgSrc=='order'?'on':'') + ' ' + (icon_show?'ac':'')">订单</text>
				</view>
				
				<!-- <view class="footer_item" data-url="/merchants/pages/goods/goods" @tap="jumpPage">
					<image v-if="imgSrc=='goods'" :src="imgUrl + 'business/goods_select.png'" mode="aspectFit"></image>
					<image v-else :src="imgUrl + 'business/goods_noselect.png'" mode="aspectFit"></image>
					<text :class="imgSrc=='goods'?'on':''">商品</text>
				</view> -->
			</view>
		</template>
	</view>
</template>
<script>
	export default {
		name: "ldjFooterBar",
		data() {
			return {
				bottomSateArea:getApp().globalData.bottomSateArea ? getApp().globalData.bottomSateArea : '0rpx',//iphone手机底部一条黑线的高度
				bottomPaddingTop: getApp().globalData.bottomSateArea ? '10rpx' : '0rpx',
				imgUrl:getApp().globalData.imgUrl
			}
		},
		props: {
			imgSrc: {
				type: String,
				value: 'index'
			},
			icon_show: {
				type: Boolean,
				value: false
			},
		},
		methods: {
			jumpPage(e) {
				let url = e.currentTarget.dataset.url;
				if (url) {
					uni.redirectTo({
						url
					});
				}
			},
		}
	}
</script>
<style>
	page{
		width: 750rpx;
		margin: 0 auto;
	}
	.ldj_footer {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		min-height: 98rpx;
		background-color: #fff;
		z-index: 99;
		display: flex;
		align-items: center;
		justify-content: space-around;
		overflow: hidden;
		/* border-top: 1rpx solid #f9f9f9; */
		width: 750rpx;
		margin: 0 auto;
	}

	.ldj_footer .footer_item {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.ldj_footer .footer_item image {
		width: 40rpx;
		height: 40rpx;
	}

	.ldj_footer .footer_item text {
		position: relative;
		color: #7F8389;
		font-size: 22rpx;
		margin-top: 9rpx;
	}

	.ldj_footer .footer_item text.on {
		color: #477FEA;
	}

	.ldj_footer .footer_item text.icon::after {
		display: none;
		position: absolute;
		content: '';
		top: -50rpx;
		right: 0;
		width: 10rpx;
		height: 10rpx;
		border-radius: 50%;
		background-color: #ff0902;
	}

	.ldj_footer .footer_item text.icon.ac::after {
		display: block;
	}
</style>
