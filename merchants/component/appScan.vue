<template>
	<view>
		<!-- ios扫码速度快于安卓 -->
	</view>
</template>
<script>
	var barcode = null;
	export default {
		data() {
			return {
				flash: false, //是否打开摄像头
			};
		},
		mounted(d) {
			let pages = getCurrentPages(); //getCurrentPages() 函数用于获取当前页面栈的实例
			let page = pages[pages.length - 1];
			// #ifdef APP-PLUS
			plus.navigator.setFullscreen(false); //设置应用是否全屏显示
			let currentWebview = page.$getAppWebview(); //$getAppWebview() 可以得到当前webview的对象实例，从而实现对 webview 更强大的控制。
			this.createBarcode(currentWebview); //创建二维码窗口
			// #endif
		},
		methods: {

			// 扫码成功回调
			onmarked(type, result) {
				let text = '未知: ';
				switch (type) {
					case plus.barcode.QR:
						text = 'QR: ';
						break;
					case plus.barcode.EAN13:
						text = 'EAN13: ';
						break;
					case plus.barcode.EAN8:
						text = 'EAN8: ';
						break;
				}
				// 这里自己处理成功后的逻辑
				plus.navigator.setFullscreen(false); //关闭全屏显示 
				this.$emit("scanResult", {
					result: result //发送数据
				});
				// barcode.close();
			},
			// 重新开始扫码
			startScan() {
				barcode.start(); //开始扫码识别
			},
			// 创建二维码窗口
			createBarcode(currentWebview) {
				// 创建扫码识别控件对象
				barcode = plus.barcode.create('barcode', [plus.barcode.EAN13,plus.barcode.EAN8,plus.barcode.UPCA,plus.barcode.UPCE, plus.barcode.CODABAR,plus.barcode.CODE39, plus.barcode.CODE93,plus.barcode.CODE128], {
					top: '0',
					left: '0',
					width: '100%',
					height: '50%',
					scanbarColor: '#3C78E9',
					position: 'static',
					frameColor: '#3C78E9'
				});
				barcode.onmarked = this.onmarked; //扫码成功回调
				barcode.setFlash(this.flash); //操作闪光灯
				currentWebview.append(barcode); //把扫码控件添加到页面
				barcode.start(); //开始扫码识别
			},
		},
		onBackPress() {
			// #ifdef APP-PLUS
			// 返回时退出全屏
			barcode.close();
			plus.navigator.setFullscreen(false);
			// #endif
		},
		onUnload() {
			plus.navigator.setFullscreen(false);
		}


	};
</script>

<style scoped>
</style>
