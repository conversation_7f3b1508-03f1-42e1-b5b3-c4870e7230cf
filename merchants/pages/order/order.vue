<template>
	<view class="">
		<view class="container">
			<view class="top_nav">
				<!-- #ifdef APP-PLUS -->
				<view class="con_top flex_row_center_center">订单管理</view>
				<!-- #endif -->
				<!-- #ifdef MP -->
				<view class="con_top flex_row_center_center" :style="{ marginTop: 'calc(' + menuButtonTop + ' - 10rpx' + ')' }">订单管理</view>
				<!-- #endif -->
				<!-- 顶部搜索开始 -->
				<view class="top_container">
					<view class="searchBox">
						<input type="text" v-model="keyWord" placeholder="请输入订单号/商品名称" @confirm="search" ref="search" />
						<image :src="imgUrl + 'business/search_small.png'" class="sea_btn" mode="" @click="search"></image>
					</view>
				</view>
				<!-- 顶部搜索结束-->
				<!-- 订单状态tap切换开始 -->
				<view class="top_tap">
					<view v-for="(item,index) in tapList" :key="index" class="word" @tap.stop="changeTap(item, index)">
						<span class="active_tab" v-if="currentIndex == index">{{item.title}}</span>
						<span v-else>{{item.title}}</span>
					</view>
				</view>
				<!-- 订单状态tap切换结束 -->
			</view>
			<!-- 商品内容区开始 -->
			<!-- #ifndef MP -->
			<view class="goods_article">
			<!-- #endif -->
				<!-- #ifdef MP -->
				<view class="goods_article"
					:style="{ paddingTop: 'calc(' + '295rpx' + ' + ' + menuButtonTop + ' - 10rpx' + ')' }">
				<!-- #endif -->
			
				<view class="goods_item" v-for="(item,index) in goodsList" :key="index">
					<view class="goods_top_info" @click="goDetail(item)">
						<text>订单号：{{item.orderSn}}</text>
						<text class="order_state">{{ item.orderStateValue }}</text>
					</view>
					<view class="goods_info" @click="goDetail(item)"  v-for="(goodsItem,goodsindex) in item.orderProductListVOList" :key="goodsindex"
						:data-goodsid="goodsItem.goods_id">
						<view class="goods_info_left">
							<view v-if="!goodsItem.productImage"></view>
							<image :src="goodsItem.productImage" mode="aspectFit" v-if="goodsItem.productImage"></image>
						</view>
						<view class="goods_info_center">
							<view class="goodstext">{{goodsItem.goodsName}}</view>
							<view class="info_bottom">
								<text class="goods_date">买家：{{item.memberName}}</text>
							</view>
						</view>
						<view class="goods_info_right">
							<text>￥{{Number(goodsItem.productShowPrice).toFixed(2)}}</text>
							<text>x{{goodsItem.productNum}}</text>
						</view>
					</view>
					<view class="goods_bottom_info" @click="goDetail(item)">
						<view class="info_left">{{ item.createTime }}</view>
						<view class="info_right">
							共{{ item.goodsNum }}件商品，合计
							<span class="red">￥{{ item.orderAmount.toFixed(2) }}</span>
							<span class="tip" v-if="item.isPickup == 0">（含运费￥ {{ item.expressFee ? item.expressFee.toFixed(2) : '0.00' }}）</span>
						</view>
					</view>
					<view class="goods_bottom_btn" v-if="(item.orderState == 20 && item.isPickup == 0) || item.orderState == 31"  @click="goDelivery(item)">
						<view class="btn">{{ item.isPickup == 1 ? '核销' : '发货' }}</view>
					</view>
				</view>
			</view>
			<!-- 商品内容结束 -->
			<!-- 底部tapBar切换组件 -->
			<footerTapBar :imgSrc="'order'" />
			<loadingState v-if="loadingState == 'first_loading' || goodsList.length > 0" :state='loadingState'/>
			<view v-if="loadingState != 'first_loading'&& goodsList.length == 0"
				class="empty_part flex_column_start_center">
				<image :src="imgUrl+'empty_orders.png'" />
				<text>{{$L('这里空空如也~')}}</text>
			</view>
		</view>
		
	</view>

</template>

<script>
	import loadingState from "@/components/loading-state.vue";
	import footerTapBar from '../../component/footerTapbar/footer.vue'
	import gouser from '../../component/gouser/gouser.vue'
	export default {
		name: "goods",
		components: {
			loadingState,
			footerTapBar,
			gouser
		},
		onLoad(option) {
			this.getInitList()
		},
		data() {
			return {
				// #ifdef MP
				menuButtonHeight: uni.getMenuButtonBoundingClientRect().height + 'px',
				menuButtonTop: uni.getMenuButtonBoundingClientRect().top + 'px',
				// #endif
				imgUrl: getApp().globalData.imgUrl,
				keyWord: '', //搜索框绑定的值
				currentIndex: 0, //控制tap切换下边下划线显示与隐藏
				tapList: [
					{ state: '', title: '全部' },
					{ state: 99, title: '进行中' },
					{ state: 40, title: '已完成' },
				],
				goodsList: [],
				param: {},
				current: 1,
				pageSize: 10,
				orderState: '', //订单状态
				total: '', //总条目数
				hasMore: true, //是否还有数据
				produceAmount: [], //商品总件数
				loadingState: 'first_loading',
			}
		},
		methods: {
			goDetail(item){
				this.$Router.push({
					path: '/merchants/pages/order/order_detail',
					query: {
						orderSn: item.orderSn
					}
				})
			},
			goDelivery(item){
				if (item.isPickup == 1) {
					this.goDetail(item)
					return
				}
				this.$Router.push({
					path: '/merchants/pages/order/order_delivery',
					query: {
						orderSn: item.orderSn
					}
				})
			},
			//控制tap切换
			changeTap(item, index) {
				this.currentIndex = index
				this.orderState = item.state
				this.current = 1
				this.getInitList()
			},
			//获取列表数据
			getInitList(param) {
				if (this.loadingState === 'loading') {
					//防止重复加载
					return;
				}
				uni.showLoading({
					title: '加载中',
					mask: false
				})
				let params = {}
				params.url = 'v3/business/seller/orderInfo/list'
				params.data = {
					current: this.current,
					pageSize: this.pageSize
				}
				if (this.orderState) {
					params.data.orderState = this.orderState
				}
				if (this.keyWord) {
					params.data.orderSnOrGoodsName = this.keyWord
				}
				this.loadingState = this.loadingState == 'first_loading' ? this.loadingState : 'loading';
				this.$seller_request(params).then(res => {
					if (res.state == 200) {
						res.data.list.map(v => {
							let goodsNum = 0
							v.orderProductListVOList.map(x => {
								goodsNum+= x.productNum
							})
							v.goodsNum = goodsNum
						})
						if (this.current == 1) {
							this.goodsList = res.data.list
						} else {
							this.goodsList = this.goodsList.concat(res.data.list)
						}
						let hasMore = this.$checkPaginationHasMore(res.data.pagination); //是否还有数据
						if (hasMore) {
							this.current++
							this.hasMore = true
						} else {
							this.hasMore = false
						}
						this.loadingState = hasMore ? 'allow_loading_more' : 'no_more_data'
						
						this.total = res.data.pagination.total
						uni.hideLoading()
						uni.stopPullDownRefresh()
					} else {
						this.$api.msg(res.msg)
					}
				})
			},
			//搜索事件函数
			search() {
				this.current = 1
				this.getInitList()
			},
			//搜索结束
			stopSearch() {
				if (!this.keyWord) {
					this.current = 1
					this.getInitList()
				}
			}
		},
		//页面滚动生命周期
		onReachBottom() {
			if (this.hasMore) {
				this.getInitList()
			}
		},
		//下拉刷新
		onPullDownRefresh() {
			this.current = 1
			this.getInitList()
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #F5F5F5;
	}

	.container {
		height: 100%;
		background-color: #F5F5F5;
		padding-bottom: 130rpx;
		box-sizing: border-box;
		
		.top_nav {
			position: fixed;
			top: 0;
			left: 0;
			z-index: 99;
			background-color: #fff;
			/* #ifdef APP */
			padding-top: var(--status-bar-height);
			/* #endif */
			
			.con_top {
				width: 100%;
				height: 88rpx;
				background-color: #fff;
				color: #000000;
				font-size: 32rpx;
				margin-bottom: 18rpx;
			}
		}
		
		.top_container{
			height: 104rpx;
			background-color: #fff;
			padding: 14rpx 0;
		}
		.searchBox{
			position: relative;
			width: calc(100% - 48rpx);
			margin: 0 auto;
			.sea_btn{
				position: absolute;
				width: 36rpx;
				height: 36rpx;
				top: 16rpx;
				right: 16rpx;
			}
			input{
				height: 76rpx;
				width: 100%;
				background-color: #F6F6F6;
				color: #888888;
				font-size: 28rpx;
				border-radius: 6rpx;
				padding: 0 52rpx 0 16rpx;
			}
		}

		//商品状态tap切换
		.top_tap {
			display: flex;
			width: 750rpx;
			height: 88rpx;
			background-color: #ffffff;
			font-size: 30rpx;
			justify-content: space-around;
			padding: 0 24rpx;
			.word {
				display: flex;
				justify-content: center;
				align-items: center;
				text-align: center;
				span{
					height: 88rpx;
					line-height: 88rpx;
				}
			}
			.active_tab{
				color: #3C78E9;
				border-bottom: 4rpx solid #3C78E9;
			}
		}

		.goods_article {
			width: 750rpx;
			// background-color: #FFFFFF;
			margin-top: 14rpx;
			/* #ifndef APP-PLUS */
			padding-top: 192rpx;
			/* #endif */
			/* #ifdef APP-PLUS */
			padding-top: calc(295rpx + var(--status-bar-height));
			/* #endif */

			.goods_item {
				background-color: #fff;
				margin-bottom: 16rpx;
				.goods_top_info {
					width: 100%;
					height: 70rpx;
					font-size: 24rpx;
					padding-left: 24rpx;
					padding-right: 28rpx;
					color: #6C6C6C;
					display: flex;
					justify-content: space-between;
					align-items: center;

					.order_state {
						font-size: 28rpx;
						color: #3C78E9;
					}
				}

				.goods_info {
					height: 146rpx;
					display: flex;
					box-sizing: border-box;
					background-color: #F8F8F8;
					padding:  8rpx 28rpx 18rpx 24rpx;
					.goods_info_left {
						view {
							width: 120rpx;
							height: 120rpx;
							background-color: #D8D8D8;
						}

						image {
							width: 120rpx;
							height: 120rpx;
						}
					}

					.goods_info_center {
						width: 390rpx;
						height: 100%;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						margin-left: 16rpx;
						box-sizing: border-box;

						.goodstext {
							height: 80rpx;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 2;
							overflow: hidden;
							font-size: 28rpx;
							font-family: SourceHanSansCN-Regular, SourceHanSansCN;
							font-weight: 400;
							color: #101010;
						}

						.goods_price {
							font-size: 28rpx;
							font-family: SourceHanSansCN-Medium, SourceHanSansCN;
							font-weight: 550;
							color: #F07500;
						}

						.info_bottom {
							display: flex;
							justify-content: space-between;
						}

						.goods_date {
							font-size: 20rpx;
							color: #9A9A9A;
						}
					}

					.goods_info_right {
						display: flex;
						flex-direction: column;
						align-items: flex-end;
						flex: 1;
						color: #4F4F4F;
						font-size: 24rpx;
						text:nth-of-type(2) {
							font-size: 24rpx;
							color: #9A9A9A;
						}
					}
				}

				.goods_bottom_info {
					width: 100%;
					height: 80rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding-left: 24rpx;
					padding-right: 28rpx;
					.info_left{
						font-size: 20rpx;
						color: #9A9A9A;
					}
					.info_right{
						display: flex;
						align-items: center;
						color: #6C6C6C;
						font-size: 24rpx;
						.tip{
							font-size: 20rpx;
							color: #9A9A9A;
						}
						.red{
							color: #EC1400;
						}
					}
				}
				.goods_bottom_btn{
					height: 80rpx;
					display: flex;
					justify-content: flex-end;
					align-items: center;
					border-top: 2rpx solid #EFEFEF;
					padding-right: 28rpx;
					.btn{
						width: 140rpx;
						height: 46rpx;
						line-height: 46rpx;
						border-radius: 4px;
						background-color: rgba(255, 255, 255, 1);
						color: rgba(60, 120, 233, 1);
						border: 2rpx solid #3C78E9;
						font-size: 24rpx;
						text-align: center;
					}
				}
			}
		}
	}
	
	.empty_part {
		padding-top: 108rpx;
	
		image {
			width: 380rpx;
			height: 280rpx;
		}
	
		text {
			color: $main-third-color;
			font-size: 26rpx;
			margin-top: 57rpx;
		}
	
		button {
			width: 245rpx;
			height: 66rpx;
			background: var(--color_halo);
			border-radius: 33rpx;
			color: var(--color_main);
			font-size: 30rpx;
			font-weight: bold;
			margin-top: 29rpx;
			border: none;
		}
	
		uni-button:after {
			border-radius: 200rpx;
			border-color: #fff;
		}
	}
</style>
