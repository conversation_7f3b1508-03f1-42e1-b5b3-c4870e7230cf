<template>
	<view class="container">
		<!-- 第一步确认收货信息开始 -->
		<view class="first_step">
			<view class="first_step_top">
				<text>第一步：确认收货信息及交易详情</text>
			</view>
			<view class="goods_item">
				<view class="goods_top_info">
					<view class="goods_code">
						<image :src="img_url + 'delv_store.png'" mode=""></image>
						<text>单号：{{orderSn}}</text>
					</view>
					<text class="order_time">{{createTime}}</text>
				</view>
				<view class="goods_info" v-for="(goodsItem,goodsindex) in goods_info_list" :key="goodsindex"
					:data-goodsid="goodsItem.goodsId">
					<view class="goods_info_left">
						<view v-if="!goodsItem.productImage"></view>
						<image :src="goodsItem.productImage" mode="" v-if="goodsItem.productImage"></image>
					</view>
					<view class="goods_info_center">
						<view class="goodstext">{{goodsItem.goodsName}}</view>
					</view>
					<view class="goods_info_right">
						<text>￥{{Number(goodsItem.productShowPrice).toFixed(2)}}</text>
						<text>*{{goodsItem.productNum}}</text>
					</view>
				</view>
				<view class="freight">
					<view class="freight_left">
						<text>运费</text>
						<text>实付款（含运费）</text>
					</view>
					<view class="freight_right">
						<text>￥{{Number(freight).toFixed(2)}}</text>
						<text>￥{{Number(orderAmount).toFixed(2)}}</text>
					</view>
				</view>
				<view class="message">
					<image :src="img_url + 'messages.png'"></image>
					<input class="message_inp" type="text" v-model="message" disabled />
				</view>
			</view>
			<view class="address">
				<view class="adress_top">
					<image class="icon2" referrerpolicy="no-referrer" :src="img_url + 'gpsposition.png'" />
					<text class="info3">收&nbsp;货&nbsp;人：</text>
					<text class="word12">{{receiverName}}</text>
					<text class="word13">{{receiverMobile}}</text>
				</view>
				<view class="adress_bottom">
					<text class="infoBox1">
						收货地址：{{receiverAddress}}
					</text>
					<!-- <image
              class="icon3"
              referrerpolicy="no-referrer"
              src="https://lanhu.oss-cn-beijing.aliyuncs.com/SketchPng1a58191bc9c3e811b662408d0df2d7183a27e77cfe92742c6af35ea90c0cf295"
            /> -->
				</view>
			</view>
		</view>
		<!-- 第一步确认收货信息结束-->
		<!-- 第二步确认发布消息开始 -->
		<view class="first_step">
			<view class="first_step_top" id="seconds">
				<text>第一步：确认发货信息</text>
			</view>
			<view class="address" id="second">
				<view class="adress_top">
					<image class="icon2" referrerpolicy="no-referrer" :src="img_url + 'gpsposition.png'" />
					<text class="info3">发&nbsp;货&nbsp;人：</text>
					<text class="word12">{{deliverName}}</text>
					<text class="word13">{{deliverMobile}}</text>
				</view>
				<view class="adress_bottom">
					<text class="infoBox1">
						发货地址：{{storeAddress}}
					</text>

				</view>
			</view>
		</view>
		<!-- 第二步确认发布消息结束-->
		<!-- 第三步选择物流服务开始 -->
		<view class="logistics">
			<view class="first_step_top">
				<text>第二步：选择物流服务</text>
			</view>
			<view class="logistics_center">
				<view class="info" data-index="0" @tap.stop="changeTap">
					<text>物流运输</text>
					<view :class="{on:isFlag == 0}"></view>
				</view>
				<view class="info_second" data-index="1" @tap.stop="changeTap">
					<text>无需物流运输</text>
					<view :class="{on:isFlag == 1}"></view>
				</view>
			</view>
			<!-- 物流运输开始 -->
			<view class="logistics_list" v-if="deliverType == 0">
				<view class="logistics_list_top">
					<view>公司名称</view>
					<view>物流单号</view>
					<view>操作</view>
				</view>
				<view class="logistics_list_info" v-for="(item,index) in traceList" :key="index">
					<view class="company_name" @tap.stop="checked" :data-expressids="item.expressId"
						:data-index="index">
						<text
							:class="{iconfont:true,iconziyuan33:item.expressId==expressId,iconziyuan43:item.expressId!=expressId}"
							:data-expressids="item.expressId" @tap.stop="checked" :data-index="index"></text>
						<text>{{item.expressName}}</text>
					</view>
					<view class="card">
						<input ref="inp" type="text" v-model="card[index]" :disabled="item.expressId!=expressId"
							:placeholder="item.expressId==expressId ? '请输入运单号':''" />
					</view>
					<view class="operation">
						<text class="deliveryGoods" @tap.stop="deliveryGoods" :data-expressId="item.expressId"
							:data-index="index">发货</text>
					</view>
				</view>
			</view>
			<!-- 物流运输结束 -->
			<!-- 无需物流运输开始 -->
			<view class="noLogistic" v-if="deliverType == '1'">
				<view class="nologistic_top">
					<view>联系人</view>
					<view>联系电话</view>
				</view>
				
				<view class="nologistic_top">
					<view>{{deliverName}}</view>
					<view>{{deliverMobile}}</view>
				</view>

					<view class="nologistic_bottom">
						<text @tap.stop="deliveryGoods">确认</text>
					</view>
				</view>
			</view>
			<!-- 第三步选择物流服务结束 -->
			<!-- 底部tap切换 -->
			<footerTapBar :imgSrc="'order'" />
		</view>
</template>

<script>
	import footerTapBar from '../../component/footerTapbar/footer.vue'
	export default {
		name: "delivery",
		components: {
			footerTapBar,
		},
		onLoad(option) {
			this.orderSn = option.orderSn
			this.getorderDetail()
			this.getTraceList()
			this.getdeliveradress()
		},
		data() {
			return {
				goodsList: [], //商品循环数组
				tapList: ['出售中', '仓库中', '违规商品', '待审核'],
				store_url: getApp().globalData.storeUrl, //接口地址
				goods_info_list: [],
				img_url: getApp().globalData.img_url,
				message: '', //买家留言
				isFlag: '',
				orderSn: '', //订单号
				createTime: '', //创建时间
				orderAmount: '', //实付款，含运费
				moneyAmount: '', //订单金额不含运费
				freight: '', //运费
				receiverName: '', //收货人
				receiverMobile: '', // 收货人联系方式
				receiverAddress: '', //收获地址
				deliverName: '', //发货人
				deliverMobile: '', //发货人电话
				storeAddress: '', //发货地址
				traceList: [], //店铺已绑定物流公司
				isChecked: false, //是否处于选中状态
				card: ['', '', '', ''], //单号
				expressId: '', //物流公司id
				deliverType: '', //发货类型
				index: '', //物流公司索引
				deliveradress: '', //发货地址
				mobile: '',
				mobile_phone: '',
			}
		},
		methods: {
			//获取发货地址
			getdeliveradress() {
				this.$seller_request({
					url: 'v3/seller/seller/address/list',
					method: 'GET',
					data: {
						type: 1
					},
				}).then(ret => {
					const adress = ret.data.list[0]
					this.deliverName = adress.contactName
					this.deliverMobile = adress.telphone
					this.storeAddress = adress.areaInfo
					this.mobile_phone = adress.telphone
					this.mobile = adress.contactName
				})
			},
			//底部物流tap栏切换
			changeTap(e) {
				const index = e.currentTarget.dataset.index
				this.isFlag = index
				this.deliverType = index
			},
			//获取详情数据
			getorderDetail() {
				// uni.showLoading({
				//   title:'加载中',
				//   mask:true
				// })
				this.$seller_request({
					url: 'v3/business/seller/orderInfo/detail',
					method: 'GET',
					data: {
						orderSn: this.orderSn
					}
				}).then(ret => {
					if (ret.state == 200) {
						uni.hideLoading()
						this.goods_info_list = ret.data.orderProductList
						this.createTime = ret.data.createTime
						this.message = ret.data.orderRemark
						this.orderAmount = ret.data.orderAmount
						this.moneyAmount = ret.data.moneyAmount
						this.freight = Number(ret.data.orderAmount) - Number(ret.data.moneyAmount)
						this.receiverAddress = ret.data.receiverAddress
						this.receiverMobile = ret.data.receiverMobile
						this.receiverName = ret.data.receiverName
						this.deliverType = ret.data.deliverType
						this.isFlag = ret.data.deliverType
						// this.deliverMobile = ret.data.deliverMobile
						// this.deliverName = ret.data.deliverName
						// this.storeAddress = ret.data.storeAddress
					}
				})
			},
			//获取店铺已绑定物流公司
			getTraceList() {
				this.$seller_request({
					url: 'v3/seller/seller/express/list',
					method: 'GET',
				}).then(ret => {
					if (ret.state == 200) {
						this.traceList = ret.data.list //获取列表
						// this.card = new Array()//创建单号
						ret.data.list.map(i => this.card.push(''))

					}
				})
			},
			//选择物流公司
			checked(e) {
				let expressId = e.currentTarget.dataset.expressids
				let index = e.currentTarget.dataset.index
				if (expressId != this.expressId) {
					this.card.splice(this.index, 1, '')
					this.expressId = expressId
				}
				this.index = index
			},
			//发货
			deliveryGoods(e) {
				if (!this.deliverName || !this.deliverMobile) {
					uni.showToast({
						title: '请补充发货人信息',
						icon: 'none'
					})
					return;
				}
				if (this.deliverType == 1) {
					if (!this.mobile || this.$checkSpace(this.mobile)) {
						uni.showToast({
							title: '请输入联系人',
							icon: 'none'
						})
						return
					}
					if (!this.$checkMobile(this.mobile_phone, '联系电话')) {
						return
					}
				}
				const index = e.currentTarget.dataset.index
				let param = {}
				param.orderSn = this.orderSn
				param.deliverType = this.deliverType
				param.expressId = this.expressId
				param.expressNumber = this.card[index]
				param.deliverType = this.deliverType
				param.deliverName = this.deliverType == 1 ? this.mobile : this.deliverName
				param.deliverMobile = this.deliverType == 1 ? this.mobile_phone : this.deliverMobile
				param.storeAddress = this.storeAddress
				this.$seller_request({
					url: 'v3/business/seller/orderInfo/deliver',
					method: 'POST',
					data: param
				}).then(ret => {
					if (ret.state == 200) {
						this.$api.msg(ret.msg)
						uni.navigateTo({
							url: "/merchants/pages/order/order?orderSn=" + this.orderSn
						})
					} else {
						this.$api.msg(ret.msg)
					}
				})
			},
			//发货与收获地址跳转事件
			goToAdress() {
				uni.navigateTo({
					url: "/merchants/pages/shopaddress/address?orderSn=" + this.orderSn
				})
			}
		},

	}
</script>

<style lang="scss" scoped>
	page {
		background-color: #F5F5F5;
	}

	.container {
		padding-bottom: 100rpx;
		box-sizing: border-box;

		.first_step {
			width: 750rpx;
			background-color: #FFFFFF;

			.first_step_top {
				width: 750rpx;
				height: 88rpx;
				background: #FFFFFF;
				display: flex;
				align-items: center;
				padding: 0 20rpx;
				font-size: 28rpx;
				font-family: SourceHanSansCN-Medium, SourceHanSansCN;
				font-weight: 500;
				color: #FF9800;
				border-bottom: 10rpx solid #F5F5F5;
			}

			.goods_item {
				padding-right: 20rpx;
				padding-left: 20rpx;
				padding-bottom: 12rpx;
				box-sizing: border-box;

				.goods_top_info {
					width: 100%;
					height: 68rpx;
					border-bottom: 2rpx solid #F5F5F5;
					// margin-bottom: 20rpx;
					font-size: 28rpx;
					font-family: SourceHanSansCN-Regular, SourceHanSansCN;
					font-weight: 400;
					color: #2D2D2D;
					display: flex;
					justify-content: space-between;
					align-items: center;

					.goods_code {
						display: flex;
						justify-content: space-between;
						align-items: center;

						image {
							width: 38rpx;
							height: 38rpx;
							margin-right: 10rpx;
						}
					}

					.order_time {
						font-size: 24rpx;
						font-family: SourceHanSansCN-Regular, SourceHanSansCN;
						font-weight: 400;
						color: #999999;
					}
				}

				.goods_info {
					height: 190rpx;
					display: flex;
					// margin-bottom: 20rpx;
					padding-top: 20rpx;
					box-sizing: border-box;
					border-bottom: 2rpx solid #F5F5F5;

					.goods_info_left {
						view {
							width: 150rpx;
							height: 150rpx;
							background-color: #D8D8D8;
							border-radius: 4rpx;
						}

						image {
							width: 150rpx;
							height: 150rpx;
							border-radius: 4rpx;
						}
					}

					.goods_info_center {
						width: 390rpx;
						height: 150rpx;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						margin-left: 10rpx;
						padding-bottom: 20rpx;
						box-sizing: border-box;

						.goodstext {
							height: 80rpx;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 2;
							overflow: hidden;
							font-size: 28rpx;
							font-family: SourceHanSansCN-Regular, SourceHanSansCN;
							font-weight: 400;
							color: #2D2D2D;
						}

						.goods_price {
							font-size: 28rpx;
							font-family: SourceHanSansCN-Medium, SourceHanSansCN;
							font-weight: 550;
							color: #F07500;
						}

						.info_bottom {
							display: flex;
							justify-content: space-between;
						}

						.goods_date {
							font-size: 24rpx;
							font-family: SourceHanSansCN-Regular, SourceHanSansCN;
							font-weight: 400;
							color: #666666;
						}
					}

					.goods_info_right {
						display: flex;
						flex-direction: column;
						align-items: flex-end;
						flex: 1;

						text:nth-of-type(2) {
							font-size: 24rpx;
							font-family: SourceHanSansCN-Regular, SourceHanSansCN;
							font-weight: 400;
							color: #999999;
						}
					}
				}

				.goods_bottom_info {
					width: 100%;
					height: 144rpx;
					display: flex;
					justify-content: space-between;
					padding-top: 20rpx;

					.goods_bottom_info_left {
						font-size: 24rpx;
						font-family: SourceHanSansCN-Regular;
						font-weight: 400;
						color: #666666;
					}

					.goods_bottom_info_right {
						display: flex;
						flex-direction: column;
						justify-content: space-between;

						.goods_bottom_info_right_top {
							display: flex;
							align-items: flex-start;
							justify-content: flex-end;

							.ammount_money {
								font-size: 24rpx;
								font-family: SourceHanSansCN-Medium, SourceHanSansCN;
								font-weight: 550;
								color: #333333;
							}

							text:nth-of-type(2) {
								font-size: 24rpx;
								font-family: SourceHanSansCN-Regular, SourceHanSansCN;
								font-weight: 400;
								color: #999999;
								margin-left: 10rpx;
							}
						}

						.goods_bottom_info_right_bottom {
							display: flex;
							justify-content: flex-end;
							margin-bottom: 22rpx;

							view {
								width: 140rpx;
								height: 52rpx;
								background: #FFFFFF;
								border-radius: 30rpx;
								border: 2rpx solid #CCCCCC;
								text-align: center;
								line-height: 52rpx;
								font-size: 26rpx;
								font-family: SourceHanSansCN-Regular, SourceHanSansCN;
								font-weight: 400;
								color: #666666;
								margin-left: 20rpx;
							}

							.on {
								border: 2rpx solid #FFA434;
								color: #FFA434;
							}
						}
					}
				}

				.freight {
					width: 750rpx;
					height: 110rpx;
					display: flex;
					padding: 20rpx;
					box-sizing: border-box;
					justify-content: space-between;
					padding-right: 40rpx;
					border-bottom: 2rpx solid #F5F5F5;

					view {
						height: 76rpx;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
					}

					.freight_left {
						font-size: 24rpx;
						font-family: SourceHanSansCN-Regular, SourceHanSansCN;
						font-weight: 400;
						color: #666666;
					}

					.freight_right {
						font-size: 24rpx;
						font-family: SourceHanSansCN-Medium, SourceHanSansCN;
						font-weight: 550;
						color: #333333;
						align-items: flex-end;

						text:nth-of-type(2) {
							color: #FF9800;
						}
					}
				}

				.message {
					display: flex;
					margin-top: 10rpx;

					image {
						width: 32rpx;
						height: 32rpx;
						margin-right: 10rpx;
					}

					.message_inp {
						font-size: 24rpx;
						font-family: SourceHanSansCN-Regular, SourceHanSansCN;
						font-weight: 400;
						color: #666666;
						width: 90%;
					}
				}
			}

			.address {
				// height: 200rpx;
				width: 750rpx;
				background-color: #FFf;
				border-top: 20rpx solid #F5F5F5;
				padding: 20rpx;
				border-bottom: 20rpx solid #F5F5F5;

				.adress_top {
					width: 100%;
					height: 32rpx;
					display: flex;
					align-items: center;
					justify-content: flex-start;
					font-size: 28rpx;
					font-family: 'SourceHanSansCN';
					font-weight: 400;
					color: #2D2D2D;

					image {
						width: 32rpx;
						height: 32rpx;
					}

					text {
						margin-left: 10rpx;
					}

					.info3 {
						width: 152rpx;
						display: flex;
					}

					.word13 {
						margin-left: 40rpx;
					}

				}

				.adress_bottom {
					width: 100%;
					display: flex;
					justify-content: space-between;
					margin-top: 20rpx;

					// align-items: center;
					image {
						width: 32rpx;
						height: 32rpx;
					}

					.infoBox1 {
						width: 588rpx;
						margin-left: 40rpx;
						font-size: 28rpx;
						font-family: SourceHanSansCN-Regular, SourceHanSansCN;
						font-weight: 400;
						color: #2D2D2D;
					}
				}
			}

			#second {
				border-top: 0;
			}

			#seconds {
				border-bottom: 2rpx solid #F5F5F5;
			}
		}

		.logistics {
			background-color: #fff;

			.first_step_top {
				width: 750rpx;
				height: 88rpx;
				background: #FFFFFF;
				display: flex;
				align-items: center;
				padding: 0 20rpx;
				font-size: 28rpx;
				font-family: SourceHanSansCN-Medium, SourceHanSansCN;
				font-weight: 500;
				color: #FF9800;
				border-bottom: 10rpx solid #F5F5F5;
			}

			.logistics_center {
				width: 750rpx;
				height: 74rpx;
				display: flex;
				padding: 20rpx;
				padding-bottom: 0;
				box-sizing: border-box;
				font-size: 28rpx;
				font-family: SourceHanSansCN-Medium, SourceHanSansCN;
				font-weight: 500;
				color: #333333;

				.info {
					height: 100%;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					align-items: center;

					view {
						width: 80rpx;
						border-bottom: 4rpx solid #FF9800;
						border-radius: 2rpx;
						display: none;
					}

					.on {
						display: block;
					}
				}

				.info_second {
					display: flex;
					flex-direction: column;
					height: 100%;
					margin-left: 20rpx;
					flex-direction: column;
					justify-content: space-between;
					align-items: center;

					view {
						width: 80rpx;
						border-bottom: 4rpx solid #FF9800;
						border-radius: 2rpx;
						display: none;
					}

					.on {
						display: block;
					}
				}
			}

			.logistics_list {
				width: 750rpx;

				.logistics_list_top {
					width: 750rpx;
					height: 60rpx;
					display: flex;
					justify-content: space-around;
					align-items: center;
					border-bottom: 2rpx solid #F5F5F5;

					view {
						flex: 1;
						text-align: center;
						font-size: 24rpx;
					}

					view:nth-of-type(1) {
						text-align: left;
						text-indent: 24rpx;
					}

					view:nth-of-type(2) {
						text-align: center;
						box-sizing: border-box;
					}

					view:nth-of-type(3) {
						text-align: right;
						box-sizing: border-box;
						padding-right: 20rpx;
					}
				}

				.logistics_list_info {
					display: flex;
					justify-content: space-between;
					border-bottom: 2rpx solid #F5F5F5;

					.card {
						flex: 1;
					}

					.company_name {
						// width: 260rpx;
						text-align: center;
						font-size: 24rpx;
						display: flex;
						align-items: center;
						justify-content: flex-start;
						padding-left: 20rpx;
						box-sizing: border-box;
					}

					.iconziyuan33 {
						color: #FF9800;
					}

					.iconziyuan43 {
						color: #666666;
					}

					.iconfont {
						margin-right: 6rpx;
					}

					.operation {
						// width: 148rpx;
						display: flex;
						justify-content: flex-start;
						align-items: center;
						height: 80rpx;
						padding-right: 20rpx;

						.deliveryGoods {
							border: 2rpx solid #FF9800;
							background-color: #FF9800;
							color: #fff;
							border-radius: 6rpx;
							font-size: 24rpx;
						}
					}

					.card {
						font-size: 24rpx;
						display: flex;
						align-items: center;
						margin-left: 20rpx;

						input {
							width: 90%;
							font-size: 24rpx;
						}
					}
				}
			}

			.nologistic_top {
				display: flex;
				justify-content: space-around;
				border: 2rpx solid #F5F5F5;
				font-size: 28rpx;
				height: 60rpx;
				line-height: 60rpx;

				view {
					flex: 1;
					text-align: left;
					padding-left: 40rpx;
					box-sizing: border-box;
				}
			}

			.nologistic_bottom {
				width: 750rpx;
				height: 180rpx;
				display: flex;
				justify-content: center;
				align-items: center;

				text {
					width: 300rpx;
					height: 80rpx;
					text-align: center;
					line-height: 80rpx;
					background-color: #FF9800;
					border-radius: 20rpx;
					color: #fff;
				}
			}

		}
	}

	.nologistic {
		display: flex;
		justify-content: space-around;
	}

	.nologistic input {
		font-size: 28rpx;
	}

	.nologistic_pla {
		font-size: 28rpx;
		// padding-left: 20rpx;
		color: #909399;
	}
</style>
