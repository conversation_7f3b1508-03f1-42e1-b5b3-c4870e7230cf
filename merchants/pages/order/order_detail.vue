<template>
	<view class="container" v-if="orderDetail" :style="mix_diyStyle">
		<view class="order_state flex_row_center_center">
			<view class="flex_row_center_center">
				<image :src="imgUrl + 'business/pick_order_icon.png'" mode=""></image>
				{{ orderDetail.orderStateValue }}
			</view>
		</view>
		<view class="pick_detail flex_row_between_center" v-if="(orderDetail.orderState == 30 || orderDetail.orderState == 40) && orderDetail.isPickup == 2">
			<view class="pd_left">
				<image :src="orderDetail.deliveryman.avatar" mode=""></image>
				<span>
				{{ orderDetail.deliveryman.name }}
				</span>
			</view>
			<image @click="callPhone(orderDetail.deliveryman.phone)" class="pd_right" :src="imgUrl + 'business/fa_phone.png'" mode=""></image>
		</view>
		
		<view class="pick_address" v-if="orderDetail.isPickup == 2">
			<div class="pa_top">
				{{ orderDetail.pickupPoint.name }}
			</div>
			<div class="pa_bot">
				{{ orderDetail.pickupPoint.address }}
			</div>
		</view>
		<view class="code_box flex_row_center_center" v-if="(orderDetail.orderState == 30 || orderDetail.orderState == 31) && orderDetail.isPickup == 1">
			核销码：{{ orderDetail.pickupCode }}</view>
		<view class="goods_box">
			<view class="goods_item" v-for="(item, index) in orderDetail.orderProductList" :key="index">
				<view class="left">
					<image :src="item.productImage" mode=""></image>
				</view>
				<view class="right">
					<view class="">
						<view class="name">{{ item.goodsName }}</view>
						<view class="size">
							<text class="item_num">数量：{{item.productNum}}</text>
							<text v-if="item.specValues">规格：{{ item.specValues }}</text>
						</view>
					</view>
					<view class="price">￥{{ item.productShowPrice.toFixed(2) }}</view>
				</view>
			</view>
		</view>
		<view class="price_box">
			<view class="price_item">
				<span class="left">商品总额</span>
				<span class="right">￥{{ orderDetail.goodsAmount ? orderDetail.goodsAmount.toFixed(2) : 0 }}</span>
			</view>
			<view class="price_item">
				<span class="left">满优惠</span>
				<span class="right">-￥{{ orderDetail.activityDiscountAmount ? orderDetail.activityDiscountAmount : 0 }}</span>
			</view>
			<view class="price_item">
				<span class="left">实付款</span>
				<span class="right">￥{{ orderDetail.orderAmount ? orderDetail.orderAmount.toFixed(2) : 0 }}</span>
			</view>
		</view>
		<view class="info_box">
			<view class="tit">订单信息</view>
			<view class="info_item">
				<span class="item_tit">订单编号：</span>
				<span class="item_val">{{ orderDetail.orderSn }}</span>
			</view>
			<view class="info_item">
				<span class="item_tit">订单备注：</span>
				<span class="item_val">{{ orderDetail.orderRemark ? orderDetail.orderRemark : '--' }}</span>
			</view>
			<block v-if="orderDetail.orderLogs && orderDetail.orderLogs.length > 0">
				<view class="info_item" v-for="(item, index) in orderDetail.orderLogs" :key="index">
					<span class="item_tit">{{ item.orderStateLog == 10 ? '创建时间：' : item.orderStateLog == 20 ? '付款时间：' 
					: item.orderStateLog == 25 ? `${item.logContent}时间：`: item.orderStateLog == 30
						? '发货时间：' : item.orderStateLog == 40 ? '完成时间：' : item.orderStateLog == 102 ? '定金支付时间：' : '取消时间：' }}</span>
					<span class="item_val">{{ item.logTime }}</span>
				</view>
			</block>
		</view>
		<view class="bottom_act flex_row_center_center" v-if="(orderDetail.orderState == 30 || orderDetail.orderState == 31) && orderDetail.isPickup == 1">
			<view class="btn" @click="showDig">确认核销</view>
		</view>
		<view class="bottom_acts" v-if="orderDetail.orderState == 20 && orderDetail.isPickup == 2">
			<view class="btnType1" @click="cancelPopup">
				取消订单
			</view>
			<view class="btnType2" @click="receiveOrder">
				接单
			</view>
		</view>
		<uni-popup ref="popup" type="dialog" :mask-click="false" class="popupDig">
			<uni-popup-dialog title="提示" content="确认核销当前订单吗？" :duration="2000" :before-close="true" @close="close"
				@confirm="confirm"></uni-popup-dialog>
		</uni-popup>
		<uni-popup ref="cancelPopup" type="bottom">
			<view class="cancel_popup">
				<view class="popup_top">
					<text>{{ $L('取消原因') }}</text>
					<image :src="imgUrl + 'order-detail/guanbi.png'" mode="aspectFit" @click="notCancel"></image>
				</view>
				<scroll-view class="uni-list cancel_list" scroll-y="true">
					<radio-group @change="radioChange">
						<label class="cancle_pre" v-for="(item, index) in cancelList" :key="index">
							<text>{{ item.content }}</text>
							<radio :value="item.reasonId" :checked="item.reasonId == reasonId"
								color="var(--color_main)" style="transform:scale(0.8);margin-right:0;" />
						</label>
					</radio-group>
				</scroll-view>
				<view class="cancel_popup_btn">
					<text class="" @click="notCancel()">{{ $L('暂不取消') }}</text>
					<text class="" @click="confirmCancel()">{{ $L('确定取消') }}</text>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import uniPopup from '@/components/uni-popup/uni-popup.vue'
	import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue'
	export default {
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				orderSn: '',
				orderDetail: {}, //订单详情
				imgUrl: getApp().globalData.imgUrl,
				reasonId: -1, //取消原因当前点击的原因id
				cancelList: [], //取消原因列表
			}
		},
		components: {
			uniPopup,
			uniPopupDialog
		},
		onShow() {
			this.orderSn = this.$Route.query.orderSn
			this.getOrderDetail()
		},
		methods: {
			receiveOrder() {
				let that = this;
				let param = {};
				param.url = 'v3/business/seller/orderInfo/acquiring';
				param.method = 'POST';
				param.data = {};
				param.data.orderSn = that.orderSn;
				that.$seller_request(param).then(res => {
					if (res.state == 200) {
						that.$api.msg(res.msg);
						that.getOrderDetail();
					} else {
						that.$api.msg(res.msg);
					}
				}).catch((e) => {
					//异常处理
				})
			},
			//确定取消订单
			confirmCancel() {
				let that = this;
				uni.showModal({
					title: '提示',
					content: '确定取消该订单?',
					confirmColor: this.diyStyle_var['--color_main'],
					success: function(res) {
						if (res.confirm) {
							let param = {};
							param.url = 'v3/business/seller/orderInfo/cancel';
							param.method = 'POST';
							param.data = {};
							param.data.orderSn = that.orderSn;
							param.data.reasonId = that.reasonId;
							that.$seller_request(param).then(res => {
								if (res.state == 200) {
									that.$api.msg(res.msg);
									that.$refs.cancelPopup.close();
									that.getOrderDetail();
								} else {
									that.$api.msg(res.msg);
								}
							}).catch((e) => {
								//异常处理
							})
						} else if (res.cancel) {
							that.$refs.cancelPopup.close();
						}
					}
				})
			},
			//取消原因单选框切换
			radioChange(e) {
				this.reasonId = e.detail.value
			},
			notCancel() {
				this.$refs.cancelPopup.close();
			},
			//打开取消订单弹框
			cancelPopup() {
				this.$refs.cancelPopup.open();
				this.getCancelList();
			},
			callPhone(phoneNumber) {
				uni.makePhoneCall({
					phoneNumber,
				});
			},
			//获取取消订单原因列表
			getCancelList() {
				let param = {};
				param.url = 'v3/system/front/reason/list';
				param.method = 'GET';
				param.data = {};
				param.data.type = 104;
				this.$request(param).then(res => {
					if (res.state == 200) {
						this.cancelList = res.data || [];
						this.cancelList && this.cancelList.map((item, index) => item.value = '' + index);
						this.reasonId = this.cancelList[0].reasonId;
					} else {
						this.$api.msg(res.msg);
					}
				}).catch((e) => {
					//异常处理
				})
			},
			getOrderDetail() {
				// uni.showLoading({
				// 	title: '加载中'
				// })
				let params = {}
				params.url = 'v3/business/seller/orderInfo/detail'
				params.data = {
					orderSn: this.orderSn
				}
				this.$seller_request(params).then(res => {
					// uni.hideLoading()
					if (res.state == 200) {
						this.orderDetail = res.data
					} else {
						this.$api.msg(res.msg)
					}
				})
			},
			showDig() {
				this.$refs.popup.open()
			},
			close() {
				this.$refs.popup.close()
			},
			// 确认核销
			confirm() {
				uni.showLoading({
					title: '加载中'
				})
				let params = {}
				params.url = 'v3/business/seller/orderInfo/writeOff'
				params.method = 'POST'
				params.data = {
					orderSn: this.orderSn
				}
				this.$seller_request(params).then(res => {
					uni.hideLoading()
					setTimeout(() => {
						this.$api.msg(res.msg)
					}, 800)
					if (res.state == 200) {
						this.close()
						this.getOrderDetail()
					}
				})
			},
		}
	}
</script>

<style lang='scss' scoped>
	page {
		background-color: #F6F6F6;
	}

	.container {
		font-size: 28rpx;
		padding: 0 12rpx;
		height: 100%;
		/* #ifdef MP */
		height: 100vh;
		/* #endif */
		
		padding-bottom: 152rpx;
		background-color: #F6F6F6;
		overflow-y: auto;
		
		.order_state {
			height: 148rpx;
			color: #4F4F4F;
			font-weight: bold;
			font-size: 40rpx;

			image {
				width: 56rpx;
				height: 56rpx;
				margin-right: 14rpx;
			}
		}

		.code_box {
			background-color: #fff;
			border-radius: 10rpx;
			height: 104rpx;
			color: #101010;
			font-size: 28rpx;
			font-weight: bold;
		}

		.goods_box {
			background-color: #fff;
			border-radius: 10rpx;
			padding: 34rpx;
			color: #4F4F4F;
			font-size: 28rpx;
			margin-top: 14rpx;

			.goods_item {
				display: flex;

				&:not(:last-child) {
					margin-bottom: 12rpx;
				}

				.left {
					image {
						width: 140rpx;
						height: 140rpx;
						border-radius: 6rpx;
					}
				}

				.right {
					flex: 1;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					margin-left: 26rpx;

					.price {}

					.name {}

					.item_num {
						margin-right: 10rpx;
					}

					.size {
						color: #9A9A9A;
						font-size: 24rpx;
						margin-top: 8rpx;
					}
				}
			}
		}

		.price_box {
			background-color: #fff;
			border-radius: 10rpx;
			margin-top: 14rpx;
			padding: 18rpx 38rpx;
			color: #333333;
			font-size: 28rpx;

			.price_item {
				display: flex;
				justify-content: space-between;
				align-items: center;

				&:not(:last-child) {
					margin-bottom: 20rpx;
				}

				.right {
					color: #000000;
					font-weight: bold;
				}
			}
		}

		.info_box {
			background-color: #fff;
			border-radius: 10rpx;
			margin-top: 14rpx;
			padding: 28rpx 38rpx;
			color: #000000;
			font-size: 28rpx;

			.tit {
				font-weight: bold;
				margin-bottom: 34rpx;
			}

			.info_item {
				display: flex;
				align-items: center;

				&:not(:last-child) {
					margin-bottom: 16rpx;
				}

				.item_tit {
					color: #9A9A9A;
				}
			}
		}
		.bottom_acts{
			display: flex;
			justify-content: space-between;
			padding: 24rpx 34rpx 0;
			background-color: #fff;
			position: fixed;
			height: 152rpx;
			left: 0;
			bottom: 0;
			width: 100%;
			.btnType1{
				color: #000000;
				border: 2rpx solid #BEBEBE;
				width: 320rpx;
				height: 84rpx;
				line-height: 84rpx;
				text-align: center;
				font-size: 28rpx;
			}
			.btnType2{
				color: #fff;
				border: 2rpx solid #3C78E9;
				background: #3C78E9;
				width: 320rpx;
				height: 84rpx;
				line-height: 84rpx;
				text-align: center;
				font-size: 28rpx;
			}
		}
		.bottom_act {
			position: fixed;
			height: 152rpx;
			left: 0;
			bottom: 0;
			width: 100%;
			background-color: #fff;

			.btn {
				width: 668rpx;
				height: 84rpx;
				line-height: 84rpx;
				border-radius: 4rpx;
				background-color: rgba(60, 120, 233, 1);
				color: #fff;
				font-size: 28rpx;
				text-align: center;
			}
		}
	}
	
	::v-deep.popupDig {
		.uni-popup-dialog {
			width: 444rpx;
		}
		.uni-dialog-title {
			padding-top: 28rpx;
			padding-bottom: 28rpx;
			
		}
		.uni-dialog-title-text {
			font-size: 28rpx;
		}
		.uni-dialog-content {
			padding-bottom: 30rpx;
		}
		.uni-dialog-content-text {
			font-size: 24rpx;
		}
		.uni-dialog-button-group,
		.uni-border-left{
			border: none;
		}
		.uni-dialog-button-text {
			color: #3C78E9;
		}
		.uni-transition {
			z-index: 9999;
		}
	}
	.pick_detail {
		background-color: #fff;
		padding: 36rpx 32rpx 32rpx 30rpx;
		margin-bottom: 22rpx;
		border-radius: 10rpx;
		color: #101010;
		.pd_left{
			flex: 1;
			display: flex;
			align-items: center;
			font-size: 32rpx;
			image{
				width: 96rpx;
				height: 96rpx;
				border-radius: 50%;
				margin-right: 24rpx;
			}
		}
		.pd_right{
			width: 36rpx;
			height: 36rpx;
		}
		
	}
	.pick_address{
		color: #101010;
		padding: 28rpx;
		background-color: #fff;
		border-radius: 10rpx;
		.pa_top{
			font-weight: bold;
			font-size: 32rpx;
		}
		.pa_bot{
			margin-top: 20rpx;
			font-size: 28rpx;
		}
	}
	.cancel_popup {
		width: 100%;
		height: 700rpx;
		background: #FFFFFF;
		border-radius: 15rpx 15rpx 0 0;
		position: fixed;
		width: 100% !important;
		z-index: 20;
		bottom: 0;
	
		.popup_top {
			height: 100rpx;
			width: 100%;
			display: flex;
			padding: 0 39rpx;
			align-items: center;
			justify-content: space-between;
			border-bottom: 1rpx solid #F8F8F8;
	
			text {
				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #343434;
				line-height: 32rpx;
			}
	
			image {
				width: 30rpx;
				height: 30rpx;
			}
		}
	
		.cancel_list {
			// padding-bottom: 128rpx;
			box-sizing: border-box;
			height: 468rpx;
			z-index: 150;
	
			.cancle_pre {
				width: 100%;
				padding: 29rpx 40rpx;
				box-sizing: border-box;
				display: flex;
				justify-content: space-between;
	
				text {
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #666666;
					line-height: 32rpx;
				}
			}
		}
	
		.cancel_popup_btn {
			position: fixed;
			bottom: 40rpx;
			z-index: 30;
			display: flex;
			width: 100%;
			justify-content: center;
	
			text:nth-child(1) {
				width: 334rpx;
				height: 70rpx;
				background: var(--color_vice);
				border-radius: 35rpx 0 0 35rpx;
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #FFFFFF;
				line-height: 32rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
	
			text:nth-child(2) {
				width: 334rpx;
				height: 70rpx;
				background: var(--color_main);
				border-radius: 0 35rpx 35rpx 0;
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #FFFFFF;
				line-height: 32rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}
</style>