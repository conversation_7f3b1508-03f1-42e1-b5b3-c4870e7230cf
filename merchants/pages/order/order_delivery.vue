<template>
	<view class="container">
		<view class="goods_box">
			<view class="tit">商品信息</view>
			<view class="goods_item" v-for="(item, index) in orderDetail.orderProductList" :key="index">
				<view class="left">
					<image :src="item.productImage" mode=""></image>
				</view>
				<view class="right">
					<view class="">
						<view class="name">{{ item.goodsName }}</view>
						<view class="size">
							<text class="item_num">数量：{{item.productNum}}</text>
							<text v-if="item.specValues">规格：{{ item.specValues }}</text>
						</view>
					</view>
					<view class="price">￥{{ item.productShowPrice.toFixed(2) }}</view>
				</view>
			</view>
		</view>
		<view class="receive_info">
			<view class="tit">收货信息</view>
			<view class="receive_item">
				<text class="left" style="margin-right: 22rpx;">{{ orderDetail.receiverName }}</text>
				<text class="right">{{ orderDetail.receiverMobile }}</text>
			</view>
			<view class="receive_item">
				<text class="left">{{ orderDetail.receiverAreaInfo }}{{ orderDetail.receiverAddress }}</text>
			</view>
		</view>
		<view class="info_box">
			<view class="tit">发货</view>
			<view class="info_item" style="margin-bottom: 60rpx;">
				<view class="item_tit">发货方式：</view>
				<view class="item_val">
					<view class="item_val_list flex_row_start_center">
						<view @click="radioChange(item)" class="item_val_item flex_row_start_center" v-for="(item, index) in radioList" :key="item.val">
							<image v-if="item.val != sendType" :src="imgUrl + 'business/no_check_icon.png'" mode=""></image>
							<image v-else :src="imgUrl + 'business/already_check_icon.png'" mode=""></image>
							<text>{{item.key}}</text>
						</view>
					</view>
				</view>
			</view>
			<block v-if="sendType != '3'">
				<view class="info_item flex_row_btween_center">
					<text class="item_tit">物流公司：</text>
					<text class="item_val">
						<block v-if="sendType == 1">
							<picker @change="bindPickerChange($event, 1)" :value="index" :range="expressList" range-key="expressName">
								<view class="choose_delivery flex_row_end_center" :class="delivery.expressName ? 'text_color' : ''">
									{{ delivery.expressName || '请选择物流公司' }}
								</view>
							</picker>
						</block>
						<block v-else>
							<picker @change="bindPickerChange($event, 2)" :value="sheetIndex" :range="expressSheetList" range-key="expressName">
								<view class="choose_delivery flex_row_end_center" :class="deliverySheet.expressName ? 'text_color' : ''">
									{{ deliverySheet.expressName || '请选择物流公司' }}
								</view>
							</picker>
						</block>
				
					</text>
				</view>
				<view class="info_item">
					<text class="item_tit">物流单号：</text>
					<text class="item_val">
						<input class="item_val_input" v-model="expressNumber" placeholder-style="color: #BEBEBE" type="number" placeholder="请输入物流单号" />
					</text>
				</view>
			</block>
			<block v-else>
				<view class="info_item">
					<text class="item_tit">联系人：</text>
					<text class="item_val">
						<input class="item_val_input" v-model="deliverName" placeholder-style="color: #BEBEBE" type="text" placeholder="请输入联系人" />
					</text>
				</view>
				<view class="info_item">
					<text class="item_tit">联系方式：</text>
					<text class="item_val">
						<input class="item_val_input" v-model="deliverMobile" placeholder-style="color: #BEBEBE" type="number" placeholder="请输入联系方式" />
					</text>
				</view>
			</block>
		</view>
		<view class="bottom_act flex_row_center_center">
			<view class="btn" @click="toDeliver">确认发货</view>
		</view>
		
		<uni-popup ref="popup" type="dialog" :mask-click="false" class="popupDig">
			<uni-popup-dialog title="" content="确认发货吗？" :duration="2000" :before-close="true" @close="close"
				@confirm="submit"></uni-popup-dialog>
		</uni-popup>
	</view>
</template>

<script>
	import uniPopup from '@/components/uni-popup/uni-popup.vue'
	import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue'
	export default {
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				sendType: '1',
				radioList: [{
						key: '物流发货',
						val: '1',
					},
					{
						key: '电子面单',
						val: '2',
					},
					{
						key: '商家自送',
						val: '3',
					}
				],
				index: 0,
				sheetIndex: 0,
				delivery: {},
				deliverySheet: {},
				orderSn: '',
				orderDetail: {}, //订单详情
				expressList: [], //物流列表
				expressSheetList: [], //支持电子面单的物流列表
				expressNumber: '',
				deliverMobile: '',
				deliverName: '',
			}
		},
		components: {
			uniPopup,
			uniPopupDialog
		},
		onShow() {

		},
		onLoad() {
			this.orderSn = this.$Route.query.orderSn
			this.getOrderDetail()
			this.getExpressList()
		},
		methods: {
			getOrderDetail() {
				uni.showLoading({
					title: '加载中'
				})
				let params = {}
				params.url = 'v3/business/seller/orderInfo/detail'
				params.data = {
					orderSn: this.orderSn
				}
				this.$seller_request(params).then(res => {
					uni.hideLoading()
					if (res.state == 200) {
						this.orderDetail = res.data
					} else {
						this.$api.msg(res.msg)
					}
				})
			},
			// 获取物流公司
			getExpressList() {
				let params = {}
				params.url = 'v3/seller/seller/express/list'
				params.data = {
					pageSize: 10000,
					expressState: 1
				}
				this.$seller_request(params).then(res => {
					if (res.state == 200) {
						this.expressList = res.data.list
						this.expressSheetList = res.data.list.filter(v => v.isSupportFaceSheet == 1)
					} else {
						this.$api.msg(res.msg)
					}
				})
			},
			bindPickerChange(e, type) {
				if (type == 1) {
					this.index = e.detail.value
					this.delivery = this.expressList[e.detail.value]
				} else {
					this.sheetIndex = e.detail.value
					this.deliverySheet = this.expressSheetList[e.detail.value]
				}
				
				
			},
			radioChange(item) {
				this.sendType = item.val
			},
			// 发货
			toDeliver() {
				if (this.sendType == '1' && !this.delivery.expressId) return this.$api.msg('请选择物流公司')
				if (this.sendType == '2' && !this.deliverySheet.expressId) return this.$api.msg('请选择物流公司')
				if (this.sendType != '3' && !this.expressNumber) return this.$api.msg('请输入物流单号')
				if (this.sendType == '3' && !this.deliverName) return this.$api.msg('请输入联系人')
				if (this.sendType == '3' && !this.deliverMobile) return this.$api.msg('请输入联系方式')
				if (this.sendType == '3' && !this.$checkMobile(this.deliverMobile)) return
				this.$refs.popup.open()
			},
			close() {
				this.$refs.popup.close()
			},
			submit() {
				let params = {}
				params.url = 'v3/business/seller/orderInfo/deliver'
				params.method = 'POST'
				params.data = {
					orderSn: this.orderSn,
				}
				if (this.sendType == '1') {
					params.data.deliverType = 1
					params.data.expressId = this.delivery.expressId
					params.data.expressNumber = this.expressNumber
					
				} else if (this.sendType == '2') {
					params.data.deliverType = 3
					params.data.expressId = this.deliverySheet.expressId
					params.data.expressNumber = this.expressNumber
				} else {
					params.data.deliverType = 2
					params.data.deliverMobile = this.deliverMobile
					params.data.deliverName = this.deliverName
				}
				this.$seller_request(params).then(res => {
					this.$api.msg(res.msg)
					if (res.state == 200) {
						this.close()
						setTimeout(() => {
							this.$Router.push('/merchants/pages/order/order')
						}, 800)
					}
				})
			},
		}
	}
</script>

<style lang='scss' scoped>
	page {
		background-color: #F6F6F6;
		height: 100%;
	}

	.container {
		font-size: 28rpx;
		padding: 0 12rpx;
		height: 100%;
		padding-bottom: 140rpx;
		overflow-y: auto;

		.goods_box {
			background-color: #fff;
			border-radius: 10rpx;
			padding: 34rpx;
			color: #4F4F4F;
			font-size: 28rpx;
			margin-top: 14rpx;

			.tit {
				font-weight: bold;
				margin-bottom: 34rpx;
			}

			.goods_item {
				display: flex;

				&:not(:last-child) {
					margin-bottom: 12rpx;
				}

				.left {
					image {
						width: 140rpx;
						height: 140rpx;
						border-radius: 6rpx;
					}
				}

				.right {
					flex: 1;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					margin-left: 26rpx;

					.price {}

					.name {
						text-overflow: -o-ellipsis-lastline;
						overflow: hidden; //溢出内容隐藏
						text-overflow: ellipsis; //文本溢出部分用省略号表示
						display: -webkit-box; //特别显示模式
						-webkit-line-clamp: 2; //行数
						line-clamp: 2;
						-webkit-box-orient: vertical; //盒子中内容竖直排列

					}

					.item_num {
						margin-right: 10rpx;
					}

					.size {
						color: #9A9A9A;
						font-size: 24rpx;
						margin-top: 8rpx;
					}
				}
			}
		}

		.receive_info {
			background-color: #fff;
			border-radius: 10rpx;
			margin-top: 14rpx;
			padding: 18rpx 38rpx;
			padding-bottom: 40rpx;
			color: #333333;
			font-size: 28rpx;

			.tit {
				font-weight: bold;
				margin-bottom: 34rpx;
			}

			.receive_item {
				display: flex;
				align-items: center;

				&:not(:last-child) {
					margin-bottom: 20rpx;
				}
			}
		}

		.info_box {
			background-color: #fff;
			border-radius: 10rpx;
			margin-top: 14rpx;
			padding: 28rpx 38rpx;
			color: #000000;
			font-size: 28rpx;

			.tit {
				font-weight: bold;
				margin-bottom: 34rpx;
			}

			.info_item {
				display: flex;
				align-items: center;

				&:not(:last-child) {
					margin-bottom: 16rpx;
				}

				.item_tit {
					color: #000000;
				}

				.item_val {
					flex: 1;
					
					.item_val_list {
						
						.item_val_item {
							margin-right: 38rpx;
							&:last-child {
								margin-right: 0;
							}
							image {
								width: 30rpx;
								height: 30rpx;
							}
							text {
								color: #101010;
								font-size: 24rpx;
								margin-left: 16rpx;
							}
						}
					}

					::v-deep uni-radio .uni-radio-input {
						width: 30rpx;
						height: 30rpx;
						margin-bottom: 2rpx;

					}

					::v-deep uni-radio .uni-radio-input.uni-radio-input-checked:before {
						font-size: 30rpx;
					}

					input {
						height: 68rpx;
						width: 100%;
						text-align: right;
						padding-right: 20rpx;
						color: #BEBEBE;
					}

					.choose_delivery {
						height: 68rpx;
						width: 100%;
						text-align: right;
						padding-right: 20rpx;
						color: #BEBEBE;
						font-size: 28rpx;
					}
					.text_color {
						color: #101010;
					}
					
					.item_val_input {
						font-size: 28rpx;
						color: #101010;
					}
				}
			}
		}

		.bottom_act {
			margin-top: 30rpx;

			.btn {
				width: 668rpx;
				height: 84rpx;
				line-height: 84rpx;
				border-radius: 4rpx;
				background-color: rgba(60, 120, 233, 1);
				color: #fff;
				font-size: 28rpx;
				text-align: center;
			}
		}
	}
	
	::v-deep.popupDig {
		.uni-popup-dialog {
			width: 444rpx;
		}
		.uni-dialog-title {
			padding-top: 28rpx;
			padding-bottom: 28rpx;
			
		}
		.uni-dialog-title-text {
			font-size: 28rpx;
		}
		.uni-dialog-content {
			padding-bottom: 30rpx;
		}
		.uni-dialog-content-text {
			font-size: 24rpx;
		}
		.uni-dialog-button-group,
		.uni-border-left{
			border: none;
		}
		.uni-dialog-button-text {
			color: #3C78E9;
		}
		.uni-transition {
			z-index: 9999;
		}
	}
</style>