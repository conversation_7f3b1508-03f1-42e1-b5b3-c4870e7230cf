<template>
	<!-- 从订单详情进入的话显示订单信息start -->
	<view class="send_uri" @click="goOrderDetail">
		<view class="record_text_type">
			{{ msgContent.orderStateValue }}
		</view>
		<view class="record_type_order_info">
			<view class="record_order">
				{{ $L('订单号:') }}{{ msgContent.orderSn }}
			</view>
			<view class="record_order_time">
				<view>{{ msgContent.createTime.slice(5, 16) }}</view>
			</view>
		</view>
		<view class="record_type_order_content">
			<view class="record_order_image">
				<image :src="msgContent.orderProductList[0].goodsImage"
					mode="aspectFit">
				</image>
			</view>
			<view class="record_order_con">
				<view class="record_order_name">
					{{ msgContent.orderProductList[0].goodsName }}
				</view>
				<view class="record_price_and_status">
					<view class="record_order_price">
						{{ $L('￥') }}{{ msgContent.orderProductList[0].goodsPrice }}
					</view>
					<view class="record_order_status handel_send_url"
						@click.stop="sendOrder">{{
							$L('发送链接') }}
					</view>
				</view>
			</view>
		</view>
	</view>
	<!-- 发送链接end -->
</template>

<script>
	export default{
		props:{
			msgContent:Object
		},
		
		methods:{
			sendOrder(){
				this.$emit('sendOrder',this.msgContent, '', 0)
			},
			
			//进入订单详情页 type为1，说明需要把json字符串转为对象，为空则直接使用
			goOrderDetail() {
				this.$Router.push({
					path: '/pages/order/detail',
					query: {
						orderSn:this.msgContent.orderSn
					}
				})
			},
		}
	}
</script>

<style lang="scss">
	/* 发送链接start */
	.send_uri {
		width: 660rpx;
		height: 260rpx;
		background: #ffffff;
		border-radius: 6rpx;
		margin: 30rpx auto;
		position: relative;
		box-sizing: border-box;
	
		.record_text_type {
			/* width: 82rpx; */
			height: 32rpx;
			font-size: 24rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #999999;
			position: absolute;
			left: 0rpx;
			bottom: 0rpx;
			background: #ededed;
			border-radius: 0 5px 0 0;
			z-index: 2;
		}
	
		.record_type_order_info {
			width: 100%;
			height: 63rpx;
			display: flex;
			font-size: 24rpx;
			line-height: 63rpx;
			position: relative;
			justify-content: space-around;
			color: #949494;
	
			.record_order {
				width: 50%;
				box-sizing: border-box;
				padding-left: 20rpx;
				/* font-size: 24rpx; */
			}
	
			.record_order_time {
				width: 50%;
				transform: translate(-20rpx, 0);
	
				display: flex;
				justify-content: flex-end;
			}
		}
	
		.record_type_order_info::before {
			content: '';
			display: inline-block;
			width: 97%;
			height: 1rpx;
			border-top: 1rpx solid #f3f3f3;
			position: absolute;
			bottom: 0;
			left: 0;
		}
	
		.record_type_order_content {
			width: 100%;
			display: flex;
			justify-content: space-around;
			box-sizing: border-box;
			padding-top: 20rpx;
	
			.record_order_image {
				width: 156rpx;
				height: 156rpx;
				border-radius: 10rpx !important;
	
				image {
					width: 156rpx;
					height: 156rpx;
				}
			}
	
			.record_order_con {
				width: 453rpx;
				/* height: 64rpx; */
				font-size: 26rpx;
				padding-top: 19rpx;
				margin-left: -20px;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
	
				.record_order_name {
					width: 100% !important;
					font-size: 26rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #333333;
					line-height: 36rpx;
					text-overflow: -o-ellipsis-lastline;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					line-clamp: 2;
					-webkit-box-orient: vertical;
					width: 418rpx;
					padding-left: 20rpx;
					box-sizing: border-box;
				}
	
				.record_price_and_status {
					width: 100%;
					height: 70rpx;
					display: flex;
					justify-content: space-between;
					padding-top: 30rpx;
	
					.handel_send_url {
						width: 75px;
						background: var(--color_main_bg);
						border-radius: 19px;
						color: #ffffff;
					}
	
					.record_order_price {
						font-size: 26rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: var(--color_price);
						margin-bottom: 12rpx;
						padding-left: 20rpx;
					}
	
					.record_order_status {
						height: 35rpx;
						border-radius: 24rpx;
						font-size: 22rpx;
						font-family: PingFang SC;
						font-weight: 500;
						line-height: 35rpx;
						text-align: center;
						margin-left: 60rpx;
					}
				}
	
				.record_order_des {
					display: flex;
					margin: 10rpx 0 20rpx;
					padding-left: 20rpx;
					box-sizing: border-box;
	
					.record_order_ordersn {
						font-size: 24rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #949494;
					}
	
					.record_order_time {
						font-size: 24rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #949494;
						margin-left: 17rpx;
					}
				}
			}
	
			.record_order_status {
				width: 760rpx;
				height: 30rpx;
				background: #f0f2f5;
				border-radius: 24rpx;
				font-size: 22rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #949494;
				line-height: 30rpx;
				text-align: center;
				margin-left: 20rpx;
			}
		}
	}
	
	/* 发送链接结束 */
</style>