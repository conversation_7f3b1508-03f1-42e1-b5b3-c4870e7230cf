<!-- 聊天界面 -->
<template>
	<view :style="mix_diyStyle">
		<view class="chat_interface" :style="{height:windowHeight+'px'}">
			<!-- 聊天信息 start -->
			<view class="chat_con">

				<!-- 聊天框 start -->
				<view class="chat_info_con">
					<view class="chat_info">
						<input type="text" :style="{ width: inputVal ? '526rpx' : '578rpx' }" class="chat_info_input"
							v-model="inputVal" :placeholder="$L('请输入您要咨询的问题')"
							placeholder-style="font-size:26rpx;color:#999;font-weight:400" confirm-type="send"
							@confirm="send" @focus="inputFocus" :cursor-spacing="cursors" @blur="inputBlur"
							:enableNative="false" :adjust-position="true" />


						<!-- <image :src="imgUrl + 'chat/expression.png'" mode="aspectFit" class="chat_info_expression"
						@click="moreOptions(1)"></image> -->

						<view @click="moreOptions(1)">
							<svgGroup type="expression" class="chat_info_options" width="50" height="50" px="rpx"
								:color="diyStyle_var['--color_main']">
							</svgGroup>
						</view>


						<div type="default" class="send_button" @touchend.prevent="send" v-if="inputVal">
							{{ $L('发送') }}
						</div>

						<view @click="moreOptions(0)" v-else>
							<svgGroup :type="optionPic" class="chat_info_options" width="46" height="46" px="rpx"
								:color="diyStyle_var['--color_main']">
							</svgGroup>
						</view>
					</view>


					<!-- 更多操作 start -->
					<view :class="{ info_options: true }" v-if="moreOptionsModel">
						<block v-if="modelFlag == 0">
							<view class="info_options_pre" @click="sendPic(0)">
								<view class="tpe">
									<image :src="imgUrl + 'chat/album.png'" mode="aspectFit"></image>
								</view>
								<text>{{ $L('相册') }}</text>
							</view>
							<view class="info_options_pre" @click="sendPic(1)">
								<view class="tpe">
									<image :src="imgUrl + 'chat/shot.png'" mode="aspectFit"></image>
								</view>
								<text>{{ $L('拍摄') }}</text>
							</view>
							<view class="info_options_pre" @click="openMoreListModel(0)">
								<view class="tpe">
									<image :src="imgUrl + 'chat/order.png'" mode="aspectFit"></image>
								</view>
								<text>{{ $L('订单') }}</text>
							</view>
							<view class="info_options_pre" @click="openMoreListModel(1)">
								<view class="tpe">
									<image :src="imgUrl + 'chat/footprint.png'" mode="aspectFit"></image>
								</view>
								<text>{{ $L('足迹') }}</text>
							</view>
							<view class="info_options_pre" @click="openMoreListModel(2)" >
								<view class="tpe">
									<image :src="imgUrl + 'chat/recommend.png'" mode="aspectFit"></image>
								</view>
								<text>{{ $L('推荐') }}</text>
							</view>
<!-- 							<view class="info_options_pre" @click="commonProblem">
								<view class="tpe">
									<image :src="imgUrl + 'chat/common_problem.png'" mode="aspectFit"></image>
								</view>
								<text>{{ $L('常见问题') }}</text>
							</view> -->
						</block>
						<block v-if="modelFlag == 1">
							<view class="emoji_item" v-for="(item, index) in emoji" :key="index"
								@click="insertEmoji(item)">
								<img :src="emojiPath + '' + item.src" alt="" />
							</view>
						</block>
					</view>
					<!-- 更多操作 end -->
				</view>
				<!-- 聊天框 end -->

				<!-- 聊天框 -->
				<view class="chat_container chat_des">
					<scroll-view scroll-y @scrolltolower="chatLogToTop" :scroll-top="scrollTop"
						:style="{maxHeight: scroller_maxHeight}" @touchmove="touchscroll" @scroll="scrollEvent"
						@touchstart="touchstart" class="chat_scroller">
						<!-- 聊天记录 start -->
						<view class="chat_record">
							<view v-for="(item, index) in msgList" :key="item.msgId" :id="'item' + index"
								class="msg_item">

								<!-- 商品链接 start -->
								<block v-if="item.msgType == 'main_goods'">
									<goodsLink :msgContent="item.msgContent" @sendGoods="sendGoods"></goodsLink>
								</block>
								<!-- 商品链接 start -->

								<!-- 从订单详情进入的话显示订单信息start -->
								<block v-else-if="item.msgType == 'main_order'">
									<orderLink :msgContent="item.msgContent" @sendOrder="sendOrder"></orderLink>
								</block>
								<!-- 发送链接end -->

								<template v-else>
									<!-- 聊天时间  start-->
									<view
										v-if="index == msgList.length-1 || (index < msgList.length-1 && $isShowTime(index < msgList.length-1 ? msgList[index + 1].addTime : '',item.addTime))"
										class="chat_info_time">
										<text>{{ $formatChatTime(item.addTime) }}</text>
									</view>
									<!-- 聊天时间  end-->

									<view :class="item.userType == 1 ? 'customer_service_info' : 'user_info_record'">
										<view class="customer_service_avatar"
											v-if="item.userType == 1">
											<view :style="{ backgroundImage: 'url(' + item.memberAvatar + ')' }"
												class="image"></view>
										</view>

										<text v-if="item.userType == 2"
											:class="{ msg_state: true, read: item.msgState == 1 }">{{ item.msgState == 1 ?
											$L('已读') : $L('未读') }}</text>
										<view :class="item.userType == 1 ? 'customer_record' : 'user_record'">
											<!-- 文本类型 start -->
											<view v-if="item.msgType == 1" class="record_type_text">
												<jyfParser :isAll="true" :html="JSON.parse(item.msgContent).content">
												</jyfParser>
											</view>
											<!-- 文本类型 end -->

											<!-- 图片类型 start -->
											<view v-if="item.msgType == 2" class="record_type_image">
												<!-- 宽 > 长 -->
												<view class="img_con"
													:style="{ width: '300rpx', height: (JSON.parse(item.msgContent).height / JSON.parse(item.msgContent).width) * 300 + 'rpx' }"
													v-if="JSON.parse(item.msgContent).width > 300">
													<view class="unload_con flex_row_center_center" v-if="!item.loaded">
														<image :src="imgUrl + 'svideo/page_loading_icon.gif'"
															mode="aspectFit">
														</image>
													</view>

													<image :src="JSON.parse(item.msgContent).pic" mode="aspectFit"
														class="record_type_image_width"
														@click="TanPreviewImage(item.msgContent)"
														@error="imageError(item, index)" @load="imageLoad(item)">
													</image>
												</view>

												<view class="img_con"
													:style="{ width: JSON.parse(item.msgContent).width + 'rpx', height: JSON.parse(item.msgContent).height + 'rpx' }"
													v-else>
													<view class="unload_con flex_row_center_center" v-if="!item.loaded">
														<image :src="imgUrl + 'svideo/page_loading_icon.gif'"
															mode="aspectFit">
														</image>
													</view>
													<image :src="JSON.parse(item.msgContent).pic" mode="aspectFit"
														class="record_type_image_width"
														@click="TanPreviewImage(item.msgContent)"
														@error="imageError(item, index)" @load="imageLoad(item)">
													</image>
												</view>
											</view>
											<!-- 图片类型 end -->

											<!-- 商品类型 start -->
											<view v-if="item.msgType == 3" class="good_type_order">
												<view class="record_type_order_content"
													@click="goGoodsDetail(item.msgContent, 1)">
													<view class="record_order_image">
														<image :src="JSON.parse(item.msgContent).goodsImage"
															mode="aspectFit">
														</image>
													</view>
													<view class="record_order_con">
														<view class="record_order_name">
															{{ JSON.parse(item.msgContent).goodsName }}
														</view>
														<view class="record_price_and_status">
															<view class="record_order_price">
																{{
																$L('￥') }}{{ parseFloat(JSON.parse(item.msgContent).goodsPrice).toFixed(2) }}
															</view>
														</view>
													</view>
												</view>
											</view>
											<!-- 商品类型 end -->
											<!-- 订单类型 start-->
											<view v-if="item.msgType == 4" class="record_type_order"
												@click="goOrderDetail(item.msgContent, 1)">
												<view class="record_type_order_info">
													<view class="record_order">
														{{ $L('订单号:')
													}}{{ JSON.parse(item.msgContent).orderSn }}
													</view>
													<view class="record_order_time">
														<view>{{
														JSON.parse(item.msgContent).createTime.slice(5, 16)
													}}</view>
													</view>
												</view>
												<view class="record_type_order_content">
													<view class="record_order_image">
														<image :src="JSON.parse(item.msgContent).goodsImage"
															mode="aspectFit">
														</image>
													</view>
													<view class="record_order_con">
														<view class="record_order_name">
															{{ JSON.parse(item.msgContent).goodsName }}&nbsp;{{
															JSON.parse(item.msgContent).specValues
														}}
														</view>
														<view class="record_price_and_status">
															<view class="record_order_price">
																{{ $L('￥')
															}}{{ JSON.parse(item.msgContent).goodsPrice }}
															</view>
															<view class="record_order_status">
																{{ JSON.parse(item.msgContent).orderStateValue }}
															</view>
														</view>
													</view>
												</view>
											</view>
											<!-- 订单类型 end -->
										</view>

										<view class="user_info_avatar"
											v-if="item.userType == 2">
											<image :src="storeDetail.storeLogoUrl" mode="aspectFill"></image>
										</view>
									</view>
								</template>
							</view>

							<view class="loading_icon flex_row_center_center" v-show="isLoadIcon" id="loading">
								<image :src="imgUrl + 'svideo/page_loading_icon.gif'" mode="aspectFit"></image>
							</view>
						</view>
						<!-- 聊天记录 end -->
					</scroll-view>

				</view>


			</view>

			<!-- 常见问题弹框 start -->
			<uni-popup ref="commonProblemModel" type="bottom">
				<view class="common_problem">
					<view class="common_problem_title">
						<text>{{ $L('请选择您要咨询的问题') }}</text>
						<view class="common_problem_close" @click="closeModel">
							<image :src="imgUrl + 'chat/close.png'" mode="aspectFit"></image>
						</view>
					</view>
					<scroll-view scroll-y="true" v-if="commonProblemList.length > 0">
						<view class="common_problem_list">
							<view class="common_problem_pre" v-for="(com, comIndex) in commonProblemList"
								:key="comIndex" @click="sendProblem(com)">
								<view class="common_problem_text">{{ com.msgContent }}</view>
							</view>
						</view>
					</scroll-view>
					<view class="empty_page" v-if="!orderList.length > 0 && firstloadinglist.problem">
						<image :src="imgUrl + 'empty_goods.png'" mode="aspectFit"></image>
						<text>{{ $L('暂无常见问题') }}</text>
					</view>
				</view>
			</uni-popup>
			<!-- 常见问题弹框 end -->

			<!-- 订单，足迹，推荐 弹框 start -->
			<uni-popup ref="moreListModel" type="bottom">
				<view class="more_list_model">
					<view class="about_more_title">
						<view class="about_more_title_left" v-if="aboutMore && aboutMore.length > 0">
							<view class="about_more_title_pre" :class="{ current: item.aboutId == currentAboutId }"
								v-for="(item, index) in aboutMore" :key="index"
								@click="openMoreListModel(item.aboutId)">
								{{ item.title }}
							</view>
						</view>
						<view class="about_more_close" @click="closeModel">
							<image :src="imgUrl + 'chat/close.png'" mode="aspectFit"></image>
						</view>
					</view>
					<view class="about_more_con">
						<!-- 我的订单 start -->
						<view v-if="currentAboutId == 0">
							<scroll-view scroll-y="true" class="order_list_scroll"
								@scrolltolower="loadData(currentAboutId)" v-if="orderList.length > 0">
								<view class="about_more_list" v-for="(orderItem, orderIndex) in orderList"
									:key="orderIndex">
									<view class="about_more_pre">
										<view class="order_title">
											<text>{{ $L('订单号') }}:{{ orderItem.orderSn }}</text>
											<text>{{ orderItem.createTime }}</text>
										</view>
										<view class="order_list"
											v-for="(goodsItem, goodsIndex) in orderItem.orderProductList"
											:key="goodsIndex" @click="sendOrder(orderItem, 'bottom', goodsIndex)">
											<view class="order_list_pre" v-if="goodsIndex < orderItem.limit">
												<view class="order_pre_img">
													<image :src="goodsItem.productImage" mode="aspectFit">
													</image>
												</view>
												<view class="order_pre_des">
													<view class="order_pre_name">
														{{ goodsItem.goodsName }}&nbsp;{{
														goodsItem.specValues
													}}
													</view>
													<view class="order_pre_des_bot">
														<view class="order_pre_price_active order_pre_price">
															{{ $L('￥')
														}}{{ goodsItem.productShowPrice.toFixed(2) }}
														</view>
														<view :class="{
														order_status: true,
														order_status_awaits:
															orderItem.orderState == 20 ||
															orderItem.orderState == 10 ||
															orderItem.orderState == 30,
														order_status_await:
															orderItem.orderState == 50 ||
															orderItem.orderState == 0
													}">
															{{ orderItem.orderStateValue }}
														</view>

														<view class="order_pre_link"
															@click.stop="sendGoods(goodsItem, 'bottom_order')">
															{{ $L('发送商品链接') }} >
														</view>
													</view>
												</view>
											</view>
										</view>
										<view class="unfold_fold" @click.stop="unfold(orderItem)"
											v-if="orderItem.orderProductList.length > 2">
											<block>
												<text v-if="orderItem.isFold == true">{{
												$L('展开全部')
											}}</text>
												<text v-if="orderItem.isFold == false">{{
												$L('收起全部')
											}}</text>
												<image :src="imgUrl + 'chat/unfold.png'" mode="aspectFit"
													v-if="orderItem.isFold == true"></image>
												<image :src="imgUrl + 'chat/fold.png'" mode="aspectFit"
													v-if="orderItem.isFold == false"></image>
											</block>
										</view>
									</view>
								</view>
								<loadingState v-if="loadingState == 'first_loading' || orderList.length > 0"
									:state="loadingState" />
							</scroll-view>
							<view class="empty_page" v-if="!orderList.length > 0 && firstloadinglist.order">
								<image :src="imgUrl + 'empty_orders.png'" mode="aspectFit"></image>
								<text>{{ $L('暂无订单数据') }}</text>
							</view>
						</view>

						<!-- 我的订单 end -->
						<!-- 我的足迹 start -->
						<view v-if="currentAboutId == 1">
							<scroll-view scroll-y="true" class="footprint_list_scroll"
								@scrolltolower="loadData(currentAboutId)" v-if="footprint.length > 0">
								<view class="footprint_list" v-for="(foot, footIndex) in footprint" :key="footIndex">
									<view class="order_list_pre" @click="sendGoods(foot, 'footprint')">
										<view class="order_pre_img">
											<image :src="foot.goodsImage" mode="aspectFit"></image>
										</view>
										<view class="order_pre_des">
											<view class="order_pre_name">{{ foot.goodsName }}</view>
											<view class="order_pre_des_bot">
												<view class="order_pre_price order_pre_price_active">
													{{ $L('￥') }}{{ foot.productPrice.toFixed(2) }}
												</view>
												<view class="order_pre_link">{{ $L('发送商品链接') }} ></view>
											</view>
										</view>
									</view>
								</view>
								<loadingState v-if="loadingState == 'first_loading' || footprint.length > 0"
									:state="loadingState" />
							</scroll-view>
							<view class="empty_page" v-if="!footprint.length > 0 && firstloadinglist.foot">
								<image :src="imgUrl + 'empty_footprint.png'" mode="aspectFit"></image>
								<text>{{ $L('暂无足迹数据') }}</text>
							</view>
						</view>

						<!-- 我的足迹end-->
						<!-- 店铺推荐 start -->
						<view v-if="currentAboutId == 2">
							<scroll-view scroll-y="true" class="footprint_list_scroll" style="width: 710rpx"
								@scrolltolower="loadData(currentAboutId)" v-if="storeRecom.length > 0">
								<view class="footprint_list" v-for="(recom, recomIndex) in storeRecom"
									:key="recomIndex">
									<view class="order_list_pre" @click="sendGoods(recom, 'rec')">
										<view class="order_pre_img">
											<image :src="recom.mainImage" mode="aspectFit"></image>
										</view>
										<view class="order_pre_des">
											<view class="order_pre_name">{{ recom.goodsName }}</view>
											<view class="order_pre_des_bot">
												<view class="order_pre_price order_pre_price_active">
													{{ $L('￥')
												}}{{ parseFloat(recom.goodsPrice).toFixed(2) }}
												</view>
												<view class="order_pre_link">{{ $L('发送商品链接') }} ></view>
											</view>
										</view>
									</view>
								</view>
								<loadingState v-if="loadingState == 'first_loading' || storeRecom.length > 0"
									:state="loadingState" />
							</scroll-view>
							<view class="empty_page" v-if="!storeRecom.length > 0 && firstloadinglist.recom">
								<image :src="imgUrl + 'empty_goods.png'" mode="aspectFit"></image>
								<text>{{ $L('暂无商品数据') }}</text>
							</view>
						</view>
						<!-- 店铺推荐 end-->
					</view>
				</view>
			</uni-popup>
			<!-- 订单，足迹，推荐 弹框 end -->

			<loginPop ref="loginPop"></loginPop>
			<w-compress ref="wCompress" />

			<!-- 聊天信息 end -->
		</view>
	</view>
</template>

<script>
	import jyfParser from '@/components/jyf-parser/jyf-parser.vue'
	import loadingState from "@/components/loading-state.vue";
	import uniPopup from '@/components/uni-popup/uni-popup.vue';
	import io from '@hyoga/uni-socket.io';
	import WCompress from '../w-compress/w-compress.vue'
	import goodsLink from './components/goodsLink.vue'
	import orderLink from './components/orderLink.vue'
	import {
		emoji,
		emojiPath
	} from './emoji.js'
	import {
		mapState
	} from 'vuex';
	export default {
		components: {
			loadingState,
			uniPopup,
			jyfParser,
			WCompress,
			goodsLink,
			orderLink
		},
		data() {
			return {
				connectBaseData: {}, //每次通信必传信息
				socketInfo: '', //socket连接成功返回的房间信息
				imgUrl: getApp().globalData.imgUrl,
				uploadSize: getApp().globalData.uploadMaxSize,
				msgList: [], //消息列表
				moreOptionsModel: false, //聊天框 更多操作是否显示
				currentAboutId: 0, //订单，足迹，推荐，弹框，默认选中第一个（即订单）
				aboutMore: [{
					title: this.$L('用户订单'),
					aboutId: 0,
				}, {
					title: this.$L('用户足迹'),
					aboutId: 1,
				}, {
					title: this.$L('店铺推荐'),
					aboutId: 2,
				}],
				inputVal: '', //输入框内容,
				orderList: [],
				footprint: [],
				storeRecom: [],
				storeId: '',
				orderCurrent: 1,
				footCurrent: 1,
				storeCurrent: 1,
				toChatLogBottom: '', //聊天界面滚动到页面最底部
				minMsgId: '', //当前消息聊天记录列表里的最小消息id
				isLoadMoreChatTop: true, //页面滑到顶部是否加载更多数据，默认true，true为加载
				isFirstLoadingChatLog: true, //是否是第一次渲染聊天记录
				comCurrent: 1,
				commonProblemList: [],
				picture: '',
				optionPic: 'more_options',
				loadingState: '',
				firstloadinglist: {
					order: false,
					foot: false,
					recom: false,
					problem: false
				},
				modelFlag: -1,
				emoji,
				emojiPath,
				msgType: null,

				//上顶加载参数start
				isScrollTop: false,
				isLoadIcon: false,

				//一些滚动高度的值
				scrollTop: 0,
				scrollHeight: 0,
				height: 0,
				cursors: 30,
				//防止过度操作
				enableScroll: false,
				// #ifdef MP-ALIPAY
				aliPreScroll: true,
				// #endif
				curMsgId: '',
				windowHeight: uni.getSystemInfoSync().windowHeight,
				
				memberId: '',

			};
		},
		computed: {
			...mapState(['userInfo', 'chatBaseInfo', 'userCenterData', 'storeDetail']),
			scroller_maxHeight() {
				return 'calc(' + uni.getSystemInfoSync().windowHeight + 'px - 49px - env(safe-area-inset-bottom))'
			},
		},
		onLoad(options) {

			this.memberId = this.$Route.query.memberId
			
			this.initSocket();
			this.initChatLog();
			let sysInfo = uni.getSystemInfoSync()
			this.height = sysInfo.windowHeight
			if (this.chatBaseInfo.storeName) {
				setTimeout(() => {
					uni.setNavigationBarTitle({
						title: this.chatBaseInfo.storeName
					})
				}, 0);

			} else {
				setTimeout(() => {
					uni.setNavigationBarTitle({
						title: this.$L('聊天详情')
					});
				}, 0);

			}


			// #ifdef MP
			if (sysInfo.system.indexOf("iOS") > -1) {
				this.cursors = 100
			} else {
				this.cursors = 20
			}
			// #endif
		},
		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {
			if (this.socket) {
				this.closeSocket();
			}
		},



		onBackPress() {
			this.closeSocket();
		},

		onNavigationBarButtonTap() {
			if (this.storeId == 0) {
				return
			}
			this.$Router.push({
				path: '/standard/store/shopHomePage',
				query: {
					vid: this.storeId
				}
			})
		},


		methods: {

			touchscroll(e) {
				this.isScrollTop = true
			},

			scrollEvent(e) {
				this.moreOptionsModel = false
				this.modelFlag = -1
				this.optionPic = 'more_options'
			},

			touchstart() {
				this.moreOptionsModel = false
				this.modelFlag = -1
				this.optionPic = 'more_options'
				this.enableScroll = true
			},


			formatTime(time) {
				const stamp2date = new Date(time)


				let dateSet1 = [
					stamp2date.getFullYear(),
					stamp2date.getMonth() + 1,
					stamp2date.getDate(),
				].map(val => val.toString().length < 2 ? `0${val}` : val)

				let dateSet2 = [
					stamp2date.getHours(),
					stamp2date.getMinutes(),
					stamp2date.getSeconds(),
				].map(val => val.toString().length < 2 ? `0${val}` : val)


				return `${dateSet1.join('-')} ${dateSet2.join(':')}`


			},



			//点击图片预览放大
			TanPreviewImage(val) {
				const _this = this
				let imageUrl = JSON.parse(val).pic
				var images = [];
				images.push(imageUrl);
				uni.previewImage({ // 预览图片  图片路径必须是一个数组 => ["http://192.168.100.251:8970/6_1597822634094.png"]
					current: 0,
					urls: images,
					longPressActions: { //长按保存图片到相册
						itemList: ['保存图片'],
						success: (data) => {
							uni.saveImageToPhotosAlbum({ //保存图片到相册
								filePath: payUrl,
								success: function() {
									uni.showToast({
										icon: 'success',
										title: _this.$L('保存成功')
									})
								},
								fail: (err) => {
									uni.showToast({
										icon: 'none',
										title: _this.$L('保存失败，请重新尝试')
									})
								}
							});
						},
						fail: (err) => {}
					}
				});
			},
			//进入聊天详情页首次获取聊天记录
			initChatLog() {
				this.handleChatLog(0);
			},




			//获取聊天记录
			getChatLogNew(msgId) {
				let param = {}
				param.data = {}
				param.url = 'v3/helpdesk/seller/chat/msgLog'
				param.method = 'POST'
				param.data.memberId = this.memberId;

				// if (this.isFirstLoadingChatLog) {
				// 	param.data.pageSize = 20
				// } else {
				// 	param.data.pageSize = 10
				// }


				if (msgId) {
					param.data.msgId = msgId;
				}

				return new Promise(resolve => {
					this.$seller_request(param).then(res => {
						if (res.state == 200) {
							resolve(res.data.reverse())
						} else {
							resolve([])
						}
					})
				})
			},

			//处理聊天记录
			async handleChatLog(msgId) {
				let chatLog = await this.getChatLogNew(msgId)
				this.isLoadIcon = false
				this.enableScroll = false
				if (chatLog.length > 0) {
					this.changeMsgState(chatLog);
					this.minMsgId = chatLog[chatLog.length - 1].msgId;
					this.msgList.push(...chatLog)
					if (chatLog.length < 10) {
						this.hasMore = false
					}
				} else {
					this.minMsgId = 0
				}
				if (!msgId && this.chatBaseInfo.source == 'goods') {
					this.msgList.push({
						addTime: this.formatTime(new Date().getTime()),
						msgType: 'main_goods',
						msgContent: this.chatBaseInfo.showData
					});
				}
				if (!msgId && this.chatBaseInfo.source == 'order') {
					this.msgList.push({
						addTime: this.formatTime(new Date().getTime()),
						msgType: 'main_order',
						msgContent: this.chatBaseInfo.showData
					});
				}
				this.isFirstLoadingChatLog = false;
			},


			//修改当前消息列表的未读消息为已读
			changeMsgState(data) {
				let tmpMsgIdArray = [];
				data.map(item => {
					if (item.userType == 1 && item.msgState == 2) {
						tmpMsgIdArray.push(item.msgId);
					}
				});
				if (tmpMsgIdArray.length > 0) {
					this.socket.emit("read_msg", {
						msgIds: tmpMsgIdArray.join(','),
						...this.connectBaseData
					});
				}
			},


			//初始化socket
			initSocket() {
				if (this.socket) {
					this.closeSocket();
				}
				let sourceUrl = '';
				//#ifdef APP-PLUS
				sourceUrl += 'APP:';
				//#endif
				//#ifdef H5
				sourceUrl += 'H5:';
				//#endif
				//#ifdef MP
				sourceUrl += '小程序:';
				//#endif


				if (this.chatBaseInfo.source == 'chat_list') {
					sourceUrl += this.$L('从聊天列表页进入')
				} else if (this.chatBaseInfo.source == 'order') {
					sourceUrl += this.$L('从订单详情页进入')
				} else if (this.chatBaseInfo.source == 'store') {
					sourceUrl += this.$L('从店铺详情页进入')
				} else if (this.chatBaseInfo.source == 'goods') {
					sourceUrl += this.$L('从商品详情页进入')
				} else if (this.chatBaseInfo.source == 'userCenter') {
					sourceUrl += this.$L('从个人中心进入')
				}

				this.connectBaseData = {
					storeId: this.chatBaseInfo.storeId,
					userId: this.userCenterData.vendorId,
					// sourceUrl,
					role: 2,
				};
				this.socket = io(getApp().globalData.chatUrl, {
					reconnection: true,
					jsonp: true,
					transports: ['websocket', 'polling'],
					timeout: 5000,
				});

				this.socket.on("connect", () => {
					//监听连接成功事件
					this.socket.emit("connect_success", this.connectBaseData);
					//连接成功之后获取房间信息
					this.socket.on("get_room_info", e => {
						this.socketInfo = e;
					})

					this.socket.emit("vendor_change_room", {
						memberId: this.memberId,
						...this.connectBaseData
					})



					//连接成功 需要将当前消息列表：未读消息改为已读状态
					if (this.msgList.length > 0) {
						this.changeMsgState(this.msgList);
					}
					//监听接收消息
					this.socket.on("get_send_msg", e => {
						if (this.curMsgId != e.msgId) {
							this.curMsgId = e.msgId
							this.msgList.unshift({
								...e,
								addTime: this.formatTime(e.addTime)
							});
							this.scrolltoBottom()
						}
					});
					//监听消息已读事件
					this.socket.on("get_read_msg", e => {
						let allData = e.msgIds.split(',');
						this.msgList.map(item => {
							if (allData.indexOf(item.msgId)) {
								item.msgState = 1;
							}
						});
					});
				});
			},

			//聊天记录滑动到页面顶部
			chatLogToTop() {
				if (this.isFirstLoadingChatLog) {
					console.log('isFirstLoadingChatLog')
					return
				}

				if (!this.enableScroll) {
					console.log('enableScroll')
					return
				}

				if (this.minMsgId) {
					this.isScrollTop = true
					this.isLoadIcon = true
					setTimeout(() => {
						this.handleChatLog(this.minMsgId);
					}, 300)
				}


				// #ifdef MP-ALIPAY
				if (!this.aliPreScroll) {
					this.isScrollTop = true
					this.isLoadIcon = true
					setTimeout(() => {
						this.getCHatLog(this.minMsgId);
					}, 500)

				}
				this.aliPreScroll = false
				// #endif

			},

			// 插入表情
			insertEmoji(item) {
				this.inputVal += `[${item.title}]`
			},


			// 输入聚焦事件
			inputFocus(e) {


				// #ifndef MP
				this.optionPic = 'more_options'
				// #endif

				/**
				 * H5端 当先打开更多选项面板 再聚焦弹起键盘时，输入框会被遮挡
				 * 此方式对微信浏览器 和 safari浏览器 进行适配
				 * **/
				// #ifdef H5
				if (this.$isWeiXinBrower()) {
					setTimeout(() => {
						window.scrollTo(0, 10000)
					}, 100)
				} else if (this.moreOptionsModel) {
					setTimeout(function() {
						window.scrollBy(0, 49)
					}, 1000);
				}
				// #endif

				this.isFocus = true
				setTimeout(() => {
					this.moreOptionsModel = false;
				}, 100)

				this.$forceUpdate()
			},

			inputBlur() {
				this.$forceUpdate()
			},

			imageLoad(item) {
				this.$set(item, 'loaded', 'load')
			},

			//发送按钮事件
			send() {

				if (!this.userInfo.access_token) {
					this.$refs.loginPop.openLogin()
					return
				}

				if (!this.inputVal.trim() && !this.inputVal) {
					return false;
				}
				this.isScrollTop = false
				let msgData = {};
				msgData.memberId = this.socketInfo.memberId;
				msgData.vendorId = this.socketInfo.vendorId;
				msgData.msgType = '1'; //1.text(文本) 2.img(图片) 3.goods(商品) 4.order(订单)用户 5.常见问题
				msgData.msg = {
					content: this.emojiDetect()
				};
				this.socket.emit("send_msg", {
					...msgData,
					...this.connectBaseData
				});
				this.msgType = msgData.msgType
				this.scrolltoBottom()
				this.inputVal = ''; //清空输入框的内容
			},

			//表情文本检测
			emojiDetect() {
				let content = this.inputVal.replace(/\[(.+?)\]/g, (res) => {
					let index = this.emoji.findIndex(itm => itm.title == res.replace(/\[|\]/g, ''))
					if (index >= 0) {
						return `<img src="${this.emojiPath}${this.emoji[index].src}" alt="img">`
					} else {
						return res
					}
				})
				return content
			},



			//选择图片(相册)
			choosePicture() {
				return new Promise(resolve => {
					uni.chooseImage({
						count: 1,
						sizeType: ['compressed'],
						//可选择原图或压缩后的图片
						sourceType: ['album'],
						success: res => {
							// #ifdef H5
							this.translate(res.tempFiles[0].path, (res1) => {
								resolve(res1)
							})
							// #endif

							// #ifdef MP||APP-PLUS
							this.mpCompress(res.tempFiles[0].path, (res1) => {
								res1.tempFiles = res.tempFiles
								resolve(res1)
							})
							// #endif

						}
					})
				})
			},


			// 图片压缩start
			translate(imgSrc, callback) {
				let _this = this
				var img = new Image();
				img.src = imgSrc;
				img.onload = function() {
					var that = this;
					var h = that.height; // 默认按比例压缩
					var w = that.width;
					var canvas = document.createElement('canvas');
					var ctx = canvas.getContext('2d');
					var anw = document.createAttribute("width");
					anw.nodeValue = w;
					var anh = document.createAttribute("height");
					anh.nodeValue = h;
					canvas.setAttributeNode(anw);
					canvas.setAttributeNode(anh);
					ctx.drawImage(that, 0, 0, w, h); //压缩比例
					var quality = 0.5;
					var base64 = canvas.toDataURL('image/jpeg', quality);
					canvas = null;
					var blob = _this.base64ToBlob(base64);
					//压缩之后的图片大小
					var sizeZip = blob.size
					//Blob对象转blob地址
					var blobUrl = window.URL.createObjectURL(blob);
					let file = new File([blob], '1.png')
					let res = {
						tempFilePaths: [file],
						tempFiles: [{
							size: sizeZip
						}]
					}
					callback(res);
				}
			},

			base64ToBlob(base64) {
				var arr = base64.split(','),
					mime = arr[0].match(/:(.*?);/)[1],
					bstr = atob(arr[1]),
					n = bstr.length,
					u8arr = new Uint8Array(n);
				while (n--) {
					u8arr[n] = bstr.charCodeAt(n);
				}
				return new Blob([u8arr], {
					type: mime
				});
			},
			// 图片压缩end

			// 小程序图片压缩start
			mpCompress(imgSrc, callback) {
				let _this = this
				this.$refs.wCompress.start(imgSrc, {
					pixels: 4000000, // 最大分辨率，默认二百万
					quality: 0.8, // 压缩质量，默认0.8
					type: 'jpg', // 图片类型，默认jpg
					base64: false, // 是否返回base64，默认false，非H5有效
				}).then(res => {
					callback(res);
				})
			},
			// 小程序图片压缩end


			//拍摄图片
			cameraShot() {
				return new Promise(resolve => {
					uni.chooseImage({
						count: 1,
						sizeType: ['compressed'],
						//可选择原图或压缩后的图片
						sourceType: ['camera'],
						success: res => {
							// #ifdef H5
							this.translate(res.tempFiles[0].path, (res1) => {
								resolve(res1)
							})
							// #endif

							// #ifdef MP||APP-PLUS
							this.mpCompress(res.tempFiles[0].path, (res1) => {
								res1.tempFiles = res.tempFiles
								resolve(res1)
							})
							// #endif
						}
					})
				})
			},


			//上传图片
			async uploadPic(type) {
				let result = ''
				let that = this
				if (type == 0) {
					result = await this.choosePicture()
				} else if (type == 1) {
					result = await this.cameraShot()
				}
				if (result.tempFiles && result.tempFiles[0].size > this.uploadSize * 1024 * 1024) {
					uni.showToast({
						title: this.$L('图片超过20M'),
						icon: 'none'
					})
					return;
				} else {



					return new Promise((resolve, reject) => {

						let param = {
							url: getApp().globalData.apiUrl + 'v3/oss/front/upload',
							name: 'file',
							formData: {
								'source': 'goods',
							},
							header: {
								Authorization: 'Bearer ' + that.userInfo.access_token
							},
						}

						// #ifdef H5
						param.file = result.tempFilePaths[0]
						// #endif

						// #ifdef MP-WEIXIN ||APP-PLUS
						param.filePath = result.tempFilePath
						// #endif




						uni.uploadFile({
							...param,
							success: resup => {
								resup = JSON.parse(resup.data);
								if (resup.state == 200) {
									resolve(resup)
								}
							},
							complete(com) {}
						});
					})
				}

			},


			//发送图片
			async sendPic(type) {

				if (!this.userInfo.access_token) {
					this.$refs.loginPop.openLogin()
					return
				}


				if (type == 0) {
					this.picture = await this.uploadPic(0)
				} else if (type == 1) {
					this.picture = await this.uploadPic(1)
				}
				let msgData = {};
				this.isScrollTop = false
				if (this.picture) {
					msgData.memberId = this.socketInfo.memberId;
					msgData.vendorId = this.socketInfo.vendorId;
					msgData.msgType = '2'; //1.text(文本) 2.img(图片) 3.goods(商品) 4.order(订单)用户 5.常见问题
					msgData.msg = {
						pic: this.picture.data.url,
						width: this.picture.data.width,
						height: this.picture.data.height
					};
					this.socket.emit("send_msg", {
						...msgData,
						...this.connectBaseData
					});
					this.msgType = msgData.msgType
					this.scrolltoBottom()
					this.moreOptionsModel = false;

					this.optionPic = 'more_options'
				}
			},

			//发送商品事件,source来源  默认为空，表示main_goods里的商品  footprint、rec 表示足迹或者推荐商品 bottom_order 表示底部订单里的商品
			sendGoods(goodsData, source) {

				if (!this.userInfo.access_token) {
					this.$refs.loginPop.openLogin()
					return
				}



				let msgData = {};
				this.isScrollTop = false
				msgData.memberId = this.socketInfo.memberId;
				msgData.vendorId = this.socketInfo.vendorId;
				msgData.msgType = '3'; //1.text(文本) 2.img(图片) 3.goods(商品) 4.order(订单)用户 5.常见问题
				msgData.msg = {
					productId: goodsData.productId,
					goodsImage: goodsData.goodsImage,
					goodsName: goodsData.goodsName,
					goodsPrice: goodsData.goodsPrice,
				};
				if (source == 'footprint') {
					msgData.msg.goodsPrice = goodsData.productPrice;
				} else if (source == 'bottom_order') {
					msgData.msg.goodsImage = goodsData.productImage;
					msgData.msg.goodsPrice = goodsData.productShowPrice;
				} else if (source == 'rec') {
					msgData.msg.productId = goodsData.productId
					msgData.msg.goodsImage = goodsData.mainImage;
				}
				this.msgType = msgData.msgType
				this.socket.emit("send_msg", {
					...msgData,
					...this.connectBaseData
				});
				this.scrolltoBottom()
				this.closeModel();
			},
			//发送订单事件,source来源  默认为空，表示main_order里的订单  bottom表示来自于底部我的订单
			sendOrder(orderData, source = '', index) {

				if (!this.userInfo.access_token) {
					this.$refs.loginPop.openLogin()
					return
				}


				let msgData = {};
				this.isScrollTop = false
				msgData.memberId = this.socketInfo.memberId;
				msgData.vendorId = this.socketInfo.vendorId;
				msgData.msgType = '4'; //1.text(文本) 2.img(图片) 3.goods(商品) 4.order(订单)用户 5.常见问题
				let tempGoodsData = orderData.orderProductList[index];
				if (source) {
					tempGoodsData.goodsImage = tempGoodsData.productImage;
					tempGoodsData.goodsPrice = tempGoodsData.productShowPrice;
				}
				msgData.msg = {
					'orderSn': orderData.orderSn,
					'orderStateValue': orderData.orderStateValue,
					'createTime': orderData.createTime,
					'productId': tempGoodsData.productId,
					'goodsImage': tempGoodsData.goodsImage,
					'goodsName': tempGoodsData.goodsName,
					'goodsPrice': tempGoodsData.goodsPrice
				};
				this.socket.emit("send_msg", {
					...msgData,
					...this.connectBaseData
				});
				this.msgType = msgData.msgType
				this.scrolltoBottom()
				this.closeModel();
			},
			//发送常见问题
			sendProblem(problemData, source = "") {

				if (!this.userInfo.access_token) {
					this.$refs.loginPop.openLogin()
					return
				}


				let msgData = {};
				this.isScrollTop = false
				msgData.memberId = this.socketInfo.memberId;
				msgData.vendorId = this.socketInfo.vendorId;
				msgData.msgType = '5'; //1.text(文本) 2.img(图片) 3.goods(商品) 4.order(订单)用户 5.常见问题
				msgData.msg = {
					content: problemData.msgContent,
					reply: problemData.msgReply
				};
				this.socket.emit("send_msg", {
					...msgData,
					...this.connectBaseData
				});
				this.msgType = msgData.msgType
				this.scrolltoBottom()
				this.closeModel();
			},
			//关闭socket
			closeSocket() {
				if (this.socket) {
					this.socket.close();
				}
			},
			//聊天框 更多操作
			moreOptions(index) {
				console.log(index)
				if (this.modelFlag == index || this.modelFlag == -1) {
					this.moreOptionsModel = !this.moreOptionsModel;
				}
				if (!this.moreOptionsModel) {
					this.modelFlag = -1
				} else {
					this.modelFlag = index
				}
				if (!this.inputVal && this.moreOptionsModel && this.modelFlag == 0) {
					this.optionPic = 'close'
				} else {
					this.optionPic = 'more_options'
				}

				this.scrolltoBottom()
			},
			//打开常见问题弹框
			commonProblem() {

				if (!this.userInfo.access_token) {
					this.$refs.loginPop.openLogin()
					return
				}


				this.$refs.commonProblemModel.open();
				this.moreOptionsModel = false;

				this.getComProblemList()
				this.optionPic = 'more_options'
			},
			//关闭弹框
			closeModel() {
				this.$refs.commonProblemModel.close();
				this.$refs.moreListModel.close();
				this.optionPic = 'more_options'
			},

			//打开订单，足迹，推荐弹框  type：0,订单，1：足迹，2：推荐
			openMoreListModel(type) {

				if (!this.userInfo.access_token) {
					this.$refs.loginPop.openLogin()
					return
				}


				this.moreOptionsModel = false;

				this.$refs.moreListModel.open();
				this.currentAboutId = type;
				if (type == 0) {
					this.getOrderList()
				} else if (type == 1) {
					this.getFootPrint()
				} else if (type == 2) {
					this.getStoreRecom()
				}
				this.optionPic = 'more_options'
			},

			//展开全部，收起全部
			unfold(item) {
				if (item.isFold) {
					item.limit = item.orderProductList.length
					item.isFold = false
				} else {
					item.limit = 2
					item.isFold = true
				}
			},

			//关闭所有弹框
			closeSomeModel() {
				this.moreOptionsModel = false;

			},

			//进入店铺
			goStore() {

				if (this.chatBaseInfo.storeId == 0) {
					return
				}

				this.$Router.push({
					path: '/standard/store/shopHomePage',
					query: {
						vid: this.chatBaseInfo.storeId
					}
				})
			},

			//进入会员中心
			goMemberCenter() {

				if (!this.userInfo.access_token) {
					this.$refs.loginPop.openLogin()
					return
				}

				this.$Router.push('/pages/user/info')
			},

			//进入商品详情页 type为1，说明需要把json字符串转为对象，为空则直接使用
			goGoodsDetail(val, type) {
				let productId = val;
				if (type) {
					productId = JSON.parse(val).productId
				}
				this.$Router.push({
					path: '/standard/product/detail',
					query: {
						productId
					}
				})
			},

			//进入订单详情页 type为1，说明需要把json字符串转为对象，为空则直接使用
			goOrderDetail(val, type = '') {

				if (!this.userInfo.access_token) {
					this.$refs.loginPop.openLogin()
					return
				}

				let orderSn = val;
				if (type) {
					orderSn = JSON.parse(val).orderSn
				}
				this.$Router.push({
					path: '/pages/order/detail',
					query: {
						orderSn
					}
				})
			},

			//订单列表
			getOrderList() {
				let params = {
					url: 'v3/business/seller/orderInfo/userOrders',
					method: 'GET',
					data: {
						current: this.orderCurrent,
						memberId: this.memberId
					}
				}
				this.$seller_request(params).then(res => {
					if (res.state == 200) {
						if (this.orderCurrent == 1) {
							this.orderList = res.data.list;
						} else {
							this.orderList = this.orderList.concat(res.data.list);
						}
						this.hasMore = this.$checkPaginationHasMore(res.data.pagination); //是否还有数据
						if (this.hasMore) {
							this.orderCurrent++;
							this.loadingState = 'allow_loading_more';
						} else {
							this.loadingState = 'no_more_data';
						}

						this.orderList.map(item => {
							this.$set(item, 'limit', 2)
							this.$set(item, 'isFold', true)
						})
						this.orderList.map(item => {
							if (item.orderState == 10) {
								item.orderStateValue = this.$L('待付款')
							}
						})
						this.firstloadinglist.order = true
					} else {
						this.$api.msg(res.msg);
					}
				})
			},

			//我的足迹
			getFootPrint() {
				let params = {
					url: 'v3/member/seller/productLook/userFootprint',
					method: 'GET',
					data: {
						current: this.footCurrent,
						memberId: this.memberId
					}
				}
				this.$seller_request(params).then(res => {
					if (res.state == 200) {
						if (this.footCurrent == 1) {
							this.footprint = res.data.list;
						} else {
							this.footprint = this.footprint.concat(res.data.list);
						}
						this.hasMore = this.$checkPaginationHasMore(res.data.pagination); //是否还有数据
						if (this.hasMore) {
							this.footCurrent++;
							this.loadingState = 'allow_loading_more';
						} else {
							this.loadingState = 'no_more_data';
						}
						this.firstloadinglist.foot = true
					} else {
						this.$api.msg(res.msg);
					}
				})
			},

			//店铺推荐
			getStoreRecom() {
				let params = {
					url: 'v3/goods/seller/goods/list',
					method: 'GET',
					data: {
						current: this.storeCurrent,
					}
				}
				this.$seller_request(params).then(res => {
					if (res.state == 200) {
						if (this.storeCurrent == 1) {
							this.storeRecom = res.data.list;
						} else {
							this.storeRecom = this.storeRecom.concat(res.data.list);
						}
						this.hasMore = this.$checkPaginationHasMore(res.data.pagination); //是否还有数据
						if (this.hasMore) {
							this.storeCurrent++;
							this.loadingState = 'allow_loading_more';
						} else {
							this.loadingState = 'no_more_data';
						}
						this.firstloadinglist.recom = true
					} else {
						this.$api.msg(res.msg);
					}
				})
			},

			//常见问题列表
			getComProblemList() {
				let params = {
					url: 'v3/helpdesk/front/chat/problemList',
					method: 'GET',
					data: {
						storeId: this.storeId,
						current: this.comCurrent,
					}
				}
				this.$request(params).then(res => {
					if (res.state == 200) {
						if (this.comCurrent == 1) {
							this.commonProblemList = res.data.list.filter(item => item.isShow == 1);
						} else {
							this.commonProblemList = this.commonProblemList.concat(res.data.list).filter(item =>
								item.isShow == 1);
						}
						this.hasMore = this.$checkPaginationHasMore(res.data.pagination); //是否还有数据
						if (this.hasMore) {
							this.comCurrent++;
							this.loadingState = 'allow_loading_more';
						} else {
							this.loadingState = 'no_more_data';
						}
						this.firstloadinglist.problem = true
					} else {
						this.$api.msg(res.msg);
					}
				})
			},


			loadData(id) {
				if (id == 0 && this.hasMore) {
					this.getOrderList()
				} else if (id == 1 && this.hasMore) {
					this.getFootPrint()
				} else if (id == 2 && this.hasMore) {
					this.getStoreRecom()
				}
			},

			scrolltoBottom() {

			}

		},
	}
</script>

<style lang="scss">
	page {
		background: #f5f5f5;
		margin: 0 auto;
		width: 750rpx;
	}

	.interlayer,
	#top {
		display: flex !important;
	}

	uni-page-wrapper {
		height: auto !important;
	}

	.disNone {
		display: none;
	}

	.emoji_item {
		height: 56rpx;
		margin: 0 10rpx;

		image {
			width: 44rpx;
			height: 44rpx;
		}
	}

	.record_type_text ::v-deep div {
		display: flex !important;
		flex-wrap: wrap;
		padding: 0 !important;
	}

	.msg_state {
		color: var(--color_main);
		font-size: 24rpx;
		margin-bottom: 6rpx;
		margin-right: 10rpx;
		align-self: flex-end;

		&.read {
			color: #999;
		}
	}

	.chat_interface {

		.transparent_mask {
			width: 100%;
			height: 100%;
			position: fixed;
			background: #ffffff;
			top: 0;
			left: 0;
			opacity: 0;
			z-index: 5;
		}

		/* 头部  start*/
		.chat_interface_header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			background: #ffffff;
			/* padding-top: var(--status-bar-height); */
			height: calc(var(--status-bar-height) + 88rpx);
			padding-right: 20rpx;
			position: fixed;
			top: 0;
			width: 750rpx;
			z-index: 50;
			left: calc((100vw - 750rpx) / 2);
			position: fixed;
			padding: 14rpx 6px;
			padding-top: calc(14rpx + env(safe-area-inset-top));
			z-index: 998;
			color: #fff;
			transition-property: all;

			.chat_header_left {
				width: 44rpx;
				height: 44rpx;
				display: flex;
				justify-content: center;
				align-items: center;

				image {
					width: 17rpx;
					height: 29rpx;
				}
			}

			.chat_header_cen {
				font-size: 36rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #2d2d2d;
				line-height: 32rpx;
			}
		}

		/* 头部  end*/

		/* 聊天消息 start */
		.chat_con {
			position: fixed;
			top: 0;
			bottom: 0;


			transform: rotate(180deg);

			padding-top: constant(safe-area-inset-bottom);
			padding-top: env(safe-area-inset-bottom);

			display: flex;
			flex-direction: column;
			justify-content: space-between;

			.chat_des {
				overflow-y: auto;

				transition: all 0.3s;

				&.posBottom {
					bottom: 98rpx;
					/* #ifdef MP */
					bottom: calc(98rpx + env(safe-area-inset-bottom));
					/* #endif */
				}

				&.posTop {
					top: 0;
				}

				&.keyup {}

				.chat_info_time {
					display: flex;
					justify-content: center;

					text {
						width: 270rpx;
						height: 40rpx;
						background: #dddddd;
						border-radius: 20rpx;
						font-size: 22rpx;
						font-family: PingFang SC;
						font-weight: 400;
						color: #949494;
						line-height: 40rpx;
						text-align: center;
						margin-top: 30rpx;
					}
				}


				/* 聊天记录 start */
				.chat_record {
					padding: 20rpx 20rpx;

					.loading_icon {
						height: 50rpx;

						image {
							width: 30rpx;
							height: 30rpx;
						}
					}

					.loadBottom {
						height: 4rpx;
					}

					.loading_icon {
						image {
							width: 30rpx;
							height: 30rpx;
						}
					}

					.msg_item {
						transform: rotate(180deg);
						padding: 1rpx;
					}


					.customer_service_info {
						display: flex;
						justify-content: flex-start;
						margin-top: 30rpx;

						.customer_service_avatar {
							width: 80rpx;
							height: 80rpx;
							border-radius: 50%;
							margin-right: 20rpx;

							.image {
								width: 80rpx;
								height: 80rpx;
								border-radius: 50%;
								background-position: center center;
								background-repeat: no-repeat;
								background-size: cover;
							}
						}

						/* 消息记录类型 start */
						.customer_record {
							.record_type_text {
								max-width: 493rpx;
								min-height: 70rpx;
								background: #ffffff;
								border-radius: 0px 10px 10px 10px;
								font-size: 28rpx;
								font-family: PingFang SC;
								font-weight: 500;
								color: #2d2d2d;
								line-height: 39rpx;
								display: flex;
								align-items: center;
								justify-content: center;
								flex-wrap: wrap;
								padding: 15rpx 15rpx;
								word-break: break-all;
							}
						}

						/* 消息记录类型 end */
					}

					.user_info_record {
						display: flex;
						justify-content: flex-end;
						margin-top: 30rpx;

						/* 消息记录类型 start */
						.user_record {
							display: flex;
							flex-direction: column;
							align-items: flex-end;
						}

						/* 消息记录类型 end */
						.user_info_avatar {
							width: 80rpx;
							height: 80rpx;
							border-radius: 50%;
							margin-left: 20rpx;

							image {
								width: 80rpx;
								height: 80rpx;
								border-radius: 50%;
							}
						}
					}
				}

				/* 聊天记录 end */
			}


			.chat_container {
				width: 750rpx;

				.chat_scroller {}
			}




			/* 聊天框 start */
			.chat_info_con {
				width: 750rpx;
				transform: rotate(180deg);
				z-index: 10;

				.chat_info {
					width: 750rpx;
					height: 49px;
					background: #ffffff;
					box-shadow: 0rpx 0rpx 19rpx 1rpx rgba(214, 214, 214, 0.1);
					display: flex;
					padding: 0 20rpx;
					box-sizing: border-box;
					justify-content: space-between;
					align-items: center;

					.send_button {
						width: 100rpx;
						height: 50rpx;
						background: var(--color_main_chat_bg);
						border-radius: 6px;
						color: #ffffff;
						line-height: 50rpx;
						text-align: center;
						margin-left: 0rpx;
						font-size: 26rpx;
					}

					.chat_info_input {
						height: 62rpx !important;
						min-height: unset !important;
						background: #f8f8f8;
						border: 1rpx solid rgba(0, 0, 0, 0.05);
						border-radius: 31rpx;
						padding-left: 20rpx;
						font-size: 24rpx;
						opacity: 0.5;
					}

					.chat_info_expression {
						width: 50rpx;
						height: 50rpx;
					}

					.chat_info_options {
						width: 50rpx;
						height: 50rpx;
					}
				}

				/* 更多操作 start */
				.info_options {
					height: 209px;
					background-color: #ffffff;
					display: flex;
					flex-wrap: wrap;
					padding: 0 25rpx 0 30rpx;

					.info_options_pre {
						display: flex;
						flex-direction: column;
						justify-content: center;
						align-items: center;
						/* margin-bottom: 3rpx; */
						width: 120rpx;
						margin-right: 70rpx;

						.tpe {
							/* width: 100rpx;
							height: 100rpx; */
							margin-top: 12rpx;
							/* padding: 20rpx 20rpx; */
							background-color: #ffffff;

							image {
								width: 58rpx;
								height: 58rpx;
								margin: auto;
							}
						}

						text {
							font-size: 26rpx;
							font-family: PingFang SC;
							font-weight: 500;
							color: #2d2d2d;
							line-height: 39rpx;
							/* margin-top: 13rpx; */
						}
					}

					.info_options_pre:nth-of-type(4n) {
						margin-right: 0;
					}
				}

				/* 更多操作 end */
			}

			/* 聊天框 end */
		}

		/* 聊天消息 end */

		/* 常见问题弹框  start*/
		.common_problem {
			width: 750rpx;
			height: 763rpx;
			background: #ffffff;
			border-radius: 10rpx 10rpx 0 0;

			.common_problem_title {
				width: 100%;
				height: 88rpx;
				display: flex;
				justify-content: space-between;
				padding: 0 8rpx 0 20rpx;
				box-sizing: border-box;
				align-items: center;
				border-bottom: 1rpx solid #f2f2f2;
				border-radius: 10rpx 10rpx 0 0;

				text {
					font-size: 30rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #2d2d2d;
					line-height: 39rpx;
				}

				.common_problem_close {
					width: 44rpx;
					height: 44rpx;
					display: flex;
					justify-content: center;
					align-items: center;

					image {
						width: 21rpx;
						height: 21rpx;
					}
				}
			}

			.common_problem_list {
				width: 750rpx;
				height: 675rpx;

				.common_problem_pre {
					width: 750rpx;

					.common_problem_text {
						width: 710rpx;
						margin: 0 20rpx;
						font-size: 28rpx;
						padding: 33rpx 0 40rpx;
						box-sizing: border-box;
						font-family: PingFang SC;
						font-weight: 500;
						color: #2d2d2d;
						line-height: 39rpx;
						border-bottom: 1rpx solid #f2f2f2;
						display: flex;
						align-items: center;
						word-break: break-all;
					}

					&:hover {
						background: #f8f8f8;
					}
				}

				.common_problem_pre_active {
					background: #f8f8f8;
				}
			}
		}

		/* 常见问题弹框 end */

		/* 订单，足迹，推荐 弹框 start */
		.more_list_model {
			width: 750rpx;
			height: 900rpx;
			background: #f8f8f8;
			border-radius: 10rpx 10rpx 0 0;

			.about_more_title {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding-left: 20rpx;
				box-sizing: border-box;
				height: 87rpx;
				background: #ffffff;

				.about_more_title_left {
					display: flex;
					align-items: center;

					.about_more_title_pre {
						font-size: 30rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #2d2d2d;
						line-height: 87rpx;
						position: relative;
						margin-right: 56rpx;

						&.current {
							color: var(--color_main);
							font-size: 32rpx;
							font-weight: bold;

							&:after {
								content: '';
								position: absolute;
								left: 50%;
								bottom: 0;
								transform: translateX(-50%);
								width: 52rpx;
								height: 6rpx;
								background: var(--color_main_chat_bg);
								border-radius: 3rpx;
							}
						}
					}

					.about_more_title_pre:nth-last-of-type(1) {
						margin-right: 0;
					}
				}

				.about_more_close {
					width: 44rpx;
					height: 44rpx;
					display: flex;
					align-items: center;

					image {
						width: 22rpx;
						height: 22rpx;
					}
				}
			}

			.about_more_con {
				width: 750rpx;
				height: 813rpx;
				padding: 20rpx;

				/* 我的订单 start */
				.order_list_scroll {
					width: 740rpx;
					height: 793rpx;
				}

				.about_more_list {
					.about_more_pre {
						width: 710rpx;
						background: #ffffff;
						position: relative;
						border-radius: 6rpx;
						margin-bottom: 14rpx;

						.order_title {
							display: flex;
							align-items: center;
							justify-content: space-between;
							height: 60rpx;
							padding: 0 20rpx;

							text:nth-child(1) {
								font-size: 24rpx;
								font-family: PingFang SC;
								font-weight: 500;
								color: #949494;
								line-height: 39rpx;
							}

							text:nth-child(2) {
								font-size: 24rpx;
								font-family: PingFang SC;
								font-weight: 500;
								color: #949494;
								line-height: 39rpx;
							}
						}

						.order_list {
							.order_list_pre {
								height: 174rpx;
								margin: 0 20rpx;
								border-top: 1rpx solid #f2f2f2;
								display: flex;
								justify-content: space-between;
								padding: 20rpx 0;
								box-sizing: border-box;

								.order_pre_img {
									width: 134rpx;
									height: 134rpx;
									border-radius: 6rpx;

									image {
										width: 134rpx;
										height: 134rpx;
										border-radius: 6rpx;
									}
								}

								.order_pre_des {
									display: flex;
									flex-direction: column;
									justify-content: space-between;

									.order_pre_name {
										width: 515rpx;
										font-size: 24rpx;
										font-family: PingFang SC;
										font-weight: 500;
										color: #2d2d2d;
										line-height: 36rpx;
										text-overflow: -o-ellipsis-lastline;
										overflow: hidden;
										text-overflow: ellipsis;
										display: -webkit-box;
										-webkit-line-clamp: 2;
										line-clamp: 2;
										-webkit-box-orient: vertical;
									}

									.order_pre_des_bot {
										display: flex;
										justify-content: space-between;
										align-items: center;

										.order_pre_price_active {
											color: var(--color_price);
										}

										.order_pre_price {
											font-size: 26rpx;
											font-family: PingFang SC;
											font-weight: 600;
											color: var(--color_price);
											line-height: 36rpx;

											text:nth-child(2) {
												font-size: 30rpx;
											}
										}

										.order_pre_link {
											font-size: 24rpx;
											font-family: PingFang SC;
											font-weight: 500;
											color: var(--color_price);
											line-height: 36rpx;
										}
									}
								}
							}
						}

						.unfold_fold {
							display: flex;
							justify-content: center;
							padding: 10rpx 0 20rpx;
							align-items: center;

							text {
								font-size: 24rpx;
								font-family: PingFang SC;
								font-weight: 500;
								color: #333333;
								line-height: 39rpx;
							}

							image {
								width: 19rpx;
								height: 11rpx;
								margin-left: 10rpx;
							}
						}

						.order_status {
							position: absolute;
							width: 100rpx;
							height: 30rpx;
							background: var(--color_halo);
							border-radius: 6rpx;
							font-size: 22rpx;
							font-family: PingFang SC;
							font-weight: 500;
							color: var(--color_main);
							line-height: 30rpx;
							text-align: center;
							bottom: 0;
							left: 0;
						}

						.order_status_await {
							background: #f8f8f8;
							color: #949494;
						}

						order_status_awaits {
							background: #f8f8f8;
							color: #ff0000;
						}
					}
				}

				/* 我的订单 end */
				.footprint_list_scroll {
					width: 710rpx;
					height: 773rpx;
				}

				/* 我的足迹 start */
				.footprint_list {
					background: #ffffff;

					.order_list_pre {
						height: 174rpx;
						margin: 0 20rpx;
						border-top: 1rpx solid #f2f2f2;
						display: flex;
						justify-content: space-between;
						padding: 20rpx 0;
						box-sizing: border-box;

						.order_pre_img {
							width: 134rpx;
							height: 134rpx;
							border-radius: 6rpx;

							image {
								width: 134rpx;
								height: 134rpx;
								border-radius: 6rpx;
							}
						}

						.order_pre_des {
							display: flex;
							flex-direction: column;
							justify-content: space-between;

							.order_pre_name {
								width: 515rpx;
								font-size: 24rpx;
								font-family: PingFang SC;
								font-weight: 500;
								color: #2d2d2d;
								line-height: 36rpx;
								text-overflow: -o-ellipsis-lastline;
								overflow: hidden;
								text-overflow: ellipsis;
								display: -webkit-box;
								-webkit-line-clamp: 2;
								line-clamp: 2;
								-webkit-box-orient: vertical;
							}

							.order_pre_des_bot {
								display: flex;
								justify-content: space-between;
								align-items: center;

								.order_pre_price {
									font-size: 26rpx;
									font-family: PingFang SC;
									font-weight: 600;
									color: var(--color_price);
									line-height: 36rpx;

									text:nth-child(2) {
										font-size: 30rpx;
									}
								}

								.order_pre_price_active {
									color: var(--color_price);
								}

								.order_pre_link {
									font-size: 24rpx;
									font-family: PingFang SC;
									font-weight: 500;
									color: var(--color_price);
									line-height: 36rpx;
								}
							}
						}
					}
				}

				/* 我的足迹 end */
			}
		}

		/* 订单，足迹，推荐 弹框 end */
	}

	.empty_page {
		margin-top: 60rpx;
		height: 270rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: flex-end;
		padding-right: 20rpx;

		image {
			width: 380rpx;
			height: 280rpx;
		}

		text {
			margin-top: 20rpx;
			font-size: 24rpx;
			color: #999999;
		}
	}

	.record_type_text {
		max-width: 493rpx;
		min-height: 70rpx;
		background: var(--color_main_chat_bg);
		border-radius: 10rpx 0rpx 10rpx 10rpx;
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #ffffff;
		line-height: 39rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 15rpx 15rpx;
		max-width: 550rpx;
		position: relative;
		word-break: break-all;

		.record_type_text_type {
			font-size: 24rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #999999;
			position: absolute;
			left: -65rpx;
			bottom: 0;
		}

		.record_type_text_type_off {
			font-size: 24rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #fe2315;
			position: absolute;
			left: -65rpx;
			bottom: 0;
		}
	}

	.good_type_order {
		width: 510rpx;
		height: 196rpx;
		background: #ffffff;
		border-radius: 10rpx;
		margin-top: 30rpx;
		position: relative;

		/* 已读/未读 */
		.record_type_text_type {
			font-size: 24rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #999999;
			position: absolute;
			left: -65rpx;
			bottom: 0;
		}

		.record_type_text_type_off {
			font-size: 24rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #fe2315;
			position: absolute;
			left: -65rpx;
			bottom: 0;
		}

		.record_text_type {
			font-size: 24rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #fe2012;
			position: absolute;
			left: -65rpx;
			bottom: 14rpx;
		}

		.record_type_order_info {
			width: 100%;
			height: 63rpx;
			display: flex;
			font-size: 24rpx;
			line-height: 63rpx;
			position: relative;
			justify-content: space-around;
			color: #949494;

			.record_order_time {
				flex-grow: 0.3;
				display: flex;
				justify-content: space-around;
			}
		}

		.record_type_order_info::before {
			content: '';
			display: inline-block;
			width: 97%;
			height: 1rpx;
			border-top: 1rpx solid #f3f3f3;
			position: absolute;
			bottom: 0;
			left: 0;
		}

		.record_type_order_content {
			width: 100%;
			display: flex;
			justify-content: space-around;
			box-sizing: border-box;
			padding-top: 20rpx;

			.record_type_text_type {
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #999999;
				position: absolute;
				left: -65rpx;
				bottom: 0;
			}

			.record_type_text_type_off {
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #fe2315;
				position: absolute;
				left: -65rpx;
				bottom: 0;
			}

			.record_order_image {
				width: 156rpx;
				height: 156rpx;
				border-radius: 10rpx !important;

				image {
					width: 156rpx;
					height: 156rpx;
				}
			}

			.record_order_con {
				width: 310rpx;
				/* height: 242rpx; */
				padding-top: 19rpx;
				margin-left: -20px;

				.record_order_name {
					width: 100% !important;
					font-size: 26rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #333333;
					line-height: 36rpx;
					text-overflow: -o-ellipsis-lastline;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					line-clamp: 2;
					-webkit-box-orient: vertical;
					width: 418rpx;
					padding-left: 20rpx;
					box-sizing: border-box;
				}

				.record_price_and_status {
					width: 100%;
					height: 70rpx;
					display: flex;
					justify-content: space-between;
					padding-top: 60rpx;

					.record_order_price {
						font-size: 26rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: var(--color_price);
						position: absolute;
						top: 140rpx;
						left: 200rpx;
					}

					.record_order_status {
						width: 76rpx;
						height: 30rpx;
						background: #f0f2f5;
						border-radius: 24rpx;
						font-size: 22rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #949494;
						line-height: 30rpx;
						text-align: center;
						margin-left: 60rpx;
					}
				}

				.record_order_des {
					display: flex;
					margin: 10rpx 0 20rpx;
					padding-left: 20rpx;
					box-sizing: border-box;

					.record_order_ordersn {
						font-size: 24rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #949494;
					}

					.record_order_time {
						font-size: 24rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #949494;
						margin-left: 17rpx;
					}
				}
			}

			.record_order_status {
				width: 76rpx;
				height: 30rpx;
				background: #f0f2f5;
				border-radius: 24rpx;
				font-size: 22rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #949494;
				line-height: 30rpx;
				text-align: center;
				margin-left: 20rpx;
			}
		}
	}

	.record_type_order {
		width: 510rpx;
		height: 260rpx;
		background: #ffffff;
		border-radius: 10rpx;
		margin-top: 20rpx;
		position: relative;

		.record_type_text_type {
			font-size: 24rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #999999;
			position: absolute;
			left: -65rpx;
			bottom: 0;
		}

		.record_type_text_type_off {
			font-size: 24rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #fe2315;
			position: absolute;
			left: -65rpx;
			bottom: 0;
		}

		.record_text_type {
			font-size: 24rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #999999;
			position: absolute;
			left: -65rpx;
			bottom: 14rpx;
		}

		.record_type_order_info {
			width: 100%;
			height: 63rpx;
			display: flex;
			font-size: 24rpx;
			line-height: 63rpx;
			position: relative;
			justify-content: space-around;
			color: #949494;

			.record_order {
				width: 80%;
				box-sizing: border-box;
				padding-left: 20rpx;
				word-break: break-all;
			}

			.record_order_time {
				width: 50%;
				transform: translate(-20rpx, 0);

				display: flex;
				justify-content: flex-end;
			}
		}

		.record_type_order_info::before {
			content: '';
			display: inline-block;
			width: 97%;
			height: 1rpx;
			border-top: 1rpx solid #f3f3f3;
			position: absolute;
			bottom: 0;
			left: 0;
		}

		.record_type_order_content {
			width: 100%;
			display: flex;
			justify-content: space-around;
			box-sizing: border-box;
			padding-top: 20rpx;

			.record_order_image {
				width: 156rpx;
				height: 156rpx;
				border-radius: 10rpx !important;

				image {
					width: 156rpx;
					height: 156rpx;
				}
			}

			.record_order_con {
				width: 310rpx;
				/* height: 242rpx; */
				padding-top: 19rpx;
				margin-left: -20px;
				display: flex;
				flex-direction: column;
				justify-content: space-between;

				.record_order_name {
					width: 100% !important;
					font-size: 26rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #333333;
					line-height: 36rpx;
					text-overflow: -o-ellipsis-lastline;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					line-clamp: 2;
					-webkit-box-orient: vertical;
					width: 418rpx;
					padding-left: 20rpx;
					box-sizing: border-box;
				}

				.record_price_and_status {
					width: 100%;
					height: 70rpx;
					display: flex;
					justify-content: space-between;
					padding-top: 30rpx;

					.record_order_price {
						font-size: 26rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: var(--color_price);
						margin-bottom: 12rpx;
						padding-left: 20rpx;
					}

					.record_order_status {
						width: 120rpx;
						height: 30rpx;
						background: #f0f2f5;
						border-radius: 14rpx;
						font-size: 22rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #949494;
						line-height: 30rpx;
						text-align: center;
						margin-left: 20rpx;
					}
				}

				.record_order_des {
					display: flex;
					margin: 10rpx 0 20rpx;
					padding-left: 20rpx;
					box-sizing: border-box;

					.record_order_ordersn {
						font-size: 24rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #949494;
					}

					.record_order_time {
						font-size: 24rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #949494;
						margin-left: 17rpx;
					}
				}
			}

			.record_order_status {
				width: 76rpx;
				height: 30rpx;
				background: #f0f2f5;
				border-radius: 14rpx;
				font-size: 22rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #949494;
				line-height: 30rpx;
				text-align: center;
				margin-left: 20rpx;
			}
		}
	}

	.record_type_image {
		display: flex;
		flex-direction: column;
		align-items: flex-end;
		margin-top: 20rpx;
		position: relative;

		.img_con {
			/* max-width: 460rpx !important; */
			max-height: 445rpx !important;
			position: relative;

			min-width: 100rpx !important;
			min-height: 100rpx !important;
			background-color: #fff;

			.unload_con {
				width: 100%;
				height: 100%;
				position: absolute;
				top: 0;
				left: 0;
				z-index: 20;

				image {
					width: 50rpx;
					height: 50rpx;
				}
			}

			.record_type_image_width {
				width: 100%;
				height: 100%;
				border-radius: 10rpx;
			}
		}

		/* 已读/未读 */
		.record_type_text_type {
			font-size: 24rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #999999;
			position: absolute;
			left: -65rpx;
			bottom: 0;
		}

		.record_type_text_type_off {
			font-size: 24rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #fe2315;
			position: absolute;
			left: -65rpx;
			bottom: 0;
		}

		.record_type_image_height {
			width: 343rpx;
			height: 460rpx;
			border-radius: 10px;
			margin-top: 30rpx;
		}

		.record_type_image_quate {
			width: 343rpx;
			height: 343rpx;
		}
	}

	.record_type_text ::v-deep #top>div div,
	.record_type_text ::v-deep .interlayerAll {
		white-space: pre-wrap !important;
		text-overflow: unset !important;
		flex-wrap: wrap;
		word-break: break-word;
		word-wrap: break-word;
	}
</style>

<!-- sss -->