<template>
	<view class="container">
		<view class="logistics">
			<label class="radio" v-for="(item,index) in traceList" :key="index">
				<checkbox-group @change="Checked" class="checkbox_group" :data-expressids="item.expressId">
					<view>
						<checkbox :value="item.expressName" />
					</view>
					<text>{{item.expressName}}</text>
				</checkbox-group>
			</label>
		</view>
	</view>
</template>

<script>
	export default {
		onLoad() {
			let param = {}
      console.log(this.current);
			param.current = this.current
			this.param = param
			this.getTranceList(param)
		},
		data() {
			return {
				// tag:'',
				traceList: [], //物流公司列表
				current: 1, //页数
				param: {}, //参数
				imgUrl: getApp().globalData.img_url,
				expressIds: [],
				total: '', //物流公司总条目数
				hasMore: false, //是否还能加载更多
			}
		},
		onShow() {

		},
		onNavigationBarButtonTap(e) {
			let param = {}
			const expressId = [...new Set(this.expressIds)].join(',')
			param.expressIds = expressId
			console.log(expressId)
			this.$seller_request({
				url: 'v3/seller/seller/express/add',
				method: 'POST',
				data: param
			}).then(ret => {
				if (ret.state == 200) {
					this.$api.msg(ret.msg)
					setTimeout(() => {
						uni.navigateTo({
							url: "/merchants/pages/index/index"
						})
					}, 1500)
				} else {
					this.$api.msg(ret.msg)
				}
			})
		},
		methods: {
			//获取物流公司列表
			getTranceList(param) {
				this.$seller_request({
					url: 'v3/seller/seller/express/list',
					method: 'GET',
					data: param
				}).then(ret => {
					console.log(ret)
					if (ret.state == 200) {
						console.log(ret)
						this.traceList = ret.data.list
						this.total = ret.data.pagination.total
					}
				})
			},
			//绑定状态改变事件
			Checked(e) {
				const expressId = e.currentTarget.dataset.expressids
				if (e.detail.value.length == 1) {
					this.expressIds.push(expressId)
				} else {
					this.expressIds = this.expressIds.filter(item => item != expressId)
				}
			},


		},
		//页面滚动生命周期
		onReachBottom() {
			if (this.hasMore) return
			if (this.traceList.length == this.total) {
				this.$api.msg('已加载全部')
				this.hasMore = true
				return
			}
			uni.showLoading({
				title: '加载中',
				mask: true
			})
			let param = this.param
			param.current++
			this.$seller_request({
				url: 'v3/seller/seller/express/list',
				method: 'GET',
				data: param
			}).then(ret => {
				if (ret.state == 200) {
					this.traceList = [...this.traceList, ...ret.data.list]
					uni.hideLoading()
				}
			})
		},
	}
</script>

<style lang="scss" scoped>
	page {
		width: 100%;
		height: 100%;
	}

	.container {
		width: 100%;
		height: 100%;
		border: 1px solid #FFFFFF;
		background-color: #eee;
	}

	.container .logistics {
		display: flex;
		flex-direction: column;
		align-content: center;
		width: 100%;
		margin: 30rpx auto;
		background-color: #FFFFFF;
	}

	.radio {
		display: flex;
		padding: 30rpx 20rpx;
		border-bottom: 1rpx solid #eee;
	}

	.checkbox_group {
		display: flex;
		align-items: center;
	}
</style>
