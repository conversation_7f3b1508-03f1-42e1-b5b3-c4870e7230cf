<template>
	<view class="content" @click="hideOperate(currentAddressId)" :class="{'clo':addressList.length==0}">
		<template v-if='addressList.length'>
			<view class="address_list">
        <!-- @click="checkAddress(item)" 原来绑定的事件 -->
				<view v-for="(item, index) in addressList" :key="index"  @tap.stop="checkAddr(item)"
					:class="{'list':true,'b-b':index!=addressList.length-1}" @longpress="showOperate(item.addressId)">
					<view class="wrapper">
						<text v-if="sourceId==item.addressId" class="iconfont iconziyuan33"></text>
						<view>
							<view class="u-box flex_row_start_center">
								<text class="name">{{item.contactName}}</text>
								<text class="mobile">{{item.telphone}}</text>
								<!-- <image v-if="item.isDefault" class="tag" :src="imgUrl+'default_address_tag.png'" /> -->
							</view>
							<view class="address-box">
								<text class="address">{{item.areaInfo}} {{item.address}}</text>
							</view>
							<view class="addrEdit">
								<!-- <button class="editBtn1" v-if="!item.isDefault" type="default"></button> -->
								<button class="editBtn" v-if="item.isDefault" type="default">{{$L('默认地址')}}</button>
								<view class="right">
									<view class="edit" @click.stop="operateAddress('edit', item.addressId)">
										<image :src="imgUrl+'edit.png'" mode=""></image>
										<text>{{$L('编辑')}}</text>
									</view>
									<view class="delete" @click.stop="delAddress(item.addressId)">
										<image :src="imgUrl+'delete2.png'" mode=""></image>
										<text>{{$L('删除')}}</text>
									</view>
								</view>
							</view>
						</view>
						
					</view>
					<view v-show="curOperateId==item.addressId" class="mask flex_row_center_center"
						@click="hideOperate(item.addressId)">
						<view class="edit flex_row_center_center" @click.stop="operateAddress('edit', item.addressId)">
							{{$L('编辑')}}</view>
						<view class="del flex_row_center_center" @click.stop="delAddress(item.addressId)">{{$L('删除')}}
						</view>
					</view>
				</view>
			</view>
			<view class="add_btn_bottom flex_row_center_center">
				<button class="add_btn flex_row_center_center" @click="operateAddress('add')">+
					{{$L('添加新地址')}}</button>
			</view>
		</template>
		<template v-if="!addressList.length&&loadingState != 'first_loading'">
			<view class="flex_column_start_center empty_part">
				<image class="img" :src="imgUrl+'empty_address.png'" />
				<text class="tip_con">{{$L('还没有发货地址哦')}}~</text>
				<view class="ope_btn flex_row_center_center" @click="operateAddress('add')">
					{{$L('新建地址')}}
				</view>
			</view>
		</template>
		<loadingState v-if="loadingState == 'first_loading'||addressList.length > 0" :state='loadingState' />
		<uni-popup ref="popup" type="dialog">
			<uni-popup-dialog type="input" :before-close="true" :title="$L('提示')" :content="popTip" :duration="2000" @close="cancelChange"
				@confirm="confirmChange"></uni-popup-dialog>
		</uni-popup>
	</view>
</template>

<script>
	import loadingState from "@/components/loading-state.vue";
	import uniPopup from '@/components/uni-popup/uni-popup.vue'
	import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue'
	import {
		mapState,
		mapMutations
	} from 'vuex';
	export default {
		components: {
			loadingState,
			uniPopup,
			uniPopupDialog
		},
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				source: 0,
				sourceId: '', //下单页面选中的地址addressId
				curOperateId: '',
				loadingState: 'first_loading',
				orderSn: '', //订单号
				currentAddressId: '', //记录当前长按点击的地址
				editing: false,
				addressId: 0,
				popTip: '',
				popType: '',
				inComfirm: false, //是否提交修改中
			}
		},
		onLoad(option) {
			// this.orderSn = this.$Route.query.orderSn;
      this.orderSn = option.orderSn
			this.source = this.$Route.query.source ? this.$Route.query.source :
			0; // source ：3 从订单详情页的修改地址进入的    2：从订单列表的修改地址进入    1：从确认下单页面的修改地址进入的
			this.sourceId = 15;
			this.getAddressList();
			console.log(this.addressList)
		},
		computed: {
			...mapState(['userInfo', 'addressList'])
		},
		//滚动事件地址蒙层
		onPageScroll(e) {
			if (e.scrollTop > 0) {
				this.hideOperate(this.currentAddressId)
			}
		},
		
        onNavigationBarButtonTap(e){
			uni.navigateTo({
				url:"/merchants/pages/shopaddress/add"
			})
		},
		methods: {
      //选择发货地址跳回发货页面
      // checkAddr(item){
      //   console.log(item)
      //   uni.redirectTo({
      //     url:"/merchants/pages/order/delivery?orderSn=" + this.orderSn + '&areaInfo=' + item.areaInfo + '&telphone=' + item.telphone + '&contactName=' + item.contactName + '&adress=' + item.
      //   })
      // },
			...mapMutations(['operateAddressData1']),
			//获取地址列表
			getAddressList() {
				this.$seller_request({
					url: 'v3/seller/seller/address/list?type=1',
					method: 'GET'
				}).then(res => {
					if (res.state == 200) {
						this.operateAddressData1(res.data.list);
					} else {
						this.$api.msg(res.msg);
					}
					this.loadingState = 'complete';
				}).catch((e) => {
					//异常处理
				})
			},
			cancelChange() {
				this.editing = false
				this.$refs.popup.close()
			},
			confirmChange() {
				if(this.inComfirm){
					return;
				}
				this.$refs.popup.close()
				if (this.popType == 'edit') {
					this.inComfirm = true;
					this.$request({
						url: 'v3/business/front/orderOperate/updateAddress',
						data: {
							orderSn: this.orderSn,
							addressId: this.addressId,
						},
						method: 'POST'
					}).then(res => {
						if (res.state == 200) {
							if (this.source == 3) { //从订单详情页的修改地址进入的
								this.$api.msg(res.msg);
								setTimeout(() => {
									this.$api.prePage().getOrderDetail();
									this.$Router.back(1)
									this.editing = false
									this.inComfirm = false
								}, 1500)
							} else if (this.source == 2) { //从订单列表的修改地址进入的
								this.$api.msg(res.msg);
								setTimeout(() => {
									this.$Router.back(1)
									this.editing = false
									this.inComfirm = false
								}, 1500)
							}
						} else {
							this.editing = false
							this.$api.msg(res.msg);
						}
					})
				} else if (this.popType == 'del') {
					this.operateAddressData1(this.addressList);
					this.$seller_request({
						url: 'v3/seller/seller/address/del',
						data: {
							addressId: this.addressId,
						},
						method: 'POST'
					}).then(res => {
						this.$api.msg(res.msg);
						if (res.state == 200) {
							//更新数据
							let tmp_data = this.addressList.filter(item => item.addressId != this.addressId)
							this.operateAddressData1(tmp_data);
						}
					}).catch((e) => {
						//异常处理
					})
				}
			},


			//选择地址
			checkAddress(item) {
				if (this.editing) {
					return
				}
				if (this.source == 1) {
					// this.$api.prePage().orderAddress=item
					this.$api.prePage().changeAddress(item);
					this.$Router.back(1)
				} else if (this.source == 2 || this.source == 3) {
					this.editing = true
					//从订单详情或订单列表里面进入的地址列表
					this.addressId = item.addressId
					this.popTip = '确认修改地址?'
					this.popType = 'edit'
					this.$refs.popup.open()
				}
			},
			operateAddress(type, addressId) {
				this.curOperateId = '';
				let query = {
					type
				}
				let url = `/merchants/pages/shopaddress/add?type=${type}`;
				if (type == 'edit') {
					query.addressId=addressId
				}
				this.$Router.push({path:'/merchants/pages/shopaddress/add',query})
			},
			//地址长按事件
			showOperate(addressId) {
				this.currentAddressId = addressId
				if (this.curOperateId == addressId) {
					this.curOperateId = '';
				} else {
					this.curOperateId = addressId;
				}
				//阻止浏览器默认行为
				// #ifdef H5
				document.oncontextmenu = function(e) {
					e.preventDefault()
				}
				// #endif
				
			},
			//点击蒙层隐藏
			hideOperate(addressId) {
				this.curOperateId = '';
			},
			//删除地址事件
			delAddress(addressId) {
				this.popTip = '确定删除地址?'
				this.popType = 'del'
				this.addressId = addressId
				this.$refs.popup.open()
			},
		}
	}
</script>

<style lang='scss'>
	/* .uni-page-head-ft{
		width: 60rpx;
		height: 60rpx;
		background-color: #000;
		text-align: center;
	} */
	page {
		width: 750rpx;
		margin: 0 auto;
		background: #eee;
		-webkit-touch-callout: none;
		/*系统默认菜单被禁用*/
		-webkit-user-select: none;
		/*webkit浏览器*/
		-khtml-user-select: none;
		/*早期浏览器*/
		-moz-user-select: none;
		/*火狐*/
		-ms-user-select: none;
		/*IE10*/
		user-select: none;
		-webkit-touch-callout: none;
		-moz-touch-callout: none;
		-ms-touch-callout: none;
		touch-callout: none;
	}

	uni-page-body {
		display: flex;
		height: 100%;
	}

	.content {
		position: relative;
		background: #eee;
		width: 100%;
		padding-bottom: 128rpx;
	}
  .clo{
    background-color: #fff;
  }

	.address_list {
		padding-bottom: 20rpx;
		
	}

	.list {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-content: flex-start;
    margin:auto 0;
		padding: 30rpx 30rpx;
		background: #fff;
		position: relative;
		&:first-child{
      margin: 20rpx auto 0;
    }
		.addrEdit{
			display: flex;
			margin-top: 10rpx;
			width: 100%;
			justify-content: flex-end;
			align-items: center;
			font-size: 24rpx;
		}
		.addrEdit .right{
			display: flex;
			align-items: center;
			
		}
		.addrEdit .right .edit{
			margin-right: 30rpx;
			
		}
		.addrEdit .editBtn{
			margin-left: -2rpx;
			height: 48rpx;
			/* width: 146rpx; */
			/* text-align: center; */
			border-radius: 6rpx;
			color: #fff;
			background-color: #FD8F2F;
			line-height: 48rpx;
			font-size: 26rpx;
		}
		.addrEdit .editBtn1{
			margin-left: -2rpx;
			height: 48rpx;
			/* width: 146rpx; */
			/* text-align: center; */
			border-radius: 6rpx;
			color: #fff;
			/* background-color: #FD8F2F; */
			line-height: 48rpx;
			font-size: 26rpx;
		}
		.addrEdit image{
			width: 30rpx;
			height: 30rpx;
			margin-right: 6rpx;
			vertical-align: middle;
		}

		&.b-b {
			&:after {
				position: absolute;
				z-index: 3;
				left: 20rpx;
				right: 0;
				height: 0;
				content: '';
				-webkit-transform: scaleY(0.5);
				transform: scaleY(0.5);
				border-bottom: 1px solid rgba(0, 0, 0, .1);
			}
		}

		.mask {
			position: absolute;
			z-index: 4;
			left: 0;
			right: 0;
			top: 0;
			bottom: 0;
			background: rgba(0, 0, 0, .6);

			view {
				width: 166rpx;
				height: 90rpx;
				border-radius: 45rpx;
				color: #fff;
				font-size: 34rpx;

				&.edit {
					background: linear-gradient(-90deg, rgba(254, 152, 32, 1), rgba(255, 183, 43, 1));
				}

				&.del {
					background: linear-gradient(-90deg, rgba(252, 29, 28, 1), rgba(255, 122, 24, 1));
					margin-left: 80rpx;
				}
			}

		}
	}

	.wrapper {
		flex: 1;
		width: 100%;
		background: #fff;

		.iconfont {
			color: $main-color;
			font-size: 32rpx;
			margin-right: 30rpx;
		}
	}

	.address-box {
		display: flex;
		align-items: center;

		.address {
			font-size: 26rpx;
			color: main-font-color;
			line-height: 38rpx;
			margin-top: 5rpx;
			word-break: break-all;
		}
	}
	

	.u-box {
		font-size: 30rpx;
		color: $font-color-light;
		color: $main-font-color;
		font-weight: bold;

		.name {
			margin-right: 40rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			max-width: 240rpx;
		}

		.tag {
			width: 63rpx;
			height: 30rpx;
			margin-left: 20rpx;
		}
	}

	.icon-bianji {
		display: flex;
		align-items: center;
		height: 80rpx;
		font-size: 40rpx;
		color: $font-color-light;
		padding-left: 30rpx;
	}

	.add_btn_bottom {
		position: fixed;
		width: 750rpx;
		height: 168rpx;
		bottom: 0;
		padding: 40rpx 0;
		background: #eee;
		margin: 0 auto;
		z-index: 95;

		.add_btn {
			width: 664rpx;
			font-size: 34rpx;
			color: #fff;
			height: 88rpx;
			background: #FD8F2F;
			border-radius: 44rpx;
			letter-spacing: 1rpx;
		}
	}

	.empty_part {
		display: flex;
		flex: 1;
		width: 100%;
		height: 100%;
		background: #fff;

		.img {
			width: 210rpx;
			height: 210rpx;
			margin-bottom: 37rpx;
			margin-top: 88rpx;
		}

		.tip_con {
			color: $main-third-color;
			font-size: 26rpx;
		}

		.ope_btn {
			color: $main-color;
			font-size: 28rpx;
			padding: 0 25rpx;
			height: 54rpx;
			background: rgba(252, 28, 28, .1);
			border-radius: 27rpx;
			margin-top: 20rpx;
		}
	}
</style>
