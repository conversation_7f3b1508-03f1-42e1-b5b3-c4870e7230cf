<template>
	<view class="container" :style="mix_diyStyle">
		<view class="itembox flex_row_center_center" @click="goShop">
			切换到买家版
		</view>
		<view class="itembox flex_row_center_center" @click="loginOutDialog(true)">
			退出登录
		</view>

		<uni-popup ref="popup" type="dialog">
			<uni-popup-dialog type="input" :title="$L('提示')" :content="$L('确定要退出登录吗?')" :duration="2000"
				@close="loginOutDialog(false)" @confirm="confirmLoginOut"></uni-popup-dialog>
		</uni-popup>
	</view>
</template>

<script>
	import uniPopup from '@/components/uni-popup/uni-popup.vue'
	import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue'
	import { mapMutations, mapState } from 'vuex'
	export default {
		name: "set",
		data() {
			return {}
		},
		components: {
			uniPopup,
			uniPopupDialog
		},
		onLoad(option) {

		},
		methods: {
			...mapMutations(['login', 'logout', 'setUserCenterData', 'userInfo']),
			goShop() {
				uni.switchTab({
					url: '/pages/index/index'
				})
			},
			loginOutDialog(type) {
				if (type) {
					this.$refs.popup.open()
				} else {
					this.$refs.popup.close()
				}
			},
			//退出登录
			confirmLoginOut() {
			  this.$request({
			    url: 'v3/frontLogin/oauth/logout',
			    method: 'POST',
			    data: {
			      refresh_token: this.userInfo.refresh_token
			    }
			  })
			    .then((res) => {
			      this.logout()
			      uni.reLaunch({
			      	url: '/pages/user/user'
			      })
			    })
			    .catch((e) => {})
			},
		}
	}
</script>

<style lang='scss' scoped>
	page {
		background: #F6F6F6;
		width: 750rpx;
		margin: 0 auto;
	}

	.container {
		margin-top: 28rpx;
		background-color: #fff;
		font-size: 28rpx;

		.itembox {
			height: 96rpx;
			color: #101010;
			border-bottom: 2rpx solid #F6F6F6;
		}
	}
</style>