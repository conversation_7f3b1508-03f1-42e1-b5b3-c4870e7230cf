<template>
	<view :style="mix_diyStyle">
		<view class="container">
			<!-- 头部开始 -->
			<!-- #ifdef MP -->
			<view class="outer1" :style="{ paddingTop: menuButtonTop, height: 'calc(280rpx + ' + menuButtonTop + ' + ' + menuButtonHeight + ')' }">
				<view class="bd2" :style="{ marginTop: 'calc(' + menuButtonHeight + ' + 10rpx' + ')' }">
					<!-- #endif -->
					<!-- #ifndef MP -->
					<view class="outer1">
						<view class="bd2">
							<!-- #endif -->
							<view class="left">
								<img class="pic1" referrerpolicy="no-referrer" :src="storeDetail.storeLogoPath" />
								<text class="word3">{{ storeDetail.storeName }}</text>
							</view>
							<view class="main2">
								<!-- 	<view class="store_btn" @click="goPage('/merchants/pages/index/inputOff')">
									<image :src="imgUrl + 'business/store_edit.png'" mode=""></image>
									<text class="info1">输入核销</text>
								</view>
								<view class="store_btn" @click="goPage('/merchants/pages/index/scanOff')">
									<image :src="imgUrl + 'business/store_scan.png'" mode=""></image>
									<text class="info1">扫码核销</text>
								</view> -->
								<view class="store_btn" @click="goPage('/merchants/pages/index/set')">
									<image :src="imgUrl + 'business/store_set.png'" mode=""></image>
									<text class="info1">设置</text>
								</view>
							</view>
						</view>
					</view>
					<!-- 头部结束 -->

					<view class="main-content">
						<view class="order-part flex_row_between_center" style="height: 170rpx; padding: 30rpx 128rpx">
							<view style="text-align: center" @click="goPage('/merchants/pages/index/inputOff')">
								<image :src="imgUrl + 'business/edit_icon.png'" mode="" style="width: 48rpx; height: 48rpx"></image>
								<view style="margin-top: 26rpx; font-size: 12px">输入核销</view>
							</view>
							<view style="text-align: center" @click="goPage('/merchants/pages/index/scanOff')">
								<image :src="imgUrl + 'business/scan_icon.png'" mode="" style="width: 48rpx; height: 48rpx"></image>
								<view style="margin-top: 26rpx; font-size: 12px">扫码核销</view>
							</view>
						</view>
						<view class="order-part flex_row_between_center" style="height: 192rpx" v-if="presentData.sellerWaitDeal">
							<view class="main3">
								<text class="word word4 bold font_20">{{ presentData.sellerWaitDeal.payOrderNum ? presentData.sellerWaitDeal.payOrderNum : 0 }}</text>
								<text class="word txt3">{{ $L('待付款') }}</text>
							</view>
							<view class="main4"></view>
							<view class="main5">
								<text class="word word5 bold font_20">{{ presentData.sellerWaitDeal.deliverOrderNum ? presentData.sellerWaitDeal.deliverOrderNum : 0 }}</text>
								<text class="word info2">{{ $L('待发货') }}</text>
							</view>
							<view class="main6"></view>
							<view class="main7">
								<text class="word word6 bold font_20">{{ presentData.sellerWaitDeal.orderReturnNum ? presentData.sellerWaitDeal.orderReturnNum : 0 }}</text>
								<text class="word txt4">{{ $L('退款/售后') }}</text>
							</view>
						</view>
						<view class="order-part flex_column_start_start">
							<view class="title flex_row_between_center">
								<text class="left">{{ $L('外卖订单') }}</text>
							</view>
							<view class="detail flex_row_around_start">
								<view class="wait-pay item flex_column_center_center">
									<text class="show-text bold font_20">{{ storeDeliveryData.orderSize }}</text>
									<text class="show-text" style="margin-top: 30rpx">{{ $L('支付订单数') }}</text>
								</view>
								<view class="wait-pay item flex_column_center_center">
									<text class="show-text bold font_20">
										{{ storeDeliveryData.orderAmount ? $formatW(storeDeliveryData.orderAmount) : 0 }}
									</text>
									<text class="show-text" style="margin-top: 30rpx">{{ $L('支付订单金额') }}</text>
								</view>
							</view>
						</view>

						<view class="order-part flex_column_start_start">
							<view class="title flex_row_between_center">
								<text class="left">{{ $L('今日') }}</text>
							</view>
							<view class="detail flex_row_around_start" v-if="presentData.simpleTodaySummary">
								<view class="wait-pay item flex_column_center_center">
									<text class="show-text bold font_20">{{ presentData.simpleTodaySummary.orderPayNum ? presentData.simpleTodaySummary.orderPayNum : 0 }}</text>
									<text class="show-text" style="margin-top: 30rpx">{{ $L('支付订单数') }}</text>
								</view>
								<view class="wait-pay item flex_column_center_center">
									<text class="show-text bold font_20">
										{{ presentData.simpleTodaySummary.orderPayAmount ? $formatW(presentData.simpleTodaySummary.orderPayAmount) : 0 }}
									</text>
									<text class="show-text" style="margin-top: 30rpx">{{ $L('支付订单金额') }}</text>
								</view>
							</view>
						</view>

						<view class="order-part flex_column_start_start">
							<view class="title flex_row_between_center">
								<text class="left">{{ $L('近7天') }}</text>
							</view>
							<view class="detail flex_row_around_start" v-if="presentData.simpleWeekSummary">
								<view class="wait-pay item flex_column_center_center">
									<text class="show-text bold font_20">{{ presentData.simpleWeekSummary.orderPayNum ? presentData.simpleWeekSummary.orderPayNum : 0 }}</text>
									<text class="show-text" style="margin-top: 30rpx">{{ $L('支付订单数') }}</text>
								</view>
								<view class="wait-pay item flex_column_center_center">
									<text class="show-text bold font_20">
										{{ presentData.simpleWeekSummary.orderPayAmount ? $formatW(presentData.simpleWeekSummary.orderPayAmount) : 0 }}
									</text>
									<text class="show-text" style="margin-top: 30rpx">{{ $L('支付订单金额') }}</text>
								</view>
							</view>
						</view>

						<view class="order-part flex_column_start_start">
							<view class="title flex_row_between_center">
								<text class="left">{{ $L('当月') }}</text>
							</view>
							<view class="detail flex_row_around_start" v-if="presentData.simpleMonthSummary">
								<view class="wait-pay item flex_column_center_center">
									<text class="show-text bold font_20">{{ presentData.simpleMonthSummary.orderPayNum ? presentData.simpleMonthSummary.orderPayNum : 0 }}</text>
									<text class="show-text" style="margin-top: 30rpx">{{ $L('支付订单数') }}</text>
								</view>
								<view class="wait-pay item flex_column_center_center">
									<text class="show-text bold font_20">
										{{ presentData.simpleMonthSummary.orderPayAmount ? $formatW(presentData.simpleMonthSummary.orderPayAmount) : 0 }}
									</text>
									<text class="show-text" style="margin-top: 30rpx">{{ $L('支付订单金额') }}</text>
								</view>
							</view>
						</view>
					</view>
					<footerTapBar :imgSrc="'index'" />
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import footerTapBar from '../../component/footerTapbar/footer.vue';
import gouser from '../../component/gouser/gouser.vue';
import { mapState, mapMutations } from 'vuex';
export default {
	name: 'goods',
	components: {
		footerTapBar,
		gouser
	},
	data() {
		return {
			// #ifdef MP
			menuButtonHeight: uni.getMenuButtonBoundingClientRect().height + 'px',
			menuButtonTop: uni.getMenuButtonBoundingClientRect().top + 'px',
			// #endif
			imgUrl: getApp().globalData.imgUrl,
			flag: false,
			memberName: '', //会员名
			storeDeliveryData: {}, //店铺外卖信息
			storeDetail: {}, //店铺信息
			presentData: {} //首页数据
		};
	},
	computed: {
		...mapState(['hasLogin', 'userInfo', 'userCenterData'])
	},
	onLoad(option) {
		this.getStoreDetail();
		this.getPresentDataSimple();
		this.getStoreDelivery();
		// this.memberName = option.memberName
	},
	methods: {
		...mapMutations(['login', 'setUserCenterData']),
		goPage(url) {
			uni.navigateTo({
				url
			});
		},
		// 返回
		// goBack(){
		// 	uni.switchTab({
		// 		url:"/pages/user/user"
		// 	})
		// },

		//获取外卖订单数据
		getStoreDelivery() {
			this.$seller_request({
				url: 'v3/business/front/orderInfo/takeawayOrder',
				data: { storeId: this.userCenterData.storeId },
				method: 'GET'
			})
				.then((res) => {
					if (res.state == 200) {
						this.storeDeliveryData = res.data;
					} else {
						this.$api.msg(res.msg);
					}
				})
				.catch((e) => {
					//异常处理
				});
		},
		//获取数据
		getStoreDetail() {
			this.$seller_request({
				url: 'v3/seller/seller/store/detail',
				method: 'GET'
			})
				.then((res) => {
					if (res.state == 200) {
						this.storeDetail = res.data;
					} else {
						this.$api.msg(res.msg);
					}
				})
				.catch((e) => {
					//异常处理
				});
		},
		// 获取首页数据
		getPresentDataSimple() {
			uni.showLoading({
				title: '加载中...'
			});
			this.$seller_request({
				url: 'v3/statistics/seller/overview/analysis/presentDataSimple',
				method: 'GET'
			}).then((res) => {
				uni.hideLoading();
				if (res.state == 200) {
					this.presentData = res.data;
				} else {
					this.$api.msg(res.msg);
				}
			});
		},
		// 登录
		// goLogin() {

		// },
		// 用户信息
		goInfo() {},
		//下载二维码
		dowonCode() {},
		// 跳转
		goOrder() {
			uni.navigateTo({
				url: '/merchants/pages/order/order?isFlag=' + '0' + '&state=' + '10'
			});
		},
		goPay() {
			uni.navigateTo({
				url: '/merchants/pages/order/order?isFlag=' + '0' + '&state=' + '10'
			});
		},
		goShop() {
			uni.navigateTo({
				url: '/merchants/pages/order/order?isFlag=' + '1' + '&state=' + '20'
			});
		},
		goShoped() {
			uni.navigateTo({
				url: '/merchants/pages/order/order?isFlag=' + '2' + '&state=' + '30'
			});
		},
		goFinish() {
			uni.navigateTo({
				url: '/merchants/pages/order/order?isFlag=' + '3' + '&state=' + '40'
			});
		},
		goGoods() {
			uni.navigateTo({
				url: '/merchants/pages/goods/goods'
			});
		},
		goGoodsOne() {
			uni.navigateTo({
				url: '/merchants/pages/goods/goods?isFlag=0'
			});
		},
		goGoodsTwo() {
			uni.navigateTo({
				url: '/merchants/pages/goods/goods?isFlag=1'
			});
		},
		goGoodsThree() {
			uni.navigateTo({
				url: '/merchants/pages/goods/goods?isFlag=2'
			});
		},
		goAddress() {
			uni.navigateTo({
				url: '/merchants/pages/shopaddress/address'
			});
		},
		goTransform() {
			uni.navigateTo({
				url: '/merchants/pages/transform/transform'
			});
		},
		show() {
			this.flag = true;
		},
		unShow() {
			this.flag = false;
		}
	}
};
</script>

<style lang="scss" scoped>
.font_20 {
	font-size: 40rpx !important;
}

page {
	background: #f5f5f5;
	width: 750rpx;
	margin: 0 auto;
}

.bold {
	font-weight: bold;
}

.container {
	height: 100%;
	background: #f5f5f5;
	padding-bottom: 130rpx;
}

.outer1 {
	width: 100%;
	/* #ifdef APP-PLUS */
	padding-top: var(--status-bar-height);
	height: calc(280rpx + var(--status-bar-height));
	/* #endif */
	/* #ifndef APP-PLUS */
	height: 280rpx;
	/* #endif */
	border: 1px solid #ffffff;
	background: linear-gradient(180.16deg, rgba(60, 120, 233, 1) -4.65%, rgba(255, 255, 255, 0) 209.53%);
}

.bd2 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
	/* height: 136rpx; */
	margin: 60rpx auto 40rpx;
}

.left {
	display: flex;
	align-items: center;
	flex: 1;
}

.pic1 {
	width: 64rpx;
	height: 64rpx;
	border-radius: 50%;
	margin: 0 20rpx 0 46rpx;
}

.main1 {
	display: flex;
	height: 90rpx;
	flex-direction: column;
}

.word2 {
	width: 158rpx;
	color: #101010;
	font-size: 32rpx;
	font-family: SourceHanSansCN-Bold;
	white-space: nowrap;
	align-self: flex-start;
}

.word3 {
	max-width: 300rpx;
	color: #fff;
	font-size: 32rpx;
	font-family: SourceHanSansCN-Regular;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.main2 {
	display: flex;

	.store_btn {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: center;
		font-size: 24rpx;
		margin-right: 14rpx;
		color: #fff;

		image {
			width: 40rpx;
			height: 40rpx;
			margin-bottom: 12rpx;
		}
	}
}

.info1 {
}

.bd3 {
	display: flex;
	align-items: center;
	z-index: auto;
	width: 100%;
	height: 68px;
	margin: 10rpx auto;
}

.main3 {
	z-index: auto;
	flex: 1;
	height: 68px;
}

.word4 {
	width: 76px;
	height: 32px;
	display: block;
	overflow-wrap: break-word;
	color: #101010;
	font-size: 28rpx;
	font-family: SourceHanSansCN-Bold;
	white-space: nowrap;
	line-height: 32px;
	align-self: center;
}

.txt3 {
	width: 96px;
	height: 24px;
	display: block;
	overflow-wrap: break-word;
	color: #101010;
	font-size: 26rpx;
	font-family: SourceHanSansCN-Regular;
	white-space: nowrap;
	line-height: 24px;
}

.main4 {
	width: 1px;
	height: 40px;
}

.main5 {
	flex: 1;
	height: 68px;
}

.word5 {
	z-index: 24;
	width: 95px;
	height: 32px;
	display: block;
	overflow-wrap: break-word;
	color: #101010;
	font-size: 28rpx;
	font-family: SourceHanSansCN-Bold;
	white-space: nowrap;
	line-height: 32px;
	align-self: flex-end;
}

.info2 {
	width: 96px;
	height: 24px;
	display: block;
	overflow-wrap: break-word;
	color: #101010;
	font-size: 26rpx;
	font-family: SourceHanSansCN-Regular;
	white-space: nowrap;
	line-height: 24px;
	align-self: flex-start;
}

.main6 {
	width: 1px;
	height: 40px;
}

.main7 {
	flex: 1;
	height: 68px;
}

.word6 {
	width: 38px;
	height: 32px;
	display: block;
	overflow-wrap: break-word;
	color: #101010;
	font-size: 28rpx;
	font-family: SourceHanSansCN-Bold;
	white-space: nowrap;
	line-height: 32px;
	align-self: center;
}

.txt4 {
	width: 72px;
	height: 24px;
	display: block;
	overflow-wrap: break-word;
	color: #101010;
	font-size: 26rpx;
	font-family: SourceHanSansCN-Regular;
	white-space: nowrap;
	line-height: 24px;
}

.word {
	margin: 0 auto;
	text-align: center;
}

/* 我的订单开始 */

.main-content {
	width: calc(100% - 32rpx);
	/* padding: 0 $com-v-border; */
	position: relative;
	top: -88rpx;
	padding-bottom: 40rpx;
	margin: 0 auto;

	.order-part {
		margin-bottom: 20rpx;
		width: 100%;
		height: 250rpx;
		background: #fff;
		box-shadow: 0px 0px 20px 0px rgba(153, 153, 153, 0.15);
		border-radius: 15rpx;

		.title {
			width: 100%;
			height: 90rpx;
			border-bottom: 1px solid #f8f8f8;
			padding-right: 46rpx;
			padding-left: 24rpx;
			box-sizing: border-box;

			.left {
				flex-shrink: 0;
				padding-left: 12rpx;
				color: #4f4f4f;
				font-size: 28rpx;
				line-height: 36rpx;
			}

			.right {
				text {
					flex-shrink: 0;
					color: #666;
					font-size: 28rpx;
					font-weight: 400;
					margin-right: 6rpx;
				}

				image {
					width: 20rpx;
					height: 20rpx;
				}
			}
		}

		.detail {
			flex: 1;
			width: 100%;

			.item {
				position: relative;

				.show-text {
					color: #333333;
					font-size: 28rpx;
					line-height: 28rpx;
					margin-top: 20rpx;
				}

				.nums {
					position: absolute;
					top: -5rpx;
					right: -5rpx;
					background: #ffffff;
					border: 1rpx solid #ff0000;
					border-radius: 11rpx;
					padding: 0rpx 5rpx;
					box-sizing: border-box;
					font-size: 20rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #ff1d2e;
					text-align: center;
					height: 25rpx;
					line-height: 25rpx;
					display: flex;
					align-items: center;
					justify-content: center;
				}

				.num {
					position: absolute;
					top: -5rpx;
					right: -5rpx;
					width: 25rpx;
					height: 25rpx;
					background: #ffffff;
					border: 1rpx solid #ff0000;
					border-radius: 50%;
					font-size: 20rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #ff1d2e;
					line-height: 25rpx;
					text-align: center;
					display: flex;
					align-items: center;
					justify-content: center;
				}

				image {
					width: 67rpx;
					height: 58rpx;
				}
			}
		}
	}
}

/* 我的订单结束 */

.shop_center {
	margin: 0 50rpx;
}

/* 常用服务 start */
.common_services {
	width: 100%;
	min-height: 485rpx;
	background: #ffffff;
	border-radius: 15rpx;
	margin: 20rpx auto 0;

	.common_services_title {
		width: 100%;
		height: 92rpx;
		border-bottom: 1rpx solid #f8f8f8;
		font-size: 34rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #2d2d2d;
		line-height: 92rpx;
		padding-left: 30rpx;
	}

	.common_services_list {
		display: flex;
		flex-direction: column;

		.common_services_pre {
			height: 130rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin: 0 42rpx;
			border-bottom: 1rpx solid #f8f8f8;

			&:nth-last-child(1) {
				border-bottom: none;
			}

			.common_serv_pre_left {
				display: flex;
				align-items: center;

				image {
					width: 51rpx;
					height: 51rpx;
				}

				text {
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #2d2d2d;
					line-height: 39rpx;
					margin-left: 25rpx;
				}
			}

			.common_serv_pre_right {
				display: flex;
				align-items: center;

				text {
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: bold;
					color: #333333;
					line-height: 39rpx;
					margin-right: 20rpx;
				}

				image {
					width: 26rpx;
					height: 26rpx;
				}
			}
		}
	}
}

/* 常用服务 end */

/* 店铺码 */
.box {
	position: fixed;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	padding: 40rpx 0;
	background-color: rgba($color: #000, $alpha: 0.5);
}

.code {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-content: space-around;
	width: 66%;
	margin: 200rpx auto;
	background-color: #ffffff;
	border-radius: 20rpx;
	text-align: center;
	position: relative;
}

.download {
	position: absolute;
	bottom: 300rpx;
	left: 50%;
	transform: translateX(-50%);
	display: flex;
	flex-direction: column;
	align-items: center;

	image {
		width: 92rpx;
		height: 92rpx;
		margin-bottom: 20rpx;
	}

	text {
		color: #ffffff;
		font-size: 28rpx;
		text-align: left;
		font-family: PingFangSC-regular;
	}
}

.code .code_word {
	margin: 30rpx auto;
	text-align: center;
	font-size: 28rpx;
}

.code .codeUp {
	border: 1rpx dashed #666;
	width: 400rpx;
	height: 400rpx;
	margin-bottom: 40rpx;
}

.delete {
	width: 80rpx;
	height: 80rpx;
	position: absolute;
	right: 64rpx;
	top: 208rpx;
}

.delete image {
	width: 48rpx;
	height: 48rpx;
}

.down {
	display: flex;
	justify-content: center;
	align-items: center;
	/* width: 44% !important;
		height: 96rpx !important; */
	padding: 12rpx;
	border: 1px solid #999;
	border-radius: 6rpx;
	margin: 30rpx auto;
	font-size: 28rpx;
}

.down .codeDown {
	width: 24rpx !important;
	height: 24rpx;
	margin-right: 10rpx;
}

/* 	.goBack{
		position: fixed;
		right: 60rpx;
		bottom: 150rpx;
		width: 50rpx;
		height: 50rpx;
		border-radius: 50%;
		background-color: #000;
	} */
</style>
