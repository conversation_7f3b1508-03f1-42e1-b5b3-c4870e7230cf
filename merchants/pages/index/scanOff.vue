<template>
	<view class="container">
		<!-- #ifdef MP-WEIXIN -->
		<view class="midddle-box">
			<span class="one" />
			<span class="two" />
			<span class="three" />
			<span class="four" />
			<camera @scancode="onscanCode" mode="scanCode" class="set-camera" device-position="back" flash="off"
				@error="error"></camera>
		</view>
		<!-- #endif -->

		<!-- #ifdef H5 -->
		<mumu-one-code @success='handlerSuccess' :continue="true" :isPadding="isWriteOff" :readers='["code_128_reader"]'></mumu-one-code>
		<!-- #endif -->
		
		<!-- #ifdef APP -->
		<appScan @scanResult="appScanResult" ref="appScan"></appScan>
		<!-- #endif -->

		<view class="inputBox">
			<input type="text" :focus="focus" v-model="code" @blur="handleBlur" @confirm="verifyCode"
				v-if="showInput">
			<view class="tipBox" v-else @click="toShowInput">
				<image :src="imgUrl + 'business/edit_icon.png'" mode=""></image>
				<text>输入核销</text>
			</view>
		</view>
	</view>
</template>

<script>
	import mumuOneCode from '../../component/mumu-oneCode/mumu-oneCode.vue'
	import appScan from '../../component/appScan.vue'
	export default {
		name: "set",
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				showInput: false,
				code: '',
				isflag: false,
				isWriteOff: false, //是否正在请求核销接口
				focus: false,
			}
		},
		components: {
			mumuOneCode,
			appScan
		},
		onLoad(option) {

		},
		methods: {
			toShowInput() {
				this.showInput = true
				this.$nextTick(() => {
					this.focus = true
				})
			},
			handlerSuccess(e) {
				this.code = e
				this.verifyCode()
			},
			onscanCode(e) {
				this.code = e.detail.result
				this.verifyCode()
			},
			error(e) {
				console.log(e,'eee')
			},
			appScanResult(e) {
				this.code = e.result
				this.verifyCode()
			},
			scanHandle(e) {
				if (this.isflag) {
					return
				}
				if (e) {
					if (!e.target.result) {
						this.$api.msg('核销码无效')
						this.isflag = false
						return
					}
					console.log(e.target.result);
					console.log(e);
					this.isflag = true
				}
			},
			verifyCode() {
				if (this.isWriteOff) return
				this.isWriteOff = true
				if (!this.code) return this.$api.msg('请输入核销码')
				let params = {}
				params.url = 'v3/business/seller/orderInfo/codeDetail'
				params.data = {
					pickupCode: this.code
				}
				this.$seller_request(params).then(res => {
					if (res.state == 200) {
						this.confirm(res.data.orderSn)
					} else {
						this.$api.msg(res.msg)
						this.isWriteOff = false
						// #ifdef APP
						this.$refs.appScan.startScan()
						// #endif
					}
				}).catch((e) => {
					this.$api.msg('核销失败，请重试')
					this.isWriteOff = false
					// #ifdef APP
					this.$refs.appScan.startScan()
					// #endif
				})
			},
			// 确认核销
			confirm(orderSn) {
				// uni.showLoading({
				// 	title: '加载中'
				// })
				let params = {}
				params.url = 'v3/business/seller/orderInfo/writeOff'
				params.method = 'POST'
				params.data = {
					orderSn
				}
				this.$seller_request(params).then(res => {
					// uni.hideLoading()
					this.$api.msg(res.msg)
					this.code = ''
					if (res.state == 200) {
						setTimeout(() => {
							this.isWriteOff = false
							this.$back()
						}, 1000)
					} else {
						// #ifdef APP
						this.$refs.appScan.startScan()
						// #endif
					}

				}).catch(() => { 
					this.$api.msg('核销失败，请重试')
					this.isWriteOff = false
					// #ifdef APP
					this.$refs.appScan.startScan()
					// #endif
				})
			},
			handleBlur(e) {
				if (!this.code) {
					this.showInput = false
					this.focus = false
				}
			},
			error(e) {
				console.log(e.detail);
			}
		}
	}
</script>

<style lang='scss' scoped>
	page {
		height: 100%;
	}

	.inputBox {
		width: 346rpx;
		height: 60rpx;
		border-radius: 30rpx;
		background-color: #474747;
		display: flex;
		justify-content: center;
		align-items: center;
		position: absolute;
		bottom: 126rpx;
		left: 50%;
		transform: translateX(-50%);
		z-index: 9999;

		/* margin: 386rpx auto 0; */
		input {
			width: 150rpx;
			height: 60rpx;
		}

		.tipBox {
			display: flex;
			color: #3C78E9;
			font-size: 24rpx;

			image {
				width: 28rpx;
				height: 28rpx;
				margin-right: 20rpx;
			}
		}
	}

	.container {
		height: 100vh;
		font-size: 28rpx;
		background-color: #000000;
		/* #ifndef H5 */
		padding-top: 326rpx;
		/* #endif */
	}

	.midddle-box {
		width: 400rpx;
		height: 400rpx;
		margin: 0 auto;
		position: relative;

		.set-camera {
			position: absolute;
			width: calc(100% - 20rpx);
			height: calc(100% - 20rpx);
			top: 10rpx;
			left: 10rpx;
		}

		span {
			position: absolute;
			width: 72rpx;
			height: 72rpx;
		}

		.one {
			border-top: 10rpx solid #3C78E9;
			border-left: 10rpx solid #3C78E9;
			top: 0;
			left: 0;
		}

		.two {
			border-top: 10rpx solid #3C78E9;
			border-right: 10rpx solid #3C78E9;
			top: 0;
			right: 0;
		}

		.three {
			border-left: 10rpx solid #3C78E9;
			border-bottom: 10rpx solid #3C78E9;
			bottom: 0;
			left: 0;
		}

		.four {
			border-right: 10rpx solid #3C78E9;
			border-bottom: 10rpx solid #3C78E9;
			bottom: 0;
			right: 0;
		}
	}
</style>