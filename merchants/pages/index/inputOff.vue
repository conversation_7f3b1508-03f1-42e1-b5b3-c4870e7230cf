<template>
	<view class="container">
		<view class="searchInput">
			<input type="text" v-model="pickupCode" placeholder="请输入核销码" />
		</view>
		<view class="searchBtn" @click="searchOrder">查找订单</view>
	</view>
</template>

<script>
	export default {
		name: "set",
		data() {
			return {
				pickupCode: '',
			}
		},
		computed: {
		},
		onLoad() {
	
		},
		methods: {
			searchOrder() {
				if (!this.pickupCode) return this.$api.msg('请输入核销码')
				let params = {}
				params.url = 'v3/business/seller/orderInfo/codeDetail'
				params.data = {
					pickupCode: this.pickupCode
				}
				this.$seller_request(params).then(res => {
					this.$api.msg(res.msg)
					if (res.state == 200) {
						this.$Router.push({
							path: '/merchants/pages/order/order_detail',
							query: {
								orderSn: res.data.orderSn
							}
						})
					}
				})
			}
		}
	}
</script>

<style lang='scss' scoped>
	.container{
		width: calc(100% - 44rpx);
		margin: 0 auto;
		margin-top: 400rpx;
		font-size: 28rpx;
		.searchInput{
			position: relative;
			.sea_btn{
				position: absolute;
				width: 70rpx;
				height: 70rpx;
				top: 24rpx;
				right: 24rpx;
			}
			input{
				height: 118rpx;
				width: 100%;
				background-color: #F6F6F6;
				color: #888888;
				font-size: 40rpx;
				border-radius: 6rpx;
				padding: 0 94rpx 0 16rpx;
			}
		}
		.searchBtn{
			height: 84rpx;
			line-height: 84rpx;
			border-radius: 4rpx;
			background-color: rgba(60, 120, 233, 1);
			color: #fff;
			font-size: 28rpx;
			text-align: center;
			margin-top: 70rpx;
		}
	}
</style>
