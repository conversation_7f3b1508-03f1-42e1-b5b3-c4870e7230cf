<template>
	<view class="container">
		<view class="top_nav">
			<!-- #ifdef APP-PLUS -->
			<view class="con_top flex_row_center_center">商品管理</view>
			<!-- #endif -->
			<!-- #ifdef MP -->
			<view class="con_top flex_row_center_center" :style="{ marginTop: 'calc(' + menuButtonTop + ' - 10rpx' + ')' }">商品管理</view>
			<!-- #endif -->
			<view class="top_container">
				<view class="searchBox">
					<input type="text" v-model="param.goodsName" @confirm="search" placeholder="请输入商品标题进行搜索" />
					<image :src="imgUrl + 'business/search_small.png'" @tap.stop="search" class="sea_btn" mode=""></image>
				</view>
			</view>
			<!-- 商品状态tap切换开始 -->
			<view class="top_tap">
				<view v-for="(item,index) in tapList" :key="index" class="word" @tap.stop="changeTap" :data-index="index">
					<span class="active_tab" v-if="isFlag == index">{{item}}</span>
					<span v-else>{{item}}</span>
				</view>
			</view>
			<!-- 商品状态tap切换结束 -->
		</view>


		<!-- 商品内容区开始 -->
		<!-- #ifndef MP -->
		<view class="goods_article">
		<!-- #endif -->
			<!-- #ifdef MP -->
			<view class="goods_article"
				:style="{ paddingTop: 'calc(' + '298rpx' + ' + ' + menuButtonTop + ' - 10rpx' + ')' }">
			<!-- #endif -->
			<view class="goods_item" v-for="(goodsItem,goodsindex) in goods_info_list" :key="goodsindex">
				<view class="goods_info">
					<view class="goods_info_left">
						<view v-if="!goodsItem.mainImage"></view>
						<image :src="goodsItem.mainImage" mode="aspectFit" v-if="goodsItem.mainImage"></image>
					</view>
					<view class="goods_info_center">
						<view class="goodstext">{{goodsItem.goodsName}}</view>
						<view>
							<view class="goods_price">¥{{Number(goodsItem.goodsPrice).toFixed(2)}}</view>
							<view class="info_bottom">
								<text class="goods_date">库存：{{goodsItem.goodsStock}}</text>
							</view>
						</view>
					</view>
					<view class="goods_info_right">
						<view class="goods_info_btn" @tap.stop="goShelves" :data-goodsids="goodsItem.goodsId"
							data-type="shelves" :data-state="goodsItem.state"
							v-if="goodsItem.state == 3 || goodsItem.state == 5 || goodsItem.state == 11 || goodsItem.state == 12 ">
							{{goodsItem.state==3? '下架' : '上架'}}
						</view>
						<view class="goods_info_btn" @tap.stop="goodsEdit" :data-goodsids="goodsItem.goodsId">编辑</view>
						<view class="goods_info_btn" @tap.stop="delgoods" :data-goodsids="goodsItem.goodsId"
							data-type="del">删除</view>
					</view>

				</view>
				<view class="w_reason flex_row_start_center" v-if="goodsItem.state == 6">
					违规原因：{{ goodsItem.offlineReason }},{{ goodsItem.offlineComment }}
				</view>
			</view>
		</view>
		<!-- 商品内容结束 -->
		<view class="bottom_act flex_row_center_center" :style="{ bottom: 'calc(' + '98rpx' + ' + ' + bottomSateArea + ' + ' + bottomPaddingTop + ')' }">
			<view class="btn" @click="navto">发布商品</view>
		</view>
		<!-- 底部tapBar切换组件 -->
		<footerTapBar :imgSrc="'goods'" />
		<!-- 删除弹出层 -->
		<uni-popup ref="popup" type="dialog" :mask-click="false" class="popupDig">
			<uni-popup-dialog :title="popTit" :content="content" :duration="2000" :before-close="true" @close="close"
				@confirm="confirm"></uni-popup-dialog>
		</uni-popup>

		<loadingState v-if="loadingState == 'first_loading' || goods_info_list.length > 0" :state='loadingState'/>
		<view v-if="loadingState != 'first_loading'&& goods_info_list.length == 0"
			class="empty_part flex_column_start_center">
			<image :src="imgUrl+'empty_orders.png'" />
			<text>{{$L('这里空空如也~')}}</text>
		</view>
	</view>
</template>

<script>
	import loadingState from "@/components/loading-state.vue";
	import uniPopup from '@/components/uni-popup/uni-popup.vue'
	import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue'
	import footerTapBar from '../../component/footerTapbar/footer.vue'
	export default {
		name: "goods",
		components: {
			loadingState,
			footerTapBar,
			uniPopup,
			uniPopupDialog
		},
		onLoad(e) {
			this.getIniList() //获取列表数据
		},
		data() {
			return {
				// #ifdef MP
				menuButtonHeight: uni.getMenuButtonBoundingClientRect().height + 'px',
				menuButtonTop: uni.getMenuButtonBoundingClientRect().top + 'px',
				// #endif
				bottomSateArea:getApp().globalData.bottomSateArea ? getApp().globalData.bottomSateArea : '0rpx',//iphone手机底部一条黑线的高度
				bottomPaddingTop: getApp().globalData.bottomSateArea ? '10rpx' : '0rpx',
				goods_info_list: [],
				imgUrl: getApp().globalData.imgUrl,
				isFlag: 0, //控制tap切换下边下划线显示与隐藏
				goodsList: [], //商品循环数组
				tapList: ['出售中', '仓库中', '待审核', '违规商品'],
				store_url: getApp().globalData.storeUrl, //接口地址
				type: '', //控制弹窗显示内容
				goodsIds: '', //点击商品的id
				param: {
					state: 3,
					current: 1,
					pageSize: 10,
					goodsName: '',
				}, //请求参数
				content: '', //弹窗显示内容
				popTit: '', //弹窗显示标题
				total: '', //总条目数
				hasMore: true, //是否还有数据
				auditComment: '', //审核失败原因
				state: '', //审核状态
				loadingState: 'first_loading',
			}
		},
		methods: {
			//控制tap切换
			changeTap(e) {
				const index = e.currentTarget.dataset.index
				this.isFlag = index
				this.param.current = 1
				if (index == 0) {
					this.param.state = 3
				} else if (index == 1) {
					this.param.state = 4
				} else if (index == 2) {
					this.param.state = 2
				} else if (index == 3) {
					this.param.state = 6
				}
				this.getIniList()

			},
			//商品编辑
			goodsEdit(e) {
				const goodsId = e.currentTarget.dataset.goodsids
				uni.navigateTo({
					url: '/merchants/pages/goods/goodsRelease?goodsId=' + goodsId
				})
			},
			//删除商品
			delgoods(e) {
				this.type = e.currentTarget.dataset.type
				this.goodsIds = e.currentTarget.dataset.goodsids
				this.popTit = '是否删除该商品？'
				this.content = '删除后无法恢复'
				this.$refs.popup.open()
			},
			//关闭弹窗
			close() {
				this.$refs.popup.close()
			},
			confirm() {
				//确认按钮
				this.$seller_request({
					url: this.type == 'del' ? 'v3/goods/seller/goods/deleteGoods' : ((this.type == 'shelves' &&
							this.state == 3) ? 'v3/goods/seller/goods/lockup' :
						'v3/goods/seller/goods/upperShelf'), //删除接口
					method: 'POST',
					data: {
						goodsIds: this.goodsIds
					},
				}).then(ret => {
					if (ret.state == 200) {
						this.param.current = 1
						this.$refs.popup.close()
						if (this.type == 'del') {
							this.$api.msg('删除成功')
						} else if (this.type == 'shelves' && this.state == 3) {
							this.$api.msg('下架成功')
						} else {
							this.$api.msg('上架成功')
						}
						setTimeout(() => {
							this.getIniList()
						}, 1000)
					}
				})
			},
			//上架下架按钮
			goShelves(e) {
				this.type = e.currentTarget.dataset.type
				this.goodsIds = e.currentTarget.dataset.goodsids
				this.state = e.currentTarget.dataset.state
				console.log(this.type, this.goodsIds, this.state)
				// this.content = this.state == '3' ? '是否下架商品？下架后还可以在仓库中上架！' : '是否上架商品？上架后还可以在出售中下架！'
				if (this.state == '3') {
					this.popTit = '是否下架该商品？'
					this.content = '下架后的商品将放入仓库中'
				} else {
					this.popTit = '是否上架该商品？'
					this.content = '上架后还可以在出售中下架'
				}
				this.$refs.popup.open()
			},
			//获取列表数据
			getIniList() {
				if (this.loadingState === 'loading') {
					//防止重复加载
					return;
				}
				uni.showLoading({
					title: '加载中',
					mask: false
				})
				this.loadingState = this.loadingState == 'first_loading' ? this.loadingState : 'loading';
				this.$seller_request({
					url: 'v3/goods/seller/goods/list',
					method: 'GET',
					data: this.param
				}).then(ret => {
					if (ret.state == 200) {
						if (this.param.current == 1) {
							this.goods_info_list = ret.data.list
						} else {
							this.goods_info_list = this.goods_info_list.concat(ret.data.list)
						}
						this.total = ret.data.pagination.total
						let hasMore = this.$checkPaginationHasMore(ret.data.pagination); //是否还有数据
						if (hasMore) {
							this.param.current++
							this.hasMore = true
						} else {
							this.hasMore = false
						}
						this.loadingState = hasMore ? 'allow_loading_more' : 'no_more_data'
						
						uni.hideLoading()
						uni.stopPullDownRefresh()
					} else {
						this.$api.msg(ret.msg)
					}
				})
			},

			//搜索事件
			search() {
				this.param.current = 1;
				this.getIniList()
			},
			// 跳转发布商品
			navto() {
				uni.navigateTo({
					url: "/merchants/pages/goods/goodsRelease"
				})
			}


		},
		//页面滚动生命周期
		onReachBottom() {
			if (!this.hasMore) return
			this.getIniList()
		},

		//自定义按钮的点击事件
		onNavigationBarButtonTap(e) {
			uni.navigateTo({
				url: "/merchants/pages/goods/goodsRelease"
			})
		},
		//下拉刷新
		onPullDownRefresh() {
			this.param.current = 1
			this.getIniList()
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #F5F5F5;
	}

	::v-deep .uni-popup-dialog .uni-dialog-title .uni-popup__error {
		color: #101010;
	}

	.container {
		height: 100%;
		background-color: #F5F5F5;
		padding-bottom: 260rpx;
		box-sizing: border-box;
		
		.top_nav {
			position: fixed;
			top: 0;
			left: 0;
			z-index: 99;
			background-color: #fff;
			/* #ifdef APP */
			padding-top: var(--status-bar-height);
			/* #endif */
			
			.con_top {
				width: 100%;
				height: 88rpx;
				background-color: #fff;
				color: #000000;
				font-size: 32rpx;
				margin-bottom: 18rpx;
			}
		}

		.top_container {
			height: 104rpx;
			background-color: #fff;
			padding: 14rpx 0;
		}

		.searchBox {
			position: relative;
			width: calc(100% - 48rpx);
			margin: 0 auto;

			.sea_btn {
				position: absolute;
				width: 36rpx;
				height: 36rpx;
				top: 16rpx;
				right: 16rpx;
			}

			input {
				height: 76rpx;
				width: 100%;
				background-color: #F6F6F6;
				color: #888888;
				font-size: 28rpx;
				border-radius: 6rpx;
				padding: 0 52rpx 0 16rpx;
			}
		}

		//商品状态tap切换
		.top_tap {
			display: flex;
			width: 750rpx;
			height: 88rpx;
			background-color: #ffffff;
			font-size: 30rpx;
			justify-content: space-between;
			padding: 0 24rpx;

			.word {
				display: flex;
				justify-content: center;
				align-items: center;
				text-align: center;

				span {
					height: 88rpx;
					line-height: 88rpx;
				}
			}

			.active_tab {
				color: #3C78E9;
				border-bottom: 4rpx solid #3C78E9;
			}
		}

		.goods_article {
			width: 750rpx;
			// margin-top: 10rpx;
			
			/* #ifndef APP-PLUS */
			padding-top: 196rpx;
			/* #endif */
			/* #ifdef APP-PLUS */
			padding-top: calc(300rpx + var(--status-bar-height));
			/* #endif */

			.goods_item {
				padding: 18rpx 24rpx;
				border-bottom: 4rpx solid #F5F5F5;
				box-sizing: border-box;
				background-color: #FFFFFF;

				.w_reason {
					min-height: 80rpx;
					color: #101010;
					font-size: 24rpx;
				}

				.goods_info {
					display: flex;
					position: relative;

					.goods_info_left {
						view {
							width: 140rpx;
							height: 140rpx;
							background-color: #D8D8D8;
							border-radius: 4rpx;
						}

						image {
							width: 140rpx;
							height: 140rpx;
							border-radius: 4rpx;
						}
					}

					.goods_info_center {
						width: 390rpx;
						height: 140rpx;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						margin-left: 10rpx;

						.goodstext {
							// height: 80rpx;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 2;
							overflow: hidden;
							font-size: 24rpx;
							font-family: SourceHanSansCN-Regular, SourceHanSansCN;
							font-weight: 400;
							color: #101010;
						}

						.goods_price {
							font-size: 24rpx;
							color: #9A9A9A;
						}

						.info_bottom {
							display: flex;
							justify-content: space-between;
							margin-top: 4rpx;
						}

						.goods_date {
							font-size: 24rpx;
							color: #9A9A9A;
						}
					}

					.goods_info_right {
						width: 160rpx;
						display: flex;
						flex-direction: column;
						align-items: flex-end;
						justify-content: center;

						.goods_info_btn {
							width: 126rpx;
							height: 46rpx;
							background: #EEF2FE;
							border-radius: 6rpx;
							font-size: 24rpx;
							color: #3C78E9;
							text-align: center;
							line-height: 46rpx;
							margin-bottom: 6rpx;
						}

						.btns {
							color: #FD8F2F;
							background-color: #FFFFFF;
						}

						.faile {
							width: 136rpx;
							display: flex;
							justify-content: space-between;
							align-items: center;
							color: #FF3434;

							image {
								width: 32rpx;
								height: 32rpx;
							}
						}

						.ban {
							background: #ADADAD;
						}
					}

					.reason_mask {
						width: 300rpx;
						height: 120rpx;
						position: absolute;
						top: -18rpx;
						right: 26rpx;
						padding: 30rpx;
						padding-top: 14rpx;
						box-sizing: border-box;
						font-size: 24rpx;
						font-family: SourceHanSansCN-Regular, SourceHanSansCN;
						font-weight: 400;
						color: #2D2D2D;
						display: none;
					}

					.according {
						display: block;
					}
				}
			}
		}

		.reconDialog {
			width: 100%;
			height: 100%;

			.remask {
				width: 100%;
				height: 100%;
				background-color: #333;
				opacity: 0.3;
				position: fixed;
				top: 0;
				z-index: 999;
			}

			.reinfo {
				width: 366rpx;
				padding: 66rpx 32rpx;
				white-space: wrap;
				background-color: #FFFFFF;
				border-radius: 8rpx;
				position: fixed;
				z-index: 9999;
				top: 50%;
				left: 50%;
				transform: translateX(-50%) translateY(-50%);
			}
		}
	}

	.goBack {
		z-index: 99 !important;
	}

	.bottom_act {
		position: fixed;
		height: 132rpx;
		left: 0;
		bottom: 98rpx;
		width: 100%;
		background-color: #fff;
		border: 2rpx solid #fff;
		z-index: 9;

		.btn {
			width: 680rpx;
			height: 64rpx;
			line-height: 64rpx;
			border-radius: 4rpx;
			background-color: rgba(60, 120, 233, 1);
			color: #fff;
			font-size: 28rpx;
			text-align: center;
		}
	}
	
	::v-deep.popupDig {
		.uni-popup-dialog {
			width: 444rpx;
		}
		.uni-dialog-title {
			padding-top: 28rpx;
			padding-bottom: 28rpx;
			
		}
		.uni-dialog-title-text {
			font-size: 28rpx;
		}
		.uni-dialog-content {
			padding-bottom: 30rpx;
		}
		.uni-dialog-content-text {
			font-size: 24rpx;
		}
		.uni-dialog-button-group,
		.uni-border-left{
			border: none;
		}
		.uni-dialog-button-text {
			color: #3C78E9;
		}
		.uni-transition {
			z-index: 9999;
		}
	}
	
	.empty_part {
		padding-top: 108rpx;
	
		image {
			width: 380rpx;
			height: 280rpx;
		}
	
		text {
			color: $main-third-color;
			font-size: 26rpx;
			margin-top: 57rpx;
		}
	
		button {
			width: 245rpx;
			height: 66rpx;
			background: var(--color_halo);
			border-radius: 33rpx;
			color: var(--color_main);
			font-size: 30rpx;
			font-weight: bold;
			margin-top: 29rpx;
			border: none;
		}
	
		uni-button:after {
			border-radius: 200rpx;
			border-color: #fff;
		}
	}
</style>