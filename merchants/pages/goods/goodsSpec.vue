<template>
	<view class="container">
		<view class="container_con">
			<view class="title">设置规格</view>
			<view class="spec_name_list">
				<view class="item" v-for="(item, index) in specInfoList" :key="index">
					<view class="operate-box">
						<view class="left">
							<span>{{ item.specName }}</span>
						</view>
						<view class="right">
							<checkbox-group @change="handleIsSetImgChange($event, item)">
								<label class="flex_row_end_center">
									<checkbox style="transform:scale(0.7)" value="1" :checked="item.isMainSpec ? true : false" />
									<text>设置图片规格</text>
								</label>
							</checkbox-group>
						</view>
					</view>
					<view class="spc-value-list" v-if="item.specValueList.length > 0">
						<view class="spc-item" v-for="(itemValue, indexValue) in item.specValueList" :key="indexValue">
							<input type="text" placeholder="请输入规格值" v-model="itemValue.specValue"
								@blur="handleInputSpecValueConfirm(itemValue, item)"
								@confirm="handleInputSpecValueConfirm(itemValue, item)" />
							<image @click="delSpecValue(item,itemValue, indexValue)" class="close-icon" :src="imgUrl + 'business/clear-icon.png'" mode="">
							</image>
						</view>
					</view>
					<view class="add-spc" @click="addSpecValue(item)">添加规格值</view>
				</view>
			</view>
			<view class="input_spec_name" v-if="isNeedInput">
				<input type="text" placeholder="输入自定义规格项" v-model="inputSpecValue" @blur="handleInputSpecConfirm" @confirm="handleInputSpecConfirm" />
				<image @click="clearInputSpecValue" class="close-icon" :src="imgUrl + 'business/clear-icon.png'" mode=""
					v-if="inputSpecValue"></image>
			</view>
			<view class="add" v-if="specInfoList.length < 3">
				<text @click="addSpecItem">添加规格项</text>
			</view>
			<view class="title">设置价格与库存</view>
			<view class="no-spc flex_row_start_center" v-if="productList.length == 0">无规格</view>
			<view class="spc-container" v-else>
				<view class="pub-item flex_column_start_start" v-for="(item, index) in productList" :key="index">
					<view class="flex_row_between_center pub-item-top">
						<view class="pub-item-tit">{{ item.specValueString }}</view>
						<view class="pub-item-con" @click="setSpcHandle(item, index)">
							<text>{{ item.isSet ? '已设置' : '待设置' }}</text>
							<image class="arrow" :src="imgUrl + 'business/arrow.png'" mode=""></image>
						</view>
					</view>
					<view class="spc-con" v-if="item.isSet">
						<span class="one">售价 <text class="black">￥{{ item.productPrice }}</text></span>
						<span class="one">库存 <text class="black">{{ item.productStock }}件</text></span>
					</view>
				</view>
			</view>
			<view class="bottom_act flex_row_center_center">
				<view class="btn" @click="navto">确定</view>
			</view>
			
		</view>
		<uni-popup ref="popup" type="bottom">
			<view class="popcontent">
				<view class="pop-tit">
					<text class="pop-tit-text">{{ setSpecValue.specValueString }}</text>
					<span class="close" @click="closepop">
						<image :src="imgUrl + 'business/close_icon.png'" mode=""></image>
					</span>
				</view>
				<view class="pob-item">
					<view class="pob-item-tit">
						售价<span class="redStar">*</span>
					</view>
					<view class="pob-item-con">
						<input type="number" class="p_input" v-model="setSpecValue.productPrice" placeholder="请输入售价，单位：元" />
					</view>
				</view>
				<view class="pob-item">
					<view class="pob-item-tit">
						库存<span class="redStar">*</span>
					</view>
					<view class="pob-item-con">
						<input type="number" class="p_input" v-model="setSpecValue.productStock" placeholder="请输入库存" />
					</view>
				</view>
				<checkbox-group @change="handleIsSelect">
					<label class="flex_row_start_center">
						<label class="flex_row_start_center" style="margin-top: 36rpx; padding-left: 20rpx;">
							<checkbox style="transform:scale(0.7)" value="1" />
							<text>默认选中</text>
						</label>
					</label>
				</checkbox-group>
				<view class="bottom_btn flex_row_center_start">
					<view class="btn" @click="publish">确定</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import UploadImg from './uploadImg.vue';
	export default {
		components: {
			UploadImg
		},
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				goodImg: [],
				isNeedInput: false, //当前是否需要输入规格项名称
				inputSpecValue: '', //添加规格项时输入的规格名称
				specInfoList: [], //规格列表
				goodsHaveSpecList: [], //后台已经保存的规格列表
				productList: [], //规格列表
				setSpecValue: {},
				setIndex: 0,
				isAdd: false,
			}
		},
		onLoad() {
			this.specInfoList = uni.getStorageSync('specInfoList') ? uni.getStorageSync('specInfoList') : []
			this.productList = uni.getStorageSync('productList') ? uni.getStorageSync('productList') : []
			this.getGoodsSpecList()
		},
		methods: {
			// 获取已存在的规格列表
			getGoodsSpecList() {
				let params = {}
				params.url = 'v3/goods/seller/goodsSpec/list'
				params.data = {
					current: 1,
					pageSize: 9999,
				}

				this.$seller_request(params).then(res => {
					this.goodsHaveSpecList = res.data.list
				})
			},
			//添加规格项
			addSpecItem() {
				if (this.isNeedInput) return this.$api.msg('请先输入自定义规格项')
				this.isNeedInput = true
			},
			// 确认输入的规格项名称
			handleInputSpecConfirm() {
				if (this.isAdd) return
				this.isAdd = true
				this.$nextTick(() => {
					if (!this.inputSpecValue.trim()) {
						this.$api.msg('请输入自定义规格项')
						this.isAdd = false
						return 
					} 
					if (this.inputSpecValue.length > 6) {
						this.$api.msg('最多输入6个字')
						this.isAdd = false
						return
					}
					// 判断当前规格项是否已经输入
					let haveInput = this.specInfoList.find(v => v.specName == this.inputSpecValue)
					if (haveInput) {
						this.$api.msg('该规格项已经存在了~')
						this.isAdd = false
						return
					}
					let specItem = {
						isMainSpec: 0,
						specId: '',
						specName: this.inputSpecValue,
						isMainSpec: 0,
						specValueList: [],
					}
					// 判断当前输入的项名称是否已存在
					let haveSpec = this.goodsHaveSpecList.find(v => v.specName == this.inputSpecValue)
					if (haveSpec) {
						specItem.specId = haveSpec.specId
						this.specInfoList.push(specItem)
						this.inputSpecValue = ''
						this.isNeedInput = false
						this.isAdd = false
					} else {
						this.addGoodsSpec(specItem)
					}
				})

			},
			addGoodsSpec(specItem) {
				let params = {}
				params.url = 'v3/goods/seller/goodsSpec/add'
				params.method = 'post'
				params.data = {
					sort: 1,
					specName: this.inputSpecValue,
					state: 1,
				}

				this.$seller_request(params).then(res => {
					if (res.state == 200) {
						specItem.specId = res.data
						this.specInfoList.push(specItem)
						this.inputSpecValue = ''
						this.isNeedInput = false
						this.getGoodsSpecList()
					} else {
						this.$api.msg(res.msg)
					}
					this.isAdd = false
				})
			},
			// 添加规格值
			addSpecValue(item) {
				let specValueItem = {
					imageList: [],
					specValue: '',
					specValueId: '',
				}
				item.specValueList.push(specValueItem)
			},
			handleInputSpecValueConfirm(itemValue, item) {
				if (this.isAdd) return
				this.isAdd = true
				if (!itemValue.specValue.trim()) {
					this.$api.msg('请输入规格值')
					this.isAdd = false
					return 
				}
				// 判断当前规格值是否已经输入
				let haveInput = item.specValueList.filter(v => v.specValue == itemValue.specValue)
				if (haveInput.length >= 2) {
					itemValue.specValue = ''
					this.$api.msg('该规格值已经存在了~')
					this.isAdd = false
					return
				}

				// 判断当前输入的值是否已存在
				let haveSpecValueList = this.goodsHaveSpecList.find(v => v.specId == item.specId).valueList
				let haveSpecValue = haveSpecValueList.find(v => v.specValue == itemValue.specValue)
				if (haveSpecValue) {
					itemValue.specValueId = haveSpecValue.specValueId
					this.specCount()
					this.isAdd = false
				} else {
					this.addGoodsSpecValue(itemValue, item)
				}
			},
			// 删除规格值
			delSpecValue(item,itemValue, indexValue) {
				item.specValueList = item.specValueList.filter(v => v.specValueId != itemValue.specValueId)
				console.log(this.specInfoList,'specValueList')
				this.$nextTick(() => {
					this.specCount()
				})
			},
			addGoodsSpecValue(itemValue, item) {
				let params = {}
				params.url = 'v3/goods/seller/goodsSpec/addSpecValue'
				params.method = 'post'
				params.data = {
					specId: item.specId,
					specValues: itemValue.specValue,
				}

				this.$seller_request(params).then(res => {
					if (res.state == 200) {
						itemValue.specValueId = res.data
						this.specCount()
						this.getGoodsSpecList()
					} else {
						this.$api.msg(res.msg)
					}
					this.isAdd = false
				})
			},
			// 是否有规格图
			handleIsSetImgChange(e, item) {
				let values = e.detail.value
				if (values.includes('1')) {
					this.specInfoList.map(v=> {
						v.isMainSpec = 0
					})
					item.isMainSpec = 1
				} else {
					item.isMainSpec = 0
				}
			},
			handleIsSelect(e) {
				let values = e.detail.value
				if (values.includes('1')) {
					this.setSpecValue.isDefault = 1
				} else {
					this.setSpecValue.isDefault = 0
				}
				
			},
			// 获取sku列表
			specCount() {

				let spec_all_data = [];

				for (let i = 0; i < this.specInfoList.length; i++) {
					let item = this.specInfoList[i];
					if (
						item.specValueList &&
						item.specValueList.length > 0 
					) {
						let spec_all_data_item = [];
						for (let j = 0; j < item.specValueList.length; j++) {
							let items = item.specValueList[j];
							if (items.specValueId) {
								spec_all_data_item.push(items);
							} else {
							}
						}
						spec_all_data.push(spec_all_data_item);
					} else {
					}
					this.setColumnData(spec_all_data);

				}
			},
			setColumnData(spec_data) {
				console.log(spec_data, 'spec_data')
				let result = spec_data.reduce((a, b, c) => {
					var res = []
					a.map(x => {
						b.map(y => {
							res.push([...x, y])
						})
					})
					return res
				}, [
					[]
				])
				this.getproductList(result)
			},
			getproductList(result) {
				this.productList = []
				result.map(v => {
					let item = {}
					item.productPrice = '';
					item.productStock = '';
					item.weight = 1;
					item.length = 1;
					item.width = 1;
					item.height = 1;
					item.state = 1;
					item.isDefault = 0;
					item.isSet = false
					item.specInfoList = []
					v.map(x => {
						let newInfoItem = {
							specValue: x.specValue,
							specValueId: x.specValueId
						}
						item.specInfoList.push(newInfoItem)
					})
					item.specValueString = item.specInfoList.map(v => v.specValue).join(',')
					if (item.specInfoList.length > 0) {
						this.productList.push(item)
					}
					

				})
				console.log(this.productList, 'productList')

			},
			publish() {
				const { productPrice, productStock } = this.setSpecValue;
				if (!productPrice) return this.$api.msg('请输入售价')
				if (!productStock) return this.$api.msg('请输入库存')
				this.setSpecValue.isSet = true
				if (this.setSpecValue.isDefault == 1) {
					this.productList.map(v => {
						v.isDefault = 0
					})
					this.productList[this.setIndex] = this.setSpecValue
				} else {
					this.productList[this.setIndex] = this.setSpecValue
				}
				this.closepop()
			},
			navto() {
				if (this.productList.length == 0) return this.$api.msg('请设置商品规格')
				let isHaveValueNoInput = false
				this.specInfoList.map(v => {
					if (isHaveValueNoInput) return
					if (v.specValueList.length == 0) {
						isHaveValueNoInput = true
						this.$api.msg(`请添加${v.specName}的规格值`)
					}
					v.specValueList.map(x => {
						if (isHaveValueNoInput) return
						if (!x.specValue) {
							isHaveValueNoInput = true
							this.$api.msg(`请输入${v.specName}的规格值`)
						}
					})
				})
				if (isHaveValueNoInput) return
				let isSet = this.productList.filter(v => v.isSet == false)
				if (isSet.length > 0) return this.$api.msg('请设置价格与库存')
				uni.setStorageSync('specInfoList', this.specInfoList)
				uni.setStorageSync('productList', this.productList)
				this.$back()
			},
			// 清空输入
			clearInputSpecValue() {
				this.inputSpecValue = ''
			},
			setSpcHandle(item, index) {
				this.setSpecValue = item
				this.setIndex = index
				this.$refs.popup.open()
			},
			closepop() {
				this.setSpecValue = {}
				this.setIndex = 0
				this.$refs.popup.close()
			}
		}
	}
</script>

<style lang="scss" scoped>
	page {
		background-color: #F6F6F6;
	}

	.popcontent {
		height: 654rpx;
		background-color: #fff;
		border-radius: 10rpx 10rpx 0px 0px;
		color: #101010;

		.bottom_btn {
			padding-top: 100rpx;

			.btn {
				width: 714rpx;
				height: 80rpx;
				line-height: 80rpx;
				border-radius: 6rpx;
				background-color: #3C78E9;
				color: #fff;
				font-size: 28rpx;
				text-align: center;
			}
		}

		.pop-tit {
			position: relative;
			text-align: center;
			font-size: 32rpx;
			padding-top: 38rpx;
			padding-bottom: 38rpx;
			
			.pop-tit-text {
				display: inline-block;
				max-width: 500rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}

			.close {
				position: absolute;
				right: 34rpx;
				top: 28rpx;
				font-size: 40rpx;
				image {
					width: 32rpx;
					height: 32rpx;
				}
			}
		}

		.redStar {
			color: red;
		}

		.pob-item2 {
			padding: 20rpx 20rpx 36rpx 22rpx;
			background-color: #fff;
			border-bottom: 2rpx solid #EFEFEF;

			.pob-item2-tit {
				display: flex;
				align-items: center;
				justify-content: space-between;

				.left {
					font-weight: bold;
				}

				.right {
					color: #BEBEBE;
					font-size: 24rpx;
				}
			}

			.pob-item2-con {
				padding-top: 26rpx;

				.text_area {
					width: 100%;
					height: 154rpx;
					border: 2rpx solid #EFEFEF;
					padding: 16rpx;

					::v-deep .uni-textarea-placeholder {
						color: #888888;
					}
				}

				uni-textarea {
					font-size: 28rpx;
				}
			}
		}

		.pob-item {
			height: 108rpx;
			display: flex;
			align-items: center;
			padding: 0 22rpx;
			border-bottom: 2rpx solid #EFEFEF;
			background-color: #fff;

			.pob-item-tit {
				font-weight: bold;
			}

			.pob-item-con {
				flex: 1;
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: flex-end;
				color: #BEBEBE;

				.p_input {
					height: 80rpx;
					width: 100%;
					text-align: right;

					::v-deep .uni-input-placeholder {
						color: #BEBEBE;
						font-size: 28rpx;
					}
				}

				.arrow {
					width: 24rpx;
					height: 24rpx;
					margin-left: 16rpx;
					margin-top: 4rpx;
				}

				@mixin circalRadio() {
					::v-deep .uni-radio-wrapper {
						.uni-radio-input {
							border-radius: 50%;
							margin-right: 10rpx;
						}

						.uni-radio-input-checked {
							background-color: transparent !important;
							border-color: #155BD4 !important;
						}

						.uni-radio-input-checked:before {
							content: "\EA01";
							color: #155BD4;
							background-color: #155BD4;
							font-size: 26rpx;
							border-radius: 50%;
						}
					}
				}

				.circle_radios {
					color: #101010;

					::v-deep uni-label {
						display: inline-block;
						margin-right: 40rpx;
					}

					@include circalRadio();
				}

				.checkBox {
					color: #101010;

					::v-deep uni-label {
						display: inline-block;
						margin-right: 40rpx;
					}

					::v-deep .uni-checkbox-input.uni-checkbox-input-checked {
						background-color: #5287EA;
						border-color: #5287EA !important;

						&:before {
							color: #fff;
						}
					}
				}
			}
		}

	}

	.container {
		height: 100%;
		background-color: #F6F6F6;
		font-size: 28rpx;
		color: #101010;
		.container_con {
			padding-bottom: 230rpx;
		}

		.no-spc {
			padding-left: 30rpx;
			height: 80rpx;
			background-color: #fff;
			margin-bottom: 20rpx;
		}

		.spc-container {
			// padding-bottom: 38rpx;
			background-color: #fff;


			.black {
				color: #000000;
			}
		}

		.pub-item {
			min-height: 80rpx;
			padding: 0 30rpx;
			margin-bottom: 2rpx;
			background-color: #fff;

			.pub-item-top {
				width: 100%;
				height: 80rpx;
			}


			.pub-item-tit {
				max-width: 500rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}

			.pub-item-con {
				flex: 1;
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: flex-end;
				color: #9A9A9A;

				.arrow {
					width: 24rpx;
					height: 24rpx;
					margin-left: 16rpx;
					margin-top: 4rpx;
				}
			}

			.spc-con {
				height: 54rpx;
				background-color: #F6F6F6;
				padding: 10rpx 30rpx;
				color: #9A9A9A;
				width: 100%;
				// width: calc(100% - 60rpx);
				// margin: 0 auto;
				display: flex;
				margin-bottom: 38rpx;

				.one {
					margin-right: 112rpx;
				}
			}
		}

		.title {
			padding: 14rpx 30rpx;
		}

		.item {
			padding: 14rpx 22rpx 24rpx 30rpx;
			background-color: #fff;
			font-size: 24rpx;

			.add-spc {
				width: 142rpx;
				height: 46rpx;
				text-align: center;
				line-height: 46rpx;
				border-radius: 4rpx;
				border: 2rpx solid #EFEFEF;
				color: #333333;
				margin-top: 18rpx;
			}

			.spc-item {
				height: 60rpx;
				border: 2rpx solid #EFEFEF;
				color: #888888;
				position: relative;
				display: flex;
				align-items: center;
				padding-left: 20rpx;
				margin-bottom: 20rpx;

				&:last-child {
					margin-bottom: 0;
				}

				input {
					width: 90%;
					height: 100%;
					color: #101010;
					font-size: 24rpx;
				}

				.close-icon {
					position: absolute;
					width: 24rpx;
					height: 24rpx;
					top: 14rpx;
					right: 14rpx;
				}
			}

			.operate-box {
				display: flex;
				justify-content: space-between;
				margin-bottom: 18rpx;

				.left {}

				.right {
					display: flex;
					align-items: center;

					::v-deep .uni-checkbox-input.uni-checkbox-input-checked {
						background-color: #5287EA;
						border-color: #5287EA !important;

						&:before {
							color: #fff;
						}
					}
				}
			}
		}

		.input_spec_name {
			height: 60rpx;
			border: 2rpx solid #EFEFEF;
			color: #888888;
			position: relative;
			display: flex;
			align-items: center;
			padding-left: 20rpx;
			margin: 20rpx 30rpx;

			input {
				width: 90%;
				height: 100%;
				color: #101010;
				font-size: 24rpx;
			}

			.close-icon {
				position: absolute;
				width: 24rpx;
				height: 24rpx;
				top: 14rpx;
				right: 14rpx;
			}
		}

		.add {
			color: #3C78E9;
			padding: 20rpx 30rpx;
		}

		.bottom_act {
			position: fixed;
			// height: 132rpx;
			left: 0;
			bottom: 98rpx;
			width: 100%;

			.btn {
				width: 680rpx;
				height: 64rpx;
				line-height: 64rpx;
				border-radius: 4rpx;
				background-color: rgba(60, 120, 233, 1);
				color: #fff;
				font-size: 28rpx;
				text-align: center;
			}
		}
	}
</style>