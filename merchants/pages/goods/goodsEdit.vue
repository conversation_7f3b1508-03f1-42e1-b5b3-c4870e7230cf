<template>
	<view class="container">
		<view class="goodsBox">
			<view class="boxTitle piece">基本信息</view>
			<view class="piece">
				<text class="name required">商品分类</text>
				<view class="picker" v-if="categoryList.length>0">
					<picker mode="multiSelector" @columnchange="columnHandle" :range="categoryList" range-key="categoryName"   @change="change">
						<view class="color_1" v-if="!category_name">请选择</view>
						<view v-if="category_name" v-model="category_name">{{category_name}}</view>
						<!-- <input placeholder="请选择" disabled="disabled" name="category_name" v-model="category_name"> -->
					</picker>
				</view>
			</view>
			<view class="piece">
				<text class="name required">{{$L('商品名称')}}</text>
				<input type="text" v-model="goods_name" placeholder="请输入商品名称" placeholder-class="goodsPlace" />
			</view>
			<view class="piece">
				<text class="name ">{{$L('商品广告语')}}</text>
				<input type="text"  v-model="advertisement" placeholder="请输入广告语" placeholder-class="goodsPlace" />
			</view>
			<view class="piece">
				<text class="name">{{$L('品牌')}}</text>
				<view class="picker" >
					<picker :range="goodsBrandList" :value="index" range-key="brandName" @change="change_brandList">
						<view class="color_1" v-if="!brandList">请选择</view>
						<view v-if="brandList" v-model="brandList">{{brandList}}</view>
					</picker>
				</view>
			</view>
		</view>

		<view class="goodsBox">
			<view class="boxTitle piece">检索属性</view>

			<view class="piece" v-for="(item,index1) in goodattribute">
				<text class="name">{{item.attributeName}}</text>
				<view class="picker">
					<picker :range="item.attributeValueList" :data-index="index1" range-key="attributeValue" :value="index" @change="change_attributeList">
						
						<view class="color_1" v-if="!item.names">请选择</view>
						<view v-if="item.names" v-model="attribute_names" >{{item.names}}</view>
					</picker>
				</view>
			</view>

		</view>

		<view class="goodsBox" >
			<view class="boxTitle piece">{{$L('店铺自定义属性')}}</view>
			<view class="piece">
				<text class="name">{{$L('属性分组')}}</text>
				<view class="picker">
					<picker :range="canUseList" :value="index" range-key="groupName" @change="change_groupName">
						<view class="color_1" v-if="!groupName">请选择</view>
						<view v-if="groupName" v-model="groupName" >{{groupName}}</view>
					</picker>
				</view>
			</view>
			<view v-for="(item,index1) in goodsParameter">
				<view class="piece">
					<text class="name">{{item.parameterName}}</text>
					<view class="picker" v-if="item.speclist.length > 0">
						<picker :range="item.speclist" :data-index="index1" :value="index"  @change="change_parameterName">
							
							<view class="color_1" v-if="!item.name">请选择</view>
							<view v-if="item.name"  >{{item.name}}</view>
						</picker>
					</view>
				</view>
			</view>
		</view>

		<view class="goodsBox">
			<view class="boxTitle piece">{{$L('物流信息')}}</view>
			<view class="piece">
				<text class="name required">{{$L('物流信息')}}</text>
				<input type="number" v-model="traceInfo" placeholder="请输入" placeholder-class="goodsPlace" />
			</view>
		</view>

		<view class="goodsBox">
			<view class="boxTitle piece">{{$L('发票信息')}}</view>
			<view class="piece">
				<text class="name">{{$L('是否开专票')}}</text>
				<view class="picker">
					<picker :range="data_list" range-key="name" :value="index" @change="change_ticket">
						
						<view class="color_1" v-if="!ticket">请选择</view>
						<view v-if="ticket" v-model="ticket" >{{ticket}}</view>
					</picker>
				</view>
			</view>
		</view>

		<view class="goodsBox">
			<view class="boxTitle piece">其他信息</view>

			<view class="piece">
				<text class="name">{{$L('店铺分类')}}</text>
				<view class="picker">
					<picker :range="storeCategory" range-key="innerLabelName" :value="index" @change="change_storeCategory">
						
						<view class="color_1" v-if="!store_category">请选择</view>
						<view v-if="store_category" v-model="store_category" >{{store_category}}</view>
					</picker>
				</view>
			</view>

			<view class="piece">
				<text class="name required">{{$L('商品标签')}}</text>
				<!-- <view class="picker">
					<picker :range="list" :value="index" @change="change">
						<view>{{list[index]}}</view>
					</picker>
				</view> -->
				<view class="picker">
					<view class="color_1" @tap="toOpen" v-if="!names">请选择</view>
					<view v-if="names" v-model="names" @tap="toOpen" style="overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">{{names}}</view>
						
						<!-- <jpSelect ref="jpSelect" :list="goodsLabel" @checked="checked" :item="item" select="more" tite="请选择商品标签"></jpSelect> -->
					</view>
			</view>

			<view class="piece">
				<text class="name">{{$L('虚拟销量')}}</text>
				<input type="text" v-model="virtualSale" placeholder="请输入" placeholder-class="goodsPlace" maxlength="2" />
			</view>

			<view class="piece">
				<text class="name required">{{$L('发布状态')}}</text>
				<view class="picker">
					<picker :range="list" range-key="name" :value="index" @change="change_state">
						<view class="color_1" v-if="!state_name">请选择</view>
						<view v-if="state_name" v-model="state_name" >{{state_name}}</view>
					</picker>
				</view>
			</view>

			<view class="piece">
				<text class="name required">{{$L('商品推荐')}}</text>
				<view class="picker">
					<picker :range="data_list1" range-key="name" :value="index" @change="change_recommend">
						<view class="color_1" v-if="!recommend">请选择</view>
						<view v-if="recommend" v-model="recommend" >{{recommend}}</view>
					</picker>
				</view>
			</view>

		</view>

		<view class="goodsBox">
			<view class="boxTitle piece">商品信息</view>
			<view class="piece">
				<text class="name">{{$L('市场价')}}</text>
				<input type="text" v-model="marketPrice" placeholder="请输入" placeholder-class="goodsPlace" />
			</view>
			<view class="piece">
				<text class="name required">{{$L('价格')}}</text>
				<input type="text" v-model="truePrice" placeholder="请输入" placeholder-class="goodsPlace" />
			</view>
			<view class="piece">
				<text class="name required">{{$L('库存')}}</text>
				<input type="text" v-model="inventory" placeholder="请输入" placeholder-class="goodsPlace" />
			</view>
			<view class="piece">
				<text class="name">{{$L('预警值')}}</text>
				<input type="text" v-model="waringValue" placeholder="请输入" placeholder-class="goodsPlace" />
			</view>
			<view class="piece">
				<text class="name">{{$L('货号')}}</text>
				<input type="text" v-model="goodsCard" placeholder="请输入" placeholder-class="goodsPlace" />
			</view>
			<view class="piece">
				<text class="name">{{$L('条形码')}}</text>
				<input type="text" v-model="lineCode" placeholder="请输入" placeholder-class="goodsPlace" />
			</view>
			<view class="piece">
				<text class="name required">{{$L('商品图片')}}</text>
				<view class="picker upPic">
					<block v-for="(item,index) in offineImg" :key="index">
						<text class="pic" v-if="!item" @tap="uploadimg(index)">+</text>
						<view v-else class="pic" @tap="uploadimg(index)">
							<image :src="item" mode="aspectFit"></image>
						</view>
					</block>
				</view>
			</view>
			<view class="piece_de">
				<text class="name required dec">{{$L('商品描述')}}</text>
				<view class="picker">
					<!-- <jyf-parser :aaa="aaa" :html="describe" ref="description"></jyf-parser> -->
					<!-- <textarea v-if="!describe"  v-model="describe" placeholder="请输入" placeholder-class="goodsPlace" /> -->
					<bgyxedit ref="son" @bgyxchange="getbgyxinfo"  :showdone="true" uploadurl="https://huayuinterseller.yourwebsite.cn/v3/oss/common/upload" filename="img"></bgyxedit>
				</view>
			</view>
		</view>
    <view class="delivery">
      <view @tap="deliver">提交编辑</view>
    </view>
	</view>
</template>

<script>
	// import jpSelect from '@/components/jp-select/jp-select.vue';
	export default {
		components: {
			// jpSelect
		},
		data() {
			return {
				list: [
					{
						name:'立即售买',
						id:'1'
					},
					{
						name:'暂不售卖，放入仓库中',
						id:'0'
					},
				],
				data_list:[
					{
						name:'是',
						id:'1',
					},
					{
						name:'否',
						id:'0',
					}
				],
				data_list1:[
					{
						name:'是',
						id:'1',
					},
					{
						name:'否',
						id:'0',
					}
				],
				index: 0,
				offineImg: [0, 0, 0, 0, 0],   //商品图片回显地址
				autorImg:[0,0,0,0,0],         //商品图片传回后端path
				categoryList:[],
				goods_name:'',//商品名称
				advertisement:'',//广告语
				initVal: [0, 0, 0],
				category_name:'',
				ticket:'', //是否开专票
				ticket_id:'',
				recommend:"",//是否推荐
				recommend_id:'',
				brandList:'',
				brandList_id:'',
				goodsBrandList:[],//品牌
				goodsAttributeList:[],//产地列表
				attributeList:[],//产地
				attributeList_id:'',
				attributeValues:[],//热卖推荐
				// recommendation:'请先选择分类',
				// recommendation_id:'',
				canUseList:[],//属性分组
				groupName:'',
				groupName_id:'',
				goodsParameter:[],//属性列表
				goodsParameter_w:{},
				goodsParameter_cz:[],//后端
				Parameter:'请选择',
				storeCategory:[],//店铺分类
				store_category:'',
				// category_id:'',
				item:'',
				goodsLabel:[],//商品标签
				names:'',
				state_name:'',
				groupIndex:'',    //自定义属性的索引
				traceInfo:'',     //物流信息
				virtualSale:'',   //虚拟销量
				marketPrice:'',   //市场价
				truePrice:'',     //价格
				inventory:'',    //库存
				waringValue:'',   //预警值
				goodsCard:'',     //货号
				lineCode:'',      //条形码
				describe:'',      //商品描述
				goodattribute:[],//检索属性
				attribute_index:'',
				categoryId3:'',//商品分类ID
				goodsLabelList_h:[],
				state_id:'',//是否立即售卖id
				goodsId:'',//商品id
				edit_data:{},
				attribute_names:'',
				descr_data:'',
			}
		},
		onLoad(options) {
				this.goodsId = options.goodsId;
				this.initFun()
				this.get_brand()
				this.get_canUseList()
				this.get_storeCategory()
				this.get_goodsLabel()
				this.get_edit_data()
				
				
		},
		methods: {
			getbgyxinfo:function(e){
				this.describe = e.html
				//获取富文本内容，默认模式拼接的html
				this.descr_data = JSON.stringify(e.data)
				//获取原始数据,可自行循环拼接html数据
			},
			get_edit_data(){
				
				
				//回显
					this.$seller_request({
						url: 'v3/goods/seller/goods/getGoodsInfo',
						method: 'get',
						data: {
							goodsId:this.goodsId,
						}
					}).then(res => {
						this.attributeList = res.data.attributeList	 //检索属性
						this.categoryId3 = res.data.categoryId3
						if(this.categoryId3){
							this.get_brand(this.categoryId3)
						}
						this.category_name = res.data.categoryPath.replace(/->/g, "") // 分类
						this.goods_name = res.data.goodsName//商品名称
						this.advertisement = res.data.goodsBrief//广告
						this.autorImg = res.data.imageList;
						for(let i in res.data.imageList){
							this.offineImg.splice(i,1,res.data.imageList[i].imageUrl)
						}
						
						setTimeout(()=>{
							this.$refs.son.query_Data(JSON.parse(res.data.goodsDetailsTwo))
						},100)
						
						this.marketPrice = res.data.marketPrice	 //市场价
						this.truePrice = res.data.productPrice        //价格
						this.goodsCard =  res.data.productCode        //货号
						this.inventory =  res.data.productStock         //库存
						this.waringValue = res.data.productStockWarning    //预警
						this.lineCode = res.data.lineCode     //条形码
						this.describe = res.data.goodsDetails //详情
						this.state_id = res.data.sellNow==true?1:0 //是否立即售卖，true==是，false==放入仓库
						this.state_name = res.data.sellNow==true?'立即售卖':'暂不售卖，放入仓库中'
						this.traceInfo = res.data.freightFee //物流信息
						this.recommend_id = res.data.storeIsRecommend//是否推荐
						this.recommend = res.data.storeIsRecommend == 1?'是':'否'
						this.ticket_id = res.data.isVatInvoice
						this.ticket = res.data.isVatInvoice == 1?'是':'否'
						this.goodsLabelList_h =res.data.goodsLabelList
						let x = []
						res.data.goodsLabelList.map((d,index)=>{
							
							this.goodsLabelList_h.map(i=>{
								if(d.labelId =i.labelId)
								i.goodsLabelId = d.labelId
								i.goodsLabelName = d.labelName
							})
							x[index] = d.labelName
						})
						this.names = x.join(',')
						this.brandList = res.data.brandName	 //品牌
						this.brandList_id = res.data.brandId	 //品牌id
						this.groupName = res.data.parameterGroup.groupName//自定义属性
						this.groupName_id = res.data.parameterGroup.groupId//自定义属性ID
						this.goodsParameter_w = res.data.parameterGroup;
					
						this.get_goodsParameter(this.groupName_id,res.data.parameterGroup.parameterList);
						
						
						
						
						this.store_category = res.data.storeInnerLabelList[0].innerLabelPath	 //店铺分类
						this.virtualSale = res.data.virtualSales	 //虚拟
						
							
						res.data.goodsLabelList.map(res=>{  //商品标签
							this.names = this.names.concat(res.labelName)
						})
						this.marketPrice = res.data.marketPrice	 //市场价
						this.$forceUpdate()
					}).catch((e) => {
						//异常处理
					})
			},
			change_state(e){
				this.state_name = this.list[e.detail.value].name;
				this.state_id = this.list[e.detail.value].id;
				this.$forceUpdate()
			},
			checked(el) {
				this.item = el
				let x = []
				this.names = '',
				this.item.map((res,i)=>{
					x[i] = this.item[i].labelName
				})
				this.names = x.join(',')
				// this.goodsLabelList_h = this.item //商品标签
				this.goodsLabelList_h.map((d,index) =>{
					this.item.map((i,index1) =>{
						if(i.labelId == d.goodsLabelId){
							d.goodsLabelName = i.labelName
						}
						
					})
					
				})
				this.goodsLabelList_h.map((y,index) =>{
					if(!this.goodsLabelList_h[index].goodsLabelName){
						this.goodsLabelList_h.splice(index,1);
					}
				})
				
			},
			toOpen() {
				this.$refs.jpSelect.toOpen()
			},
			change_storeCategory(e){
				
				this.store_category = this.storeCategory[e.detail.value].innerLabelName;
			},
			change_parameterName(e){
				let index = e.currentTarget.dataset.index
                this.groupIndex = index
				this.goodsParameter[index].name = this.goodsParameter[index].speclist[e.detail.value]
				this.goodsParameter_cz[index].parameterId = this.goodsParameter[index].parameterId
				this.goodsParameter_cz[index].parameterName = this.goodsParameter[index].parameterName
				this.goodsParameter_cz[index].parameterValue = this.goodsParameter[index].speclist[e.detail.value]
				this.goodsParameter_w.parameterList = this.goodsParameter_cz
				// console.log(this.goodsParameter_w,'this.goodsParameter_w') //店铺自定义属性
				this.$forceUpdate()
			},
			change_groupName(e){
				this.groupName = this.canUseList[e.detail.value].groupName;
				this.groupName_id = this.canUseList[e.detail.value].groupId;
				this.goodsParameter_w.groupId = this.groupName_id;
				this.goodsParameter_w.groupName = this.groupName;
				this.get_goodsParameter(this.groupName_id);
			},
			// change_recommendation(e){
			// 	this.recommendation = this.attributeValues[e.detail.value].attributeValue;
			// 	this.recommendation_id = this.attributeValues[e.detail.value].valueId;
			// },
			change_attributeList(e){
				
				let index = e.currentTarget.dataset.index
				this.attribute_index = index
				this.goodattribute[index].names = this.goodattribute[index].attributeValueList[e.detail.value].attributeValue;
				this.attributeList[index].attributeValue = this.goodattribute[index].attributeValueList[e.detail.value].attributeValue;
				this.attributeList[index].attributeValueId = this.goodattribute[index].attributeValueList[e.detail.value].valueId;
				this.attributeList[index].attributeName = this.goodattribute[index].attributeName;
				this.attributeList[index].attributeId = this.goodattribute[index].attributeId;
				this.$forceUpdate()
				 console.log(this.attributeList,this.goodattribute)   //检索属性
			},
			
			get_goodsParameter (id,selectedData=[]){
				//获取属性列表
					this.$seller_request({
						url: 'v3/goods/seller/goodsParameter/canUseList',
						method: 'GET',
						data:{
							groupId:id
						}
					}).then(res => {
						this.goodsParameter = res.data.list;
						
						this.goodsParameter.map(ite =>{
							ite.speclist = ite.parameterValue.split(',');
							this.goodsParameter_cz.push({});
							ite.name = '';
							if(selectedData.length>0){
								let temp = selectedData.filter(item=>item.parameterId == ite.parameterId);
								if(temp.length>0){
									ite.name = temp[0].parameterValue
								}
							}
						})
					}).catch((e) => {
						//异常处理
					})
			},
			
			get_goodsLabel(){
				//获取商品标签列表
					this.$seller_request({
						url: 'v3/goods/seller/goodsLabel/list',
						method: 'GET',
					}).then(res => {
						this.goodsLabel = res.data.list;
						this.goodsLabel.map((i,index) =>{
							this.goodsLabelList_h.push({});
							this.goodsLabelList_h[index].goodsLabelId = i.labelId;
						})
						
					}).catch((e) => {
						//异常处理
					})
			},
			get_storeCategory(){
				//获取店铺分类列表
					this.$seller_request({
						url: 'v3/seller/seller/storeCategory/list',
						method: 'GET',
					}).then(res => {
						this.storeCategory = res.data;
					}).catch((e) => {
						//异常处理
					})
			},
			get_canUseList(){
				//获取属性分组列表
					this.$seller_request({
						url: 'v3/goods/seller/goodsParameterGroup/canUseList',
						method: 'GET',
					}).then(res => {
						this.canUseList = res.data.list;
					}).catch((e) => {
						//异常处理
					})
			},
			get_brand(id){
				//获取检索属性列表
					this.$seller_request({
						url: 'v3/goods/seller/goodsAttribute/listByCategoryId',
						method: 'GET',
						data:{
							categoryId:id
						},
					}).then(res => {
						this.goodsBrandList = res.data.goodsBrandList
						this.goodattribute = res.data.goodsAttributeList
						if(!this.attributeList){
							let arr = [];
							if(this.goodattribute.length > 0){
								this.goodattribute.forEach(i=>{
									arr.push({})
								})
								this.attributeList = arr;
							}
						}else{
							this.attributeList.map(i=>{
								this.goodattribute.map(d=>{
									if(i.attributeId == d.attributeId){
										d.names = i.attributeValue
									}
								})
							})
						}
						
						// this.goodsAttributeList = res.data.goodsAttributeList[0].attributeValueList
						// this.attributeValues = res.data.goodsAttributeList[1].attributeValueList
					}).catch((e) => {
						//异常处理
					})
			},
			async columnHandle(e) {
			  let { categoryList } = this;
			  let { column, value } = e.detail;
			  this.initVal[column] = value;
			  if (column == 2) {
			    this.initVal[column] = value;
			    uni.hideLoading();
			    return;
			  }
			  let id = categoryList[column][value].categoryId;
			  if (column == 0) {
			    this.initVal = [value, 0, 0];
			    let c = await this.get_categoryList(2,id);
			    let a = await this.get_categoryList(3,c[0].categoryId);
			    categoryList[1] = c;
			    categoryList[2] = a;
			    uni.hideLoading();
			  } else if (column == 1) {
			    this.initVal[column] = [0,value, 0];;
			    this.initVal[2] = 0;
			    let a = await this.get_categoryList(3,id);
			    categoryList[2] = a;
			    uni.hideLoading();
			  }
			  categoryList = JSON.parse(JSON.stringify(categoryList));
			  this.categoryList = categoryList
			},
			
			async initFun() {
			  try {
			    let p = await this.get_categoryList(1,0);
			    let c = await this.get_categoryList(2,p[0].categoryId);
			    let a = await this.get_categoryList(3,c[0].categoryId);
			    let categoryList = [p, c, a];
			    this.categoryList=categoryList
			  } catch (err) {
			    throw new Error(err);
			  }
			},
			
			get_categoryList(grade,id){
				return new Promise((resolve, reject) => {
				//获取分类列表
					this.$seller_request({
						url: 'v3/goods/seller/goodsCategory/list',
						method: 'GET',
						data:{
							grade:grade,
							categoryId:id
						},
					}).then(res => {
						resolve(res.data);
					}).catch((e) => {
						//异常处理
					})
					});
			},
			change(e) {
				if(this.categoryList[2][e.detail.value[2]]){
					this.category_name = this.categoryList[0][e.detail.value[0]].categoryName + this.categoryList[1][e.detail.value[1]].categoryName + this.categoryList[2][e.detail.value[2]].categoryName
					this.categoryId3 = this.categoryList[2][e.detail.value[2]].categoryId;
					this.get_brand(this.categoryList[2][e.detail.value[2]].categoryId)
				}else if(this.categoryList[1][e.detail.value[1]]) {
					uni.showToast({
						title:'商品分类必须是三级',
						icon:'none'
					})
				}
				
				
			},
			change_ticket(e){
				this.ticket = this.data_list[e.detail.value].name;
				this.ticket_id = this.data_list[e.detail.value].id;
			},
			change_recommend(e){
				this.recommend = this.data_list1[e.detail.value].name;
				this.recommend_id = this.data_list1[e.detail.value].id;
			},
			change_brandList(e){
				this.brandList = this.goodsBrandList[e.detail.value].brandName;
				this.brandList_id = this.goodsBrandList[e.detail.value].brandId;
				
			},
			
			// 上传图片
			uploadimg(index) {

				uni.chooseImage({
					count: 1,
					success: ret => {
						const tempFilePaths = ret.tempFilePaths
						// this.offineImg.splice(index, 1, tempFilePaths[0])
						
						uni.uploadFile({
							url: getApp().globalData.storeUrl + 'v3/oss/common/upload',
							filePath: tempFilePaths[0],
							formData: {
								source: 'goods',
							},
							success: ret => {
                const res = JSON.parse(ret.data)
								if (res.state == 200) {
                    this.offineImg.splice(index,1,res.data.url)
                    const obj = {
                      image:res.data.path,
                      isMain:index == 0 ? 1 : 2
                    }
                    this.autorImg.splice(index,1,obj)
								}
							}
						});
						
					}
				})
			},
      //发布商品
      deliver(){
        if(!this.category_name){
          this.$api.msg('请选择商品分类')
          return
        }
        if(!this.goods_name){
          this.$api.msg('请输入商品名称')
          return
        }
        // if(!this.advertisement){
        //   this.$api.msg('请输入广告语')
        //   return
        // }
        // if(!this.brandList){
        //   this.$api.msg('请选择品牌')
        //   return
        // }
        // if(!this.attributeList){
        //   this.$api.msg('请选择产地')
        //   return
        // }
        // if(!this.recommendation){
        //   this.$api.msg('请选择热卖推荐')
        //   return
        // }
        // if(!this.goodsParameter[Number(this.groupIndex)].name){
        //   this.$api.msg('请选择'+ this.goodsParameter[Number(this.groupIndex)].parameterName)
        //   return
        // }
        
        if(!this.traceInfo){
          this.$api.msg('请输入物流信息')
          return
        }
        // if(!this.store_category){
        //   this.$api.msg('请选择店铺分类')
        //   return
        // }
        if(!this.names){
          this.$api.msg('请选择商品标签')
          return
        }
        // if(!this.virtualSale){
        //   this.$api.msg('请输入虚拟销量')
        //   return
        // }
        if(!this.state_name){
          this.$api.msg('请选择发布状态')
          return
        }
        if(!this.recommend){
          this.$api.msg('请选择是否推荐商品')
          return
        }
        // if(!this.marketPrice){
        //   this.$api.msg('请输入市场价')
        //   return
        // }
        if(!this.truePrice){
          this.$api.msg('请输入价格')
          return
        }
        if(!this.inventory){
          this.$api.msg('请输入库存')
          return
        }
        if(!this.waringValue){
          this.$api.msg('请输入预警值')
          return
        }
        // if(!this.goodsCard){
        //   this.$api.msg('请输入货号')
        //   return
        // }
        // if(!this.lineCode){
        //   this.$api.msg('请输入条形码')
        //   return
        // }
        if(!this.describe){
          this.$api.msg('请输入商品描述')
          return
        }
        
        let paramDTO = {}
		
		paramDTO.attributeAndParameter = this.attributeList //检索属性
		paramDTO.attributeAndParameter = this.goodsParameter_w //自定义属性
        paramDTO.barCode = this.lineCode     //条形码
		paramDTO.brandId = this.brandList_id //品牌
		paramDTO.categoryId3 = this.categoryId3 //商品分类
		paramDTO.freightFee = this.traceInfo //物流信息
		paramDTO.goodsLabelList = this.goodsLabelList_h //标签
		paramDTO.goodsName = this.goods_name //商品名称
		paramDTO.imageList = [];//图片列表
		for(let i in this.autorImg){
			if(this.autorImg[i]){
				paramDTO.imageList.push(this.autorImg[i]);  
			}
		}
		paramDTO.isVatInvoice = this.ticket_id //发票
		paramDTO.marketPrice = this.marketPrice //市场价
		paramDTO.productCode = this.goodsCard //货号
		paramDTO.productStock = this.inventory //库存
        paramDTO.productStockWarning = this.waringValue //预警
		paramDTO.sellNow = this.state_id?true:false //是否立即售卖，true==是，false==放入仓库
		paramDTO.storeIsRecommend = this.recommend_id //是否推荐
		paramDTO.productPrice = this.truePrice //价格
		paramDTO.virtualSales = this.virtualSale // 虚拟
		paramDTO.goodsDetails = this.describe //详情
		paramDTO.goodsDetailsTwo = this.descr_data
		paramDTO.advertisement = this.advertisement //广告
		paramDTO.goodsId = this.goodsId

		// console.log(paramDTO)
		// 编辑商品
			this.$seller_request({
				url: 'v3/goods/seller/goods/edit',
				method: 'post',
				isJSon:true,
				data:paramDTO,
			}).then(res => {
				if(res.state == 200){
					uni.showToast({
						title:res.msg,
						icon:'none',
						duration:2000
					})
					setTimeout(()=>{
						uni.navigateTo({
							url:'/merchants/pages/goods/goods'
						})
					},2000)
				}else{
					uni.showToast({
						title:res.msg,
						icon:'none',
					})
				}
			}).catch((e) => {
				//异常处理
			})
        
      }
		}
	}
</script>

<style lang="scss" scoped>
	page {
		width: 100%;
		// height: 100%;
	}

	.container {
		width: 100%;
		// height: 100%;
		border-top: 1rpx solid #fff;
		background-color: #E9E9E9;
	}

	.goodsBox {
		width: 100%;
		overflow: hidden;
		margin: 4rpx auto 10rpx;
	}

	.piece {
		display: flex;
		align-items: center;
		width: 100%;
		padding: 38rpx 0;
		margin: 2rpx auto;
		background-color: #FFFFFF;
	}
	.piece_de {
		// display: flex;
		align-items: center;
		width: 100%;
		padding: 38rpx 0;
		margin: 2rpx auto;
		background-color: #FFFFFF;
	}

	.boxTitle {
		padding-left: 30rpx;
		font-size: 32rpx;
		font-family: SourceHanSansCN-Medium, SourceHanSansCN;
		font-weight: 600;
		color: rgba(0, 0, 0, 0.85);
	}

	.piece input,
	.piece .picker {
		width: 70%;
		margin: 0 10rpx;
	}

	.piece .goodsPlace,
	.piece .picker view {
		// padding-left: 20rpx;
		font-size: 28rpx;
		font-family: SourceHanSansCN-Regular, SourceHanSansCN;
		font-weight: 400;
		
	}
	.color_1 {
		color: #999999;
	}
	.color_2 {
		color: #999999;
		width: 400rpx;
	}
	.piece .name {
		flex: 1;
		padding-left: 40rpx;
		font-size: 28rpx;
		font-family: SourceHanSansCN-Regular, SourceHanSansCN;
		font-weight: 540;
		color: rgba(0, 0, 0, 0.85);
	}

	.goodsBox .upPic {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-left: 22rpx;
	}

	.goodsBox .upPic .pic {
		width: 90rpx;
		height: 90rpx;
		border: 1rpx dashed #888;
		line-height: 90rpx;
		text-align: center;
	}

	.goodsBox .upPic .pic image {
		width: 100%;
		height: 100%;
	}

	.goodsBox .dec {
		height: 290rpx;
	}
  .container .delivery{
    display: flex;
    width: 750rpx;
    height: 100rpx;
    justify-content: center;
    align-items: center;
  }
  .container .delivery>view{
    width: 80%;
    height: 60rpx;
    background-color: #FF9800;
    border-radius: 20rpx;
    text-align: center;
    line-height: 60rpx;
    color: #fff;
  }
  .required::before {
  	  content: "* ";
  	  color: red;
  }
</style>
