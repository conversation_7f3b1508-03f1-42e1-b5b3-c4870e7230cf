<template>
	<view class="container">
		<view class="list">
			<view class="item" :class="{ active: grade == 1 }" @click="changeTab(1)">
				一级分类
			</view>
			<view class="item" :class="{ active: grade == 2 }" @click="changeTab(2)">
				二级分类
			</view>
			<view class="item" :class="{ active: grade == 3 }" @click="changeTab(3)">
				三级分类
			</view>
		</view>
		<view class="content" v-if="grade == 1">
			<view class="item" @click="changeType(1, item)" v-for="(item, index) in list1" :key="index">
				<span>{{ item.categoryName }}</span>
				<image class="arrow" :src="imgUrl + 'business/arrow.png'" mode=""></image>
			</view>
		</view>
		<view class="content" v-if="grade == 2">
			<view class="item" @click="changeType(2, item)" v-for="(item, index) in list2" :key="index">
				<span>{{ item.categoryName }}</span>
				<image class="arrow" :src="imgUrl + 'business/arrow.png'" mode=""></image>
			</view>
		</view>
		<view class="content" v-if="grade == 3">
			<view class="item" @click="changeType(3, item)" v-for="(item, index) in list3" :key="index">
				<span>{{ item.categoryName }}</span>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		components: {

		},
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				list1: [],
				list2: [],
				list3: [],
				grade: 1, //分类等级 默认1
				categoryId1: '', //上级分类id
				categoryId2: '', //上级分类id
				categoryInfo: {}, //选择的分类信息
			}
		},
		onLoad() {
			this.categoryInfo = uni.getStorageSync('categoryInfo') ? uni.getStorageSync('categoryInfo') : {}
			if (this.categoryInfo.categoryId) {
				this.categoryId1 = this.categoryInfo.categoryId1
				this.categoryId2 = this.categoryInfo.categoryId2
				this.grade = 3
			}
			this.getGoodsCategory()
		},
		methods: {
			changeType(index, item) {
				if (index == 1) {
					this.categoryId2 = ''
				}
				if (index != 3) {
					this.grade = index + 1;
					let newCategoryId = 'categoryId' + index
					this[newCategoryId] = item.categoryId
					this.getGoodsCategory()
				} else {
					this.chooseGoodsCategory(item)
				}


			},
			changeTab(index) {
				if (this.grade == 1 && (index == 2 || index == 3)) return
				if (this.grade == 2 && index == 3) return
				this.grade = index;
				this.getGoodsCategory()
			},
			// 选择商品分类
			chooseGoodsCategory(item) {
				let categoryInfo = {
					...item,
					categoryId1: this.categoryId1,
					categoryId2: this.categoryId2
				}
				uni.setStorageSync('categoryInfo', categoryInfo)
				this.$back()
			},
			// 获取分类信息
			getGoodsCategory() {
				let params = {}
				params.url = 'v3/goods/seller/goodsCategory/list'
				params.data = {
					grade: this.grade,
				}
				if (this.grade != 1) {
					params.data.categoryId = this.grade == 2 ? this.categoryId1 : this.categoryId2
				}
				this.$seller_request(params).then(res => {
					if (res.state == 200) {
						let nowList = 'list' + this.grade
						this[nowList] = res.data
					} else {
						this.$api.msg(res.msg)
					}
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	page {
		width: 100%;
		height: 100%;
	}

	.container {
		width: 100%;
		height: 100%;
		background-color: #F6F6F6;
		font-size: 28rpx;
		color: #101010;

		.list {
			background-color: #fff;
			display: flex;
			align-items: center;
			justify-content: space-around;
			margin-bottom: 14rpx;

			.item {
				height: 92rpx;
				width: 160rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.active {
				color: #3996FC;
				border-bottom: 4rpx solid #3C78E9;
			}
		}

		.content {
			.item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 0 22rpx 0 44rpx;
				height: 96rpx;
				background-color: #fff;
				margin-bottom: 2rpx;

				.arrow {
					width: 38rpx;
					height: 38rpx;
				}
			}
		}
	}
</style>