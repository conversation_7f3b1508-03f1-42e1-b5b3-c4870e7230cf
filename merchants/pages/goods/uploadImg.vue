>
<template>
	<view>
		<view>
			<block>
				<view class="upload_content">
					<view class="upload_img" v-for="(item, index) in uploadFiles" :key="index">
						<image :src="item.imageUrl" v-if="item.imageUrl" mode="aspectFit" @click="kan(index)"></image>
						<view style="" class="shortName" v-if="item.file_name">{{ item.file_name }}</view>
						<view style="color: red;" @click="delImg(item, index)" v-if="!onlyShow">删除</view>
					</view>
					<view class="upload_click" @click="uploadfImgiles"
						v-if="!onlyShow && uploadFiles.length < (num?num:1)">
						<view class="upload_image">
							<image :src="imgUrl + 'business/value_add.png'" mode=""></image>
						</view>
					</view>
				</view>
			</block>

		</view>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	export default {
		props: {
			num: {
				type: Number,
			},
			uploadFiles: {
				type: Array,
				value: [],
			},
			onlyShow: { // 仅作展示
				type: Boolean,
				default: false,
			},
		},
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
			};
		},
		computed: {
		  ...mapState(['userInfo'])
		},
		methods: {
			kan(index) {
				uni.previewImage({
					urls: [this.uploadFiles[index].imageUrl],
					current: 1
				});
			},
			// 删除事件
			delImg(item, index) {
				const newUploadFiles = [...this.uploadFiles];
				newUploadFiles.splice(index,1)
				this.$emit('update:uploadFiles',newUploadFiles)
			},
			// 上传图片事件
			uploadfImgiles() {
				let _this = this
				uni.chooseImage({
				  count: 1,
				  success: function (res) {
				    uni.showLoading({
				      title: _this.$L('上传中...')
				    })
				    if (res.tempFiles[0].size > Math.pow(1024, 2) * 4) {
				      uni.hideLoading()
				      uni.showToast({
				        title: _this.$L('超出了图片大小限制4M'),
				        icon: 'none',
				        duration: 700
				      })
				    } else {
				      uni.uploadFile({
				        url: getApp().globalData.storeUrl + 'v3/oss/seller/upload',
				        filePath: res.tempFilePaths[0],
				        name: 'file',
				        formData: {
				          source: 'goods'
				        },
				        header: {
				            Authorization:  'Bearer '+_this.userInfo.vendorLoginVO.access_token
				         },
				        success: (uploadFileRes) => {
				          // #ifdef  MP-ALIPAY||MP-BAIDU
				          let result = uploadFileRes.data
				          // #endif
				
				          // #ifndef MP-ALIPAY||MP-BAIDU
				          let result = JSON.parse(uploadFileRes.data)
				          // #endif
				          if (result.state == 200) {
				            uni.hideLoading()
				            console.log(result);
							const obj = {
								imageUrl: result.data.url + '?bindId=' + result.data.fileId,
								image: result.data.path + '?bindId=' + result.data.fileId,
							}
							const newUploadFiles = [..._this.uploadFiles];
							newUploadFiles.push(obj)
							_this.$emit('update:uploadFiles',newUploadFiles)
				          } else {
				            uni.hideLoading()
				            uni.showToast({
				              title: uploadFileRes.msg,
				              icon: 'none',
				              duration: 700
				            })
				          }
				        },
				        fail: (uploadFileRes) => {
				          uni.showToast({
				            title: uploadFileRes.msg,
				            icon: 'none',
				            duration: 700
				          })
				        }
				      })
				    }
				  }
				})
			},
		},
	};
</script>

<style lang="scss" scoped>
	.shortName {
		max-width: 200rpx;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		word-break: break-all;
		overflow: hidden;
	}

	.upload_content {
		border-radius: 20rpx;
		position: relative;
		display: flex;
		flex-wrap: wrap;

		.upload_img {
			// width: 130rpx;
			height: 180rpx;
			font-size: 24rpx;
			color: #888888;
			text-align: center;
			margin-right: 20rpx;
			image {
				width: 90rpx;
				height: 90rpx;
			}

			video {
				width: 80rpx;
				height: 80rpx;
			}

			::v-deep .uni-video-cover-play-button {
				width: 46rpx;
				height: 46rpx;
			}

			::v-deep .uni-video-cover-duration {
				font-size: 24rpx;
				margin-top: 0;
			}
		}
	}

	.upload_click {
		.upload_image {
			width: 90rpx;
			height: 90rpx;
			border-radius: 2rpx;
			position: relative;
			margin-top: 0rpx;
			border: 2rpx #D9D9D9 dashed;

			image {
				width: 18rpx;
				height: 40rpx;
				position: absolute;
				left: 34rpx;
				top: 24rpx;
			}
		}

		.tips {
			color: rgba(190, 190, 190, 1);
			font-size: 24rpx;
			margin-top: 26rpx;
		}
	}

	video::-webkit-media-controls {
		display: none !important;
	}
</style>