<template>
	<view class="container">
		<view class="reserve_list">
			<view class="item" v-for="(item, index) in reserveInfoList" :key="index">
				<view class="input-box">
					<input v-model="item.reserveName" placeholder="请输入预留信息名称，最多10个字" type="text" maxlength="10" />
					<image @click="clear(item)" v-if="item.reserveName" :src="imgUrl + 'business/clear-icon.png'" mode=""></image>
				</view>
				<view class="operate-box">
					<view class="left" @click.stop="showReserveType(item)">
						<span>{{ item.reserveTypeText }}</span>
						<image :src="imgUrl + 'business/arrow.png'" mode=""></image>
						<view class="bottom-con" v-if="item.isShow">
							<view class="bc-item" @click.stop="chooseReserveType(itemType,item)" hover-class="item_click" hover-stay-time="50"
								v-for="(itemType, indexType) in reserveInfoTypeList" :key="indexType">
								{{ itemType.title }}
							</view>
						</view>
					</view>
					<view class="right">
						<checkbox-group @change="checkboxChange($event, item, index)">
							<label>
								<checkbox style="transform:scale(0.7)" :checked="item.isRequired == 0 ? false : true"
									value="1" />
							</label>
						</checkbox-group>
						<text>必填</text>
					</view>
				</view>
			</view>
			
			<view class="add">
				<text @click="addReserveInfo">添加预留信息</text>
			</view>

		</view>

		<view class="bottom_act flex_row_center_center">
			<view class="btn" @click="submitBtn">确定</view>
		</view>
		
		<view class="mask_con" @click="closeShow" v-if="isHaveShow"></view>
	</view>
</template>

<script>
	export default {
		components: {

		},
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				reserveInfoList: [], //用户预留信息列表
				reserveInfoTypeList: [{
						id: 1,
						title: '手机号'
					},
					{
						id: 2,
						title: '身份证号'
					},
					{
						id: 3,
						title: '数字'
					},
					{
						id: 4,
						title: '文本'
					},
					{
						id: 5,
						title: '邮箱'
					},
					
				],
				isHaveShow: false,
			}
		},
		onLoad() {

		},
		methods: {
			clear(item) {
				item.reserveName = ''
			},
			checkboxChange(e, item, index) {
				this.reserveInfoList[index].isRequired = item.isRequired == 0 ? 1 : 0
			},
			// 新增预留信息项
			addReserveInfo() {
				let reserveItem = {
					isRequired: 0,
					reserveName: '',
					reserveType: 1,
					reserveTypeText: '手机号',
					isShow: false,
				}
				this.reserveInfoList.push(reserveItem)
			},
			showReserveType(item) {
				item.isShow = !item.isShow
				this.isHaveShow = item.isShow ? true : false
			},
			closeShow() {
				this.reserveInfoList.map(v => {
					v.isShow = false
				})
				this.isHaveShow = false
			},
			// 选择每一项的预留信息类型
			chooseReserveType(childItem, item) {
				const { id, title } = childItem;
				item.reserveType = id
				item.reserveTypeText = title
				item.isShow = false
				this.isHaveShow = false
			},
			// 保存所选的预留信息
			submitBtn() {
				if (this.reserveInfoList.length == 0) return this.$api.msg('请添加预留信息')
				let isCanSubmit = true
				let newReserveList = []
				this.reserveInfoList.map(v => {
					if (!isCanSubmit) return
					if (!v.reserveName.trim()) {
						this.$api.msg('请输入预留信息名称')
						isCanSubmit = false
					}
					let newReserveItem = {
						isRequired: v.isRequired,
						reserveName: v.reserveName,
						reserveType: v.reserveType,
					}
					newReserveList.push(newReserveItem)
				})
				if (!isCanSubmit) return
				uni.setStorageSync('reserveInfoList', newReserveList)
				this.$back()
			},
		}
	}
</script>

<style lang="scss" scoped>
	page {
		background-color: #F6F6F6;
	}

	.container {
		height: 100%;
		background-color: #F6F6F6;
		
		
		.reserve_list {
			width: 100%;
			font-size: 28rpx;
			color: #101010;
			padding-bottom: 230rpx;
			.item {
				padding: 20rpx 24rpx;
				background-color: #fff;
				margin-bottom: 14rpx;
			
				&:last-child {
					margin-bottom: 0;
				}
			
				.operate-box {
					margin-top: 14rpx;
					display: flex;
					justify-content: space-between;
			
					.left {
						width: 240rpx;
						height: 64rpx;
						display: flex;
						justify-content: space-between;
						align-items: center;
						padding: 0 28rpx;
						position: relative;
						// z-index: 9;
			
						image {
							transform: rotate(90deg);
							width: 24rpx;
							height: 24rpx;
						}
						
						.bottom-con {
							width: 240rpx;
							position: absolute;
							top: 64rpx;
							left: 0;
							color: #101010;
							background-color: #fff;
							padding: 10rpx 0;
							z-index: 99;
									
							.bc-item {
								padding: 10rpx 28rpx;
								// margin-bottom: 18rpx;
							}
									
							.item_click {
								background-color: #eee;
							}
						}
					}
			
					.right {
						display: flex;
						align-items: center;
			
						::v-deep.uni-label-pointer {
							margin-right: 0;
						}
			
						::v-deep uni-label {
							display: inline-block;
							margin-right: 40rpx;
						}
			
						::v-deep .uni-checkbox-input.uni-checkbox-input-checked {
							background-color: #5287EA;
							border-color: #5287EA !important;
			
							&:before {
								color: #fff;
							}
						}
					}
				}
			
				.input-box {
					position: relative;
			
					input {
						height: 74rpx;
						width: 100%;
						background-color: #F6F6F6;
						color: #888888;
						border-radius: 4rpx;
						padding-left: 20rpx;
					}
			
					::v-deep .uni-input-placeholder {
						color: #888888;
						font-size: 28rpx;
					}
			
					image {
						width: 24rpx;
						height: 24rpx;
						position: absolute;
						top: 28rpx;
						right: 24rpx;
					}
				}
			}
			
		}

		
		.add {
			color: #3C78E9;
			padding: 40rpx 30rpx;
		}

		.bottom_act {
			position: fixed;
			// height: 132rpx;
			left: 0;
			bottom: 98rpx;
			width: 100%;
			z-index: 1;

			.btn {
				width: 680rpx;
				height: 80rpx;
				line-height: 80rpx;
				border-radius: 4rpx;
				background-color: rgba(60, 120, 233, 1);
				color: #fff;
				font-size: 28rpx;
				text-align: center;
			}
		}
	    
		.mask_con {
			width: 100%;
			height: 100%;
			background-color: rgba(255, 255, 255, 0);
			position: fixed;
			top: 0;
			left: 0;
			z-index: 3;
		}
	}
</style>