<template>
	<view class="container">
		<view class="pub-item">
			<view class="pub-item-tit">
				商品分类<span class="redStar">*</span>
			</view>
			<view class="pub-item-con" :class="categoryInfo.categoryId ? 'choose_active' : ''" @click="chooseType">
				{{categoryInfo.categoryId ? categoryInfo.categoryName  : '请选择商品分类'}}
				<image class="arrow" :src="imgUrl + 'business/arrow.png'" mode=""></image>
			</view>
		</view>
		<view class="pub-item" style="">
			<view class="pub-item-tit">
				商品类型
			</view>
			<view class="pub-item-con">
				<radio-group class="circle_radios" @change="e => releaseInfo.isVirtualGoods = e.detail.value">
					<label>
						<radio value="1" style="transform:scale(0.7);" :checked="releaseInfo.isVirtualGoods == 1" />实物商品
					</label>
					<label>
						<radio value="2" style="transform:scale(0.7)" :checked="releaseInfo.isVirtualGoods == 2" />虚拟商品
					</label>
				</radio-group>
			</view>
		</view>
		<view class="pub-item" v-if="releaseInfo.isVirtualGoods == 2">
			<view class="pub-item-tit">
				用户预留信息
			</view>
			<view class="pub-item-con" :class="reserveInfoList.length > 0 ? 'choose_active' : ''" @click="reserveInfo">
				{{ reserveInfoList.length > 0 ? '已设置' : '未设置' }}
				<image class="arrow" :src="imgUrl + 'business/arrow.png'" mode=""></image>
			</view>
		</view>
		<view class="pub-item2" style="margin-bottom: 20rpx;margin-top: 36rpx;">
			<view class="pub-item2-tit">
				<view class="left">
					商品名称<span class="redStar">*</span>
				</view>
				<view class="right">
					{{ (releaseInfo.goodsName || '').length }}/50
				</view>
			</view>
			<view class="pub-item2-con">
				<textarea v-model="releaseInfo.goodsName" placeholder="请输入商品名称" maxlength="50"
					class="text_area"></textarea>
			</view>
		</view>
		<view class="pub-item2" v-if="JSON.stringify(haveSpecImgInfo) == '{}'">
			<view class="pub-item2-tit">
				<view class="left">
					商品图片<span class="redStar">*</span>
				</view>
				<view class="right">
					{{ goodImg.length }}/6
				</view>
			</view>
			<view class="pub-item2-con">
				<UploadImg :uploadFiles.sync="goodImg" :num="6" />
			</view>
		</view>
		<view v-else class="">
			<view class="pub-item2" v-for="(item, index) in haveSpecImgInfo.specValueList" :key="index">
				<view class="pub-item2-tit">
					<view class="left">
						{{ item.specValue }}<span class="redStar">*</span>
					</view>
					<view class="right">
						{{ item.imageList.length }}/6
					</view>
				</view>
				<view class="pub-item2-con">
					<UploadImg :uploadFiles.sync="item.imageList" :num="6" />
				</view>
			</view>
		</view>
		<view class="pub-item2" style="margin-bottom: 20rpx;">
			<view class="pub-item2-tit">
				<view class="left">
					商品详情
				</view>
				<view class="right"></view>
			</view>
			<view class="pub-item2-con" v-if="!goodsId">
				<UploadImg :uploadFiles.sync="goodDetail" :num="99" />
			</view>
			<view class="pub-item2-con pub_color" v-else>如需修改商品详情，请前往PC端操作</view>
		</view>
		<view class="pub-item" v-if="releaseInfo.isVirtualGoods == 1">
			<view class="pub-item-tit">
				配送方式<span class="redStar">*</span>
			</view>
			<view class="pub-item-con">
				<checkbox-group class="checkBox" @change="handleCheckBoxChange">
					<label>
						<checkbox style="transform:scale(0.7)" value="1" :checked="releaseInfo.supportMailing == 1" />
						<text>送货到家</text>
					</label>
					<label>
						<checkbox style="transform:scale(0.7)" value="2" :checked="releaseInfo.supportPickup == 1" />
						<text>到店自提</text>
					</label>
				</checkbox-group>
			</view>
		</view>
		<view class="pub-item" v-if="releaseInfo.isVirtualGoods == 1">
			<view class="pub-item-tit">
				运费<span class="redStar">*</span>
			</view>
			<view class="pub-item-con">
				<radio-group class="circle_radios" @change="e => goodsParam.ytype = e.detail.value">
					<label>
						<radio value="1" style="transform:scale(0.7);" :checked="goodsParam.ytype == 1" />固定运费
					</label>
					<label>
						<radio value="2" style="transform:scale(0.7)" :checked="goodsParam.ytype == 2" />运费模板
					</label>
				</radio-group>
			</view>
		</view>
		<view class="pub-item" style="margin-bottom: 20rpx;" v-if="goodsParam.ytype == 1">
			<view class="pub-item-tit">
				固定运费<span class="redStar">*</span>
			</view>
			<view class="pub-item-con">
				<input class="p_input" type="text" v-model="freightFee" placeholder="请输入运费金额，单位：元" />
			</view>
		</view>
		<view class="pub-item" style="margin-bottom: 20rpx;" v-if="goodsParam.ytype == 2">
			<view class="pub-item-tit">
				运费模版<span class="redStar">*</span>
			</view>
			<view class="pub-item-con" :class=" freightId ? 'choose_active' : ''">
				<picker class="con" range-key="templateName" :value="selFreightIndex" @change="selFreight"
					:range="freightTemplate">
					<view class="picker">
						{{freightInfo.freightTemplateId ? freightInfo.templateName : '请选择运费模版'}}
					</view>
				</picker>
				<image class="arrow" :src="imgUrl + 'business/arrow.png'" mode=""></image>
			</view>
		</view>
		<view class="pub-item" style="margin-bottom: 28rpx;">
			<view class="pub-item-tit">
				商品状态
			</view>
			<view class="pub-item-con">
				<radio-group class="circle_radios" @change="handleRadioChange">
					<label>
						<radio value="1" style="transform:scale(0.7);" :checked="releaseInfo.sellNow" />立即售卖
					</label>
					<label>
						<radio value="2" style="transform:scale(0.7)" :checked="!releaseInfo.sellNow" />放入仓库
					</label>
				</radio-group>
			</view>
		</view>
		<view class="pub-item">
			<view class="pub-item-tit">
				商品规格
			</view>
			<view class="pub-item-con" @click="tosetSpec">
				去设置
				<image class="arrow" :src="imgUrl + 'business/arrow.png'" mode=""></image>
			</view>
		</view>
		<view class="pub-item" v-if="specInfoList == 0">
			<view class="pub-item-tit">
				售价<span class="redStar">*</span>
			</view>
			<view class="pub-item-con">
				<input type="number" class="p_input" v-model="productPrice" placeholder="请输入售价，单位：元" />
			</view>
		</view>
		<view class="pub-item" v-if="specInfoList == 0">
			<view class="pub-item-tit">
				库存<span class="redStar">*</span>
			</view>
			<view class="pub-item-con">
				<input type="number" class="p_input" v-model="productStock" placeholder="请输入库存" />
			</view>
		</view>
		<view class="bottom_btn flex_row_center_start">
			<view class="btn" @click="publish">
				发布
			</view>
		</view>
	</view>
</template>

<script>
	import UploadImg from './uploadImg.vue';
	export default {
		components: {
			UploadImg
		},
		data() {
			return {
				imgUrl: getApp().globalData.imgUrl,
				goodsParam: {
					ptype: 1,
					ytype: 1,
				},
				goodImg: [],
				goodDetail: [],
				releaseInfo: { //发布的信息
					goodsName: '',
					categoryId3: '',
					isVirtualGoods: 1,
					supportMailing: 1,
					supportPickup: 1,
					sellNow: true, //是否立即售卖，true==是，false==放入仓库
					imageList: [],
					productList: [],
				},
				reserveInfoList: [], //选择的虚拟预留信息列表
				specInfoList: [],
				productList: [],
				freightFee: '', //固定运费
				freightId: '', //模版id
				freightInfo: {}, //选择的运费模版
				productPrice: '',
				productStock: '',
				categoryInfo: {}, //选择的分类信息
				isToChoose: false, //是否跳转到另一个页面选择信息
				goodsId: '', //编辑商品的id
				goodsInfo: {}, //编辑的商品信息
				freightTemplate: [], //运费模版列表
				haveSpecImgInfo: {}, //是否存在设置主图的规格信息
				selFreightIndex: 0, //选择运费模版的索引
			}
		},
		onLoad() {
			if (this.$Route.query.goodsId) {
				this.goodsId = this.$Route.query.goodsId
			}
			uni.setNavigationBarTitle({
				title: this.goodsId ? '编辑商品' : '发布商品'
			})
			this.removeStorage()

			if (this.goodsId) {
				this.getGoodsInfo()
			} else {
				this.getGoodsFreightTemplate()
			}
		},
		onShow() {
			if (this.isToChoose) {
				this.categoryInfo = uni.getStorageSync('categoryInfo') ? uni.getStorageSync('categoryInfo') : {}
				this.reserveInfoList = uni.getStorageSync('reserveInfoList') ? uni.getStorageSync(
					'reserveInfoList') : []
				this.releaseInfo.categoryId3 = this.categoryInfo.categoryId
				this.specInfoList = uni.getStorageSync('specInfoList') ? uni.getStorageSync('specInfoList') : []
				if (this.specInfoList.length > 0) {
					this.haveSpecImgInfo = this.specInfoList.find(v => v.isMainSpec == 1)
				}
				this.productList = uni.getStorageSync('productList') ? uni.getStorageSync('productList') : []
				this.isToChoose = false
			}
		},
		methods: {
			removeStorage() {
				uni.removeStorageSync('categoryInfo')
				uni.removeStorageSync('reserveInfoList')
				uni.removeStorageSync('specInfoList')
				uni.removeStorageSync('productList')
			},
			// 获取运费模版
			getGoodsFreightTemplate() {
				let params = {}
				params.url = 'v3/goods/seller/goodsFreightTemplate/list'
				params.data = {
					pageSize: 10000
				}
				this.$seller_request(params).then(res => {
					this.freightTemplate = res.data.list
					let haveFreightInfo = {}

					this.freightTemplate.map((v, i) => {
						if (v.freightTemplateId == this.freightId) {
							haveFreightInfo = v
							this.selFreightIndex = i
						}

					})
					this.freightInfo = haveFreightInfo ? haveFreightInfo : {}

				})
			},
			// 获取编辑商品的信息
			getGoodsInfo() {
				let params = {}
				params.url = 'v3/goods/seller/goods/getGoodsInfo'
				params.data = {
					goodsId: this.goodsId
				}
				this.$seller_request(params).then(res => {
					if (res.state == 200) {
						this.goodsInfo = res.data
						this.releaseInfo.goodsName = res.data.goodsName
						this.releaseInfo.categoryId3 = res.data.categoryId3
						this.releaseInfo.isVirtualGoods = res.data.isVirtualGoods
						this.releaseInfo.supportMailing = res.data.supportMailing
						this.releaseInfo.supportPickup = res.data.supportPickup
						this.releaseInfo.sellNow = res.data.sellNow
						this.releaseInfo.imageList = res.data.imageList


						this.categoryInfo = {
							categoryId: res.data.categoryId3,
							categoryName: res.data.categoryPath.split('->')[2],
							categoryId1: res.data.categoryId1,
							categoryId2: res.data.categoryId2,
						}
						if (res.data.freightFee) {
							this.freightFee = res.data.freightFee
						}
						if (res.data.freightId) {
							this.freightId = res.data.freightId
							this.goodsParam.ytype = 2
						}
						uni.setStorageSync('categoryInfo', this.categoryInfo)
						if (res.data.reserveInfoList && res.data.reserveInfoList.length > 0) {
							this.reserveInfoList = res.data.reserveInfoList
							uni.setStorageSync('reserveInfoList', res.data.reserveInfoList)
						}
						if (res.data.productList.length == 1 && !res.data.productList[0].specValueIds) {
							this.productPrice = res.data.productPrice
							this.productStock = res.data.productStock
						} else {
							this.specInfoList = res.data.specInfoList
							this.haveSpecImgInfo = this.specInfoList.find(v => v.isMainSpec == 1)
							this.productList = res.data.productList.map(v => {
								v.isSet = true
								v.specInfoList = []
								v.specValueString = v.specValues
								let specValueIds = v.specValueIds.split(',')
								let specValues = v.specValues.split(',')
								specValueIds.map((x, i) => {
									let sepcValueItem = {
										specValue: specValues[i],
										specValueId: specValueIds[i]
									}
									v.specInfoList.push(sepcValueItem)
								})
								return v
							})

							uni.setStorageSync('specInfoList', this.specInfoList)
							uni.setStorageSync('productList', this.productList)
						}
						if (res.data.imageList && res.data.imageList.length > 0) {
							this.goodImg = []
							res.data.imageList.map(v => {
								let imgItem = {
									image: v.image,
									imageUrl: v.imageUrl
								}
								this.goodImg.push(imgItem)
							})
						}

						this.getGoodsFreightTemplate()
					} else {
						this.$api.msg(res.msg)
					}
				})
			},
			// 选择配送方式
			handleCheckBoxChange(e) {
				let values = e.detail.value
				if (values.includes('1')) {
					this.releaseInfo.supportMailing = 1
				} else {
					this.releaseInfo.supportMailing = 0
				}
				if (values.includes('2')) {
					this.releaseInfo.supportPickup = 1
				} else {
					this.releaseInfo.supportPickup = 0
				}
			},
			handleRadioChange(e) {
				let values = e.detail.value
				if (values.includes('1')) {
					this.releaseInfo.sellNow = true
				} else {
					this.releaseInfo.sellNow = false
				}
				console.log(e, 'e', this.releaseInfo.sellNow)
			},
			// 选择运费模版
			selFreight(e) {
				if (!e.detail.value) return this.$api.msg('请先去维护运费模板')
				this.freightInfo = this.freightTemplate[e.detail.value]
				this.freightId = this.freightInfo.freightTemplateId
			},
			chooseType() {
				this.isToChoose = true
				uni.navigateTo({
					url: "/merchants/pages/goods/choose_type"
				})
			},
			reserveInfo() {
				this.isToChoose = true
				uni.navigateTo({
					url: "/merchants/pages/goods/reservedInfo"
				})
			},
			tosetSpec() {
				this.isToChoose = true
				uni.navigateTo({
					url: "/merchants/pages/goods/goodsSpec"
				})
			},

			// 发布
			publish() {
				
				const {
					categoryId3,
					isVirtualGoods,
					goodsName,
					supportMailing,
					supportPickup
				} = this.releaseInfo;
				if (!categoryId3) return this.$api.msg('请选择商品分类')
				if (isVirtualGoods == 2 && this.reserveInfoList.length == 0) return this.$api.msg('请设置用户预留信息')
				if (!goodsName) return this.$api.msg('请输入商品名称')

				if (JSON.stringify(this.haveSpecImgInfo) != '{}') {
					console.log(this.haveSpecImgInfo.specValueList, 'imageList')
					let isImgUp = true
					this.haveSpecImgInfo.specValueList.map(v => {
						if (!isImgUp) return
						if (v.imageList.length == 0) {
							isImgUp = false
							this.$api.msg(`请上传${v.specValue}的图片`)
						}

					})

				} else {
					if (this.goodImg.length == 0) return this.$api.msg('请上传商品图片')
				}

				if (!supportMailing && !supportPickup) return this.$api.msg('请选择配送方式')
				if (this.goodsParam.ytype == 1 && this.freightFee != '0' && !this.freightFee) return this.$api.msg(
					'请输入运费金额')
				if (this.goodsParam.ytype == 2 && !this.freightId) return this.$api.msg('请选择运费模版')
				if (this.specInfoList.length == 0 && !this.productPrice) return this.$api.msg('请输入售价')
				if (this.specInfoList.length == 0 && !this.productStock) return this.$api.msg('请输入库存')
				

				let params = {}
				params.url = this.goodsId ? 'v3/goods/seller/goods/edit' : 'v3/goods/seller/goods/add'
				params.method = 'POST'
				params.header = {
						'Content-Type': 'application/json',
					},
					params.data = {
						...this.releaseInfo
					}

				if (isVirtualGoods == 2) {
					params.data.reserveInfoList = this.reserveInfoList
				}
				if (this.goodsParam.ytype == 1) {
					params.data.freightFee = this.freightFee
				}
				if (this.goodsParam.ytype == 2) {
					params.data.freightId = this.freightId
				}
				if (this.specInfoList.length == 0) {
					params.data.productPrice = this.productPrice
					params.data.productStock = this.productStock
					
					params.data.imageList = []
					this.goodImg.map((v, i) => {
						let item = {
							image: v.image,
							isMain: i == 0 ? 1 : 2
						}
						params.data.imageList.push(item)
					})
				} else {
					let newProductList = []
					newProductList = this.productList.map(v => {
						delete v.isSet
						delete v.specValueString
						if (v.specValueIds) {
							delete v.specValueIds
							delete v.specValues
						}
						return v
					})
					params.data.productList = newProductList

					if (JSON.stringify(this.haveSpecImgInfo) != '{}') {
						this.haveSpecImgInfo.specValueList.map(v => {
							v.imageList.map((x, i) => {
								x.isMain = i == 0 ? 1 : 2
							})
						})
						this.specInfoList = this.specInfoList.filter(v => v.isMainSpec == 0)
						this.specInfoList.unshift(this.haveSpecImgInfo)
						params.data.imageList = this.haveSpecImgInfo.specValueList[0].imageList
						params.data.specInfoList = this.specInfoList
					} else {
						params.data.imageList = []
						this.goodImg.map((v, i) => {
							let item = {
								image: v.image,
								isMain: i == 0 ? 1 : 2
							}
							params.data.imageList.push(item)
						})
						params.data.specInfoList = this.specInfoList
					}

				}

				if (this.goodsId) {
					params.data.goodsId = this.goodsId
					params.data.goodsDetails = this.goodsInfo.goodsDetails
				} else {
					// 新增商品时处理详情
					let newGoodsDetail = ''
					if (this.goodDetail.length > 0) {
						this.goodDetail.map(v => {
							let imgStr = `<img src="${v.imageUrl}" />`
							newGoodsDetail += imgStr
						})
					}
					params.data.goodsDetails = `<p>${newGoodsDetail}</p>`
				}


				this.$seller_request(params).then(res => {
					this.$api.msg(res.msg)
					if (res.state == 200) {
						setTimeout(() => {
							this.$Router.push('/merchants/pages/goods/goods')
						}, 1000)
					}
				})

			}
		}
	}
</script>

<style lang="scss" scoped>
	page {
		width: 100%;
		height: 100%;
	}

	.container {
		width: 100%;
		height: 100%;
		background-color: #F6F6F6;
		font-size: 28rpx;
		color: #101010;
		overflow-y: auto;

		.pub-item2 {
			padding: 20rpx 20rpx 36rpx 22rpx;
			background-color: #fff;
			margin-bottom: 2rpx;

			.pub-item2-tit {
				display: flex;
				align-items: center;
				justify-content: space-between;

				.left {
					font-weight: bold;
				}

				.right {
					color: #BEBEBE;
					font-size: 24rpx;
				}
			}

			.pub-item2-con {
				padding-top: 26rpx;

				.text_area {
					width: 100%;
					height: 154rpx;
					border: 2rpx solid #EFEFEF;
					padding: 16rpx;

					::v-deep .uni-textarea-placeholder {
						color: #888888;
					}
				}

				uni-textarea {
					font-size: 28rpx;
				}
			}
			
			.pub_color {
				font-size: 28rpx;
				color: #BEBEBE;
			}
		}

		.pub-item {
			height: 108rpx;
			display: flex;
			align-items: center;
			padding: 0 22rpx;
			margin-bottom: 2rpx;
			background-color: #fff;

			.pub-item-tit {
				font-weight: bold;
			}

			.pub-item-con {
				flex: 1;
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: flex-end;
				color: #BEBEBE;

				.p_input {
					height: 80rpx;
					width: 100%;
					text-align: right;
					color: #101010;

					::v-deep .uni-input-placeholder {
						color: #BEBEBE;
						font-size: 28rpx;
					}
				}

				.arrow {
					width: 24rpx;
					height: 24rpx;
					margin-left: 16rpx;
					margin-top: 4rpx;
				}

				@mixin circalRadio() {
					::v-deep .uni-radio-wrapper {
						.uni-radio-input {
							border-radius: 50%;
							margin-right: 10rpx;
						}

						.uni-radio-input-checked {
							background-color: transparent !important;
							border-color: #155BD4 !important;
						}

						.uni-radio-input-checked:before {
							content: "\EA01";
							color: #155BD4;
							background-color: #155BD4;
							font-size: 26rpx;
							border-radius: 50%;
						}
					}
				}

				.circle_radios {
					color: #101010;

					::v-deep uni-label {
						display: inline-block;
						margin-right: 40rpx;
					}

					@include circalRadio();
				}

				.checkBox {
					color: #101010;

					::v-deep uni-label {
						display: inline-block;
						margin-right: 40rpx;
					}

					::v-deep .uni-checkbox-input.uni-checkbox-input-checked {
						background-color: #5287EA;
						border-color: #5287EA !important;

						&:before {
							color: #fff;
						}
					}
				}
			}

			.choose_active {
				color: #101010;
			}
		}

		.redStar {
			color: red;
		}

		.bottom_btn {
			height: 264rpx;
			padding-top: 40rpx;

			.btn {
				width: 714rpx;
				height: 80rpx;
				line-height: 80rpx;
				border-radius: 6rpx;
				background-color: #3C78E9;
				color: #fff;
				font-size: 28rpx;
				text-align: center;
			}
		}
	}
</style>