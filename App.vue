<script>
import { mapMutations, mapState } from 'vuex';
import Router from './router.js';
import { initStat } from './utils/stat.js';
import Config from './utils/config.js';
import { callNativeLogin } from './utils/common.js';
// #ifdef H5
import { WXBrowserShareStart } from '@/utils/common.js';
// #endif
//app-1-start
// #ifdef APP
import { checkUpdate, appPushHandle } from '@/utils/common.js';
// #endif
//app-1-end
export default {
    globalData: {
        goLogin(Route) {
            uni.showModal({
                title: '',
                content: '当前暂未登录',
                confirmText: '去登录',
                confirmColor: '#c70e2d',
                success: (res) => {
                    if (res.confirm) {
                        let url = Route.path;
                        const query = Route.query;
                        uni.setStorageSync('fromurl', {
                            url,
                            query
                        });
                        // #ifdef H5
                        // 检查是否是App内嵌
                        const isAppOpen = uni.getStorageSync('openType') === 'app-h5';
                        if (isAppOpen) {
                            // 在App环境中且未登录，可以调用原生登录
                            console.log('App内嵌H5页面，调用原生登录');
                            callNativeLogin();
                        } else {
                            // 在H5环境中，跳转到登录页面
                            Router.push('/pages/public/login');
                        }
                        // #endif
                        // #ifdef MP-WEIXIN
                        Router.push('/pages/public/login');
                        // #endif
                    }
                }
            });
        },
        ...Config
    }, //全局配置
    computed: {
        ...mapState(['hasLogin', 'userInfo', 'userCenterData'])
    },
    methods: {
        ...mapMutations(['login', 'appToh5', 'logout', 'setUserCenterData']),

        //判断当前时间是否为今天 并且不是第二天
        isToday(data) {
            return new Date().toDateString() === data.toDateString() && Date.parse(data) < Date.parse(new Date(new Date().setHours(23, 59, 59, 59)));
        },
        // #ifdef APP
        appOperation() {
            //定时关闭启动页
            setTimeout(() => {
                plus.navigator.closeSplashscreen();
            }, 3000);
            // 锁定竖屏
            plus.screen.lockOrientation('portrait-primary');

            checkUpdate();

            let clientId = '';
            let timer = setInterval(function () {
                if (clientId != null && clientId != 'null' && clientId) {
                    clearInterval(timer);
                    return;
                }
                let now_clientId = plus.push.getClientInfo();
                if (clientId != now_clientId.clientid) {
                    clientId = now_clientId.clientid;
                    uni.setStorageSync('clientId', clientId);
                }
            }, 1000);

            appPushHandle();
        },
        // #endif
        initStatics() {
            //初始化统计数据
            let source = 'h5';
            //app-2-start
            // #ifdef APP-PLUS
            source = uni.getDeviceInfo().platform;
            // #endif
            //app-2-end
            //wx-1-start
            // #ifdef MP-WEIXIN
            source = 'xcx';
            // #endif
            //wx-1-end
            // #ifdef MP-BAIDU
            source = 'bdxcx';
            // #endif
            // #ifdef MP-ALIPAY
            source = 'alixcx';
            // #endif
            // #ifdef MP-TOUTIAO
            source = 'dyxcx';
            // #endif
            // #ifdef H5
            WXBrowserShareStart();
            // #endif
            //统计初始化
            initStat(this.globalData.statShowDebug, {
                equipmentType: 2, //设备类型，1-pc，2-移动设备，3-其他
                source: source, //终端名称，pc-pc；h5-H5；android-Android；ios-IOS；xcx-微信小程序
                memberId: 0, //会员id默认为0
                ip: '' //移动端ip默认都为空
            });
        }
    },
    onLaunch: function (options) {
        // #ifdef APP
        this.appOperation();
        // #endif
        // 获取应用打开参数
        // access_token 用于App嵌入H5页面的登录凭证
        const { query, scene, path } = options;
        const access_token = query?.access_token || '';
        console.log('应用打开参数:', { query, scene, options });
        // #ifdef H5
        let userInfoH5 = uni.getStorageSync('userInfo') || query?.access_token;
        const isApp = query.type && query.type === 'app';
        if (isApp) {
            // 如果是通过App打开的H5页面
            uni.setStorageSync('openType', 'app-h5');
        }
        console.log("H5端获取的用户信息:", userInfoH5);
        if (userInfoH5?.access_token) {
            //更新登陆状态
            this.setUserCenterData(uni.getStorageSync('userCenterData'));
            this.login(userInfoH5);
        } else if (access_token) {
            // 如果是通过access_token登录
            this.appToh5({
                access_token: access_token,
                refresh_token: '' // H5端没有refresh_token
            });
        }
        // #endif
        if (query) {
            const openData = {
                ...query,
                scene,
                path,
                timestamp: new Date().getTime()
            };
            delete openData.access_token; // 删除access_token，避免存储过多敏感信息
            const localStore = uni.getStorageSync('openData');
            const oldData = localStore ? JSON.parse(localStore) : {};
            if (oldData.pos) {
                // 检查是否过期
                if (oldData.timestamp) {
                    if (this.dayjs().isAfter(this.dayjs(oldData.timestamp).add(1, 'day'))) {
                        // console.log('上次的打开的参数已过期');
                        // 保存本地
                        uni.setStorage({
                            key: `openData`,
                            data: JSON.stringify(openData)
                        });
                    } else {
                        console.log('上次的打开的参数正在使用');
                    }
                } else {
                    // 保存本地
                    uni.setStorage({
                        key: `openData`,
                        data: JSON.stringify(openData)
                    });
                }
            } else {
                // 保存本地
                uni.setStorage({
                    key: `openData`,
                    data: JSON.stringify(openData)
                });
            }
        } else {
            const openData = {
                scene,
                path,
                timestamp: new Date().getTime()
            };
            // 保存本地
            uni.setStorage({
                key: `openData`,
                data: JSON.stringify(openData)
            });
        }
        //设置tabbar自定义内容
        // this.$diyStyle.dynaSetTabbar();

        //初始化统计
        // this.initStatics();

        // #ifdef MP-WEIXIN
        let userInfo = uni.getStorageSync('userInfo') || '';
        let _this = this;
        if (userInfo.access_token) {
            //更新登陆状态
            uni.getStorage({
                key: 'userInfo',
                success: (res) => {
                    _this.login(res.data);
                    _this.setUserCenterData(uni.getStorageSync('userCenterData'));
                }
            });
        }
        const windowInfo = uni.getWindowInfo();
        this.globalData.statusBarHeight = windowInfo.statusBarHeight;
        // #endif
        this.globalData.bottomSateArea = 0; //手机底部安全区域高度
    },
    onShow: function () {
        //app-4-start
        // 进入app时先清除页面栈长度本地缓存routeNetwork和本地保存的上一页的路由netNetwork以免出现异常
        //#ifdef APP
        uni.removeStorageSync('routeNetwork');
        uni.removeStorageSync('netNetwork');
        // #endif
        //app-4-end
        setInterval(function () {
            let data = new Date();
            let isToday = new Date().toDateString() === data.toDateString() && Date.parse(data) < Date.parse(new Date(new Date().setHours(23, 59, 59, 59))); //判断当前时间是否为今天并且不为第二天
            if (!isToday) {
                uni.removeSavedFile({
                    key: 'cookie'
                });
            }
        }, 1000);
    },
    onHide: function () {}
};
</script>

<style lang="scss">
/* 全局图标——阿里巴巴图标库 */
@import './common/css/icon.css';
@import './common/css/iconfont.css';

/* 全局公共样式 */
@import './common/css/base.scss';
@import './common/css/ql_edit.css';

/* H5富文本样式 */
/* #ifdef H5 */
@import './common/css/ql_edit.css';
/* #endif */

[type='search']::-webkit-search-decoration {
    display: none;
}

/* 自定义tabbar 页面底部高度填充 */
.tab-page-safe-bottom {
    height: 130rpx;
    width: 100%;
    background-color: transparent;
}

/* 微信浏览器分享提示 start */
.wx_brower_share_mask {
    width: 750rpx;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.45);
    position: fixed;
    z-index: 99999;
    top: 0;
}

/* #ifdef H5 */
page {
    width: 750rpx;
    margin: 0 auto;
}
/* app 内嵌H5适配 */
uni-page {
    // padding-top: 40px;
}
/* #endif */

/* #ifdef MP-ALIPAY */
page {
    height: 100vh;
}

/* #endif */

.wx_brower_share_top_wrap {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;
    margin-top: 150rpx;
}

.wx_brower_share_top_wrap .wx_brower_share_img {
    width: 450rpx;
    height: 150rpx;
    margin-right: 80rpx;
}

.share_h5 {
    width: 100% !important;
    height: 100% !important;
}

uni-image > img {
    opacity: unset;
    object-fit: contain;
}

.share_h5_operate_img {
    width: 440rpx !important;
    height: 120rpx !important;
}

.share_h5_close_img {
    width: 50rpx !important;
    height: 50rpx !important;
}

.share_h5_img_bottom {
    width: 50rpx !important;
    height: 160rpx !important;
}

/* 微信浏览器分享提示 end */

.yticon {
    font-family: 'yticon' !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* wx-2-start */
/* #ifdef MP-WEIXIN */
.ql-container {
    display: flex;
    align-items: center;
    line-height: 62rpx;
}

/* #endif */
/* wx-2-end */

.ql-editor {
    padding: 0;
}

button::after {
    border: none;
}

view,
scroll-view,
swiper,
swiper-item,
cover-view,
cover-image,
icon,
text,
rich-text,
progress,
button,
checkbox,
form,
input,
label,
radio,
slider,
switch,
textarea,
navigator,
audio,
camera,
image,
video {
    box-sizing: border-box;
}

/* 骨架屏替代方案 */
.Skeleton {
    background: #f3f3f3;
    padding: 20upx 0;
    border-radius: 8upx;
}

/* 图片载入替代方案 */
.image-wrapper {
    font-size: 0;
    background: #f3f3f3;
    border-radius: 4px;

    image {
        width: 100%;
        height: 100%;
        transition: 0.6s;
        opacity: 0;

        &.loaded {
            opacity: 1;
        }
    }
}

.clamp {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
}

.common-hover {
    background: #f5f5f5;
}

/*边框*/
.b-b:after,
.b-t:after {
    position: absolute;
    z-index: 3;
    left: 0;
    right: 0;
    height: 0;
    content: '';
    transform: scaleY(0.5);
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

/*边框*/
.b_b:after,
.b_t:after {
    position: absolute;
    z-index: 3;
    left: 0;
    right: 0;
    height: 0;
    content: '';
    transform: scaleY(0.5);
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.b_b:after {
    bottom: 0;
}

.b_t:after {
    top: 0;
}

.b-b:after {
    bottom: 0;
}

.b-t:after {
    top: 0;
}

/* button样式改写 */
uni-button,
button {
    height: 80upx;
    line-height: 80upx;
    font-size: $font-lg + 2upx;
    font-weight: normal;

    &.no-border:before,
    &.no-border:after {
        border: 0;
    }
}

uni-button[type='default'],
button[type='default'] {
    color: $font-color-dark;
}

/* input 样式 */
.input-placeholder {
    color: #999999;
}

.placeholder {
    color: #949494;
    font-size: 26rpx;
}

/* #ifdef MP-WEIXIN */
input {
    font-weight: normal;
}

/* #endif */

::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    color: transparent;
    display: flex;
    justify-content: center;
}

uni-tabbar .uni-tabbar {
    -webkit-box-shadow: 0 -2px 7px 0 hsla(0, 0%, 89.8%, 0.5);
    box-shadow: 0 -2px 7px 0 hsla(0, 0%, 89.8%, 0.5);
    width: 750rpx !important;
    left: auto !important;
    right: auto !important;
}

uni-tabbar {
    right: 0;
    display: flex;
    justify-content: center;
}

uni-page-head .uni-page-head {
    width: 750rpx !important;
    left: auto !important;
    right: auto !important;
}

uni-page-head {
    display: flex !important;
    justify-content: center;
}

body {
    font-family: 'PingFangSC-Regular', 'Microsoft YaHei', 'Helvetica', 'Droid Sans', 'Arial', 'sans-serif';
}

* {
    -webkit-font-smoothing: subpixel-antialiased;
}

/* 商品详情页 H5 图文详情强制换行处理 start */
/* #ifdef H5 */
.detail-desc {
    word-break: break-all;
}

/* #endif */
/* 商品详情页 H5 图文详情强制换行处理 end */
.uni-page-head .uni-page-head-ft .uni-page-head-btn .uni-btn-icon {
    font-size: 40rpx;
    margin-right: 15rpx;
}

.uni-popup.spec_model.bottom .uni-transition.fade-out {
    width: 750rpx;
    right: 0;
    left: 0;
    margin: 0 auto;
}

.uni-picker-container,
.uni-picker-toggle,
.uni-picker-toggle {
    width: 750rpx !important;
    left: 0 !important;
    right: 0 !important;
    margin: 0 auto !important;
}

.uni-popup .fade-out {
    width: 750rpx;
    margin: 0 auto;
}

.select_address .content_view {
    width: 750rpx;
    margin: 0 auto;
}

/* 分页loading */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 80rpx;
    font-size: 24rpx;
    color: #999;
}

.spinner {
    display: inline-block;
    vertical-align: middle;
    margin-right: 20rpx;
    font-size: 26rpx;
    width: 26rpx;
    height: 26rpx;
    text-align: left;
    border-radius: 50%;
    box-shadow: inset 0 0 0 3rpx rgba(58, 168, 237, 0.3);
}

.spinner text {
    position: absolute;
    clip: rect(0, 26rpx, 26rpx, 13rpx);
    width: 26rpx;
    height: 26rpx;
    animation: spinner-circle-clipper 1s ease-in-out infinite;
    -webkit-animation: spinner-circle-clipper 1s ease-in-out infinite;
}

@keyframes spinner-circle-clipper {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(180deg);
    }
}

@-webkit-keyframes spinner-circle-clipper {
    0% {
        -webkit-transform: rotate(0deg);
    }

    100% {
        -webkit-transform: rotate(180deg);
    }
}

.spinner text:after {
    position: absolute;
    clip: rect(0, 26rpx, 26rpx, 13rpx);
    width: 26rpx;
    height: 26rpx;
    content: '';
    animation: spinner-circle 1s ease-in-out infinite;
    -webkit-animation: spinner-circle 1s ease-in-out infinite;
    border-radius: 50%;
    box-shadow: inset 0 0 0 3rpx #3aa8ed;
}

@keyframes spinner-circle {
    0% {
        transform: rotate(-180deg);
    }

    100% {
        transform: rotate(180deg);
    }
}

@-webkit-keyframes spinner-circle {
    0% {
        -webkit-transform: rotate(-180deg);
    }

    100% {
        -webkit-transform: rotate(180deg);
    }
}

.loading {
    width: 100%;
    display: flex;
    justify-content: center;
    padding: 10rpx 0;
    opacity: 0;
    transition: all 0.2s;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 4;

    image {
        width: 100rpx;
        height: 56rpx;
    }

    &.show_loading {
        opacity: 1;
    }
}

.scroll_content {
    position: relative;
}
</style>
